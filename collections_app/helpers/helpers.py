from django.conf import settings
from django.utils import timezone
from accounts.agency_banking import AgencyBankingClass
import requests, json
from json.decoder import JSONDecodeError
from datetime import datetime
import ast



class LoanDiskClass():
    """
    A loandisk API utility class.
    Perform various loandisk related queries.
    """

    loan_disk_auth_code = settings.LOAN_DISK_SEC_KEY
    loan_disk_public_key = settings.LOANDISK_PUBLIC_KEY
    loan_disk_go_collect_branch_id = settings.LOAN_DISK_GO_COLLECT_BRANCH_ID
    loan_disk_borrower_base_url = \
        f"https://api-main.loandisk.com/{loan_disk_public_key}/{loan_disk_go_collect_branch_id}/borrower"
    borrower_advanced_search_base_url = \
        f"https://api-main.loandisk.com/{loan_disk_public_key}/{loan_disk_go_collect_branch_id}/advanced_search_borrowers"
    loan_disk_loans_base_url = \
        f"https://api-main.loandisk.com/{loan_disk_public_key}/{loan_disk_go_collect_branch_id}/loan"
    loan_disk_repayment_base_url = \
        f"https://api-main.loandisk.com/{loan_disk_public_key}/{loan_disk_go_collect_branch_id}/repayment"
    loan_disk_due_loans_base_url = \
        f"https://api-main.loandisk.com/{loan_disk_public_key}/{loan_disk_go_collect_branch_id}/due_loans"
    headers = {
                "Authorization": loan_disk_auth_code,
                "content-type": "application/json"
                }

    # Borrower
    def fetch_borrowers_list(self, page_number, size):
        """"""
        url = f"{self.loan_disk_borrower_base_url}/from/{page_number}/count/{size}"
        try:
            resp = requests.request("GET", url, headers=self.headers)
            data = resp.text
            return json.loads(data)
        except JSONDecodeError as e:
            return {}


    def fetch_borrowers_details(
            self, borrower_id=None, borrower_mobile=None, borrower_email=None,
            borrower_unique_number=None, borrower_dob=None):
        """"""
        if borrower_id:
            url = f"{self.loan_disk_borrower_base_url}/{borrower_id}"
        elif borrower_mobile:
            url = url = f"{self.loan_disk_borrower_base_url}/borrower_mobile/{borrower_mobile}"
        elif borrower_email:
            url = f"{self.loan_disk_borrower_base_url}/borrower_email/{borrower_email}"
        elif borrower_unique_number:
            url = f"{self.loan_disk_borrower_base_url}/borrower_unique_number/{borrower_unique_number}"
        elif borrower_dob:
            url = f"{self.loan_disk_borrower_base_url}/borrower_dob/{borrower_dob}"
        else:
            return {}

        resp = requests.request("GET", url, headers=self.headers)
        data = resp.text

        return json.loads(data)


    def get_borrower_branch_id(self, borrower_id):
        """Returns a branch id based on the borrower_id"""
        url = f"{self.loan_disk_borrower_base_url}/get_borrower_branch_id/{borrower_id}"

        resp = requests.request("GET", url, headers=self.headers)
        data = resp.text
        return ast.literal_eval(data)


    def get_total_borrowers_count_in_brach(self):
        """Returns total number of borrowers in a branch"""
        url = f"{self.loan_disk_borrower_base_url}/total_count"

        resp = requests.request("GET", url, headers=self.headers)
        data = resp.text
        return json.loads(data)
    
    def fetch_borrowers_advance_search(self, data):
        """
        Returns a list of borrowers with ongoing loans.
        """
        url = f"{self.borrower_advanced_search_base_url}"
        resp = requests.request("POST", url, headers=self.headers, data=data)
        data = resp.text

        return json.loads(data)

    # Loans
    def fetch_all_loans(self, page_number, size):
        """Returns a list of all loan"""
        url = f"{self.loan_disk_loans_base_url}/from/{page_number}/count/{size}"

        resp = requests.request("GET", url, headers=self.headers)
        data = resp.text
        return json.loads(data)

    def fetch_all_due_loans(self, page_number, size):
        """Returns a list of all loan"""
        url = f"{self.loan_disk_due_loans_base_url}"
        data = {
            "from": page_number,
            "count": size
            }
        resp = requests.request("POST", url, headers=self.headers, data=data)
        data = resp.text
        return json.loads(data)


    def get_loan_detail(self, loan_id=None, loan_application_id=None):
        """Returns a single loan object"""

        if loan_id:
            url = f"{self.loan_disk_loans_base_url}/{loan_id}"
        elif loan_application_id:
            url = f"{self.loan_disk_loans_base_url}/loan_application_id/{loan_application_id}"
        else:
            return {}

        resp = requests.request("GET", url, headers=self.headers)
        data = resp.text
        return json.loads(data)


    def get_borrower_loans(self, borrower_id, page_number, size):
        """Get all loans of a borrower"""
        url = f"{self.loan_disk_loans_base_url}/borrower/{borrower_id}/from/{page_number}/count/{size}"

        resp = requests.request("GET", url, headers=self.headers)
        data = resp.text

        return json.loads(data)


    def get_loan_repayments(self, loan_id, page_number, size):
        """
        Arg:
            loan_id: unique id for a loan record
        """
        url = f"{self.loan_disk_repayment_base_url}/loan/{loan_id}/from/{page_number}/count/{size}"
        resp = requests.request("GET", url, headers=self.headers)
        data = resp.text

        return json.loads(data)


class SupervisorClass:
    @classmethod
    def get_supervisor_users(cls, supervisor, retries: int = 0):
        agency_banking_supervisors = AgencyBankingClass.get_agency_supervisor_details()
        agency_banking_acquisition_officers = AgencyBankingClass.get_agency_acquisition_officer_details(
            agent_id=supervisor.customer_user_id
            )
        
        merchants_list = []
        if agency_banking_acquisition_officers.get("data"):
            merchant_dicts_list = agency_banking_acquisition_officers.get("data", {}).get("merchants", {})
            merchants_list += [merchant.get("user_id") for merchant in merchant_dicts_list]

        supervisor_users_list = []
        if "success" in agency_banking_supervisors and agency_banking_supervisors.get("success") == True:
            supervisors_list = agency_banking_supervisors.get("supervisors_teams_list")

            for supervisors in supervisors_list:
                if supervisors.get("supervisor_user_id") == supervisor.customer_user_id:
                    supervisor_users_list = supervisors.get("users_list", [])
                    break
        else:
            if retries <= 2:
                retries+=1
                return cls.get_supervisor_users(supervisor, retries=retries)
            else:
                supervisor_users_list = []

        return supervisor_users_list + merchants_list
    
    @classmethod
    def get_all_supervisors(cls):
        agency_banking_supervisors = AgencyBankingClass.get_agency_supervisor_details()

        all_supervisor_users_list = []
        if "success" in agency_banking_supervisors and agency_banking_supervisors.get("success") == True:
            supervisors_list = agency_banking_supervisors.get("supervisors_teams_list")

            for supervisors in supervisors_list:
                supervisor_user_id = supervisors.get("supervisor_user_id")
                all_supervisor_users_list.append(supervisor_user_id)
        return all_supervisor_users_list

def get_branch_loan_officers(query_branch=None):
    supervisors_list = AgencyBankingClass.get_agency_supervisor_details()

    if "success" in supervisors_list and supervisors_list.get("success") == True:
        teams = supervisors_list.get("supervisors_teams_list")
    else:
        teams = []

    branch_dict = {}
    for team in teams:
        supervisor_user_id = int(team.get("supervisor_user_id"))
        loan_officers = team.get("users_list")
        loan_officers_emails = team.get("user_email", [])
        branch = team.get("branch")

        if branch in branch_dict:
            branch =  branch
            existing_loan_officers = branch_dict[f"{branch}"].get("loan_officers")
            existing_loan_officers_emails = branch_dict[f"{branch}"].get("loan_officers_emails", [])
            updated_loan_officers_list = existing_loan_officers + loan_officers
            updated_loan_officers_emails_list = existing_loan_officers_emails + loan_officers_emails
            supervisors_count = branch_dict[f"{branch}"].get("supervisors_count")
            supervisors_count += 1

            branch_dict[f"{branch}"]["loan_officers"] = updated_loan_officers_list
            branch_dict[f"{branch}"]["supervisors_count"] = supervisors_count
            branch_dict[f"{branch}"]["loan_officers_emails"] = updated_loan_officers_emails_list
        else:
            branch_dict[f"{branch}"] = {}
            branch_dict[f"{branch}"] = {}
            branch_dict[f"{branch}"]["loan_officers"] = loan_officers
            branch_dict[f"{branch}"]["supervisors_count"] = 1

    if query_branch == None:
        return branch_dict
    try:
        search_branch = branch_dict[f"{query_branch}"]
        return search_branch
    except KeyError:
        return {}

def get_supervisor_loan_officers(supervisor_id=None):
    supervisors_list = AgencyBankingClass.get_agency_supervisor_details()
    if "success" in supervisors_list and supervisors_list.get("success") == True:
        teams = supervisors_list.get("supervisors_teams_list")
    else:
        teams = []

    loan_officers_list = []
    for team in teams:
        supervisor_name = team.get("supervisor")
        supervisor_email = team.get("supervisor_email")
        supervisor_user_id = int(team.get("supervisor_user_id"))
        loan_officers = team.get("users_list")
        branch = team.get("branch")

        if supervisor_id:
            if supervisor_user_id == supervisor_id:
                return loan_officers
        else:
            loan_officers_list += loan_officers
    return loan_officers_list


def get_loan_branches_list():
    supervisors_list = AgencyBankingClass.get_agency_supervisor_details()

    if "success" in supervisors_list and supervisors_list.get("success") == True:
        teams = supervisors_list.get("supervisors_teams_list")
    else:
        teams = []
    branch_list = [d["branch"] for d in teams]
    return branch_list


def get_agent_supervisor_details(customer_user_id):
    # Get Supervisor Details
    agent_supervisor = AgencyBankingClass.get_agency_user_details(
        user_id=customer_user_id,
        start_date=datetime.now().date()
        )

    if agent_supervisor.get("status") != False:
        supervisor = agent_supervisor.get("supervisor")
        branch = agent_supervisor.get("branch")
        user_email = agent_supervisor.get("user_email")
    else:
        supervisor = ""
        branch = ""
        user_email = ""

    return {
        "branch": branch,
        "supervisor": supervisor,
        "user_email": user_email
    }

def get_loan_age(loan):
    if loan.end_date < timezone.now().date():
        loan_age = (timezone.now().date() - loan.start_date).days
    else:
        loan_age = (loan.end_date - loan.start_date).days

    return loan_age


def get_total_due_repayment_qs(due_loans_qs):
    from django.db.models import Sum, Count, F, CharField, Value, ExpressionWrapper, IntegerField, Case, When
    from django.db.models.functions import Concat, ExtractDay
    total_due_repayment_qs = due_loans_qs.annotate(
            loan_age=Case(
                When(
                    end_date__gte=timezone.now().date(),
                    then=(
                        ExpressionWrapper(
                            ExtractDay(timezone.now().date() - F("start_date")),
                            output_field=IntegerField(),
                        )
                    ),
                ),
                When(
                    end_date__lt=timezone.now().date(),
                    then=(
                        ExpressionWrapper(
                            ExtractDay(F("end_date") - F("start_date")),
                            output_field=IntegerField(),
                        )
                    ),
                ),
            )
        ).annotate(
            amount_due=ExpressionWrapper(F("loan_age") * F("daily_repayment_amount"), output_field=IntegerField())
        )

    return total_due_repayment_qs


def match_bank_account_name_against_verification_name(borrower, bank_name):
    from loans.models import BorrowerInfo, LoanKYCDocumentation
    from accounts.models import IDVerificationDumps

    try:
        borrower_info = BorrowerInfo.objects.filter(
            borrower=borrower, is_verified=True
            ).last()
        
        kyc_data = LoanKYCDocumentation.objects.filter(borrower=borrower).last()

        if kyc_data:
            verf_first_name = kyc_data.borrower_first_name
            verf_last_name = kyc_data.borrower_last_name
            verf_middle_name = kyc_data.borrower_middle_name

        else:
            if borrower_info is None:
                id_verf_dumps = IDVerificationDumps.objects.filter(ajo_user=borrower).last()
                data = json.loads(id_verf_dumps.dump) if id_verf_dumps and id_verf_dumps.dump else {}

                verf_first_name = data.get("data", {}).get("data", {}).get("data", {}).get("firstName", "").lower().strip()
                verf_last_name = data.get("data", {}).get("data", {}).get("data", {}).get("lastName", "").lower().strip()
                verf_middle_name = data.get("data", {}).get("data", {}).get("data", {}).get("middleName", "").lower().strip()
            else:
                data = borrower_info.you_verify_metadata or {}

                verf_first_name = data.get("data", {}).get("firstName", "").lower().strip()
                verf_last_name = data.get("data", {}).get("lastName", "").lower().strip()
                verf_middle_name = data.get("data", {}).get("middleName", "").lower().strip()

        verf_names_set = {verf_last_name, verf_first_name, verf_middle_name}
        bank_name_list = bank_name.lower().split(" ")
        bank_account_names_set = set(bank_name_list)

        common_names = bank_account_names_set.intersection(verf_names_set)
        # print(verf_names_set, bank_account_names_set, common_names)

        return bank_account_names_set.issubset(verf_names_set) and len(common_names) > 1
    except Exception as e:
        return False