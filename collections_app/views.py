import ast
import json

from django.core.cache import cache
from rest_framework import permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView

from accounts.agency_banking import AgencyBankingClass
from accounts.models import ConstantTable, StaffCommissionAllocation
from accounts.permissions import IsBlackListed, IsLoanAvailable
from ajo.models import <PERSON>joSaving, AjoSepo
from ajo.payment_actions import bnpl_disbursement_settlement
from ajo.selectors import AjoUserSelector
from collections_app.helpers.helpers import LoanDiskClass, SupervisorClass
from collections_app.serializers import (
    SupervisorApproveLoanSerializer,
    SupervisorEligibilityApprovalSerializer,
    SupervisorGroupLoanDisbursementSerializer,
    SupervisorLoanDeclineSerializer,
    SupervisorLoanDisbursementSerializer,
)
from collections_app.services import (
    DebtCollectionClass,
    GroupLoansClass,
    LoanDetailClass,
    LoanDisbursementClass,
    LoansRequestsClass,
    RepaymentClass,
)
from loans.enums import (
    LoanStatus,
    LoanType,
    RepaymentFrequency,
    VerificationStage,
    EligibilityType,
)
from loans.models import (
    <PERSON><PERSON><PERSON><PERSON>,
    AjoLoanRepayment,
    GroupLoansDisbursementLog,
    LoanEligibility,
)
from loans.serializers import LoanHistorySerializer, MiniLoanEligibilitySerializer
from loans.tasks import update_loan_status_on_loan_disk
from payment.models import Transaction, WalletSystem
from payment.services import TransactionService


class BorrowersListApiView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        page_number = request.query_params.get("page_number")
        size = request.query_params.get("size")

        data = LoanDiskClass().fetch_borrowers_list(page_number, size)

        return Response(data, status=status.HTTP_200_OK)


class BorrowersAdvanceSearchListApiView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        page_number = request.query_params.get("page_number")
        size = request.query_params.get("size")

        data = LoanDiskClass().fetch_borrowers_advance_search(data=request.data)

        return Response(data, status=status.HTTP_200_OK)


class BorrowerDetailsView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        borrower_id = request.query_params.get("borrower_id")
        borrower_mobile = request.query_params.get("borrower_mobile")
        borrower_email = request.query_params.get("borrower_email")
        borrower_unique_number = request.query_params.get("borrower_unique_number")
        borrower_dob = request.query_params.get("borrower_dob")

        data = LoanDiskClass().fetch_borrowers_details(
            borrower_id=borrower_id,
            borrower_mobile=borrower_mobile,
            borrower_email=borrower_email,
            borrower_unique_number=borrower_unique_number,
            borrower_dob=borrower_dob,
        )
        return Response(data, status=status.HTTP_200_OK)


class LoansListApiView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        page_number = request.query_params.get("page_number")
        size = request.query_params.get("size")

        data = LoanDiskClass().fetch_all_loans(
            page_number=page_number,
            size=size,
            # borrower_email=borrower_email,
            # borrower_unique_number=borrower_unique_number,
            # borrower_dob=borrower_dob,
        )
        return Response(data, status=status.HTTP_200_OK)


class LoanDetailsApiView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        loan_id = request.query_params.get("loan_id")
        loan_application_id = request.query_params.get("loan_application_id")

        data = LoanDiskClass().get_loan_detail(
            loan_id=loan_id,
            loan_application_id=loan_application_id,
            # borrower_email=borrower_email,
            # borrower_unique_number=borrower_unique_number,
            # borrower_dob=borrower_dob,
        )
        return Response(data, status=status.HTTP_200_OK)


class BorrowerLoansApiView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        borrower_id = request.query_params.get("borrower_id")
        page_number = request.query_params.get("page_number")
        size = request.query_params.get("size")

        data = LoanDiskClass().get_borrower_loans(
            borrower_id=borrower_id,
            page_number=page_number,
            size=size,
            # borrower_email=borrower_email,
            # borrower_unique_number=borrower_unique_number,
            # borrower_dob=borrower_dob,
        )
        return Response(data, status=status.HTTP_200_OK)


# Debt Collection
class DebtCollectionOverview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        borrower_id = request.query_params.get("borrower_id")
        page_number = request.query_params.get("page_number")
        size = request.query_params.get("size")

        data = DebtCollectionClass().get_loan_performance_insight()
        return Response({"data": data}, status=status.HTTP_200_OK)


class AllOutstandingDebtorsListview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        page_number = request.query_params.get("page_number")
        size = request.query_params.get("size")
        data = DebtCollectionClass().get_borrowers_details_list(page_number, size)

        return Response(data, status=status.HTTP_200_OK)


class AllOutstandingPtPDebtorsListview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        page_number = request.query_params.get("page_number")
        size = request.query_params.get("size")
        data = DebtCollectionClass().get_borrowers_details_list(page_number, size)

        return Response(data, status=status.HTTP_200_OK)


class AllOutstandingDebtorsNoPromiseListview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        page_number = request.query_params.get("page_number")
        size = request.query_params.get("size")
        data = DebtCollectionClass().get_borrowers_details_list(page_number, size)

        return Response(data, status=status.HTTP_200_OK)


class DebtorDetailsView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        borrower_id = request.query_params.get("borrower_id")
        page_number = request.query_params.get("page_number")
        size = request.query_params.get("size")

        data = DebtCollectionClass().get_debtor_details(borrower_id=borrower_id)

        return Response(data, status=status.HTTP_200_OK)


# LOANS REQUESTS
class LoansRequestOverview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = LoansRequestsClass(request=request).get_loans_requests_overview()
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        return Response({"data": data}, status=status.HTTP_200_OK)


class LoansRequestListview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = LoansRequestsClass(request=request).get_all_loans_request_list()
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        return Response({"data": data}, status=status.HTTP_200_OK)


class LoansPendingRequestListview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = LoansRequestsClass(request=request).get_pending_loans_request_list()
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        return Response({"data": data}, status=status.HTTP_200_OK)


class LoansApprovedRequestListview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = LoansRequestsClass(request=request).get_approved_loans_request_list()
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        return Response({"data": data}, status=status.HTTP_200_OK)


class LoansRejectedRequestListview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = LoansRequestsClass(request=request).get_rejected_loans_request_list()
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        return Response({"data": data}, status=status.HTTP_200_OK)


class LoanDetailOverview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = LoanDetailClass(request=request).get_loan_detail_overview()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class LoanDetailview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = LoanDetailClass(request=request).get_loan_details()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BorrowerInfoview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = LoanDetailClass(request=request).get_borrower_info()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class GuarantorInfoview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = LoanDetailClass(request=request).get_guarantor_info()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AgentInfoview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = LoanDetailClass(request=request).get_agent_info()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BorrowerCrcDataView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = LoanDetailClass(
                request=request
            ).get_credit_intelligence_borrower_crc_data()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BorrowerCreditHistoryView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = LoanDetailClass(
                request=request
            ).get_credit_intelligence_borrower_credit_history()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BorrowerSpendingPatternView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = LoanDetailClass(
                request=request
            ).get_borrower_financial_data_spending_pattern()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BorrowerTransactionsHistoryView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = LoanDetailClass(request=request).get_borrower_transactions_history()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BorrowerNonIndebtednessDocsView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = LoanDetailClass(request=request).get_borrower_non_indebtedness_docs()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class LoansDisbursementOverview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = LoanDisbursementClass(request=request).get_disbursements_overview()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SupervisorDisbursedLoansListView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = LoanHistorySerializer

    def get(self, request):
        user = request.user
        # agency_banking_supervisors = AgencyBankingClass.get_agency_supervisor_details()

        # supervisor_users_list = []
        # if (
        #     "success" in agency_banking_supervisors
        #     and agency_banking_supervisors.get("success") == True
        # ):
        #     supervisors_list = agency_banking_supervisors.get("supervisors_teams_list")
        #     for supervisor in supervisors_list:
        #         if supervisor.get("supervisor_user_id") == user.customer_user_id:
        #             supervisor_users_list = supervisor.get("users_list", [])
        #             break

        supervisor_users_list = SupervisorClass().get_supervisor_users(supervisor=user)
        # supervisor_users_list += [2, 5]
        if supervisor_users_list:
            open_to_supervisors_loans = AjoLoan.objects.filter(
                is_disbursed=True, agent__customer_user_id__in=supervisor_users_list
            ).order_by("created_at")
            serializer = self.serializer_class(open_to_supervisors_loans, many=True)

            return Response({"data": serializer.data}, status=status.HTTP_200_OK)
        else:
            return Response({"data": []}, status=status.HTTP_200_OK)


class ReadyToDisburseLoansListView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = LoanHistorySerializer

    def get(self, request):
        user = request.user
        # agency_banking_supervisors = AgencyBankingClass.get_agency_supervisor_details()

        # supervisor_users_list = []
        # if (
        #     "success" in agency_banking_supervisors
        #     and agency_banking_supervisors.get("success") == True
        # ):
        #     supervisors_list = agency_banking_supervisors.get("supervisors_teams_list")
        #     for supervisor in supervisors_list:
        #         if supervisor.get("supervisor_user_id") == user.customer_user_id:
        #             supervisor_users_list = supervisor.get("users_list", [])
        #             break

        supervisor_users_list = SupervisorClass().get_supervisor_users(supervisor=user)
        # supervisor_users_list += [2, 5]

        if request.user.is_staff:
            open_to_supervisors_loans = AjoLoan.objects.filter(
                verification_stage=VerificationStage.SUPERVISOR_DISBURSEMENT,
                status=LoanStatus.OPEN_TO_SUPERVISOR,
            ).order_by("created_at")
        else:
            if supervisor_users_list:
                open_to_supervisors_loans = AjoLoan.objects.filter(
                    verification_stage=VerificationStage.SUPERVISOR_DISBURSEMENT,
                    status=LoanStatus.OPEN_TO_SUPERVISOR,
                    agent__customer_user_id__in=supervisor_users_list,
                ).order_by("created_at")
            else:
                return Response({"data": []}, status=status.HTTP_200_OK)
        serializer = self.serializer_class(open_to_supervisors_loans, many=True)
        return Response(
            {"data": serializer.data, "count": len(serializer.data)},
            status=status.HTTP_200_OK,
        )


class SupervisorDisburseAjoLoan(APIView):
    permission_classes = [permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable]

    def post(self, request):
        from django.conf import settings


        serializer = SupervisorLoanDisbursementSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        loan_instance: AjoLoan = validated_data.get("loan_instance")
        account_number = validated_data.get("account_number")
        account_name = validated_data.get("account_name")
        bank_name = validated_data.get("bank_name")
        bank_code = validated_data.get("bank_code")
        ajo_user = validated_data.get("ajo_user")
        is_head_of_operations = validated_data.get("is_head_of_operations")
        consent_given = validated_data.get("consent_given")
        disbursement_destination = validated_data.get("disbursement_destination")

        # Check if disbursement is available
        if ConstantTable.get_constant_table_instance().transfer_provider is None:
            resp = {"error": "Disbursement engine is unavailable."}
            return Response(data=resp, status=status.HTTP_400_BAD_REQUEST)

        # Get user disbursement wallet balance here and check it
        available_balance = (
            AjoUserSelector(ajo_user=ajo_user)
            .get_loan_disbursement_wallet()
            .available_balance
        )

        with open(settings.LOG_FILE, "a") as log_file:
            log_file.write(f"LOAN ID: {loan_instance.id}\n")
            log_file.write(f"AVAILABLE BALANCE: {available_balance}\n")
            log_file.write(f"LOAN AMOUNT: {loan_instance.amount}\n")
            log_file.write(f"LOAN TYPE: {loan_instance.loan_type}\n")
            log_file.write(f"LOAN PHONE: {loan_instance.borrower_phone_number}\n")
            log_file.write(
                f"""
                LOGIC OUTCOME: {(
            (
                loan_instance.repayment_type == RepaymentFrequency.WEEKLY
                or loan_instance.loan_type
                in [LoanType.BOOSTA_2X, LoanType.BOOSTA_2X_MINI]
            )
                    and available_balance > 15000
                    and available_balance <= loan_instance.amount
                )}\n"""
            )


        if (
            (
                loan_instance.repayment_type == RepaymentFrequency.WEEKLY
                or loan_instance.loan_type
                in [LoanType.BOOSTA_2X, LoanType.BOOSTA_2X_MINI]
                or (
                    loan_instance.application_type == EligibilityType.TOP_UP
                    and loan_instance.loan_type != LoanType.BNPL
                )
            )
            and available_balance > 15000
            and available_balance <= loan_instance.amount
        ):
            pass
        elif loan_instance.loan_type == LoanType.BNPL:
            pass
        elif (
            available_balance < loan_instance.amount
            or available_balance > loan_instance.amount
        ):
            response = {
                "error": "Insufficient funds in disbursement wallet.",
            }
            return Response(data=response, status=status.HTTP_400_BAD_REQUEST)
        else:
            pass

        if (
            is_head_of_operations is False
            and loan_instance.amount >= 300_000
            and loan_instance.loan_type != LoanType.MERCHANT_OVERDRAFT
        ):
            response = {
                "error": "You are not authorized to disburse up to 300,000. Contact head of operations.",
            }
            return Response(data=response, status=status.HTTP_400_BAD_REQUEST)

        if loan_instance.loan_type == LoanType.BNPL:
            StaffCommissionAllocation.create_commission_distribution(
                loan_id=loan_instance.id
            )  # commission allocation
            disburse_bnpl = bnpl_disbursement_settlement(
                loan=loan_instance, ajo_user=ajo_user
            )

            if disburse_bnpl.get("status") is True:
                response = {
                    "status": "Success",
                    "message": "Loan successfully disbursed",
                }
                return Response(data=response, status=status.HTTP_200_OK)

            else:
                response = {
                    "status": "Failed",
                    "message": "Please try again after 5 minutes!",
                }
                return Response(data=response, status=status.HTTP_200_OK)
        else:
            credit_ajo_user = AjoLoan.supervisor_disburse_ajo_loan(
                loan=loan_instance,
                account_number=account_number,
                account_name=account_name,
                bank_name=bank_name,
                bank_code=bank_code,
                consent_given=consent_given,
                disbursement_destination=disbursement_destination,
            )
            if credit_ajo_user.get("status") is True:
                StaffCommissionAllocation.create_commission_distribution(
                    loan_id=loan_instance.id
                )  # commission allocation
                if loan_instance.repayment_type == RepaymentFrequency.WEEKLY:
                    AjoLoanRepayment.loan_first_week_repayment(loan_id=loan_instance.id)

                response = {
                    "status": "Success",
                    "message": "Loan successfully disbursed",
                }
                return Response(data=response, status=status.HTTP_200_OK)
            else:
                response = {
                    "status": "Failed",
                    "message": "Please try again after 5 minutes!",
                }
                return Response(data=response, status=status.HTTP_200_OK)


class SupervisorApproveLoan(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        serializer = SupervisorApproveLoanSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        loan_instance = validated_data.get("loan_instance")
        borrower_info_instance = validated_data.get("borrower_info_instance")
        ajo_user = validated_data.get("ajo_user")
        is_head_of_operations = validated_data.get("is_head_of_operations")

        if is_head_of_operations is False and loan_instance.amount >= 200000:
            response = {
                "error": "You cannot approve up to 200,000. Kindly contact head of operations",
            }
            return Response(data=response, status=status.HTTP_400_BAD_REQUEST)
        else:
            loan_instance.status = LoanStatus.PROCESSING
            loan_instance.verification_stage = VerificationStage.GUARANTOR
            borrower_info_instance.is_verified = True
            borrower_info_instance.save()
            update_loan_status_on_loan_disk.delay(
                loan_id=loan_instance.id, loan_status=VerificationStage.GUARANTOR
            )
            loan_instance.save()

            response = {
                "status": "Success",
                "message": f"Loan approved by {request.user.email}",
            }
            return Response(data=response, status=status.HTTP_200_OK)


class SupervisorDeclineLoan(APIView):
    permission_classes = [permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable]

    def post(self, request):
        serializer = SupervisorLoanDeclineSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        loan_instance = validated_data.get("loan_instance")
        ajo_user = validated_data.get("ajo_user")
        # is_head_of_operations = validated_data.get("is_head_of_operations")

        # Get user disbursement wallet balance here and check it
        available_balance = (
            AjoUserSelector(ajo_user=ajo_user)
            .get_loan_disbursement_wallet()
            .available_balance
        )

        # if available_balance < loan_instance.amount:
        #     response = {
        #         "error": "No funds in disbursement wallet to refund.",
        #     }
        #     return Response(data=response, status=status.HTTP_400_BAD_REQUEST)
        # else:
        #     pass

        credit_ajo_user_spend = AjoLoan.supervisor_decline_ajo_loan(loan=loan_instance)

        if credit_ajo_user_spend.get("status") is True:
            response = {
                "status": "Success",
                "message": "Funds transferred to spend wallet",
            }
            return Response(data=response, status=status.HTTP_200_OK)
        else:
            response = {
                "status": "Failed",
                "message": "Please try again after 5 minutes!",
            }
            return Response(data=response, status=status.HTTP_400_BAD_REQUEST)


class CheckSupervisorRole(APIView):
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = LoanHistorySerializer

    def get(self, request):
        user = request.user
        agency_banking_supervisors = AgencyBankingClass.get_agency_supervisor_details()

        is_supervisor = False
        if (
            "success" in agency_banking_supervisors
            and agency_banking_supervisors.get("success") == True
        ):
            supervisors_list = agency_banking_supervisors.get("supervisors_teams_list")
            for supervisor in supervisors_list:
                # print(supervisor)
                if supervisor.get("supervisor_user_id") == user.customer_user_id:
                    is_supervisor = True
                    break
        data = {"is_supervisor": is_supervisor, "is_admin": user.is_staff}
        return Response({"data": data}, status=status.HTTP_200_OK)


class SupervisorManualResolveTransactionView(APIView):
    def post(self, request):
        data = request.data
        quotation_id = data.get("quotation_id")

        transaction = Transaction.objects.filter(
            quotation_id=quotation_id,
            transaction_form_type="TRANSFER_TO_EXTERNAL_ACCOUNT",
            wallet_type="LOAN_DISBURSEMENT",
        ).last()

        if not transaction:
            return Response(
                {"error": "transaction does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        transaction_verification = AgencyBankingClass.verify_transaction_from_agency_v2(
            reference=quotation_id
        )

        if transaction_verification.get("status") == True:
            reversed = transaction_verification.get("data").get("data").get("reversed")
            transaction_response_status = (
                transaction_verification.get("data").get("data").get("status")
            )

            loan_instance = AjoLoan.objects.filter(loan_ref=quotation_id).last()

            if reversed == True:
                transaction.status = "FAILED"

                ajo_user = loan_instance.borrower
                disburse_amount = loan_instance.amount

                if loan_instance:
                    loan_instance.status = LoanStatus.OPEN_TO_SUPERVISOR
                    loan_instance.supervisor_disbursement_status = "FAILED"

                # Get user disbursement wallet
                ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
                ajo_user_disbursment_wallet = (
                    ajo_user_selector.get_loan_disbursement_wallet()
                )

                # Create Reversal Wallet Transaction
                reversal_transaction = (
                    TransactionService.create_reversal_of_funds_transaction(
                        user=ajo_user_disbursment_wallet.user,
                        amount=disburse_amount,
                        wallet_type=ajo_user_disbursment_wallet.wallet_type,
                        quotation_id=loan_instance.loan_ref,
                        plan_type="LOAN",
                        ajo_user=ajo_user,
                        request_data={},
                    )
                )
                reversal_transaction.payload = transaction_verification
                reversal_transaction.save()

                # Refund User wallet
                fund_wallet = WalletSystem.fund_balance(
                    wallet=ajo_user_disbursment_wallet,
                    amount=disburse_amount,
                    transaction_instance=reversal_transaction,
                    onboarded_user=ajo_user,
                )
            elif transaction_response_status == "SUCCESSFUL":
                transaction.status = "SUCCESS"
                if loan_instance:
                    loan_instance.supervisor_disbursement_status = "SUCCESSFUL"
            else:
                pass
            transaction.save()
            loan_instance.save() or None

        return Response(
            {"data": "transaction resolved successfully"}, status=status.HTTP_200_OK
        )


class PendingEligibilityListView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = MiniLoanEligibilitySerializer

    def get(self, request):
        user = request.user
        # agency_banking_supervisors = AgencyBankingClass.get_agency_supervisor_details()

        # supervisor_users_list = []
        # if (
        #     "success" in agency_banking_supervisors
        #     and agency_banking_supervisors.get("success") == True
        # ):
        #     supervisors_list = agency_banking_supervisors.get("supervisors_teams_list")
        #     for supervisor in supervisors_list:
        #         if supervisor.get("supervisor_user_id") == user.customer_user_id:
        #             supervisor_users_list = supervisor.get("users_list", [])
        #             break

        supervisor_users_list = SupervisorClass().get_supervisor_users(supervisor=user)

        if request.user.is_staff:
            pending_eligibility = LoanEligibility.objects.filter(
                approved=False, verified_saver=True, requires_approval=True
            ).order_by("-created_at")
        else:
            if supervisor_users_list:
                pending_eligibility = LoanEligibility.objects.filter(
                    approved=False,
                    verified_saver=True,
                    requires_approval=True,
                    agent__customer_user_id__in=supervisor_users_list,
                ).order_by("-created_at")
            else:
                return Response({"data": []}, status=status.HTTP_200_OK)
        serializer = self.serializer_class(pending_eligibility, many=True)
        return Response(
            {"data": serializer.data, "count": len(serializer.data)},
            status=status.HTTP_200_OK,
        )


class EligibilityDetailView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = MiniLoanEligibilitySerializer

    def get(self, request):
        eligibility_id = int(request.query_params.get("eligibility_id"))
        if not isinstance(eligibility_id, int):
            return Response(
                {
                    "error": "Invalid eligibility ID",
                    "message": f"Expected int got {type(eligibility_id)}",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            eligibility = LoanEligibility.objects.get(id=eligibility_id)
        except LoanEligibility.DoesNotExist:
            return Response(
                {"data": "This eligibility does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.serializer_class(eligibility)
        return Response({"data": serializer.data}, status=status.HTTP_200_OK)


class SupervisorApproveLoanEligibilityView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        serializer = SupervisorEligibilityApprovalSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        eligibility_instance: LoanEligibility = validated_data.get(
            "eligibility_instance"
        )
        ajo_user = validated_data.get("ajo_user")
        is_head_of_operations = validated_data.get("is_head_of_operations")
        amount_approved = float(validated_data.get("amount_approved"))
        approval_reason = validated_data.get("approval_reason")

        if amount_approved > eligibility_instance.amount:
            response = {
                "error": "You cannot approve more than the eligible amount",
            }
            return Response(data=response, status=status.HTTP_400_BAD_REQUEST)
        elif is_head_of_operations is False and eligibility_instance.amount >= 200000:
            response = {
                "error": "You cannot approve up to 200,000. Kindly contact head of operations",
            }
            return Response(data=response, status=status.HTTP_400_BAD_REQUEST)
        else:
            eligibility_instance.approved = True
            eligibility_instance.amount_approved = amount_approved
            eligibility_instance.reason = approval_reason
            eligibility_instance.approved_by = request.user
            eligibility_instance.save()

            response = {
                "status": "Success",
                "message": f"Loan eligibility approved by {request.user.email}",
            }
            return Response(data=response, status=status.HTTP_200_OK)


class PendingApprovalLoanListView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = LoanHistorySerializer

    def get(self, request):
        user = request.user
        # agency_banking_supervisors = AgencyBankingClass.get_agency_supervisor_details()

        # supervisor_users_list = []
        # if (
        #     "success" in agency_banking_supervisors
        #     and agency_banking_supervisors.get("success") == True
        # ):
        #     supervisors_list = agency_banking_supervisors.get("supervisors_teams_list")
        #     for supervisor in supervisors_list:
        #         if supervisor.get("supervisor_user_id") == user.customer_user_id:
        #             supervisor_users_list = supervisor.get("users_list", [])
        #             break

        supervisor_users_list = SupervisorClass().get_supervisor_users(supervisor=user)

        if request.user.is_staff:
            pending_approval = AjoLoan.objects.filter(
                verification_stage__in=[
                    VerificationStage.CREDIT_BUREAU,
                    VerificationStage.WARNING,
                ],
            ).order_by("-created_at")
        else:
            if supervisor_users_list:
                pending_approval = AjoLoan.objects.filter(
                    verification_stage__in=[
                        VerificationStage.CREDIT_BUREAU,
                        VerificationStage.WARNING,
                    ],
                    agent__customer_user_id__in=supervisor_users_list,
                ).order_by("-created_at")
            else:
                return Response({"data": []}, status=status.HTTP_200_OK)
        serializer = self.serializer_class(pending_approval, many=True)
        return Response(
            {"data": serializer.data, "count": len(serializer.data)},
            status=status.HTTP_200_OK,
        )


class SupervisorManualLoanReviewConfirmationView(APIView):
    """A supervisor is to confirm that he has manually review
    a loan application before approval.
    """

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        loan_id = request.data.get("loan_id")

        try:
            loan_instance = AjoLoan.objects.get(id=loan_id)
            loan_instance.supervisor_checks_completed = True
            loan_instance.checked_by = request.user
            loan_instance.save()

            return Response(
                {"status": "success", "message": "Supervisor checks confirmed"},
                status=status.HTTP_200_OK,
            )
        except AjoLoan.DoesNotExist:
            return Response(
                {"status": "error", "message": "This loan does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class MissedRepaymentOverview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = RepaymentClass(request=request).get_missed_repayment_overview()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MissedRepaymentsListview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = RepaymentClass(request=request).get_missed_repayments_list()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class RepaymentsDetailview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = RepaymentClass(request=request).get_repayment_details()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AgentsMissedRepaymentsChartview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = RepaymentClass(request=request).get_agents_missed_repayments_chart()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class LoanOfficersListView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = RepaymentClass(request=request).get_loan_officers_list()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BranchesListView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = RepaymentClass(request=request).get_branches_list()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BranchesSupervisorsListView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = RepaymentClass(request=request).get_branch_supervisors_list()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class TopMissedBranchesView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = RepaymentClass(request=request).get_top_missed_repayment_branches()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class TopMissedLoanOfficersView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = RepaymentClass(
                request=request
            ).get_top_missed_repayment_loan_officers()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AdminMissedRepaymentAnalysisView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = RepaymentClass(request=request).get_admin_missed_repayment_analysis()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AdminPastMaturityAnalysisView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = RepaymentClass(request=request).get_past_maturity_analysis()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AdminWatchListAnalysisView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = RepaymentClass(request=request).get_watchlist_analysis()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AdminDefaultsAnalysisView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = RepaymentClass(request=request).get_defaults_analysis()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MissedRepaymentsClassificationView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = RepaymentClass(
                request=request
            ).get_missed_repayment_classifications()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DueCollectionsView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = cache.get(f"{request.user.customer_user_id}-due_collections_data")
            if not data:
                data = LoanDisbursementClass(request=request).get_due_collections()

            if "error" in data:
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class OngoingLoansRequestListview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = LoansRequestsClass(request=request).get_ongoing_loans_request_list()
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        return Response({"data": data}, status=status.HTTP_200_OK)


class GroupLoansRequestListview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = GroupLoansClass(request=request).get_group_loans_request_list()
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        return Response({"data": data}, status=status.HTTP_200_OK)


class PendingGroupLoansRequestListview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = GroupLoansClass(
                request=request
            ).get_pending_group_loans_request_list()
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        return Response({"data": data}, status=status.HTTP_200_OK)


class ApprovedGroupLoansRequestListview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = GroupLoansClass(
                request=request
            ).get_approved_group_loans_request_list()
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        return Response({"data": data}, status=status.HTTP_200_OK)


class LoansInGroupRequestListview(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            data = GroupLoansClass(request=request).get_loans_in_a_group()
        except Exception as e:
            return Response({"data": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        return Response({"data": data}, status=status.HTTP_200_OK)


class SupervisorGroupLoansDisbursementView(APIView):
    permission_classes = [permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable]

    def post(self, request):
        serializer = SupervisorGroupLoanDisbursementSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        loans_to_disburse = validated_data.get("loans_to_disburse")
        serializer_response = validated_data.get("response_data")
        serializer_response = validated_data.get("serializer_response")
        loan_group = validated_data.get("loan_group")

        view_response = {}

        if not loans_to_disburse:
            view_response = {
                "status": "Failed",
                "message": "No loan met disbursement requirements",
            }
            return Response(data=view_response, status=status.HTTP_400_BAD_REQUEST)

        for loan_instance, bank_details, ajo_user in loans_to_disburse:
            if loan_instance.loan_type == LoanType.BNPL:
                disburse_bnpl = bnpl_disbursement_settlement(
                    loan=loan_instance, ajo_user=ajo_user
                )

                if disburse_bnpl.get("status") is True:
                    response = {
                        "status": "Success",
                        "message": "Loan successfully disbursed",
                    }
                    view_response[f"{loan_instance.borrower.phone}"] = response
                    continue
                else:
                    response = {
                        "status": "Failed",
                        "message": "Please try again after 5 minutes!",
                    }
                    view_response[f"{loan_instance.borrower.phone}"] = response
                    continue
            else:
                credit_ajo_user = AjoLoan.supervisor_disburse_ajo_loan(
                    loan=loan_instance,
                    account_number=bank_details.account_number,
                    account_name=bank_details.account_name,
                    bank_name=bank_details.bank_name,
                    bank_code=bank_details.bank_code,
                    consent_given=loan_instance.consent_given,
                )

                if credit_ajo_user.get("status") is True:
                    if loan_instance.repayment_type == RepaymentFrequency.WEEKLY:
                        AjoLoanRepayment.loan_first_week_repayment(
                            loan_id=loan_instance.id
                        )

                    response = {
                        "status": "Success",
                        "message": "Loan successfully disbursed",
                    }
                    view_response[f"{loan_instance.borrower.phone}"] = response
                    continue
                else:
                    response = {
                        "status": "Failed",
                        "message": "Please try again after 5 minutes!",
                    }
                    view_response[f"{loan_instance.borrower.phone}"] = response
                    continue
        GroupLoansDisbursementLog.create_log(
            {
                "group": loan_group,
                "serializer_response": serializer_response,
                "view_response": view_response,
            }
        )
        return Response(data=view_response, status=status.HTTP_200_OK)


class SupervisorManualGroupLoanReviewConfirmationView(APIView):
    """A supervisor is to confirm that he has manually reviewed
    a group loan application before approval.
    """

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        group_id = request.data.get("group_id")
        consent_given = request.data.get("consent_given")

        try:
            group = AjoSepo.objects.get(group_id=group_id)
        except AjoSepo.DoesNotExist:
            return Response(
                {
                    "status": "error",
                    "message": "Sorry, no group loan found for the provided details.",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        group_saving = AjoSaving.objects.filter(group=group, is_activated=True)
        if group_saving is None:
            return Response(
                {
                    "status": "error",
                    "message": "An active savings does not exist for this group.",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        eligibility_qs = LoanEligibility.objects.filter(saving__in=group_saving)
        loans_in_group = AjoLoan.objects.filter(eligibility__in=eligibility_qs)

        if not loans_in_group:
            return Response(
                {"status": "error", "message": "Loans do not exist in this group"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        for loan_instance in loans_in_group:
            loan_instance.supervisor_complete_checks = True
            loan_instance.checked_by = request.user
            loan_instance.consent_given = consent_given or False
            loan_instance.save()

        return Response(
            {"status": "success", "message": "Supervisor checks confirmed"},
            status=status.HTTP_200_OK,
        )


class RepaymentAnalysisChartView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        data = RepaymentClass(request=request).get_repayment_analysis_chart()
        return Response({"data": data}, status=status.HTTP_200_OK)
