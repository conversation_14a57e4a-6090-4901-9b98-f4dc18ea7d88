import json
import os
from datetime import datetime

import gspread
import pandas as pd
from celery import shared_task
from django.conf import settings
from django.contrib.auth import get_user_model
from django.db.models import Case, Count, ExpressionWrapper, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Sum, When
from django.db.models.functions import ExtractDay
from django.utils import timezone
from google.oauth2 import service_account

from accounts.agency_banking import AgencyBankingClass
from accounts.models import CustomUser
from ajo.selectors import AjoUserSelector
from collections_app.enums import AttendanceStatus, UserTypes
from loans.enums import DisbursementStatus, LoanChargeNarration, LoanStatus
from loans.helpers.helpers import (
    get_percentages,
    send_ajo_loan_officers_daily_report_email,
)
from loans.models import AjoLoan, AjoLoanRepayment
from loans.tasks import admin_email_list
from payment.model_choices import PlanType, Status, TransactionFormType, WalletTypes
from payment.models import Transaction, WalletSystem
from payment.services import TransactionService

User = get_user_model()
disbursed_loans_status_list = ["COMPLETED", "OPEN"]


@shared_task
def google_sheet_collection_module():
    """
    Description:
        - Updates the record for missed repayments to enhance close follow up with defaulters.
    """
    # Google Client Authentication
    google_json = settings.GOOGLE_SHEET_CREDENTIALS
    service_account_info = json.loads(google_json)
    credentials = service_account.Credentials.from_service_account_info(
        service_account_info
    )
    scope = [
        "https://spreadsheets.google.com/feeds",
        "https://www.googleapis.com/auth/drive",
    ]
    creds_with_scope = credentials.with_scopes(scope)
    client = gspread.authorize(creds_with_scope)

    google_sheet_link = settings.GOOGLE_SHEET_LINK
    spreadsheet = client.open_by_url(google_sheet_link)  # Create spreadsheet instance
    worksheet = spreadsheet.get_worksheet(1)  # Get the second worksheet
    record_data = worksheet.get_all_records()  # Get all records in json format

    # Get all open loans
    loan_officers_qs = User.objects.filter(user_type="STAFF_AGENT")
    ajo_loans_qs = AjoLoan.objects.filter()
    ajo_loan_repayment_qs = AjoLoanRepayment.objects.all()

    disbursed_loans_qs = ajo_loans_qs.filter(
        status__in=disbursed_loans_status_list, is_disbursed=True
    )

    loans_list = []
    n = 0
    for loan in disbursed_loans_qs:
        loan_repayments = ajo_loan_repayment_qs.filter(ajo_loan=loan)
        repayments_count = list(loan_repayments.aggregate(Count("id")).values())[0]
        repayments_amount = list(
            loan_repayments.aggregate(Sum("repayment_amount")).values()
        )[0]
        loan_age = (timezone.now().date() - loan.start_date).days

        guarantor = loan.guarantor
        loan_amount = loan.amount
        borrower = loan.borrower
        agent = loan.agent

        borrower_name = (
            (borrower.first_name if borrower and borrower.first_name else "")
            + " "
            + (borrower.last_name if borrower and borrower.last_name else "")
        )
        borrower_phone_number = borrower.phone
        staff_agent_email = agent.get_username()
        staff_agent_phone_number = agent.user_phone
        guarantor_name = (
            (guarantor.surname if guarantor and guarantor.surname else "")
            + " "
            + (guarantor.last_name if guarantor and guarantor.last_name else "")
        )
        guarantor_phone = guarantor.phone_number if guarantor else ""
        repayment_amount = loan.repayment_amount
        daily_amount = loan.daily_repayment_amount
        repayment_count = repayments_count if repayments_count else 0
        total_paid_amount = loan.total_paid_amount
        if loan_age >= 0:
            amount_due_today = (daily_amount * loan_age) - total_paid_amount
        else:
            amount_due_today = 0
        last_paid_amount = loan.last_paid_amount
        n += 1
        balance = repayment_amount - total_paid_amount
        days_paid = round((total_paid_amount) / (daily_amount if daily_amount else 1))
        days_past_due = loan_age - days_paid

        data = {
            "S/N": n,
            "Borrower Name": borrower_name,
            "Borrower Phone": f"{borrower_phone_number}",
            "Staff Agent Email": f"{staff_agent_email}",
            "Staff Agent Phone": f"{staff_agent_phone_number}",
            "Guarantor Name": f"{guarantor_name}",
            "Guarantor Phone": f"{guarantor_phone}",
            "Loan Amount": f"{loan_amount:,}",
            "Due Today": f"{amount_due_today:,}",
            "Total Paid": f"{total_paid_amount:,}",
            "Loan Age": f"{loan_age}",
            "Last Paid Amount": f"{last_paid_amount:,}",
            "Repayment Amount": f"{repayment_amount:,}",
            "Daily Amount": f"{daily_amount:,}",
            "Repayments Count": f"{repayment_count}" if repayment_count else 0,
            "Loan Balance": f"{balance:,}",
            "Days Past Due": days_past_due if days_past_due > 0 else 0,
        }
        loans_list.append(data)

    dataframe = pd.DataFrame().from_dict(loans_list)
    worksheet.update([dataframe.columns.values.tolist()] + dataframe.values.tolist())


# @shared_task
# def update_supervisor_disbursement_status():
#     disbursement_transactions_qs = Transaction.objects.filter(
#         status=Status.PENDING,
#         transaction_form_type=TransactionFormType.DISBURSE_TO_EXTERNAL_ACCOUNT
#         )

#     for transaction in disbursement_transactions_qs:
#         customer_reference = transaction.transaction_id

#         transaction_verification = AgencyBankingClass.verify_transaction_from_agency_v2(
#             reference=customer_reference
#             )

#         if transaction_verification.get("status") == True:
#             reversed = transaction_verification.get("data").get("data").get("reversed")
#             status = transaction_verification.get("data").get("data").get("status")
#             loan_instance = AjoLoan.objects.filter(
#                 loan_ref=transaction.quotation_id,
#                 user=transaction.user
#             ).last()

#             if reversed == True:
#                 transaction.status = Status.FAILED

#                 ajo_user = loan_instance.borrower
#                 disburse_amount = loan_instance.amount

#                 # if loan_instance:
#                 loan_instance.status = LoanStatus.OPEN_TO_SUPERVISOR
#                 loan_instance.supervisor_disbursement_status = DisbursementStatus.FAILED

#                 # Get user disbursement wallet
#                 ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
#                 ajo_user_disbursment_wallet = ajo_user_selector.get_loan_disbursement_wallet()

#                 # Create Reversal Wallet Transaction
#                 reversal_transaction = TransactionService.create_reversal_of_funds_transaction(
#                     user=ajo_user_disbursment_wallet.user,
#                     amount=disburse_amount,
#                     wallet_type=ajo_user_disbursment_wallet.wallet_type,
#                     quotation_id=loan_instance.loan_ref,
#                     plan_type=PlanType.LOAN,
#                     ajo_user=ajo_user,
#                     request_data={}
#                     )
#                 reversal_transaction.payload=transaction_verification
#                 reversal_transaction.save()

#                 # Refund User wallet
#                 fund_wallet = WalletSystem.fund_balance(
#                     wallet=ajo_user_disbursment_wallet,
#                     amount=disburse_amount,
#                     transaction_instance=reversal_transaction,
#                     onboarded_user=ajo_user,
#                     )
#             elif status == "SUCCESSFUL":
#                 transaction.status = Status.SUCCESS
#                 if loan_instance:
#                     loan_instance.supervisor_disbursement_status = DisbursementStatus.SUCCESSFUL
#             else:
#                 pass
#             transaction.save()
#             loan_instance.save() or None


@shared_task
def run_loan_officers_daily_repayment_report():
    """
    Daily repayment report for Ajo loan officers
    """
    # Get all loan officers
    loan_officers_qs = User.objects.filter(user_type="STAFF_AGENT")
    ajo_loans_qs = AjoLoan.objects.filter()
    ajo_loan_repayment_qs = AjoLoanRepayment.objects.all()
    due_loans_qs = ajo_loans_qs.filter(start_date__lte=timezone.now().date())

    disbursed_loans_qs = ajo_loans_qs.filter(
        status__in=disbursed_loans_status_list, is_disbursed=True
    )
    loan_officers_repayments_list = []

    for agent in loan_officers_qs:
        agent: CustomUser
        loan_repayments = ajo_loan_repayment_qs.filter(agent=agent)
        # collected_repayments_count = list(loan_repayments.aggregate(Count("id")).values())[0]
        collected_repayments_amount = (
            list(loan_repayments.aggregate(Sum("repayment_amount")).values())[0] or 0
        )

        total_due_collections_amount = (
            due_loans_qs.filter(agent=agent)
            .annotate(
                loan_age=Case(
                    When(
                        end_date__lte=timezone.now().date(),
                        then=(
                            ExpressionWrapper(
                                ExtractDay(timezone.now().date() - F("start_date")),
                                output_field=IntegerField(),
                            )
                        ),
                    ),
                    When(
                        end_date__gt=timezone.now().date(),
                        then=(
                            ExpressionWrapper(
                                ExtractDay(F("end_date") - F("start_date")),
                                output_field=IntegerField(),
                            )
                        ),
                    ),
                )
            )
            .annotate(
                amount_due=ExpressionWrapper(
                    F("loan_age") * F("daily_repayment_amount"),
                    output_field=IntegerField(),
                )
            )
            .aggregate(amount_due_total=Sum("amount_due"))["amount_due_total"]
            or 0
        )

        missed_repayment_amount = (
            total_due_collections_amount - collected_repayments_amount
        ) or 0
        missed_repayment_amount = (
            missed_repayment_amount if missed_repayment_amount >= 0 else 0
        )
        percentage_collections = get_percentages(
            collected_repayments_amount, total_due_collections_amount
        )

        # Get Supervisor Details
        agent_supervisor = AgencyBankingClass.get_agency_user_details(
            user_id=agent.customer_user_id, start_date=datetime.now().date()
        )

        if agent_supervisor.get("status") is True:
            agent_supervisor = agent_supervisor.get("supervisor")
            branch = agent_supervisor.get("branch")
        else:
            agent_supervisor = ""
            branch = ""

        data = {
            # "Staff Agent Name": agent.get_full_name(),
            "Staff Agent Email": agent.get_username(),
            "Staff Agent Phone": f"{agent.user_phone}",
            "Due Repayment": f"{total_due_collections_amount:,}",
            "Missed Repayments": f"{missed_repayment_amount:,}",
            "Collected Repayments": f"{collected_repayments_amount:,}",
            "Percentage Collections": f"{percentage_collections:,}%",
            "Branch": branch,
            "Supervisor": agent_supervisor,
        }
        loan_officers_repayments_list.append(data)

    df = pd.DataFrame().from_dict(loan_officers_repayments_list)
    file_path = os.path.join(settings.BASE_DIR, "media/loan_officers")

    try:
        os.mkdir(file_path)
    except:
        pass

    excel_report = df.to_excel(
        f"{file_path}/loan_officers_daily_repayment_report_{datetime.now().date()}.xlsx",
        index=False,
    )

    with open(
        f"{file_path}/loan_officers_daily_repayment_report_{datetime.now().date()}.xlsx",
        "rb",
    ) as read_file:
        excel_file = read_file.read()

    for email in admin_email_list:
        send_email = send_ajo_loan_officers_daily_report_email(
            message="This is the Loan officers Repayment report for today",
            file=excel_file,
            file_name=f"Loan Officers Daily Report_{datetime.now().date()}.xlsx",
            email_subject="Loan Officers Daily Repayment Report",
            email=email,
            name="Team",
            date=datetime.now(),
        )

    return "DONE!!!"


@shared_task
def agents_daily_attendance_log():
    """
    Create daily attendance log for all agents
    and supervisors; excluding public holidays and weekends
    """
    from collections_app.helpers.helpers import SupervisorClass
    from collections_app.models import AgentDailyAttendance
    from loans.models import Holiday

    loan_oficers_list = list(
        get_user_model()
        .objects.filter(user_type="STAFF_AGENT")
        .values_list("customer_user_id", flat=True)
    )
    all_supervisors = SupervisorClass().get_all_supervisors() or []
    all_users = set(loan_oficers_list + all_supervisors)

    today = datetime.now()
    holidays = Holiday.objects.filter(
        date=today.date(),
        type__icontains="Company",
        )

    if holidays or today.date().weekday() in [5, 6]:
        return "Today is a public holiday or weekend"

    for officer in all_users:
        if officer in all_supervisors:
            user_type = UserTypes.SUPERVISOR
        else:
            user_type = UserTypes.STAFF_AGENT
        try:
            officer: CustomUser = CustomUser.objects.get(customer_user_id=officer)
        except CustomUser.DoesNotExist:
            continue
        if AgentDailyAttendance.objects.filter(
            agent=officer, created_at__date=today.date()
        ):
            return

        AgentDailyAttendance.create_attendance(officer=officer, user_type=user_type)
    return {
        "supervisors": all_supervisors,
        "loan_officers": loan_oficers_list,
        "today": today.date()
        }
