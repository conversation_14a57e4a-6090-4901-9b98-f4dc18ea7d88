from django.db import models
from django.utils.translation import gettext_lazy as _


class AttendanceStatus(models.TextChoices):
    ABSENT = _("ABSENT"), "ABSENT"
    PARTIAL_ATTENDANCE = _("PARTIAL_ATTENDANCE"), "PARTIAL_ATTENDANCE"
    FULL_ATTENDANCE = _("FULL_ATTENDANCE"), "FULL_ATTENDANCE"
    PRESENT = _("PRESENT"), "PRESENT"


class UserTypes(models.TextChoices):
    STAFF_AGENT =  _("STAFF_AGENT"), "STAFF_AGENT"
    SUPERVISOR =  _("SUPERVISOR"), "SUPERVISOR"