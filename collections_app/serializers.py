from rest_framework import serializers

from accounts.models import ConstantTable
from ajo.models import A<PERSON><PERSON><PERSON>, <PERSON><PERSON>Sep<PERSON>, AjoUserWithdrawalAccount
from ajo.payment_actions import bnpl_disbursement_settlement
from ajo.selectors import AjoUserSelector
from collections_app.helpers.helpers import SupervisorClass
from loans.enums import Loan<PERSON>tat<PERSON>, LoanType, VerificationStage
from loans.models import AjoLoan, BorrowerInfo, LoanEligibility
from payment.checks import verify_transaction_pin


class SupervisorLoanDisbursementSerializer(serializers.Serializer):
    DESTINATION_CHOICES = ["SPEND_WALLET", "EXTERNAL_ACCOUNT"]
    loan_id = serializers.IntegerField()
    transaction_pin = serializers.CharField(max_length=4)
    consent_given = serializers.BooleanField(default=False)
    disbursement_destination = serializers.ChoiceField(
        choices=DESTINATION_CHOICES,
        default="SPEND_WALLET",
    )

    def validate(self, data):
        from django.utils import timezone

        # from collections_app.helpers.helpers import (
        #     match_bank_account_name_against_verification_name,
        # )
        from payment.model_choices import TransactionFormType
        from payment.models import Transaction

        request = self.context.get("request")
        user = request.user
        loan_id = data.get("loan_id")
        disbursement_destination = data.get("disbursement_destination")

        const_instance = ConstantTable.get_constant_table_instance()
        disburse_to_spend_available = const_instance.disburse_to_spend_available
        disburse_to_external_available = const_instance.disburse_to_external_available

        try:
            ajo_loan = AjoLoan.objects.get(id=loan_id)
        except AjoLoan.DoesNotExist:
            raise serializers.ValidationError(
                "Sorry, no loan record found for the provided details."
            )

        loan_status = ajo_loan.status
        disburse_status = ajo_loan.is_disbursed
        verification_stage = ajo_loan.verification_stage
        borrower = ajo_loan.borrower

        if (
            loan_status != LoanStatus.OPEN_TO_SUPERVISOR
            or verification_stage != VerificationStage.SUPERVISOR_DISBURSEMENT
            or disburse_status is False
        ):
            raise serializers.ValidationError(
                f"This loan is not available for supervisor disbursement."
            )
        elif ajo_loan.requires_consent and data.get("consent_given") is False:
            raise serializers.ValidationError(
                f"This loan requires that supervisor provides consent."
            )
        else:
            pass

        # Get User authority
        is_head_of_operations = False
        user_groups = request.user.groups.all()

        if "Head of Operations" in [group.name for group in user_groups]:
            is_head_of_operations = True

        # Check that this loan officer belongs to the active supervisor
        get_supervisor_users = SupervisorClass().get_supervisor_users(supervisor=user)
        # get_supervisor_users += [1, 2, 5]
        if (
            get_supervisor_users
            and ajo_loan.agent.customer_user_id in get_supervisor_users
        ):
            pass
        elif is_head_of_operations:
            pass
        else:
            raise serializers.ValidationError(
                "You are not authorized to disburse this loan."
            )

        # Check whether borrower is lite user
        if borrower.lite_user is None and disbursement_destination == "SPEND_WALLET":
            raise serializers.ValidationError(
                "Borrower does not have a lite user account."
            )

        if (
            disbursement_destination == "SPEND_WALLET"
            and disburse_to_spend_available is False
        ):
            raise serializers.ValidationError(
                "Disbursement to spend wallet is unavailable. Use an external account."
                )
            # disbursement_destination = "EXTERNAL_ACCOUNT"

        elif disbursement_destination == "EXTERNAL_ACCOUNT" and disburse_to_external_available is False:
            raise serializers.ValidationError(
                "Disbursement to external account is unavailable. Use seeds app."
                )

        # Check for borrower external account
        borrower_external_account = AjoUserWithdrawalAccount.objects.filter(
            user=ajo_loan.agent, ajo_user=borrower
        ).last()

        if (
            borrower_external_account is None
            and ajo_loan.loan_type != "BNPL"
            and disbursement_destination == "EXTERNAL_ACCOUNT"
        ):
            raise serializers.ValidationError(
                "Borrower must provide an external account for withdrawal."
            )

        # Check Last Disbursement Attempt
        try:
            last_transaction = Transaction.objects.filter(
                quotation_id=ajo_loan.loan_ref,
                user=ajo_loan.agent,
                onboarded_user=ajo_loan.borrower,
                transaction_form_type=TransactionFormType.DISBURSE_TO_EXTERNAL_ACCOUNT,
            ).latest("date_created")

            if last_transaction:
                last_disbursement_time = last_transaction.date_created
                time_since_attempt_secs = (
                    timezone.now() - last_disbursement_time
                ).seconds
                time_since_attempt_mins = round(time_since_attempt_secs / 60)

                if time_since_attempt_mins < 30:
                    raise serializers.ValidationError(
                        f"Try again in {30-time_since_attempt_mins} minutes"
                    )
        except Transaction.DoesNotExist:
            pass

        if ajo_loan.last_disbursement_attempt:
            last_disbursement_time = ajo_loan.last_disbursement_attempt
            time_since_attempt_secs = (timezone.now() - last_disbursement_time).seconds
            time_since_attempt_mins = round(time_since_attempt_secs / 60)

            if time_since_attempt_mins < 30:
                raise serializers.ValidationError(
                    f"Try again in {30 - time_since_attempt_mins} minutes"
                )

        # Validate the Account Name
        # names_match = match_bank_account_name_against_verification_name(
        #     borrower=borrower, bank_name=borrower_external_account.account_name
        # )

        # if names_match:
        #     pass
        # else:
        #     raise serializers.ValidationError(
        #         "Bank account names mismatch. You can only use your own bank account."
        #     )

        # Check Loans Wallet Balance
        # try:
        #     available_balance = AjoLoan.check_balance()
        #     if available_balance < ajo_loan.amount:
        #         raise serializers.ValidationError("Error 911. Contact Support.")
        # except ValueError:
        #     raise serializers.ValidationError("Error 910, please try again later")

        access_token = request.headers.get("Authorization", "").partition(" ")[-1]
        supervisor_transaction_pin = data.get("transaction_pin")

        verify_supervisor_pin = verify_transaction_pin(
            transaction_pin=supervisor_transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )

        if verify_supervisor_pin is True:
            pass
        else:
            raise serializers.ValidationError("Incorrect transaction pin")

        data["loan_instance"] = ajo_loan
        data["ajo_user"] = ajo_loan.borrower
        data["is_head_of_operations"] = is_head_of_operations
        data["disbursement_destination"] = disbursement_destination

        if ajo_loan.loan_type == "BNPL":
            data["account_name"] = ""
            data["account_number"] = ""
            data["bank_name"] = ""
            data["bank_code"] = ""
        elif disbursement_destination == "SPEND_WALLET":
            data["account_name"] = ""
            data["account_number"] = ""
            data["bank_name"] = ""
            data["bank_code"] = ""
        else:
            data["account_name"] = borrower_external_account.account_name
            data["account_number"] = borrower_external_account.account_number
            data["bank_name"] = borrower_external_account.bank_name
            data["bank_code"] = borrower_external_account.bank_code
        return data


class SupervisorLoanDeclineSerializer(serializers.Serializer):
    loan_id = serializers.IntegerField()
    transaction_pin = serializers.CharField(max_length=4)

    def validate(self, data):
        request = self.context.get("request")
        user = request.user
        loan_id = data.get("loan_id")

        try:
            ajo_loan = AjoLoan.objects.get(id=loan_id)
        except AjoLoan.DoesNotExist:
            raise serializers.ValidationError(
                "Sorry, no loan record found for the provided details."
            )

        loan_status = ajo_loan.status
        disburse_status = ajo_loan.is_disbursed
        verification_stage = ajo_loan.verification_stage

        if (
            loan_status != LoanStatus.OPEN_TO_SUPERVISOR
            or verification_stage != VerificationStage.SUPERVISOR_DISBURSEMENT
            or disburse_status is False
        ):
            raise serializers.ValidationError(
                f"This loan is not available for supervisor decline."
            )
        else:
            pass

        # Get User authority
        is_head_of_operations = False
        user_groups = request.user.groups.all()

        if "Head of Operations" in [group.name for group in user_groups]:
            is_head_of_operations = True

        # Check that this loan officer belongs to the active supervisor
        get_supervisor_users = SupervisorClass().get_supervisor_users(supervisor=user)

        # get_supervisor_users
        if (
            get_supervisor_users
            and ajo_loan.agent.customer_user_id in get_supervisor_users
        ):
            pass
        elif is_head_of_operations:
            pass
        else:
            raise serializers.ValidationError(
                "You are not authorized to decline this loan."
            )

        data["loan_instance"] = ajo_loan
        data["ajo_user"] = ajo_loan.borrower
        data["is_head_of_operations"] = is_head_of_operations
        return data


class SupervisorEligibilityApprovalSerializer(serializers.Serializer):
    eligibility_id = serializers.IntegerField()
    # transaction_pin = serializers.CharField(max_length=4)
    amount_approved = serializers.CharField(max_length=100)
    approval_reason = serializers.CharField(max_length=200)

    def validate(self, data):
        request = self.context.get("request")
        user = request.user
        eligibility_id = data.get("eligibility_id")

        try:
            loan_eligibility = LoanEligibility.objects.get(id=eligibility_id)
        except LoanEligibility.DoesNotExist:
            raise serializers.ValidationError(
                "Sorry, no eligibility found for the provided details."
            )

        if (
            loan_eligibility.verified_saver == False
            or loan_eligibility.approved == True
        ):
            raise serializers.ValidationError(
                f"This eligibility is not available for supervisor approval."
            )
        else:
            pass

        # Get User authority
        is_head_of_operations = False
        user_groups = request.user.groups.all()

        if "Head of Operations" in [group.name for group in user_groups]:
            is_head_of_operations = True

        # Check that this eligibility officer belongs to the active supervisor
        get_supervisor_users = SupervisorClass().get_supervisor_users(supervisor=user)

        # get_supervisor_users
        if (
            get_supervisor_users
            and loan_eligibility.agent.customer_user_id in get_supervisor_users
        ):
            pass
        elif is_head_of_operations:
            pass
        else:
            raise serializers.ValidationError(
                "You are not authorized to approve this eligibility."
            )

        data["eligibility_instance"] = loan_eligibility
        data["ajo_user"] = loan_eligibility.ajo_user
        data["is_head_of_operations"] = is_head_of_operations
        return data


class SupervisorApproveLoanSerializer(serializers.Serializer):
    loan_id = serializers.IntegerField()

    def validate(self, data):
        request = self.context.get("request")
        user = request.user
        loan_id = data.get("loan_id")

        try:
            ajo_loan = AjoLoan.objects.get(id=loan_id)
        except AjoLoan.DoesNotExist:
            raise serializers.ValidationError(
                "Sorry, no loan record found for the provided details."
            )

        loan_status = ajo_loan.status
        disburse_status = ajo_loan.is_disbursed
        verification_stage = ajo_loan.verification_stage
        borrower = ajo_loan.borrower
        borrower_info_instance = BorrowerInfo.objects.filter(
            borrower=borrower, loan=ajo_loan
        ).last()

        if (
            verification_stage
            not in [VerificationStage.CREDIT_BUREAU, VerificationStage.WARNING]
            or disburse_status is True
        ):
            raise serializers.ValidationError(
                f"This loan is not available for supervisor approval."
            )
        elif borrower_info_instance is None:
            raise serializers.ValidationError(
                f"This loan is not available for supervisor approval. No borrower info"
            )
        else:
            pass

        # Get User authority
        is_head_of_operations = False
        user_groups = request.user.groups.all()

        if "Head of Operations" in [group.name for group in user_groups]:
            is_head_of_operations = True

        # Check that this loan officer belongs to the active supervisor
        get_supervisor_users = SupervisorClass().get_supervisor_users(supervisor=user)

        if (
            get_supervisor_users
            and ajo_loan.agent.customer_user_id in get_supervisor_users
        ):
            pass
        elif is_head_of_operations:
            pass
        else:
            raise serializers.ValidationError(
                "You are not authorized to approve this loan."
            )

        data["loan_instance"] = ajo_loan
        data["ajo_user"] = ajo_loan.borrower
        data["is_head_of_operations"] = is_head_of_operations
        data["borrower_info_instance"] = borrower_info_instance
        return data


class SupervisorGroupLoanDisbursementSerializer(serializers.Serializer):
    group_id = serializers.CharField()
    transaction_pin = serializers.CharField(max_length=4)
    # consent_given = serializers.BooleanField(default=False)

    def validate(self, data):
        request = self.context.get("request")
        user = request.user
        group_id = data.get("group_id")
        loans_to_disburse = []

        try:
            group = AjoSepo.objects.get(group_id=group_id)
        except AjoSepo.DoesNotExist:
            raise serializers.ValidationError(
                "Sorry, no group record found for the provided details."
            )

        # Get User authority
        is_head_of_operations = False
        user_groups = request.user.groups.all()

        if "Head of Operations" in [group.name for group in user_groups]:
            is_head_of_operations = True

        get_supervisor_users = SupervisorClass().get_supervisor_users(supervisor=user)

        group_saving = AjoSaving.objects.filter(group=group, is_activated=True)
        if group_saving is None:
            raise serializers.ValidationError(
                "An active savings does not exist for this group."
            )

        eligibility_qs = LoanEligibility.objects.filter(saving__in=group_saving)
        loans_in_group = AjoLoan.objects.filter(eligibility__in=eligibility_qs)

        if not loans_in_group:
            raise serializers.ValidationError("No pending disbursement in this group.")

        for ajo_loan in loans_in_group:
            loan_status = ajo_loan.status
            disburse_status = ajo_loan.is_disbursed
            verification_stage = ajo_loan.verification_stage
            borrower = ajo_loan.borrower

            # Check loan availability for disbursement
            if (
                loan_status != LoanStatus.OPEN_TO_SUPERVISOR
                or verification_stage != VerificationStage.SUPERVISOR_DISBURSEMENT
                or disburse_status is False
            ):
                raise serializers.ValidationError(
                    "One or more of the loans is not available for supervisor disbursement."
                )
            elif ajo_loan.requires_consent and ajo_loan.consent_given is False:
                raise serializers.ValidationError(
                    "One or more loans require supervisor consent."
                )
            else:
                pass

            # Check that this loan officer belongs to the active supervisor
            if (
                get_supervisor_users
                and ajo_loan.agent.customer_user_id in get_supervisor_users
            ):
                pass
            elif is_head_of_operations:
                pass
            else:
                raise serializers.ValidationError(
                    "You are not authorized to disburse one or more of the loans in this group."
                )

            # Check for borrower external account
            borrower_external_account = AjoUserWithdrawalAccount.objects.filter(
                user=ajo_loan.agent, ajo_user=borrower
            ).last()

            if borrower_external_account is None and ajo_loan.loan_type != "BNPL":
                raise serializers.ValidationError(
                    "All borrowers must provide an external account for withdrawal."
                )

        access_token = request.headers.get("Authorization", "").partition(" ")[-1]
        supervisor_transaction_pin = data.get("transaction_pin")

        verify_supervisor_pin = verify_transaction_pin(
            transaction_pin=supervisor_transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )

        if verify_supervisor_pin is True:
            pass
        else:
            raise serializers.ValidationError("Incorrect transaction pin")

        response_data = {}
        # Perform Account Level Checks
        for ajo_loan in loans_in_group:
            if not ConstantTable.get_constant_table_instance().transfer_provider:
                response = "Disbursement engine unavailable."
                response_data[f"{ajo_loan.borrower.phone}"] = response
                continue

            # Get user disbursement wallet balance here and check it
            ajo_user = ajo_loan.borrower
            available_balance = (
                AjoUserSelector(ajo_user=ajo_user)
                .get_loan_disbursement_wallet()
                .available_balance
            )

            if available_balance < ajo_loan.amount:
                response = "Insufficient funds in disbursement wallet."
                response_data[f"{ajo_loan.borrower.phone}"] = response
                continue

            elif is_head_of_operations is False and ajo_loan.amount >= 300000:
                response = "You are not authorized to disburse up to 300,000. Contact head of operations."
                response_data[f"{ajo_loan.borrower.phone}"] = response
                continue
            else:
                response = "Validated successfully."
                response_data[f"{ajo_loan.borrower.phone}"] = response

            if ajo_loan.loan_type == "BNPL":
                loans_to_disburse.append((ajo_loan, None, ajo_user))
            else:
                loans_to_disburse.append(
                    (ajo_loan, borrower_external_account, ajo_user)
                )

        data["loans_to_disburse"] = loans_to_disburse
        data["serializer_response"] = response_data
        data["loan_group"] = group
        return data
