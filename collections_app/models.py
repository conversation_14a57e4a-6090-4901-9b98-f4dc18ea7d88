from django.db import models
from django.db.models import Sum, Count
from django.conf import settings

from accounts.agency_banking import AgencyBankingClass
from accounts.helpers import BaseModel
from accounts.models import CustomUser
from collections_app.enums import AttendanceStatus, UserTypes
from loans.models import <PERSON><PERSON><PERSON><PERSON>, AjoLoanRepayment, Holiday
from loans.enums import LoanStatus
from payment.models import Transaction

from datetime import datetime
from dateutil.rrule import DAILY, rrule


class AgentDailyAttendance(BaseModel):
    agent = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE,
        related_name="agent_attendance"
        )
    user_type = models.CharField(
        max_length=1200, default=UserTypes.STAFF_AGENT,
        choices=UserTypes.choices
        )
    agent_name = models.CharField(max_length=1200, blank=True, null=True)
    branch = models.CharField(max_length=1200, blank=True, null=True)
    login_count = models.IntegerField(default=0)
    number_of_active_days = models.IntegerField(default=0)
    number_of_workdays_in_month = models.IntegerField(default=0)
    activity_score = models.FloatField(default=0.00)
    month_activity_score = models.FloatField(default=0.00)
    loan_disbursements_count = models.IntegerField(default=0)
    loan_disbursements_amount = models.FloatField(default=0.00)
    due_repayments_count_today = models.IntegerField(default=0)
    due_repayments_count_month = models.IntegerField(default=0)
    repayments_collected_count = models.IntegerField(default=0)
    repayments_collected_amount = models.FloatField(default=0.00)
    savings_transactions_count = models.IntegerField(default=0)
    savings_transactions_amount = models.FloatField(default=0.00)
    activity = models.CharField(
        max_length=1200, default=AttendanceStatus.ABSENT,
        choices=AttendanceStatus.choices
        )
    total_earnings = models.FloatField(default=0.00, blank=True, null=True)
    due_repayments_amount_today = models.FloatField(default=0.00, blank=True, null=True)
    due_collections_amount_today = models.FloatField(default=0.00, blank=True, null=True)
    expected_workdays_in_month = models.IntegerField(default=0, blank=True, null=True)


    @classmethod
    def create_attendance(cls, officer: CustomUser, user_type):
        from collections_app.helpers.helpers import (
        SupervisorClass, get_supervisor_loan_officers
        )
        from accounts.models import ConstantTable
        from loans.enums import DisbursementStatus
        from loans.helpers.helpers import SalaryDecisioning

        today = datetime.now()
        month_start = ConstantTable.get_constant_table_instance().last_payroll_date

        officer: CustomUser
        login_counts = AgencyBankingClass.get_agency_user_details(
            user_id=officer.customer_user_id, start_date=today.date()
        )

        prorated_earnings = SalaryDecisioning.get_prorated_salary(
            user=officer,
        )

        if "success" in login_counts and login_counts.get("success") == True:
            login_count = login_counts.get("login_count")
            branch = login_counts.get("branch")
        else:
            login_count = 0
            branch = ""

        if user_type == UserTypes.SUPERVISOR:
            supervisor_users_list = get_supervisor_loan_officers(
                officer.customer_user_id
                )

            loans_disbursed_amount_today = (
                AjoLoan.objects.filter(
                agent__customer_user_id__in=supervisor_users_list,
                    date_disbursed=today.date(),
                    supervisor_disbursement_status=DisbursementStatus.SUCCESSFUL
                    )
                .aggregate(total_disbursed=Sum("amount"))["total_disbursed"] or 0.00
            )
            loans_disbursed_count_today = (
                AjoLoan.objects.filter(
                agent__customer_user_id__in=supervisor_users_list,
                    date_disbursed=today.date(),
                    supervisor_disbursement_status=DisbursementStatus.SUCCESSFUL
                    )
                .aggregate(total_disbursed_count=Count("amount"))["total_disbursed_count"] or 0
            )

            repayments_collected_amount_today = (
                AjoLoanRepayment.objects.filter(
                    agent__customer_user_id__in=supervisor_users_list, 
                    created_at__date=today.date())
                .aggregate(total_repayment_amount=Sum("repayment_amount")
                )["total_repayment_amount"] or 0.00
            )
            repayments_collected_count_today = (
                AjoLoanRepayment.objects.filter(
                    agent__customer_user_id__in=supervisor_users_list, 
                    created_at__date=today.date())
                .aggregate(total_repayments_count=Count("repayment_amount")
                )["total_repayments_count"] or 0
            )
            repayments_collected_count_month = (
                AjoLoanRepayment.objects.filter(
                    agent__customer_user_id__in=supervisor_users_list,
                    created_at__month=today.date().month,
                    created_at__year=today.date().year)
                .aggregate(total_repayments_count=Count("repayment_amount")
            )["total_repayments_count"] or 0
            )

            ajo_savings_transactions_qs = Transaction.objects.filter(
                user__customer_user_id__in=supervisor_users_list,
                plan_type="AJO", wallet_type="AJO_USER",
                status="SUCCESS", transaction_form_type="WALLET_DEPOSIT",
            )
            savings_collected_amount_today = (
                ajo_savings_transactions_qs.filter(date_created__date=today.date())
                .aggregate(savings_amount=Sum("amount"))["savings_amount"] or 0.00
            )
            savings_collected_count_today = (
                ajo_savings_transactions_qs.filter(date_created__date=today.date())
                .aggregate(savings_count=Count("amount"))["savings_count"] or 0
            )

            due_repayments_count_today = sum(
                [1 for loan in AjoLoan.objects.filter(
                agent__customer_user_id__in=supervisor_users_list,
                status=LoanStatus.OPEN) if loan.due_today > 0
                ])
            
            due_repayments_amount_today = sum(
                [loan.due_today for loan in AjoLoan.objects.filter(
                agent__customer_user_id__in=supervisor_users_list,
                status=LoanStatus.OPEN)
                ])
            
            due_collections_amount_today = sum(
                [loan.daily_repayment_amount for loan in AjoLoan.objects.filter(
                agent__customer_user_id__in=supervisor_users_list,
                status=LoanStatus.OPEN, start_date__lte=today.date())
                ])
        else:
            loans_disbursed_amount_today = (
                AjoLoan.objects.filter(
                agent=officer, date_disbursed__date=today.date(),
                supervisor_disbursement_status=DisbursementStatus.SUCCESSFUL
                ).aggregate(total_disbursed=Sum("amount"))["total_disbursed"] or 0.00
            )
            loans_disbursed_count_today = (
                AjoLoan.objects.filter(
                agent=officer, date_disbursed__date=today.date(),
                supervisor_disbursement_status=DisbursementStatus.SUCCESSFUL
                ).aggregate(total_disbursed_count=Count("amount"))["total_disbursed_count"] or 0
            )

            repayments_collected_amount_today = (
                AjoLoanRepayment.objects.filter(agent=officer, created_at__date=today.date())
                .aggregate(total_repayment_amount=Sum("repayment_amount")
                )["total_repayment_amount"] or 0.00
            )
            repayments_collected_count_today = (
                AjoLoanRepayment.objects.filter(agent=officer, created_at__date=today.date())
                .aggregate(total_repayments_count=Count("repayment_amount")
                )["total_repayments_count"] or 0
            )
            repayments_collected_count_month = (
                AjoLoanRepayment.objects.filter(
                    agent=officer, created_at__month=today.date().month,
                    created_at__year=today.date().year)
                .aggregate(total_repayments_count=Count("repayment_amount")
            )["total_repayments_count"] or 0
            )

            ajo_savings_transactions_qs = Transaction.objects.filter(
                plan_type="AJO", wallet_type="AJO_USER",
                status="SUCCESS", transaction_form_type="WALLET_DEPOSIT",
            )
            savings_collected_amount_today = (
                ajo_savings_transactions_qs.filter(user=officer, date_created__date=today.date())
                .aggregate(savings_amount=Sum("amount"))["savings_amount"] or 0.00
            )
            savings_collected_count_today = (
                ajo_savings_transactions_qs.filter(user=officer, date_created__date=today.date())
                .aggregate(savings_count=Count("amount"))["savings_count"] or 0
            )

            due_repayments_count_today = sum([
                1 for loan in AjoLoan.objects.filter(
                agent=officer, status=LoanStatus.OPEN)
                if loan.due_today > 0
            ])

            due_repayments_amount_today = sum([
                loan.due_today for loan in AjoLoan.objects.filter(
                agent=officer, status=LoanStatus.OPEN)
                if loan.due_today > 0
            ])

            due_collections_amount_today = sum([
                loan.daily_repayment_amount for loan in AjoLoan.objects.filter(
                agent=officer, status=LoanStatus.OPEN,
                start_date__lte=today.date())
                if loan.due_today > 0
            ])

        due_repayments_count_month = (
            AgentDailyAttendance.objects.filter(
            agent=officer, created_at__gte=month_start)
            .aggregate(total_due_repayment_count=Sum("due_repayments_count_today")
            )["total_due_repayment_count"] or 0) + due_repayments_count_today

        activity = AttendanceStatus.PRESENT if login_count >=3 else AttendanceStatus.ABSENT

        try:
            activity_score = (
                repayments_collected_count_today / due_repayments_count_today
                ) * 100
        except ZeroDivisionError:
            activity_score = 0
        try:
            month_activity_score = (
                repayments_collected_count_month / due_repayments_count_month
                ) * 100
        except ZeroDivisionError:
            month_activity_score = 0.00

        prorated_earnings = SalaryDecisioning.get_prorated_salary(
            user=officer,
        )

        AgentDailyAttendance.objects.create(
            agent=officer,
            agent_name=f"{officer.first_name} {officer.last_name}",
            login_count=login_count,
            activity=activity,
            activity_score=activity_score,
            month_activity_score=month_activity_score,
            repayments_collected_count=repayments_collected_count_today,
            repayments_collected_amount=repayments_collected_amount_today,
            loan_disbursements_count=loans_disbursed_count_today,
            loan_disbursements_amount=loans_disbursed_amount_today,
            savings_transactions_count=savings_collected_count_today,
            savings_transactions_amount=savings_collected_amount_today,
            due_repayments_count_today=due_repayments_count_today,
            due_repayments_count_month=due_repayments_count_month,
            number_of_workdays_in_month=prorated_earnings.get("work_days_in_month", 0),
            number_of_active_days=prorated_earnings.get("days_present", 0),
            user_type=user_type,
            branch=branch,
            total_earnings=prorated_earnings.get("prorated_earnings_for_the_month", 0.00),
            due_repayments_amount_today=due_repayments_amount_today,
            due_collections_amount_today=due_collections_amount_today,
            expected_workdays_in_month=prorated_earnings.get("general_work_days_till_today", 0),
        )


class PayrollLog(BaseModel):
    request_data = models.TextField()
    response_data = models.TextField(blank=True, null=True)
    last_payroll_date = models.DateTimeField(blank=True, null=True)
    sent_to_paybox =  models.BooleanField(default=False, blank=True, null=True)
    agent_details_data = models.JSONField(default=list, blank=True, null=True)