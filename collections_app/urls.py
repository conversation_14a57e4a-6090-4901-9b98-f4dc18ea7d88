from django.urls import path
from collections_app import views


SUPERVISOR_LOANS_REQUEST_URLS = [
    path("loans-requests-overview", views.LoansRequestOverview.as_view()),
    path("loans-requests-list", views.LoansRequestListview.as_view()),
    path("pending-loans-requests-list", views.LoansPendingRequestListview.as_view()),
    path("approved-loans-requests-list", views.LoansApprovedRequestListview.as_view()),
    path("rejected-loans-requests-list", views.LoansRejectedRequestListview.as_view()),
    path("loan-detail-overview", views.LoanDetailOverview.as_view()),
    path("supervisor-loan-details", views.LoanDetailview.as_view()),
    path("supervisor-borrower-info", views.BorrowerInfoview.as_view()),
    path("supervisor-guarantor-info", views.GuarantorInfoview.as_view()),
    path("supervisor-agent-info", views.AgentInfoview.as_view()),
    path("borrower-crc-data", views.BorrowerCrcDataView.as_view()),
    path("borrower-credit-history-data", views.BorrowerCreditHistoryView.as_view()),
    path("borrower-spending-pattern", views.BorrowerSpendingPatternView.as_view()),
    path("borrower-transaction-history", views.BorrowerTransactionsHistoryView.as_view()),
    path("borrower-non-indebtedness-list", views.BorrowerNonIndebtednessDocsView.as_view()),
    path("supervisor-ready-to-disburse-loans-list", views.ReadyToDisburseLoansListView.as_view()),
    path("supervisor-disbursed-loans-list", views.SupervisorDisbursedLoansListView.as_view()),
    path("loans-disbursement-overview", views.LoansDisbursementOverview.as_view()),
    path("supervisor-disburse-ajo-loan", views.SupervisorDisburseAjoLoan.as_view()),
    path("supervisor-approve-ajo-loan", views.SupervisorApproveLoan.as_view()),
    path("supervisor-decline-ajo-loan", views.SupervisorDeclineLoan.as_view()),
    path("check-supervisor-role", views.CheckSupervisorRole.as_view()),
    path("supervisor-manual-resolve-transaction", views.SupervisorManualResolveTransactionView.as_view()),
    path("supervisor-decline-loan", views.SupervisorDeclineLoan.as_view()),
    path("pending-eligibility-list", views.PendingEligibilityListView.as_view()),
    path("eligibility-detail", views.EligibilityDetailView.as_view()),
    path("supervisor-approve-eligibility", views.SupervisorApproveLoanEligibilityView.as_view()),
    path("supervisor-pending-loan-approvals", views.PendingApprovalLoanListView.as_view()),
    path("supervisor-loan-review-confirmation", views.SupervisorManualLoanReviewConfirmationView.as_view()),
    path("due-collections-summary", views.DueCollectionsView.as_view()),
    path("ongoing-loans-request", views.OngoingLoansRequestListview.as_view()),
]

MISSED_REPAYMENTS = [
    path("missed-repayments-overview", views.MissedRepaymentOverview.as_view()),
    path("missed-repayments-list", views.MissedRepaymentsListview.as_view()),
    path("repayment-detail", views.RepaymentsDetailview.as_view()),
    path("agents-missed-repayments-chart", views.AgentsMissedRepaymentsChartview.as_view()),
    path("loan-officers-list", views.LoanOfficersListView.as_view()),
    path("branches-list", views.BranchesListView.as_view()),
    path("branches-supervisors-list", views.BranchesSupervisorsListView.as_view()),
    path("top-missed-repayment-branches", views.TopMissedBranchesView.as_view()),
    path("top-missed-repayment-loan-officers", views.TopMissedLoanOfficersView.as_view()),
    path("admin-missed-repayment-analysis", views.AdminMissedRepaymentAnalysisView.as_view()),
    path("admin-past-maturity-analysis", views.AdminPastMaturityAnalysisView.as_view()),
    path("admin-watch-list-analysis", views.AdminWatchListAnalysisView.as_view()),
    path("admin-default-analysis", views.AdminDefaultsAnalysisView.as_view()),
    path("missed-repayment-classifications", views.MissedRepaymentsClassificationView.as_view()),
    path("repayment-analysis-chart", views.RepaymentAnalysisChartView.as_view()),
]

SUPERVISOR_GROUP_LOANS_REQUEST_URLS = [
    path("group-loans-request", views.GroupLoansRequestListview.as_view()),
    path("pending-group-loans-request", views.PendingGroupLoansRequestListview.as_view()),
    path("approved-group-loans-request", views.ApprovedGroupLoansRequestListview.as_view()),
    path("loans-in-group-list", views.LoansInGroupRequestListview.as_view()),
    path("supervisor-disburse-group-loan", views.SupervisorGroupLoansDisbursementView.as_view()),
    path("supervisor-group-loan-review-confirmation", views.SupervisorManualGroupLoanReviewConfirmationView.as_view()),
]

urlpatterns = [
    path("borrowers-list", views.BorrowersListApiView.as_view()),
    path("borrowers-advance-search", views.BorrowersAdvanceSearchListApiView.as_view()),
    path("borrowers-details", views.BorrowerDetailsView.as_view()),
    path("loans-list", views.LoansListApiView.as_view()),
    path("loan-details", views.LoanDetailsApiView.as_view()),
    path("borrower-loans", views.BorrowerLoansApiView.as_view()),
    path("debt-collections-overview", views.DebtCollectionOverview.as_view()),
    path("all-outstanding-debtors-list", views.AllOutstandingDebtorsListview.as_view()),
    path("all-outstanding-ptp-debtors-list", views.AllOutstandingPtPDebtorsListview.as_view()),
    path("all-outstanding-no-promise-debtors-list", views.AllOutstandingDebtorsNoPromiseListview.as_view()),
    path("debtor-details", views.DebtorDetailsView.as_view()),
    *MISSED_REPAYMENTS,
    *SUPERVISOR_LOANS_REQUEST_URLS,
    *SUPERVISOR_GROUP_LOANS_REQUEST_URLS,
]