import json
from datetime import datetime, timedelta

from django.conf import settings
from django.db.models import Avg, <PERSON><PERSON><PERSON><PERSON>, Count, F, Sum, Value
from django.db.models.functions import Concat
from django.utils import timezone
from django.core.cache import cache

from accounts.agency_banking import AgencyBankingClass
from accounts.models import CustomUser
from admin_dashboard.helpers.helpers import get_date_filter, get_percentage_diff
from admin_dashboard.services import DateUtility
from ajo.models import AjoSaving, AjoSepo, AjoUserWithdrawalAccount, BankAccountDetails
from collections_app.helpers.helpers import (
    LoanDiskClass,
    get_agent_supervisor_details,
    get_branch_loan_officers,
    get_loan_age,
    get_loan_branches_list,
    get_supervisor_loan_officers,
    get_total_due_repayment_qs,
)
from loans.enums import LoanStatus, UserVerificationType
from loans.models import (
    AjoLoan,
    AjoLoanRepayment,
    BiometricsMetaData,
    BorrowerInfo,
    LoanKYCDocumentation,
    Missed28Days,
    Missed60Days,
    Missed90Days,
    MissedPastMaturity15Days,
    MissedRepaymentsTable,
    YouVerifyRequest,
)
from payment.models import DebitCreditRecordOnAccount, Transaction
from collections_app.models import AgentDailyAttendance
from collections_app.enums import UserTypes
from collections_app.helpers.helpers import SupervisorClass

non_performing_loan_status_list = ["LOST", "DEFAULT"]
declined_loans_list = ["REJECTED", "DENIED", "DECLINED_BY_SUPERVISOR"]
ongoing_loans_request_status_list = ["PROCESSING", "IN_PROGRESS"]


class DebtCollectionClass:
    def get_loan_performance_insight(self):
        first_call = LoanDiskClass().fetch_all_loans(page_number=1, size=1)

        total_results = first_call.get("response").get("TotalResults")

        all_loans_list = []
        total_count = 0
        page_number = 0

        # while total_count < total_results:
        #     page_number += 1
        # Fetch loans
        all_loans = LoanDiskClass().fetch_all_loans(page_number=page_number, size=10)
        # loans_list = all_loans.get("response").get("Results")[0]
        # all_loans_list += loans_list
        return_results = all_loans.get("response").get("ReturnResults")
        # total_count += return_results

        # sample_loan_data = {'loan_id': 1867552, 'loan_product_id': 29865, 'borrower_id': 1678269, 'loan_application_id': '1000001',
        #         'loan_disbursed_by_id': 22751, 'loan_principal_amount': '200000.00', 'loan_released_date':
        #         '04/03/2021', 'loan_interest_method': 'flat_rate', 'loan_interest_type': 'fixed',
        #         'loan_interest_period': 'Month', 'loan_interest': '5000.0000', 'loan_duration_period': 'Years',
        #         'loan_duration': 1, 'loan_payment_scheme_id': 1123, 'loan_num_of_repayments': 12,
        #         'loan_restructure_principal_amount': '0.00', 'loan_decimal_places': 'round_off_to_two_decimal',
        #         'loan_interest_start_date': None, 'loan_fees_pro_rata': None, 'loan_do_not_adjust_remaining_pro_rata': None,
        #         'loan_first_repayment_pro_rata': None, 'loan_first_repayment_date': None,
        #         'first_repayment_amount': None, 'last_repayment_amount': None, 'loan_override_maturity_date': None,
        #         'override_each_repayment_amount': None, 'loan_interest_each_repayment_pro_rata': None,
        #         'loan_interest_schedule': None, 'loan_principal_schedule': None, 'loan_balloon_repayment_amount': None,
        #         'loan_move_first_repayment_date_days': None, 'automatic_payments': None, 'payment_posting_period': None,
        #         'automated_payments_dea_cash_bank_account': None, 'after_maturity_extend_loan': None,
        #         'after_maturity_percentage_or_fixed': None, 'after_maturity_calculate_interest_on': None,
        #         'after_maturity_loan_interest': None, 'after_maturity_recurring_period_num': None,
        #         'after_maturity_recurring_period_payment_scheme_id': None, 'after_maturity_include_fees': None,
        #         'after_maturity_past_maturity_status': None, 'after_maturity_apply_to_date': None,
        #         'total_amount_due': '260000.00', 'past_due': 0,
        #         'amortization': 259999.98, 'pending_due': 259999.98, 'pending_due_principal': 200000,
        #         'pending_due_interest': 59999.98, 'pending_due_fees': 0, 'pending_due_penalty': 0,
        #         'days_past_due': 1071, 'principal_balance_amount': 200000, 'interest_balance_amount': 59999.98,
        #         'fees_balance_amount': 0, 'penalty_balance_amount': 0, 'restructured_loan_history': [],
        #         'balance_amount': '259999.98', 'due_date': '28/02/2022', 'total_paid': '0.02', 'child_status_id': 6,
        #         'loan_fee_schedule_1822': None, 'loan_fee_id_1822': 0, 'loan_fee_schedule_5999': None, 'loan_fee_id_5999': 0,
        #         'loan_fee_schedule_5998': None, 'loan_fee_id_5998': 0, 'loan_fee_schedule_5997': None, 'loan_fee_id_5997': 0,
        #         'loan_fee_schedule_5996': None, 'loan_fee_id_5996': 0, 'loan_fee_schedule_5995': None, 'loan_fee_id_5995': 0,
        #         'loan_fee_schedule_5994': None, 'loan_fee_id_5994': 0, 'loan_fee_schedule_2761': None, 'loan_fee_id_2761': 0,
        #         'loan_fee_schedule_2703': None, 'loan_fee_id_2703': 0, 'loan_fee_schedule_1821': None, 'loan_fee_id_1821': 0,
        #         'loan_fee_schedule_1823': None, 'loan_fee_id_1823': 0, 'loan_fee_schedule_1824': None, 'loan_fee_id_1824': 0,
        #         'loan_fee_schedule_1826': None, 'loan_fee_id_1826': 0, 'loan_fee_id_4142': 0, 'loan_fee_id_2746': 0,
        #         'loan_fee_id_3915': 0, 'loan_fee_id_4002': 0, 'loan_fee_id_1825': 0, 'loan_fee_id_4003': 0, 'loan_fee_id_1914': 0,
        #         'loan_fee_id_4153': 0, 'loan_fee_id_4152': 0, 'loan_fee_id_4004': 0, 'loan_fee_id_4141': 0, 'loan_fee_id_4140': 0,
        #         'loan_fee_id_4139': 0, 'loan_fee_id_4052': 0, 'loan_fee_id_4051': 0, 'loan_fee_id_4006': 0, 'loan_fee_id_4005': 0,
        #         'loan_override_sys_gen_penalty': 0, 'loan_manual_penalty_amount': '0.00', 'loan_status_id': 1, 'dea_cash_bank_account': None,
        #         'loan_title': None, 'loan_description': '', 'custom_field_4287': '200000', 'custom_fields': [{'id': 'custom_field_4287',
        #         'name': 'Amount requested'}, {'id': 'custom_field_4362', 'name': 'Gender'}, {'id': 'custom_field_4221',
        #         'name': 'Bank Name'}, {'id': 'custom_field_4222', 'name': 'Bank Code'}, {'id': 'custom_field_5040',
        #         'name': 'Marital  Status'}], 'custom_field_4362': 'Male', 'borrower_country': 'NG', 'borrower_fullname': None,
        #         'borrower_firstname': 'Test', 'borrower_lastname': '', 'borrower_business_name': '',
        #         'borrower_unique_number': '1000001', 'borrower_gender': 'Male', 'borrower_title': 1, 'borrower_mobile': '',
        #         'borrower_email': '', 'borrower_dob': None, 'borrower_address': '', 'borrower_city': '', 'borrower_province': '',
        #         'borrower_zipcode': '', 'borrower_landline': '', 'borrower_working_status': '', 'borrower_credit_score': '',
        #         'borrower_description': '', 'borrower_access_ids': [], 'custom_field_4221': 'BankName', 'custom_field_4222': '**********',
        #         'custom_field_5040': 'Single', 'borrower_photo': None
        #         }

        total_principal_amount = 0
        total_principal_count = 0
        total_interest_accrued_amount = 0
        total_interest_accrued_count = 0
        total_overdue_amount = 0
        total_overdue_count = 0

        for loan in all_loans_list:
            loan_id = loan.get("loan_id")
            loan_principal_amount = loan.get("loan_principal_amount")
            loan_released_date = loan.get("loan_released_date")
            loan_interest_period = loan.get("loan_interest_period")
            loan_interest = loan.get("loan_interest")
            loan_duration_period = loan.get("loan_duration_period")
            loan_duration = loan.get("loan_duration")
            loan_num_of_repayments = loan.get("loan_num_of_repayments")
            total_amount_due = loan.get("total_amount_due")
            past_due = loan.get("past_due")
            amortization = loan.get("amortization")
            pending_due = loan.get("pending_due")
            pending_due_principal = loan.get("pending_due_principal")
            pending_due_interest = loan.get("pending_due_interest")
            balance_amount = loan.get("balance_amount")
            principal_balance_amount = loan.get("principal_balance_amount")
            interest_balance_amount = loan.get("interest_balance_amount")

            total_principal_amount += float(principal_balance_amount)
            total_principal_count += 1
            total_interest_accrued_amount += float(interest_balance_amount)
            total_interest_accrued_count += 1
            total_overdue_amount += float(total_amount_due)

            if float(total_amount_due) > 0:
                total_overdue_count += 1

            # Loan Repayment
            loan_repayments_list = []
            repayments_list = LoanDiskClass().get_loan_repayments(
                loan_id=loan_id, page_number=1, size=10
            )

        average_overdue_amount = (
            total_overdue_amount // total_overdue_count if total_overdue_count else 1
        )
        data = {
            "total_principal_due": {
                "amount": total_principal_amount,
                "count": total_principal_count,
                "change": "up",
                "percentage": 2,
            },
            "interest_accrued": {
                "amount": total_interest_accrued_amount,
                "count": total_interest_accrued_count,
                "change": "up",
                "percentage": 2,
            },
            "overdue": {
                "amount": total_overdue_amount,
                "count": total_overdue_count,
                "change": "none",
                "percentage": 0,
            },
            "average_overdue": {
                "amount": average_overdue_amount,
                "count": 0,
                "change": "none",
                "percentage": 0,
            },
            "interest_repaid": {
                "amount": average_overdue_amount,
                "count": 12,
                "change": "up",
                "percentage": 80,
            },
        }

        return data

    def get_borrowers_details_list(self, page_number, size):
        """
        Function to organise borrowers data
        """
        borrowers_data_list = []
        borrowers = LoanDiskClass().fetch_borrowers_list(page_number, size)
        if borrowers and borrowers.get("response"):
            borrowers_list = borrowers.get("response").get("Results")[0]
        else:
            return {}

        for borrower in borrowers_list:
            borrower_id = borrower.get("borrower_id")
            borrower_fullname = borrower.get("borrower_fullname")
            borrower_firstname = borrower.get("borrower_firstname")
            borrower_lastname = borrower.get("borrower_lastname")

            borrower_loans = LoanDiskClass().get_borrower_loans(
                borrower_id=borrower_id, page_number=1, size=500
            )
            borrower_loans_list = borrower_loans.get("response").get("Results")[0]

            if borrower_loans_list:
                loan = borrower_loans_list[-1]
                loan_principal_amount = loan.get("loan_principal_amount", 0)
                total_paid_amount = int(loan.get("total_paid_amount", 0))
                total_amount_due = float(loan.get("total_amount_due", 0))
                loan_disbursed_by_id = loan.get("loan_disbursed_by_id")
                past_due = loan.get("past_due", 0)
                loan_disbursed_by_id = loan.get("loan_disbursed_by_id")
                days_past_due = loan.get("days_past_due")
            else:
                total_loan_amount = 0
                total_repaid_amount = 0
                past_due = 0
                loan_disbursed_by_id = ""
                days_past_due = 0

            borrower_details = {
                "borrower_id": borrower_id,
                "fullname": borrower_firstname + " " + borrower_lastname,
                "loan_amount": total_amount_due,
                "repaid": total_paid_amount,
                "overdue": past_due,
                "days_past_due": days_past_due,
                "agent": loan_disbursed_by_id,
                "last_contact_date": datetime.now() - timedelta(days=1),
                "last_action": "",
                "comments": "Initial comments",
                "suggestion": "Call back tomorrow",
            }
            borrowers_data_list.append(borrower_details)

        return {"data": borrowers_data_list, "count": len(borrowers_data_list)}

    def get_debtor_details(self, borrower_id):
        data = {}
        # Fetch borrower's details
        loan_disk_details = LoanDiskClass().fetch_borrowers_details(
            borrower_id=borrower_id
        )
        details = loan_disk_details.get("response").get("Results")

        if details:
            loan_disk_borrower_details = details[0]
        else:
            return loan_disk_details

        # Fetch borrower's active Loans
        existing_loans = LoanDiskClass().get_borrower_loans(
            borrower_id, page_number=1, size=500
        )
        active_loan = existing_loans.get("response").get("Results")[0]
        if active_loan:
            active_loan = active_loan[-1]
            loan_amount = active_loan.get("loan_principal_amount")
            age_of_loan = active_loan.get("loan_principal_amount")
            total_amount_due = active_loan.get("total_amount_due")
            loan_released_date = active_loan.get("loan_released_date")
            loan_duration = active_loan.get("loan_duration")
            days_past_due = active_loan.get("days_past_due")
            loan_disbursed_by_id = active_loan.get("loan_disbursed_by_id")
            loan_id = active_loan.get("loan_id")

            # Loan Repayment
            repayments = LoanDiskClass().get_loan_repayments(
                loan_id=loan_id, page_number=1, size=500
            )
            repayments_list = repayments.get("response").get("Results")[0]
        else:
            age_of_loan = 0
            total_amount_due = 0
            loan_released_date = ""
            loan_amount = 0
            loan_duration = 0
            days_past_due = 0
            loan_disbursed_by_id = ""

        data = {
            "customer_id": loan_disk_borrower_details.get("borrower_id"),
            "full_name": loan_disk_borrower_details.get("borrower_firstname", "")
            + " "
            + loan_disk_borrower_details.get("borrower_lastname", ""),
            "status": "",
            "agent_id": loan_disbursed_by_id,
            "agent_name": "",
            "phone_number": loan_disk_borrower_details.get("borrower_mobile"),
            "email": loan_disk_borrower_details.get("borrower_email"),
            "loan_amount": loan_amount,
            "age_of_loan": 0,
            "loan_tenure": loan_duration,
            "days_past_due": days_past_due,
            "days_past_maturity": 0,
            "overdue_amount": total_amount_due,
            "next_payment_date": datetime.now() + timedelta(days=2),
            "next_payment_amount": 0,
            "comment": "",
        }
        return data


class LoansRequestsClass:
    def __init__(self, request):
        # Get Supervisor USERS
        self.date_filter, self.disburse_filter = get_date_filter(
            filter_value=request.query_params.get("filter")
        )
        self.request = request
        user = self.request.user
        # agency_banking_supervisors = AgencyBankingClass.get_agency_supervisor_details()
        # agency_banking_acquisition_officers = (
        #     AgencyBankingClass.get_agency_acquisition_officer_details(
        #         agent_id=user.customer_user_id
        #     )
        # )

        # merchants_list = []
        # self.supervisor_users_list = []
        # if agency_banking_acquisition_officers.get("data"):
        #     merchant_dicts_list = agency_banking_acquisition_officers.get(
        #         "data", {}
        #     ).get("merchants", {})
        #     merchants_list += [
        #         merchant.get("user_id") for merchant in merchant_dicts_list
        #     ]

        # if (
        #     "success" in agency_banking_supervisors
        #     and agency_banking_supervisors.get("success") == True
        # ):
        #     supervisors_list = agency_banking_supervisors.get("supervisors_teams_list")

        #     for supervisor in supervisors_list:
        #         if supervisor.get("supervisor_user_id") == user.customer_user_id:
        #             self.supervisor_users_list = supervisor.get("users_list", [])
        #             break
        # else:
        #     self.supervisor_users_list = []
        # self.supervisor_users_list += merchants_list

        self.supervisor_users_list = SupervisorClass().get_supervisor_users(user)

        # print(":::::::::::::::::::::::::::")
        # print("::::::::::::::PRINT SUPERVISOR USERS LIST:::::::::::::")
        # print("::::::::::::::PRINT SUPERVISOR USERS LIST:::::::::::::")
        # print("::::::::::::::PRINT SUPERVISOR USERS LIST:::::::::::::")
        # print("::::::::::::::PRINT SUPERVISOR USERS LIST:::::::::::::")
        # print(self.supervisor_users_list)
        # print(":::::::::::::::::::::::::::")
        # print("::::::::::::::PRINT SUPERVISOR USERS LIST:::::::::::::")
        # print("::::::::::::::PRINT SUPERVISOR USERS LIST:::::::::::::")
        # # print(merchants_list)
        # print("::::::::::::::PRINT SUPERVISOR USERS LIST:::::::::::::")
        # print("::::::::::::::PRINT SUPERVISOR USERS LIST:::::::::::::")
        # # print(agency_banking_acquisition_officers)
        # print("::::::::::::::PRINT SUPERVISOR USERS LIST:::::::::::::")
        # print(":::::::::::::::::::::::::::")

        self.previous_day = DateUtility().previous_day.date()
        self.week_start = DateUtility().week_start
        self.month_start = DateUtility().month_start

        if request.user.is_staff:
            self.ajo_loan_qs = AjoLoan.objects.filter()  # **date_filter)
        else:
            self.ajo_loan_qs = AjoLoan.objects.filter(
                # **date_filter,
                agent__customer_user_id__in=self.supervisor_users_list
            )

        self.approved_loans_qs = self.ajo_loan_qs.filter(
            is_disbursed=True, **self.disburse_filter
        )
        self.declined_loans_qs = self.ajo_loan_qs.filter(
            status__in=declined_loans_list, **self.date_filter
        )
        self.ongoing_loans_application_qs = self.ajo_loan_qs.filter(
            status__in=ongoing_loans_request_status_list, **self.date_filter
        )
        self.disbursed_loans_qs = self.ajo_loan_qs.filter(
            is_disbursed=True,
            status__in=[LoanStatus.OPEN, LoanStatus.COMPLETED, LoanStatus.DEFAULTED],
            # supervisor_disbursement_status="SUCCESSFUL",
            **self.disburse_filter,
        )

    def get_loans_requests_overview(self):
        # Loans Request
        loans_request_amount_till_yesterday = list(
            self.ajo_loan_qs.filter(created_at__date__lte=self.previous_day)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        loans_request_count_till_yesterday = list(
            self.ajo_loan_qs.filter(created_at__date__lte=self.previous_day)
            .aggregate(Count("amount"))
            .values()
        )[0]

        loans_request_amount = list(
            self.ajo_loan_qs.filter(**self.date_filter)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        loans_request_count = list(
            self.ajo_loan_qs.filter(**self.date_filter)
            .aggregate(Count("amount"))
            .values()
        )[0]

        get_loans_request_amount_diff = get_percentage_diff(
            previous=loans_request_amount_till_yesterday, current=loans_request_amount
        )

        get_loans_request_count_diff = get_percentage_diff(
            previous=loans_request_count_till_yesterday, current=loans_request_count
        )

        # Approved Loans
        approved_loans_amount_till_yesterday = list(
            self.approved_loans_qs.filter(created_at__date__lte=self.previous_day)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        approved_loans_count_till_yesterday = list(
            self.approved_loans_qs.filter(created_at__date__lte=self.previous_day)
            .aggregate(Count("amount"))
            .values()
        )[0]

        approved_loans_amount = list(
            self.approved_loans_qs.aggregate(Sum("amount")).values()
        )[0]
        approved_loans_count = list(
            self.approved_loans_qs.aggregate(Count("amount")).values()
        )[0]

        get_approved_loans_amount_diff = get_percentage_diff(
            previous=approved_loans_amount_till_yesterday, current=approved_loans_amount
        )

        get_approved_loans_count_diff = get_percentage_diff(
            previous=approved_loans_count_till_yesterday, current=approved_loans_count
        )

        # Declined Loans
        declined_loans_amount_till_yesterday = list(
            self.declined_loans_qs.filter(created_at__date__lte=self.previous_day)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        declined_loans_count_till_yesterday = list(
            self.declined_loans_qs.filter(created_at__date__lte=self.previous_day)
            .aggregate(Count("amount"))
            .values()
        )[0]

        declined_loans_amount = list(
            self.declined_loans_qs.aggregate(Sum("amount")).values()
        )[0]
        declined_loans_count = list(
            self.declined_loans_qs.aggregate(Count("amount")).values()
        )[0]

        get_declined_loans_amount_diff = get_percentage_diff(
            previous=declined_loans_amount_till_yesterday, current=declined_loans_amount
        )

        get_declined_loans_count_diff = get_percentage_diff(
            previous=declined_loans_count_till_yesterday, current=declined_loans_count
        )

        # Ongoing Loans Application
        ongoing_loans_application_amount_till_yesterday = list(
            self.ongoing_loans_application_qs.filter(
                created_at__date__lte=self.previous_day
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]
        ongoing_loans_application_count_till_yesterday = list(
            self.ongoing_loans_application_qs.filter(
                created_at__date__lte=self.previous_day
            )
            .aggregate(Count("amount"))
            .values()
        )[0]

        ongoing_loans_application_amount = list(
            self.ongoing_loans_application_qs.aggregate(Sum("amount")).values()
        )[0]
        ongoing_loans_application_count = list(
            self.ongoing_loans_application_qs.aggregate(Count("amount")).values()
        )[0]

        get_ongoing_loans_application_amount_diff = get_percentage_diff(
            previous=ongoing_loans_application_amount_till_yesterday,
            current=ongoing_loans_application_amount,
        )

        get_ongoing_loans_application_count_diff = get_percentage_diff(
            previous=ongoing_loans_application_count_till_yesterday,
            current=ongoing_loans_application_count,
        )

        # External Disbursements
        disbursed_loans_amount_till_yesterday = list(
            self.disbursed_loans_qs.filter(created_at__date__lte=self.previous_day)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        disbursed_loans_count_till_yesterday = list(
            self.disbursed_loans_qs.filter(created_at__date__lte=self.previous_day)
            .aggregate(Count("amount"))
            .values()
        )[0]

        disbursed_loans_amount = list(
            self.disbursed_loans_qs.aggregate(Sum("amount")).values()
        )[0]
        disbursed_loans_count = list(
            self.disbursed_loans_qs.aggregate(Count("amount")).values()
        )[0]

        get_disbursed_loans_amount_diff = get_percentage_diff(
            previous=disbursed_loans_amount_till_yesterday,
            current=disbursed_loans_amount,
        )

        get_disbursed_loans_count_diff = get_percentage_diff(
            previous=disbursed_loans_count_till_yesterday, current=disbursed_loans_count
        )

        data = {
            "loans_requests": {
                "amount": loans_request_amount if loans_request_amount else 0.00,
                "amount_change": get_loans_request_amount_diff.get("change"),
                "amount_percentage": get_loans_request_amount_diff.get("percentage"),
                "count": loans_request_count if loans_request_count else 0,
                "count_change": get_loans_request_count_diff.get("change"),
                "count_percentage": get_loans_request_count_diff.get("percentage"),
            },
            "approved_loans": {
                "amount": approved_loans_amount if approved_loans_amount else 0.00,
                "amount_change": get_approved_loans_amount_diff.get("change"),
                "amount_percentage": get_approved_loans_amount_diff.get("percentage"),
                "count": approved_loans_count if approved_loans_count else 0,
                "count_change": get_approved_loans_count_diff.get("change"),
                "count_percentage": get_approved_loans_count_diff.get("percentage"),
            },
            "declined_loans": {
                "amount": declined_loans_amount if declined_loans_amount else 0.00,
                "amount_change": get_declined_loans_amount_diff.get("change"),
                "amount_percentage": get_declined_loans_amount_diff.get("percentage"),
                "count": declined_loans_count if declined_loans_count else 0,
                "count_change": get_declined_loans_count_diff.get("change"),
                "count_percentage": get_declined_loans_count_diff.get("percentage"),
            },
            "ongoing_applications": {
                "amount": (
                    ongoing_loans_application_amount
                    if ongoing_loans_application_amount
                    else 0.00
                ),
                "amount_change": get_ongoing_loans_application_amount_diff.get(
                    "change"
                ),
                "amount_percentage": get_ongoing_loans_application_amount_diff.get(
                    "percentage"
                ),
                "count": (
                    ongoing_loans_application_count
                    if ongoing_loans_application_count
                    else 0
                ),
                "count_change": get_ongoing_loans_application_count_diff.get("change"),
                "count_percentage": get_ongoing_loans_application_count_diff.get(
                    "percentage"
                ),
            },
            "disbursed_loans": {
                "amount": disbursed_loans_amount if disbursed_loans_amount else 0.00,
                "amount_change": get_disbursed_loans_amount_diff.get("change"),
                "amount_percentage": get_disbursed_loans_amount_diff.get("percentage"),
                "count": disbursed_loans_count if disbursed_loans_count else 0,
                "count_change": get_disbursed_loans_count_diff.get("change"),
                "count_percentage": get_disbursed_loans_count_diff.get("percentage"),
            },
        }
        return data

    def get_all_loans_request_list(self):
        loans_requests_list = self.ajo_loan_qs.annotate(
            loan_id=F("id"),
            borrower_name=Concat(
                "borrower__first_name",
                Value(" "),
                "borrower__last_name",
                output_field=CharField(),
            ),
            loan_amount=F("amount"),
            loan_term=F("tenor"),
            applied_date=F("created_at"),
            agent_fullname=Concat(
                "agent__first_name",
                Value(" "),
                "agent__last_name",
                output_field=CharField(),
            ),
            branch=Value(""),
            loan_status=F("status"),
            agent_name=F("agent__username"),
        ).values(
            "loan_id",
            "borrower_name",
            "loan_amount",
            "loan_term",
            "applied_date",
            "agent_name",
            "branch",
            "loan_status",
            "verification_stage",
            "agent_fullname",
            "supervisor_disbursement_status",
            "date_disbursed",
        )

        data = {"data": loans_requests_list, "count": loans_requests_list.count()}
        return data

    def get_pending_loans_request_list(self):
        pending_loans_request_qs = self.ajo_loan_qs.filter(status="PENDING")
        loans_requests_list = pending_loans_request_qs.annotate(
            loan_id=F("id"),
            borrower_name=Concat(
                "borrower__first_name",
                Value(" "),
                "borrower__last_name",
                output_field=CharField(),
            ),
            loan_amount=F("amount"),
            loan_term=F("tenor"),
            applied_date=F("created_at"),
            agent_fullname=Concat(
                "agent__first_name",
                Value(" "),
                "agent__last_name",
                output_field=CharField(),
            ),
            branch=Value(""),
            loan_status=F("status"),
            agent_name=F("agent__username"),
        ).values(
            "loan_id",
            "borrower_name",
            "loan_amount",
            "loan_term",
            "applied_date",
            "agent_name",
            "branch",
            "loan_status",
            "agent_fullname",
            "verification_stage",
        )

        data = {"data": loans_requests_list, "count": loans_requests_list.count()}
        return data

    def get_approved_loans_request_list(self):
        approved_loans_request_qs = self.ajo_loan_qs.filter(status="APPROVED")
        loans_requests_list = approved_loans_request_qs.annotate(
            loan_id=F("id"),
            borrower_name=Concat(
                "borrower__first_name",
                Value(" "),
                "borrower__last_name",
                output_field=CharField(),
            ),
            loan_amount=F("amount"),
            loan_term=F("tenor"),
            applied_date=F("created_at"),
            agent_fullname=Concat(
                "agent__first_name",
                Value(" "),
                "agent__last_name",
                output_field=CharField(),
            ),
            branch=Value(""),
            loan_status=F("status"),
            agent_name=F("agent__username"),
        ).values(
            "loan_id",
            "borrower_name",
            "loan_amount",
            "loan_term",
            "applied_date",
            "agent_name",
            "branch",
            "loan_status",
            "agent_fullname",
            "verification_stage",
        )

        data = {"data": loans_requests_list, "count": loans_requests_list.count()}
        return data

    def get_rejected_loans_request_list(self):
        rejected_loans_request_qs = self.ajo_loan_qs.filter(status="REJECTED")
        loans_requests_list = rejected_loans_request_qs.annotate(
            loan_id=F("id"),
            borrower_name=Concat(
                "borrower__first_name",
                Value(" "),
                "borrower__last_name",
                output_field=CharField(),
            ),
            loan_amount=F("amount"),
            loan_term=F("tenor"),
            applied_date=F("created_at"),
            agent_fullname=Concat(
                "agent__first_name",
                Value(" "),
                "agent__last_name",
                output_field=CharField(),
            ),
            branch=Value(""),
            loan_status=F("status"),
            agent_name=F("agent__username"),
        ).values(
            "loan_id",
            "borrower_name",
            "loan_amount",
            "loan_term",
            "applied_date",
            "agent_name",
            "branch",
            "loan_status",
            "agent_fullname",
            "verification_stage",
        )

        data = {"data": loans_requests_list, "count": loans_requests_list.count()}
        return data

    def get_ongoing_loans_request_list(self):
        loans_requests_list = (
            self.ajo_loan_qs.filter(status__in=ongoing_loans_request_status_list)
            .annotate(
                loan_id=F("id"),
                borrower_name=Concat(
                    "borrower__first_name",
                    Value(" "),
                    "borrower__last_name",
                    output_field=CharField(),
                ),
                loan_amount=F("amount"),
                loan_term=F("tenor"),
                applied_date=F("created_at"),
                agent_fullname=Concat(
                    "agent__first_name",
                    Value(" "),
                    "agent__last_name",
                    output_field=CharField(),
                ),
                branch=Value(""),
                loan_status=F("status"),
                agent_name=F("agent__username"),
            )
            .values(
                "loan_id",
                "borrower_name",
                "loan_amount",
                "loan_term",
                "applied_date",
                "agent_name",
                "branch",
                "loan_status",
                "verification_stage",
                "agent_fullname",
            )
        )

        data = {"data": loans_requests_list, "count": loans_requests_list.count()}
        return data


class LoanDetailClass:
    def __init__(self, request):
        self.request = request
        filter, disburse_filter = get_date_filter(
            filter_value=request.query_params.get("filter")
        )
        # Get Supervisor USERS
        self.request = request
        user = self.request.user
        # agency_banking_supervisors = AgencyBankingClass.get_agency_supervisor_details()
        # if (
        #     "success" in agency_banking_supervisors
        #     and agency_banking_supervisors.get("success") == True
        # ):
        #     supervisors_list = agency_banking_supervisors.get("supervisors_teams_list")

        #     for supervisor in supervisors_list:
        #         if supervisor.get("supervisor_user_id") == user.customer_user_id:
        #             self.supervisor_users_list = supervisor.get("users_list", [])
        #             break
        # else:
        #     self.supervisor_users_list = []
        # self.supervisor_users_list += [1, 2, 3, 5]
        self.supervisor_users_list = SupervisorClass().get_supervisor_users(user)

        if self.request.user.is_staff:
            self.ajo_loan_qs = AjoLoan.objects.filter()
        else:
            self.ajo_loan_qs = AjoLoan.objects.filter(
                agent__customer_user_id__in=self.supervisor_users_list
            )

        self.ajo_savings_transactions_qs = Transaction.objects.filter(
            plan_type="AJO",
            wallet_type="AJO_USER",
            status="SUCCESS",
            transaction_form_type="WALLET_DEPOSIT",
        )
        self.borrower_info_qs = BorrowerInfo.objects.all()
        self.bank_account_details_qs = BankAccountDetails.objects.all()
        self.external_bank_account_details_qs = AjoUserWithdrawalAccount.objects.all()
        self.biometric_data_qs = BiometricsMetaData.objects.all()
        self.debit_credit_qs = DebitCreditRecordOnAccount.objects.filter()
        self.repayment_qs = AjoLoanRepayment.objects.filter()
        self.youverify_data = YouVerifyRequest.objects.all()
        self.loan_kyc_doc = LoanKYCDocumentation.objects.all()

        # Define Dates
        self.month_start = DateUtility().month_start

    def get_loan_detail_overview(self):
        loan_id = self.request.query_params.get("loan_id")

        if loan_id and isinstance(int(loan_id), int):
            loan_obj = self.ajo_loan_qs.filter(id=int(loan_id)).last()

            if loan_obj:
                # Borrower Details
                borrower = loan_obj.borrower
                total_savings_amount = list(
                    self.ajo_savings_transactions_qs.filter(onboarded_user=borrower)
                    .aggregate(Sum("amount"))
                    .values()
                )[0]
                total_savings_amount_month = list(
                    self.ajo_savings_transactions_qs.filter(
                        date_created__date__gte=self.month_start,
                        onboarded_user=borrower,
                    )
                    .aggregate(Sum("amount"))
                    .values()
                )[0]

                # BORROWER INFO
                borrower_info = self.borrower_info_qs.filter(borrower=borrower).last()

                data = {
                    "borrower_details": {
                        "name": borrower.fullname,
                        "user_id": borrower.id,
                        "email": "",  # borrower.email,
                        "phone_number": borrower.phone,
                        "address": borrower.address if borrower.address else "",
                        "city": borrower.lga if borrower.lga else "",
                    },
                    "demographics": {
                        "age": borrower.age if borrower.age else "",
                        "gender": borrower.gender if borrower.gender else "",
                        "marital_status": (
                            borrower.marital_status if borrower.marital_status else ""
                        ),
                        "weekly_income": (
                            borrower_info.weekly_income
                            if borrower_info and borrower_info.weekly_income
                            else 0.00
                        ),
                        "daily_income": (
                            borrower_info.daily_income
                            if borrower_info and borrower_info.daily_income
                            else 0.00
                        ),
                        "income": (
                            borrower_info.weekly_income
                            if borrower_info and borrower_info.weekly_income
                            else 0.00
                        ),
                    },
                    "work_records": {
                        "status": "",
                        "employer": borrower.trade if borrower.trade else "",
                        "address": (
                            borrower.trade_location if borrower.trade_location else ""
                        ),
                        "shop_rent": (
                            borrower_info.annual_shop_rent
                            if borrower_info and borrower_info.annual_shop_rent
                            else ""
                        ),
                    },
                    "loan_overview_data": {
                        "request_amount": loan_obj.amount,
                        "loan_term": loan_obj.tenor,
                        "verification_stage": loan_obj.verification_stage,
                        "total_interest": loan_obj.interest_amount,
                        "annual_interest_rate": loan_obj.interest_amount,
                        "monthly_repayment": "",
                        "monthly_interest_rate": "",
                        "daily_repayment": loan_obj.daily_repayment_amount,
                        "daily_interest_rate": "",
                        "toal_savings_amount": (
                            total_savings_amount if total_savings_amount else 0.00
                        ),
                        "total_monthly_savings_amount": (
                            total_savings_amount_month
                            if total_savings_amount_month
                            else 0.00
                        ),
                    },
                }
                return data
            else:
                return {"error": "Loan Record does not exist"}
        else:
            return {"error": "Invalid loan id provided"}

    def get_loan_details(self):
        loan_id = self.request.query_params.get("loan_id")

        if loan_id and isinstance(int(loan_id), int):
            loan_obj = self.ajo_loan_qs.filter(id=int(loan_id)).last()

            if loan_obj:
                # Borrower Details
                borrower = loan_obj.borrower
                total_savings_amount = list(
                    self.ajo_savings_transactions_qs.filter(onboarded_user=borrower)
                    .aggregate(Sum("amount"))
                    .values()
                )[0]
                total_savings_amount_month = list(
                    self.ajo_savings_transactions_qs.filter(
                        date_created__date__gte=self.month_start,
                        onboarded_user=borrower,
                    )
                    .aggregate(Sum("amount"))
                    .values()
                )[0]
                borrower_info = self.borrower_info_qs.filter(borrower=borrower).last()
                bank_details = self.external_bank_account_details_qs.filter(
                    ajo_user=borrower
                ).last()

                data = {
                    "loan_id": loan_obj.id,
                    "request_amount": loan_obj.amount,
                    "loan_purpose": "",
                    "account_name": (
                        bank_details.account_name
                        if bank_details and bank_details.account_name
                        else ""
                    ),
                    "account_number": (
                        bank_details.account_number
                        if bank_details and bank_details.account_number
                        else ""
                    ),
                    "bank": (
                        bank_details.bank_name
                        if bank_details and bank_details.bank_name
                        else ""
                    ),
                    "loan_type": loan_obj.loan_type,
                    "loan_term": loan_obj.tenor,
                    "date_applied": loan_obj.created_at,
                    "loan_status": loan_obj.status,
                    "verification_stage": loan_obj.verification_stage,
                    "due_date": loan_obj.end_date,
                    "total_interest": loan_obj.interest_amount,
                    "annual_interest_rate": loan_obj.interest_amount,
                    "monthly_repayment": "",
                    "monthly_interest_rate": "",
                    "daily_repayment": loan_obj.daily_repayment_amount,
                    "daily_interest_rate": "",
                    "toal_savings_amount": (
                        total_savings_amount if total_savings_amount else 0.00
                    ),
                    "total_monthly_savings_amount": (
                        total_savings_amount_month
                        if total_savings_amount_month
                        else 0.00
                    ),
                    "requires_consent": loan_obj.requires_consent,
                }
                return data
            else:
                return {"error": "Loan Record does not exist"}
        else:
            return {"error": "Invalid loan id provided"}

    def get_borrower_info(self):
        loan_id = self.request.query_params.get("loan_id")

        if loan_id and isinstance(int(loan_id), int):
            loan_obj = self.ajo_loan_qs.filter(id=int(loan_id)).last()

            if loan_obj:
                # Borrower Details
                borrower = loan_obj.borrower

                # Borrower Info
                borrower_info = self.borrower_info_qs.filter(borrower=borrower).last()
                biometrics_meta_data = self.biometric_data_qs.filter(
                    borrower_info=borrower_info,
                    request_type="FACE_MATCH",
                    user_type=UserVerificationType.BORROWER,
                ).last()

                if biometrics_meta_data:
                    borrower_image_upload = (
                        biometrics_meta_data.base64_img_str
                        if biometrics_meta_data
                        else ""
                    )
                else:
                    borrower_image_upload = borrower_info.snapped_image

                # you_verify_data_bvn = self.youverify_data.filter(
                #     ajo_user=borrower, request_type="BVN", is_valid=True
                #     ).last()

                # you_verify_data_nin = self.youverify_data.filter(
                #     ajo_user=borrower, request_type="NIN", is_valid=True
                #     ).last()

                # you_verify_data_bvn = (you_verify_data_bvn.youverify_response or {} if you_verify_data_bvn else {})
                # you_verify_data_nin = (you_verify_data_nin.youverify_response if you_verify_data_nin else {} or {})

                # if you_verify_data_bvn:
                #     you_verify_data = you_verify_data_bvn
                # else:
                #     you_verify_data = you_verify_data_nin
                you_verify_data = borrower_info.you_verify_metadata
                # you_verify_data = {"data": {
                #     "bvn": "22148344921", "mobile": "07067582189", "status": "found",
                #     "lastName": " IWOWO ", "firstName": "BENEDICT", "middleName": "EZE", "dateOfBirth": "1987-10-24"},
                #     "statusCode": 200
                #     }

                data = {
                    "borrower_details": {
                        "name": borrower.fullname,
                        "user_id": borrower.id,
                        "email": "",  # borrower.email,
                        "phone_number": borrower.phone,
                        "residential_address": (
                            borrower.address if borrower.address else ""
                        ),
                        "city": borrower.lga if borrower.lga else "",
                        "date_of_birth": borrower.dob if borrower.dob else "",
                        "borrower_image": borrower_image_upload,
                        "photo": (
                            str(f"{settings.BASE_DIR}/{borrower.image}")
                            if borrower and borrower.image
                            else ""
                        ),
                    },
                    "demographics": {
                        "age": borrower.age if borrower.age else "",
                        "gender": borrower.gender if borrower.gender else "",
                        "marital_status": (
                            borrower.marital_status if borrower.marital_status else ""
                        ),
                        "income": (
                            borrower_info.weekly_income
                            if borrower_info and borrower_info.weekly_income
                            else 0
                        ),
                    },
                    "work_records": {
                        "status": "",
                        "employer": borrower.trade if borrower.trade else "",
                        "office_address": (
                            borrower.trade_location if borrower.trade_location else ""
                        ),
                        "shop_rent": (
                            borrower_info.annual_shop_rent
                            if borrower_info and borrower_info.annual_shop_rent
                            else 0
                        ),
                    },
                    "next_of_kin": {
                        "name": "",
                        "email": "",
                        "phone_number": "",
                        "address": "",
                    },
                    "verification_details": {
                        "first_name": you_verify_data.get("data", {}).get("firstName"),
                        "last_name": you_verify_data.get("data", {}).get("lastName"),
                        "other_name": you_verify_data.get("data", {}).get("middleName"),
                        "email": you_verify_data.get("data", {}).get("email"),
                        "date_of_birth": you_verify_data.get("data", {}).get(
                            "dateOfBirth"
                        ),
                        "bvn": you_verify_data.get("data", {}).get("bvn"),
                        "nin": you_verify_data.get("data", {}).get("nin"),
                        "phone_number": you_verify_data.get("data", {}).get("mobile"),
                        "marital_status": "",
                        "photo": you_verify_data.get("data", {}).get("image"),
                        "residential_address": you_verify_data.get("data", {})
                        .get("address", {})
                        .get("addressLine"),
                        "state_of_origin": you_verify_data.get("data", {}).get(
                            "nokState"
                        ),
                    },
                }
                return data
            else:
                return {"error": "Loan Record does not exist"}
        else:
            return {"error": "Invalid loan id provided"}

    def get_guarantor_info(self):
        loan_id = self.request.query_params.get("loan_id")

        if loan_id and isinstance(int(loan_id), int):
            loan_obj = self.ajo_loan_qs.filter(id=int(loan_id)).last()

            if loan_obj:
                # Gurarantor Details
                guarantor = loan_obj.guarantor

                biometrics_meta_data = self.biometric_data_qs.filter(
                    loan_guarantor=guarantor,
                    request_type="FACE_MATCH",
                    user_type=UserVerificationType.GUARANTOR,
                ).last()
                you_verify_data = (
                    guarantor.you_verify_metadata
                    if guarantor and guarantor.you_verify_metadata
                    else {}
                )

                if guarantor:
                    data = {
                        "guarantor_details": {
                            "name": guarantor.surname + " " + guarantor.last_name,
                            "user_id": guarantor.id,
                            "email": guarantor.email,
                            "phone_number": guarantor.phone_number,
                            "residential_address": (
                                guarantor.address if guarantor.address else ""
                            ),
                            "city": guarantor.address if guarantor.address else "",
                            "date_of_birth": (
                                guarantor.date_of_birth
                                if guarantor.date_of_birth
                                else ""
                            ),
                            "bvn": (
                                guarantor.verification_number
                                if guarantor.verification_type == "BVN"
                                else ""
                            ),
                            "nin": (
                                guarantor.verification_number
                                if guarantor.verification_type == "NIN"
                                else ""
                            ),
                            "photo": (
                                guarantor.snapped_image
                                if guarantor and guarantor.snapped_image
                                else ""
                            ),
                        },
                        "demographics": {
                            "age": guarantor.age if guarantor.age else "",
                            "date_of_birth": guarantor.age if guarantor.age else "",
                            "gender": "",  # guarantor.gender if guarantor.gender else "",
                            "marital_status": "",  # guarantor.marital_status if guarantor.marital_status else "",
                            "income": "",
                        },
                        "work_records": {
                            "occupation": "",
                            "status": "",
                            "employer": "",  # guarantor.trade if guarantor.trade else "",
                            "office_address": "",  # guarantor.trade_location if guarantor.trade_location else "",
                            "shop_rent": "",
                        },
                        "verification_details": {
                            "first_name": you_verify_data.get("data", {}).get(
                                "firstName"
                            ),
                            "last_name": you_verify_data.get("data", {}).get(
                                "lastName"
                            ),
                            "other_name": you_verify_data.get("data", {}).get(
                                "middleName"
                            ),
                            "email": you_verify_data.get("data", {}).get("email"),
                            "date_of_birth": you_verify_data.get("data", {}).get(
                                "dateOfBirth"
                            ),
                            "bvn": (
                                you_verify_data.get("data", {}).get("bvn")
                                if guarantor and guarantor.verification_type == "BVN"
                                else ""
                            ),
                            "nin": (
                                you_verify_data.get("data", {}).get("nin")
                                if guarantor and guarantor.verification_type == "NIN"
                                else ""
                            ),
                            "phone_number": you_verify_data.get("data", {}).get(
                                "mobile"
                            ),
                            "marital_status": "",
                            "photo": (
                                guarantor.base_64_img_string
                                if guarantor and guarantor.base_64_img_string
                                else ""
                            ),
                            "residential_address": you_verify_data.get("data", {})
                            .get("address", {})
                            .get("addressLine"),
                            "state_of_origin": you_verify_data.get("data", {}).get(
                                "nokState"
                            ),
                        },
                    }
                    return data
                else:
                    return {"error": "Guarantor Record does not exist"}
            else:
                return {"error": "Loan Record does not exist"}
        else:
            return {"error": "Invalid loan id provided"}

    def get_agent_info(self):
        loan_id = self.request.query_params.get("loan_id")

        if loan_id and isinstance(int(loan_id), int):
            loan_obj = self.ajo_loan_qs.filter(id=int(loan_id)).last()

            if loan_obj:
                # Agent Details
                agent = loan_obj.agent
                supervisor_details = get_agent_supervisor_details(
                    customer_user_id=agent.customer_user_id
                )

                data = {
                    "agent_details": {
                        "name": agent.get_username(),
                        "fullname": agent.first_name + agent.last_name,
                        "user_id": agent.id,
                        "email": agent.email,
                        "phone_number": agent.user_phone,
                        "residential_address": "",  # agent.address if agent.address else "",
                        "city": "",  # agent.lga if agent.lga else "",
                        "date_of_birth": "",  # agent.dob if agent.dob else "",
                        "bvn": "",
                        "nin": "",
                        "photo": "",  # agent.image,
                        "agent_id": agent.id,
                        "branch": supervisor_details.get("branch", ""),
                        "supervisor": supervisor_details.get("supervisor", ""),
                        "branch_address": supervisor_details.get("supervisor", ""),
                        "terminal_id": "",
                        "agent_rating": "",
                    }
                }
                return data
            else:
                return {"error": "Loan Record does not exist"}
        else:
            return {"error": "Invalid loan id provided"}

    def get_credit_intelligence_borrower_crc_data(self):
        loan_id = self.request.query_params.get("loan_id")

        if loan_id and isinstance(int(loan_id), int):
            loan_obj = self.ajo_loan_qs.filter(id=int(loan_id)).last()

            if loan_obj:
                # Credit Details
                agent = loan_obj.agent
                borrower = loan_obj.borrower

                borrower_info = self.borrower_info_qs.filter(loan=loan_obj).last()

                if borrower_info and borrower_info.credit_bureau:
                    borrower_credit_bureau_data = json.loads(
                        borrower_info.credit_bureau
                    )
                    bad_loans_institutions_count = borrower_credit_bureau_data.get(
                        "bad_loans_institions_count", 0
                    )
                    open_loans_count = borrower_credit_bureau_data.get(
                        "count_of_open_loans", 0
                    )
                    total_outstanding = borrower_credit_bureau_data.get(
                        "total_outstanding", 0
                    )
                    eligibility_score = (total_outstanding / 500000) * 100
                else:
                    borrower_credit_bureau_data = {}
                    bad_loans_institutions_count = 0
                    open_loans_count = 0
                    total_outstanding = 0
                    eligibility_score = 0

                data = {
                    "crc_details": {
                        "credibility_eligibility_score": eligibility_score,
                        "performing_accounts": open_loans_count,
                        "paid_off": 0,
                        "outstanding": total_outstanding,
                        "performing_balance": 0,
                        "bad_or_lost_accounts": bad_loans_institutions_count,
                        "bad_or_lost_balance": 0,
                        "duration": 0,
                        "non_performing_accounts": 0,
                        "rating": eligibility_score,
                    }
                }
                return data
            else:
                return {"error": "Loan Record does not exist"}
        else:
            return {"error": "Invalid loan id provided"}

    def get_credit_intelligence_borrower_credit_history(self):
        loan_id = self.request.query_params.get("loan_id")

        if loan_id and isinstance(int(loan_id), int):
            loan_obj = self.ajo_loan_qs.filter(id=int(loan_id)).last()

            if loan_obj:
                # Credit Details
                agent = loan_obj.agent
                borrower_info = self.borrower_info_qs.filter(loan=loan_obj).last()

                if borrower_info and borrower_info.credit_bureau:
                    borrower_credit_bureau_data = json.loads(
                        borrower_info.credit_bureau
                    )

                    open_loan_institutions = borrower_credit_bureau_data.get(
                        "open_loan_institutions", []
                    )
                    bad_loans_institions = borrower_credit_bureau_data.get(
                        "bad_loans_institions", []
                    )
                else:
                    open_loan_institutions = []
                    bad_loans_institions = []

                credit_history_list = []
                for bank in open_loan_institutions:
                    data = {
                        "bank": bank,
                        "amount": 0,
                        "account_open_date": "",
                        "missed_payment": 0,
                        "loan_type": 0,
                        "status": "",
                    }
                    credit_history_list.append(data)

                for bank in bad_loans_institions:
                    data = {
                        "bank": bank,
                        "amount": 0,
                        "account_open_date": "",
                        "missed_payment": 0,
                        "loan_type": 0,
                        "status": "",
                    }
                    credit_history_list.append(data)

                data = {
                    "credit_history": credit_history_list,
                    "count": len(credit_history_list),
                }
                return data
            else:
                return {"error": "Loan Record does not exist"}
        else:
            return {"error": "Invalid loan id provided"}

    def get_borrower_financial_data_spending_pattern(self):
        loan_id = self.request.query_params.get("loan_id")

        if loan_id and isinstance(int(loan_id), int):
            loan_obj = self.ajo_loan_qs.filter(id=int(loan_id)).last()

            if loan_obj:
                # Credit Details
                borrower = loan_obj.borrower
                borrower_transactions_qs = self.debit_credit_qs.filter(
                    onboarded_user=borrower
                )
                total_inflow_amount = list(
                    borrower_transactions_qs.filter(entry="CREDIT")
                    .aggregate(Sum("amount"))
                    .values()
                )[0]
                total_outflow_amount = list(
                    borrower_transactions_qs.filter(entry="DEBIT")
                    .aggregate(Sum("amount"))
                    .values()
                )[0]
                total_transaction_amount = list(
                    borrower_transactions_qs.filter().aggregate(Sum("amount")).values()
                )[0]
                total_loans_amount = list(
                    self.ajo_loan_qs.filter(borrower=borrower, is_disbursed=True)
                    .aggregate(Sum("amount"))
                    .values()
                )[0]
                total_expected_repayment_amount = list(
                    self.ajo_loan_qs.filter(borrower=borrower, is_disbursed=True)
                    .aggregate(Sum("repayment_amount"))
                    .values()
                )[0]
                paid_off_amount = list(
                    self.repayment_qs.filter(borrower=borrower)
                    .aggregate(Sum("repayment_amount"))
                    .values()
                )[0]
                active_loan = self.ajo_loan_qs.filter(
                    borrower=borrower, status="OPEN"
                ).last()

                total_outstanding_amount = (
                    total_expected_repayment_amount
                    if total_expected_repayment_amount
                    else 0.00
                ) - (paid_off_amount if paid_off_amount else 0.00)

                data = {
                    "spending_data": {
                        "total_inflow_amount": (
                            total_inflow_amount if total_inflow_amount else 0.00
                        ),
                        "total_loans_amount": (
                            total_loans_amount if total_loans_amount else 0.00
                        ),
                        "total_outstanding_amount": total_outstanding_amount,
                        "total_outflow_amount": (
                            total_outflow_amount if total_outflow_amount else 0.00
                        ),
                        "paid_off_amount": paid_off_amount if paid_off_amount else 0.00,
                        "active_loan_duration": (
                            (datetime.now().date() - active_loan.start_date).days
                            if active_loan
                            else 0
                        ),
                        "total_transaction_amount": (
                            total_transaction_amount
                            if total_transaction_amount
                            else 0.00
                        ),
                    }
                }
                return data
            else:
                return {"error": "Loan Record does not exist"}
        else:
            return {"error": "Invalid loan id provided"}

    def get_borrower_transactions_history(self):
        loan_id = self.request.query_params.get("loan_id")

        if loan_id and isinstance(int(loan_id), int):
            loan_obj = self.ajo_loan_qs.filter(id=int(loan_id)).last()

            if loan_obj:
                # Credit Details
                borrower = loan_obj.borrower
                borrower_transactions_qs = (
                    self.debit_credit_qs.filter(onboarded_user=borrower)
                    .annotate(
                        date=F("date_created"),
                        transaction_type=F("type_of_trans"),
                        wallet_balance=F("balance_after"),
                        status=Value(""),
                        overdue=Value("0"),
                    )
                    .values(
                        "id",
                        "date",
                        "transaction_id",
                        "amount",
                        "date_created",
                        "type_of_trans",
                        "transaction_type",
                        "balance_after",
                        "wallet_balance",
                        "overdue",
                        "status",
                    )
                )

                data = {
                    "transaction_history": borrower_transactions_qs,
                    "count": borrower_transactions_qs.count(),
                }
                return data
            else:
                return {"error": "Loan Record does not exist"}
        else:
            return {"error": "Invalid loan id provided"}

    def get_borrower_non_indebtedness_docs(self):
        loan_id = self.request.query_params.get("loan_id")

        if loan_id and isinstance(int(loan_id), int):
            loan_obj = self.ajo_loan_qs.filter(id=int(loan_id)).last()

            if loan_obj:
                # Credit Details
                agent = loan_obj.agent
                docs_image_list = [
                    {"id": 1, "image_url": ""},
                    {"id": 2, "image_url": ""},
                ]

                data = {
                    "transaction_history": docs_image_list,
                    "count": len(docs_image_list),
                }
                return data
            else:
                return {"error": "Loan Record does not exist"}
        else:
            return {"error": "Invalid loan id provided"}


class LoanDisbursementClass:
    def __init__(self, request) -> None:
        filter, disburse_filter = get_date_filter(
            filter_value=request.query_params.get("filter")
        )
        # Get Supervisor USERS
        self.request = request
        user = self.request.user

        # self.supervisor_users_list = []
        # agency_banking_supervisors = AgencyBankingClass.get_agency_supervisor_details()
        # if (
        #     "success" in agency_banking_supervisors
        #     and agency_banking_supervisors.get("success") == True
        # ):
        #     supervisors_list = agency_banking_supervisors.get("supervisors_teams_list")

        #     for supervisor in supervisors_list:
        #         if supervisor.get("supervisor_user_id") == user.customer_user_id:
        #             self.supervisor_users_list = supervisor.get("users_list", [])
        #             break
        # else:
        #     self.supervisor_users_list = []
        self.supervisor_users_list = SupervisorClass().get_supervisor_users(user)
        # self.supervisor_users_list += [1, 2, 3, 5]

        if self.request.user.is_staff:
            self.ajo_loan_qs = AjoLoan.objects.filter()
            self.agents_daily_attendance_qs = AgentDailyAttendance.objects.filter(
                user_type=UserTypes.STAFF_AGENT,
            )
            self.repayments_qs = AjoLoanRepayment.objects.filter()
        else:
            self.ajo_loan_qs = AjoLoan.objects.filter(
                agent__customer_user_id__in=self.supervisor_users_list
            )
            self.agents_daily_attendance_qs = AgentDailyAttendance.objects.filter(
                agent__customer_user_id__in=self.supervisor_users_list,
                user_type=UserTypes.STAFF_AGENT,
            )
            self.repayments_qs = AjoLoanRepayment.objects.filter(
                ajo_loan__agent__customer_user_id__in=self.supervisor_users_list
            )

        self.approved_loans_qs = self.ajo_loan_qs.filter(status="APPROVED")
        self.declined_loans_qs = self.ajo_loan_qs.filter(status="REJECTED")
        self.ongoing_loans_application_qs = self.ajo_loan_qs.filter(
            status__in=["PROCESSING", "IN_PROGRESS"]
        )

    def get_disbursements_overview(self):
        # if self.supervisor_users_list:
        supervisor_loans_qs = self.ajo_loan_qs.filter(
            # is_disbursed=True,
            # agent__id__in=self.supervisor_users_list
        )

        # Total Disbursed
        total_disbursed_amount = list(
            supervisor_loans_qs.filter(is_disbursed=True)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_disbursed_count = list(
            supervisor_loans_qs.filter(is_disbursed=True)
            .aggregate(Count("amount"))
            .values()
        )[0]

        # New Disbursed
        total_new_disbursed_amount = list(
            supervisor_loans_qs.filter(is_disbursed=True)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_new_disbursed_count = list(
            supervisor_loans_qs.filter(is_disbursed=True)
            .aggregate(Count("amount"))
            .values()
        )[0]

        # Pending Disbursed
        total_pending_disbursed_amount = list(
            supervisor_loans_qs.filter(is_disbursed=False, status="APPROVED")
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_pending_disbursed_count = list(
            supervisor_loans_qs.filter(is_disbursed=False, status="APPROVED")
            .aggregate(Count("amount"))
            .values()
        )[0]

        data = {
            "total_disbursed": {
                "amount": total_disbursed_amount or 0.00,
                "count": total_disbursed_count or 0,
                "amount_change": "up",
                "count_charge": "up",
                "amount_percentage": 0,
                "count_percentage": 0,
            },
            "new_disbursement": {
                "amount": total_new_disbursed_amount or 0.00,
                "count": total_new_disbursed_count or 0,
                "amount_change": "up",
                "count_charge": "up",
                "amount_percentage": 0,
                "count_percentage": 0,
            },
            "pending_disbursement": {
                "amount": total_pending_disbursed_amount or 0.00,
                "count": total_pending_disbursed_count or 0,
                "amount_change": "up",
                "count_charge": "up",
                "amount_percentage": 0,
                "count_percentage": 0,
            },
        }

        return data

    def get_due_collections(self):
        from loans.enums import LoanStatus
        from loans.helpers.helpers import get_due_collections_amount

        week_start = DateUtility().week_start
        today = DateUtility().today
        month_start = DateUtility().month_start
        year_start = DateUtility().year_start
        previous_day = DateUtility().previous_day

        due_loans_qs = self.ajo_loan_qs.filter(
            status=LoanStatus.OPEN, start_date__lte=today.date()
        )

        total_due_today = (
            due_loans_qs.aggregate(total_due_today=Sum("daily_repayment_amount"))[
                "total_due_today"
            ]
            or 0.00
        )

        total_due_today_count = due_loans_qs.count()

        total_due_collections_amount_week = (
            self.agents_daily_attendance_qs.filter(
                created_at__gte=week_start
            ).aggregate(amount_due_total=Sum("due_collections_amount_today"))[
                "amount_due_total"
            ]
            or 0.00
        ) + total_due_today

        total_due_collections_count_week = (
            self.agents_daily_attendance_qs.filter(
                created_at__gte=week_start
            ).aggregate(amount_due_total=Sum("due_repayments_count_today"))[
                "amount_due_total"
            ]
            or 0.00
        ) + total_due_today_count

        total_due_collections_amount_month = (
            self.agents_daily_attendance_qs.filter(
                created_at__gte=month_start
            ).aggregate(amount_due_total=Sum("due_collections_amount_today"))[
                "amount_due_total"
            ]
            or 0.00
        ) + total_due_today

        total_due_collections_count_month = (
            self.agents_daily_attendance_qs.filter(
                created_at__gte=month_start
            ).aggregate(amount_due_total=Sum("due_repayments_count_today"))[
                "amount_due_total"
            ]
            or 0.00
        ) + total_due_today_count

        total_due_collections_amount_year = (
            self.agents_daily_attendance_qs.filter(
                created_at__gte=year_start
            ).aggregate(amount_due_total=Sum("due_collections_amount_today"))[
                "amount_due_total"
            ]
            or 0.00
        ) + total_due_today

        total_due_collections_count_year = (
            self.agents_daily_attendance_qs.filter(
                created_at__gte=year_start
            ).aggregate(amount_due_total=Sum("due_repayments_count_today"))[
                "amount_due_total"
            ]
            or 0.00
        )

        total_due_collections_amount = sum(
            [loan.due_today for loan in self.ajo_loan_qs]
        )
        due_collections_count = (
            sum([1 for loan in self.ajo_loan_qs if loan.due_today > 0])
            + total_due_today_count
        )

        # Collections
        total_collections_today = (
            self.repayments_qs.filter(paid_date__date=today.date()).aggregate(
                amount_paid_total=Sum("repayment_amount")
            )
        )["amount_paid_total"] or 0.00

        total_collections_today_count = (
            self.repayments_qs.filter(paid_date__date=today.date()).aggregate(
                amount_paid_total=Count("repayment_amount")
            )
        )["amount_paid_total"] or 0

        total_collections_yesterday = (
            self.repayments_qs.filter(paid_date__date=previous_day.date()).aggregate(
                amount_paid_total=Sum("repayment_amount")
            )
        )["amount_paid_total"] or 0.00

        total_collections_yesterday_count = (
            self.repayments_qs.filter(paid_date__date=previous_day.date()).aggregate(
                amount_paid_total=Count("repayment_amount")
            )
        )["amount_paid_total"] or 0.00

        total_collections_week = (
            self.repayments_qs.filter(paid_date__gte=week_start).aggregate(
                amount_paid_total=Sum("repayment_amount")
            )
        )["amount_paid_total"] or 0.00

        total_collections_week_count = (
            self.repayments_qs.filter(paid_date__gte=week_start).aggregate(
                amount_paid_total=Count("repayment_amount")
            )
        )["amount_paid_total"] or 0.00

        total_collections_month = (
            self.repayments_qs.filter(paid_date__gte=month_start).aggregate(
                amount_paid_total=Sum("repayment_amount")
            )
        )["amount_paid_total"] or 0.00

        total_collections_month_count = (
            self.repayments_qs.filter(paid_date__gte=month_start).aggregate(
                amount_paid_total=Count("repayment_amount")
            )
        )["amount_paid_total"] or 0.00

        total_collections_year = (
            self.repayments_qs.filter(paid_date__gte=year_start).aggregate(
                amount_paid_total=Sum("repayment_amount")
            )
        )["amount_paid_total"] or 0.00

        total_collections_year_count = (
            self.repayments_qs.filter(paid_date__gte=year_start).aggregate(
                amount_paid_total=Count("repayment_amount")
            )
        )["amount_paid_total"] or 0.00

        total_collections_all_time = (
            self.repayments_qs.aggregate(amount_paid_total=Sum("repayment_amount"))
        )["amount_paid_total"] or 0.00

        total_collections_all_time_count = (
            self.repayments_qs.aggregate(amount_paid_total=Count("repayment_amount"))
        )["amount_paid_total"] or 0.00

        # if today.weekday() in [5, 6]:
        #     total_due_today = 0.00
        #     total_due_today_count = 0

        total_due_today = get_due_collections_amount(
            due_loans_qs, agent=self.request.user
        )

        data = {
            "total_due_collections": {
                "amount": total_due_collections_amount,
                "count": due_collections_count,
                "amount_change": "up",
                "count_change": "up",
                "percentage_change": 0,
            },
            "total_due_collections_amount_week": {
                "amount": total_due_collections_amount_week,
                "count": total_due_collections_count_week,
                "amount_change": "up",
                "count_change": "up",
                "percentage_change": 0,
            },
            "total_due_today": {
                "amount": total_due_today,
                "count": total_due_today_count,
                "amount_change": "up",
                "count_change": "up",
                "percentage_change": 0,
            },
            "due_this_week": {
                "amount": total_due_collections_amount_week,
                "count": total_due_collections_count_week,
                "amount_change": "up",
                "count_change": "up",
                "percentage_change": 0,
            },
            "due_this_month": {
                "amount": total_due_collections_amount_month,
                "count": total_due_collections_count_month,
                "amount_change": "up",
                "count_change": "up",
                "percentage_change": 0,
            },
            "due_this_year": {
                "amount": total_due_collections_amount_year,
                "count": total_due_collections_count_year,
                "amount_change": "up",
                "count_change": "up",
                "percentage_change": 0,
            },
            "total_collections_today": {
                "amount": total_collections_today,
                "count": total_collections_today_count,
                "amount_change": "up",
                "count_change": "up",
                "percentage_change": 0,
            },
            "total_collections_yesterday": {
                "amount": total_collections_yesterday,
                "count": total_collections_yesterday_count,
                "amount_change": "up",
                "count_change": "up",
                "percentage_change": 0,
            },
            "total_collections_week": {
                "amount": total_collections_week,
                "count": total_collections_week_count,
                "amount_change": "up",
                "count_change": "up",
                "percentage_change": 0,
            },
            "total_collections_month": {
                "amount": total_collections_month,
                "count": total_collections_month_count,
                "amount_change": "up",
                "count_change": "up",
                "percentage_change": 0,
            },
            "total_collections_year": {
                "amount": total_collections_year,
                "count": total_collections_year_count,
                "amount_change": "up",
                "count_change": "up",
                "percentage_change": 0,
            },
            "total_collections_all_time": {
                "amount": total_collections_all_time,
                "count": total_collections_all_time_count,
                "amount_change": "up",
                "count_change": "up",
                "percentage_change": 0,
            },
        }
        cache.set(
            f"{self.request.user.customer_user_id}-due_collections_data",
            data,
            timeout=60 * 60,
        )

        return data


class RepaymentClass:
    def __init__(self, request):
        # Get Supervisor USERS
        self.request = request
        date_filter, disburse_filter = get_date_filter(
            filter_value=request.query_params.get("filter")
        )
        # branch_filter = self.request.query_params.get("branch")
        # agent_filter = self.request.query_params.get("agent")

        # Get Supervisor USERS
        self.request = request
        user = self.request.user
        # self.supervisor_users_list = get_supervisor_loan_officers(
        #     supervisor_id=user.customer_user_id
        # )
        self.supervisor_users_list = SupervisorClass().get_supervisor_users(user)
        # self.supervisor_users_list += [1, 2, 3, 5]

        self.previous_day = DateUtility().previous_day.date()
        self.today = DateUtility().today
        self.week_start = DateUtility().week_start
        self.month_start = DateUtility().month_start
        self.year_start = DateUtility().year_start

        self.months_list_names = DateUtility().months_list_names
        self.days_label = DateUtility().days_label
        self._month_2_month_dates_list = DateUtility()._month_2_month_dates_list
        self._previous_week_current_week_date = (
            DateUtility()._previous_week_current_week_date
        )
        self._previous_month_current_month_date = (
            DateUtility()._previous_month_current_month_date
        )
        self.previous_year_current_month_start = (
            DateUtility().previous_year_current_month_start
        )
        self.year_months_tuple_list = DateUtility().year_months_tuple_list

        if request.user.is_staff:
            self.ajo_loan_qs = AjoLoan.objects.filter(**date_filter)
            self.ajo_loan_repayment_qs = AjoLoanRepayment.objects.filter(**date_filter)
            self.missed_repayments_qs = MissedRepaymentsTable.objects.filter(
                **date_filter
            )
            self.missed_30_days_qs = Missed28Days.objects.filter(**date_filter)
            self.missed_60_days_qs = Missed60Days.objects.filter(**date_filter)
            self.missed_90_days_qs = Missed90Days.objects.filter(**date_filter)
            self.missed_past_maturity_15_days_qs = (
                MissedPastMaturity15Days.objects.filter(**date_filter)
            )
        else:
            self.missed_repayments_qs = MissedRepaymentsTable.objects.filter(
                **date_filter,
                loan_officer__customer_user_id__in=self.supervisor_users_list,
                missed_amount__gt=0,
            )
            self.missed_30_days_qs = Missed28Days.objects.filter(
                **date_filter,
                loan_officer__customer_user_id__in=self.supervisor_users_list,
                missed_amount__gt=0,
            )
            self.missed_60_days_qs = Missed60Days.objects.filter(
                **date_filter,
                loan_officer__customer_user_id__in=self.supervisor_users_list,
                missed_amount__gt=0,
            )
            self.missed_90_days_qs = Missed90Days.objects.filter(
                **date_filter,
                loan_officer__customer_user_id__in=self.supervisor_users_list,
                missed_amount__gt=0,
            )
            self.missed_past_maturity_15_days_qs = (
                MissedPastMaturity15Days.objects.filter(
                    **date_filter,
                    loan_officer__customer_user_id__in=self.supervisor_users_list,
                    missed_amount__gt=0,
                )
            )
            self.ajo_loan_repayment_qs = AjoLoanRepayment.objects.filter(
                **date_filter,
                agent__customer_user_id__in=self.supervisor_users_list,
            )

        self.repayment_qs = AjoLoanRepayment.objects.filter()
        self.biometric_data_qs = BiometricsMetaData.objects.all()
        self.borrower_info_qs = BorrowerInfo.objects.all()

    def get_missed_repayment_overview(self):
        # Missed Repayment Amount
        total_missed_repayment_amount = (
            self.missed_repayments_qs.aggregate(total_amount=Sum("missed_amount"))[
                "total_amount"
            ]
            or 0.00
        )
        total_missed_repayment_amount_year = (
            self.missed_repayments_qs.filter(
                loan__start_date__gte=self.year_start
            ).aggregate(total_amount=Sum("missed_amount"))["total_amount"]
            or 0.00
        )
        total_missed_repayment_amount_month = (
            self.missed_repayments_qs.filter(
                loan__start_date__gte=self.month_start
            ).aggregate(total_amount=Sum("missed_amount"))["total_amount"]
            or 0.00
        )
        total_missed_repayment_amount_week = (
            self.missed_repayments_qs.filter(
                loan__start_date__gte=self.week_start
            ).aggregate(total_amount=Sum("missed_amount"))["total_amount"]
            or 0.00
        )

        # Missed Repayment Count
        missed_repayment_count = (
            self.missed_repayments_qs.aggregate(total_count=Count("missed_amount"))[
                "total_count"
            ]
            or 0.00
        )
        # total_missed_repayment_count_week = (
        #     self.missed_repayments_qs.filter(
        #     date_created__gte=self.week_start
        #     ).aggregate(
        #     total_amount=Count("missed_amount"))["total_amount"] or 0.00
        # )

        # Average Missed Repayment
        average_missed_repayment_amount = (
            self.missed_repayments_qs.aggregate(total_amount=Avg("missed_amount"))[
                "total_amount"
            ]
            or 0.00
        )

        # Non-performing loans
        non_performing_loans_amount = (
            self.missed_repayments_qs.filter(
                loan__status__in=non_performing_loan_status_list
            ).aggregate(total_count=Sum("missed_amount"))["total_count"]
            or 0.00
        )
        non_performing_loans_count = (
            self.missed_repayments_qs.filter(
                loan__status__in=non_performing_loan_status_list
            ).aggregate(total_count=Count("missed_amount"))["total_count"]
            or 0.00
        )

        # Overdue Amount
        overdue_loans_30_days_amount = (
            self.missed_30_days_qs.aggregate(total_amount=Sum("missed_amount"))[
                "total_amount"
            ]
            or 0.00
        )
        overdue_loans_30_days_count = (
            self.missed_30_days_qs.aggregate(total_amount=Count("missed_amount"))[
                "total_amount"
            ]
            or 0.00
        )
        overdue_loans_60_days_amount = (
            self.missed_60_days_qs.aggregate(total_amount=Sum("missed_amount"))[
                "total_amount"
            ]
            or 0.00
        )
        overdue_loans_60_days_count = (
            self.missed_60_days_qs.aggregate(total_amount=Count("missed_amount"))[
                "total_amount"
            ]
            or 0.00
        )
        overdue_loans_90_days_amount = (
            self.missed_90_days_qs.aggregate(total_amount=Sum("missed_amount"))[
                "total_amount"
            ]
            or 0.00
        )
        overdue_loans_90_days_count = (
            self.missed_90_days_qs.aggregate(total_amount=Count("missed_amount"))[
                "total_amount"
            ]
            or 0.00
        )

        data = {
            "missed_repayment": {
                "total_amount": total_missed_repayment_amount,
                "total_count": missed_repayment_count,
                "this_week": total_missed_repayment_amount_week,
                "this_month": total_missed_repayment_amount_month,
                "this_year": total_missed_repayment_amount_year,
                "percentage": 0,
                "change": "down",
            },
            "average_missed_repayment": {
                "total_amount": average_missed_repayment_amount,
                "total_count": missed_repayment_count,
                "percentage": 0,
                "change": "down",
            },
            "non_performing_loan": {
                "total_amount": non_performing_loans_amount,
                "total_count": non_performing_loans_count,
                "percentage": 0,
                "change": "down",
            },
            "aging_analytics_30_plus": {
                "total_amount": overdue_loans_30_days_amount,
                "total_count": overdue_loans_30_days_count,
                "percentage": 0,
                "change": "down",
            },
            "aging_analytics_60_plus": {
                "total_amount": overdue_loans_60_days_amount,
                "total_count": overdue_loans_60_days_count,
                "percentage": 0,
                "change": "down",
            },
            "aging_analytics_90_plus": {
                "total_amount": overdue_loans_90_days_amount,
                "total_count": overdue_loans_90_days_count,
                "percentage": 0,
                "change": "down",
            },
        }

        return data

    def get_missed_repayments_list(self):
        agents_filter = self.request.query_params.get("agents_filter")
        branch_filter = self.request.query_params.get("branch_filter")
        supervisor_filter = self.request.query_params.get("supervisor_filter")
        # Get all open loans

        # ajo_loan_repayment_qs = AjoLoanRepayment.objects.all()
        # due_loans_qs = self.due_loans_qs
        missed_repayments_qs = self.missed_repayments_qs

        if agents_filter and agents_filter != "all_loan_officers":
            agents_filter = int(agents_filter)
            # due_loans_qs = self.due_loans_qs.filter(
            #     agent__customer_user_id=agents_filter
            #     )
            missed_repayments_qs = missed_repayments_qs.filter(
                loan_officer__customer_user_id=agents_filter
            )

        if branch_filter and branch_filter != "":
            branch_name = branch_filter
            branch_loan_officers = get_branch_loan_officers(branch_name)
            branch_loan_officers_list = branch_loan_officers.get("loan_officers", [])
            # due_loans_qs = self.due_loans_qs.filter(
            #     agent__customer_user_id__in=branch_loan_officers_list
            #     )
            missed_repayments_qs = missed_repayments_qs.filter(
                loan_officer__customer_user_id__in=branch_loan_officers_list
            )

        if supervisor_filter and supervisor_filter != "":
            supervisor_id = int(supervisor_filter)
            supervisor_officers_list = get_supervisor_loan_officers(
                supervisor_id=supervisor_id
            )

            # due_loans_qs = self.due_loans_qs.filter(
            #     agent__customer_user_id__in=supervisor_officers_list
            #     )
            missed_repayments_qs = self.missed_repayments_qs.filter(
                loan_officer__customer_user_id__in=supervisor_officers_list
            )

        # missed_repayments_list = []
        missed_repayments_list_qs = missed_repayments_qs.annotate(
            # loan_id=F("loan__id"),
            borrower_name=Concat(
                F("borrower__first_name"), Value(" "), F("borrower__last_name")
            ),
            # borrower_phone=F("borrower_phone"),
            staff_agent_email=F("loan_officer__email"),
            staff_agent_name=Concat(
                F("loan_officer__first_name"), F("loan_officer__last_name")
            ),
            staff_agent_phone=F("loan_officer__user_phone"),
            guarantor_name=Concat(
                F("loan__guarantor__last_name"),
                Value(" "),
                F("loan__guarantor__surname"),
            ),
            # guarantor_phone=F("guarantor_phone"),
            loan_term=F("loan__tenor"),
            due_today=F("missed_amount"),
            total_paid=F("paid_amount"),
            loan_age=Value(""),
            last_paid_amount=F("loan__last_paid_amount"),
            # repayment_amount=F("repayment_amount"),
            daily_amount=F("loan__daily_repayment_amount"),
            repayments_count=F("loan__count_of_recorded_repayments"),
            loan_balance=F("outstanding_loan_balance"),
            days_past_due=F("missed_days"),
            branch=F("loan_officer__user_branch"),
            start_date=F("loan__start_date"),
            end_date=F("loan__end_date"),
            # created_at=F("loan__created_at"),
            borrower_image=Value(""),
        ).values(
            "loan_id",
            "borrower_phone",
            "staff_agent_email",
            "staff_agent_phone",
            "guarantor_name",
            "guarantor_phone",
            "loan_term",
            "due_today",
            "total_paid",
            "loan_age",
            "last_paid_amount",
            "repayment_amount",
            "daily_amount",
            "repayments_count",
            "loan_balance",
            "days_past_due",
            "branch",
            "start_date",
            "end_date",
            "created_at",
            "borrower_image",
            "staff_agent_name",
            "borrower_name",
            "loan_amount",
            "missed_days",
        )

        #     if loan.missed_amount > 0:
        #         # loan_repayments = loan.paid_amount
        #         repayments_count = loan.loan.count_of_recorded_repayments

        #         guarantor = loan.loan.guarantor
        #         borrower = loan.borrower
        #         agent = loan.loan_officer

        #         borrower_name = (
        #             (borrower.first_name if borrower and borrower.first_name else "")
        #             + " " + (borrower.last_name if borrower and borrower.last_name else "")
        #             )

        #         guarantor_name = (
        #             (guarantor.surname if guarantor and guarantor.surname else "")
        #             + " " + (guarantor.last_name if guarantor and guarantor.last_name else "")
        #             )

        #         # guarantor_phone = guarantor.phone_number if guarantor else ""
        #         repayment_amount = loan.repayment_amount
        #         daily_amount = loan.loan.daily_repayment_amount

        #         # borrower_info = self.borrower_info_qs.filter(
        #         #     loan=loan).last()

        #         # biometrics_meta_data = self.biometric_data_qs.filter(
        #         #         borrower_info=borrower_info,
        #         #         request_type="FACE_MATCH",
        #         #         user_type=UserVerificationType.BORROWER
        #         #         ).last()

        #         agent_branch = get_agent_supervisor_details(
        #             customer_user_id=agent.customer_user_id
        #             )

        #         data = {
        #             "id": loan.loan.id,
        #             "borrower_name": borrower_name,
        #             "borrower_phone": f"{borrower.phone}",
        #             "staff_agent_email": f"{agent.get_username()}",
        #             "staff_agent_phone": f"{agent.user_phone}",
        #             "guarantor_name": f"{guarantor_name}",
        #             "guarantor_phone": f"{loan.guarantor_phone}",
        #             "loan_amount": f"{loan.loan_amount:,}",
        #             "loan_term": loan.loan.tenor,
        #             "due_today": f"{loan.missed_amount:,}",
        #             "total_paid": f"{loan.paid_amount:,}",
        #             "loan_age": f"{loan.loan.loan_age}",
        #             "last_paid_amount": f"{loan.loan.last_paid_amount:,}",
        #             "repayment_amount": f"{repayment_amount:,}",
        #             "daily_amount": f"{daily_amount:,}",
        #             "repayments_count": f"{repayments_count}" or 0,
        #             "loan_balance": f"{loan.repayment_amount - loan.paid_amount:,}",
        #             "days_past_due": loan.missed_days,
        #             "branch": agent_branch.get("branch", ""),
        #             "start_date": loan.loan.start_date,
        #             "end_date": loan.loan.end_date,
        #             "created_at": loan.loan.created_at,
        #             "borrower_image": "" #biometrics_meta_data.img_str_bvn_or_nin if biometrics_meta_data else ""
        #         }
        #         missed_repayments_list.append(data)

        data = {
            "missed_repayments_list": missed_repayments_list_qs,
            "count": missed_repayments_list_qs.count(),
        }
        return data

    def get_repayment_details(self):
        loan_id = self.request.query_params.get("loan_id")
        if loan_id:
            try:
                loan_instance = self.ajo_loan_qs.get(id=int(loan_id))
            except AjoLoan.DoesNotExist:
                return {"error": "This loan does not exist"}
        else:
            return {"error": "This loan does not exist"}

        # loan_age = get_loan_age(loan=loan_instance)

        # days_paid = round(
        #     (loan_instance.total_paid_amount) /
        #     (loan_instance.daily_repayment_amount or 1)
        #     )
        # days_past_due = loan_age - days_paid

        # if days_past_due > 0:
        #     missed_repayment_amount = (
        #         days_past_due *
        #         loan_instance.daily_repayment_amount
        #         )
        # else:
        #     missed_repayment_amount = 0

        loan_repayments_qs = (
            self.ajo_loan_repayment_qs.filter(ajo_loan=loan_instance)
            .annotate(
                transaction=F("repayment_type"),
                status=Value("True"),
                amount=F("repayment_amount"),
            )
            .values("transaction", "status", "amount", "created_at")
        )

        # total_repayment_amount = loan_repayments_qs.aggregate(
        #     Sum("amount")
        #     )["amount__sum"] or 0

        # loan_balance = loan_instance.repayment_amount - loan_instance.total_paid_amount

        borrower_info = self.borrower_info_qs.filter(loan=loan_instance).last()
        biometrics_meta_data = self.biometric_data_qs.filter(
            borrower_info=borrower_info,
            request_type="FACE_MATCH",
            user_type=UserVerificationType.BORROWER,
        ).last()

        # agent_branch = get_agent_supervisor_details(
        #     customer_user_id=loan_instance.agent.customer_user_id
        #     )
        missed_repayment_instance = MissedRepaymentsTable.objects.filter(
            loan=loan_instance
        ).last()
        missed_repayment_amount = (
            missed_repayment_instance.missed_amount
            if missed_repayment_instance
            else 0.00
        )
        loan_balance = (
            missed_repayment_instance.outstanding_loan_balance
            if missed_repayment_instance
            else 0.00
        )

        data = {
            "overview": {
                "original_loan_amount": loan_instance.amount,
                "payment_received": loan_instance.total_paid_amount,
                "loan_balance": loan_balance,
                "next_payment_due": "",
                "missed_repayment_amount": missed_repayment_amount,
            },
            "scheduled_payments": {"list": [], "count": len([])},
            "pending_payments": {
                "list": [],  # pending_repayments_qs,
                "count": len([]),
            },
            "missed_payments": {"list": [], "count": len([])},
            "loan_payment_history": {
                "list": loan_repayments_qs,
                "count": len(loan_repayments_qs),
            },
            "loan_detail": {
                "loan_reference_id": loan_instance.loan_ref,
                "financed_amount": loan_instance.amount,
                "number_of_payments": loan_instance.expected_repayment_count,
                "interest_rate": loan_instance.interest_rate,
                "day_of_interest": 0,
                "accrued_interest": 0,
                "regular_payment": loan_instance.daily_repayment_amount,
                "late_fee": 0,
                "default_fee": 0,
                "interest_start": "",
                "payment_start": loan_instance.start_date,
                "maturity_date": loan_instance.end_date,
            },
            "contact_info": {
                "user_id": loan_instance.borrower.id,
                "email": loan_instance.borrower_full_name or "",
                "name": loan_instance.borrower_full_name or "",
                "phone": loan_instance.borrower.phone or "",
                "address": loan_instance.borrower.address or "",
                "city": loan_instance.borrower.location_city or "",
                "profile_image": (
                    biometrics_meta_data.img_str_bvn_or_nin
                    if biometrics_meta_data
                    else ""
                ),
            },
            "agent_info": {
                "user_id": "",
                "email": loan_instance.agent.email or "",
                "name": loan_instance.agent.username or "",
                "phone": loan_instance.agent.user_phone or "",
                "address": "",
                "city": "",
                "profile_image": "",
            },
        }
        return data

    def get_agents_missed_repayments_chart(self):
        if self.request.user.is_staff:
            loan_officers_list = get_supervisor_loan_officers()
        else:
            loan_officers_list = self.supervisor_users_list

        loan_officers_names_list = []
        missed_repayment_values_list = []

        for officer in set(loan_officers_list):
            agent_instance = CustomUser.objects.filter(customer_user_id=officer).last()
            if agent_instance:
                agent_name = (
                    (agent_instance.first_name if agent_instance.first_name else "")
                    + " "
                    + (agent_instance.last_name if agent_instance.last_name else "")
                )
                if agent_name == " " or agent_name == "":
                    agent_name = agent_instance.email
            else:
                continue

            loan_officers_names_list.append(agent_name)

            total_missed_repayment_amount = (
                self.missed_repayments_qs.filter(
                    loan_officer__customer_user_id=officer
                ).aggregate(total_amount=Sum("missed_amount"))["total_amount"]
                or 0.00
            )
            missed_repayment_values_list.append(total_missed_repayment_amount)

        data = {
            "agents_list": loan_officers_names_list,
            "values": missed_repayment_values_list,
        }
        return data

    def get_loan_officers_list(self):
        if self.request.user.is_staff:
            loan_officers_list = get_supervisor_loan_officers()
        else:
            loan_officers_list = self.supervisor_users_list

        loan_officers_names_list = []
        for officer in loan_officers_list:
            agent_instance = CustomUser.objects.filter(customer_user_id=officer).last()

            agent_name = (
                (agent_instance.first_name if agent_instance else "")
                + " "
                + (agent_instance.last_name if agent_instance else "")
            )

            loan_officers_names_list.append(
                {"loan_officer_id": officer, "name": agent_name}
            )

        data = {
            "loan_officers": loan_officers_names_list,
        }
        return data

    def get_branches_list(self):
        supervisors_list = AgencyBankingClass.get_agency_supervisor_details()

        if "success" in supervisors_list and supervisors_list.get("success") == True:
            teams = supervisors_list.get("supervisors_teams_list")
        else:
            teams = []
        branch_list = [d["branch"] for d in teams]
        data = {"branches_list": branch_list}
        return data

    def get_branch_supervisors_list(self):
        supervisors_list = AgencyBankingClass.get_agency_supervisor_details()

        if "success" in supervisors_list and supervisors_list.get("success") == True:
            teams = supervisors_list.get("supervisors_teams_list")
        else:
            teams = []

        supervisor_details_list = [
            {
                "supevisor_id": d["supervisor_user_id"],
                "supervisor_name": d["supervisor"],
            }
            for d in teams
        ]

        data = {"supervisors_list": supervisor_details_list}
        return data

    def get_top_missed_repayment_branches(self):
        branches = get_loan_branches_list()

        repayment_ranking_list = []
        for branch in set(branches):
            branch_loan_officers = get_branch_loan_officers(query_branch=branch).get(
                "loan_officers"
            )

            single_agent = branch_loan_officers[0] if branch_loan_officers else None

            if single_agent != None:
                supervisor = get_agent_supervisor_details(customer_user_id=single_agent)
                supervisor = supervisor.get("supervisor")
            else:
                supervisor = ""

            disbursed_amount = (
                self.ajo_loan_qs.filter(
                    is_disbursed=True, agent__customer_user_id__in=branch_loan_officers
                ).aggregate(total_amount=Sum("amount"))["total_amount"]
                or 0.00
            )

            total_repayment = (
                self.ajo_loan_repayment_qs.filter(
                    agent__customer_user_id__in=branch_loan_officers
                ).aggregate(total_amount=Sum("repayment_amount"))["total_amount"]
                or 0.00
            )

            # missed_repayment = 0
            # for loan in self.due_loans_qs.filter(
            #     agent__customer_user_id__in=branch_loan_officers
            #     ):
            #     if loan.outstanding_due_today > 0:
            missed_repayment = (
                self.missed_repayments_qs.filter(
                    loan_officer__customer_user_id__in=branch_loan_officers
                ).aggregate(total_amount=Sum("missed_amount"))["total_amount"]
                or 0.00
            )

            branch_data = {
                "branch": branch,
                "supervisor": supervisor,
                "disbursed": disbursed_amount,
                "collected": total_repayment,
                "missed": missed_repayment,
            }
            repayment_ranking_list.append(branch_data)

        top_branches = sorted(repayment_ranking_list, key=lambda d: d["missed"])

        data = {"top_branches": top_branches[::-1], "count": len(top_branches)}
        return data

    def get_top_missed_repayment_loan_officers(self):
        loan_officers_missed_list = []

        loan_officers = get_supervisor_loan_officers()

        for officer in set(loan_officers):
            agent_instance = CustomUser.objects.filter(customer_user_id=officer).last()

            if agent_instance:
                agent_name = (
                    (agent_instance.first_name if agent_instance.first_name else "")
                    + " "
                    + (agent_instance.last_name if agent_instance.last_name else "")
                )
                if agent_name == " " or agent_name == "":
                    agent_name = agent_instance.email

                supervisor = get_agent_supervisor_details(
                    customer_user_id=agent_instance.customer_user_id
                )

                supervisor_name = supervisor.get("supervisor")
                branch = supervisor.get("branch")

                disbursed_amount = (
                    self.ajo_loan_qs.filter(
                        is_disbursed=True,
                        agent__customer_user_id=agent_instance.customer_user_id,
                    ).aggregate(total_amount=Sum("amount"))["total_amount"]
                    or 0.00
                )

                missed_repayment = (
                    self.missed_repayments_qs.filter(
                        loan_officer__customer_user_id=agent_instance.customer_user_id
                    ).aggregate(total_amount=Sum("missed_amount"))["total_amount"]
                    or 0.00
                )
            else:
                continue

            total_repayment = (
                self.ajo_loan_repayment_qs.filter(
                    agent__customer_user_id=agent_instance.customer_user_id
                ).aggregate(total_amount=Sum("repayment_amount"))["total_amount"]
                or 0.00
            )

            officer_data = {
                "id": officer,
                "loan_officer": agent_name,
                "branch": branch,
                "supervisor": supervisor_name,
                "disbursed": disbursed_amount,
                "collected": total_repayment,
                "missed": missed_repayment,
            }
            loan_officers_missed_list.append(officer_data)

        top_missed_officers = sorted(
            loan_officers_missed_list, key=lambda d: d["missed"]
        )[::-1]

        data = {"top_branches": top_missed_officers, "count": len(top_missed_officers)}
        return data

    def get_admin_missed_repayment_analysis(self):
        # self.total_due_repayment_qs = get_total_due_repayment_qs(
        #     due_loans_qs=self.due_loans_qs
        # )

        filter_value = self.request.query_params.get("filter")

        months_label = (
            self.days_label
            if filter_value == "weekly"
            else (
                self._month_2_month_dates_list
                if filter_value == "monthly"
                else self.months_list_names
            )
        )

        amounts_values_list = []

        # Weekly Chart
        if filter_value == "weekly":
            past_one_week_amounts_qs = (
                self.missed_repayments_qs.filter(
                    # due_balance__gt=0,
                    created_at__date__gte=self._previous_week_current_week_date
                )
                .values("created_at__date")
                .annotate(missed_amount_total=Sum("missed_amount"))
            )

        # Monthly chart
        elif filter_value == "monthly":
            past_one_month_amounts_qs = (
                self.missed_repayments_qs.filter(
                    # due_balance__gt=0,
                    created_at__date__gte=self._previous_month_current_month_date
                )
                .values("created_at__date")
                .annotate(missed_amount_total=Sum("missed_amount"))
            )
        else:
            past_year_amount_qs = (
                self.missed_repayments_qs.filter(
                    # due_balance__gt=0,
                    created_at__gte=self.previous_year_current_month_start
                )
                .values("created_at__year", "created_at__month")
                .annotate(missed_amount_total=Sum("missed_amount"))
                .order_by("created_at__year")
            )

            past_year_amounts_list = [
                (
                    data["created_at__year"],
                    data["created_at__month"],
                    data["missed_amount_total"],
                )
                for data in past_year_amount_qs
            ]

            past_year_amounts_final_list = []
            for item in self.year_months_tuple_list:
                if item in [(tup[0], tup[1]) for tup in past_year_amounts_list]:
                    for trans in past_year_amounts_list:
                        if item == (trans[0], trans[1]):
                            past_year_amounts_final_list.append(trans)
                            break
                else:
                    past_year_amounts_final_list.append((0, 0, 0))
            amounts_values_list = [trans[2] for trans in past_year_amounts_final_list]

        try:
            if past_one_week_amounts_qs:
                week_days_amounts_list = [
                    amounts["missed_amount_total"]
                    for amounts in past_one_week_amounts_qs
                ]
            else:
                week_days_amounts_list = [0] * 8
        except Exception as e:
            week_days_amounts_list = [0] * 8

        try:
            if past_one_month_amounts_qs:
                month_days_amounts_list = [
                    amount["missed_amount_total"]
                    for amount in past_one_month_amounts_qs
                ]
            else:
                month_days_amounts_list = [0] * 30
        except Exception as e:
            month_days_amounts_list = [0] * 30

        chart_data = {
            "months_list": months_label,
            "amounts": (
                week_days_amounts_list
                if filter_value == "weekly"
                else (
                    month_days_amounts_list
                    if filter_value == "monthly"
                    else amounts_values_list
                )
            ),
        }
        return chart_data

    def get_past_maturity_analysis(self):
        filter_value = self.request.query_params.get("filter")
        # self.total_due_repayment_qs = get_total_due_repayment_qs(
        #     due_loans_qs=self.due_loans_qs
        # )
        past_maturity_qs = self.missed_past_maturity_15_days_qs.filter(
            # end_date__lt=timezone.now().date()
        )

        months_label = (
            self.days_label
            if filter_value == "weekly"
            else (
                self._month_2_month_dates_list
                if filter_value == "monthly"
                else self.months_list_names
            )
        )

        amounts_values_list = []

        # Weekly Chart
        if filter_value == "weekly":
            past_one_week_amounts_qs = (
                past_maturity_qs.filter(
                    # due_balance__gt=0,
                    created_at__date__gte=self._previous_week_current_week_date
                )
                .values("created_at__date")
                .annotate(missed_amount_total=Sum("missed_amount"))
            )

        # Monthly chart
        elif filter_value == "monthly":
            past_one_month_amounts_qs = (
                past_maturity_qs.filter(
                    # due_balance__gt=0,
                    created_at__date__gte=self._previous_month_current_month_date
                )
                .values("created_at__date")
                .annotate(missed_amount_total=Sum("missed_amount"))
            )
        else:
            past_year_amount_qs = (
                past_maturity_qs.filter(
                    # due_balance__gt=0,
                    created_at__gte=self.previous_year_current_month_start
                )
                .values("created_at__year", "created_at__month")
                .annotate(missed_amount_total=Sum("missed_amount"))
                .order_by("created_at__year")
            )

            past_year_amounts_list = [
                (
                    data["created_at__year"],
                    data["created_at__month"],
                    data["missed_amount_total"],
                )
                for data in past_year_amount_qs
            ]

            past_year_amounts_final_list = []
            for item in self.year_months_tuple_list:
                if item in [(tup[0], tup[1]) for tup in past_year_amounts_list]:
                    for trans in past_year_amounts_list:
                        if item == (trans[0], trans[1]):
                            past_year_amounts_final_list.append(trans)
                            break
                else:
                    past_year_amounts_final_list.append((0, 0, 0))
            amounts_values_list = [trans[2] for trans in past_year_amounts_final_list]

        try:
            if past_one_week_amounts_qs:
                week_days_amounts_list = [
                    amounts["missed_amount_total"]
                    for amounts in past_one_week_amounts_qs
                ]
            else:
                week_days_amounts_list = [0] * 8
        except Exception as e:
            week_days_amounts_list = [0] * 8

        try:
            if past_one_month_amounts_qs:
                month_days_amounts_list = [
                    amount["missed_amount_total"]
                    for amount in past_one_month_amounts_qs
                ]
            else:
                month_days_amounts_list = [0] * 30
        except Exception as e:
            month_days_amounts_list = [0] * 30

        chart_data = {
            "months_list": months_label,
            "amounts": (
                week_days_amounts_list
                if filter_value == "weekly"
                else (
                    month_days_amounts_list
                    if filter_value == "monthly"
                    else amounts_values_list
                )
            ),
        }
        return chart_data

    def get_watchlist_analysis(self):
        filter_value = self.request.query_params.get("filter")
        # self.total_due_repayment_qs = get_total_due_repayment_qs(
        #     due_loans_qs=self.due_loans_qs
        # )
        watchlist_qs = self.missed_30_days_qs.filter(
            # end_date__lt=timezone.now().date() - timedelta(days=30)
        )

        months_label = (
            self.days_label
            if filter_value == "weekly"
            else (
                self._month_2_month_dates_list
                if filter_value == "monthly"
                else self.months_list_names
            )
        )

        amounts_values_list = []

        # Weekly Chart
        if filter_value == "weekly":
            past_one_week_amounts_qs = (
                watchlist_qs.filter(
                    # due_balance__gt=0,
                    created_at__date__gte=self._previous_week_current_week_date
                )
                .values("created_at__date")
                .annotate(missed_amount_total=Sum("missed_amount"))
            )

        # Monthly chart
        elif filter_value == "monthly":
            past_one_month_amounts_qs = (
                watchlist_qs.filter(
                    # due_balance__gt=0,
                    created_at__date__gte=self._previous_month_current_month_date
                )
                .values("created_at__date")
                .annotate(missed_amount_total=Sum("missed_amount"))
            )
        else:
            past_year_amount_qs = (
                watchlist_qs.filter(
                    # due_balance__gt=0,
                    created_at__gte=self.previous_year_current_month_start
                )
                .values("created_at__year", "created_at__month")
                .annotate(missed_amount_total=Sum("missed_amount"))
                .order_by("created_at__year")
            )

            past_year_amounts_list = [
                (
                    data["created_at__year"],
                    data["created_at__month"],
                    data["missed_amount_total"],
                )
                for data in past_year_amount_qs
            ]

            past_year_amounts_final_list = []
            for item in self.year_months_tuple_list:
                if item in [(tup[0], tup[1]) for tup in past_year_amounts_list]:
                    for trans in past_year_amounts_list:
                        if item == (trans[0], trans[1]):
                            past_year_amounts_final_list.append(trans)
                            break
                else:
                    past_year_amounts_final_list.append((0, 0, 0))
            amounts_values_list = [trans[2] for trans in past_year_amounts_final_list]

        try:
            if past_one_week_amounts_qs:
                week_days_amounts_list = [
                    amounts["missed_amount_total"]
                    for amounts in past_one_week_amounts_qs
                ]
            else:
                week_days_amounts_list = [0] * 8
        except Exception as e:
            week_days_amounts_list = [0] * 8

        try:
            if past_one_month_amounts_qs:
                month_days_amounts_list = [
                    amount["missed_amount_total"]
                    for amount in past_one_month_amounts_qs
                ]
            else:
                month_days_amounts_list = [0] * 30
        except Exception as e:
            month_days_amounts_list = [0] * 30

        chart_data = {
            "months_list": months_label,
            "amounts": (
                week_days_amounts_list
                if filter_value == "weekly"
                else (
                    month_days_amounts_list
                    if filter_value == "monthly"
                    else amounts_values_list
                )
            ),
        }
        return chart_data

    def get_defaults_analysis(self):
        filter_value = self.request.query_params.get("filter")
        # self.total_due_repayment_qs = get_total_due_repayment_qs(
        #     due_loans_qs=self.due_loans_qs
        # )
        defaults_qs = self.missed_90_days_qs.filter(
            # performance_status__in=["DEFAULTED", "LOST"]
        )

        months_label = (
            self.days_label
            if filter_value == "weekly"
            else (
                self._month_2_month_dates_list
                if filter_value == "monthly"
                else self.months_list_names
            )
        )

        amounts_values_list = []

        # Weekly Chart
        if filter_value == "weekly":
            past_one_week_amounts_qs = (
                defaults_qs.filter(
                    # due_balance__gt=0,
                    created_at__date__gte=self._previous_week_current_week_date
                )
                .values("created_at__date")
                .annotate(missed_amount_total=Sum("missed_amount"))
            )

        # Monthly chart
        elif filter_value == "monthly":
            past_one_month_amounts_qs = (
                defaults_qs.filter(
                    # due_balance__gt=0,
                    created_at__date__gte=self._previous_month_current_month_date
                )
                .values("created_at__date")
                .annotate(missed_amount_total=Sum("missed_amount"))
            )
        else:
            past_year_amount_qs = (
                defaults_qs.filter(
                    # due_balance__gt=0,
                    created_at__gte=self.previous_year_current_month_start
                )
                .values("created_at__year", "created_at__month")
                .annotate(missed_amount_total=Sum("missed_amount"))
                .order_by("created_at__year")
            )

            past_year_amounts_list = [
                (
                    data["created_at__year"],
                    data["created_at__month"],
                    data["missed_amount_total"],
                )
                for data in past_year_amount_qs
            ]

            past_year_amounts_final_list = []
            for item in self.year_months_tuple_list:
                if item in [(tup[0], tup[1]) for tup in past_year_amounts_list]:
                    for trans in past_year_amounts_list:
                        if item == (trans[0], trans[1]):
                            past_year_amounts_final_list.append(trans)
                            break
                else:
                    past_year_amounts_final_list.append((0, 0, 0))
            amounts_values_list = [trans[2] for trans in past_year_amounts_final_list]

        try:
            if past_one_week_amounts_qs:
                week_days_amounts_list = [
                    amounts["missed_amount_total"]
                    for amounts in past_one_week_amounts_qs
                ]
            else:
                week_days_amounts_list = [0] * 8
        except Exception as e:
            week_days_amounts_list = [0] * 8

        try:
            if past_one_month_amounts_qs:
                month_days_amounts_list = [
                    amount["missed_amount_total"]
                    for amount in past_one_month_amounts_qs
                ]
            else:
                month_days_amounts_list = [0] * 30
        except Exception as e:
            month_days_amounts_list = [0] * 30

        chart_data = {
            "months_list": months_label,
            "amounts": (
                week_days_amounts_list
                if filter_value == "weekly"
                else (
                    month_days_amounts_list
                    if filter_value == "monthly"
                    else amounts_values_list
                )
            ),
        }
        return chart_data

    def get_missed_repayment_classifications(self):
        _3_days_missed_amount = (
            self.missed_repayments_qs.filter(missed_days=3).aggregate(
                missed_amount_total=Sum("missed_amount")
            )["missed_amount_total"]
            or 0.00
        )

        _3_days_missed_amount = (
            self.missed_repayments_qs.filter(missed_days=8).aggregate(
                missed_amount_total=Sum("missed_amount")
            )["missed_amount_total"]
            or 0.00
        )

        above_8_days = (
            self.missed_repayments_qs.filter(missed_days__gt=8).aggregate(
                missed_amount_total=Sum("missed_amount")
            )["missed_amount_total"]
            or 0.00
        )

        past_maturity_amount = (
            self.missed_past_maturity_15_days_qs.aggregate(
                missed_amount_total=Sum("missed_amount")
            )["missed_amount_total"]
            or 0.00
        )

        data = {
            "3_days": _3_days_missed_amount,
            "one_week_missed": "",
            "above_8_days": above_8_days,
            "past_maturity": past_maturity_amount,
        }

        return data

    def get_repayment_analysis_chart(self):
        filter_value = self.request.query_params.get("filter")

        months_label = (
            self.days_label
            if filter_value == "weekly"
            else (
                self._month_2_month_dates_list
                if filter_value == "monthly"
                else self.months_list_names
            )
        )

        # Weekly Chart
        if filter_value == "weekly":
            # Get date range for past week
            date_list = [
                self._previous_week_current_week_date + timedelta(days=x)
                for x in range(8)
            ]

            # Get repayments data
            repayments_by_date = {
                x["paid_date__date"]: x["repayment_total"]
                for x in self.ajo_loan_repayment_qs.filter(
                    paid_date__date__gte=self._previous_week_current_week_date
                )
                .values("paid_date__date")
                .annotate(repayment_total=Sum("repayment_amount"))
            }

            # Fill in zeros for missing dates
            amounts_values_list = [
                repayments_by_date.get(date, 0) for date in date_list
            ]

        # Monthly chart
        elif filter_value == "monthly":
            today = self.today.date()
            current_day = today.day

            # Get last month's date while handling edge cases
            last_month = today.replace(day=1) - timedelta(days=1)  # Go to last month

            # Handle end of month cases (30th, 31st)
            if current_day in [30, 31]:
                # If last month has fewer days, use its last day
                last_day_of_last_month = last_month.day
                start_date = last_month.replace(
                    day=min(current_day, last_day_of_last_month)
                )
            else:
                # Normal case - use same day as today
                start_date = last_month.replace(day=current_day)

            end_date = today

            # Get repayments data
            monthly_repayments = (
                self.ajo_loan_repayment_qs.filter(
                    paid_date__date__gte=self._previous_month_current_month_date,
                    # paid_date__date__lte=end_date
                )
                .values("paid_date__date")
                .annotate(repayment_total=Sum("repayment_amount"))
                .order_by("paid_date__date")
            )

            # Create a dictionary with formatted dates as keys
            repayments_by_date = {}
            for repayment in monthly_repayments:
                date_key = repayment["paid_date__date"].strftime("%b %d")
                if date_key in repayments_by_date:
                    repayments_by_date[date_key] += repayment["repayment_total"]
                else:
                    repayments_by_date[date_key] = repayment["repayment_total"]

            # Fill in zeros for missing dates using the existing labels
            amounts_values_list = [
                repayments_by_date.get(date_label, 0)
                for date_label in self._month_2_month_dates_list
            ]

        # Yearly chart
        else:
            # Get repayments data
            repayments_by_month = {
                (x["paid_date__year"], x["paid_date__month"]): x["repayment_total"]
                for x in self.ajo_loan_repayment_qs.filter(
                    paid_date__gte=self.previous_year_current_month_start
                )
                .values("paid_date__year", "paid_date__month")
                .annotate(repayment_total=Sum("repayment_amount"))
            }

            # Fill in zeros for missing months using year_months_tuple_list
            amounts_values_list = [
                repayments_by_month.get(month_tuple, 0)
                for month_tuple in self.year_months_tuple_list
            ]

        chart_data = {"months_list": months_label, "amounts": amounts_values_list}
        return chart_data


class GroupLoansClass:
    def __init__(self, request):
        self.request = request
        filter, disburse_filter = get_date_filter(
            filter_value=request.query_params.get("filter")
        )
        # Get Supervisor USERS
        self.request = request
        user = self.request.user
        self.supervisor_users_list = []
        agency_banking_supervisors = AgencyBankingClass.get_agency_supervisor_details()
        if (
            "success" in agency_banking_supervisors
            and agency_banking_supervisors.get("success") == True
        ):
            supervisors_list = agency_banking_supervisors.get("supervisors_teams_list")

            for supervisor in supervisors_list:
                if supervisor.get("supervisor_user_id") == user.customer_user_id:
                    self.supervisor_users_list = supervisor.get("users_list", [])
                    break
        else:
            self.supervisor_users_list = []

        if self.request.user.is_staff:
            self.group_loans = AjoSepo.objects.filter()
        else:
            self.group_loans = AjoSepo.objects.filter(
                user__customer_user_id__in=self.supervisor_users_list,
                # is_activated=True
            )

    def get_group_loans_request_list(self):
        group_loans = AjoSaving.objects.filter(
            group__in=self.group_loans, is_activated=True
        )

        loans_requests_list = (
            group_loans.annotate(
                group_name=F("group__name"),
                group_no=F("group__participants"),
                total_amount=F("group__participants"),
                loan_term=F("group__loan_tenure"),
                applied_date=F("group__created_at"),
                agent=F("user__email"),
                branch=Value(""),
                status=Value(""),
            )
            .values(
                "group__group_id",
                "group_name",
                "group_no",
                "loan_term",
                "applied_date",
                "agent",
                "branch",
                "status",
            )
            .distinct("group")
        )

        data = {"data": loans_requests_list, "count": loans_requests_list.count()}
        return data

    def get_pending_group_loans_request_list(self):
        pending_group_loans_qs = self.group_loans.filter(status="PENDING")
        group_loans = AjoSaving.objects.filter(group__in=pending_group_loans_qs)

        loans_requests_list = (
            group_loans.annotate(
                group_name=F("group__name"),
                group_no=F("group__participants"),
                total_amount=F("group__participants"),
                loan_term=F("group__loan_tenure"),
                applied_date=F("group__created_at"),
                agent=F("user__email"),
                branch=Value(""),
                status=Value(""),
            )
            .values(
                "group__group_id",
                "group_name",
                "group_no",
                "loan_term",
                "applied_date",
                "agent",
                "branch",
                "status",
            )
            .distinct("group")
        )

        data = {"data": loans_requests_list, "count": loans_requests_list.count()}
        return data

    def get_approved_group_loans_request_list(self):
        approved_group_loans_qs = self.group_loans.filter(status="RUNNING")
        group_loans = AjoSaving.objects.filter(group__in=approved_group_loans_qs)

        loans_requests_list = (
            group_loans.annotate(
                group_name=F("group__name"),
                group_no=F("group__participants"),
                total_amount=F("group__participants"),
                loan_term=F("group__loan_tenure"),
                applied_date=F("group__created_at"),
                agent=F("user__email"),
                branch=Value(""),
                status=Value(""),
            )
            .values(
                "group__group_id",
                "group_name",
                "group_no",
                "loan_term",
                "applied_date",
                "agent",
                "branch",
                "status",
            )
            .distinct("group")
        )

        data = {"data": loans_requests_list, "count": loans_requests_list.count()}
        return data

    def get_loans_in_a_group(self):
        from loans.models import LoanEligibility

        group_id = self.request.query_params.get("group_id")
        group = self.group_loans.get(group_id=group_id)
        group_savings = AjoSaving.objects.filter(group=group, is_activated=True)
        eligibility_qs = LoanEligibility.objects.filter(saving__in=group_savings)

        loans_requests_list = qs = (
            AjoLoan.objects.filter(eligibility__in=eligibility_qs)
            .annotate(
                loan_id=F("id"),
                borrower_name=Concat(
                    "borrower__first_name",
                    Value(" "),
                    "borrower__last_name",
                    output_field=CharField(),
                ),
                loan_amount=F("amount"),
                loan_term=F("tenor"),
                applied_date=F("created_at"),
                agent_fullname=Concat(
                    "agent__first_name",
                    Value(" "),
                    "agent__last_name",
                    output_field=CharField(),
                ),
                branch=Value(""),
                loan_status=F("status"),
                agent_name=F("agent__username"),
            )
            .values(
                "loan_id",
                "borrower_name",
                "loan_amount",
                "loan_term",
                "applied_date",
                "agent_name",
                "branch",
                "loan_status",
                "agent_fullname",
                "verification_stage",
            )
        )

        data = {"data": loans_requests_list, "count": loans_requests_list.count()}
        return data
