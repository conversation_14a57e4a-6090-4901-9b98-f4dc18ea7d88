from django.contrib import admin
from django.conf import settings
from import_export.admin import ImportExportModelAdmin

from accounts.agency_banking import paybox_login
from collections_app.resources import *
import requests
import json

# Register your models here.


class AgentDailyAttendanceResourceAdmin(ImportExportModelAdmin):
    resource_class = AgentDailyAttendanceResource
    date_hierarchy = "created_at"
    list_filter = ["activity", "user_type"]
    search_fields = ["agent__email", "agent__user_phone"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PayrollLogResourceAdmin(ImportExportModelAdmin):
    resource_class = PayrollLogResource
    date_hierarchy = "created_at"
    # list_filter = ["activity", "user_type"]

    # date_hierarchy = 'date_created'
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def send_payroll_to_paybox(self, request, queryset: PayrollLog):
        """
        On demand action to share seeds loan officers payroll data with paybox
        """
        import ast
        for query in queryset:
            query: PayrollLog
            data = ast.literal_eval(query.request_data)

            # Send Data to Paybox
            paybox_user = paybox_login().get("access")
            url = f"{settings.PAYBOX_URL}/payroll/edit_employee_payroll_savings/?company_uuid={settings.PAYBOX_SEEDS_COMPANY_UUID}"

            headers = {
                "Authorization": f"Bearer {paybox_user}",
                "Content-Type": "application/json",
            }

            try:
                req = requests.post(url, headers=headers, data=json.dumps(data))
                if req.status_code == 200:
                    res = req.json()
                    query.sent_to_paybox = True
                else:
                    res = req.text
            except Exception as e:
                res = str(e)
            
            query.response_data = res
            query.save()

            self.message_user(request, res)

    send_payroll_to_paybox.short_description = "Send payroll data to paybox"
    send_payroll_to_paybox.allow_tags = True

    actions = [
        send_payroll_to_paybox,
        ]


admin.site.register(AgentDailyAttendance, AgentDailyAttendanceResourceAdmin)
admin.site.register(PayrollLog, PayrollLogResourceAdmin)
