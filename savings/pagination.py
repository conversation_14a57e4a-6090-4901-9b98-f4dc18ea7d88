from collections import OrderedDict

from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response


class CustomPagination(PageNumberPagination):
    page_size = 20  # Sets the number of items per page
    page_size_query_param = "page_size"  # customize the query parameter for controlling page size
    max_page_size = 50  # Set the maximum allowed page size

    def get_paginated_response(self, data):
        return Response(
            OrderedDict(
                [
                    ("status", True),
                    ("count", self.page.paginator.count),
                    ("next", self.get_next_link()),
                    ("previous", self.get_previous_link()),
                    ("results", data),
                ]
            )
        )
