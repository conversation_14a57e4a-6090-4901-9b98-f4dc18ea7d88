"""
Custom Request Class for DRF to Include 'saver' Property

This module extends the default DRF `Request` class to include a `saver` property,
allowing seamless access to the `Saver` object associated with the authenticated user.
The `saver` attribute works similarly to the `user` attribute, making it available
throughout Django and DRF views.

### Purpose
The goal is to simplify access to the `Saver` object in views without requiring 
repeated database queries or manual lookups in every view. This approach ensures:
- Consistent behavior across the application.
- Lazy loading of the `Saver` object, preventing unnecessary database hits.
- Full compatibility with Django's and DRF's default `Request` behavior.

### Usage
1. Ensure this file is correctly referenced in your DRF settings under `DEFAULT_REQUEST_CLASS`:
   ```python
   REST_FRAMEWORK = {
       'DEFAULT_REQUEST_CLASS': 'your_app.custom_request.CustomRequest',
   }
"""

# from rest_framework.request import Request
# from django.utils.functional import SimpleLazyObject

# from ajo.models import AjoUser


# def get_saver(user):
#     """
#     Fetches the Saver associated with the given user.
#     """
#     if user.is_authenticated:
#         return AjoUser.objects.filter(user=user).first()

#     return None


# class CustomRequest(Request):
#     @property
#     def saver(self):
#         """
#         Lazily fetch the saver object for the authenticated user.
#         """
#         if not hasattr(self, "_cached_saver"):
#             self._cached_saver = SimpleLazyObject(lambda: get_saver(self.user))
#         return self._cached_saver
