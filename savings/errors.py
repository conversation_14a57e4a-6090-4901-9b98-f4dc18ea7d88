from django.http import JsonResponse
from django.views.defaults import server_error


def custom_server_error(request, *args, **kwargs):
    response = JsonResponse(
        {
            "status": False,
            "message": "Internal Server Error",
            "error": "500",
            "error_message": "An unexpected error occurred on the server.",
        }
    )
    response.status_code = 500
    return response


def custom_page_not_found(request, exception):
    response = JsonResponse(
        {
            "status": False,
            "message": "Not Found",
            "error": "404",
            "error_message": "The requested resource was not found.",
        }
    )
    response.status_code = 404
    return response


from django.http import HttpResponse
from django.shortcuts import render


def trigger_error(request):
    division_by_zero = 1 / 0  # This will raise a ZeroDivisionError
    return HttpResponse("This should never get executed.")
