from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include, path
from drf_yasg import openapi
from drf_yasg.views import get_schema_view
from rest_framework import permissions
from rest_framework_simplejwt.views import TokenObtainPairView

schema_view = get_schema_view(
    openapi.Info(
        title="Savings API",
        default_version="v1",
        description="An API for Agency Savings",
        terms_of_service="#",
        contact=openapi.Contact(email="#"),
        license=openapi.License(name="Test License"),
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
)

admin.site.site_header = "Seeds and Penny"

urlpatterns = [
    path("admin/", admin.site.urls),
    # Local apps
    # path("user/login/create/", TokenObtainPairView.as_view(), name="token_obtain_pair"),
    path("accounts/", include("accounts.urls")),
    path("payment/", include("payment.urls")),
    path("chestlock/", include("chestlock.urls.chestlock_urls")),
    path("quicksavings/", include("quicksavings.urls")),
    path("halal/", include("halal.urls")),
    path("ajo/", include("ajo.urls.ajo_urls")),
    path("ajosepo/", include("ajo.urls.ajosepo_urls")),
    path("loans/", include("loans.urls")),
    path("api/admin/", include("admin_dashboard.urls")),
    path("onlending/", include("chestlock.urls.onlending_urls")),
    path("api/collections/", include("collections_app.urls")),
    path("location/api/v2/", include("location_app.urls")),
    path("api/cluster/", include("cluster.urls")),
    path("cards/", include("cards.urls")),
    path("call_center/", include("call_center.urls")),
    # Swagger Docs url
    path("", schema_view.with_ui("swagger", cache_timeout=0), name="schema-swagger-ui"),
    # Redoc Docs url
    path("redoc", schema_view.with_ui("redoc", cache_timeout=0), name="schema-redoc"),
    path("apiv1/gini-engine/", include("loans.giniengine.urls")),
    path("api/v1/lite/", include("lite.urls")),
    path("api/v1/web/", include("web.urls")),
    path("api/v1/bnpl/", include("bnpl_app.urls")),
    path("api/mida/", include("mida.urls")),
    path("api/ai/", include("ai_hub.urls")),
]

# Add media URL pattern for development environment
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

handler404 = "savings.errors.custom_page_not_found"
handler500 = "savings.errors.custom_server_error"
