import logging
import os
from datetime import datetime, timedelta
from pathlib import Path

import sentry_sdk
from corsheaders.defaults import default_headers
from decouple import Csv, config
from sentry_sdk.integrations.django import DjangoIntegration

logging.basicConfig(level=logging.DEBUG)
LOGGER = logging.getLogger("__name__")

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config("SECRET_KEY")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = int(config("DEBUG", default=False))
ENVIRONMENT = config("ENVIRONMENT")

ALLOWED_HOSTS = config("ALLOWED_HOSTS", default="127.0.0.1", cast=Csv())

# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # 3rd party apps
    "rest_framework",
    "rest_framework_simplejwt",
    "drf_yasg",
    "corsheaders",
    "import_export",
    "django_extensions",
    # Celery apps
    "django_celery_beat",
    "django_celery_results",
    # Local apps
    "accounts",
    "ajo",
    "chestlock",
    "payment",
    "quicksavings",
    "halal",
    "admin_dashboard",
    "loans",
    "collections_app",
    "singlelogin",
    "location_app",
    "cluster",
    "lite",
    "cards",
    "bnpl_app",
    "web",
    "call_center",
    "mida",
    "health_insurance",
    "ai_hub"
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "corsheaders.middleware.CorsMiddleware",  # CORS Middleware
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "savings.middlewares.EntryPointMiddleware",  # Custom Middleware
    "savings.middlewares.SaverEntryPointMiddleware",  # Custom Middleware
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "singlelogin.middlewares.SingleLoginCaseMiddleware",  # singlelogin middleware
]

ROOT_URLCONF = "savings.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [os.path.join(BASE_DIR, "templates")],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "savings.wsgi.application"

# Database
# https://docs.djangoproject.com/en/4.1/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": config("DB_NAME"),
        "USER": config("DB_USER"),
        "PASSWORD": config("DB_PASS"),
        "HOST": config("DB_HOST"),
        "PORT": config("DB_PORT"),
    }
    # This is the default sqlite3 database settings
    # "default": {
    #     "ENGINE": "django.db.backends.sqlite3",
    #     "NAME": BASE_DIR / "db.sqlite3",
    # }
}

# Password validation
# https://docs.djangoproject.com/en/4.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.1/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "Africa/Lagos"

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.1/howto/static-files/

STATIC_URL = "static/"

# Default primary key field type
# https://docs.djangoproject.com/en/4.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"


if ENVIRONMENT == "production":
    sentry_sdk.init(
        dsn="https://<EMAIL>/4505721161908224",
        # dsn="https://<EMAIL>/4506983535738880",
        integrations=[DjangoIntegration()],
        # If you wish to associate users to errors (assuming you are using
        # django.contrib.auth) you may enable sending PII data.
        send_default_pii=True,
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for performance monitoring.
        # We recommend adjusting this value in production.
        # traces_sample_rate=0.001,
        traces_sample_rate=0.1,
        # To set a uniform sample rate
        # Set profiles_sample_rate to 1.0 to profile 100%
        # of sampled transactions.
        # We recommend adjusting this value in production,
        profiles_sample_rate=1.0,
    )

# REST framework settings
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework_simplejwt.authentication.JWTAuthentication",
    ),
    "NON_FIELD_ERRORS_KEY": "error",
    # "DEFAULT_REQUEST_CLASS": "savings.custom_request.CustomRequest",
}

# JWT settings
SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(days=13),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=10),
    "USER_ID_FIELD": "customer_user_id",
    # "USER_ID_CLAIM": "customer_user_id",
}

# Specifying User model
AUTH_USER_MODEL = "accounts.CustomUser"

# Swagger docs settings
SWAGGER_SETTINGS = {
    "PERSIST_AUTH": True if DEBUG else False,  # No suitable for prod
    "SECURITY_DEFINITIONS": {
        "Auth Token eg [Bearer (JWT)]": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header",
        }
    },
}

# django cache settings.
CACHES = {
    # default django cache setting
    # "default": {
    #     "BACKEND": "django.core.cache.backends.db.DatabaseCache",
    #     "LOCATION": "my_cache_table",
    # }
    # Redis Django cache setting
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://127.0.0.1:6379/1",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        },
    }
}
DEFAULT_CACHE_TIMEOUT = 20

AJO_PAYMENT_TEMPLATE_ID = config("AJO_PAYMENT_TEMPLATE_ID")

# Singlelogin LOGIN URL
LOGIN_URL = "/payment/savings-balance-summary/"

# AGENCY BANKING
if ENVIRONMENT == "development":
    # AGENCY BANKING
    AGENCY_BANKING_BASE_URL = config("AGENCY_BANKING_BASE_URL_TEST")
    AGENCY_AUTH_TOKEN = config("AGENCY_AUTH_TOKEN_TEST")
    AGENCY_BANKING_SUPER_TOKEN = config("AGENCY_BANKING_SUPER_TOKEN_TEST")
    AGENCY_BANKING_USEREMAIL = config("AGENCY_BANKING_USEREMAIL_TEST")
    AGENCY_BANKING_PASSWORD = config("AGENCY_BANKING_PASSWORD_TEST")
    AGENCY_BANKING_TRANSACTION_PIN = config("AGENCY_BANKING_TRANSACTION_PIN_TEST")
    # PAYSTACK
    PAYSTACK_AUTHORIZATION_TOKEN = config("PAYSTACK_AUTHORIZATION_TOKEN_TEST")
    # OTP
    WHISPER_URL = config("WHISPER_URL")
    WHISPER_TOKEN = config("WHISPER_TOKEN")
    SMS_VOICE_APPLICATION_ID = config("SMS_VOICE_APPLICATION_ID")
    SMS_VOICE_MESSAGE_ID = config("SMS_VOICE_MESSAGE_ID")
    WHATSAPP_BASE_URL = config("WHATSAPP_BASE_URL")
    WHATSAPP_TOKEN = config("WHATSAPP_TOKEN")
    # EMAIL
    MAILGUN_URL = config("MAILGUN_URL")
    MAILGUN_API_KEY = config("MAILGUN_API_KEY")
    # LIBERTY BVN
    LIBERTY_BVN_URL = config("LIBERTY_BVN_URL")
    LIBERTY_BVN_TOKEN = config("LIBERTY_BVN_TOKEN")
    # IDENTITY PASS
    ID_PASS_BASE_URL = config("ID_PASS_BASE_URL")
    ID_PASS_API_KEY = config("ID_PASS_API_KEY")
    ID_PASS_APP_ID = config("ID_PASS_APP_ID")
    # AFRICAS_TALKING
    AFRICAS_TALKING_API_KEY = config("AFRICAS_TALKING_API_KEY_TEST")
    AFRICAS_TALKING_SERVICE_CODE = config("AFRICAS_SERVICE_CODE_TEST")
    # VFD
    AGENCY_BANKING_CALLBACK_TOKEN = config("AGENCY_BANKING_CALLBACK_TOKEN")
    # CARD_WITHDRAW IP
    CARD_PURCHASE_IP = config("CARD_PURCHASE_IP")
    # AJO USSD
    AJO_CALLBACK_TOKEN = config("AJO_CALLBACK_TOKEN")
    # EMPTY TEMPLATE ID
    EMPTY_TEMPLATE_ID = config("EMPTY_TEMPLATE_ID")
    # LIBERTY CORPORATE ID
    LIBERTY_CORPORATE_ID = config("LIBERTY_CORPORATE_ID")

    AJO_ROSCA_COLLECTION_TEMPLATE = config("AJO_ROSCA_COLLECTION_TEMPLATE")
    AJO_ROSCA_CONTRIBUTION_TEMPLATE = config("AJO_ROSCA_CONTRIBUTION_TEMPLATE")
    CRC_BASE_URL = config("CRC_TEST_BASE_URL")

    # Google API Credentials
    GOOGLE_SHEET_CREDENTIALS = config("GOOGLE_SHEET_CREDENTIALS")
    GOOGLE_SHEET_LINK = config("GOOGLE_SHEET_LINK")


else:
    # AGENCY BANKING
    AGENCY_BANKING_BASE_URL = config("AGENCY_BANKING_BASE_URL")
    AGENCY_AUTH_TOKEN = config("AGENCY_AUTH_TOKEN")
    AGENCY_BANKING_SUPER_TOKEN = config("AGENCY_BANKING_SUPER_TOKEN")
    AGENCY_BANKING_USEREMAIL = config("AGENCY_BANKING_USEREMAIL")
    AGENCY_BANKING_PASSWORD = config("AGENCY_BANKING_PASSWORD")
    AGENCY_BANKING_TRANSACTION_PIN = config("AGENCY_BANKING_TRANSACTION_PIN")
    # PAYSTACK
    PAYSTACK_AUTHORIZATION_TOKEN = config("PAYSTACK_AUTHORIZATION_TOKEN")
    # OTP
    WHISPER_URL = config("WHISPER_URL")
    WHISPER_TOKEN = config("WHISPER_TOKEN")
    SMS_VOICE_APPLICATION_ID = config("SMS_VOICE_APPLICATION_ID")
    SMS_VOICE_MESSAGE_ID = config("SMS_VOICE_MESSAGE_ID")
    WHATSAPP_BASE_URL = config("WHATSAPP_BASE_URL")
    WHATSAPP_TOKEN = config("WHATSAPP_TOKEN")
    # EMAIL
    MAILGUN_URL = config("MAILGUN_URL")
    MAILGUN_API_KEY = config("MAILGUN_API_KEY")
    # LIBERTY BVN
    LIBERTY_BVN_URL = config("LIBERTY_BVN_URL")
    LIBERTY_BVN_TOKEN = config("LIBERTY_BVN_TOKEN")
    # IDENTITY PASS
    ID_PASS_BASE_URL = config("ID_PASS_BASE_URL")
    ID_PASS_API_KEY = config("ID_PASS_API_KEY")
    ID_PASS_APP_ID = config("ID_PASS_APP_ID")
    # AFRICAS_TALKING
    AFRICAS_TALKING_API_KEY = config("AFRICAS_TALKING_API_KEY")
    AFRICAS_TALKING_SERVICE_CODE = config("AFRICAS_SERVICE_CODE")
    # VFD
    AGENCY_BANKING_CALLBACK_TOKEN = config("AGENCY_BANKING_CALLBACK_TOKEN")
    # CARD_WITHDRAW IP
    CARD_PURCHASE_IP = config("CARD_PURCHASE_IP")
    # AJO USSD
    AJO_CALLBACK_TOKEN = config("AJO_CALLBACK_TOKEN")
    # EMPTY SMS ID
    EMPTY_TEMPLATE_ID = config("EMPTY_TEMPLATE_ID")
    # LIBERTY CORPORATE ID
    LIBERTY_CORPORATE_ID = config("LIBERTY_CORPORATE_ID")
    CRC_BASE_URL = config("CRC_LIVE_BASE_URL")

    AJO_ROSCA_COLLECTION_TEMPLATE = config("AJO_ROSCA_COLLECTION_TEMPLATE")
    AJO_ROSCA_CONTRIBUTION_TEMPLATE = config("AJO_ROSCA_CONTRIBUTION_TEMPLATE")

    # Google API Credentials
    GOOGLE_SHEET_CREDENTIALS = config("GOOGLE_SHEET_CREDENTIALS")
    GOOGLE_SHEET_LINK = config("GOOGLE_SHEET_LINK")

ADMIN_USER_EMAIL = config("ADMIN_USER_EMAIL", cast=Csv())

# AGENCY_BANKING_BASE_URL_TEST = config("AGENCY_BANKING_BASE_URL_TEST")
# AGENCY_AUTH_TOKEN_TEST = config("AGENCY_AUTH_TOKEN_TEST")
# AGENCY_BANKING_SUPER_TOKEN_TEST = config("AGENCY_BANKING_SUPER_TOKEN_TEST")

# LOAN SETTINGS
LOAN_BUDDY_PHONE_NO = config("LOAN_BUDDY_PHONE_NO")
LOAN_DISK_SEC_KEY = config("LOAN_DISK_SEC_KEY")
LOANDISK_PUBLIC_KEY = config("LOANDISK_PUBLIC_KEY")
LOAN_DISK_BRANCH_ID = config("LOAN_DISK_BRANCH_ID")
LOAN_DISK_GO_COLLECT_BRANCH_ID = config("LOAN_DISK_GO_COLLECT_BRANCH_ID")
CORE_BANKING_USER = config("CORE_BANKING_USER")
CORE_BANKING_PASS = config("CORE_BANKING_PASS")
WEB_TOKEN = config("WEB_TOKEN")
YOU_VERIFY_PROD_TOKEN = config("YOU_VERIFY_PROD_TOKEN")
YOU_VERIFY_SAN_BOX_TOKEN = config("YOU_VERIFY_SAN_BOX_TOKEN")
LIBERTY_BIOMETRICS_EMAIL = config("LIBERTY_BIOMETRICS_EMAIL")
LIBERTY_BIOMETRICS_PASSWORD = config("LIBERTY_BIOMETRICS_PASSWORD")
# AJO_LOANS_POOL_EMAIL=config("AJO_LOANS_POOL_EMAIL")
LOAN_AGENCY_BANKING_USEREMAIL = config("LOAN_AGENCY_BANKING_USEREMAIL")
LOAN_AGENCY_BANKING_PASSWORD = config("LOAN_AGENCY_BANKING_PASSWORD")
CRC_TOKEN = config("CRC_TOKEN")
CRC_USERNAME = config("CRC_USERNAME")
CRC_PASSWORD = config("CRC_PASSWORD")
LOAN_AGENCY_BANKING_TRANSACTION_PIN = config("LOAN_AGENCY_BANKING_TRANSACTION_PIN")
REPAYMENT_CALLBACK_TOKEN = config("REPAYMENT_CALLBACK_TOKEN")

# ENROLLMENT LINK
ENROLLMENT_BASE_URL = config("ENROLLMENT_BASE_URL")


# LOCATIONS API
OPEN_CAGE_API_KEY = config("OPEN_CAGE_API_KEY")
WEATHER_API_KEY = config("WEATHER_API_KEY")
GEOCODE_API_KEY = config("GEOCODE_API_KEY")

# REPAYMENT SETTINGS
LOAN_EXTRA_DAYS = 4
REPAYMENT_QUALIFICATION = 0.9

# LIBERTY USSD
LIBERTY_USSD_BASE_URL = config("LIBERTY_USSD_BASE_URL")
LIBERTY_USSD_USERNAME = config("LIBERTY_USSD_USERNAME")
LIBERTY_USSD_PASSWORD = config("LIBERTY_USSD_PASSWORD")

# DOJAH SETTINGS
DOJAH_SANDBOX_TOKEN = config("DOJAH_SANDBOX_TOKEN")
DOJAH_APP = config("DOJAH_APP")
DOJAH_PROD_TOKEN = config("DOJAH_PROD_TOKEN")

# Celery settings
# changed below from "redis://127.0.0.1:6379"
CELERY_BROKER_URL = "redis://localhost:6379"
CELERY_ACCEPT_CONTENT = ["application/json"]
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_SERIALIZER = "json"
CELERY_TIMEZONE = TIME_ZONE
CELERY_RESULT_BACKEND = "django-db"
CELERY_RESULT_EXTENDED = True
# CELERY_BEAT_SCHEDULE = {
#     "loan_data": "mida.tasks.post_loan_to_mida",
#     "schedule": timedelta(seconds=60),
# }

# Django CORS settings
CORS_ALLOW_METHODS = [
    "DELETE",
    "GET",
    "OPTIONS",
    "PATCH",
    "POST",
    "PUT",
]

CORS_ALLOW_ALL_ORIGINS = True

# Updated because of the frontend CORS error
CORS_ALLOW_HEADERS = (
    *default_headers,
    "Email-Address",
    "Webhook-Header",
)

PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
STATIC_URL = "/static/"
STATIC_ROOT = os.path.join(BASE_DIR, "static")

# This is the media URL for serving the files uploaded
MEDIA_ROOT = os.path.join(BASE_DIR, "media")
MEDIA_URL = "/media/"

# Django Console mail backend setting
EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"


# WHISPER SMS TEMPLATE ID
LOAN_OFFICER_SMS_TEMPLATE_ID = config("LOAN_OFFICER_SMS_TEMPLATE_ID")
LOAN_BORROWER_SMS_TEMPLATE_ID = config("LOAN_BORROWER_SMS_TEMPLATE_ID")
APPROVED_LOAN_TO_BORROWER_SMS_TEMPLATE_ID = config(
    "APPROVED_LOAN_TO_BORROWER_SMS_TEMPLATE_ID"
)


# Monnify
MONNIFY_BASE_URL = config("MONNIFY_BASE_URL")
MONNIFY_API_KEY = config("MONNIFY_API_KEY")
MONNIFY_SECRET_KEY = config("MONNIFY_SECRET_KEY")
MONNIFY_SOURCE_ACCOUNT = config("MONNIFY_SOURCE_ACCOUNT")
LOAN_REPAYMENT_USER_EMAIL = config("LOAN_REPAYMENT_USER_EMAIL")
LOAN_REPAYMENT_USER_PASSWORD = config("LOAN_REPAYMENT_USER_PASSWORD")
ESCROW_USER_EMAIL = config("ESCROW_USER_EMAIL")
ESCROW_USER_PASSWORD = config("ESCROW_USER_PASSWORD")
LOAN_COMMISSIONS_USER_EMAIL = config("LOAN_COMMISSIONS_USER_EMAIL")
LOAN_COMMISSIONS_USER_PASSWORD = config("LOAN_COMMISSIONS_USER_PASSWORD")
AGENCY_BANKING_DISBURSEMENT_USEREMAIL = config("AGENCY_BANKING_DISBURSEMENT_USEREMAIL")
AGENCY_BANKING_DISBURSEMENT_PASSWORD = config("AGENCY_BANKING_DISBURSEMENT_PASSWORD")
MONNIFY_SOURCE_ACCOUNT_NUMBER = config("MONNIFY_SOURCE_ACCOUNT_NUMBER")
BNPL_USER_EMAIL = config("BNPL_USER_EMAIL")
BNPL_USER_PASS = config("BNPL_USER_PASS")
BNPL_USER_PIN = config("BNPL_USER_PIN")
MONNIFY_ACCOUNT_NAME = config("MONNIFY_ACCOUNT_NAME")
MONNIFY_BANK_CODE = config("MONNIFY_BANK_CODE")
MONNIFY_BANK_NAME = config("MONNIFY_BANK_NAME")
ESCROW_TRANSACTION_PIN = config("ESCROW_TRANSACTION_PIN")
REPAYMENT_TRANSACTION_PIN = config("REPAYMENT_TRANSACTION_PIN")
COMMISSIONS_TRANSACTION_PIN = config("COMMISSIONS_TRANSACTION_PIN")
MONNIFY_COLLECTIONS_ACCOUNT_NUMBER = config("MONNIFY_COLLECTIONS_ACCOUNT_NUMBER")


# CLUSTER SETTINGS
CLUSTER_INVITE_BASE_LINK = "https://seeds.libertypayng.com/cluster/invite-member"

# SPACES Settings
AWS_S3_ACCESS_KEY_ID = config("AWS_S3_ACCESS_KEY_ID")
AWS_S3_SECRET_ACCESS_KEY = config("AWS_S3_SECRET_ACCESS_KEY")
AWS_STORAGE_BUCKET_NAME = config("AWS_STORAGE_BUCKET_NAME")
AWS_S3_ENDPOINT_URL = config("AWS_S3_ENDPOINT_URL")
AWS_S3_REGION_NAME = config("AWS_S3_REGION_NAME")
AWS_S3_OBJECT_PARAMETERS = {
    "CacheControl": "max-age=86400",
}

# Paybox
PAYBOX_URL = config("PAYBOX_URL")
PAYBOX_TOKEN = config("PAYBOX_TOKEN")
GINI_MACHINE_APIKEY = config("GINI_MACHINE_APIKEY")
PAYBOX_COMPANY_UUID = config("PAYBOX_COMPANY_UUID")
PAYBOX_SEEDS_COMPANY_UUID = config("PAYBOX_SEEDS_COMPANY_UUID")
PAYBOX_USER_EMAIL = config("PAYBOX_USER_EMAIL")
PAYBOX_USER_PASSWORD = config("PAYBOX_USER_PASSWORD")

# Easy control
EASYCRTL_USERNAME = config("EASYCRTL_USERNAME")
EASYCRTL_PASSWORD = config("EASYCRTL_PASSWORD")

# CRC Upload
CRC_USER_ID = config("CRC_USER_ID")

# Liberty Life
LIBERTY_LIFE_USERNAME = config("LIBERTY_LIFE_USERNAME")
LIBERTY_LIFE_PASSWORD = config("LIBERTY_LIFE_PASSWORD")
LIBERTY_LIFE_AGENCY_USER_EMAIL = config("LIBERTY_LIFE_AGENCY_USER_EMAIL")
LIBERTY_LIFE_AGENCY_TRANSACTION_PIN = config("LIBERTY_LIFE_AGENCY_TRANSACTION_PIN")
LIBERTY_LIFE_AGENCY_USER_PASSWORD = config("LIBERTY_LIFE_AGENCY_USER_PASSWORD")
LIBERTY_LIFE_BASE_URL = config("LIBERTY_LIFE_BASE_URL")
NEM_BUDDY_PHONE_NUMBER = config("NEM_BUDDY_PHONE_NUMBER") # HOLD THE ACTIVATION FEE/RENEWAL AMOUNT
LIBERTY_LIFE_BUDDY_PHONE_NUMBER = config("LIBERTY_LIFE_BUDDY_PHONE_NUMBER") # HOLD DAILY FEE CHARGED FROM USER'S DAILY REPAYMENT

# Paystack Api
PAYSTACK_API_SECRET = config("PAYSTACK_API_SECRET")
PAYSTACK_BASE_URL = config("PAYSTACK_BASE_URL")

# Fernet
FERNET_KEY = config("FERNET_KEY_3")
CAN_MODIFY_RECORDS = config("CAN_MODIFY_RECORDS")

LIBERTY_CARD_BASE_URL = config("LIBERTY_CARD_BASE_URL")

LIBERTY_CARD_API_KEY = config("LIBERTY_CARD_API_KEY")

CARD_SETTLEMENT_WALLET_ID = config("CARD_SETTLEMENT_WALLET_ID")
OPENAI_API_KEY = config("OPENAI_API_KEY")
MONO_SECRET_KEY = config("MONO_SECRET_KEY")
SEEDS_AND_PENNY_WHISPER_TOKEN = config("SEEDS_AND_PENNY_WHISPER_TOKEN")

##### 3cx Call Center
THREECX_API_URL = config("THREECX_API_URL")
THREECX_API_KEY = config("THREECX_API_KEY")
THREECX_USERNAME = config("THREECX_USERNAME")
THREECX_PASSWORD = config("THREECX_PASSWORD")

##### Liberty credi 
LIBERTY_CREDI_TOKEN = config("LIBERTY_CREDI_TOKEN")
 
LOGS_DIR = os.path.join(BASE_DIR, "logs")  # Define the logs directory
os.makedirs(LOGS_DIR, exist_ok=True)  # Ensure the logs directory exists

LOG_FILE = os.path.join(
    LOGS_DIR, f"errors_{datetime.now().strftime('%Y-%m-%d')}.log"
)  # Log file with today's date

LOGGING = {
    "version": 1,  # Version of the logging configuration
    "disable_existing_loggers": False,  # Keep existing loggers active
    "formatters": {  # Define log message formats
        "verbose": {  # Detailed format including timestamp, module, and message
            "format": "{levelname} {asctime} {module} {message}",
            "style": "{",
        },
        "simple": {  # Simple format with only log level and message
            "format": "{levelname} {message}",
            "style": "{",
        },
    },
    "handlers": {  # Define how logs are handled
        "console": {  # Logs to the console (terminal)
            "level": "DEBUG",  # Capture logs at DEBUG level and above
            "class": "logging.StreamHandler",  # Outputs logs to the console
            "formatter": "simple",  # Uses the simple formatter
        },
        "file": {  # Logs errors to a file
            "level": "ERROR",  # Capture only ERROR level logs and above
            "class": "logging.FileHandler",  # Writes logs to a file
            "filename": LOG_FILE,  # File path for the log file (with date)
            "formatter": "verbose",  # Uses the verbose formatter
        },
    },
    "loggers": {  # Define loggers for specific parts of Django
        "django": {  # Django’s main logger
            "handlers": ["console", "file"],  # Send logs to console and file
            "level": "INFO",  # Capture INFO and above (so runserver logs show)
            "propagate": True,  # Allow logs to propagate to the root logger
        },
        "django.utils.autoreload": {  # Disable autoreload logs
            "handlers": [],  # No handlers, so logs are ignored
            "level": "CRITICAL",  # Capture only CRITICAL logs
            "propagate": False,  # Prevent propagation
        },
    },
    "root": {  # Root logger (handles all unhandled logs)
        "handlers": ["console", "file"],  # Logs to console and file
        "level": "DEBUG",  # Capture everything at DEBUG level and above
    },
}
