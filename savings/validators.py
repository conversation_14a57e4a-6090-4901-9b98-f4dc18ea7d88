

from django.conf import settings
from django.forms import ValidationError


   


def validate_file_size(value):
    """
    Get the maximum file size from settings (in bytes)
    """
    max_size = getattr(settings, 'MAX_FILE_SIZE', 5 * 1024 * 1024)  # 5MB by default

    if value.size > max_size:
        raise ValidationError(f"File size is too large. Maximum size allowed is {int(max_size/1048576)} MB.")
    
    return value
