from __future__ import absolute_import, unicode_literals

import os
from datetime import timedelta

from celery import Celery
from celery.schedules import crontab
from django.conf import settings

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "savings.settings")
app = Celery("savings")
app.config_from_object("django.conf:settings", namespace="CELERY")
app.autodiscover_tasks()


@app.task(bind=True)
def debug_task(self):
    print("Request: {0!r}".format(self.request))


if settings.ENVIRONMENT == "development":
    app.conf.beat_schedule = {
        "debit_subscribed_users_hourly": {
            "task": "accounts.tasks.set_next_charge_time",
            "schedule": crontab(minute="*"),
        },
        "calculate_interest_and_pay_everyday": {
            "task": "accounts.tasks.pay_daily_interests",
            # For observing on the server
            # every minute
            "schedule": crontab(minute="*"),
            # at midnight everyday
            # "schedule": crontab(minute=0, hour="0"),
            # every hour
            # "schedule": crontab(minute=0, hour="*")
        },
        "process_starting_date_for_rotation_groups": {
            "task": "ajo.tasks.process_starting_date",
            "schedule": crontab(minute="*"),
        },
        "mark_pending_card_deposits": {
            "task": "payment.tasks.mark_old_card_deposit_pending_transactions_as_failed",
            "schedule": crontab(minute="*"),
        },
        "calculate_loan_eligibility": {
            "task": "ajo.tasks.calculate_the_loan_eligibility_of_all_ajo_users",
            "schedule": timedelta(minutes=10),
        },
        # "send_user_info_to_all_ajo_users": {
        #     "task": "ajo.tasks.send_user_info_to_all_ajo_users",
        #     "schedule": timedelta(minutes=1),
        # },
        # "process_mature_ajo_plans": {
        #     "task": "ajo.tasks.handle_mature_ajo_plans",
        #     "schedule": timedelta(minutes=2),
        # },
        "ajo_users_with_digital_funds": {
            "task": "ajo.tasks.ajo_users_with_digital_wallet_balance",
            "schedule": timedelta(minutes=1),  # Run minute
        },
        "closing_personal_rotation_groups_update": {
            "task": "ajo.tasks.close_ending_personal_rotation_groups",
            "schedule": timedelta(minutes=2),  # Run every two minutes
        },
        "ending_rotation_groups_update": {
            "task": "ajo.tasks.update_ending_rosca_groups",
            "schedule": timedelta(minutes=2),  # Run every two minutes
        },
        "close_mature_ajo_plans_at_month_end": {
            "task": "ajo.tasks.close_daily_plans",
            "schedule": timedelta(minutes=2),
        },
        "close_mature_weekly_monthly_ajo_plans": {
            "task": "ajo.tasks.close_weekly_monthly_plans",
            "schedule": timedelta(minutes=2),
        },
        "settle_due_balance": {
            "task": "ajo.tasks.settle_due_balance_from_agent_wallet",
            "schedule": timedelta(minutes=2),
        },
        "send_closing_plan_sms": {
            "task": "ajo.tasks.closing_ajo_plans_for_sms",
            "schedule": timedelta(minutes=2),
        },
        "update_external_transfers": {
            "task": "ajo.tasks.update_successful_transfers_and_reversals",
            "schedule": timedelta(minutes=3),  # run every 3 minutes
            "options": {
                "queue": "accountman",
            },
        },
        "resolve_pending_bank_deposits": {
            "task": "ajo.tasks.settle_bank_deposit_transactions",
            "schedule": timedelta(minutes=3),
        },
        # "repay_loan_from_savings": {
        #     "task": "loan.tasks.repay_loan_from_savings",
        #     "schedule": crontab(
        #         minute=0,
        #         hour=0
        #     )
        # },
        "health_insurance_renewal": {
            "task": "loans.tasks.renew_health_insurance_plan",
            "schedule": crontab(minute="*/2"),
        },
        "posting_loans_to_loandisk": {
            "task": "loans.tasks.post_loans_to_loandisk_from_mirror_db",
            "schedule": crontab(minute="*/2"),
        },
        "updating_loans_to_loandisk": {
            "task": "loans.tasks.update_loans_to_loandisk_from_mirror_db",
            "schedule": crontab(minute="*/2"),
        },
        "sending_repayments_to_loandisk": {
            "task": "loans.tasks.run_repayment_posting_to_loan_disk",
            "schedule": crontab(minute="*/2"),
        },
    }


else:
    app.conf.beat_schedule = {
        "debit_subscribed_users_hourly": {
            "task": "accounts.tasks.set_next_charge_time",
            "schedule": crontab(minute=0, hour="*"),
        },
        "calculate_interest_and_pay_everyday": {
            "task": "accounts.tasks.pay_daily_interests",
            # Execute every three hours: midnight, 3am, 6am, 9am, noon, 3pm, 6pm, 9pm.
            "schedule": crontab(minute=0, hour="*/3"),
        },
        "process_starting_date_for_rotation_groups": {
            "task": "ajo.tasks.process_starting_date",
            "schedule": crontab(hour=0, minute=0),
        },
        "mark_pending_card_deposits": {
            "task": "payment.tasks.mark_old_card_deposit_pending_transactions_as_failed",
            "schedule": crontab(minute=0, hour="*/2"),  # Run every 2 hours
        },
        "calculate_loan_eligibility": {
            "task": "ajo.tasks.calculate_the_loan_eligibility_of_all_ajo_users",
            "schedule": timedelta(days=3),
        },
        # "send_user_info_to_all_ajo_users": {
        #     "task": "ajo.tasks.send_user_info_to_all_ajo_users",
        #     "schedule": crontab(minute=0, hour="*/2,*/3"),
        # },
        # "process_mature_ajo_plans": {
        #     "task": "ajo.tasks.handle_mature_ajo_plans",
        #     "schedule": timedelta(days=1),  # Run every day
        # },
        "ajo_users_with_digital_funds": {
            "task": "ajo.tasks.ajo_users_with_digital_wallet_balance",
            "schedule": timedelta(days=1),  # Run every day
        },
        "closing_personal_rotation_groups_update": {
            "task": "ajo.tasks.close_ending_personal_rotation_groups",
            "schedule": crontab(minute=0, hour="*/6"),  # Run every 6 hours
        },
        "ending_rotation_groups_update": {
            "task": "ajo.tasks.update_ending_rosca_groups",
            "schedule": crontab(minute=0, hour="*/6"),  # Run every 6 hours
        },
        "close_mature_ajo_plans_at_month_end": {
            "task": "ajo.tasks.close_daily_plans",
            "schedule": timedelta(days=1),
        },
        "close_mature_weekly_monthly_ajo_plans": {
            "task": "ajo.tasks.close_weekly_monthly_plans",
            "schedule": timedelta(days=1),
        },
        "settle_due_balance": {
            "task": "ajo.tasks.settle_due_balance_from_agent_wallet",
            "schedule": crontab(minute=0, hour="*/5"),  # Run every 5 hours
        },
        "send_closing_plan_sms": {
            "task": "ajo.tasks.closing_ajo_plans_for_sms",
            "schedule": crontab(
                minute=0,
                hour=0,
                day_of_month="25-31",
            ),
        },
        "update_external_transfers": {
            "task": "ajo.tasks.update_successful_transfers_and_reversals",
            "schedule": timedelta(minutes=5),
        },
        "resolve_pending_bank_deposits": {
            "task": "ajo.tasks.settle_bank_deposit_transactions",
            "schedule": timedelta(minutes=30),
        },
        # "repay_loan_from_savings": {
        #     "task": "loan.tasks.repay_loan_from_savings",
        #     "schedule": crontab(
        #         minute=0,
        #         hour=0
        #     )
        # },
        "health_insurance_renewal": {
            "task": "loans.tasks.renew_health_insurance_plan",
            # "schedule": crontab(minute="*/2"),
        },
        "posting_loans_to_loandisk": {
            "task": "loans.tasks.post_loans_to_loandisk_from_mirror_db",
            # "schedule": crontab(minute="*/2"),
        },
        "updating_loans_to_loandisk": {
            "task": "loans.tasks.update_loans_to_loandisk_from_mirror_db",
            # "schedule": crontab(minute="*/2"),
        },
        # "sending_repayments_to_loandisk": {
        #     "task": "loans.tasks.run_repayment_posting_to_loan_disk",
        #     # "schedule": crontab(minute="*/2"),
    # }
    }
