from django.core.cache import cache
from django.http import HttpResponse
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from rest_framework.response import Response


def user_cache_key(view_name, request, *args, **kwargs):
    if request.user.is_authenticated:
        return f"{view_name}_{request.user.id}"
    return view_name


def cache_page_per_user(timeout: int):
    def decorator(view_func):
        def _wrapped_view(request, *args, **kwargs):
            cache_key = user_cache_key(view_func.__name__, request, *args, **kwargs)
            return cache_page(timeout=timeout, key_prefix=cache_key)(view_func)(request, *args, **kwargs)

        return _wrapped_view

    return decorator
