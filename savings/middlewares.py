# import requests
from django.conf import settings
from rest_framework import status
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.response import Response

from accounts.agency_banking import AgencyBankingClass
from accounts.models import CustomUser
from accounts.services import UserService
from accounts.utils import decode_access_token
from ajo.models import AjoUser
from django.http import JsonResponse

# from payment.model_choices import WalletTypes
from payment.models import WalletSystem
from pprint import pprint


from django.utils.functional import SimpleLazyObject


def get_saver(user, phone_number=None):
    """
    Retrieves the Saver associated with the authenticated user.
    """
    if user.is_authenticated and phone_number:

        return AjoUser.objects.filter(user=user, phone_number=phone_number).last()

    return None


def format_response(response: Response) -> Response:
    response.accepted_renderer = JSONRenderer()
    response.accepted_media_type = "application/json"
    response.renderer_context = {}
    response.render()
    return response


class SaverEntryPointMiddleware:
    """
    Middleware to attach the Saver object to the request, if the user is authenticated.

    This middleware ensures that:
    1. Requests to specific paths (e.g., '/api/v1/web/auth/login/' and '/api/v1/web/onboarding/')
       bypass additional validations.
    2. For all other paths under '/api/v1/web/', the request must include:
       - A valid 'Bearer' token in the Authorization header.
       - A valid phone number in the 'Phone-Number' header.
    3. If the above conditions are met, the Saver object associated with the user is lazily loaded
       and attached to the request for downstream usage.

    Note:
    - This middleware expects the `get_saver` function and `CustomRequest` class to be implemented
      as utilities for fetching and caching the Saver object.
    """

    def __init__(self, get_response):
        self.get_response = get_response
        self.allowed_paths = [
            "auth/",
            "onboarding/",
        ]  # Paths that bypass additional checks
        self.prefix = "/api/v1/web/"  # Prefix for API paths handled by this middleware

    def __call__(self, request):
        """
        Process the incoming request:
        1. Check if the request path starts with the specified prefix.
        2. If the path is not in the list of allowed paths:
           - Validate the presence of a 'Bearer' token in the Authorization header.
           - Validate the presence of a phone number in the 'Phone-Number' header.
        3. Attach a lazily-loaded Saver object to the request if validation passes.
        """
        path = request.path

        # Retrieve headers
        authorization_header = request.headers.get("Authorization", "")
        saver_phone = request.headers.get("Phone-Number", "")

        # Step 1: Check if the path starts with the specified prefix
        if path.startswith(self.prefix):
            # Extract the segment after the prefix
            remaining_path = path[len(self.prefix) :]

            # Step 2: Check if the path is allowed
            if not any(
                remaining_path.startswith(allowed) for allowed in self.allowed_paths
            ):
                # Validate Bearer token
                if not authorization_header.startswith("Bearer "):
                    return JsonResponse(
                        {
                            "error": "Bad Request: Bearer token required in Authorization header."
                        },
                        status=400,
                    )

                # Validate Phone-Number header
                if not saver_phone:
                    return JsonResponse(
                        {
                            "error": "Bad Request: Phone number required in Authorization header."
                        },
                        status=400,
                    )

            if (
                authorization_header.startswith("Bearer ")
                and saver_phone
                and path.startswith(self.prefix)
            ):
                _saver_object = SimpleLazyObject(
                    lambda: AjoUser.objects.filter(phone_number=saver_phone).last()
                )
                if not _saver_object:
                    return JsonResponse(
                        {"error": "Bad Request: Saver not found. Confirm Phone Number"},
                        status=400,
                    )
                else:

                    request.saver = SimpleLazyObject(
                        lambda: get_saver(request.user, saver_phone)
                    )

                    # Pass the request to the next middleware or view
        response = self.get_response(request)

        if response.status_code == 401:

            return JsonResponse(
                {"error": "Unauthorized: Invalid Token or Token Expired"},
                status=401,
            )

        return response


class EntryPointMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        # One-time configuration and initialization.
        self.web_url = "/api/v1/web/"

    def __call__(self, request):

        # Code to be executed for each request before
        # the view (and later middleware) are called.

        response = self.get_response(request)

        # Code to be executed for each request/response after
        # the view is called.

        ### THE SUPPOSED FLOW OF THINGS ###

        # Checking for 401 in authorization and checking the error message for if the user is not found in the system
        # If the user is not found, it will get the email from the header and the user id from the payload of the token
        # it will use these details to check the db for the pair if it exists
        # if the pair doesn't exist, it will get the password of that user and save the details in the savings database
        # if the email exists but doesn't correspond with the id, it will take it as a new user and request for the password of that user, if the password is not the same, it saves it as a new user
        # If the password is the same, the id is in the db but the email is different, it will take it that the user has changed the email and update the db
        # Ask for revision on the above code

        # if response.status_code == 401 and (
        #     "User not found" in response.data["detail"] or "user_not_found" in response.data["code"]
        # ):

        if not request.path.startswith(self.web_url):
            if response.status_code == 401:

                email_in_headers = request.headers.get("Email-Address", None)

                auth_header = request.headers.get("Authorization", "")

                token_type, _, access_token = auth_header.partition(" ")

                # print("jjn", email_in_headers)
                # Check if the email is not None and is in the headers
                if email_in_headers is None:
                    r = Response(
                        {
                            "error": "565",
                            "message": "no email in header and this user does not exist in the database",
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                    return format_response(r)

                # print(access_token)
                # print(settings.SECRET_KEY)
                payload = decode_access_token(
                    access_token=access_token, signature=settings.SECRET_KEY
                )

                if payload.get("user_id"):

                    jwt_user_id = payload["user_id"]

                    # check if the user_id is in the database
                    user_id_qs = CustomUser.objects.filter(
                        customer_user_id=jwt_user_id
                    ).last()
                    if user_id_qs:

                        # if user_id_qs.user_type == "lite":
                        #     return Response(
                        #         data={"message": "User is a lite user."},
                        #         status=200
                        #     )

                        # Get the current user details here
                        user_details = AgencyBankingClass.get_current_user_details(
                            user_id=jwt_user_id
                        )

                        # if status is success, return the data
                        if user_details["status"] == "success":
                            # user_details["data"] has id, email, password

                            # Update the email and password, then save
                            user_id_qs.password = user_details["data"]["password"]
                            user_id_qs.email = user_details["data"]["email"]
                            user_id_qs.save()
                            r = Response(
                                {
                                    "error": "565",
                                    "message": "session expired 1, please try again",
                                },
                                status=status.HTTP_400_BAD_REQUEST,
                            )
                            return format_response(r)

                        # if status is error, return the data in a customized way that the user_id is not there oh
                        else:
                            r = Response(
                                {
                                    "error": "565",
                                    "message": user_details.get("message"),
                                },
                                status=status.HTTP_400_BAD_REQUEST,
                            )
                            return format_response(r)

                    # user_id not in the database, create a new user
                    else:
                        # Get the current user details here
                        user_details = AgencyBankingClass.get_current_user_details(
                            user_id=jwt_user_id
                        )
                        # if status is success, return the data
                        if user_details.get("status", "") == "success":
                            savings_user = CustomUser.objects.filter(
                                email=user_details["data"]["email"]
                            ).last()

                            if savings_user:
                                # Edit user in the database
                                savings_user.email = user_details["data"]["email"]
                                savings_user.customer_user_id = user_details["data"][
                                    "id"
                                ]
                                savings_user.user_password = user_details["data"][
                                    "password"
                                ]
                                savings_user.save()
                                WalletSystem.create_missing_wallets_for_user(
                                    savings_user
                                )

                            else:
                                # Create a new user in the database
                                savings_user = CustomUser.objects.create(
                                    customer_user_id=user_details["data"]["id"],
                                    email=user_details["data"]["email"],
                                    user_password=user_details["data"]["password"],
                                )

                                # create Chestlock, Halal and Quicksavings wallets for users
                                WalletSystem.create_wallets_for_user(savings_user)

                            try:
                                UserService.update_user_fields_from_agency(
                                    user=savings_user,
                                    access_token=access_token,
                                )
                            except ValueError as err:
                                pass

                            r = Response(
                                {
                                    "error": "565",
                                    "message": "session expired 2, please try again",
                                },
                                status=status.HTTP_400_BAD_REQUEST,
                            )
                            return format_response(r)

                        # if status is error or false, return the data in a customized way that the user_id is not there
                        else:
                            r = Response(
                                {
                                    "error": "565",
                                    "message": user_details.get("message"),
                                },
                                status=status.HTTP_400_BAD_REQUEST,
                            )
                            return format_response(r)

                    # # The jwt user_id does not exist in the database
                    # else:
                    #     user_email_qs = CustomUser.objects.filter(email=email_in_headers)
                    #     # if the email is in the database
                    #     if user_email_qs:
                    #         # What should be done if the email is in the database
                    #         pass

                    #     # The user_id and email do not exist in the database at all
                    #     else:

                    #         # create a new user instance for this new user

                    #         password = get_current_user_details(user_id=jwt_user_id, access_token=access_token)

                    #         new_savings_user = CustomUser.objects.create(
                    #             customer_user_id=jwt_user_id,
                    #             email=email_in_headers,
                    #             user_password=password[""],
                    #         )

                else:
                    r = Response(
                        {
                            "error": "565",
                            "message": "session expired 3, please try again",
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                    return format_response(r)

        return response
