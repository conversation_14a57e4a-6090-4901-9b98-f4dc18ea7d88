from django.conf import settings
from django.core.exceptions import ImproperlyConfigured
from django.core.files.storage import FileSystemStorage
from storages.backends.s3boto3 import S3Boto3Storage

from savings.enums import Environment

environment = settings.ENVIRONMENT
class DigitalOceanSpaceStorage(S3Boto3Storage):
    """
    Custom storage class that uses file system when Environment is development,
    and DigitalOcean Spaces when Environment is production.
    """

    def __init__(self, *args, **kwargs):
        if not settings.AWS_S3_ACCESS_KEY_ID or not settings.AWS_S3_SECRET_ACCESS_KEY:
            raise ImproperlyConfigured("DigitalOcean Spaces credentials are missing")
        if not settings.AWS_STORAGE_BUCKET_NAME:
            raise ImproperlyConfigured("DigitalOcean Spaces bucket name is missing")

        super().__init__(*args, **kwargs)

    def get_aws_access_key_id(self):
        return settings.AWS_ACCESS_KEY_ID

    def get_aws_secret_access_key(self):
        return settings.AWS_SECRET_ACCESS_KEY

    def get_bucket(self):
        return settings.AWS_STORAGE_BUCKET_NAME
        


class ConditionalStorage:
    def __new__(cls, *args, **kwargs):
        if environment == Environment.PRODUCTION.value:
            return DigitalOceanSpaceStorage(*args, **kwargs)
        return FileSystemStorage(*args, **kwargs)
