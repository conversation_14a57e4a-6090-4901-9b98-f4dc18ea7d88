import requests
import json

from django.conf import settings
from django.contrib.auth import get_user_model

from accounts.agency_banking import AgencyBankingClass, agent_login
from payment.model_choices import Status, TransactionFormType, TransactionTypeCreditOrDebitChoices
from payment.services import TransactionService


User = get_user_model()


def call_create_card(payload: dict) -> dict:
    
    url = f"{settings.LIBERTY_CARD_BASE_URL}cards/request/physical/"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f"Bearer {settings.LIBERTY_CARD_API_KEY}"
    }

    payload = json.dumps(payload)

    try:
        resp = requests.post(url, data=payload, headers=headers)
        response = resp.json()
        
        if resp.status_code == 200:  
            # card_request_id = response.get("responseDetails")["cardRequest"]["requestCardId"]
            card_request_id = response.get("responseDetails", {}).get("cardRequest", {}).get("requestCardId", {})

            data = {
                "status": True,
                "response": {"card_request_id": card_request_id}
            }
            
        else:
                
            data = {
                "status": False,
                "message": response.get("responseMessage")
            }

        return data
    
    except (
            requests.exceptions.RequestException,
            Exception,
            json.decoder.JSONDecodeError,
        ) as err:
        data = {
                "status": False,
                "message": f"{err}"
            }
        
        return data


def call_assign_card(payload: dict) -> dict:
    
    url = f"{settings.LIBERTY_CARD_BASE_URL}cards/physical/assign/"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f"Bearer {settings.LIBERTY_CARD_API_KEY}"
    }

    payload = json.dumps(payload)

    try:
        resp = requests.post(url, data=payload, headers=headers)
        response = resp.json()
        if resp.status_code == 200:
            card_id = response.get("responseDetails")["cardId"]

            data = {
                "status": True,
                "response": {"card_id": card_id}
            }
            return data
        else:
             data = {
                "status": False,
                "message": f'{response.get("responseMessage")} : {response.get("developerErrorMessage")}'
            }
             return data
            
    except (
            requests.exceptions.RequestException,
            Exception,
            json.decoder.JSONDecodeError,
        ) as err:

        data = {
                "status": False,
                "message": f"{err}"
            }
        
        return data


def send_card_debit_amount_to_card_service(plan, amount, ajo_user):
    description = "Transfer of card debit amount from Savings to Liberty Cards"
    savings_access_token = agent_login(token_not_valid=True).get("access")

    # Savings Account Login
    transaction_pin = settings.AGENCY_BANKING_TRANSACTION_PIN
    savings_user_email = settings.AGENCY_BANKING_USEREMAIL
    savings_user = User.objects.filter(email=savings_user_email).last()

    # Get LibertyCards User
    liberty_cards_user_email = settings.LIBERTY_CARDS_EMAIL
    liberty_cards_user = User.objects.filter(email=liberty_cards_user_email).last()
    liberty_cards_phone = liberty_cards_user.user_phone


    transfer_transaction = (
        TransactionService.create_internal_transfer_between_accounts(
            user=savings_user,
            amount=amount,
            transaction_description=description,
            transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
            transaction_form_type=TransactionFormType.TRANSFER,
            status=Status.PENDING,
            ajo_user=ajo_user
        )
    )
    reference = str(transfer_transaction.transaction_id)

    request_data = {
        "from_wallet_type": "COLLECTION",
        "to_wallet_type": "COLLECTION",
        "data": [
            {
                "buddy_phone_number": liberty_cards_phone,
                "amount": amount,
                "narration": description,
                "is_beneficiary": "False",
                "save_beneficiary": "False",
                "remove_beneficiary": "False",
                "is_recurring": "False",
                "custom_reference": reference,
            }
        ],
        "transaction_pin": "",
    }

    send_money_to_liberty_cards = AgencyBankingClass.send_money_from_an_account_in_liberty_to_user_through_pay_buddy(
        transaction_pin=transaction_pin,
        phone_number=liberty_cards_phone,
        amount=amount,
        transaction_reference=reference,
        access_token=savings_access_token,
        narration=description,
    )

    if send_money_to_liberty_cards.get("data", {}).get("message") == "success":
        transfer_transaction.status = Status.SUCCESS
 
        transfer_transaction.request_data = request_data
        transfer_transaction.payload = send_money_to_liberty_cards
        transfer_transaction.save()
        return send_money_to_liberty_cards
    




