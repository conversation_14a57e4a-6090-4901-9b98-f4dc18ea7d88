from django.urls import path

from .views import (
    CardBalanceRetrieval,
    CardWithdrawal,
    RetrieveCardDetails,
    UpdateCardStatus,
    RequestPhysicalCards,
    AssignPhysicalCards,
)

urlpatterns = [
    path("balance/<str:card_id>", CardBalanceRetrieval.as_view(), name="retrieve_card_balance" ),
    path("withdraw/", CardWithdrawal.as_view(), name="card_withdrawal"),
    path("v1/details/<str:card_id>", RetrieveCardDetails.as_view(), name="get_card_details"),
    path("v1/update/status/<str:card_id>/<str:new_status>", UpdateCardStatus.as_view(), name="update_card_status"),
    path("physical/request/", RequestPhysicalCards.as_view(), name="request_physical_card"),
    path("assign/physical/", AssignPhysicalCards.as_view(), name="assign_cards")
]
