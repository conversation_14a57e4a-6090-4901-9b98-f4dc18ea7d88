from rest_framework import serializers

from accounts.models import ConstantTable
from ajo.models import AjoUser
from ajo.payment_actions import check_if_agent_can_pay
from ajo.selectors import AjoAgentSelector
from payment.checks import verify_transaction_pin

from .models import Cards, CustomerCardDetail, RequestCardData
from .enums import CardTypes


class RequestPhysicalCardsSerializer(serializers.Serializer):
    ajo_user_phone = serializers.CharField(max_length=16)
    transaction_pin = serializers.CharField()
    user_email = serializers.CharField()
    user_ip_address = serializers.CharField()
    card_brand = serializers.CharField()
    bvn_name = serializers.CharField()
    bvn_number = serializers.CharField()
    bvn_phone = serializers.CharField()
    bvn_first_name = serializers.CharField()
    bvn_last_name = serializers.CharField()
    bvn_middle_name = serializers.Char<PERSON>ield()
    bvn_birthdate = serializers.Char<PERSON>ield()
    # wallet_id = serializers.Char<PERSON>ield()

    def validate(self, attrs):
        request = self.context.get("request")
        user = request.user
        ajo_user_phone = attrs.get("ajo_user_phone")
        constant = ConstantTable.get_constant_table_instance()
        card_creation_fee = constant.card_creation_fee
        ajo_user = AjoUser.objects.filter(phone_number=ajo_user_phone, user=user).first()
        
        if not ajo_user:
            raise serializers.ValidationError("Ajo User not found")
    
        card_requests = RequestCardData.objects.filter(user=ajo_user)
        for card_request in card_requests:
            if card_request.assigned is False and card_request.is_request_cancelled is False:
                raise serializers.ValidationError("User still have an active request for card, cancel to make another request.")
        
        card = CustomerCardDetail.objects.filter(user=ajo_user, is_active=True)
        if card:
            raise serializers.ValidationError("Already have an active card")
        
        
        # if not ajo_user.first_name or not ajo_user.last_name:
        #     raise serializers.ValidationError("Kindly confirm and update user full name")
        
        agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()
        if not check_if_agent_can_pay(
            agent_wallet=agent_wallet, amount=card_creation_fee
        ):
            raise serializers.ValidationError("Insufficient wallet balance ")
        
        # access_token = request.headers.get("Authorization", "").partition(" ")[-1]
        # agent_transaction_pin = attrs.get("transaction_pin")

        # verify_agent_pin = verify_transaction_pin(
        #     transaction_pin=agent_transaction_pin,
        #     access_token=access_token,
        #     customer_user_id=user.customer_user_id,
        # )
        
        # if verify_agent_pin is True:
        #     pass
        # else:
        #     raise serializers.ValidationError("Incorrect transaction pin")
        attrs["ajo_user"] = ajo_user
        attrs["agent_wallet"] = agent_wallet
        attrs["card_creation_fee"] = card_creation_fee

        return attrs


    

class AssignPhysicalCardsSerializer(serializers.Serializer):
    user_ip_address = serializers.CharField()
    card_pan = serializers.CharField()
    card_expiry_month = serializers.CharField()
    card_expiry_year = serializers.CharField()
    card_request_id = serializers.CharField()
    spend_limit = serializers.IntegerField(required=False)



class CardWithdrawalSerializer(serializers.Serializer):
    
    card_id = serializers.CharField()
    amount = serializers.FloatField()

    class Meta:
        fields = [
            "card_id",
            "amount"
        ]
