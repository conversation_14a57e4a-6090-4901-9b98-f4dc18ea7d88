from django.utils import timezone
from typing import Optional

from django.db import models, IntegrityError
from django.core.exceptions import ValidationError
from django.conf import settings

from accounts.helpers import RoundedFloatField
from django.core.validators import MinValueValidator
from ajo.models import AjoUser
from payment.models import WalletSystem
from payment.model_choices import Status

from .enums import *

# Create your models here.


class Cards(models.Model):

    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="debit_card",
        null=True,
    )

    card_id = models.CharField(max_length=250, editable=False, unique=True, db_index=True)
    card_daily_limit = models.DecimalField(max_digits=12, default=0.00, decimal_places=2)
    card_creation_message = models.TextField(null=True, blank=True)
    card_type = models.CharField(max_length=50, choices=CardTypes.choices)
    # unique_card_user_ref = models.CharField(max_length=200, )
    status = models.CharField(max_length=50, choices=CardStatus.choices, default="ACTIVE")
    card_pin = models.CharField(max_length=4, null=True, blank=True)

    wallet = models.ForeignKey(
        WalletSystem, 
        on_delete=models.SET_NULL, 
        null=True
    ) 

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        try:
            super().save(*args, **kwargs)
        except IntegrityError:
            raise ValidationError("Card ID already exist. Card ID must be unique.")
        
    
class RequestCardData(models.Model):
    user = models.ForeignKey(
        AjoUser,
        on_delete=models.CASCADE,
        related_name="request_card",
        null=True,
    )
    wallet_tied = models.ForeignKey(WalletSystem, on_delete=models.CASCADE, null=True, blank=True)
    assigned = models.BooleanField(default=False)
    card_type = models.CharField(max_length=200, choices=CardTypes.choices)
    card_brand = models.CharField(max_length=200)
    card_request_id = models.CharField(max_length=200, null=True, blank=True, unique=True) 
    # delivery_address_id = models.CharField(max_length=200)
    unique_reference = models.CharField(max_length=1200, null=True, blank=True)
    card_given_out = models.BooleanField(default=False)
    is_request_cancelled = models.BooleanField(default=False)
    # date_requested = models.DateTimeField()
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class CustomerCardDetail(models.Model):

    user = models.ForeignKey(
        AjoUser,
        on_delete=models.CASCADE,
        related_name="customer_card",
        null=True,
    )
    wallet = models.ForeignKey(
        WalletSystem, 
        on_delete=models.SET_NULL, 
        null=True
    ) 
    request_card = models.ForeignKey(RequestCardData, on_delete=models.CASCADE)
    card_id = models.CharField(max_length=400, blank=True, null=True, unique=True) # Generate Card
    card_type = models.CharField(max_length=400, blank=True, null=True, choices=CardTypes.choices)
    daily_limit = models.FloatField(default=30000)
    card_brand = models.CharField(max_length=400, blank=True, null=True)
    card_masked_pan = models.CharField(max_length=400, blank=True, null=True)
    card_expiry_month = models.CharField(max_length=400, blank=True, null=True)
    card_expiry_year = models.CharField(max_length=400, blank=True, null=True)
    card_status = models.CharField(max_length=400, blank=True, null=True)
    delivery_address_id = models.TextField(blank=True, null=True)
    is_activated = models.BooleanField(default=True)
    is_frozen = models.BooleanField(default=False)
    is_canceled = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    pin_changed = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)
    ip_address = models.CharField(max_length=400, blank=True, null=True)



class SavingsToCardsReconciliationTable(models.Model):

    TRANS_TYPE = [
        ("create_card", "create_card"),
        ("withdrawal", "withdrawal"),
    ]

    date_created = models.DateTimeField(default=timezone.localtime)
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name="reconciliation",
        null=True,
        blank=True
    )
    ajo_user = models.ForeignKey(
        AjoUser,
        on_delete=models.PROTECT,
        related_name="reconciliation",
        null=True,
    )
    amount = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="amount should be >= 0"),
        ],
    )

    charge = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="charge should be >= 0"),
        ],
    )
    trnsaction_type = models.CharField(max_length=100, choices=TRANS_TYPE, null=True, blank=True)
    status = models.CharField(max_length=50, default=Status.PENDING)
    narration = models.CharField(max_length=100, null=True, blank=True)
    unique_reference = models.CharField(max_length=100, null=True, blank=True)





    
