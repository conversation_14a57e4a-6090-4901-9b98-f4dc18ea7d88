from django.db.models import TextChoices


class CardStatus(TextChoices):
    ACTIVE = "ACTIVE", "ACTIVE"
    FROOZEN = "FROOZEN", "FROOZEN"
    CANCELLED = "CANCELLED", "CANCELLED"
    EXPIRED = "EXPIRED", "EXPIRED"



class CardTypes(TextChoices):
    PHYSICAL = "PHYSICAL", "PHYSICAL"
    VIRTUAL = "VIRTUAL", "VIRTUAL"

class CardBrands(TextChoices):
    VERVE = "Verve", "Verve"
    VISA = "Visa", "Visa"
    MASTER = "Master", "Master"


class ResponseCode:
    SUCCESS_CODE = "00"
    FAILED_CODE = "01"
    UNUSUCCESSFUL_CODE = "05"



 