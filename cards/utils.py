import random
import string

from datetime import datetime
from typing import Op<PERSON>, Dict, Union
from payment.models import (
    WalletSystem,
    Transaction,
    DebitCreditRecordOnAccount,
)
from payment.model_choices import (
    TransactionTypeCreditOrDebitChoices,
    TransactionDestination,
    TransactionSource,
    Status,
    WalletTypes
)
from accounts.models import ConstantTable
from .models import Cards, CustomerCardDetail

from django.db import transaction as django_transaction
from django.core.exceptions import ValidationError

from .enums import *


class CardManager:

    @staticmethod
    def sufficient_balance(amount, card: CustomerCardDetail, charge: ConstantTable) -> bool:
        balance = card.wallet.available_balance
        if not balance > amount + charge: # Ask for charges and add to amount
            return False
        return True
    
    @staticmethod
    def active_choices(card: Cards) -> str:
        # check card stat, and return stats
        
        pass

    @staticmethod
    def can_assign_cards( 
            user,
            wallet: WalletSystem, 
            cons_instance: ConstantTable,
        ): 
        
        # ENSURE THERE IS NOT ALREADY A CARD ATTACHED TO THE WALLET AND TO THE USER.
        # FIRST CHECK THAT THE USER ID DOES NOT ALREADY HAVE AN ACTIVE CARD.
        # IF SO ASK THEM TO RETRY, INSTEAD OF RECREATING.
        card = Cards.objects.filter(wallet=wallet, status="ACTIVE")
        if card:
            data = {
                "status": False,
                "message": "Already have an active card, cannot create another"
            }
            return data

        if not wallet.available_balance >= cons_instance.card_creation_fee:
            data = {
                "status": False,
                "message": "Insufficient Balance for card creation"
            }
            return data
        
        if not wallet.is_active:
            data = {
                "status": False,
                "message": "Wallet is not active"
            }
            return data
        
        # CREATE A CARD META DATA TABLE, OR CREATION FAILED TABLE, TO STORES REASONS 
        # IT FAILED TO CREATE AND THE USER.
        data = {
            "status": True,
            "message": "Can assign card"
        }
        return data

    @staticmethod
    def create_card_debit_transaction(
        # I NEED TO CREATE TWO TRANSACTIONS IN HERE ...
        # ONE FOR DEBIT AND THE OTHER FOR CREDIT..
        user, 
        amount,
        wallet_type: WalletTypes,
        transaction_destination: TransactionDestination,
        status: Status = Status.PENDING,
        description: str | None = None,
        unique_reference: Optional[str] = None,

    ) -> Transaction:
        
        transaction = Transaction.objects.create(
            user=user,
            amount=amount,
            wallet_type=wallet_type,
            transaction_destination=transaction_destination,
            status=status,
            transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
            description=description,
            unique_reference=unique_reference
        )

        return transaction
    
    @staticmethod
    def create_card_credit_transaction(
        # I NEED TO CREATE TWO TRANSACTIONS IN HERE ...
        # ONE FOR DEBIT AND THE OTHER FOR CREDIT..
        user, 
        amount,
        wallet_type: WalletTypes,
        transaction_source: TransactionDestination,
        status: Status = Status.PENDING,
        description: str | None = None,
        unique_reference: Optional[str] = None,

    ) -> Transaction:
        
        transaction = Transaction.objects.create(
            user=user,
            amount=amount,
            wallet_type=wallet_type,
            transaction_destination=transaction_source,
            status=status,
            transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
            description=description,
            unique_reference=unique_reference
        )

        return transaction
    
    @staticmethod
    @django_transaction.atomic
    def perform_card_wallet_debit(
        debit_wallet: WalletSystem,
        credit_wallet: WalletSystem,
        amount: float,
        transaction_instance: Transaction,
        unique_reference: str | None = None,

    ) -> Dict:
        
        debit_trans = WalletSystem.deduct_balance(
            wallet=debit_wallet,
            amount=amount,
            transaction_instance=transaction_instance,
            unique_reference=unique_reference
        )

        credit_settlement = WalletSystem.fund_balance(
            wallet=credit_wallet,
            amount=amount,
            transaction_instance=transaction_instance,
            unique_reference=unique_reference
        )

        return {
            "debit_transaction": debit_trans,
            "credit_transaction": credit_settlement
        }


    @staticmethod
    def create_cards(
            user,
            card_id: str,
            card_type: CardTypes,
            wallet: WalletSystem, 
            card_daily_limit: float,
            status: CardStatus,
            card_pin: str = None,
        ) -> Cards:
        
        card = Cards.objects.create(
            user=user,
            card_id=card_id,
            card_type=card_type,
            wallet=wallet,
            card_daily_limit=card_daily_limit,
            status=status,
            card_pin=card_pin
        )

        return card
    

def generate_unique_reference(
        model_instance: Transaction | DebitCreditRecordOnAccount, 
        attempts: int = 0
) -> str: 
    
    if attempts >= 2: 
        raise ValueError("Unable to generate Unique reference.")
       
    time = str(datetime.now())
    bad_char = ('-', ':', ' ', '.')
    time_stamp = [x for x in time if x not in bad_char]
    time_stamp = time_stamp[-15:]

    random_part = random.choices(string.ascii_uppercase, k=5)
    reference = time_stamp + random_part 
    random.shuffle(reference)  
    shuffled_reference = ''.join(reference)  
    if model_instance.__class__.objects.filter(
        unique_reference=shuffled_reference
    ).exists():
        return generate_unique_reference(model_instance, attempts=attempts + 1)
    
    return shuffled_reference
    

def get_serializer_key_error(errors_dict: dict):
    
    try:
        key = list(errors_dict)[0]
        error = errors_dict.get(key)
        return f'{error[0]}'
    except Exception:
        return ''
    

def response_package(
        response_code: str,
        response_message: str,
        response_detail=None,
        developer_error_message=None
    ):

    """ 
    Returns a structured response body.
    """
    data = {
        "responseCode": response_code,
        "responseMessage": response_message,
        "responseDetails": response_detail,
        "developerErrorMessage": developer_error_message
    }
    return data


def format_phone_number(phone_number):
    phone_number = phone_number.strip()
    if not phone_number.isdigit():
        raise ValueError("Phone Number not digits")
    
    if phone_number.startswith('0') and len(phone_number) == 11:
        return '234' + phone_number[1:]

    elif phone_number.startswith('+234') and len(phone_number) == 14:
        return phone_number[1:]
    
    else:
        raise ValueError("Invalid phone number format. It should start with '0' or '+234' with length of 11 or 14 respectively.")