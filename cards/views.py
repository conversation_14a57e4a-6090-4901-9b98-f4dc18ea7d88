from datetime import timezone,  datetime
from django.shortcuts import render
from django.contrib.auth import get_user_model
from django.conf import settings
from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
# from rest_framework.exceptions import ValidationError
from rest_framework import serializers
from ajo.models import AjoUser
import uuid
from ajo.payment_actions import fund_agent_wallet
from ajo.selectors import AjoAgentSelector, AjoUserSelector
from cards.models import Cards, RequestCardData, CustomerCardDetail, SavingsToCardsReconciliationTable
from accounts.models import ConstantTable
from payment.models import WalletSystem
from payment.model_choices import PlanType, TransactionDestination, TransactionFormType
from payment.services import TransactionService
from .serializers import CardWithdrawalSerializer, RequestPhysicalCardsSerializer, AssignPhysicalCardsSerializer
from .enums import *
from .services import call_create_card, call_assign_card
from payment.model_choices import Status

from .utils import *
# Create your views here.


Users = get_user_model()

class AssignPhysicalCards(generics.GenericAPIView):

    permission_classes = [IsAuthenticated]

    def post(self, request):

        try:
            serializer = AssignPhysicalCardsSerializer(data=request.data)
            serializer.is_valid()

            unique_reference = str(uuid.uuid4()).replace("-", "")
            card_pan = serializer.validated_data["card_pan"]
            card_expiry_month = serializer.validated_data["card_expiry_month"]
            card_expiry_year = serializer.validated_data["card_expiry_year"][-2:]
            spend_limit = serializer.validated_data["spend_limit"]
            card_request_id =  serializer.validated_data.get("card_request_id")

            user_ip_address = serializer.validated_data.get("user_ip_address")

            request_card = RequestCardData.objects.get(card_request_id=card_request_id)
            if request_card.assigned is True:
                raise Exception("Requested Card alredy assigned")

            if spend_limit is None:
                const = ConstantTable.get_constant_table_instance()
                spend_limit = const.card_trans_daily_limit


            payload = {
                "card_request_id": card_request_id,
                "user_ip_address": user_ip_address,
                "card_pan": card_pan,
                "card_expiry_month": card_expiry_month,
                "card_expiry_year": card_expiry_year,
                "unique_reference": unique_reference,
                "spend_limit": spend_limit
            }

            assign_card = call_assign_card(payload=payload)
            if assign_card.get("status") is True:
                card_id = assign_card.get("response")["card_id"]
                request_card.assigned = True
                request_card.save()

                CustomerCardDetail.objects.create(
                    user=request_card.user,
                    request_card=request_card,
                    card_id=card_id,
                    card_type="PHYSICAL",
                    daily_limit=spend_limit,
                    card_brand=request_card.card_brand,
                    card_masked_pan=card_pan,
                    card_expiry_month=card_expiry_month,
                    card_expiry_year=card_expiry_year,
                    wallet=request_card.wallet_tied
                )

                data = response_package(
                        response_code=ResponseCode.SUCCESS_CODE,
                        response_message="Card Assigned Successfully",
                        response_detail={"card_id": card_id}
                    )

                return Response(data, status.HTTP_200_OK)
            else:
                data = response_package(
                    response_code=ResponseCode.SUCCESS_CODE,
                    response_message="Unable to assign card",
                    developer_error_message=assign_card.get("message")
                )

                return Response(data, status.HTTP_400_BAD_REQUEST)
            
        except serializers.ValidationError:
            data = response_package(
                response_code=ResponseCode.FAILED_CODE,
                response_message="Invalid Request Payload.",
                developer_error_message=get_serializer_key_error(serializer.errors)
            )
            return Response(data, status.HTTP_400_BAD_REQUEST)
        
        except RequestCardData.DoesNotExist:
            data = response_package(
                response_code=ResponseCode.FAILED_CODE,
                response_message="Request card does not exist",
                developer_error_message="Requested card does not existt."
            )
            return Response(data, status.HTTP_400_BAD_REQUEST)
        
        except Exception as err:
            data = response_package(
                response_code=ResponseCode.FAILED_CODE,
                response_message="Failed to assign cards. Try again",
                developer_error_message=str(err)
            )
            return Response(data, status.HTTP_400_BAD_REQUEST)
        

class RequestPhysicalCards(generics.GenericAPIView):

    # serializer_class = AssignCardsSerializer
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            user = request.user
            serializer = RequestPhysicalCardsSerializer(data=request.data, context={"request": request})
            serializer.is_valid(raise_exception=True)
            validated_data = serializer.validated_data

            user_email = serializer.validated_data.get("user_email")
            user_ip_address = serializer.validated_data.get("user_ip_address")
            card_brand = serializer.validated_data.get("card_brand")
            bvn_number = serializer.validated_data.get("bvn_number")
            bvn_name = serializer.validated_data.get("bvn_name")
            bvn_phone = serializer.validated_data.get("bvn_phone")
            bvn_first_name = serializer.validated_data.get("bvn_first_name")
            bvn_last_name = serializer.validated_data.get("bvn_last_name")
            bvn_middle_name = serializer.validated_data.get("bvn_middle_name")
            bvn_birthdate = serializer.validated_data.get("bvn_birthdate")

            formatted_phone_number = format_phone_number(bvn_phone)

            ajo_user = validated_data.get("ajo_user")
            agent_wallet = validated_data.get("agent_wallet")
            card_creation_fee = validated_data.get("card_creation_fee")

            unique_reference = str(uuid.uuid4()).replace("-", "")
            ref = f"rc{unique_reference}"
            transaction_form_type = "CARD_REQUEST"
            trans_objs = TransactionService.dynamic_deduction_from_wallet_transaction(
                user=user,
                amount=card_creation_fee,
                wallet_type=agent_wallet.wallet_type,
                request_data=None,
                quotation_id=ref,
                ajo_user=ajo_user,
                transaction_form_type=transaction_form_type,
            )
            # deduct agent wallet
            deduct_agent_wallet = WalletSystem.deduct_balance(
                wallet=agent_wallet,
                amount=card_creation_fee,
                transaction_instance=trans_objs,
                unique_reference=None,
                onboarded_user=ajo_user,
            )
            
            trans_objs.status = Status.SUCCESS
            trans_objs.transaction_date_completed = datetime.now()
            trans_objs.wallet_balance_before = deduct_agent_wallet["balance_before"]
            trans_objs.wallet_balance_after = deduct_agent_wallet["balance_after"]
            trans_objs.save()
  
            payload = {
                "user_email": user_email,
                "user_ip_address": user_ip_address,
                "card_brand": card_brand,
                "bvn_number": bvn_number,
                "bvn_name": bvn_name,
                "bvn_phone": formatted_phone_number,
                "bvn_first_name": bvn_first_name,
                "bvn_last_name": bvn_last_name,
                "bvn_middle_name": bvn_middle_name,
                "bvn_birthdate": bvn_birthdate,
                "unique_reference": unique_reference
            }

            create_card = call_create_card(payload=payload)
            if create_card.get("status") is False:
                response = create_card.get("message")

                # transaction for refund when card API returns a failed response
                # unique_reference = str(uuid.uuid4()).replace("-", "")
                ref = f"rev-rc{unique_reference}"
                transaction_form_type = TransactionFormType.REVERSAL
                refund_trans_objs = Transaction.objects.create(
                        user=user,
                        amount=card_creation_fee,
                        wallet_type=agent_wallet.wallet_type,
                        quotation_id=ref,
                        onboarded_user=ajo_user,
                        transaction_form_type=transaction_form_type,
                        transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
                )
                # refund agent wallet
                refund_agent_wallet = WalletSystem.fund_balance(
                    wallet=agent_wallet,
                    amount=card_creation_fee,
                    transaction_instance=refund_trans_objs,
                    unique_reference=None,
                    onboarded_user=ajo_user,
                )
            
                refund_trans_objs.status = Status.SUCCESS
                refund_trans_objs.transaction_date_completed = datetime.now()
                refund_trans_objs.wallet_balance_before = refund_agent_wallet["balance_before"]
                refund_trans_objs.wallet_balance_after = refund_agent_wallet["balance_after"]
                refund_trans_objs.save()

                data = response_package(
                    response_code=ResponseCode.FAILED_CODE,
                    response_message=f"Failed to request card on Card Service: {response}",
                    developer_error_message=response
                )
                return Response(data, status.HTTP_400_BAD_REQUEST) 
            
            card_request_id = create_card.get("response")["card_request_id"]
            reconciliation = SavingsToCardsReconciliationTable.objects.create(
                user=user,
                ajo_user=ajo_user,
                amount=card_creation_fee,
                narration="Card Creation Transaction",
                unique_reference=unique_reference,
                trnsaction_type="create_card"
            )
            
            reconciliation.status = Status.SUCCESS
            reconciliation.save()
            
            ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
            ajo_user_spending_wallet = ajo_user_selector.get_spending_wallet()

            RequestCardData.objects.create(
                user=ajo_user,
                card_type="PHYSICAL",
                card_brand=card_brand,
                card_request_id=card_request_id,
                unique_reference=unique_reference,
                wallet_tied=ajo_user_spending_wallet
            )

            data = response_package(
                response_code=ResponseCode.SUCCESS_CODE,
                response_message="Card Created Successfully",
                response_detail={"card_request_id": card_request_id}
            )

            return Response(data, status.HTTP_200_OK)  
            
        except serializers.ValidationError:
            data = response_package(
                response_code=ResponseCode.FAILED_CODE,
                response_message="Invalid Request Payload.",
                developer_error_message=get_serializer_key_error(serializer.errors)
            )
            return Response(data, status.HTTP_400_BAD_REQUEST)
        
        except Exception as err:
            data = response_package(
                response_code=ResponseCode.FAILED_CODE,
                response_message="Failed to create cards. Try again",
                developer_error_message=str(err)
            )
            return Response(data, status.HTTP_400_BAD_REQUEST)




class CardBalanceRetrieval(generics.GenericAPIView):

    def get(self, request, card_id):
        
        try:
            card = CustomerCardDetail.objects.get(card_id=card_id)
          
            wallet = card.wallet
            balance = wallet.available_balance
            hold_balance = wallet.hold_balance
        

            response_detail = {
                "availableBalance": balance,
                "holdBalance": hold_balance,
            }
            

            data = response_package(
                response_code=ResponseCode.SUCCESS_CODE,
                response_message="Successfully retrieved balance",
                response_detail={"data": balance}
            )
            print(f"BALANCE DATA : {data}")
            return Response(data, status.HTTP_200_OK)
        
        except CustomerCardDetail.DoesNotExist:
            data = response_package(
                    response_code=ResponseCode.FAILED_CODE,
                    response_message="Card does not exist.",
                    developer_error_message="Card does not exist."
            )
            return Response(data, status.HTTP_400_BAD_REQUEST)
        
        except Exception as err:
            data = response_package(
                    response_code=ResponseCode.FAILED_CODE,
                    response_message="Failed to retrieve balance.",
                    developer_error_message=str(err)
            )
            return Response(data, status.HTTP_400_BAD_REQUEST)
        

class RetrieveCardDetails(generics.GenericAPIView):

    def get(self, request, card_id):
        
        try:
            card = Cards.objects.get(card_id=card_id)
            
            username = card.user.username
            user_email = card.user.email
            wallet_number = card.wallet.wallet_number
            wallet_type = card.wallet.wallet_type
            card_daily_limit = card.card_daily_limit
            card_status = card.status
            card_type = card.card_type
            card_created = card.created_at
            card_updated = card.updated_at

            response_detail = {
                "username": username,
                "userEmail": user_email,
                "walletNumber": wallet_number,
                "walletType": wallet_type,
                "cardDailyLimit": card_daily_limit,
                "cardStatus": card_status,
                "cardType": card_type,
                "createdAt": card_created,
                "updateAt": card_updated
            }

            data = response_package(
                response_code=ResponseCode.SUCCESS_CODE,
                response_message="Successfully retrieved card details.",
                response_detail=response_detail
            )
            return Response(data, status.HTTP_200_OK)
        
        except Cards.DoesNotExist:
            data = response_package(
                    response_code=ResponseCode.FAILED_CODE,
                    response_message="Card does not exist.",
                    developer_error_message="Card does not exist."
            )
            return Response(data, status.HTTP_400_BAD_REQUEST)
        
        except Exception as err:
            data = response_package(
                    response_code=ResponseCode.FAILED_CODE,
                    response_message="Failed to retrieve card details.",
                    developer_error_message=str(err)
            )
            return Response(data, status.HTTP_400_BAD_REQUEST)
        

class CardWithdrawal(generics.GenericAPIView):

    serializer_class = CardWithdrawalSerializer

    def post(self, request):
        try:
            serializer = CardWithdrawalSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            card_id = serializer.validated_data.get("card_id")
            amount = serializer.validated_data.get("amount")

            card = CustomerCardDetail.objects.get(card_id=card_id)
            if not card.is_active:
                data = response_package(
                        response_code="99",
                        response_message="Card not active for wallet debits",
                        developer_error_message="Card not active for wallet debits",
                )
                return Response(data, status.HTTP_400_BAD_REQUEST)
                
            card_withdrawal_charge = ConstantTable.get_constant_table_instance().card_withdrawal_charge
            is_balance_sufficient = CardManager.sufficient_balance(amount=amount, card=card, charge=card_withdrawal_charge)

            if not is_balance_sufficient:
                data = response_package(
                        response_code="51",
                        response_message="INSUFFICENT BALANCE",
                        developer_error_message="INSUFFICENT BALANCE"
                )
                print(f"SAVINGS RESP :: {data}")
                return Response(data, status.HTTP_400_BAD_REQUEST)
            
            total_amount = amount + card_withdrawal_charge
            unique_reference = str(uuid.uuid4()).replace("-", "")
            ref = f"cdb{unique_reference}"
            transaction_form_type = "CARD_WITHRAWAL"

            # ----------  transaction for amount withdrawn ---------- #
            trans_objs = TransactionService.dynamic_deduction_from_wallet_transaction(
                user=card.user.user,
                amount=amount,
                wallet_type=card.wallet.wallet_type,
                quotation_id=ref,
                ajo_user=card.user,
                transaction_form_type=transaction_form_type,
            )
            # ----------  deduct ajo wallet for amount withdrawn ---------- #
            deduct_ajo_wallet = WalletSystem.deduct_balance(
                wallet=card.wallet,
                amount=amount,
                transaction_instance=trans_objs,
                unique_reference=None,
                onboarded_user=card.user,
            )
            trans_objs.status = Status.SUCCESS
            trans_objs.transaction_date_completed = datetime.now()
            trans_objs.wallet_balance_before = deduct_ajo_wallet["balance_before"]
            trans_objs.wallet_balance_after = deduct_ajo_wallet["balance_after"]
            trans_objs.save()

            # ----------  transaction for withdrawal charge ---------- #
            unique_reference = str(uuid.uuid4()).replace("-", "")
            ref = f"cc{unique_reference}"
            trans_charge_objs = TransactionService.dynamic_deduction_from_wallet_transaction(
                user=card.user.user,
                amount=card_withdrawal_charge,
                wallet_type=card.wallet.wallet_type,
                quotation_id=ref,
                ajo_user=card.user,
                transaction_form_type="CARD_WITHRAWAL_CHARGE",
            )

            # ----------  deduct ajo wallet for withdrawal charge ---------- #
            deduct_charge_wallet = WalletSystem.deduct_balance(
                wallet=card.wallet,
                amount=card_withdrawal_charge,
                transaction_instance=trans_charge_objs,
                unique_reference=None,
                onboarded_user=card.user,
            )

            trans_charge_objs.status = Status.SUCCESS
            trans_charge_objs.transaction_date_completed = datetime.now()
            trans_charge_objs.wallet_balance_before = deduct_charge_wallet["balance_before"]
            trans_charge_objs.wallet_balance_after = deduct_charge_wallet["balance_after"]
            trans_charge_objs.save()
            
            SavingsToCardsReconciliationTable.objects.create(
                user=card.request_card.user.user,
                ajo_user=card.request_card.user,
                amount=total_amount,
                narration="Card Wallet Debit Transaction",
                unique_reference=unique_reference,
                trnsaction_type="withdrawal",
                charge=card_withdrawal_charge
            )

            data = response_package(
                    response_code=ResponseCode.SUCCESS_CODE,
                    response_message="Withdrawal Successful",
                    response_detail=f"{amount} Withdrawn successfully."
            )
            return Response(data, status.HTTP_200_OK)
        
        except CustomerCardDetail.DoesNotExist:
            data = response_package(
                    response_code=ResponseCode.FAILED_CODE,
                    response_message="Card does not exist.",
                    developer_error_message="Card does not exist."
            )
            return Response(data, status.HTTP_400_BAD_REQUEST)
        
        except Exception as err:
            data = response_package(
                    response_code=ResponseCode.FAILED_CODE,
                    response_message="Failed to make card withdrawal.",
                    developer_error_message=str(err)
            )
            return Response(data, status.HTTP_400_BAD_REQUEST)

        
class UpdateCardStatus(generics.GenericAPIView):

    def patch(self, request, card_id, new_status):
        # THERE SHOULD BE A DIFFERENT TABLE TO SAVE THESE DETAILS
        # LIKE REASON, AND TIME.
        try:
            card = Cards.objects.get(card_id=card_id) 
            if not card.status == "ACTIVE":
                raise Exception("Not an active card.")
            
            if new_status not in CardStatus.values:
                raise Exception("Card status not a valid choice")
            
            card.status = new_status
            card.save()

            return Response(status=status.HTTP_204_NO_CONTENT)
        
        except Cards.DoesNotExist:
            data = response_package(
                    response_code=ResponseCode.FAILED_CODE,
                    response_message="Card does not exist.",
                    developer_error_message="Card does not exist."
            )
            return Response(data, status.HTTP_400_BAD_REQUEST)
        
        except Exception as err:
            data = response_package(
                    response_code=ResponseCode.FAILED_CODE,
                    response_message="Failed to update card.",
                    developer_error_message=str(err)
            )
            return Response(data, status.HTTP_400_BAD_REQUEST)



