from django.contrib import admin

# Register your models here.

# admin.py
from import_export.admin import ImportExportModelAdmin
from django.contrib import admin
from .models import CustomerCardDetail, RequestCardData, SavingsToCardsReconciliationTable

# @admin.register(CustomerCardDetail)
# class CustomerCardDetailAdmin(admin.ModelAdmin):
#     # Fields to display in the admin list view
#     list_display = (
#         'user', 
#         'wallet', 
#         'request_card', 
#         'card_id', 
#         'card_type', 
#         'daily_limit', 
#         'card_brand', 
#         'card_masked_pan', 
#         'card_status', 
#         'is_activated', 
#         'is_frozen', 
#         'is_canceled', 
#         'is_active', 
#         'date_created', 
#         'date_updated'
#     )

#     # Enable search functionality on key fields
#     search_fields = (
#         'card_id', 
#         'cuser__username', 
#         'card_brand', 
#         'card_masked_pan', 
#         'card_status'
#     )

#     # Filters to make it easier to narrow down the list
#     list_filter = (
#         'is_activated', 
#         'is_frozen', 
#         'is_canceled', 
#         'is_active', 
#         'card_type', 
#         'date_created'
#     )

#     # Read-only fields that shouldn't be modified directly
#     readonly_fields = (
#         'card_id', 
#         'date_created', 
#         'date_updated'
#     )

#     # Automatically generate a card ID if not already set
#     def save_model(self, request, obj, form, change):
#         if not obj.card_id:
#             obj.card_id = self.generate_unique_card_id()
#         super().save_model(request, obj, form, change)

#     def generate_unique_card_id(self):
#         import uuid
#         return str(uuid.uuid4())





# # admin.py


# class RequestCardDataAdmin(admin.ModelAdmin):
#     list_display = (
#         'id', 'user', 'card_type', 'card_brand', 'card_request_id', 'wallet_tied', 'assigned', 
#         'unique_reference', 'card_given_out', 'date_created', 'is_request_cancelled',
#         'last_updated'
#     )
#     list_filter = (
#         'card_type', 'assigned', 'card_given_out', 'date_created', 'is_request_cancelled', 'wallet_tied'
#     )
#     search_fields = ('card_request_id', 'user__email', 'unique_reference', 'card_brand')
#     ordering = ('-date_created',)
#     autocomplete_fields = ['user']  # Optional: Enable autocomplete for ForeignKey to User

#     fieldsets = (
#         (None, {
#             'fields': ('user', 'card_type', 'card_brand', 'card_request_id', 'unique_reference', 'wallet_tied')
#         }),
#         ('Card Status', {
#             'fields': ('assigned', 'card_given_out')
#         }),
#         ('Dates', {
#             'fields': ('date_created', 'last_updated')
#         })
#     )

#     readonly_fields = ('date_created', 'last_updated')  # Make date fields read-only

#     def save_model(self, request, obj, form, change):
#         # Here, you can add custom logic before saving the model if needed.
#         super().save_model(request, obj, form, change)


# @admin.register(SavingsToCardsReconciliationTable)
# class SavingsToCardsReconciliationTableAdmin(admin.ModelAdmin):
#     list_display = (
#         'id',
#         'date_created',
#         'user',
#         'ajo_user',
#         'amount',
#         'charge',
#         'trnsaction_type',
#         'status',
#         'narration',
#         'unique_reference'
#     )
#     list_filter = ('status', 'trnsaction_type', 'date_created')
#     search_fields = ('user__username', 'ajo_user__username', 'unique_reference', 'narration')
#     readonly_fields = ('date_created', 'unique_reference')
#     fieldsets = (
#         (None, {
#             'fields': (
#                 'date_created',
#                 'user',
#                 'ajo_user',
#                 'amount',
#                 'charge',
#                 'trnsaction_type',
#                 'status',
#                 'narration',
#                 'unique_reference'
#             ),
#         }),
#     )



# admin.site.register(RequestCardData, RequestCardDataAdmin)
# # admin.site.register(CustomerCardDetail, CustomerCardDetailAdmin)




class RequestCardDataResource(ImportExportModelAdmin):
    resource_classes = [RequestCardData]
    # search_fields = ("agent__email",)
    list_filter = [
        ("date_created", admin.DateFieldListFilter),
    ]

    raw_id_fields = ["user", "wallet_tied"]

    # def get_list_display(self, request):
    #     return [field.name for field in self.model._meta.concrete_fields]

class CustomerCardDetailsResource(ImportExportModelAdmin):
    resource_classes = [CustomerCardDetail]
    # search_fields = ("agent__email",)
    list_filter = [
        ("date_created", admin.DateFieldListFilter),
    ]

    raw_id_fields = ["user", "wallet", "request_card"]

class SavingsToCardsReconciliationResource(ImportExportModelAdmin):
    resource_classes = [SavingsToCardsReconciliationTable]
    # search_fields = ("agent__email",)
    list_filter = [
        ("date_created", admin.DateFieldListFilter),
    ]

    raw_id_fields = ["user"]


admin.site.register(RequestCardData, RequestCardDataResource)
admin.site.register(CustomerCardDetail, CustomerCardDetailsResource)
admin.site.register(SavingsToCardsReconciliationTable, SavingsToCardsReconciliationResource)
