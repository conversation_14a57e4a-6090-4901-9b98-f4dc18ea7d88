import base64
import io
import json
import uuid
from io import BytesIO

import requests
from django.conf import settings
from PIL import Image, ImageFile

from redis_storage import RedisStore

# Enable handling of truncated images
ImageFile.LOAD_TRUNCATED_IMAGES = True


class Biometrics:
    def __init__(self) -> None:
        # self.base_url = "https://kyc.libertyng.com/api/v1"
        self.base_url = "http://159.223.9.10:8020/api/v1"
        self.email = settings.LIBERTY_BIOMETRICS_EMAIL
        self.password = settings.LIBERTY_BIOMETRICS_PASSWORD
        self.environ = settings.ENVIRONMENT
        self.redis_store = RedisStore()
        self.retry_count: int = 0

    @staticmethod
    def generate_unique_ref() -> str:
        # timestamp = int(time.time() * 1000)  # Current timestamp in milliseconds
        # random_number = random.randint(1000, 9999)  # Random 4-digit number
        # unique_ref = f"loans-{timestamp}-{random_number}"
        unique_ref = f"loans-{uuid.uuid4()}"
        return unique_ref

    @staticmethod
    def convert_b64_2_jpg(b64_str):

        # with open(image_path, 'rb') as image_file:
        # Decode base64 string and create in-memory image
        b64_str = b64_str.split(",")[-1]  # FIrst get just the base64 side of the image
        image_bytes = base64.b64decode(b64_str.encode())
        image_file = io.BytesIO(image_bytes)
        image = Image.open(image_file)

        # Convert to JPEG (modify format if needed)
        image_buffer = io.BytesIO()

        try:
            image.save(image_buffer, format="JPEG")
        except OSError:
            image = image.convert("RGB")

        image.save(image_buffer, format="JPEG")
        image_data = image_buffer.getvalue()

        return image_data

    def login(self):
        url = f"{self.base_url}/accounts/user/login"

        payload = json.dumps({"email": self.email, "password": self.password})
        headers = {"Content-Type": "application/json"}

        response = requests.request("POST", url, headers=headers, data=payload)

        return response.json()

    def _result_handler(self, payload, response, url):
        """
        Handles the response from API requests, returning a structured result.

        Args:
            payload (dict): The data sent with the request.
            response (requests.Response): The response object returned by the API.
            url (str): The URL that was called.

        Returns:
            dict: A dictionary containing the request result, including status, response,
                  and other related information.
        """
        try:
            response = {
                "url": url,
                "status_code": response.status_code,
                "status": "success",
                "response": response.json(),
                "method": response.request.method,
                "payload": payload,
            }
        except (
            requests.exceptions.RequestException,
            Exception,
            json.decoder.JSONDecodeError,
        ) as e:
            response = {
                "url": url,
                "error": str(e),
                "status_code": response.status_code,
                "status": "failed",
                "method": response.request.method,
                "response": response.text,
                "payload": payload,
            }
        return response

    def face_Similarity(self, ref_id, face_one, face_two):
        payload = {"client_reference": ref_id}

        files = [
            ("face_one", ("IMG_ONE.jpg", face_one, "image/jpeg")),
            ("face_two", ("IMG_TWO.jpg", face_two, "image/jpeg")),
        ]
        headers = {
            "Authorization": f"Bearer {self.get_token()}",
            # 'Content-Type': 'multipart/form-data'
        }
        url = f"{self.base_url}/kyc/face-match"
        response = requests.request(
            "POST", url, headers=headers, data=payload, files=files
        )
        result = self._result_handler(payload=payload, response=response, url=url)
        return result

    def get_token(self):
        access_token = self.redis_store.get_data(key=self.password)
        # print(access_token, "\n\n")
        if access_token is None:
            login_request = self.login()
            data = login_request.get("data")
            access = data.get("access")

            self.redis_store.set_data(key=self.password, value=access)
            return access

        else:
            return str(access_token).replace("b'", "").replace("'", "")

    def face_match(self, request_ref, face_one, face_two):
        # print(self.get_token())
        payload = {"client_reference": request_ref}

        files = [
            ("face_one", ("IMG_ONE.jpg", face_one, "image/jpeg")),
            ("face_two", ("IMG_TWO.jpg", face_two, "image/jpeg")),
        ]
        headers = {
            "Authorization": f"Bearer {self.get_token()}",
            # 'Content-Type': 'multipart/form-data'
        }
        url = f"{self.base_url}/kyc/face-match"
        response = requests.request(
            "POST", url, headers=headers, data=payload, files=files
        )
        # print()
        # print(response.text, "\n\n\n")
        if response.status_code == 401:
            if self.retry_count < 3:
                self.redis_store.delete_data(key=self.password)
                self.retry_count += 1
                return self.face_match(request_ref, face_one, face_two)
            else:
                raise ValueError("An error occurred.Contact support")

        else:
            if self.environ == "production":
                # error resultreturn {
                #     "status": "error",
                #     "message": "Data Processed Successfully",
                #     "errors": {"data": {"error": "API request failed"}},
                # }
                return response.json()

            else:

                return response.json()
                # data = {
                #     "status": "success",
                #     "message": "Data Processed Successfully",
                #     "data": {
                #         "data": {
                #             "result": True,
                #             "similarity": 1
                #         },
                #         "extra_data": {
                #             "Result": "Same person",
                #             "Similarity": "1.0"
                #         }
                #     }
                # }
                # return data

    def liveness_detection(self, request_ref, face):
        # print(self.get_token())
        payload = {"client_reference": request_ref}

        files = [
            ("face", ("IMG_ONE.jpg", face, "image/jpeg")),
        ]
        headers = {
            "Authorization": f"Bearer {self.get_token()}",
            # 'Content-Type': 'multipart/form-data'
        }
        url = f"{self.base_url}/kyc/liveness-detection"
        response = requests.request(
            "POST", url, headers=headers, data=payload, files=files
        )
        # print(response.text)
        if response.status_code == 401:
            if self.retry_count < 3:
                self.redis_store.delete_data(key=self.password)
                self.retry_count += 1
                return self.liveness_detection(request_ref, face)
            else:
                raise ValueError("An error occurred.Contact support")

        else:
            if self.environ == "production":
                return response.json()
            else:
                return response.json()

    # @staticmethod
    # def convert_b64_2_jpg(b64_str):

    #     # with open(image_path, 'rb') as image_file:
    #     # Decode base64 string and create in-memory image
    #     b64_str = b64_str.split(",")[-1]  # FIrst get just the base64 side of the image
    #     image_bytes = base64.b64decode(b64_str.encode())
    #     image_file = io.BytesIO(image_bytes)
    #     image = Image.open(image_file)

    #     # Convert to JPEG (modify format if needed)
    #     image_buffer = io.BytesIO()

    #     try:
    #         image.save(image_buffer, format="JPEG")
    #     except OSError:
    #         image = image.convert("RGB")

    #     image.save(image_buffer, format="JPEG")
    #     image_data = image_buffer.getvalue()

    @staticmethod
    def convert_b64_2_jpg(b64_str):
        try:
            # Extract the base64 portion
            b64_str = b64_str.split(",")[-1]  # Handles base64 strings with data URIs
            # Decode the base64 string into bytes
            image_bytes = base64.b64decode(b64_str.encode())
            image_file = io.BytesIO(image_bytes)

            # Open the image
            image = Image.open(image_file)
            image.verify()  # Verifies the integrity of the image

            # Re-open after verification (to reset file pointer)
            image = Image.open(io.BytesIO(image_bytes))

            # Convert to JPEG format
            image_buffer = io.BytesIO()
            try:
                image.save(image_buffer, format="JPEG")
            except OSError:
                # Convert to RGB if the image mode is incompatible
                image = image.convert("RGB")
                image.save(image_buffer, format="JPEG")

            # Get the resulting image bytes
            image_data = image_buffer.getvalue()
            return image_data

        except (base64.binascii.Error, ValueError) as err:
            raise ValueError(f"Invalid base64 string. error: {str(err)}")
        except (OSError, IOError) as e:
            raise ValueError(f"Invalid or corrupted image file. error: {str(err)}")

    def facial_biometrics_on_request(self, model_instance, face_two_bs64):

        request_ref = self.generate_unique_ref()
        # print(face_two, "-----------\n\n")
        base_64_img_string = model_instance.base_64_img_string

        # Extract the image data from the base64 string
        split_string = base_64_img_string.split(",")
        image_string = split_string[1] if len(split_string) > 1 else base_64_img_string

        # # Decode the base64 image string to image data
        # image_data = base64.b64decode(image_string)

        # # Open the image from the image data
        # image = Image.open(BytesIO(image_data))

        # # Convert the image to bytes
        # image_bytes = BytesIO()
        # image.save(image_bytes, format="JPEG")
        # image_bytes.seek(0)

        # # Extract the image data from the base64 string
        # face_two_str = face_two_bs64.split(",")
        # image_string = face_two_str[1] if len(face_two_str) > 1 else face_two_bs64

        # # Decode the base64 image string to image data
        # image_data = base64.b64decode(image_string)

        # # Open the image from the image data
        # image = Image.open(BytesIO(image_data))

        # # Convert the image to bytes
        # image_bytes2 = BytesIO()
        # image.save(image_bytes2, format="JPEG")
        # image_bytes2.seek(0)

        face_two = self.convert_b64_2_jpg(b64_str=face_two_bs64)
        face_one = self.convert_b64_2_jpg(b64_str=image_string)

        # Perform face matching
        face_match_response = self.face_match(
            request_ref=request_ref, face_one=face_one, face_two=face_two
        )

        result = {
            "face_match_response": face_match_response,
            "model_instance": model_instance,
            "request_ref": request_ref,
            "base64_img_str": face_two_bs64,
        }
        return result

    def liveness_on_request(self, face):

        # print(face, "\n\n")
        # Generate a unique request reference
        request_ref = self.generate_unique_ref()

        # Read the image data of the second face
        image_data = face.read()
        # print(image_data, "\n\n\n000000000")
        # Create a BytesIO object to hold the image data of the second face
        image_data_io = BytesIO()
        image_data_io.write(image_data)
        image_data_io.seek(0)  # Reset the position of the buffer to the beginning
        # Encode the binary data of the second face to base64
        base64_image_string = base64.b64encode(image_data_io.getvalue()).decode()

        # Close the BytesIO object
        image_data_io.close()

        # Perform face matching
        image_liveness = self.liveness_detection(
            request_ref=request_ref, face=image_data
        )

        result = {
            "face_match_response": image_liveness,
            # "model_instance": model_instance,
            "request_ref": request_ref,
            "base64_img_str": base64_image_string,
        }
        return result

    def verify_two_bs64_img(
        self, face_one_bs64, face_two_bs64, ajo_user, kyc_ref, user_type="BORROWER"
    ):
        from loans.models import BiometricsMetaData

        request_ref = self.generate_unique_ref()

        # Extract the image data from the base64 string
        split_string = face_one_bs64.split(",")
        image_string1 = split_string[1] if len(split_string) > 1 else face_one_bs64

        # # Decode the base64 image string to image data
        # image_data = base64.b64decode(image_string)

        # # Open the image from the image data
        # image = Image.open(BytesIO(image_data))

        # # Convert the image to bytes
        # image_bytes = BytesIO()
        # image.save(image_bytes, format="JPEG")
        # image_bytes.seek(0)

        # Extract the image data from the base64 string
        face_two_str = face_two_bs64.split(",")
        image_string2 = face_two_str[1] if len(face_two_str) > 1 else face_two_bs64

        # # Decode the base64 image string to image data
        # image_data = base64.b64decode(image_string)

        # # Open the image from the image data
        # image = Image.open(BytesIO(image_data))

        # # Convert the image to bytes
        # image_bytes2 = BytesIO()
        # image.save(image_bytes2, format="JPEG")
        # image_bytes2.seek(0)

        # # Perform face matching
        # face_match_response = self.face_match(
        #     request_ref=request_ref,
        #     face_one=image_bytes,
        #     face_two=image_bytes2,
        # )

        face_two = self.convert_b64_2_jpg(b64_str=image_string1)
        face_one = self.convert_b64_2_jpg(b64_str=image_string2)

        # Perform face matching
        face_match_response = self.face_match(
            request_ref=request_ref, face_one=face_one, face_two=face_two
        )
        # print(face_match_response, "\n\n")
        data = face_match_response.get("data")
        BiometricsMetaData.objects.create(
            ajo_user=ajo_user,
            request_ref=request_ref,
            user_type=user_type,
            base64_img_str=face_one_bs64,
            img_str_bvn_or_nin=face_two_bs64,
            status=data.get("data")["result"] if data else False,
            similarity=data.get("data").get("similarity", 0.0001) if data else 0.0001,
            response_data=face_match_response,
            is_documentation=True,
            kyc_ref=kyc_ref,
        )

        result = {
            "face_match": face_match_response,
            "request_ref": request_ref,
        }
        return result

    def base_64_liveliness_check(
        self, img_str, ajo_user, kyc_ref, user_type="BORROWER"
    ):
        from loans.models import BiometricsMetaData

        request_ref = self.generate_unique_ref()

        # Extract the image data from the base64 string
        split_string = img_str.split(",")
        image_string = split_string[1] if len(split_string) > 1 else img_str

        # print(image_string, "\n\n")
        # Decode the base64 image string to image data
        # image_data = base64.b64decode(image_string)

        # # Open the image from the image data
        # image = Image.open(BytesIO(image_data))

        # # Convert the image to bytes
        # image_bytes = BytesIO()
        # image.save(image_bytes, format="JPEG")
        # image_bytes.seek(0)
        # image = self.convert_b64_2_jpg(b64_str=img_str)

        face = self.convert_b64_2_jpg(b64_str=image_string)

        image_liveness = self.liveness_detection(request_ref=request_ref, face=face)
        # creat biometrics model instance

        BiometricsMetaData.objects.create(
            ajo_user=ajo_user,
            request_ref=request_ref,
            user_type=user_type,
            base64_img_str=image_string,
            status=image_liveness.get("data").get("data")["result"],
            similarity=image_liveness.get("data")
            .get("data")
            .get("liveness_score", 0.001),
            request_type="LIVELINESS",
            response_data=image_liveness,
            is_documentation=True,
            kyc_ref=kyc_ref,
        )

        result = {
            "image_liveness": image_liveness,
            "request_ref": request_ref,
        }
        return result
