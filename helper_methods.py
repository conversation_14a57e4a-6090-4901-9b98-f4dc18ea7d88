"""
module: helper_methods.py

This module contains pure Python utility functions that are designed to be reusable across the entire project.

The functions defined here implement common logic that is independent of any frameworks, external dependencies,
or project-specific details. All methods in this module are built using Python's standard library and are meant
to serve as shared logic across different parts of the codebase.

These functions provide a centralized location for common operations, improving maintainability and reducing
code duplication. Any new functionality that meets these criteria should be added to this module.

Guidelines for using this module:
- Only add logic that is independent of specific frameworks or third-party libraries.
- Ensure that the methods are general-purpose and can be reused in multiple places across the project.

Example usage:
    from helper_methods import some_function

    result = some_function(input_data)
"""

import ast
import json
import secrets
import re
import uuid
import requests
from datetime import datetime


def is_valid_digit_number(input_string, num_digits):
    """
    Checks if the input_string is a valid number with the specified number of digits.

    Args:
        input_string (str): The string to be checked.
        num_digits (int): The required number of digits for the input to be valid.

    Returns:
        bool: True if the input_string is valid, False otherwise.
    """
    pattern = rf"^\d{{{num_digits}}}$"  # Using f-string for dynamic number of digits
    return bool(re.match(pattern, input_string))


def convert_to_standard_date(date_str):
    """Convert various date formats to YYYY-MM-DD format."""
    date_formats = [
        "%d-%b-%Y",
        "%d/%m/%Y",
        "%m-%d-%Y",
        "%Y/%m/%d",
        "%d %B %Y",
    ]  # Add more formats if needed

    for fmt in date_formats:
        try:
            return datetime.strptime(str(date_str), fmt).strftime("%Y-%m-%d")
        except ValueError:
            continue

    return None


class BNPLProductHelper:
    def __init__(self, item_description, price, image):
        self.item_description = item_description
        self.price = price
        self.image = image
        self.category_patterns = {
            "electronics": {
                "patterns": [
                    r"RAM",
                    r"ROM",
                    r"Dual SIM",
                    r"Android",
                    r"iOS",
                    r"screen",
                    r"display",
                    r"laptop",
                    r"tablet",
                    r"smartphone",
                    r"phone",
                    r"mobile",
                    r"smartwatch",
                    r"headphone",
                    r"earbud",
                    r"camera",
                    r"tv",
                    r"television",
                    r"charger",
                    r"power bank",
                    r"gaming",
                    r"monitor",
                    r"fm",
                ],
                "subcategories": {
                    "phones": [
                        r"phone",
                        r"mobile",
                        r"smartphone",
                        r"Android",
                        r"iOS",
                        r"sim",
                        r"fm",
                    ],
                    "tablet": [r"tablet", r"iPad"],
                    "laptop": [r"laptop", r"notebook", r"MacBook", r"Chromebook"],
                    "smartwatch": [r"smartwatch", r"fitness tracker", r"wearable"],
                    "tv": [r"tv", r"television", r"LED", r"OLED", r"display"],
                    "audio": [
                        r"headphone",
                        r"earbud",
                        r"speaker",
                        r"audio",
                        r"soundbar",
                    ],
                    "camera": [r"camera", r"DSLR", r"mirrorless", r"GoPro"],
                    "accessories": [r"charger", r"adapter", r"power bank", r"USB"],
                },
            },
            "home appliances": {
                "patterns": [
                    r"fridge",
                    r"refrigerator",
                    r"microwave",
                    r"oven",
                    r"air conditioner",
                    r"fan",
                    r"heater",
                    r"vacuum",
                    r"washing machine",
                    r"dryer",
                ],
                "subcategories": {
                    "kitchen appliance": [
                        r"microwave",
                        r"oven",
                        r"blender",
                        r"toaster",
                        r"mixer",
                        r"juicer",
                        r"cooker",
                    ],
                    "air conditioning": [
                        r"air conditioner",
                        r"fan",
                        r"heater",
                        r"humidifier",
                    ],
                    "cleaning appliance": [r"vacuum", r"steam cleaner"],
                    "laundry": [r"washing machine", r"dryer"],
                },
            },
            "fashion and apparel": {
                "patterns": [
                    r"shirt",
                    r"t-shirt",
                    r"jeans",
                    r"dress",
                    r"sneakers",
                    r"shoes",
                    r"heels",
                    r"watch",
                    r"jewelry",
                    r"handbag",
                    r"backpack",
                    r"suit",
                ],
                "subcategories": {
                    "men's clothing": [
                        r"shirt",
                        r"jeans",
                        r"t-shirt",
                        r"blazer",
                        r"suit",
                    ],
                    "women's clothing": [
                        r"dress",
                        r"blouse",
                        r"skirt",
                        r"heels",
                        r"jewelry",
                    ],
                    "footwear": [r"shoes", r"sneakers", r"heels", r"boots", r"sandals"],
                    "accessories": [r"watch", r"belt", r"scarf", r"hat", r"sunglasses"],
                },
            },
            "beauty and personal care": {
                "patterns": [
                    r"skincare",
                    r"makeup",
                    r"perfume",
                    r"soap",
                    r"lotion",
                    r"shampoo",
                    r"conditioner",
                    r"hair dryer",
                    r"straightener",
                    r"serum",
                    r"sunscreen",
                    r"body lotion",
                    r"anti-perspirant spray",
                    r"micellar water",
                    r"deodorant",
                    r"spray",
                    r"crème",
                    r"gel",
                    r"cream",
                    r"facial",
                ],
                "subcategories": {
                    "makeup": [
                        r"lipstick",
                        r"Gel",
                        r"foundation",
                        r"eyeliner",
                        r"mascara",
                    ],
                    "skincare": [
                        r"serum",
                        r"moisturizer",
                        r"Crème",
                        r"cleanser",
                        r"sunscreen",
                        r"cream",
                        r"facial ",
                    ],
                    "hair care": [
                        r"shampoo",
                        r"conditioner",
                        r"hair oil",
                        r"hair dryer",
                        r"straightener",
                    ],
                    "fragrance": [
                        r"perfume",
                        r"cologne",
                        r"body lotion",
                        r"anti-perspirant spray",
                        r"micellar water",
                        r"deodorant",
                        r"spray",
                    ],
                },
            },
            "groceries and food": {
                "patterns": [
                    r"snacks",
                    r"beverages",
                    r"dairy",
                    r"frozen",
                    r"vegetables",
                    r"meat",
                    r"spices",
                    r"condiments",
                ],
                "subcategories": {
                    "packaged foods": [r"chips", r"biscuits", r"chocolate", r"cereal"],
                    "beverages": [r"juice", r"soda", r"tea", r"coffee"],
                    "fresh produce": [r"vegetables", r"fruits"],
                    "frozen foods": [r"frozen", r"ice cream"],
                    "condiments": [r"sauce", r"ketchup", r"mayonnaise"],
                },
            },
            "sports and outdoors": {
                "patterns": [
                    r"fitness",
                    r"gym",
                    r"equipment",
                    r"yoga",
                    r"bicycle",
                    r"tent",
                    r"camping",
                    r"hiking",
                    r"skateboard",
                    r"cricket",
                    r"football",
                ],
                "subcategories": {
                    "fitness equipment": [
                        r"dumbbell",
                        r"treadmill",
                        r"weights",
                        r"yoga",
                    ],
                    "outdoor gear": [r"tent", r"camping", r"hiking", r"backpack"],
                    "cycling": [r"bicycle", r"helmet"],
                    "sports equipment": [
                        r"cricket",
                        r"football",
                        r"basketball",
                        r"tennis",
                    ],
                },
            },
            "toys and baby products": {
                "patterns": [
                    r"toys",
                    r"dolls",
                    r"puzzles",
                    r"baby",
                    r"crib",
                    r"stroller",
                    r"diapers",
                    r"feeding bottle",
                ],
                "subcategories": {
                    "toys": [
                        r"puzzles",
                        r"dolls",
                        r"cars",
                        r"blocks",
                        r"action figure",
                    ],
                    "baby essentials": [
                        r"diapers",
                        r"stroller",
                        r"crib",
                        r"feeding bottle",
                    ],
                },
            },
            "books and stationery": {
                "patterns": [
                    r"book",
                    r"novel",
                    r"notebook",
                    r"pen",
                    r"pencil",
                    r"marker",
                    r"highlighter",
                ],
                "subcategories": {
                    "books": [r"novel", r"fiction", r"non-fiction", r"biography"],
                    "stationery": [
                        r"notebook",
                        r"pen",
                        r"pencil",
                        r"highlighter",
                        r"marker",
                    ],
                },
            },
            "automotive": {
                "patterns": [
                    r"car",
                    r"motorcycle",
                    r"engine",
                    r"tire",
                    r"battery",
                    r"bike",
                ],
                "subcategories": {
                    "car accessories": [r"seat cover", r"steering", r"wiper"],
                    "motorcycle accessories": [r"helmet", r"bike cover"],
                    "spare parts": [r"battery", r"engine", r"tire"],
                },
            },
            "health and wellness": {
                "patterns": [
                    r"supplements",
                    r"vitamins",
                    r"protein",
                    r"mask",
                    r"sanitizer",
                    r"thermometer",
                ],
                "subcategories": {
                    "health supplements": [
                        r"vitamin",
                        r"protein",
                        r"omega-3",
                        r"probiotic",
                    ],
                    "medical equipment": [r"mask", r"sanitizer", r"thermometer"],
                },
            },
        }

    def extract_brand_and_product(self):
        """
        Extract the brand (first word) and the full product name,
        keeping critical product identifiers intact (e.g., "14C").
        """
        # Regular expression to identify brand and product
        match = re.match(
            r"(\w+)\s+(.*?)(\s[\d+]?GB|RAM|ROM|Android|mAh|Dual|SIM|FM|Torch|MHz| - ).*",
            self.item_description,
            flags=re.IGNORECASE,
        )
        if match:
            brand = match.group(1)  # First word as brand
            product = match.group(
                2
            ).strip()  # Everything before the specifications section
            return brand, product
        return None, None  # In case no match

    def to_valid_float(self):
        try:
            # If the value is already a float, return it
            if isinstance(self.price, float):
                return self.price

            # Convert the input to a string and remove all non-numeric characters except for the decimal point
            clean_value = re.sub(r"[^\d.]", "", str(self.price))

            # Convert the cleaned string to a float
            return float(clean_value)
        except ValueError:
            # Return None if the value cannot be converted
            return 100000000000000000000000000000000000

    def categorize_item(self):
        main_category = None
        subcategory = None

        # Iterate over main categories and their patterns
        for category, data in self.category_patterns.items():
            for pattern in data["patterns"]:
                if re.search(rf"\b{pattern}\b", self.item_description, re.IGNORECASE):
                    main_category = category
                    # Check subcategory patterns
                    for subcat, subcat_patterns in data.get(
                        "subcategories", {}
                    ).items():
                        for subcat_pattern in subcat_patterns:
                            if re.search(
                                rf"\b{subcat_pattern}\b",
                                self.item_description,
                                re.IGNORECASE,
                            ):
                                subcategory = subcat
                                break
                        if subcategory:  # Break if subcategory is found
                            break
                    break
            if main_category:  # Break if main category is found
                break

        # Return categorized result
        return {
            "item": self.item_description,
            "main_category": main_category or "Others",
            "subcategory": subcategory or "Others",
        }

    def get_product_details(self):
        result = self.categorize_item()
        result["product_price"] = self.to_valid_float()
        result["image"] = self.image
        result["name"] = self.extract_brand_and_product()[0]
        result["model"] = self.extract_brand_and_product()[1]

        return result


# def validate_address_pattern(address: str):
#     """
#     Validates if a given address matches the standard Nigerian address format.

#     Raises:
#         ValueError: If the address format is incorrect.
#     """
#     # address_pattern = re.compile(
#     #     r"^\d{0,5}[A-Za-z0-9\s,./#-]*,\s*[A-Za-z\s]+,\s*[A-Za-z\s]+(?:,\s*\d{6})?(?:,\s*Nigeria)?$"
#     # )

#     address_pattern = re.compile(
#         r"^\d{0,5}[A-Za-z0-9\s,./#-]+,\s*(?:Near|Opposite|By|Beside)\s+[A-Za-z0-9\s,./#-]+,\s*[A-Za-z\s]+,\s*[A-Za-z\s]+(?:,\s*\d{6})?(?:,\s*Nigeria)?$"
#     )

#     if not address_pattern.match(address):
#         raise ValueError(
#             "Invalid address format. Expected format: 'House/Building Number, Street, Locality/City, State, [Optional: Postal Code], [Optional: Nigeria]'. "
#             "Example: '27 Alara Street, Sabo-Yaba, Lagos, Nigeria'"
#         )

#     return True  # If valid, return True


def validate_address_pattern(
    address: str, pattern: str, uttility_bill_address_pattern: str = None
):
    """
    Validates if a given address matches the specified pattern.

    Args:
        address (str): The address string to validate.
        pattern (str): A regex pattern to validate the address format.

    Raises:
        ValueError: If the address format is incorrect.

    Returns:
        bool: True if the address is valid.
    """

    if not pattern:
        raise ValueError("A regex pattern must be provided for address validation.")

    address_pattern = re.compile(pattern)

    if not address_pattern.match(address):

        if uttility_bill_address_pattern:
            uttility_bill_address_pattern = re.compile(uttility_bill_address_pattern)
            if not uttility_bill_address_pattern:
                raise ValueError(f"Invalid Address Format{address}")

        else:
            raise ValueError(
                """Invalid address format. Expected format: 'House/Building Number,
                Street, Locality/City, State, [Optional: Postal Code], [Optional: Nigeria]'.
                Example: '27 Alara Street, Sabo-Yaba, Lagos, Nigeria'"""
            )
    return True


def parse_to_dict(data):
    """
    Parse input data to a Python dictionary.

    Args:
        data (str | dict): Input data which could be a JSON string or dictionary.

    Returns:
        dict: Parsed dictionary.

    Raises:
        ValueError: If the input is neither a dictionary nor a JSON string.
    """
    # print(type(data))
    if isinstance(data, dict):
        # Already a dictionary, return it as is.
        return data
    elif isinstance(data, str):
        try:
            # Attempt to convert the JSON string to a dictionary.
            return json.loads(data)
        except json.JSONDecodeError as e:
            try:
                return ast.literal_eval(data)
            except (ValueError, SyntaxError) as e:
                raise ValueError(f"Invalid JSON string: {e}")
    else:
        # Raise error for unsupported types.
        raise ValueError("Input data must be a dictionary or a JSON string.")


def format_api_response(payload, response, url, params=None):
    """
    Handles the response from API requests, returning a structured result.

    Args:
        payload (dict): The data sent with the request.
        response (requests.Response): The response object returned by the API.
        url (str): The URL that was called.

    Returns:
        dict: A dictionary containing the request result, including status, response,
              and other related information.
    """
    try:
        response = {
            "url": url,
            "status_code": response.status_code,
            "status": "success",
            "params": params,
            "response": response.json(),
            "method": response.request.method,
            "payload": payload,
        }
    except (
        requests.exceptions.RequestException,
        Exception,
        json.decoder.JSONDecodeError,
    ) as e:
        response = {
            "url": url,
            "error": str(e),
            "status_code": response.status_code,
            "status": "failed",
            "params": params,
            "method": response.request.method,
            "response": response.text,
            "payload": payload,
        }
    return response


def format_amount(amount):
    return "N{:,}".format(int(amount))


def generate_jti():
    return secrets.token_hex(16)  # Generates a 32-character hex string


def generate_reference():
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    uuid_str = str(uuid.uuid4()).split("-")[0]
    unique_id = f"{timestamp}-{uuid_str}"
    return unique_id


def normalize_ngn_phone(phone):
    phone = str(phone).strip()

    # Remove any leading '+' or spaces
    if phone.startswith("+"):
        phone = phone[1:]

    # If it starts with '234' and is 13 digits, convert to 11-digit local
    if phone.startswith("234") and len(phone) == 13:
        return "0" + phone[3:]

    # If it starts with 0 and is 11 digits, keep it
    if phone.startswith("0") and len(phone) == 11:
        return phone

    # If already normalized, return
    return phone
