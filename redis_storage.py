import redis


class RedisStore:
    """
    Class for storing data in Redis.
    """

    redis_client: object = redis.StrictRedis(host="localhost", port="6379", db=0)

    @classmethod
    def set_data(cls, key, value):
        """
        Set data in Redis.
        """
        cls.redis_client.set(key, value, ex=10800)

    @classmethod
    def get_data(cls, key):
        """
        Get data from Redis.
        """
        return cls.redis_client.get(key)

    @classmethod
    def get_or_create_data(cls, key, default=0):
        """
        Get data from Redis.
        """
        data = cls.redis_client.get(key)
        if data is None:
            cls.set_data(key, default)
            return default

        return data.decode()

    @classmethod
    def delete_data(cls, key):
        """
        Delete data from Redis.
        """
        cls.redis_client.delete(key)

    def clear_data(cls):
        """
        Clear data from Redis.
        """
        cls.redis_client.flushdb()

    @classmethod
    def set_data_with_expiration(cls, cache_key, value, expiration):
        """
        Set data in Redis.
        """
        cls.redis_client.setex(cache_key, expiration, value)
