from rest_framework import generics
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from admin_dashboard.services import (
    MainDashboard, MainDashboardCharts,
    Agent, AgentDetails, CustomerDetail,
    Branch, BranchDetails, SupervisorData,
    LoansDashboard
    )
from admin_dashboard.serializers import BranchSerializer
from admin_dashboard.permissions import AjoIsAuthenticated


class MainDashboardRevenueMetricsView(APIView):
    permission_classes = [AjoIsAuthenticated]

    def get(self, request):
        try:
            data = MainDashboard(request=request).get_main_dash_revenue_metrics()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardTransactionsMetricsView(APIView):
    permission_classes = [AjoIsAuthenticated]

    def get(self, request):
        try:
            data = MainDashboard(request).get_transactions_overview_one()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardTransactionsMetricsTwoView(APIView):
    permission_classes = [AjoIsAuthenticated]

    def get(self, request):
        try:
            data = MainDashboard(request).get_transactions_overview_two()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardSavingsMetricsView(APIView):
    permission_classes = [AjoIsAuthenticated]

    def get(self, request):
        try:
            data = MainDashboard(request).get_main_dash_savings_metrics()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardChartView(APIView):
    permission_classes = [AjoIsAuthenticated]

    def get(self, request):
        try:
            data = MainDashboardCharts(request).get_charts()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

class AgentOverviewView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request):
        try:
            data = Agent(request).get_agent_overview()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

class TopPerformingAgentsView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request):
        try:
            data = Agent(request).get_top_agents()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

class AgentsListView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request):
        try:
            data = Agent(request).get_agents_table()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class ActiveAgentsListView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request):
        try:
            data = Agent(request).get_active_agents_table()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
 
        
class InactiveAgentsListView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request):
        try:
            data = Agent(request).get_inactive_agents_table()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class SuspendedAgentsListView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request):
        try:
            data = Agent(request).get_suspended_agents_table()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AgentsDetailsOverView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request, id):
        try:
            data = AgentDetails(request).get_agent_detail(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AgentsWalletDetailsView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request, id):
        try:
            data = AgentDetails(request).get_agent_wallets(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AgentsTransactionsView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request, id):
        try:
            data = AgentDetails(request).get_agent_transactions(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class AgentsRevenueDetailsView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request, id):
        try:
            data = AgentDetails(request).get_agent_revenue_overview(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class TransactionsDetailView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request, id):
        try:
            data = AgentDetails(request).get_transaction_detail(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class AgentCustomersListView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request, id):
        try:
            data = AgentDetails(request).get_agent_customers_list(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AgentsActiveSessionsView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request, id):
        try:
            data = AgentDetails(request).get_agent_active_session(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
     

class AgentsLogHistoryListView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request):
        try:
            data = LeaderBoard(request).get_agents_log_history()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class CustomersOverView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request):
        try:
            data = CustomerDetail(request).get_customers_overview()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class Top5CustomersView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request):
        try:
            data = CustomerDetail(request).get_top5_customers_list()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class CustomersListView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request):
        try:
            data = CustomerDetail(request).get_customers_list()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

class CustomerDetailOverView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request, id):
        try:
            data = CustomerDetail(request).get_customer_overview_details(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class CustomerWalletDetailView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request, id):
        try:
            data = CustomerDetail(request).get_customer_wallet_details(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class CustomerTransactionsListView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request, id):
        try:
            data = CustomerDetail(request).get_customer_transactions_list(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class OverallTransactionsListView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request):
        try:
            data = AgentDetails(request).get_overall_transactions_list()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class CustomerActiveSavingsListView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request, id):
        try:
            data = CustomerDetail(request).get_customer_active_savings_list(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class CustomerInActiveSavingsListView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request, id):
        try:
            data = CustomerDetail(request).get_customer_inactive_savings_list(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class CustomerSavingsPlanOverView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request, id):
        try:
            data = CustomerDetail(request).get_customers_saving_plans_overview(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

class BranchOverviewView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request):
        try:
            data = Branch(request).get_branch_overview()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BranchChartsView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request):
        try:
            data = Branch(request).get_branch_charts()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BranchTop5View(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request):
        try:
            data = Branch(request).get_top5_branches()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BranchListView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request):
        try:
            data = Branch(request).get_branch_list()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        
        
class PerformingBranchListView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request):
        try:
            data = Branch(request).get_performing_branch_list()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

class UnderPerformingBranchListView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request):
        try:
            data = Branch(request).get_under_performing_branch_list()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BranchDetailsView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request, id):
        try:
            data = BranchDetails(request).get_branch_details(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BranchTransactionComparativesView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request, id):
        try:
            data = BranchDetails(request).get_branch_transaction_comparatives(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BranchTopSupervisorsView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request, id):
        try:
            data = BranchDetails(request).get_branch_top_supervisors_ranking(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class BranchTopSupervisorsRankingView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request, id):
        try:
            data = BranchDetails(request).get_branch_top_supervisors_ranking(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BranchSupervisorsListView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request, id):
        try:
            data = BranchDetails(request).get_branch_supervisors_list(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class BranchInactivatedAgentsListView(APIView):
    permission_classes = [AjoIsAuthenticated]
    def get(self, request, id):
        try:
            data = BranchDetails(request).get_branch_inactivated_collectors_list(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class CreateBranchView(generics.CreateAPIView):
    permission_classes = [AjoIsAuthenticated]
    serializer_class = BranchSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid(raise_exception=True):
            serializer.save()
            return Response({"data": serializer.data}, status=status.HTTP_200_OK)
        else:
            return Response({"error": "error occurred. branch not created."}, status=status.HTTP_400_BAD_REQUEST)


class AssignTerminalToCollector(generics.CreateAPIView):
    permission_classes = [AjoIsAuthenticated]
    serializer_class = ""

    def post(self, request):
        # serializer = self.serializer_class(data=request.data)

        # if serializer.is_valid(raise_exception=True):
            # terminal_id = serializer.validated_data["terminal_id"]
            # supervisor_email = serializer.validated_data["supervisor_email"]
            # agent_email = serializer.validated_data["agent_email"]

            # terminal_id_in_table = TerminalSerialTable.objects.filter(terminal_id=terminal_id).last()
            # terminal_already_assigned = User.objects.filter(terminal_id=terminal_id)

            
            # if terminal_id_in_table and not terminal_already_assigned:
            #     user = User.objects.filter(email=agent_email).last()
            #     retail_user = RetailSystem.objects.filter(user_assigned=user).last()
            #     supervisor = User.objects.filter(email=supervisor_email)
            #     if retail_user and supervisor:
            #         retail_user.user_assigned.terminal_id = terminal_id
            #         retail_user.supervisor = supervisor
            #         user.save()
            #     else:
            #         return Response({"error": "Retail account does not exist"}, status = status.HTTP_400_BAD_REQUEST)
            # else:
            #     return Response({"error": "This Terminal ID is not available"}, status = status.HTTP_400_BAD_REQUEST)            
            return Response({"message": f"Terminal assigned to user: <EMAIL>"}, status = status.HTTP_200_OK)
 

class SupervisorsDataOverView(APIView):
    permission_classes = [AjoIsAuthenticated]

    def get(self, request):
        try:
            data = SupervisorData(request=request).get_supervisor_overview_data()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class Top10SupervisorsView(APIView):
    permission_classes = [AjoIsAuthenticated]

    def get(self, request):
        try:
            data = SupervisorData(request=request).get_top10_supervisors()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SupervisorsListView(APIView):
    permission_classes = [AjoIsAuthenticated]

    def get(self, request):
        try:
            data = SupervisorData(request=request).get_supervisors_list()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class ActiveSupervisorsListView(APIView):
    permission_classes = [AjoIsAuthenticated]

    def get(self, request):
        try:
            data = SupervisorData(request=request).get_active_supervisors_list()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class InactiveSupervisorsListView(APIView):
    permission_classes = [AjoIsAuthenticated]

    def get(self, request):
        try:
            data = SupervisorData(request=request).get_inactive_supervisors_list()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SupervisorDetailView(APIView):
    permission_classes = [AjoIsAuthenticated]

    def get(self, request, id):
        try:
            data = SupervisorData(request=request).get_supervisor_details_overview(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SupervisorCollectorsListView(APIView):
    permission_classes = [AjoIsAuthenticated]

    def get(self, request, id):
        try:
            data = SupervisorData(request=request).get_supervisor_collectors_list(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SupervisorActiveCollectorsListView(APIView):
    permission_classes = [AjoIsAuthenticated]

    def get(self, request, id):
        try:
            data = SupervisorData(request=request).get_supervisor_active_collectors_list(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SupervisorInactiveCollectorsListView(APIView):
    permission_classes = [AjoIsAuthenticated]

    def get(self, request, id):
        try:
            data = SupervisorData(request=request).get_supervisor_inactive_collectors_list(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

##################################################################################################
# LOANS DASHBOARD
##################################################################################################

class LoanRepaymentView(APIView):
    permission_classes = [AjoIsAuthenticated]

    def get(self, request):
        try:
            data = LoansDashboard().get_loan_repayment_overview()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

class LoanDisbursementDataView(APIView):
    permission_classes = [AjoIsAuthenticated]

    def get(self, request):
        try:
            data = LoansDashboard().get_disbursement_data()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class LoanCollectionsDataView(APIView):
    permission_classes = [AjoIsAuthenticated]

    def get(self, request):
        try:
            data = LoansDashboard().get_collections_data()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class LoanRequestsOverView(APIView):
    permission_classes = [AjoIsAuthenticated]

    def get(self, request):
        try:
            data = LoansDashboard().get_loans_request_overview()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


# class LoanRequestsOverView(APIView):
#     permission_classes = [AjoIsAuthenticated]

#     def get(self, request):
#         try:
#             data = LoansDashboard().get_loans_request_overview()
#             return Response({"data": data}, status=status.HTTP_200_OK)
#         except Exception as e:
#             return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        
