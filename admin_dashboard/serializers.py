from rest_framework import serializers
from ajo.models import Branch



class BranchSerializer(serializers.ModelSerializer):
    class Meta:
        model = Branch
        fields = "__all__"

    def validate(self, attrs):
        self.name = attrs.get("name").lower()
        branch_names = map(lambda name: name[0].lower(), Branch.objects.values_list("name"))
        if self.name in branch_names:
            raise serializers.ValidationError("Branch with name already exists")
        
        return attrs
        