from django.contrib import admin
from admin_dashboard.models import WalletBalanceRecord
from import_export import resources
from import_export.admin import ImportExportModelAdmin



class WalletBalanceResource(resources.ModelResource):
    class Meta:
        model = WalletBalanceRecord


class WalletBalanceResourceAdmin(ImportExportModelAdmin):
    resource_class = WalletBalanceResource
    list_filter = ["actual_date", "date_created"]
    date_hierarchy = "actual_date"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(WalletBalanceRecord, WalletBalanceResourceAdmin)
