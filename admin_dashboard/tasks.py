from django.utils import timezone
from django.db.models import Su<PERSON>, Count
from django.contrib.auth import get_user_model
from django.conf import settings

from ajo.models import AjoUser, AjoSaving, Branch
from payment.models import Transaction

from admin_dashboard.helpers.helpers import (
    send_ajo_agents_inactivity_email,
    send_ajo_branch_inactivity_email,
    send_ajo_agents_daily_collection_email,
    date_utility
    )
from admin_dashboard.models import WalletBalanceRecord

from datetime import datetime, date, timedelta
from celery import shared_task

import pandas as pd
from pathlib import Path
import os

User = get_user_model()

if settings.ENVIRONMENT == "development":
    report_recipients_list = [
                        "<EMAIL>",
                        "<EMAIL>"
                        ]
else:
    report_recipients_list = [
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "joelchuk<PERSON>@gmail.com",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>"
                        ]


class DateUtility:
    def __init__(self) -> None:
        # dates and time
        filter_date = date_utility(datetime=datetime)
        self.today_date = timezone.now().date()
        current_month = self.today_date.month
        current_year = self.today_date.year
        self.month_start = date(current_year, current_month, 1)
        self.days_since_month_start = (self.today_date - self.month_start).days
        self.previous_day = timezone.now() - timedelta(days=1)
        self.week_start = filter_date.get("week_start")


@shared_task
def run_ajo_agents_activities_report():
    # Dates Initializations
    month_start = DateUtility().month_start
    days_since_month_start = DateUtility().days_since_month_start

    # queries
    # main_ajo_agents_qs = ajo_savings_qs.distinct("user")

    ajo_users_qs = AjoUser.objects.filter()
    ajo_savings_qs = AjoSaving.objects.filter()
    ajo_transactions_qs = Transaction.objects.filter(
                        plan_type="AJO",
                        wallet_type="AJO_USER",
                        status="SUCCESS"
                        )
    savings_transactions_qs = ajo_transactions_qs.filter(
                    transaction_form_type="WALLET_DEPOSIT"
                    )

    # ajo_savers_qs = 
    withdrawals_qs = Transaction.objects.filter(
                    plan_type="AJO",
                    transaction_form_type__in=[
                    "WITHDRAWAL",
                    "WALLET_WITHDRAWAL"
                    ])
    """For an Ajo User to qualify as a collector they must have made up to 7 collections"""
    collectors_based_on_collections_qs = ajo_transactions_qs.values("user"
                                        ).annotate(collections_count=Count("user")
                                        ).filter(collections_count__gte=7)
    collectors_based_collections_list = [trans["user"] for trans in collectors_based_on_collections_qs]

    ajo_agents_count = collectors_based_on_collections_qs.count()
    total_number_of_savers = ajo_users_qs.count()
    total_number_of_new_savers = ajo_users_qs.filter(
                                 created_at__gte=month_start
                                 ).count()

    total_number_of_active_agents = collectors_based_on_collections_qs.filter(
                            user__performance_status="ACTIVE"
                            ).count()
    total_number_of_inactive_agents = collectors_based_on_collections_qs.filter(
                            user__performance_status__in=["INACTIVE", "PARTIALLY_INACTIVE"]
                            ).count()
    total_number_of_partially_inactive_agents = collectors_based_on_collections_qs.filter(
                            user__performance_status="PARTIALLY_INACTIVE"
                            ).count()
    current_month_total_saving_amount = list(ajo_transactions_qs.filter(
                                date_created__gte=month_start
                                ).aggregate(Sum("amount")
                            ).values())[0]


    dataframe_list = []
    for query in ajo_users_qs.distinct("user"):
        agent = query.user
        if agent and agent.id in collectors_based_collections_list:
            # agent = ajo_custom_user_qs.get(id=agent)
            agent_collections_qs = ajo_transactions_qs.filter(
                user=agent,
                transaction_form_type="WALLET_DEPOSIT"
                )

            # Transactions Completed
            agent_transactions_count = ajo_transactions_qs.filter(user=agent).count()
            agent_withdrawals_count = withdrawals_qs.filter(user=agent).count()
            agent_savings_transactions_count = savings_transactions_qs.filter(user=agent).count()

            # Agent Info
            agent_name = agent.username
            branch = agent.branch_users.last()
            collector_id = agent.customer_user_id

            # savers metrics
            agent_total_number_of_savers = ajo_users_qs.filter(user=agent).count()
            active_savers_qs = ajo_savings_qs.filter(user=agent, is_active=True).distinct("ajo_user")
            savers_current_month_qs = ajo_users_qs.filter(
                user=agent, created_at__gte=month_start)
            agent_active_savers_count = active_savers_qs.count()
            agent_current_month_savers_count = savers_current_month_qs.count()

            # Cards Issued
            agent_cards_issued_count = ajo_users_qs.filter(user=agent, card_issued=True).count()

            # Disputes
            agent_issues_resolved = 0

            # collections amount
            agent_current_month_savings_amount = list(agent_collections_qs.filter(
                                    date_created__gte=month_start
                                    ).aggregate(Sum("amount")).values())[0]

            agent_current_month_avg_daily_collections = (
                            (agent_current_month_savings_amount if agent_current_month_savings_amount else 0.00) / 
                            (days_since_month_start if days_since_month_start else 1.00)
                            )

            # Agent Commission Amount
            agent_total_commission_amount = 0
            agent_average_daily_commission = (
                            (agent_total_commission_amount if
                            agent_total_commission_amount else 0)
                            / (days_since_month_start if
                            days_since_month_start else 1)
                            )

            # Agent Performance status
            collection_target_amount_monthly = 525000
            collection_target_amount_daily = 17500
            active_savers_target_monthly = 25
            commission_generated_target_monthly = 6000
            commission_generated_target_daily = 200

            agent_collection_vs_target_percentage = (
                            (agent_current_month_avg_daily_collections if
                            agent_current_month_avg_daily_collections else 0)
                            / collection_target_amount_daily
                            ) * 100

            if agent_collection_vs_target_percentage >= 70:
                agent.performance_status = "ACTIVE"
            elif agent_collection_vs_target_percentage > 100:
                agent.performance_status = "SUPER_PERFORMER"
            else:
                agent.performance_status = "INACTIVE"

            agent.save()

            data = {
                "Agent Name": agent_name,
                "Agent Email": agent.email,
                "Status": agent.performance_status if agent.performance_status else "",
                "Branch": branch.name if branch else "",
                "Collector Id": collector_id,
                "Total Number of Savers": agent_total_number_of_savers,
                "Number of Savers this Month": agent_current_month_savers_count,
                "Active Savers": agent_active_savers_count,
                "Transactions Completed": agent_transactions_count,
                "Withdrawals Completed": agent_withdrawals_count,
                "Savings Transactions": agent_savings_transactions_count,
                "Cards Issued": agent_cards_issued_count,
                "Issues Resolved": agent_issues_resolved,
                "Average Daily Collections Current Month": agent_current_month_avg_daily_collections \
                                                if agent_current_month_avg_daily_collections else 0.00,
                "Current Month Total Savings Amount": agent_current_month_savings_amount if agent_current_month_savings_amount \
                                                else 0.00,
                "Last Login": agent.last_login.date() if agent.last_login else "",
                "Last Inactive": "",
                "Last Collection Date": "",
                "Notes/Comments": ""
            }
            dataframe_list.append(data)

    # Prepare report sheet
    df = pd.DataFrame()
    sheet_df = df.from_dict(dataframe_list).set_index("Agent Name")

    media_file_path = os.path.join(settings.BASE_DIR, "media/ajo_agents/")

    try:
        os.mkdir(media_file_path)
    except Exception:
        pass

    file_path = Path("%sAjo Agents Activities_%s.xlsx" %(media_file_path, datetime.now().date()))

    if file_path.is_file():
        os.remove(file_path)
    else:
        pass

    ajo_agents_activities_excel = sheet_df.to_excel(
                        "%s/Ajo Agents Activities_%s.xlsx" %(media_file_path, datetime.now().date())
                        )

    with open(file_path, "rb") as excel_file:
        ajo_agents_report_sheet = excel_file.read()
        excel_file.close()

    # Send report via email
    for email in report_recipients_list:
        sned_emai=send_ajo_agents_inactivity_email(
            message="This is the Ajo agent performance report for the week",
            file=ajo_agents_report_sheet,
            file_name=f"Ajo Agents Performance Report_{datetime.now().date()}.xlsx",
            email_subject="Ajo Agents Performance Report",
            email=email,
            number_of_agents=ajo_agents_count,
            number_of_savers=total_number_of_savers,
            number_new_savers=total_number_of_new_savers,
            number_of_active_agents=total_number_of_active_agents,
            number_of_inactive_agents=total_number_of_inactive_agents,
            number_of_partially_inactive_agents=total_number_of_partially_inactive_agents,
            current_month_total_saving_amount=current_month_total_saving_amount if current_month_total_saving_amount else 0.00,
            date=datetime.now().date()
         )


@shared_task
def run_ajo_branch_activities_report():
    # Dates Initializations
    month_start = DateUtility().month_start
    days_since_month_start = DateUtility().days_since_month_start

    # queries
    ajo_users_qs = AjoUser.objects.filter()
    ajo_savings_qs = AjoSaving.objects.filter()
    ajo_transactions_qs = Transaction.objects.filter(
                            plan_type="AJO",
                            wallet_type="AJO_USER",
                            status="SUCCESS"
                        )
    # agent_savers_qs = 
    withdrawals_qs = Transaction.objects.filter(
                    transaction_form_type__in=[
                        "WITHDRAWAL",
                        "WALLET_WITHDRAWAL"
                        ])
    savings_transactions_qs = ajo_transactions_qs.filter(
                    transaction_form_type="WALLET_DEPOSIT"
                    )
    ajo_branch_qs = Branch.objects.filter()
    ajo_branches_count = ajo_branch_qs.count()

    total_number_of_active_branches = ajo_branch_qs.filter(
                            performance_status__in=[
                            "ACTIVE", "SUPER_PERFORMER", 
                            "PERFORMING"
                            ]).count()
    total_number_of_inactive_branches = ajo_branch_qs.filter(
                            performance_status__in=[
                            "INACTIVE",
                            "UNDER_PERFORMING",
                            "PARTIALLY_INACTIVE"]
                            ).count()
    total_number_of_partially_inactive_branches = ajo_branch_qs.filter(
                            performance_status="PARTIALLY_INACTIVE"
                            ).count()
    current_month_total_saving_amount = list(savings_transactions_qs.filter(
                                date_created__gte=month_start
                                ).aggregate(Sum("amount")).values())[0]


    dataframe_list = []
    for branch in ajo_branch_qs:
        branch_users = branch.users.all()
        branch_collections_qs = ajo_savings_qs.filter(
            user__in=branch_users,
            is_active=True
            )

        # Transactions Completed
        branch_transactions_qs = ajo_transactions_qs.filter(user__in=branch_users)
        branch_transactions_count = ajo_transactions_qs.filter(user__in=branch_users).count()
        branch_withdrawals_count = withdrawals_qs.filter(user__in=branch_users).count()
        branch_savings_transactions_count = savings_transactions_qs.filter(user__in=branch_users).count()

        # branch Info
        branch_name = branch.name
        # collector_id = agent.customer_user_id

        # savers metrics
        branch_total_number_of_savers = ajo_savings_qs.filter(
                        user__in=branch_users).distinct("ajo_user").count()
        active_savers_qs = ajo_savings_qs.filter(
                          user__in=branch_users,
                          is_active=True
                          ).distinct("ajo_user")
        savers_current_month_qs = ajo_savings_qs.filter(
            user__in=branch_users, created_at__gte=month_start).distinct("ajo_user")
        branch_active_savers_count = active_savers_qs.count()
        branch_current_month_savers_count = savers_current_month_qs.count()

        # Cards Issued
        branch_cards_issued_count = ajo_users_qs.filter(
                            user__in=branch_users,
                            card_issued=True
                            ).count()

        # Disputes
        branch_issues_resolved = 0

        # collections amount
        branch_total_savings_amount = list(branch_transactions_qs.aggregate(
                               Sum("amount")).values())[0]
        branch_current_month_savings_amount = list(branch_transactions_qs.filter(
                                date_created__gte=month_start
                                ).aggregate(Sum("amount")).values())[0]

        branch_current_month_avg_daily_collections = (
                        (branch_current_month_savings_amount if branch_current_month_savings_amount else 0.00) / 
                        (days_since_month_start if days_since_month_start else 1.00)
                        )

        # Branch Commission Amount
        branch_total_commission_amount = 0
        branch_average_daily_commission = (
                        (branch_total_commission_amount if
                        branch_total_commission_amount else 0)
                        / (days_since_month_start if
                        days_since_month_start else 1)
                        )

        # Branch Performance status
        branch_collection_target_amount_monthly = 525000
        branch_collection_target_amount_daily = 17500
        active_savers_target_monthly = 25
        commission_generated_target_monthly = 6000
        commission_generated_target_daily = 200

        branch_collection_vs_target_percentage = (
                        (branch_current_month_avg_daily_collections if 
                         branch_current_month_avg_daily_collections else 0)
                        / branch_collection_target_amount_daily
                        ) * 100

        if branch_collection_vs_target_percentage >= 70:
            branch.performance_status = "PERFORMING"
        elif branch_collection_vs_target_percentage > 100:
            branch.performance_status = "TOP_PERFORMER"
        else:
            branch.performance_status = "UNDER_PERFORMING"
        branch.save()


        data = {
            "Branch Name": branch_name,
            "Supervisor Email": branch.supervisor.email if branch.supervisor else "",
            # "Supervisor Phone": branch.supervisor.phone if branch.supervisor else "",
            "Status": branch.performance_status if branch.performance_status else "",
            "Total number of Agents": len(branch_users),
            "Total Number of Savers": branch_total_number_of_savers,
            "Number of Savers this Month": branch_current_month_savers_count,
            "Active Savers": branch_active_savers_count,
            "Transactions Completed": branch_transactions_count,
            "Withdrawals Completed": branch_withdrawals_count,
            "Savings Transactions": branch_savings_transactions_count,
            "Cards Issued": branch_cards_issued_count,
            "Issues Resolved": branch_issues_resolved,
            "Average Daily Collections Current Month": branch_current_month_avg_daily_collections \
                                            if branch_current_month_avg_daily_collections else 0.00,
            "Current Month Total Savings Amount": branch_current_month_savings_amount if branch_current_month_savings_amount \
                                            else 0.00,
            "Last Login": "",
            "Last Collection Date": "",
            "Notes/Comments": ""
        }
        dataframe_list.append(data)


    # Prepare report sheet
    df = pd.DataFrame()
    if ajo_branch_qs:
        sheet_df = df.from_dict(dataframe_list).set_index("Branch Name")
    else:
        sheet_df = df.from_dict(dataframe_list)

    media_file_path = os.path.join(settings.BASE_DIR, "media/ajo_branches/")

    try:
        os.mkdir(media_file_path)
    except Exception:
        pass

    file_path = Path("%sAjo Branch Activities_%s.xlsx" %(media_file_path, datetime.now().date()))

    if file_path.is_file():
        os.remove(file_path)
    else:
        pass

    ajo_agents_activities_excel = sheet_df.to_excel(
                        "%sAjo Branch Activities_%s.xlsx" %(media_file_path, datetime.now().date())
                        )
    with open(file_path, "rb") as excel_file:
        ajo_agents_report_sheet = excel_file.read()
        excel_file.close()

    # Send report via email
    for email in report_recipients_list:
        sned_emai = send_ajo_branch_inactivity_email(
            message="This is the Ajo branch performance report for the week",
            file = ajo_agents_report_sheet,
            file_name=f"Ajo branch Performance Report_{datetime.now().date()}.xlsx",
            email_subject="Ajo branch Performance Report",
            email=email,
            number_of_branches=ajo_branches_count,
            number_of_active_branch=total_number_of_active_branches,
            number_of_inactive_branch=total_number_of_inactive_branches,
            number_of_partially_inactive_branch=total_number_of_partially_inactive_branches,
            date=datetime.now().date()
         )


@shared_task
def ajo_agents_daily_activities():
    """"""
    ajo_savings_qs = AjoSaving.objects.filter()
    ajo_users_qs = AjoUser.objects.all()
    ajo_transactions_qs = Transaction.objects.filter(
                            plan_type="AJO",
                            wallet_type="AJO_USER",
                            status="SUCCESS"
                        )

    main_ajo_agents_qs = ajo_savings_qs.distinct("user")
    savings_transactions_qs = ajo_transactions_qs.filter(
                    transaction_form_type="WALLET_DEPOSIT"
                    )

    """For an Ajo User to qualify as a collector they must have made up to 7 collections"""
    collectors_based_on_collections_qs = ajo_transactions_qs.values("user"
                                        ).annotate(collections_count=Count("user")
                                        ).filter(collections_count__gte=7)
    collectors_based_collections_list = [trans["user"] for trans in collectors_based_on_collections_qs]

    agents_list = []
    for collector in ajo_users_qs.distinct("user"):
        collector = collector.user

        if collector and collector.id in collectors_based_collections_list:

            previous_day = DateUtility().previous_day
            agent_ajo_savings = savings_transactions_qs.filter(user=collector)
            ajo_savers_qs = ajo_users_qs.filter(user=collector)
            previous_day_savings = agent_ajo_savings.filter(date_created__date=previous_day.date())

            ajo_agent_previous_day_savers = ajo_savings_qs.filter(
                                    created_at__date=previous_day.date(),
                                    user=collector,
                                    is_active=True)

            collection_amount = list(previous_day_savings.aggregate(Sum("amount")).values())[0]
            # number_of_savers = ajo_agent_previous_day_savers.distinct("ajo_user").count()
            number_of_savers = ajo_savers_qs.count()

            number_of_days = (timezone.now() - collector.date_joined).days

            data = {
                "id": collector.id,
                "Collector Email": collector.email,
                # "Collector Location": collector.trade_location,
                "Collection Count": previous_day_savings.count(),
                "Collection Amount": collection_amount if collection_amount else 0.00,
                "Number of Savers": number_of_savers,
                "Number of Days Since Joined": number_of_days,
                "Date Joined": collector.date_joined.date()
                }
            agents_list.append(data)

    df = pd.DataFrame()
    data_frame = df.from_dict(agents_list).set_index("id")

    file_path = os.path.join(settings.BASE_DIR, "media/ajo_agents/")

    try:
        os.mkdir(file_path)
    except:
        pass

    daily_report_excel = data_frame.to_excel(f"{file_path}/Ajo Savers Daily Report_{datetime.now().date()}.xlsx")
    full_file_path = f"{file_path}/Ajo Savers Daily Report_{datetime.now().date()}.xlsx"

    with open(full_file_path, "rb") as excel_file:
        daily_report_sheet = excel_file.read()
        excel_file.close()

    for email in report_recipients_list:
        send_email = send_ajo_agents_daily_collection_email(
            message="This is the Ajo collectors performance report for yesterday",
            file=daily_report_sheet,
            file_name=f"Ajo Savers Daily Report_{datetime.now().date()}.xlsx",
            email_subject="Ajo Agents Daily Performance Report",
            email=email
            )
    return send_email


@shared_task
def send_simple_email_test():
    """Test that emails are sending on Ajo Production server"""
    
    report_recipients_list = ["<EMAIL>", "<EMAIL>"]
    for email in report_recipients_list:
        send_email = send_ajo_agents_daily_collection_email(
            message="Ajo Email Delivery Test!!!!",
            file="",
            file_name=f"",
            email_subject="Test Ajo email delivery system!!!",
            email=email
            )
    return send_email


@shared_task
def run_inactive_savers_check():
    month_start = DateUtility().month_start
    number_of_days_since_month_start = (timezone.now().date() - month_start).days

    ajo_savings_qs = AjoSaving.objects.filter()
    ajo_savers_qs = AjoUser.objects.filter()

    savers = ajo_savings_qs.distinct("ajo_user")

    inactive_savers_list = []
    for saver in savers:
        saver = saver.ajo_user
        
        expected_savings_amount = 500 * number_of_days_since_month_start
        current_month_savings_qs = ajo_savings_qs.filter(
                        ajo_user=saver, 
                        last_updated__gte=month_start
                        )
        current_month_amount_saved = list(
                            current_month_savings_qs.aggregate(
                            Sum("amount_saved")).values()
                            )[0]
        
        amount_saved_to_expectation_percentage = (
                                                (current_month_amount_saved if current_month_amount_saved else 0) /
                                                (expected_savings_amount if expected_savings_amount else 1)
                                                ) * 100
        
        if amount_saved_to_expectation_percentage < 40:
            data = {
                "First Name": saver.first_name,
                "Last Name": saver.last_name,
                "Phone Number": saver.phone_number,
                "status": "Inactive"
            }
            inactive_savers_list.append(data)


    # Generate and Send email
    df = pd.DataFrame()
    data_frame = df.from_dict(inactive_savers_list).set_index("First Name")
    file_path = os.path.join(settings.BASE_DIR, "media/ajo_savers/")

    try:
        os.mkdir(file_path)
    except:
        pass

    daily_report_excel = data_frame.to_excel(f"{file_path}/Ajo Inactive Savers List_{datetime.now().date()}.xlsx")
    full_file_path = f"{file_path}/Ajo Inactive Savers List_{datetime.now().date()}.xlsx"

    with open(full_file_path, "rb") as excel_file:
        inactive_savers_list = excel_file.read()
        excel_file.close()

    for email in ["<EMAIL>", "<EMAIL>"]:
        send_email = send_ajo_agents_daily_collection_email(
            message="This is the contact list of inactive Ajo savers",
            file=inactive_savers_list,
            file_name=f"Ajo Inactive Savers List_{datetime.now().date()}.xlsx",
            email_subject="Ajo Inactive Savers List",
            email=email
            )
    return send_email


@shared_task
def ajo_collectors_email_sequencing(collector=False):
    month_start = DateUtility().month_start
    number_of_days_since_month_start = (timezone.now().date() - month_start).days
    ajo_collectors = AjoUser.objects.distinct("user")

    if collector:
        # send collector welcome email
        pass
    else:
        for user in ajo_collectors:
            user = user.user
            date_joined = user.date_joined
            user_email = user.email

            if date_joined == timezone.now().date:
                "send email 1"
            if date_joined == "2 weeks ago":
                "send email 2"
            if date_joined == "3 weeks ago":
                "send email 3"


@shared_task
def wallet_balance_records():
    new_record = WalletBalanceRecord.create_balance_record()


@shared_task
def auto_create_branch_from_user_email():
    # Get all already existing branches
    existing_branches = Branch.objects.values_list("name", flat=True)
    existing_branches_lower = [branch.lower() for branch in existing_branches]

    # Collect all the branches from the collectors email
    collectors_qs = User.objects.all()
    collectors_emails = collectors_qs.values_list("email", flat=True)
    collectors_branches = set([user.split(".")[1].split("+")[0][3:].lower() for user in collectors_emails if ".ajo" in user])

    for branch in collectors_branches:
        users_list = []
        if branch.lower() not in existing_branches_lower:
            # Create branch
            new_branch, created = Branch.objects.get_or_create(
                name=branch
            )

            # Assign users to the branch
            for user in collectors_qs:
                if new_branch.name.lower() in user.email:
                    users_list.append(user.id)

            new_branch.users.set(users_list)
        else:
            existing_branch = Branch.objects.filter(name__icontains=branch).last()
            existing_users = existing_branch.users.all()

            for user in collectors_qs:
                if existing_branch.name.lower() in user.email:
                    users_list.append(user.id)
            users_list += existing_users
            existing_branch.users.set(list(set(users_list)))
