from django.db import models
from django.db.models import Sum, F
from payment.models import WalletSystem
from django.utils import timezone



class WalletBalanceRecord(models.Model):
    balance_sum = models.IntegerField(default=0)
    hold_balance_sum = models.IntegerField(default=0)
    actual_date = models.DateField(auto_now_add=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def create_balance_record(cls):
        due_balance_qs = WalletSystem.objects.filter(
                        wallet_type="AJO_PREFUNDING").annotate(
                        due_balance=F("hold_balance")-F("available_balance"))
        
        balance_record, created = cls.objects.get_or_create(
                        actual_date=timezone.now().date()
                        )
        available_balance_sum_till_now = list(WalletSystem.objects.filter(
                        wallet_type__in=["AJO_USER", "AJO_AGENT"]
                        ).aggregate(Sum("available_balance")).values())[0]
        hold_balance_sum_till_now = list(due_balance_qs.aggregate(Sum("due_balance")).values())[0]

        balance_record.balance_sum = available_balance_sum_till_now if available_balance_sum_till_now else 0.00
        balance_record.hold_balance_sum = hold_balance_sum_till_now if hold_balance_sum_till_now else 0.00
        balance_record.save()
        return balance_record