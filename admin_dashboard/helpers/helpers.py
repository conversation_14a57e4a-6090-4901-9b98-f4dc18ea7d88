from django.conf import settings
from django.utils import timezone
from string import Template
from datetime import datetime, timedelta
import requests
import os
import calendar

def send_ajo_agents_inactivity_email(
    message, file, file_name, email_subject,
    email, number_of_agents,
    number_of_savers,
    number_new_savers,
    number_of_active_agents,
    number_of_inactive_agents,
    number_of_partially_inactive_agents,
    current_month_total_saving_amount,
    date
    ):
    """For sending template emails"""

    template_dir = os.path.join(settings.BASE_DIR, "templates/ajo/report.html")

    with open(template_dir) as temp_file:
        template = temp_file.read()
        temp_file.close()

    template = Template(template).safe_substitute(
        message=message,
        email_subject=email_subject,
        number_of_inactive_agents=number_of_inactive_agents,
        number_of_agents=number_of_agents,
        number_of_savers=number_of_savers,
        number_new_savers=number_new_savers,
        number_of_active_agents=number_of_active_agents,
        number_of_partially_inactive_agents=number_of_partially_inactive_agents,
        current_month_total_saving_amount=current_month_total_saving_amount,
        date=date
        )

    data = {
        "from": "Liberty Pay <<EMAIL>>",
        "to": email,
        "subject": email_subject,
        "html": template,
    }
    message = requests.post(
            f"{settings.MAILGUN_URL}",
            auth=("api", f"{settings.MAILGUN_API_KEY}"),
            data = data,
            files=[(f"attachment", (f"{file_name}", file))],
        )

    return message.text


def send_ajo_branch_inactivity_email(
    message, file, file_name, email_subject,
    email, number_of_branches,
    number_of_active_branch,
    number_of_inactive_branch,
    number_of_partially_inactive_branch,
    date
    ):
    """For sending template emails"""

    template_dir = os.path.join(settings.BASE_DIR, "templates/ajo/branch_report.html")

    with open(template_dir) as temp_file:
        template = temp_file.read()
        temp_file.close()

    template = Template(template).safe_substitute(
        message=message,
        email_subject=email_subject,
        file = file,
        file_name = file_name,
        email=email,
        number_of_branches = number_of_branches,
        number_of_active_branch = number_of_active_branch,
        number_of_inactive_branch = number_of_inactive_branch,
        number_of_partially_inactive_branch = number_of_partially_inactive_branch,
        date = date
    )

    data = {
        "from": "Liberty Pay <<EMAIL>>",
        "to": email,
        "subject": email_subject,
        "html": template,
    }
    message = requests.post(
            f"{settings.MAILGUN_URL}",
            auth=("api", f"{settings.MAILGUN_API_KEY}"),
            data = data,
            files=[(f"attachment", (f"{file_name}", file))],
        )

    return message.text


def send_ajo_agents_daily_collection_email(
    message, file, file_name, 
    email_subject, email
    ):
    """For sending template emails"""

    template_dir = os.path.join(settings.BASE_DIR, "templates/ajo/collectors_daily_activities.html")

    with open(template_dir) as temp_file:
        template = temp_file.read()
        temp_file.close()

    template = Template(template).safe_substitute(
        message=message,
        email_subject=email_subject,
        file=file,
        file_name=file_name,
        email=email
        )

    data = {
        "from": "Liberty Pay <<EMAIL>>",
        "to": email,
        "subject": email_subject,
        "html": template,
    }
    message = requests.post(
            f"{settings.MAILGUN_URL}",
            auth=("api", f"{settings.MAILGUN_API_KEY}"),
            data = data,
            files=[(f"attachment", (f"{file_name}", file))],
        )

    return message.text


def date_utility(datetime):
    start_of_all_agents = datetime(2021, 1, 1)
    previous_day = timezone.now() - timedelta(days=1)
    date_today = timezone.now()

    if date_today.month > 1:
        previous_month_start = datetime(date_today.year, date_today.month-1, 1)
    else:
        previous_month_start = datetime(date_today.year, 12, 1)
    current_month_start = datetime(date_today.year, date_today.month, 1)
    previous_month_end = current_month_start + timedelta(days=-1)

    current_date = timezone.now().date()
    month_start = datetime(current_date.year, current_date.month, 1)
    year_start = datetime(current_date.year, 1, 1)
    year_end = datetime(current_date.year, 12, 31)

    week_start = date_today - timedelta(days=current_date.weekday())
    previous_week_end = week_start - timedelta(days=1)
    previous_week_start = previous_week_end - timedelta(days=previous_week_end.weekday())

    previous_year_start = datetime(date_today.year-1, 1, 1)
    previous_year_end = datetime(date_today.year-1, 12, 31)

    first_day, last_day = calendar.monthrange(date_today.year-1, date_today.month)
    previous_year_current_month_start = datetime(date_today.year-1, current_date.month, 1)
    previous_year_current_month_end = datetime(date_today.year-1, current_date.month, last_day)
    previous_year_current_following_month = current_date.month + 1
    date_from = datetime.now() - timedelta(days=1)
    month_ago = timezone.now() - timedelta(days=30)
    datetime_today_6am = datetime(date_today.year, date_today.month, date_today.day, 6, 0)
    previous_day_six_am = datetime_today_6am - timedelta(days=1)
    midnight_time = datetime(current_date.year, current_date.month, current_date.day)
    day_ago = timezone.now() - timedelta(hours=24)


    data = {
        "start_of_all_agents": start_of_all_agents,
        "start_of_all_transactions": start_of_all_agents,
        "previous_day": previous_day,
        "today": date_today + timedelta(days=1),
        "previous_month_end": previous_month_end,
        "previous_month_start": previous_month_start,
        "init_start": start_of_all_agents,
        "month_start": month_start,
        "year_start": year_start,
        "year_end": year_end,
        "week_start": week_start.date(),
        "previous_week_start": previous_week_start.date(),
        "previous_week_end": previous_week_end.date(),
        "previous_year_start": previous_year_start,
        "previous_year_end": previous_year_end,
        "previous_year_current_month_start": previous_year_current_month_start,
        "previous_year_current_month_end": previous_year_current_month_end,
        "previous_year_current_following_month": previous_year_current_following_month,
        "date_from": date_from,
        "month_ago": month_ago,
        "date_today": date_today,
        "previous_day_six_am": previous_day_six_am,
        "datetime_today_6am": datetime_today_6am,
        "midnight_time": midnight_time,
        "day_ago": day_ago,
        "current_date": current_date
    }
    return data

def get_percentage_diff(previous, current):
    try:
        percentage = abs(previous - current)/max(previous, current) * 100
    except ZeroDivisionError:
        percentage = float('inf')
        percentage = 0
    except TypeError:
        percentage = 0

    try:

        if current > previous:
            change = "up"
        elif previous > current:
            change = "down"
        else:
            change = "no change"

        return {
            "percentage": percentage,
            "change": change
        }
    except:
        return {
            "percentage": 0,
            "change": "none"
        }
    

def date_utility(datetime):
    start_of_all_agents = datetime(2021, 1, 1)

    previous_day = timezone.now() - timedelta(days=1)
    date_today = timezone.now()

    if date_today.month > 1:
        previous_month_start = datetime(date_today.year, date_today.month-1, 1)
    else:
        previous_month_start = datetime(date_today.year, 12, 1)
    current_month_start = datetime(date_today.year, date_today.month, 1)
    previous_month_end = current_month_start + timedelta(days=-1)

    current_date = timezone.now().date()
    month_start = datetime(current_date.year, current_date.month, 1)
    year_start = datetime(current_date.year, 1, 1)
    year_end = datetime(current_date.year, 12, 31)

    week_start = date_today - timedelta(days=current_date.weekday())
    previous_week_end = week_start - timedelta(days=1)
    previous_week_start = previous_week_end - timedelta(days=previous_week_end.weekday())

    previous_year_start = datetime(date_today.year-1, 1, 1)
    previous_year_end = datetime(date_today.year-1, 12, 31)

    first_day, last_day = calendar.monthrange(date_today.year-1, date_today.month)
    previous_year_current_month_start = datetime(date_today.year-1, current_date.month, 1)
    previous_year_current_month_end = datetime(date_today.year-1, current_date.month, last_day)
    previous_year_current_following_month = current_date.month + 1
    date_from = datetime.now() - timedelta(days=1)
    month_ago = timezone.now() - timedelta(days=30)
    datetime_today_6am = datetime(date_today.year, date_today.month, date_today.day, 6, 0)
    previous_day_six_am = datetime_today_6am - timedelta(days=1)
    midnight_time = datetime(current_date.year, current_date.month, current_date.day)
    day_ago = timezone.now() - timedelta(hours=24)


    data = {
        "start_of_all_agents": start_of_all_agents,
        "start_of_all_transactions": start_of_all_agents,
        "previous_day": previous_day,
        "today": date_today,
        "previous_month_end": previous_month_end,
        "previous_month_start": previous_month_start,
        "init_start": start_of_all_agents,
        "month_start": month_start,
        "year_start": year_start,
        "year_end": year_end,
        "week_start": week_start.date(),
        "previous_week_start": previous_week_start.date(),
        "previous_week_end": previous_week_end.date(),
        "previous_year_start": previous_year_start,
        "previous_year_end": previous_year_end,
        "previous_year_current_month_start": previous_year_current_month_start,
        "previous_year_current_month_end": previous_year_current_month_end,
        "previous_year_current_following_month": previous_year_current_following_month,
        "date_from": date_from,
        "month_ago": month_ago,
        "date_today": date_today,
        "previous_day_six_am": previous_day_six_am,
        "datetime_today_6am": datetime_today_6am,
        "midnight_time": midnight_time,
        "day_ago": day_ago,
        "current_date": current_date
    }

    return data


def get_date_filter(filter_value):
    """Will always return a date filter"""

    date_util = date_utility(datetime=datetime)

    if filter_value is not None:
        if filter_value == "today":
            date_filter = {"created_at__date": timezone.now().date()}
            disburse_filter = {"date_disbursed__date": timezone.now().date()}
        elif filter_value == "this_week":
            date_filter = {"created_at__gte": date_util.get("week_start")}
            disburse_filter = {"date_disbursed__gte": date_util.get("week_start")}
        elif filter_value == "last_week":
            date_filter = {"created_at__gte": date_util.get("previous_week_start"), 
            "created_at__lte": date_util.get("previous_week_end")}
            disburse_filter = {"date_disbursed__gte": date_util.get("previous_week_start"), 
            "date_disbursed__lte": date_util.get("previous_week_end")}
        elif filter_value == "this_month":
            date_filter = {"created_at__gte": date_util.get("month_start")}
            disburse_filter = {"date_disbursed__gte": date_util.get("month_start")}
        elif filter_value == "last_month":
            date_filter = {"created_at__gte": date_util.get("previous_month_start"), 
            "created_at__lte": date_util.get("previous_month_end")}
            disburse_filter = {"date_disbursed__gte": date_util.get("previous_month_start"), 
            "date_disbursed__lte": date_util.get("previous_month_end")}
        elif filter_value == "this_year":
            date_filter = {"created_at__gte": date_util.get("year_start")}
            disburse_filter = {"date_disbursed__gte": date_util.get("year_start")}
        elif filter_value == "last_year":
            date_filter = {"created_at__gte": date_util.get("previous_year_start"), 
            "created_at__lte": date_util.get("previous_year_end")}
            disburse_filter = {"date_disbursed__gte": date_util.get("previous_year_start"), 
            "date_disbursed__lte": date_util.get("previous_year_end")}
        elif filter_value == "yesterday":
            date_filter = {"created_at__date": date_util.get("previous_day").date()}
            disburse_filter = {"date_disbursed__date": date_util.get("previous_day").date()}
        else:
            date_filter = {"created_at__gte": date_util.get("start_of_all_transactions")}
            disburse_filter = {"date_disbursed__gte": date_util.get("start_of_all_transactions")}
    else:
        date_filter = {"created_at__gte": date_util.get("start_of_all_transactions")}
        disburse_filter = {"date_disbursed__gte": date_util.get("start_of_all_transactions")}
    return date_filter, disburse_filter
