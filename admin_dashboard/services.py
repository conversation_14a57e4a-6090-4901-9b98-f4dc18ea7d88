from django.utils import timezone
from django.db.models import Sum, F, Count, Avg, Q
from django.contrib.auth import get_user_model
from django.contrib.sessions.models import Session
from django.core.cache import cache
from payment.models import Transaction, WalletSystem, Commission
from admin_dashboard.helpers.helpers import date_utility, get_percentage_diff
from admin_dashboard.models import WalletBalanceRecord
from ajo.models import (
    Branch as AjoBranch,
    BankAccountDetails,
    Supervisor,
    RotationGroupMember,
)
from ajo.models import AjoSaving, AjoUser
from datetime import datetime, timedelta
from math import floor
import calendar


all_possible_transactions_list = [""]
ajo_savings_wallets_list = [
    "AJO_USER",
    "AJO_AGENT",
    "AJO_SPENDING",
    "AJO_DIGITAL",
    "AJO_PREFUNDING",
    "ROSCA_AJO_USER",
]
customer_wallet_list = ["AJO_USER", "AJO_SPENDING", "AJO_DIGITAL", "ROSCA_AJO_USER"]


class DateUtility:
    def __init__(self):
        filter_date = date_utility(datetime=datetime)
        self.previous_month_start = filter_date.get("previous_month_start")
        self.previous_month_end = filter_date.get("previous_month_end")
        self.previous_year_current_month_start = filter_date.get(
            "previous_year_current_month_start"
        )
        self.previous_year_current_month_end = filter_date.get(
            "previous_year_current_month_end"
        )
        self.previous_year_current_previous_month = filter_date.get(
            "previous_year_current_previous_month"
        )
        self.year_end = filter_date.get("year_end")
        self.year_start = filter_date.get("year_start")
        self.init_start = filter_date.get("init_start")
        self.previous_year_current_following_month = filter_date.get(
            "previous_year_current_following_month"
        )
        self.start_of_all_transactions = filter_date.get("start_of_all_transactions")
        self.month_start = filter_date.get("month_start")
        self.today = filter_date.get("today")
        self.previous_day = filter_date.get("previous_day")
        self.previous_year_end = filter_date.get("previous_year_end")
        self.previous_year_start = filter_date.get("previous_year_start")
        self.week_start = filter_date.get("week_start")
        self.date_today = filter_date.get("date_today")
        self.month_start = filter_date.get("month_start")
        self.month_ago = filter_date.get("month_ago")
        self.date_today_date = filter_date.get("date_today_date")
        self.week_start = filter_date.get("week_start")
        self.month_start = filter_date.get("month_start")
        self.midnight_time = filter_date.get("midnight_time")
        self.day_ago = filter_date.get("day_ago")
        self.previous_week_start = filter_date.get("previous_week_start")
        self.previous_week_end = filter_date.get("previous_week_end")
        self.current_date = filter_date.get("current_date")

        # Create an array of the month keys
        months_list1 = list(
            range(
                self.previous_year_current_month_start.month,
                (
                    self.previous_year_current_month_start.month
                    + 13
                    - self.previous_year_current_month_start.month
                ),
            )
        )
        months_list2 = list(
            range(1, 12 - (12 - self.previous_year_current_month_start.month) + 1)
        )
        months_list_names = [
            f"{calendar.month_name[month][:3]} {self.previous_year_current_month_start.year}"
            for month in months_list1
        ]
        months_list2_names = [
            f"{calendar.month_name[month][:3]} {self.today.year}"
            for month in months_list2
        ]
        self.months_list_names = months_list_names + months_list2_names
        months_list = months_list1 + months_list2
        self.year_months_tuple_list = [
            (self.previous_year_current_month_start.year, month)
            for month in months_list1
        ] + [(self.today.year, month) for month in months_list2]

        # days list for day labels
        last_week_days = [
            (calendar.day_name[day])[:3]
            for day in range(self.current_date.weekday(), 7)
        ]
        current_week_days = [
            (calendar.day_name[day])[:3]
            for day in range(self.current_date.weekday() + 1)
        ]
        self.days_label = last_week_days + current_week_days

        # Create an array of dates starting from exactly 1 month ago
        if self.current_date.month > 1:
            if self.current_date.month == 3 and self.current_date.day > 28:
                self._previous_month_current_month_date = self.current_date.replace(
                    month=self.current_date.month - 1,
                    day=calendar.monthrange(
                        self.current_date.year, self.current_date.month - 1
                    )[1],
                )
            else:
                self._previous_month_current_month_date = self.current_date.replace(
                    month=self.current_date.month - 1,
                    day=calendar.monthrange(
                        self.current_date.year, self.current_date.month - 1
                    )[1],
                )
            _last_month_name = calendar.month_name[
                (
                    self.current_date.replace(
                        month=self.current_date.month - 1,
                        day=calendar.monthrange(
                            self.current_date.year, self.current_date.month - 1
                        )[1],
                    )
                ).month
            ][:3]
        else:
            self._previous_month_current_month_date = self.current_date.replace(
                year=self.current_date.year - 1, month=12
            )
            _last_month_name = calendar.month_name[
                (
                    self.current_date.replace(
                        month=12,
                        day=calendar.monthrange(self.current_date.year - 1, 12)[1],
                    )
                ).month
            ][:3]

        self._previous_week_current_week_date = self.current_date - timedelta(days=7)
        _current_month_name = calendar.month_name[self.current_date.month][:3]
        _current_month_day_date = self.current_date.day
        if self.current_date.month > 1:
            _last_month_num_days = calendar.monthrange(
                self.current_date.year, self.current_date.month - 1
            )[1]
        else:
            _last_month_num_days = calendar.monthrange(self.current_date.year - 1, 12)[
                1
            ]
        if _current_month_day_date == 31:
            _last_month_dates = [
                f"{_last_month_name} {day}"
                for day in range(30, _last_month_num_days + 1)
            ]
        else:
            _last_month_dates = [
                f"{_last_month_name} {day}"
                for day in range(_current_month_day_date, _last_month_num_days + 1)
            ]
        _current_month_date = [
            f"{_current_month_name} {day}"
            for day in range(1, _current_month_day_date + 1)
        ]
        self._month_2_month_dates_list = _last_month_dates + _current_month_date

    def get_date_filter(self, request):
        """Will always return a date filter"""

        filter_value = request.GET.get("filter")
        if filter_value is not None:
            if filter_value == "today":
                self.date_filter = {"date_created__date": timezone.now().date()}
            elif filter_value == "this_week":
                self.date_filter = {"date_created__gte": self.week_start}
            elif filter_value == "last_week":
                self.date_filter = {
                    "date_created__gte": self.previous_week_start,
                    "date_created__lte": self.previous_week_end,
                }
            elif filter_value == "this_month":
                self.date_filter = {"date_created__gte": self.month_start}
            elif filter_value == "last_month":
                self.date_filter = {
                    "date_created__gte": self.previous_month_start,
                    "date_created__lte": self.previous_month_end,
                }
            elif filter_value == "this_year":
                self.date_filter = {"date_created__gte": self.year_start}
            elif filter_value == "last_year":
                self.date_filter = {
                    "date_created__gte": self.previous_year_start,
                    "date_created__lte": self.previous_year_end,
                }
            elif filter_value == "yesterday":
                self.date_filter = {"date_created__date": self.previous_day.date()}
            else:
                self.date_filter = {"date_created__gte": self.start_of_all_transactions}
        else:
            self.date_filter = {"date_created__gte": self.start_of_all_transactions}
        return self.date_filter


class MainDashboard:
    def __init__(self, request):
        # Initialize dates
        self.previous_day = DateUtility().previous_day
        self.previous_week_end = DateUtility().previous_week_end
        self.previous_month_end = DateUtility().previous_month_end
        self.previous_year_end = DateUtility().previous_year_end
        self.week_start = DateUtility().week_start
        self.month_start = DateUtility().month_start
        self.year_start = DateUtility().year_start
        self.previous_year_start = DateUtility().previous_year_start
        self.previous_week_start = DateUtility().previous_week_start
        self.previous_month_start = DateUtility().previous_month_start
        self.midnight_time = DateUtility().midnight_time

        self.transactions_qs = Transaction.objects.filter(plan_type="AJO")
        self.successful_transactions_qs = self.transactions_qs.filter(
            Q(status="SUCCESS") & ~Q(transaction_form_type="REVERSAL")
        )
        self.wallet_system_qs = WalletSystem.objects.filter(
            wallet_type__in=ajo_savings_wallets_list
        )
        self.wallet_balance_qs = WalletBalanceRecord.objects.filter()
        self.branch_qs = AjoBranch.objects.all()
        self.commission_qs = Commission.objects.filter(plan_type="AJO")

        # number of days since start of Ajo
        if self.successful_transactions_qs:
            self.numbers_day_since_ajo_start = (
                timezone.now().date()
                - self.successful_transactions_qs.first().date_created.date()
            ).days
            self.numbers_day_since_ajo_start_less_weekend = (
                self.numbers_day_since_ajo_start
                - floor(self.numbers_day_since_ajo_start / 7)
            )
        else:
            self.numbers_day_since_ajo_start_less_weekend = 60

        self.savings_transactions_qs = self.successful_transactions_qs.filter(
            plan_type="AJO", wallet_type="AJO_USER", status="SUCCESS"
        )

    def get_transactions_overview_one(self):
        # Transaction Amount
        total_transactions_amount = list(
            self.successful_transactions_qs.aggregate(Sum("amount")).values()
        )[0]
        total_transactions_amount_today = list(
            self.successful_transactions_qs.filter(
                date_created__date=timezone.now().date()
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_transactions_amount_yesterday = list(
            self.successful_transactions_qs.filter(
                date_created__date=self.previous_day.date()
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_transactions_amount_this_week = list(
            self.successful_transactions_qs.filter(date_created__gte=self.week_start)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_transactions_amount_this_month = list(
            self.successful_transactions_qs.filter(date_created__gte=self.month_start)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_transactions_amount_this_year = list(
            self.successful_transactions_qs.filter(date_created__gte=self.year_start)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_transactions_amount_last_year = list(
            self.successful_transactions_qs.filter(
                date_created__gte=self.previous_year_start,
                date_created__lte=self.previous_year_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_transactions_amount_last_week = list(
            self.successful_transactions_qs.filter(
                date_created__gte=self.previous_week_start,
                date_created__lte=self.previous_week_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_transactions_amount_last_month = list(
            self.successful_transactions_qs.filter(
                date_created__gte=self.previous_month_start,
                date_created__lte=self.previous_month_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        # Wallet Balance
        """Available balance for Ajo Savers and Ajo Collectors"""
        wallet_balance = list(
            self.wallet_system_qs.aggregate(Sum("available_balance")).values()
        )[0]
        wallet_balance_12_am = self.wallet_balance_qs.filter(
            date_created__lte=self.midnight_time
        ).last()
        wallet_balance_today = self.wallet_balance_qs.filter(
            date_created__lt=timezone.now().date()
        ).last()
        wallet_balance_yesterday = self.wallet_balance_qs.filter(
            date_created__lte=self.previous_day
        ).last()
        wallet_balance_this_week = self.wallet_balance_qs.filter(
            date_created__lte=self.week_start
        ).last()
        wallet_balance_this_month = self.wallet_balance_qs.filter(
            date_created__lte=self.month_start
        ).last()
        wallet_balance_this_year = self.wallet_balance_qs.filter(
            date_created__lte=self.year_start
        ).last()
        wallet_balance_last_year = self.wallet_balance_qs.filter(
            date_created__gte=self.previous_year_start,
            date_created__lte=self.previous_year_end,
        ).last()
        wallet_balance_last_week = self.wallet_balance_qs.filter(
            date_created__gte=self.previous_week_start,
            date_created__lte=self.previous_week_end,
        ).last()
        wallet_balance_last_month = self.wallet_balance_qs.filter(
            date_created__gte=self.previous_month_start,
            date_created__lte=self.previous_month_end,
        ).last()

        # PREFUND BALANCE
        """This is the sum of the amount collectors have left from the prefundung done by the companies
        to collectors."""
        # prefunding_qs = self.successful_transactions_qs.filter(transaction_form_type="PREFUNDING_DEPOSIT")
        prefunding_wallet_qs = WalletSystem.objects.filter(wallet_type="AJO_PREFUNDING")
        total_prefunding_balance_amount = list(
            prefunding_wallet_qs.aggregate(Sum("available_balance")).values()
        )[0]
        total_prefunding_balance_amount_today = list(
            prefunding_wallet_qs.filter(date_created__date=timezone.now().date())
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        total_prefunding_balance_amount_yesterday = list(
            prefunding_wallet_qs.filter(date_created__date=self.previous_day.date())
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        total_prefunding_balance_amount_this_week = list(
            prefunding_wallet_qs.filter(date_created__gte=self.week_start)
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        total_prefunding_balance_amount_this_month = list(
            prefunding_wallet_qs.filter(date_created__gte=self.month_start)
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        total_prefunding_balance_amount_this_year = list(
            prefunding_wallet_qs.filter(date_created__gte=self.year_start)
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        total_prefunding_balance_amount_last_year = list(
            prefunding_wallet_qs.filter(
                date_created__gte=self.previous_year_start,
                date_created__lte=self.previous_year_end,
            )
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        total_prefunding_balance_amount_last_week = list(
            prefunding_wallet_qs.filter(
                date_created__gte=self.previous_week_start,
                date_created__lte=self.previous_week_end,
            )
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        total_prefunding_balance_amount_last_month = list(
            prefunding_wallet_qs.filter(
                date_created__gte=self.previous_month_start,
                date_created__lte=self.previous_month_end,
            )
            .aggregate(Sum("available_balance"))
            .values()
        )[0]

        # DUE BALANCE
        """How much of the pre-funding amount a collector may have used
        and is due for refunds to the company.
        """
        # due_balance_qs = self.wallet_system_qs.filter(wallet_type="AJO_AGENT")
        due_balance_qs = WalletSystem.objects.filter(
            wallet_type="AJO_PREFUNDING"
        ).annotate(due_balance=F("hold_balance") - F("available_balance"))
        total_due_balance_amount = list(
            due_balance_qs.aggregate(Sum("due_balance")).values()
        )[0]
        total_due_balance_amount_today = list(
            due_balance_qs.filter(date_created__date=timezone.now().date())
            .aggregate(Sum("due_balance"))
            .values()
        )[0]
        total_due_balance_amount_yesterday = list(
            due_balance_qs.filter(date_created__date=self.previous_day.date())
            .aggregate(Sum("due_balance"))
            .values()
        )[0]
        total_due_balance_amount_this_week = list(
            due_balance_qs.filter(date_created__gte=self.week_start)
            .aggregate(Sum("due_balance"))
            .values()
        )[0]
        total_due_balance_amount_this_month = list(
            due_balance_qs.filter(date_created__gte=self.month_start)
            .aggregate(Sum("due_balance"))
            .values()
        )[0]
        total_due_balance_amount_this_year = list(
            due_balance_qs.filter(date_created__gte=self.year_start)
            .aggregate(Sum("due_balance"))
            .values()
        )[0]
        total_due_balance_amount_last_year = list(
            due_balance_qs.filter(
                date_created__gte=self.previous_year_start,
                date_created__lte=self.previous_year_end,
            )
            .aggregate(Sum("due_balance"))
            .values()
        )[0]
        total_due_balance_amount_last_week = list(
            due_balance_qs.filter(
                date_created__gte=self.previous_week_start,
                date_created__lte=self.previous_week_end,
            )
            .aggregate(Sum("due_balance"))
            .values()
        )[0]
        total_due_balance_amount_last_month = list(
            due_balance_qs.filter(
                date_created__gte=self.previous_month_start,
                date_created__lte=self.previous_month_end,
            )
            .aggregate(Sum("due_balance"))
            .values()
        )[0]

        # Cashout Amount
        cashout_qs = self.successful_transactions_qs.filter(
            transaction_form_type="CASHOUT"
        )
        total_cashout_amount = list(cashout_qs.aggregate(Sum("amount")).values())[0]
        total_cashout_amount_today = list(
            cashout_qs.filter(date_created__date=timezone.now().date())
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_cashout_amount_yesterday = list(
            cashout_qs.filter(date_created__date=self.previous_day.date())
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_cashout_amount_this_week = list(
            cashout_qs.filter(date_created__gte=self.week_start)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_cashout_amount_this_month = list(
            cashout_qs.filter(date_created__gte=self.month_start)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_cashout_amount_this_year = list(
            cashout_qs.filter(date_created__gte=self.year_start)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_cashout_amount_last_year = list(
            cashout_qs.filter(
                date_created__gte=self.previous_year_start,
                date_created__lte=self.previous_year_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_cashout_amount_last_week = list(
            cashout_qs.filter(
                date_created__gte=self.previous_week_start,
                date_created__lte=self.previous_week_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_cashout_amount_last_month = list(
            cashout_qs.filter(
                date_created__gte=self.previous_month_start,
                date_created__lte=self.previous_month_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        # Cashout Count
        cashout_qs = self.successful_transactions_qs.filter(
            transaction_form_type="CASHOUT"
        )
        cashout_count = list(cashout_qs.aggregate(Count("amount")).values())[0]
        cashout_count_today = list(
            cashout_qs.filter(date_created__date=timezone.now().date())
            .aggregate(Count("amount"))
            .values()
        )[0]
        cashout_count_yesterday = list(
            cashout_qs.filter(date_created__date=self.previous_day.date())
            .aggregate(Count("amount"))
            .values()
        )[0]
        cashout_count_this_week = list(
            cashout_qs.filter(date_created__gte=self.week_start)
            .aggregate(Count("amount"))
            .values()
        )[0]
        cashout_count_this_month = list(
            cashout_qs.filter(date_created__gte=self.month_start)
            .aggregate(Count("amount"))
            .values()
        )[0]
        cashout_count_this_year = list(
            cashout_qs.filter(date_created__gte=self.year_start)
            .aggregate(Count("amount"))
            .values()
        )[0]
        cashout_count_last_year = list(
            cashout_qs.filter(
                date_created__gte=self.previous_year_start,
                date_created__lte=self.previous_year_end,
            )
            .aggregate(Count("amount"))
            .values()
        )[0]
        cashout_count_last_week = list(
            cashout_qs.filter(
                date_created__gte=self.previous_week_start,
                date_created__lte=self.previous_week_end,
            )
            .aggregate(Count("amount"))
            .values()
        )[0]
        cashout_count_last_month = list(
            cashout_qs.filter(
                date_created__gte=self.previous_month_start,
                date_created__lte=self.previous_month_end,
            )
            .aggregate(Count("amount"))
            .values()
        )[0]

        data = {
            "overall_transactions": {
                "total_amount": total_transactions_amount or 0.00,
                "amount_today": total_transactions_amount_today or 0.00,
                "amount_this_week": total_transactions_amount_this_week or 0.00,
                "amount_this_month": total_transactions_amount_this_month or 0.00,
                "amount_last_month": total_transactions_amount_last_month or 0.00,
                "amount_yesterday": total_transactions_amount_yesterday or 0.00,
                "amount_last_week": total_transactions_amount_last_week or 0.00,
                "amount_this_year": total_transactions_amount_this_year or 0.00,
                "amount_last_year": total_transactions_amount_last_year or 0.00,
            },
            "wallet_balance": {
                "overall_amount": wallet_balance or 0.00,
                "12am_balance": (
                    (wallet_balance_12_am.balance_sum or 0.00)
                    if wallet_balance_12_am
                    else 0.00
                ),
                "current_balance": wallet_balance or 0.00,
                "amount_today": (
                    (wallet_balance_yesterday.balance_sum or 0.00)
                    if wallet_balance_yesterday
                    else 0.00
                ),
                "amount_this_week": (
                    (wallet_balance_this_week.balance_sum or 0.00)
                    if wallet_balance_this_week
                    else 0.00
                ),
                "amount_this_month": (
                    (wallet_balance_this_month.balance_sum or 0.00)
                    if wallet_balance_this_month
                    else 0.00
                ),
                "amount_last_month": (
                    (wallet_balance_last_month.balance_sum or 0.00)
                    if wallet_balance_last_month
                    else 0.00
                ),
                "amount_yesterday": (
                    (wallet_balance_yesterday.balance_sum or 0.00)
                    if wallet_balance_yesterday
                    else 0.00
                ),
                "amount_last_week": (
                    (wallet_balance_last_week.balance_sum or 0.00)
                    if wallet_balance_last_week
                    else 0.00
                ),
                "amount_this_year": (
                    (wallet_balance_this_year.balance_sum or 0.00)
                    if wallet_balance_this_year
                    else 0.00
                ),
                "amount_last_year": (
                    (wallet_balance_last_year.balance_sum or 0.00)
                    if wallet_balance_last_year
                    else 0.00
                ),
            },
            "prefund_balance": {
                "overall_amount": total_prefunding_balance_amount or 0.00,
                "amount_today": total_prefunding_balance_amount_today or 0.00,
                "amount_this_week": total_prefunding_balance_amount_this_week or 0.00,
                "amount_this_month": total_prefunding_balance_amount_this_month or 0.00,
                "amount_last_month": total_prefunding_balance_amount_last_month or 0.00,
                "amount_yesterday": total_prefunding_balance_amount_yesterday or 0.00,
                "amount_last_week": total_prefunding_balance_amount_last_week or 0.00,
                "amount_this_year": total_prefunding_balance_amount_this_year or 0.00,
                "amount_last_year": total_prefunding_balance_amount_last_year or 0.00,
            },
            "due_balance": {
                "overall_amount": total_due_balance_amount or 0.00,
                "amount_today": total_due_balance_amount_today or 0.00,
                "amount_this_week": total_due_balance_amount_this_week or 0.00,
                "amount_this_month": total_due_balance_amount_this_month or 0.00,
                "amount_last_month": total_due_balance_amount_last_month or 0.00,
                "amount_yesterday": total_due_balance_amount_yesterday or 0.00,
                "amount_last_week": total_due_balance_amount_last_week or 0.00,
                "amount_this_year": total_due_balance_amount_this_year or 0.00,
                "amount_last_year": total_due_balance_amount_last_year or 0.00,
            },
            "total_cashout": {
                "overall_amount": total_cashout_amount or 0.00,
                "amount_today": total_cashout_amount_today or 0.00,
                "amount_this_week": total_cashout_amount_this_week or 0.00,
                "amount_this_month": total_cashout_amount_this_month or 0.00,
                "amount_last_month": total_cashout_amount_last_month or 0.00,
                "amount_yesterday": total_cashout_amount_yesterday or 0.00,
                "amount_last_week": total_cashout_amount_last_week or 0.00,
                "amount_this_year": total_cashout_amount_this_year or 0.00,
                "amount_last_year": total_cashout_amount_last_year or 0.00,
            },
            "cashout_count": {
                "overall_count": cashout_count,
                "percentage": 0,
                "change": "up",
                "count_today": cashout_count_today,
                "count_this_week": cashout_count_this_week,
                "count_this_month": cashout_count_this_month,
                "count_last_month": cashout_count_last_month,
                "count_yesterday": cashout_count_yesterday,
                "count_last_week": cashout_count_last_week,
                "count_this_year": cashout_count_this_year,
                "count_last_year": cashout_count_last_year,
            },
        }
        return data

    def get_transactions_overview_two(self):
        # Commission
        collector_commission_qs = self.commission_qs.filter(commission_type="AGENT")
        collector_commission_amount = list(
            collector_commission_qs.aggregate(Sum("amount")).values()
        )[0]

        collector_commission_amount_today = list(
            collector_commission_qs.filter(created_at__date=timezone.now().date())
            .aggregate(Sum("amount"))
            .values()
        )[0]

        collector_commission_amount_this_week = list(
            collector_commission_qs.filter(created_at__gte=self.week_start)
            .aggregate(Sum("amount"))
            .values()
        )[0]

        collector_commission_amount_this_month = list(
            collector_commission_qs.filter(created_at__gte=self.month_start)
            .aggregate(Sum("amount"))
            .values()
        )[0]

        collector_commission_amount_last_month = list(
            collector_commission_qs.filter(
                created_at__gte=self.previous_month_start,
                created_at__lte=self.previous_month_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        collector_commission_amount_yesterday = list(
            collector_commission_qs.filter(created_at__date=self.previous_day.date())
            .aggregate(Sum("amount"))
            .values()
        )[0]

        collector_commission_amount_last_week = list(
            collector_commission_qs.filter(
                created_at__gte=self.previous_week_start,
                created_at__lte=self.previous_week_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        collector_commission_amount_this_year = list(
            collector_commission_qs.filter(created_at__gte=self.year_start)
            .aggregate(Sum("amount"))
            .values()
        )[0]

        collector_commission_amount_last_year = list(
            collector_commission_qs.filter(
                created_at__gte=self.previous_year_start,
                created_at__lte=self.previous_year_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]
        # collectors must have collected up to 7 X
        # Customer Wallet Balance
        customer_wallet_qs = self.wallet_system_qs.filter(
            wallet_type__in=customer_wallet_list
        )
        customer_wallet_balance = list(
            customer_wallet_qs.aggregate(Sum("available_balance")).values()
        )[0]
        customer_wallet_balance_12_am = list(
            customer_wallet_qs.filter(date_created__lte=self.midnight_time)
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        customer_wallet_balance_today = list(
            customer_wallet_qs.filter(date_created__lte=timezone.now().date())
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        customer_wallet_balance_yesterday = list(
            customer_wallet_qs.filter(date_created__lte=self.previous_day)
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        customer_wallet_balance_this_week = list(
            customer_wallet_qs.filter(date_created__lte=self.week_start)
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        customer_wallet_balance_this_month = list(
            customer_wallet_qs.filter(date_created__lte=self.month_start)
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        customer_wallet_balance_this_year = list(
            customer_wallet_qs.filter(date_created__lte=self.year_start)
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        customer_wallet_balance_last_year = list(
            customer_wallet_qs.filter(date_created__lte=self.previous_year_end)
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        customer_wallet_balance_last_week = list(
            customer_wallet_qs.filter(date_created__lte=self.previous_week_end)
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        customer_wallet_balance_last_month = list(
            customer_wallet_qs.filter(date_created__lte=self.previous_month_end)
            .aggregate(Sum("available_balance"))
            .values()
        )[0]

        # Spend Wallet Balance
        spend_wallet_qs = WalletSystem.objects.filter(wallet_type="AJO_SPENDING")
        spend_wallet_balance = list(
            spend_wallet_qs.aggregate(Sum("available_balance")).values()
        )[0]
        spend_wallet_balance_12_am = list(
            spend_wallet_qs.filter(date_created__lte=self.midnight_time)
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        spend_wallet_balance_today = list(
            spend_wallet_qs.filter(date_created__lte=timezone.now().date())
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        spend_wallet_balance_yesterday = list(
            spend_wallet_qs.filter(date_created__lte=self.previous_day)
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        spend_wallet_balance_this_week = list(
            spend_wallet_qs.filter(date_created__lte=self.week_start)
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        spend_wallet_balance_this_month = list(
            spend_wallet_qs.filter(date_created__lte=self.month_start)
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        spend_wallet_balance_this_year = list(
            spend_wallet_qs.filter(date_created__lte=self.year_start)
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        spend_wallet_balance_last_year = list(
            spend_wallet_qs.filter(date_created__lte=self.previous_year_end)
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        spend_wallet_balance_last_week = list(
            spend_wallet_qs.filter(date_created__lte=self.previous_week_end)
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        spend_wallet_balance_last_month = list(
            spend_wallet_qs.filter(date_created__lte=self.previous_month_end)
            .aggregate(Sum("available_balance"))
            .values()
        )[0]

        # Collectors Count
        """This count is based on onboarded collectors"""
        collectors_qs = AjoUser.objects.filter().distinct("user")

        # Collectors Count Based on Number of Collections Made
        """For an Ajo User to qualify as a collector they must have made up to 7 collections"""
        collectors_based_on_collections_qs = (
            self.savings_transactions_qs.values("user")
            .annotate(collections_count=Count("user"))
            .filter(collections_count__gte=7)
        )

        overall_collectors_count_based_on_collections = (
            collectors_based_on_collections_qs.count()
        )
        active_collectors_count_based_on_collections = (
            collectors_based_on_collections_qs.filter(
                user__performance_status="ACTIVE"
            ).count()
        )
        inactive_collectors_count_based_on_collections = (
            collectors_based_on_collections_qs.filter(
                user__performance_status="INACTIVE"
            ).count()
        )
        suspended_collectors_count_based_on_collections = (
            collectors_based_on_collections_qs.filter(
                user__performance_status="SUSPENDED"
            ).count()
        )
        number_of_collectors_till_yesterday_based_on_collections = (
            collectors_based_on_collections_qs.filter(
                user__created_at__lte=self.previous_day.date()
            ).count()
        )
        number_of_collectors_till_last_week_based_on_collections = (
            collectors_based_on_collections_qs.filter(
                user__created_at__lte=self.previous_week_end
            ).count()
        )
        number_of_collectors_till_last_month_based_on_collections = (
            collectors_based_on_collections_qs.filter(
                user__created_at__lte=self.previous_month_end
            ).count()
        )
        number_of_collectors_till_last_year_based_on_collections = (
            collectors_based_on_collections_qs.filter(
                user__created_at__lte=self.previous_year_end
            ).count()
        )

        # New Collectors Count
        # collectors_qs = AjoSaving.objects.filter(user__isnull=False).distinct("user")
        overall_new_collector_count = collectors_based_on_collections_qs.filter(
            user__created_at__gte=self.month_start
        ).count()
        new_collector_count_today = collectors_based_on_collections_qs.filter(
            user__created_at__date=timezone.now().date()
        ).count()
        new_collector_count_yesterday = collectors_based_on_collections_qs.filter(
            user__created_at__date=self.previous_day.date()
        ).count()
        new_collector_count_this_week = collectors_based_on_collections_qs.filter(
            user__created_at__gte=self.week_start
        ).count()
        new_collector_count_this_month = collectors_based_on_collections_qs.filter(
            user__created_at__gte=self.month_start
        ).count()
        new_collector_count_this_year = collectors_based_on_collections_qs.filter(
            user__created_at__gte=self.year_start
        ).count()
        new_collector_count_last_year = collectors_based_on_collections_qs.filter(
            user__created_at__gte=self.previous_year_start,
            user__created_at__lte=self.previous_year_end,
        ).count()
        new_collector_count_last_week = collectors_based_on_collections_qs.filter(
            user__created_at__gte=self.previous_week_start,
            user__created_at__lte=self.previous_week_end,
        ).count()
        new_collector_count_last_month = collectors_based_on_collections_qs.filter(
            user__created_at__gte=self.previous_month_start,
            user__created_at__lte=self.previous_month_end,
        ).count()

        # Contributors Count
        contributors_qs = AjoUser.objects.all()
        overall_contributors_count = contributors_qs.count()
        contributors_count_today = contributors_qs.filter(
            created_at__date=timezone.now().date()
        ).count()
        contributors_count_yesterday = contributors_qs.filter(
            created_at__date=self.previous_day.date()
        ).count()
        contributors_count_this_week = contributors_qs.filter(
            created_at__gte=self.week_start
        ).count()
        contributors_count_this_month = contributors_qs.filter(
            created_at__gte=self.month_start
        ).count()
        contributors_count_this_year = contributors_qs.filter(
            created_at__gte=self.year_start
        ).count()
        contributors_count_last_year = contributors_qs.filter(
            created_at__gte=self.previous_year_start,
            created_at__lte=self.previous_year_end,
        ).count()
        contributors_count_last_week = contributors_qs.filter(
            created_at__gte=self.previous_week_start,
            created_at__lte=self.previous_week_end,
        ).count()
        contributors_count_last_month = contributors_qs.filter(
            created_at__gte=self.previous_month_start,
            created_at__lte=self.previous_month_end,
        ).count()

        # Savings Wallet Balance
        savings_wallet_qs = WalletSystem.objects.filter(wallet_type="AJO_USER")
        savings_wallet_balance = list(
            savings_wallet_qs.aggregate(Sum("available_balance")).values()
        )[0]
        savings_wallet_balance_12_am = list(
            savings_wallet_qs.filter(date_created__lte=self.midnight_time)
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        savings_wallet_balance_today = list(
            savings_wallet_qs.filter(date_created__lte=timezone.now().date())
            .aggregate(Sum("available_balance"))
            .values()
        )[0]

        data = {
            "collector_commission": {
                "overall_amount": collector_commission_amount or 0.00,
                "amount_today": collector_commission_amount_today or 0.00,
                "amount_this_week": collector_commission_amount_this_week or 0.00,
                "amount_this_month": collector_commission_amount_this_month or 0.00,
                "amount_last_month": collector_commission_amount_last_month or 0.00,
                "amount_yesterday": collector_commission_amount_yesterday or 0.00,
                "amount_last_week": collector_commission_amount_last_week or 0.00,
                "amount_this_year": collector_commission_amount_this_year or 0.00,
                "amount_last_year": collector_commission_amount_last_year or 0.00,
            },
            "customer_wallet_balance": {
                "percentage": 0,
                "change": 0,
                "overall_amount": customer_wallet_balance or 0.00,
                "12am_balance": customer_wallet_balance_12_am or 0.00,
                "current_balance": customer_wallet_balance or 0.00,
                "amount_today": customer_wallet_balance_today or 0.00,
                "amount_this_week": customer_wallet_balance_this_week or 0.00,
                "amount_this_month": customer_wallet_balance_this_month or 0.00,
                "amount_last_month": customer_wallet_balance_last_month or 0.00,
                "amount_yesterday": customer_wallet_balance_yesterday or 0.00,
                "amount_last_week": customer_wallet_balance_last_week or 0.00,
                "amount_this_year": customer_wallet_balance_this_year or 0.00,
                "amount_last_year": customer_wallet_balance_last_year or 0.00,
            },
            "spend_balance": {
                "percentage": 0,
                "change": 0,
                "overall_amount": spend_wallet_balance or 0.00,
                "12am_balance": spend_wallet_balance_12_am or 0.00,
                "current_balance": spend_wallet_balance or 0.00,
                "amount_today": spend_wallet_balance_today or 0.00,
                "amount_this_week": spend_wallet_balance_this_week or 0.00,
                "amount_this_month": spend_wallet_balance_this_month or 0.00,
                "amount_last_month": spend_wallet_balance_last_month or 0.00,
                "amount_yesterday": spend_wallet_balance_yesterday or 0.00,
                "amount_last_week": spend_wallet_balance_last_week or 0.00,
                "amount_this_year": spend_wallet_balance_this_year or 0.00,
                "amount_last_year": spend_wallet_balance_last_year or 0.00,
            },
            "collectors": {
                "overall_count": overall_collectors_count_based_on_collections,
                "number_of_collectors_till_yesterday": number_of_collectors_till_yesterday_based_on_collections,
                "number_of_collectors_till_last_week": number_of_collectors_till_last_week_based_on_collections,
                "number_of_collectors_till_last_month": number_of_collectors_till_last_month_based_on_collections,
                "number_of_collectors_till_last_year": number_of_collectors_till_last_year_based_on_collections,
                "active": active_collectors_count_based_on_collections,
                "inactive": inactive_collectors_count_based_on_collections,
                "suspended_agentds": suspended_collectors_count_based_on_collections,
                "percentage": {"percentage": 0, "change": "up"},
            },
            "new_collectors": {
                "overall_count": overall_new_collector_count,
                "percentage": 0,
                "change": 0,
                "count_today": new_collector_count_today,
                "count_this_week": new_collector_count_this_week,
                "count_this_month": new_collector_count_this_month,
                "count_last_month": new_collector_count_last_month,
                "count_yesterday": new_collector_count_yesterday,
                "count_last_week": new_collector_count_last_week,
                "count_this_year": new_collector_count_this_year,
                "count_last_year": new_collector_count_last_year,
            },
            "contributors": {
                "overall_count": overall_contributors_count,
                "percentage": 0,
                "change": 0,
                "count_today": contributors_count_today,
                "count_this_week": contributors_count_this_week,
                "count_this_month": contributors_count_this_month,
                "count_last_month": contributors_count_last_month,
                "count_yesterday": contributors_count_yesterday,
                "count_last_week": contributors_count_last_week,
                "count_this_year": contributors_count_this_year,
                "count_last_year": contributors_count_last_year,
            },
            "company_commission": {
                "overall_amount": collector_commission_amount or 0.00,
                "amount_today": collector_commission_amount_today or 0.00,
                "amount_this_week": collector_commission_amount_this_week or 0.00,
                "amount_this_month": collector_commission_amount_this_month or 0.00,
                "amount_last_month": collector_commission_amount_last_month or 0.00,
                "amount_yesterday": collector_commission_amount_yesterday or 0.00,
                "amount_last_week": collector_commission_amount_last_week or 0.00,
                "amount_this_year": collector_commission_amount_this_year or 0.00,
                "amount_last_year": collector_commission_amount_last_year or 0.00,
            },
            "savings_wallet_balance": {
                "percentage": 0,
                "change": 0,
                "overall_amount": savings_wallet_balance or 0.00,
                "12am_balance": savings_wallet_balance_12_am or 0.00,
                "current_balance": savings_wallet_balance or 0.00,
                "amount_today": savings_wallet_balance_today or 0.00,
            },
            "bank_account_balance": 0.00,
        }
        return data

    def get_main_dash_savings_metrics(self):
        savings_transactions_qs = self.successful_transactions_qs.filter(
            plan_type="AJO", wallet_type="AJO_USER", status="SUCCESS"
        )
        ajo_savings_qs = AjoSaving.objects.filter()

        # Total Savings Amount
        total_savings_amount = list(
            savings_transactions_qs.aggregate(Sum("amount")).values()
        )[0]
        total_savings_amount_12_am = list(
            savings_transactions_qs.filter(date_created__lte=self.midnight_time)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_savings_amount_today = list(
            savings_transactions_qs.filter(date_created__lt=timezone.now().date())
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_savings_amount_yesterday = list(
            savings_transactions_qs.filter(date_created__date=self.previous_day.date())
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_savings_amount_this_week = list(
            savings_transactions_qs.filter(date_created__gte=self.week_start)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_savings_amount_this_month = list(
            savings_transactions_qs.filter(date_created__gte=self.month_start)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_savings_amount_this_year = list(
            savings_transactions_qs.filter(date_created__gte=self.year_start)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_savings_amount_last_year = list(
            savings_transactions_qs.filter(
                date_created__gte=self.previous_year_start,
                date_created__lte=self.previous_year_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_savings_amount_last_week = list(
            savings_transactions_qs.filter(
                date_created__gte=self.previous_week_start,
                date_created__lte=self.previous_week_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_savings_amount_last_month = list(
            savings_transactions_qs.filter(
                date_created__gte=self.previous_month_start,
                date_created__lte=self.previous_month_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        # Average Savings Amount
        average_savings_amount = list(
            savings_transactions_qs.aggregate(Avg("amount")).values()
        )[0]
        average_savings_amount_12_am = list(
            savings_transactions_qs.filter(date_created__lte=self.midnight_time)
            .aggregate(Avg("amount"))
            .values()
        )[0]
        average_savings_amount_today = list(
            savings_transactions_qs.filter(date_created__date=timezone.now().date())
            .aggregate(Avg("amount"))
            .values()
        )[0]
        average_savings_amount_yesterday = list(
            savings_transactions_qs.filter(date_created__date=self.previous_day.date())
            .aggregate(Avg("amount"))
            .values()
        )[0]
        average_savings_amount_this_week = list(
            savings_transactions_qs.filter(date_created__gte=self.week_start)
            .aggregate(Avg("amount"))
            .values()
        )[0]
        average_savings_amount_this_month = list(
            savings_transactions_qs.filter(date_created__gte=self.month_start)
            .aggregate(Avg("amount"))
            .values()
        )[0]
        average_savings_amount_this_year = list(
            savings_transactions_qs.filter(date_created__lte=self.year_start)
            .aggregate(Avg("amount"))
            .values()
        )[0]
        average_savings_amount_last_year = list(
            savings_transactions_qs.filter(
                date_created__gte=self.previous_year_start,
                date_created__lte=self.previous_year_end,
            )
            .aggregate(Avg("amount"))
            .values()
        )[0]
        average_savings_amount_last_week = list(
            savings_transactions_qs.filter(
                date_created__gte=self.previous_week_start,
                date_created__lte=self.previous_week_end,
            )
            .aggregate(Avg("amount"))
            .values()
        )[0]
        average_savings_amount_last_month = list(
            savings_transactions_qs.filter(
                date_created__gte=self.previous_month_start,
                date_created__lte=self.previous_month_end,
            )
            .aggregate(Avg("amount"))
            .values()
        )[0]

        # Active Savings Count
        active_savings_qs = ajo_savings_qs.filter(is_active=True)
        total_active_savings_count = list(
            active_savings_qs.aggregate(Count("amount_saved")).values()
        )[0]
        total_active_savings_count_12_am = list(
            active_savings_qs.filter(created_at__lte=self.midnight_time)
            .aggregate(Count("amount_saved"))
            .values()
        )[0]
        total_active_savings_count_today = list(
            active_savings_qs.filter(created_at__date=timezone.now().date())
            .aggregate(Count("amount_saved"))
            .values()
        )[0]
        total_active_savings_count_yesterday = list(
            active_savings_qs.filter(created_at__date=self.previous_day.date())
            .aggregate(Count("amount_saved"))
            .values()
        )[0]
        total_active_savings_count_this_week = list(
            active_savings_qs.filter(created_at__gte=self.week_start)
            .aggregate(Count("amount_saved"))
            .values()
        )[0]
        total_active_savings_count_this_month = list(
            active_savings_qs.filter(created_at__gte=self.month_start)
            .aggregate(Count("amount_saved"))
            .values()
        )[0]
        total_active_savings_count_this_year = list(
            active_savings_qs.filter(created_at__gte=self.year_start)
            .aggregate(Count("amount_saved"))
            .values()
        )[0]
        total_active_savings_count_last_year = list(
            active_savings_qs.filter(
                created_at__gte=self.previous_year_start,
                created_at__lte=self.previous_year_end,
            )
            .aggregate(Count("amount_saved"))
            .values()
        )[0]
        total_active_savings_count_last_week = list(
            active_savings_qs.filter(
                created_at__gte=self.previous_week_start,
                created_at__lte=self.previous_week_end,
            )
            .aggregate(Count("amount_saved"))
            .values()
        )[0]
        total_active_savings_count_last_month = list(
            active_savings_qs.filter(
                created_at__gte=self.previous_month_start,
                created_at__lte=self.previous_month_end,
            )
            .aggregate(Count("amount_saved"))
            .values()
        )[0]

        # Active Savings Amount
        total_active_savings_amount = list(
            active_savings_qs.aggregate(Sum("amount_saved")).values()
        )[0]
        total_active_savings_amount_12_am = list(
            active_savings_qs.filter(created_at__lte=self.midnight_time)
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]
        total_active_savings_amount_today = list(
            active_savings_qs.filter(created_at__date=timezone.now().date())
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]
        total_active_savings_amount_yesterday = list(
            active_savings_qs.filter(created_at__date=self.previous_day.date())
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]
        total_active_savings_amount_this_week = list(
            active_savings_qs.filter(created_at__gte=self.week_start)
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]
        total_active_savings_amount_this_month = list(
            active_savings_qs.filter(created_at__gte=self.month_start)
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]
        total_active_savings_amount_this_year = list(
            active_savings_qs.filter(created_at__gte=self.year_start)
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]
        total_active_savings_amount_last_year = list(
            active_savings_qs.filter(
                created_at__gte=self.previous_year_start,
                created_at__lte=self.previous_year_end,
            )
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]
        total_active_savings_amount_last_week = list(
            active_savings_qs.filter(
                created_at__gte=self.previous_week_start,
                created_at__lte=self.previous_week_end,
            )
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]
        total_active_savings_amount_last_month = list(
            active_savings_qs.filter(
                created_at__gte=self.previous_month_start,
                created_at__lte=self.previous_month_end,
            )
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]

        # In-Active Savings Count
        inactive_savings_qs = ajo_savings_qs.filter(is_active=False)
        total_inactive_savings_count = list(
            inactive_savings_qs.aggregate(Count("amount_saved")).values()
        )[0]
        total_inactive_savings_count_12_am = list(
            inactive_savings_qs.filter(created_at__lte=self.midnight_time)
            .aggregate(Count("amount_saved"))
            .values()
        )[0]
        total_inactive_savings_count_today = list(
            inactive_savings_qs.filter(created_at__date=timezone.now().date())
            .aggregate(Count("amount_saved"))
            .values()
        )[0]
        total_inactive_savings_count_yesterday = list(
            inactive_savings_qs.filter(created_at__date=self.previous_day.date())
            .aggregate(Count("amount_saved"))
            .values()
        )[0]
        total_inactive_savings_count_this_week = list(
            inactive_savings_qs.filter(created_at__gte=self.week_start)
            .aggregate(Count("amount_saved"))
            .values()
        )[0]
        total_inactive_savings_count_this_month = list(
            inactive_savings_qs.filter(created_at__gte=self.month_start)
            .aggregate(Count("amount_saved"))
            .values()
        )[0]
        total_inactive_savings_count_this_year = list(
            inactive_savings_qs.filter(created_at__gte=self.year_start)
            .aggregate(Count("amount_saved"))
            .values()
        )[0]
        total_inactive_savings_count_last_year = list(
            inactive_savings_qs.filter(
                created_at__gte=self.previous_year_start,
                created_at__lte=self.previous_year_end,
            )
            .aggregate(Count("amount_saved"))
            .values()
        )[0]
        total_inactive_savings_count_last_week = list(
            inactive_savings_qs.filter(
                created_at__gte=self.previous_week_start,
                created_at__lte=self.previous_week_end,
            )
            .aggregate(Count("amount_saved"))
            .values()
        )[0]
        total_inactive_savings_count_last_month = list(
            inactive_savings_qs.filter(
                created_at__gte=self.previous_month_start,
                created_at__lte=self.previous_month_end,
            )
            .aggregate(Count("amount_saved"))
            .values()
        )[0]

        # In-Active Savings Amount
        total_inactive_savings_amount = list(
            inactive_savings_qs.aggregate(Sum("amount_saved")).values()
        )[0]
        total_inactive_savings_amount_12_am = list(
            inactive_savings_qs.filter(created_at__lte=self.midnight_time)
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]
        total_inactive_savings_amount_today = list(
            inactive_savings_qs.filter(created_at__date=timezone.now().date())
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]
        total_inactive_savings_amount_yesterday = list(
            inactive_savings_qs.filter(created_at__date=self.previous_day.date())
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]
        total_inactive_savings_amount_this_week = list(
            inactive_savings_qs.filter(created_at__gte=self.week_start)
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]
        total_inactive_savings_amount_this_month = list(
            inactive_savings_qs.filter(created_at__gte=self.month_start)
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]
        total_inactive_savings_amount_this_year = list(
            inactive_savings_qs.filter(created_at__gte=self.year_start)
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]
        total_inactive_savings_amount_last_year = list(
            inactive_savings_qs.filter(
                created_at__gte=self.previous_year_start,
                created_at__lte=self.previous_year_end,
            )
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]
        total_inactive_savings_amount_last_week = list(
            inactive_savings_qs.filter(
                created_at__gte=self.previous_week_start,
                created_at__lte=self.previous_week_end,
            )
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]
        total_inactive_savings_amount_last_month = list(
            inactive_savings_qs.filter(
                created_at__gte=self.previous_month_start,
                created_at__lte=self.previous_month_end,
            )
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]

        # Average Savings Per Collector
        number_of_collectors_function = self.get_transactions_overview_two().get(
            "collectors"
        )
        number_of_collectors = number_of_collectors_function.get("overall_count")
        number_of_collectors_till_yesterday = number_of_collectors_function.get(
            "number_of_collectors_till_yesterday"
        )
        number_of_collectors_till_last_year = number_of_collectors_function.get(
            "number_of_collectors_till_last_year"
        )
        number_of_collectors_till_last_week = number_of_collectors_function.get(
            "number_of_collectors_till_last_week"
        )
        number_of_collectors_till_last_month = number_of_collectors_function.get(
            "number_of_collectors_till_last_month"
        )

        average_savings_per_collector = (
            total_savings_amount if total_savings_amount else 0
        ) / (number_of_collectors if number_of_collectors else 1)
        average_savings_per_collector_today = (
            total_savings_amount_today if total_savings_amount_today else 0
        ) / (number_of_collectors if number_of_collectors else 1)
        average_savings_per_collector_this_week = (
            total_savings_amount_this_week if total_savings_amount_this_week else 0
        ) / (number_of_collectors if number_of_collectors else 1)
        average_savings_per_collector_this_month = (
            total_savings_amount_this_month if total_savings_amount_this_month else 0
        ) / (total_savings_amount_this_month if total_savings_amount_this_month else 1)
        average_savings_per_collector_last_month = (
            total_savings_amount_last_month if total_savings_amount_last_month else 0
        ) / (
            number_of_collectors_till_last_month
            if number_of_collectors_till_last_month
            else 1
        )
        average_savings_per_collector_yesterday = (
            total_savings_amount_yesterday if total_savings_amount_yesterday else 0
        ) / (
            number_of_collectors_till_yesterday
            if number_of_collectors_till_yesterday
            else 1
        )
        average_savings_per_collector_last_week = (
            total_savings_amount_last_week if total_savings_amount_last_week else 0
        ) / (
            number_of_collectors_till_last_week
            if number_of_collectors_till_last_week
            else 1
        )
        average_savings_per_collector_this_year = (
            total_savings_amount_this_year if total_savings_amount_this_year else 0
        ) / (number_of_collectors if number_of_collectors else 1)
        average_savings_per_collector_last_year = (
            total_savings_amount_last_year if total_savings_amount_last_year else 0
        ) / (
            number_of_collectors_till_last_year
            if number_of_collectors_till_last_year
            else 1
        )

        # Average Savings Per Collector
        contributors_function = self.get_transactions_overview_two().get("contributors")

        number_of_contributors = contributors_function.get("overall_count")
        number_of_contributors_today = contributors_function.get("count_today")
        number_of_contributors_this_week = contributors_function.get("count_this_week")
        number_of_contributors_this_month = contributors_function.get(
            "count_this_month"
        )
        number_of_contributors_last_month = contributors_function.get(
            "count_last_month"
        )
        number_of_contributors_yesterday = contributors_function.get("count_yesterday")
        number_of_contributors_last_week = contributors_function.get("count_last_week")
        number_of_contributors_this_year = contributors_function.get("count_this_year")
        number_of_contributors_last_year = contributors_function.get("count_last_year")

        average_savers_per_collector = (
            number_of_contributors if number_of_contributors else 0
        ) / (number_of_collectors if number_of_collectors else 1)
        average_savers_per_collector_today = (
            number_of_contributors_today if number_of_contributors_today else 0
        ) / (number_of_collectors if number_of_collectors else 1)
        average_savers_per_collector_this_week = (
            number_of_contributors_this_week if number_of_contributors_this_week else 0
        ) / (number_of_collectors if number_of_collectors else 1)
        average_savers_per_collector_this_month = (
            number_of_contributors_this_month
            if number_of_contributors_this_month
            else 0
        ) / (number_of_collectors if number_of_collectors else 1)
        average_savers_per_collector_last_month = (
            number_of_contributors_last_month
            if number_of_contributors_last_month
            else 0
        ) / (
            number_of_collectors_till_last_month
            if number_of_collectors_till_last_month
            else 1
        )
        average_savers_per_collector_yesterday = (
            number_of_contributors_yesterday if number_of_contributors_yesterday else 0
        ) / (
            number_of_collectors_till_yesterday
            if number_of_collectors_till_yesterday
            else 1
        )
        average_savers_per_collector_last_week = (
            number_of_contributors_last_week if number_of_contributors_last_week else 0
        ) / (
            number_of_collectors_till_last_week
            if number_of_collectors_till_last_week
            else 1
        )
        average_savers_per_collector_this_year = (
            number_of_contributors_this_year if number_of_contributors_this_year else 0
        ) / (number_of_collectors if number_of_collectors else 1)
        average_savers_per_collector_last_year = (
            number_of_contributors_last_year if number_of_contributors_last_year else 0
        ) / (
            number_of_collectors_till_last_year
            if number_of_collectors_till_last_year
            else 1
        )

        # Funded Savings
        funded_savings_amount = (
            ajo_savings_qs.filter(is_activated=True).aggregate(
                total_amount=Sum("amount_saved")
            )["total_amount"]
            or 0
        )
        not_funded_savings_amount = (
            ajo_savings_qs.filter(is_activated=False).aggregate(
                total_amount=Sum("amount_saved")
            )["total_amount"]
            or 0
        )
        loans_savings_amount = (
            ajo_savings_qs.filter(loan=True, is_activated=True).aggregate(
                total_amount=Sum("amount_saved")
            )["total_amount"]
            or 0
        )

        data = {
            "total_savings": {
                "overall_amount": total_savings_amount or 0.00,
                "amount_today": total_savings_amount_today or 0.00,
                "amount_this_week": total_savings_amount_this_week or 0.00,
                "amount_this_month": total_savings_amount_this_month or 0.00,
                "amount_last_month": total_savings_amount_last_month or 0.00,
                "amount_yesterday": total_savings_amount_yesterday or 0.00,
                "amount_last_week": total_savings_amount_last_week or 0.00,
                "amount_this_year": total_savings_amount_this_year or 0.00,
                "amount_last_year": total_savings_amount_last_year or 0.00,
                "percentage": 0,
                "change": 0,
            },
            "active_savings_amount": {
                "overall_amount": total_active_savings_amount or 0.00,
                "percentage": 0,
                "change": 0,
                "amount_today": total_active_savings_amount_today or 0,
                "amount_this_week": total_active_savings_amount_this_week or 0,
                "amount_this_month": total_active_savings_amount_this_month or 0,
                "amount_last_month": total_active_savings_amount_last_month or 0,
                "amount_yesterday": total_active_savings_amount_yesterday or 0,
                "amount_last_week": total_active_savings_amount_last_week or 0,
                "amount_this_year": total_active_savings_amount_this_year or 0,
                "amount_last_year": total_active_savings_amount_last_year or 0,
            },
            "inactive_savings_amount": {
                "overall_amount": total_inactive_savings_amount or 0.00,
                "percentage": 0,
                "change": 0,
                "amount_today": total_inactive_savings_amount_today or 0,
                "amount_this_week": total_inactive_savings_amount_this_week or 0,
                "amount_this_month": total_inactive_savings_amount_this_month or 0,
                "amount_last_month": total_inactive_savings_amount_last_month or 0,
                "amount_yesterday": total_inactive_savings_amount_yesterday or 0,
                "amount_last_week": total_inactive_savings_amount_last_week or 0,
                "amount_this_year": total_inactive_savings_amount_this_year or 0,
                "amount_last_year": total_inactive_savings_amount_last_year or 0,
            },
            "active_savings_count": {
                "overall_count": total_active_savings_count,
                "percentage": 0,
                "change": 0,
                "count_today": total_active_savings_count_today or 0,
                "count_this_week": total_active_savings_count_this_week or 0,
                "count_this_month": total_active_savings_count_this_month or 0,
                "count_last_month": total_active_savings_count_last_month or 0,
                "count_yesterday": total_active_savings_count_yesterday or 0,
                "count_last_week": total_active_savings_count_last_week or 0,
                "count_this_year": total_active_savings_count_this_year or 0,
                "count_last_year": total_active_savings_count_last_year or 0,
            },
            "inactive_savings_count": {
                "overall_count": total_inactive_savings_count or 0,
                "percentage": 0,
                "change": "down",
                "count_today": total_inactive_savings_count_today or 0,
                "count_this_week": total_inactive_savings_count_this_week or 0,
                "count_this_month": total_inactive_savings_count_this_month or 0,
                "count_last_month": total_inactive_savings_count_last_month or 0,
                "count_yesterday": total_inactive_savings_count_yesterday or 0,
                "count_last_week": total_inactive_savings_count_last_week or 0,
                "count_this_year": total_inactive_savings_count_this_year or 0,
                "count_last_year": total_inactive_savings_count_last_year or 0,
            },
            "average_savings_per_collector": {
                "overall_amount": average_savings_per_collector,
                "percentage": 0,
                "change": 0,
                "amount_today": average_savings_per_collector_today,
                "amount_this_week": average_savings_per_collector_this_week,
                "amount_this_month": average_savings_per_collector_this_month,
                "amount_last_month": average_savings_per_collector_last_month,
                "amount_yesterday": average_savings_per_collector_yesterday,
                "amount_last_week": average_savings_per_collector_last_week,
                "amount_this_year": average_savings_per_collector_this_year,
                "amount_last_year": average_savings_per_collector_last_year,
            },
            "average_savers_per_collector": {
                "overall_count": average_savers_per_collector,
                "percentage": 0,
                "change": 0,
                "count_today": average_savers_per_collector_today,
                "count_this_week": average_savers_per_collector_this_week,
                "count_this_month": average_savers_per_collector_this_month,
                "count_last_month": average_savers_per_collector_last_month,
                "count_yesterday": average_savers_per_collector_yesterday,
                "count_last_week": average_savers_per_collector_last_week,
                "count_this_year": average_savers_per_collector_this_year,
                "count_last_year": average_savers_per_collector_last_year,
            },
            "average_total_savings": {
                "overall_amount": average_savings_amount or 0.00,
                "amount_today": average_savings_amount_today or 0.00,
                "amount_this_week": average_savings_amount_this_week or 0.00,
                "amount_this_month": average_savings_amount_this_month or 0.00,
                "amount_last_month": average_savings_amount_last_month or 0.00,
                "amount_yesterday": average_savings_amount_yesterday or 0.00,
                "amount_last_week": average_savings_amount_last_week or 0.00,
                "amount_this_year": average_savings_amount_this_year or 0.00,
                "amount_last_year": average_savings_amount_last_year or 0.00,
                "percentage": 0,
                "change": 0,
            },
            "funded_savings": {"amount": funded_savings_amount},
            "not_funded_savings": {"amount": not_funded_savings_amount},
            "total_loan_savings": {"amount": loans_savings_amount},
        }
        return data

    def get_main_dash_revenue_metrics(self):
        # Revenue Amount
        revenue_qs = self.commission_qs.filter(commission_type="COMPANY")

        total_revenue_amount = list(revenue_qs.aggregate(Sum("amount")).values())[0]
        total_revenue_amount_12_am = list(
            revenue_qs.filter(created_at__lte=self.midnight_time)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_revenue_amount_today = list(
            revenue_qs.filter(created_at__date=timezone.now().date())
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_revenue_amount_yesterday = list(
            revenue_qs.filter(created_at__date=self.previous_day.date())
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_revenue_amount_this_week = list(
            revenue_qs.filter(created_at__gte=self.week_start)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_revenue_amount_this_month = list(
            revenue_qs.filter(created_at__gte=self.month_start)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_revenue_amount_this_year = list(
            revenue_qs.filter(created_at__gte=self.year_start)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_revenue_amount_last_year = list(
            revenue_qs.filter(
                created_at__gte=self.previous_year_start,
                created_at__lte=self.previous_year_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_revenue_amount_last_week = list(
            revenue_qs.filter(
                created_at__gte=self.previous_week_start,
                created_at__lte=self.previous_week_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_revenue_amount_last_month = list(
            revenue_qs.filter(
                created_at__gte=self.previous_month_start,
                created_at__lte=self.previous_month_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        # Number of Collectors
        number_of_collectors_function = self.get_transactions_overview_two().get(
            "collectors"
        )
        number_of_collectors = number_of_collectors_function.get("overall_count")
        number_of_collectors_till_yesterday = number_of_collectors_function.get(
            "number_of_collectors_till_yesterday"
        )
        number_of_collectors_till_last_year = number_of_collectors_function.get(
            "number_of_collectors_till_last_year"
        )
        number_of_collectors_till_last_week = number_of_collectors_function.get(
            "number_of_collectors_till_last_week"
        )
        number_of_collectors_till_last_month = number_of_collectors_function.get(
            "number_of_collectors_till_last_month"
        )

        # Revenue Target Amount
        daily_target_amount = 200
        overall_revenue_target_amount = (
            daily_target_amount
            * self.numbers_day_since_ajo_start_less_weekend
            * number_of_collectors
        )
        total_revenue_target_amount_today = daily_target_amount * number_of_collectors
        total_revenue_target_amount_yesterday = (
            daily_target_amount * number_of_collectors_till_yesterday
        )
        total_revenue_target_amount_this_week = total_revenue_target_amount_today * 6
        total_revenue_target_amount_this_month = total_revenue_target_amount_today * 24
        total_revenue_target_amount_this_year = (
            total_revenue_target_amount_today * 24 * 12
        )
        total_revenue_target_amount_last_year = (
            daily_target_amount * number_of_collectors_till_last_year * 24 * 12
        )
        total_revenue_target_amount_last_week = (
            daily_target_amount * number_of_collectors_till_last_week * 6
        )
        total_revenue_target_amount_last_month = (
            daily_target_amount * number_of_collectors_till_last_month * 24
        )

        # Revenue Generated Per Collector
        average_revenue_per_collector = (
            total_revenue_amount if total_revenue_amount else 0
        ) / (number_of_collectors if number_of_collectors else 1)
        average_revenue_per_collector_today = (
            total_revenue_amount_today if total_revenue_amount_today else 0
        ) / (number_of_collectors if number_of_collectors else 1)
        average_revenue_per_collector_this_week = (
            total_revenue_amount_this_week if total_revenue_amount_this_week else 0
        ) / (number_of_collectors if number_of_collectors else 1)
        average_revenue_per_collector_this_month = (
            total_revenue_amount_this_month if total_revenue_amount_this_month else 0
        ) / (number_of_collectors if number_of_collectors else 1)
        average_revenue_per_collector_last_month = (
            total_revenue_amount_last_month if total_revenue_amount_last_month else 0
        ) / (
            number_of_collectors_till_last_month
            if number_of_collectors_till_last_month
            else 1
        )
        average_revenue_per_collector_yesterday = (
            total_revenue_amount_yesterday if total_revenue_amount_yesterday else 0
        ) / (
            number_of_collectors_till_yesterday
            if number_of_collectors_till_yesterday
            else 1
        )
        average_revenue_per_collector_last_week = (
            total_revenue_amount_last_week if total_revenue_amount_last_week else 0
        ) / (
            number_of_collectors_till_last_week
            if number_of_collectors_till_last_week
            else 1
        )
        average_revenue_per_collector_this_year = (
            total_revenue_amount_this_year if total_revenue_amount_this_year else 0
        ) / (number_of_collectors if number_of_collectors else 1)
        average_revenue_per_collector_last_year = (
            total_revenue_amount_last_year if total_revenue_amount_last_year else 0
        ) / (
            number_of_collectors_till_last_year
            if number_of_collectors_till_last_year
            else 1
        )

        # Revenue Generated Per Branch
        number_of_branches = self.branch_qs.count()
        number_of_branches_till_last_month = self.branch_qs.filter(
            date_created__lte=self.previous_month_end
        ).count()
        number_of_branches_till_yesterday = self.branch_qs.filter(
            date_created__date=self.previous_day.date()
        ).count()
        number_of_branches_till_last_week = self.branch_qs.filter(
            date_created__lte=self.previous_week_end
        ).count()
        number_of_branches_till_last_year = self.branch_qs.filter(
            date_created__lte=self.previous_year_end
        ).count()

        average_revenue_per_branch = (total_revenue_amount or 0) / (
            number_of_branches or 1
        )
        average_revenue_per_branch_today = (total_revenue_amount_today or 0) / (
            number_of_branches or 1
        )
        average_revenue_per_branch_this_week = (total_revenue_amount_this_week or 0) / (
            number_of_branches or 1
        )
        average_revenue_per_branch_this_month = (
            total_revenue_amount_this_month or 0
        ) / (number_of_branches or 1)
        average_revenue_per_branch_last_month = (
            total_revenue_amount_last_month or 0
        ) / (number_of_branches_till_last_month or 1)
        average_revenue_per_branch_yesterday = (total_revenue_amount_yesterday or 0) / (
            number_of_branches_till_yesterday or 1
        )
        average_revenue_per_branch_last_week = (total_revenue_amount_last_week or 0) / (
            number_of_branches_till_last_week or 1
        )
        average_revenue_per_branch_this_year = (total_revenue_amount_this_year or 0) / (
            number_of_branches or 1
        )
        average_revenue_per_branch_last_year = (total_revenue_amount_last_year or 0) / (
            number_of_branches_till_last_year or 1
        )

        data = {
            "revenue_amount": {
                "average_daily_amount": 0.00,
                "total_amount": total_revenue_amount or 0.00,
                "overall_amount": total_revenue_amount or 0.00,
                "amount_today": total_revenue_amount_today or 0.00,
                "amount_this_week": total_revenue_amount_this_week or 0.00,
                "amount_this_month": total_revenue_amount_this_month or 0.00,
                "amount_last_month": total_revenue_amount_last_month or 0.00,
                "amount_yesterday": total_revenue_amount_yesterday or 0.00,
                "amount_last_week": total_revenue_amount_last_week or 0.00,
                "amount_this_year": total_revenue_amount_this_year or 0.00,
                "amount_last_year": total_revenue_amount_last_year or 0.00,
            },
            "target_revenue_amount": {
                "overall_amount": overall_revenue_target_amount,
                "average_daily_target": 0,
                "amount_today": total_revenue_target_amount_today,
                "amount_this_week": total_revenue_target_amount_this_week,
                "amount_this_month": total_revenue_target_amount_this_month,
                "amount_last_month": total_revenue_target_amount_last_month,
                "amount_yesterday": total_revenue_target_amount_yesterday,
                "amount_last_week": total_revenue_target_amount_last_week,
                "amount_this_year": total_revenue_target_amount_this_year,
                "amount_last_year": total_revenue_target_amount_last_year,
            },
            "revenue_generated_per_collector": {
                "overall_amount": average_revenue_per_collector,
                "amount_today": average_revenue_per_collector_today,
                "amount_this_week": average_revenue_per_collector_this_week,
                "amount_this_month": average_revenue_per_collector_this_month,
                "amount_last_month": average_revenue_per_collector_last_month,
                "amount_yesterday": average_revenue_per_collector_yesterday,
                "amount_last_week": average_revenue_per_collector_last_week,
                "amount_this_year": average_revenue_per_collector_this_year,
                "amount_last_year": average_revenue_per_collector_last_year,
                "percentage": 0,
                "change": "up",
            },
            "revenue_generated_per_branch": {
                "overall_amount": average_revenue_per_branch or 0.00,
                "amount_today": average_revenue_per_branch_today or 0.00,
                "amount_this_week": average_revenue_per_branch_this_week or 0.00,
                "amount_this_month": average_revenue_per_branch_this_month or 0.00,
                "amount_last_month": average_revenue_per_branch_last_month or 0.00,
                "amount_yesterday": average_revenue_per_branch_yesterday or 0.00,
                "amount_last_week": average_revenue_per_branch_last_week or 0.00,
                "amount_this_year": average_revenue_per_branch_this_year or 0.00,
                "amount_last_year": average_revenue_per_branch_last_year or 0.00,
                "percentage": 0,
                "change": "up",
            },
        }
        return data


class MainDashboardCharts:
    def __init__(self, request):
        self._month_2_month_dates_list = DateUtility()._month_2_month_dates_list
        self.days_label = DateUtility().days_label
        self.months_list_names = DateUtility().months_list_names
        self.year_months_tuple_list = DateUtility().year_months_tuple_list
        self._previous_week_current_week_date = (
            DateUtility()._previous_week_current_week_date
        )
        self._previous_month_current_month_date = (
            DateUtility()._previous_month_current_month_date
        )
        self.previous_month_end = DateUtility().previous_month_end
        self.previous_month_start = DateUtility().previous_month_start
        self.previous_year_current_month_start = (
            DateUtility().previous_year_current_month_start
        )
        self.previous_week_end = DateUtility().previous_week_end
        self.previous_week_start = DateUtility().previous_week_start

        self.filter = request.GET.get("filter")
        self.transactions_qs = Transaction.objects.filter()
        self.wallet_system_qs = WalletSystem.objects.filter(
            wallet_type__in=ajo_savings_wallets_list
        )

        self.successful_transactions_qs = self.transactions_qs.filter(
            Q(status="SUCCESS") & ~Q(transaction_form_type="REVERSAL")
        )
        self.savings_transactions_qs = self.successful_transactions_qs.filter(
            plan_type="AJO", wallet_type="AJO_USER", status="SUCCESS"
        )

        self.commission_qs = Commission.objects.filter(plan_type="AJO")
        self.ajo_saving_qs = AjoSaving.objects.all()

    def get_charts(self):
        # Wallet Balance
        total_wallet_balance_amount = list(
            self.wallet_system_qs.aggregate(Sum("available_balance")).values()
        )[0]
        previous_month_total_wallet_balance_amount = list(
            self.wallet_system_qs.filter(date_created__lte=self.previous_month_end)
            .aggregate(Sum("available_balance"))
            .values()
        )[0]
        total_wallet_balance_amount_change = get_percentage_diff(
            current=total_wallet_balance_amount,
            previous=previous_month_total_wallet_balance_amount,
        )

        past_year_wallet_balance_amt_qs = (
            self.wallet_system_qs.filter(
                date_created__gte=self.previous_year_current_month_start
            )
            .values("date_created__year", "date_created__month")
            .annotate(total_amount=Sum("available_balance"))
            .order_by("date_created__year")
        )
        past_year_wallet_balance_list = [
            (
                data["date_created__year"],
                data["date_created__month"],
                data["total_amount"],
            )
            for data in past_year_wallet_balance_amt_qs
        ]

        past_year_wallet_balance_final_list = []
        for item in self.year_months_tuple_list:
            if item in [(tup[0], tup[1]) for tup in past_year_wallet_balance_list]:
                for trans in past_year_wallet_balance_list:
                    if item == (trans[0], trans[1]):
                        past_year_wallet_balance_final_list.append(trans)
                        break
            else:
                past_year_wallet_balance_final_list.append((0, 0, 0))
        wallet_balance_values_list = [
            trans[2] for trans in past_year_wallet_balance_final_list
        ]

        # Total Savings Amount
        total_savings_amount = list(
            self.savings_transactions_qs.aggregate(Sum("amount")).values()
        )[0]
        previous_month_savings_amount = list(
            self.savings_transactions_qs.filter(
                date_created__lte=self.previous_month_end
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]
        savings_amount_change = get_percentage_diff(
            current=total_savings_amount, previous=previous_month_savings_amount
        )

        past_year_savings_amt_qs = (
            self.savings_transactions_qs.filter(
                date_created__gte=self.previous_year_current_month_start
            )
            .values("date_created__year", "date_created__month")
            .annotate(total_amount=Sum("amount"))
            .order_by("date_created__year")
        )

        past_year_savings_list = [
            (
                data["date_created__year"],
                data["date_created__month"],
                data["total_amount"],
            )
            for data in past_year_savings_amt_qs
        ]

        past_year_savings_final_list = []
        for item in self.year_months_tuple_list:
            if item in [(tup[0], tup[1]) for tup in past_year_savings_list]:
                for trans in past_year_savings_list:
                    if item == (trans[0], trans[1]):
                        past_year_savings_final_list.append(trans)
                        break
            else:
                past_year_savings_final_list.append((0, 0, 0))
        savings_amount_values_list = [
            trans[2] for trans in past_year_savings_final_list
        ]

        # Total Cashout
        cashout_qs = self.successful_transactions_qs.filter(
            transaction_form_type="CASHOUT"
        )
        total_cashout_amount = list(cashout_qs.aggregate(Sum("amount")).values())[0]
        previous_month_cashout_amount = list(
            cashout_qs.filter(date_created__lte=self.previous_month_end)
            .aggregate(Sum("amount"))
            .values()
        )[0]

        cashout_amount_change = get_percentage_diff(
            current=total_cashout_amount, previous=previous_month_cashout_amount
        )

        past_year_cashout_amt_qs = (
            cashout_qs.filter(date_created__gte=self.previous_year_current_month_start)
            .values("date_created__year", "date_created__month")
            .annotate(total_amount=Sum("amount"))
            .order_by("date_created__year")
        )

        past_year_cashout_list = [
            (
                data["date_created__year"],
                data["date_created__month"],
                data["total_amount"],
            )
            for data in past_year_cashout_amt_qs
        ]

        past_year_cashout_final_list = []
        for item in self.year_months_tuple_list:
            if item in [(tup[0], tup[1]) for tup in past_year_cashout_list]:
                for trans in past_year_cashout_list:
                    if item == (trans[0], trans[1]):
                        past_year_cashout_final_list.append(trans)
                        break
            else:
                past_year_cashout_final_list.append((0, 0, 0))
        cashout_amount_values_list = [
            trans[2] for trans in past_year_cashout_final_list
        ]

        # Commission Values List
        commission_qs = self.commission_qs.filter()
        total_commission_amount = list(commission_qs.aggregate(Sum("amount")).values())[
            0
        ]
        previous_month_commission_amount = list(
            commission_qs.filter(created_at__lte=self.previous_month_end)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        commission_amount_change = get_percentage_diff(
            current=total_commission_amount, previous=previous_month_commission_amount
        )

        past_year_commission_amt_qs = (
            commission_qs.filter(created_at__gte=self.previous_year_current_month_start)
            .values("created_at__year", "created_at__month")
            .annotate(total_commission=Sum("amount"))
            .order_by("created_at__year")
        )

        past_year_commission_list = [
            (
                data["created_at__year"],
                data["created_at__month"],
                data["total_commission"],
            )
            for data in past_year_commission_amt_qs
        ]

        past_year_commission_final_list = []
        for item in self.year_months_tuple_list:
            if item in [(tup[0], tup[1]) for tup in past_year_commission_list]:
                for trans in past_year_commission_list:
                    if item == (trans[0], trans[1]):
                        past_year_commission_final_list.append(trans)
                        break
            else:
                past_year_commission_final_list.append((0, 0, 0))
        commission_amount_values_list = [
            trans[2] for trans in past_year_commission_final_list
        ]

        # Past One Week Transactions Charts
        """
        This section provides data for transactions through the past 8 days.
        Let us say today is Thursday it would provide a list a daily transactions amount sums
        from last week's Thursday till today.
        """
        # Wallet Balance
        past_one_week_wallet_qs = (
            self.wallet_system_qs.filter(
                date_created__date__gte=self._previous_week_current_week_date
            )
            .values("date_created__date")
            .annotate(wallet_amount=Sum("available_balance"))
        )
        total_wallet_amount_past_week = list(
            past_one_week_wallet_qs.aggregate(Sum("wallet_amount")).values()
        )[0]
        last_week_wallet_amount = list(
            self.wallet_system_qs.filter(
                date_created__date__gte=self.previous_week_start,
                date_created__date__lte=self.previous_week_end,
            )
            .aggregate(Sum("available_balance"))
            .values()
        )[0]

        week_days_wallet_change = get_percentage_diff(
            current=total_wallet_amount_past_week, previous=last_week_wallet_amount
        )

        try:
            if past_one_week_wallet_qs:
                week_days_wallet_list = [
                    wallet["wallet_amount"] for wallet in past_one_week_wallet_qs
                ]
            else:
                week_days_wallet_list = [0] * 8
        except Exception as e:
            week_days_wallet_list = [0] * 8

        # Total Savings Amount
        past_one_week_savings_qs = (
            self.savings_transactions_qs.filter(
                date_created__date__gte=self._previous_week_current_week_date
            )
            .values("date_created__date")
            .annotate(transaction_amount=Sum("amount"))
        )

        total_savings_amount_past_week = list(
            past_one_week_savings_qs.aggregate(Sum("transaction_amount")).values()
        )[0]

        last_week_savings_amount = list(
            self.savings_transactions_qs.filter(
                date_created__date__gte=self.previous_week_start,
                date_created__date__lte=self.previous_week_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        try:
            if past_one_week_savings_qs:
                week_days_savings_list = [
                    transaction["transaction_amount"]
                    for transaction in past_one_week_savings_qs
                ]
            else:
                week_days_savings_list = [0] * 8
        except Exception as e:
            week_days_savings_list = [0] * 8

        week_days_savings_change = get_percentage_diff(
            current=total_savings_amount_past_week, previous=last_week_savings_amount
        )

        # Cashout
        past_one_week_cashout_qs = (
            cashout_qs.filter(
                date_created__date__gte=self._previous_week_current_week_date
            )
            .values("date_created__date")
            .annotate(transaction_amount=Sum("amount"))
        )

        total_cashout_amount_past_week = list(
            past_one_week_cashout_qs.aggregate(Sum("transaction_amount")).values()
        )[0]
        last_week_cashout_amount = list(
            cashout_qs.filter(
                date_created__date__gte=self.previous_week_start,
                date_created__date__lte=self.previous_week_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        try:
            if past_one_week_cashout_qs:
                week_days_cashout_list = [
                    transaction["transaction_amount"]
                    for transaction in past_one_week_cashout_qs
                ]
            else:
                week_days_cashout_list = [0] * 8
        except Exception as e:
            week_days_cashout_list = [0] * 8

        week_days_cashout_change = get_percentage_diff(
            current=total_cashout_amount_past_week, previous=last_week_cashout_amount
        )

        # Commission
        past_one_week_commission_qs = (
            commission_qs.filter(
                created_at__date__gte=self._previous_week_current_week_date
            )
            .values("created_at__date")
            .annotate(commission_amount=Sum("amount"))
        )

        total_commission_amount_past_week = list(
            past_one_week_commission_qs.aggregate(Sum("commission_amount")).values()
        )[0]
        last_week_commission_amount = list(
            commission_qs.filter(
                created_at__date__gte=self.previous_week_start,
                created_at__date__lte=self.previous_week_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        try:
            if past_one_week_commission_qs:
                week_days_commission_list = [
                    transaction["commission_amount"]
                    for transaction in past_one_week_commission_qs
                ]
            else:
                week_days_commission_list = [0] * 8
        except Exception as e:
            week_days_commission_list = [0] * 8

        week_days_commission_change = get_percentage_diff(
            current=total_commission_amount_past_week,
            previous=last_week_commission_amount,
        )

        # Past One Month Transactions Charts
        """
        This section provides data for transactions through the past 30/31/28/29 days.
        Let us say today is July 3rd it would provide a list a daily transactions amount sums
        from last months's July 3rd till today.
        """
        # Wallet Balance
        past_one_month_wallet_balance_qs = (
            self.wallet_system_qs.filter(
                date_created__date__gte=self._previous_month_current_month_date
            )
            .values("date_created__date")
            .annotate(transaction_amount=Sum("available_balance"))
            .values("transaction_amount")
        )

        total_wallet_balance_amount_past_month = list(
            past_one_month_wallet_balance_qs.aggregate(
                Sum("transaction_amount")
            ).values()
        )[0]

        last_month_wallet_balance_amount = list(
            self.wallet_system_qs.filter(
                date_created__date__gte=self.previous_month_start,
                date_created__date__lte=self.previous_month_end,
            )
            .aggregate(Sum("available_balance"))
            .values()
        )[0]

        month_days_wallet_balance_change = get_percentage_diff(
            current=total_wallet_balance_amount_past_month,
            previous=last_month_wallet_balance_amount,
        )

        try:
            if past_one_month_wallet_balance_qs:
                month_days_wallet_balance_list = [
                    transaction["transaction_amount"]
                    for transaction in past_one_month_wallet_balance_qs
                ]
            else:
                month_days_wallet_balance_list = [0] * 30
        except Exception as e:
            month_days_wallet_balance_list = [0] * 30

        # Savings Amount
        past_one_month_savings_qs = (
            self.savings_transactions_qs.filter(
                date_created__date__gte=self._previous_month_current_month_date
            )
            .values("date_created__date")
            .annotate(transaction_amount=Sum("amount"))
        )

        total_savings_amount_past_month = list(
            past_one_month_savings_qs.aggregate(Sum("transaction_amount")).values()
        )[0]

        last_month_savings_amount = list(
            self.savings_transactions_qs.filter(
                date_created__date__gte=self.previous_month_start,
                date_created__date__lte=self.previous_month_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        try:
            if past_one_month_savings_qs:
                month_days_savings_list = [
                    transaction["transaction_amount"]
                    for transaction in past_one_month_savings_qs
                ]
            else:
                month_days_savings_list = [0] * 8
        except Exception as e:
            month_days_savings_list = [0] * 8

        month_days_savings_change = get_percentage_diff(
            current=total_savings_amount_past_month, previous=last_month_savings_amount
        )

        # Commission Amount
        past_one_month_commission_qs = (
            commission_qs.filter(
                created_at__date__gte=self._previous_month_current_month_date
            )
            .values("created_at__date")
            .annotate(transaction_amount=Sum("amount"))
        )

        total_commission_amount_past_month = list(
            past_one_month_commission_qs.aggregate(Sum("transaction_amount")).values()
        )[0]

        last_month_commission_amount = list(
            commission_qs.filter(
                created_at__date__gte=self.previous_month_start,
                created_at__date__lte=self.previous_month_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        try:
            if past_one_month_commission_qs:
                month_days_commission_list = [
                    transaction["transaction_amount"]
                    for transaction in past_one_month_commission_qs
                ]
            else:
                month_days_commission_list = [0] * 8
        except Exception as e:
            month_days_commission_list = [0] * 8

        month_days_commission_change = get_percentage_diff(
            current=total_commission_amount_past_month,
            previous=last_month_commission_amount,
        )

        # Cashout
        past_one_month_cashout_qs = (
            cashout_qs.filter(
                date_created__date__gte=self._previous_month_current_month_date
            )
            .values("date_created__date")
            .annotate(cashout_amount=Sum("amount"))
        )

        total_cashout_amount_past_month = list(
            past_one_month_cashout_qs.aggregate(Sum("cashout_amount")).values()
        )[0]

        last_month_cashout_amount = list(
            cashout_qs.filter(
                date_created__date__gte=self.previous_month_start,
                date_created__date__lte=self.previous_month_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        try:
            if past_one_month_cashout_qs:
                month_days_cashout_list = [
                    transaction["cashout_amount"]
                    for transaction in past_one_month_cashout_qs
                ]
            else:
                month_days_cashout_list = [0] * 8
        except Exception as e:
            month_days_cashout_list = [0] * 8

        month_days_cashout_change = get_percentage_diff(
            current=total_cashout_amount_past_month, previous=last_month_cashout_amount
        )

        chart_data = {
            "months_list": (
                self.days_label
                if self.filter == "weekly"
                else (
                    self._month_2_month_dates_list
                    if self.filter == "monthly"
                    else self.months_list_names
                )
            ),
            "wallet_balance": (
                {
                    "total_balance": (
                        last_week_wallet_amount if last_week_wallet_amount else 0.00
                    ),
                    "wallet_balance_chart_values": week_days_wallet_list,
                    "percentage": week_days_wallet_change.get("percentage"),
                    "change": week_days_wallet_change.get("change"),
                }
                if self.filter == "weekly"
                else (
                    {
                        "total_balance": last_month_wallet_balance_amount or 0.00,
                        "wallet_balance_chart_values": month_days_wallet_balance_list,
                        "percentage": month_days_wallet_balance_change.get(
                            "percentage"
                        ),
                        "change": month_days_wallet_balance_change.get("change"),
                    }
                    if self.filter == "monthly"
                    else {
                        "total_balance": total_wallet_balance_amount or 0.00,
                        "wallet_balance_chart_values": wallet_balance_values_list,
                        "percentage": total_wallet_balance_amount_change.get(
                            "percentage"
                        ),
                        "change": total_wallet_balance_amount_change.get("change"),
                    }
                )
            ),
            "total_savings": (
                {
                    "total_savings": total_savings_amount_past_week or 0.00,
                    "savings_chart_values": week_days_savings_list,
                    "percentage": week_days_savings_change.get("percentage"),
                    "change": week_days_savings_change.get("change"),
                }
                if self.filter == "weekly"
                else (
                    {
                        "total_savings": total_savings_amount_past_month or 0.00,
                        "savings_chart_values": month_days_savings_list,
                        "percentage": month_days_savings_change.get("percentage"),
                        "change": month_days_savings_change.get("change"),
                    }
                    if self.filter == "monthly"
                    else {
                        "total_savings": total_savings_amount or 0.00,
                        "savings_chart_values": savings_amount_values_list,
                        "percentage": savings_amount_change.get("percentage"),
                        "change": savings_amount_change.get("change"),
                    }
                )
            ),
            "total_cashout": (
                {
                    "total_cashout": last_week_cashout_amount or 0.00,
                    "cashout_chart_values": week_days_cashout_list,
                    "percentage": week_days_cashout_change.get("percentage"),
                    "change": week_days_cashout_change.get("change"),
                }
                if self.filter == "weekly"
                else (
                    {
                        "total_cashout": last_month_cashout_amount or 0.00,
                        "cashout_chart_values": month_days_cashout_list,
                        "percentage": month_days_cashout_change.get("percentage"),
                        "change": month_days_cashout_change.get("change"),
                    }
                    if self.filter == "monthly"
                    else {
                        "total_cashout": total_cashout_amount or 0.00,
                        "cashout_chart_values": cashout_amount_values_list,
                        "percentage": cashout_amount_change.get("percentage"),
                        "change": cashout_amount_change.get("change"),
                    }
                )
            ),
            "total_commission": (
                {
                    "total_commission": last_week_commission_amount or 0.00,
                    "commission_chart_values": week_days_commission_list,
                    "percentage": week_days_commission_change.get("percentage"),
                    "change": week_days_commission_change.get("change"),
                }
                if self.filter == "weekly"
                else (
                    {
                        "total_commission": last_month_commission_amount or 0.00,
                        "commission_chart_values": month_days_commission_list,
                        "percentage": month_days_commission_change.get("percentage"),
                        "change": month_days_commission_change.get("change"),
                    }
                    if self.filter == "monthly"
                    else {
                        "total_commission": total_commission_amount or 0.00,
                        "commission_chart_values": commission_amount_values_list,
                        "percentage": commission_amount_change.get("percentage"),
                        "change": commission_amount_change.get("change"),
                    }
                )
            ),
        }
        return chart_data


class Agent:
    def __init__(self, request):
        self.week_start = DateUtility().week_start
        self.previous_week_start = DateUtility().previous_week_start
        self.previous_week_end = DateUtility().previous_week_end
        self.month_start = DateUtility().month_start
        self.previous_month_start = DateUtility().previous_month_start
        self.previous_month_end = DateUtility().previous_month_end
        self.year_start = DateUtility().year_start
        self.previous_year_start = DateUtility().previous_year_start
        self.previous_year_end = DateUtility().previous_year_end
        self.previous_day = DateUtility().previous_day
        self.start_of_all_transactions = DateUtility().start_of_all_transactions

        self.date_filter = DateUtility().get_date_filter(request=request)
        self.agent_filter = request.GET.get("agent-filter")
        self.request = request

        self.successful_transactions_qs = Transaction.objects.filter(status="SUCCESS")
        self.ajo_transactions_qs = self.successful_transactions_qs.filter(
            plan_type="AJO", wallet_type="AJO_USER", status="SUCCESS"
        )

        self.ajo_savings_qs = AjoSaving.objects.filter()
        self.ajo_users_qs = AjoUser.objects.all()
        self.collectors_qs = self.ajo_savings_qs.values("user").distinct("user")
        self.custom_users_qs = get_user_model().objects.all()
        # self.commission_qs = Commission.objects.filter(plan_type="AJO")

    def get_agent_overview(self):
        collectors_based_on_collections_qs = (
            self.ajo_transactions_qs.values("user")
            .annotate(collections_count=Count("user"))
            .filter(collections_count__gte=7)
        )

        all_collectors_count = collectors_based_on_collections_qs.count()
        previous_month_all_collectors_count = collectors_based_on_collections_qs.filter(
            user__date_joined__lte=self.previous_month_end
        )
        new_collectors_count = collectors_based_on_collections_qs.filter(
            user__date_joined__gte=self.month_start
        )
        previous_month_new_collectors_count = collectors_based_on_collections_qs.filter(
            user__date_joined__gte=self.previous_month_start,
            user__date_joined__lte=self.previous_month_end,
        )

        collectors_change = get_percentage_diff(
            current=new_collectors_count,
            previous=previous_month_all_collectors_count.count(),
        )
        new_collectors_change = get_percentage_diff(
            current=new_collectors_count.count(),
            previous=previous_month_new_collectors_count.count(),
        )
        active_collectors_count = collectors_based_on_collections_qs.filter(
            user__performance_status="ACTIVE"
        ).count()
        previous_month_active_collectors_count = (
            collectors_based_on_collections_qs.filter(
                user__performance_status="ACTIVE",
                user__date_joined__lte=self.previous_month_end,
            )
        )
        active_collectors_change = get_percentage_diff(
            current=active_collectors_count,
            previous=previous_month_active_collectors_count.count(),
        )

        inactive_collectors_count = collectors_based_on_collections_qs.filter(
            user__performance_status="INACTIVE"
        ).count()
        previous_month_inactive_collectors_count = (
            collectors_based_on_collections_qs.filter(
                user__performance_status="INACTIVE",
                user__date_joined__lte=self.previous_month_end,
            )
        )
        inactive_collectors_change = get_percentage_diff(
            current=inactive_collectors_count,
            previous=previous_month_inactive_collectors_count,
        )

        partially_inactive_collectors_count = collectors_based_on_collections_qs.filter(
            user__performance_status="PARTIALLY_INACTIVE"
        ).count()
        previous_month_partially_inactive_collectors_count = (
            collectors_based_on_collections_qs.filter(
                user__performance_status="PARTIALLY_INACTIVE",
                user__date_joined__lte=self.previous_month_end,
            ).count()
        )
        partially_inactive_collectors_change = get_percentage_diff(
            current=partially_inactive_collectors_count,
            previous=previous_month_partially_inactive_collectors_count,
        )

        suspended_collectors_count = collectors_based_on_collections_qs.filter(
            user__performance_status="SUSPENDED"
        ).count()
        previous_month_suspended_collectors_count = (
            collectors_based_on_collections_qs.filter(
                user__performance_status="SUSPENDED",
                user__date_joined__lte=self.previous_month_end,
            )
        )
        suspended_collectors_change = get_percentage_diff(
            current=suspended_collectors_count,
            previous=previous_month_suspended_collectors_count.count(),
        )

        data = {
            "all_agents": {
                "count": all_collectors_count,
                "percentage": collectors_change.get("percentage"),
                "change": collectors_change.get("change"),
            },
            "new_agents": {
                "count": new_collectors_count.count(),
                "percentage": new_collectors_change.get("percentage"),
                "change": new_collectors_change.get("change"),
            },
            "active_agents": {
                "count": active_collectors_count,
                "percentage": active_collectors_change.get("percentage"),
                "change": active_collectors_change.get("change"),
            },
            "inactive_agents": {
                "count": inactive_collectors_count,
                "percentage": inactive_collectors_change.get("percentage"),
                "change": inactive_collectors_change.get("change"),
            },
            "suspended_agents": {
                "count": suspended_collectors_count,
                "percentage": suspended_collectors_change.get("percentage"),
                "change": suspended_collectors_change.get("change"),
            },
            "partially_inactive_agents": {
                "count": partially_inactive_collectors_count,
                "percentage": partially_inactive_collectors_change.get("percentage"),
                "change": partially_inactive_collectors_change.get("change"),
            },
        }
        return data

    def get_top_agents(self):
        # Top 10
        discrete_transaction_amounts_overall = []
        for collector in self.ajo_users_qs.distinct("user"):
            collector = collector.user
            total_transactions_amount = list(
                self.successful_transactions_qs.filter(
                    user=collector, **self.date_filter
                )
                .aggregate(Sum("amount"))
                .values()
            )[0]
            total_transactions_count = list(
                self.successful_transactions_qs.filter(
                    user=collector, **self.date_filter
                )
                .aggregate(Count("amount"))
                .values()
            )[0]

            data = {
                "id": collector.id,
                "agent_id": collector.customer_user_id,
                "agent_name": collector.email,
                "agent_email": collector.email,
                "transaction_amount": total_transactions_amount or 0.00,
                "transaction_count": total_transactions_count,
                "agent_location": "",
            }
            discrete_transaction_amounts_overall.append(data)
        amounts_sorted_list = sorted(
            discrete_transaction_amounts_overall, key=lambda d: d["transaction_amount"]
        )[::-1][:10]
        counts_sorted_list = sorted(
            discrete_transaction_amounts_overall, key=lambda d: d["transaction_count"]
        )[::-1][:10]

        data = {
            "transactions_amount": amounts_sorted_list,
            "transactions_count": counts_sorted_list,
        }
        return data

    def get_agents_table(self):
        collectors_based_on_collections_qs = (
            self.ajo_transactions_qs.values("user")
            .annotate(collections_count=Count("user"))
            .filter(collections_count__gte=7)
        )

        collectors_based_collections_list = [
            trans["user"] for trans in collectors_based_on_collections_qs
        ]

        collectors_list = []
        for collector in self.ajo_users_qs.distinct("user"):
            collector = collector.user

            if collector and collector.id in collectors_based_collections_list:
                collector_transactions_amount = list(
                    self.successful_transactions_qs.filter(
                        user=collector, **self.date_filter
                    )
                    .aggregate(Sum("amount"))
                    .values()
                )[0]
                number_of_savers = (
                    self.ajo_savings_qs.filter(user=collector)
                    .distinct("ajo_user")
                    .count()
                )
                collector_branch = collector.branch_users.last()

                agent_details = {
                    "name": collector.email,
                    "id": collector.id,
                    "email": collector.email,
                    "branch": collector_branch.name or "",
                    "location": collector_branch.name or "",
                    "phone_number": "",
                    "savers": number_of_savers,
                    "transactions": collector_transactions_amount or 0.00,
                    "status": collector.performance_status,
                }
                collectors_list.append(agent_details)
            else:
                pass

        data = {"collectors_list": collectors_list, "count": len(collectors_list)}
        return data

    def get_active_agents_table(self):
        collectors_based_on_collections_qs = (
            self.ajo_transactions_qs.values("user")
            .annotate(collections_count=Count("user"))
            .filter(collections_count__gte=7)
        )
        collectors_based_collections_list = [
            trans["user"] for trans in collectors_based_on_collections_qs
        ]

        collectors_list = []
        for collector in self.ajo_users_qs.distinct("user").filter(
            user__performance_status="ACTIVE"
        ):
            collector = collector.user

            if collector and collector.id in collectors_based_collections_list:
                collector_transactions_amount = list(
                    self.successful_transactions_qs.filter(
                        user=collector, **self.date_filter
                    )
                    .aggregate(Sum("amount"))
                    .values()
                )[0]
                number_of_savers = (
                    self.ajo_savings_qs.filter(user=collector)
                    .distinct("ajo_user")
                    .count()
                )
                collector_branch = collector.branch_users.all()

                agent_details = {
                    "name": collector.email,
                    "id": collector.id,
                    "email": collector.email,
                    "branch": collector_branch[0].name or "",
                    "location": collector_branch[0].name or "",
                    "phone_number": "",
                    "savers": number_of_savers,
                    "transactions": collector_transactions_amount or 0.00,
                    "status": collector.performance_status,
                }
                collectors_list.append(agent_details)
            else:
                pass

        data = {"active_agents_list": collectors_list, "count": len(collectors_list)}
        return data

    def get_inactive_agents_table(self):
        collectors_based_on_collections_qs = (
            self.ajo_transactions_qs.values("user")
            .annotate(collections_count=Count("user"))
            .filter(collections_count__gte=7)
        )
        collectors_based_collections_list = [
            trans["user"] for trans in collectors_based_on_collections_qs
        ]

        collectors_list = []
        for collector in self.ajo_users_qs.distinct("user").filter(
            user__performance_status__in=["INACTIVE", "PARTIALLY_INACTIVE"]
        ):
            collector = collector.user

            if collector and collector.id in collectors_based_collections_list:
                collector_transactions_amount = list(
                    self.successful_transactions_qs.filter(
                        user=collector, **self.date_filter
                    )
                    .aggregate(Sum("amount"))
                    .values()
                )[0]
                number_of_savers = (
                    self.ajo_savings_qs.filter(user=collector)
                    .distinct("ajo_user")
                    .count()
                )
                collector_branch = collector.branch_users.last()

                agent_details = {
                    "name": collector.email,
                    "id": collector.id,
                    "email": collector.email,
                    "branch": collector_branch.name or "",
                    "location": collector_branch.name or "",
                    "phone_number": "",
                    "savers": number_of_savers,
                    "transactions": collector_transactions_amount or 0.00,
                    "status": collector.performance_status,
                }
                collectors_list.append(agent_details)
            else:
                pass

        data = {"inactive_agents_list": collectors_list, "count": len(collectors_list)}
        return data

    def get_suspended_agents_table(self):
        collectors_based_on_collections_qs = (
            self.ajo_transactions_qs.values("user")
            .annotate(collections_count=Count("user"))
            .filter(collections_count__gte=7)
        )
        collectors_based_collections_list = [
            trans["user"] for trans in collectors_based_on_collections_qs
        ]

        collectors_list = []
        for collector in self.ajo_users_qs.distinct("user").filter(
            user__performance_status="SUSPENDED"
        ):
            collector = collector.user

            if collector and collector.id in collectors_based_collections_list:
                collector_transactions_amount = list(
                    self.successful_transactions_qs.filter(
                        user=collector, **self.date_filter
                    )
                    .aggregate(Sum("amount"))
                    .values()
                )[0]
                number_of_savers = (
                    self.ajo_savings_qs.filter(user=collector)
                    .distinct("ajo_user")
                    .count()
                )
                collector_branch = collector.branch_users.last()

                agent_details = {
                    "name": collector.email,
                    "id": collector.id,
                    "email": collector.email,
                    "branch": collector_branch.name or "",
                    "location": collector_branch.name or "",
                    "phone_number": "",
                    "savers": number_of_savers,
                    "transactions": collector_transactions_amount or 0.00,
                    "status": collector.performance_status,
                }
                collectors_list.append(agent_details)
            else:
                pass

        data = {"suspended_agents_list": collectors_list, "count": len(collectors_list)}
        return data


class AgentDetails:
    def __init__(self, request):
        self.week_start = DateUtility().week_start
        self.previous_week_start = DateUtility().previous_week_start
        self.previous_week_end = DateUtility().previous_week_end
        self.month_start = DateUtility().month_start
        self.previous_month_start = DateUtility().previous_month_start
        self.previous_month_end = DateUtility().previous_month_end
        self.year_start = DateUtility().year_start
        self.previous_year_start = DateUtility().previous_year_start
        self.previous_year_end = DateUtility().previous_year_end
        self.previous_day = DateUtility().previous_day
        self.start_of_all_transactions = DateUtility().start_of_all_transactions
        self.date_filter = DateUtility().get_date_filter(request=request)

        self.collector_qs = get_user_model().objects.filter()
        self.ajo_users_qs = AjoUser.objects.all()
        self.transactions_qs = Transaction.objects.filter(plan_type="AJO")
        self.successful_transactions_qs = self.transactions_qs.filter(status="SUCCESS")
        self.wallet_system_qs = WalletSystem.objects.filter(
            wallet_type__in=["AJO_USER", "AJO_AGENT"]
        )
        self.savings_transactions_qs = self.successful_transactions_qs.filter(
            plan_type="AJO", wallet_type="AJO_USER", status="SUCCESS"
        )
        self.sessions_qs = Session.objects.filter()
        self.commission_qs = Commission.objects.filter(plan_type="AJO")

    def get_agent_detail(self, id):
        collector = self.collector_qs.get(id=id)
        active_sessions_qs = self.sessions_qs.filter(expire_date__lte=timezone.now())
        acive_sessions_ids = [
            data.get_decoded().get("_auth_user_id", "-") for data in active_sessions_qs
        ]

        data = {
            "personal_data": {
                "id": collector.id,
                "agent_name": collector.email,
                "phone_number": "",
                "last_seen": collector.last_login,
                "status": "online" if collector.id in acive_sessions_ids else "offline",
                "email": collector.email,
                "terminal_status": collector.performance_status,
            }
        }
        return data

    def get_agent_wallets(self, id):
        collector = self.collector_qs.get(id=id)

        ajo_main_wallet = self.wallet_system_qs.filter(user=collector).last()
        total_savings_amount = list(
            self.savings_transactions_qs.filter(user=collector)
            .aggregate(Sum("amount"))
            .values()
        )[0]

        # Cashout/Withdrawal
        cashout_transactions_amount = list(
            self.successful_transactions_qs.filter(
                transaction_form_type="CASHOUT", user=collector
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        cashout_transactions_amount_today = list(
            self.successful_transactions_qs.filter(
                date_created__date=timezone.now().date(),
                transaction_form_type="CASHOUT",
                user=collector,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        cashout_transactions_count = list(
            self.successful_transactions_qs.filter(
                transaction_form_type="CASHOUT", user=collector
            )
            .aggregate(Count("amount"))
            .values()
        )[0]
        cashout_transactions_count_today = list(
            self.successful_transactions_qs.filter(
                date_created__date=timezone.now().date(),
                transaction_form_type="CASHOUT",
                user=collector,
            )
            .aggregate(Count("amount"))
            .values()
        )[0]

        total_commission_amount = list(
            self.commission_qs.filter(user=collector, commission_type="AGENT")
            .aggregate(Sum("amount"))
            .values()
        )[0]

        # Transaction Amount
        total_transaction_amount = list(
            self.successful_transactions_qs.filter(user=collector)
            .aggregate(Sum("amount"))
            .values()
        )[0]

        total_transaction_amount_today = list(
            self.successful_transactions_qs.filter(
                user=collector, date_created__date=timezone.now().date()
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        # Transaction Count
        total_transaction_count = list(
            self.successful_transactions_qs.filter(
                user=collector,
            )
            .aggregate(Count("amount"))
            .values()
        )[0]

        total_transaction_count_today = list(
            self.successful_transactions_qs.filter(
                user=collector, date_created__date=timezone.now().date()
            )
            .aggregate(Count("amount"))
            .values()
        )[0]

        # Transfer
        total_transfer_amount = list(
            self.successful_transactions_qs.filter(
                transaction_form_type__in=["TRANSFER"], user=collector
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        total_transfer_amount_today = list(
            self.successful_transactions_qs.filter(
                transaction_form_type__in=["TRANSFER"],
                user=collector,
                date_created__date=timezone.now().date(),
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        total_transfer_count = list(
            self.successful_transactions_qs.filter(
                transaction_form_type__in=["TRANSFER"], user=collector
            )
            .aggregate(Count("amount"))
            .values()
        )[0]

        total_transfer_count_today = list(
            self.successful_transactions_qs.filter(
                transaction_form_type__in=["TRANSFER"],
                user=collector,
                date_created__date=timezone.now().date(),
            )
            .aggregate(Count("amount"))
            .values()
        )[0]

        # Bills and Utilities
        total_airtime_data_amount = list(
            self.successful_transactions_qs.filter(
                transaction_form_type__in=["AIRTIME"], user=collector
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_cable_tv_amount = list(
            self.successful_transactions_qs.filter(
                transaction_form_type__in=["CABLE_TV"], user=collector
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        total_bills_amount = list(
            self.successful_transactions_qs.filter(
                transaction_form_type__in=["BILLS_AND_UTILITY", "SUBSCRIPTION"],
                user=collector,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        data = {
            "general": {
                "ajo_main_wallet": (
                    ajo_main_wallet.available_balance
                    if ajo_main_wallet and ajo_main_wallet.available_balance
                    else 0.00
                ),
                "total_savings": total_savings_amount or 0.00,
                "total_cashout": cashout_transactions_amount or 0.00,
                "total_commission": total_commission_amount or 0.00,
            },
            "others": {
                "total_transactions": total_transaction_amount or 0.00,
                "total_transactions_amount_today": total_transaction_amount_today
                or 0.00,
                "total_withdrawal": cashout_transactions_amount or 0.00,
                "total_withdrawal_today": cashout_transactions_amount_today or 0.00,
                "transaction_counts": total_transaction_count,
                "transaction_counts_today": total_transaction_count_today,
                "total_commission": total_commission_amount or 0.00,
                "airtime_data": total_airtime_data_amount or 0.00,
                "cable_tv": total_cable_tv_amount or 0.00,
                "subscriptions": total_bills_amount or 0.00,
                "bills": total_bills_amount or 0.00,
                "total_transfer_amount": total_transfer_amount or 0.00,
                "total_transfer_amount_today": total_transfer_amount_today or 0.00,
                "total_withdrawal_count": cashout_transactions_count,
                "total_withdrawal_count_today": cashout_transactions_count_today,
                "total_transfer_count": total_transfer_count or 0,
                "total_transfer_count_today": total_transfer_count_today or 0,
            },
        }
        return data

    def get_agent_transactions(self, id):
        collector = self.collector_qs.get(id=id)
        collector_transactions = self.transactions_qs.filter(
            user=collector, **self.date_filter
        )

        collector_transactions_list = []
        for transaction in collector_transactions:
            transactions_data = {
                "id": transaction.id,
                "description": (
                    "Cashout"
                    if transaction.transaction_form_type in ["CASHOUT"]
                    else (
                        "Transfer"
                        if transaction.transaction_form_type in ["TRANSFER"]
                        else (
                            "Bills and Utilities"
                            if transaction.transaction_form_type
                            in ["BILLS_AND_UTILITIES", "AIRTIME_PIN"]
                            else "Others"
                        )
                    )
                ),
                "amount": transaction.amount if transaction.amount else 0.00,
                "transaction_count": 1,
                "refrence_id": (
                    transaction.unique_reference if transaction.unique_reference else ""
                ),
                "date": transaction.date_created,
                "customer_name": "",
                "plan": transaction.plan_type,
                "status": transaction.status,
                "reference_id": (
                    transaction.unique_reference if transaction.unique_reference else ""
                ),
                "date": transaction.date_created,
            }
            collector_transactions_list.append(transactions_data)

        data = {
            "transactions_table": collector_transactions_list,
            "count": len(collector_transactions_list),
        }
        return data

    def get_overall_transactions_list(self):
        # collector = self.collector_qs.get(id=id)
        collector_transactions = self.transactions_qs

        overall_transactions_list = cache.get("overall_transactions_list")

        if not overall_transactions_list:
            agent_transactions_list = []
            for transaction in collector_transactions:
                transactions_data = {
                    "id": transaction.id,
                    "description": (
                        "Cashout"
                        if transaction.transaction_form_type in ["CASHOUT"]
                        else (
                            "Transfer"
                            if transaction.transaction_form_type in ["TRANSFER"]
                            else (
                                "Bills and Utilities"
                                if transaction.transaction_form_type
                                in ["BILLS_AND_UTILITIES", "AIRTIME_PIN"]
                                else "Others"
                            )
                        )
                    ),
                    "amount": transaction.amount if transaction.amount else 0.00,
                    "transaction_count": 1,
                    "refrence_id": (
                        transaction.unique_reference
                        if transaction.unique_reference
                        else ""
                    ),
                    "date": transaction.date_created,
                    "customer_name": "",
                    "plan": transaction.plan_type,
                    "status": transaction.status,
                    "reference_id": (
                        transaction.unique_reference
                        if transaction.unique_reference
                        else ""
                    ),
                    "date": transaction.date_created,
                }
                agent_transactions_list.append(transactions_data)
                cache.set(
                    "overall_transactions_list",
                    value=agent_transactions_list,
                    timeout=60 * 30,
                )
        else:
            agent_transactions_list = overall_transactions_list

        data = {
            "transactions_table": agent_transactions_list,
            "count": len(agent_transactions_list),
        }
        return data

    def get_transaction_detail(self, id):
        transaction = self.transactions_qs.get(id=id)

        transaction_details = {
            "status": (transaction.status).title() if transaction.status else "",
            "reason_for_decline": transaction.failure_reason,
            "description": (
                "Cashout"
                if transaction.transaction_type in ["CASHOUT"]
                else (
                    "Transfer"
                    if transaction.transaction_type in ["TRANSFER"]
                    else (
                        "Bills and Utilities"
                        if transaction.transaction_type
                        in ["BILLS_AND_UTILITIES", "AIRTIME_PIN"]
                        else "Others"
                    )
                )
            ),
            "sender": "",
            "receiver_name": "",
            "receiver_bank": "",
            "receiver_account_number": "",
            "amount": transaction.amount if transaction.amount else 0.00,
            "narration": transaction.description if transaction.description else "",
            "charges": 0,
            "reference_id": (
                transaction.unique_reference if transaction.unique_reference else ""
            ),
            "date": transaction.date_created,
        }
        return transaction_details

    def get_agent_customers_list(self, id):
        collector = self.collector_qs.get(id=id)
        collector_customers = self.ajo_users_qs.filter(user=collector)

        agent_customers_list = []
        for customer in collector_customers:
            data = {
                "id": customer.id,
                "name": customer.first_name,
                "phone_number": customer.phone_number,
                "trade": customer.trade_location,
                "address": customer.address,
                "gender": customer.gender,
                "marital_status": customer.marital_status,
                "status": "",
            }
            agent_customers_list.append(data)

        response = {"agents": agent_customers_list, "count": len(agent_customers_list)}
        return response

    def get_agent_active_session(self, id):
        collector = self.collector_qs.get(id=id)
        collector_sessions = self.sessions_qs.order_by("-expire_date")
        # collector_location = self.retail_system_qs.filter(retail_account=collector).last()

        collector_sessions_list = []
        for session in collector_sessions:
            user_id = session.get_decoded().get("_auth_user_id", "-")

            if user_id == id:
                data = {
                    "id": id,
                    "log_time": session.expire_date - timezone.timedelta(days=12),
                    "session_duration": "2 hours 3 mins",
                    "device": "",
                    "location": "",
                }
                collector_sessions_list.append(data)
            else:
                pass

        data = {
            "active_sessions": collector_sessions_list,
            "count": len(collector_sessions_list),
        }
        return data

    def get_agent_revenue_overview(self, id):
        """
        Provides metrics for Revenue generation of Liberty Retail Agents.
        The daily revenue amount is derived from the LedgerModelTable model.
        A field named "charge_sum" holds the value for every revenue generated
        per transaction record.
        """
        collector = self.collector_qs.get(id=id)
        collector_date_joined = collector.date_joined
        number_of_days_since_joined = (
            datetime.now().date() - collector_date_joined.date()
        ).days
        number_of_days_since_joined_less_weekends = (
            number_of_days_since_joined
            - floor(number_of_days_since_joined / 7)
            + (number_of_days_since_joined % 7)
        )

        collector_transactions = self.successful_transactions_qs.filter(user=collector)
        revenue_qs = self.successful_transactions_qs.filter(
            user=collector, transaction_form_type__in=["COMMISSION"]
        )
        total_revenue_count = list(revenue_qs.aggregate(Count("amount")).values())[0]

        # Revenue Amount
        total_revenue_amount = list(
            revenue_qs.filter(**self.date_filter).aggregate(Sum("amount")).values()
        )[0]
        total_revenue_amount_today = list(
            revenue_qs.filter(date_created__date=timezone.now().date())
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_revenue_amount_yesterday = list(
            revenue_qs.filter(date_created__date=self.previous_day.date())
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_revenue_amount_this_week = list(
            revenue_qs.filter(date_created__gte=self.week_start)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_revenue_amount_this_month = list(
            revenue_qs.filter(date_created__gte=self.month_start)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_revenue_amount_this_year = list(
            revenue_qs.filter(date_created__gte=self.year_start)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_revenue_amount_last_year = list(
            revenue_qs.filter(
                date_created__gte=self.previous_year_start,
                date_created__lte=self.previous_year_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_revenue_amount_last_week = list(
            revenue_qs.filter(
                date_created__gte=self.previous_week_start,
                date_created__lte=self.previous_week_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_revenue_amount_last_month = list(
            revenue_qs.filter(
                date_created__gte=self.previous_month_start,
                date_created__lte=self.previous_month_end,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        # Revenue Count
        total_revenue_count = list(
            revenue_qs.filter(**self.date_filter).aggregate(Count("amount")).values()
        )[0]
        total_revenue_count_today = list(
            revenue_qs.filter(date_created__date=timezone.now().date())
            .aggregate(Count("amount"))
            .values()
        )[0]
        total_revenue_count_yesterday = list(
            revenue_qs.filter(date_created__date=self.previous_day.date())
            .aggregate(Count("amount"))
            .values()
        )[0]
        total_revenue_count_this_week = list(
            revenue_qs.filter(date_created__gte=self.week_start)
            .aggregate(Count("amount"))
            .values()
        )[0]
        total_revenue_count_this_month = list(
            revenue_qs.filter(date_created__gte=self.month_start)
            .aggregate(Count("amount"))
            .values()
        )[0]
        total_revenue_count_this_year = list(
            revenue_qs.filter(date_created__gte=self.year_start)
            .aggregate(Count("amount"))
            .values()
        )[0]
        total_revenue_count_last_year = list(
            revenue_qs.filter(
                date_created__gte=self.previous_year_start,
                date_created__lte=self.previous_year_end,
            )
            .aggregate(Count("amount"))
            .values()
        )[0]
        total_revenue_count_last_week = list(
            revenue_qs.filter(
                date_created__gte=self.previous_week_start,
                date_created__lte=self.previous_week_end,
            )
            .aggregate(Count("amount"))
            .values()
        )[0]
        total_revenue_count_last_month = list(
            revenue_qs.filter(
                date_created__gte=self.previous_month_start,
                date_created__lte=self.previous_month_end,
            )
            .aggregate(Count("amount"))
            .values()
        )[0]

        # Revenue Target Amount
        daily_target_amount = 3000
        overall_revenue_target_amount = (
            daily_target_amount * number_of_days_since_joined_less_weekends
        )
        total_revenue_target_amount_today = daily_target_amount
        total_revenue_target_amount_yesterday = daily_target_amount
        total_revenue_target_amount_this_week = total_revenue_target_amount_today * 6
        total_revenue_target_amount_this_month = total_revenue_target_amount_today * 24
        total_revenue_target_amount_this_year = (
            total_revenue_target_amount_today * 24 * 12
        )
        total_revenue_target_amount_last_year = daily_target_amount * 24 * 12
        total_revenue_target_amount_last_week = daily_target_amount * 6
        total_revenue_target_amount_last_month = daily_target_amount * 24

        # Revenue Target Count
        daily_target_count = 13
        overall_revenue_target_count = (
            daily_target_count * number_of_days_since_joined_less_weekends
        )
        total_revenue_target_count_today = daily_target_count
        total_revenue_target_count_yesterday = daily_target_count
        total_revenue_target_count_this_week = total_revenue_target_count_today * 6
        total_revenue_target_count_this_month = total_revenue_target_count_today * 24
        total_revenue_target_count_this_year = (
            total_revenue_target_count_today * 24 * 12
        )
        total_revenue_target_count_last_year = daily_target_count * 24 * 12
        total_revenue_target_count_last_week = daily_target_count * 6
        total_revenue_target_count_last_month = daily_target_count * 24

        average_daily_revenue_amount = (
            total_revenue_amount if total_revenue_amount else 0.00
        ) / (number_of_days_since_joined_less_weekends or 1)
        average_daily_revenue_count = total_revenue_count / (
            number_of_days_since_joined_less_weekends or 1
        )
        data = {
            "revenue_amount": {
                "total_amount": total_revenue_amount or 0.00,
                "average_daily_amount": average_daily_revenue_amount or 0.00,
                "amount_today": total_revenue_amount_today or 0.00,
                "amount_this_week": total_revenue_amount_this_week or 0.00,
                "amount_this_month": total_revenue_amount_this_month or 0.00,
                "amount_last_month": total_revenue_amount_last_month or 0.00,
                "amount_yesterday": total_revenue_amount_yesterday or 0.00,
                "amount_last_week": total_revenue_amount_last_week or 0.00,
                "amount_this_year": total_revenue_amount_this_year or 0.00,
                "amount_last_year": total_revenue_amount_last_year or 0.00,
            },
            "revenue_count": {
                "total_count": total_revenue_count,
                "average_daily_count": average_daily_revenue_count,
                "count_today": total_revenue_count_today,
                "count_this_week": total_revenue_count_this_week,
                "count_this_month": total_revenue_count_this_month,
                "count_last_month": total_revenue_count_last_month,
                "count_yesterday": total_revenue_count_yesterday,
                "count_last_week": total_revenue_count_last_week,
                "count_this_year": total_revenue_count_this_year,
                "count_last_year": total_revenue_count_last_year,
            },
            "target_revenue_amount": {
                "overall_amount": overall_revenue_target_amount or 0.00,
                "amount_today": total_revenue_target_amount_today or 0.00,
                "amount_this_week": total_revenue_target_amount_this_week or 0.00,
                "amount_this_month": total_revenue_target_amount_this_month or 0.00,
                "amount_last_month": total_revenue_target_amount_last_month or 0.00,
                "amount_yesterday": total_revenue_target_amount_yesterday or 0.00,
                "amount_last_week": total_revenue_target_amount_last_week or 0.00,
                "amount_this_year": total_revenue_target_amount_this_year or 0.00,
                "amount_last_year": total_revenue_target_amount_last_year or 0.00,
            },
            "target_revenue_count": {
                "total_count": overall_revenue_target_count,
                "count_today": total_revenue_target_count_today,
                "count_this_week": total_revenue_target_count_this_week,
                "count_this_month": total_revenue_target_count_this_month,
                "count_last_month": total_revenue_target_count_last_month,
                "count_yesterday": total_revenue_target_count_yesterday,
                "count_last_week": total_revenue_target_count_last_week,
                "count_this_year": total_revenue_target_count_this_year,
                "count_last_year": total_revenue_target_count_last_year,
            },
            "average_daily_amount": {
                "average_daily_revenue_amount": average_daily_revenue_amount
            },
        }
        return data


class CustomerDetail:
    def __init__(self, request):
        self.week_start = DateUtility().week_start
        self.previous_week_start = DateUtility().previous_week_start
        self.previous_week_end = DateUtility().previous_week_end
        self.month_start = DateUtility().month_start
        self.previous_month_start = DateUtility().previous_month_start
        self.previous_month_end = DateUtility().previous_month_end
        self.year_start = DateUtility().year_start
        self.previous_year_start = DateUtility().previous_year_start
        self.previous_year_end = DateUtility().previous_year_end
        self.previous_day = DateUtility().previous_day
        self.start_of_all_transactions = DateUtility().start_of_all_transactions

        self.date_filter = DateUtility().get_date_filter(request=request)
        self.customer_qs = AjoUser.objects.all()
        self.wallet_qs = WalletSystem.objects.filter(
            wallet_type__in=["AJO_USER", "AJO_AGENT"]
        )
        self.ajo_savings_qs = AjoSaving.objects.filter()
        self.transactions_qs = Transaction.objects.filter(plan_type="AJO")
        self.successful_transactions_qs = self.transactions_qs.filter(status="SUCCESS")

        self.rotation_group_members_qs = RotationGroupMember.objects.values(
            name=F("group__name"),
            collection_amount=F(
                "group__collection_amount"
            ),  # This is the amount this user is to collect from the group
            contribution_amount=F(
                "group__contribution_amount"
            ),  # This is the amount this user is to contribute
            amount_contributed=F(
                "total_amount_contributed"
            ),  # This is the amount this user has contributed thus far
            start_date=F("group__starting_date"),
            number_of_participants=F("group__number_of_participants"),
            postion=F("position"),
        )

    def get_customer_overview_details(self, id):
        customer = self.customer_qs.get(id=id)
        data = {
            "id": customer.id,
            "name": (
                customer.first_name
                if customer.first_name
                else "" + " " + customer.last_name if customer.last_name else ""
            ),
            "phone_number": customer.phone_number,
            "bvn": customer.bvn,
            "gender": customer.gender,
            "marital_status": customer.marital_status,
            "location": customer.trade_location,
        }
        return data

    def get_customer_wallet_details(self, id):
        customer = self.customer_qs.get(id=id)
        customer_wallet_qs = self.wallet_qs.filter(onboarded_user=customer)

        customer_general_wallet = customer_wallet_qs.filter(
            wallet_type__in=customer_wallet_list
        ).last()
        customer_spend_wallet = customer_wallet_qs.filter(
            wallet_type="AJO_SPENDING"
        ).last()
        customer_savings_wallet = customer_wallet_qs.filter(
            wallet_type="AJO_USER"
        ).last()

        customer_bank_account = BankAccountDetails.objects.filter(
            ajo_user=customer
        ).last()

        data = {
            "spend_account": {
                "account_number": (
                    customer_bank_account.account_number
                    if customer_bank_account
                    else ""
                ),
                "bank_name": (
                    customer_bank_account.bank_name if customer_bank_account else ""
                ),
            },
            "wallet_account": {
                "account_number": (
                    customer_bank_account.account_number
                    if customer_bank_account
                    else ""
                ),
                "bank_name": (
                    customer_bank_account.bank_name if customer_bank_account else ""
                ),
            },
            "balances": {
                "savings_balance": (
                    customer_savings_wallet.available_balance
                    if customer_savings_wallet
                    else 0.00
                ),
                "wallet_balance": (
                    customer_general_wallet.available_balance
                    if customer_general_wallet
                    else 0.00
                ),
                "spend_balance": (
                    customer_spend_wallet.available_balance
                    if customer_spend_wallet
                    else 0.00
                ),
            },
        }
        return data

    def get_customer_transactions_list(self, id):
        customer = self.customer_qs.filter(id=id).last()
        # user = customer.user

        customer_transactions_list = list(
            Transaction.objects.filter(
                onboarded_user=customer,
                plan_type="AJO",
                wallet_type="AJO_USER",
                status="SUCCESS",
            )
            .values(
                "amount",
                "status",
                "failure_reason",
                "transaction_type",
                "description",
                "unique_reference",
            )
            .annotate(
                plan=F("plan_type"),
                date=F("date_created"),
                transaction=F("transaction_type"),
            )
        )

        data = {
            "transactions_list": customer_transactions_list,
            "count": len(customer_transactions_list),
        }
        return data

    def get_customer_active_savings_list(self, id):
        customer = self.customer_qs.filter(id=id).last()
        # user = customer.user

        customer_active_savings_list = list(
            self.successful_transactions_qs.filter(onboarded_user=customer)
            .values(
                "amount",
                "status",
                "failure_reason",
                "transaction_type",
                "description",
                "unique_reference",
            )
            .annotate(
                plan=F("plan_type"),
                date=F("date_created"),
                transaction=F("transaction_type"),
            )
        )

        data = {
            "transactions_list": customer_active_savings_list,
            "count": len(customer_active_savings_list),
        }
        return data

    def get_customer_inactive_savings_list(self, id):
        customer = self.customer_qs.filter(id=id).last()
        # user = customer.user
        customer_inactive_savings_list = list(
            self.successful_transactions_qs.filter(onboarded_user=customer)
            .values(
                "amount",
                "status",
                "failure_reason",
                "transaction_type",
                "description",
                "unique_reference",
            )
            .annotate(
                plan=F("plan_type"),
                date=F("date_created"),
                transaction=F("transaction_type"),
            )
        )

        data = {
            "transactions_list": customer_inactive_savings_list,
            "count": len(customer_inactive_savings_list),
        }
        return data

    def get_customers_saving_plans_overview(self, id):
        """Summary of Ajo User Rosca activities"""

        customer = self.customer_qs.get(id=id)
        customer_rotation_groups_qs = self.rotation_group_members_qs.filter(
            ajo_user_member=customer
        )  # This is a list of the member accounts this customer has with details of each group they belong

        data = {
            "plans_list": customer_rotation_groups_qs,
            "count": len(customer_rotation_groups_qs),
        }
        return data

    def get_customers_overview(self):
        customers_qs = self.customer_qs.all()
        new_customers = self.customer_qs.filter(
            created_at__gte=self.month_start
        ).count()
        all_customers = self.customer_qs.count()
        active_customers = self.customer_qs.filter(
            user__performance_status="ACTIVE"
        ).count()
        inactive_customers = self.customer_qs.filter(
            user__performance_status="INACTIVE"
        ).count()

        data = {
            "new_customers": new_customers,
            "all_customers": all_customers,
            "active_customers": active_customers,
            "inactive_customers": inactive_customers,
        }

        return data

    def get_top5_customers_list(self):
        # Top 10
        discrete_transaction_amounts_overall = []
        for customer in self.customer_qs:
            collector = customer.user
            collector_branch = collector.branch_users.last()
            # customer_user = customer.user
            total_transactions_amount = list(
                self.successful_transactions_qs.filter(
                    onboarded_user=customer, **self.date_filter
                )
                .aggregate(Sum("amount"))
                .values()
            )[0]
            total_transactions_count = list(
                self.successful_transactions_qs.filter(
                    onboarded_user=customer, **self.date_filter
                )
                .aggregate(Count("amount"))
                .values()
            )[0]

            total_savings_count = list(
                self.ajo_savings_qs.filter(ajo_user=customer)
                .aggregate(Count("amount_saved"))
                .values()
            )[0]

            data = {
                "id": customer.id,
                # "agent_id": customer.customer_user_id,
                "customer_name": (
                    customer.first_name
                    if customer.first_name
                    else "" + " " + customer.last_name if customer.last_name else ""
                ),
                # "agent_email": customer.email,
                "transaction_amount": (
                    total_transactions_amount if total_transactions_count else 0.00
                ),
                "transaction_count": (
                    total_transactions_count if total_transactions_count else 0
                ),
                "total_savings_count": (
                    total_savings_count if total_savings_count else 0
                ),
                "branch": collector_branch.name if collector_branch else "",
            }
            discrete_transaction_amounts_overall.append(data)
        amounts_sorted_list = sorted(
            discrete_transaction_amounts_overall, key=lambda d: d["total_savings_count"]
        )[::-1][:10]
        counts_sorted_list = sorted(
            discrete_transaction_amounts_overall, key=lambda d: d["total_savings_count"]
        )[::-1][:10]

        data = {
            "top_5_active_customers": sorted(
                discrete_transaction_amounts_overall,
                key=lambda d: d["total_savings_count"],
            )[::-1][:10],
            "top_5_inactive_customers": sorted(
                discrete_transaction_amounts_overall,
                key=lambda d: d["total_savings_count"],
            )[:10],
        }
        return data

    def get_customers_list(self):
        customers_list = self.customer_qs.values(
            "id", "phone_number", "trade", "address", "gender", "marital_status"
        ).annotate(name=F("first_name"), status=F("user__performance_status"))

        data = {"customers_list": customers_list, "count": customers_list.count()}

        return data


class Branch:
    def __init__(self, request):
        self.week_start = DateUtility().week_start
        self.previous_week_start = DateUtility().previous_week_start
        self.previous_week_end = DateUtility().previous_week_end
        self.month_start = DateUtility().month_start
        self.previous_month_start = DateUtility().previous_month_start
        self.previous_month_end = DateUtility().previous_month_end
        self.year_start = DateUtility().year_start
        self.previous_year_start = DateUtility().previous_year_start
        self.previous_year_end = DateUtility().previous_year_end
        self.previous_day = DateUtility().previous_day
        self.start_of_all_transactions = DateUtility().start_of_all_transactions
        self.day_ago = DateUtility().day_ago
        self.date_filter = DateUtility().get_date_filter(request=request)

        self.branch_qs = AjoBranch.objects.all()
        self.ajo_users_qs = AjoUser.objects.all()
        self.commission_qs = Commission.objects.filter(plan_type="AJO")
        self.transactions_qs = Transaction.objects.filter(plan_type="AJO")
        self.successful_transactions_qs = self.transactions_qs.filter(status="SUCCESS")
        self.savings_transactions_qs = self.successful_transactions_qs.filter(
            plan_type="AJO", wallet_type="AJO_USER", status="SUCCESS"
        )
        self.revenue_qs = self.commission_qs.filter(commission_type="AGENT").annotate(
            company_revenue_amount=F("total_amount_taken_as_commission") * 0.4
        )

        """For an Ajo User to qualify as a collector they must have made up to 7 collections"""
        self.collectors_based_on_collections_qs = (
            self.savings_transactions_qs.values("user")
            .annotate(collections_count=Count("user"))
            .filter(collections_count__gte=7)
        )

    def get_branch_overview(self):
        number_of_branches = list(self.branch_qs.aggregate(Count("id")).values())[0]
        number_of_collectors = (
            self.collectors_based_on_collections_qs.count()
        )  # self.ajo_users_qs.distinct("user").count()
        number_of_savers = self.ajo_users_qs.count()

        data = {
            "number_of_branches": number_of_branches if number_of_branches else 0,
            "number_of_collectors": number_of_collectors,
            "number_of_savers": number_of_savers,
        }
        return data

    def get_branch_charts(self):
        branches = [branch.name for branch in self.branch_qs]
        branch_users = [branch.users.all() for branch in self.branch_qs]

        savers_values = []
        collectors_values = [branch.users.count() for branch in self.branch_qs]
        commission_values = []
        branch_activities_values = [0 for branch in self.branch_qs]
        revenue_values = []

        for branch in self.branch_qs:
            branch_users = branch.users.all()
            commission_amount = list(
                self.commission_qs.filter(
                    user__in=branch_users, commission_type="AGENT"
                )
                .aggregate(Sum("amount"))
                .values()
            )[0]
            savers_count = self.ajo_users_qs.filter(user__in=branch_users).count()
            revenue_amount = list(
                self.revenue_qs.filter(user__in=branch_users)
                .aggregate(Sum("company_revenue_amount"))
                .values()
            )[0]

            commission_values.append(commission_amount if commission_amount else 0.00)
            savers_values.append(savers_count)
            revenue_values.append(revenue_amount if revenue_amount else 0.00)

        data = {
            "branches": branches,
            "collectors_values": collectors_values,
            "savers_values": savers_values,
            "commission_values": commission_values,
            "branch_activities_values": [0.0] * len(branches),
            "branch_revenues": revenue_values,
        }
        return data

    def get_top5_branches(self):
        branch_details_list = []
        for branch in self.branch_qs:
            branch_users = branch.users.all()
            transactions_amount = list(
                self.successful_transactions_qs.filter(
                    user__in=branch_users, **self.date_filter
                )
                .aggregate(Sum("amount"))
                .values()
            )[0]

            branch_details = {
                "id": branch.id,
                "branch": branch.name,
                "name": branch.name,
                "rating": 5,
                "transaction_amount": (
                    transactions_amount if transactions_amount else 0.00
                ),
            }
            branch_details_list.append(branch_details)

        sorted_branches = sorted(
            branch_details_list, key=lambda d: d["transaction_amount"]
        )
        top5_performing = sorted_branches[::-1][:5]

        data = {
            "top5_performing": top5_performing,
            "top5_non_performing": sorted_branches[:5],
        }
        return data

    def get_branch_list(self):
        branch_details_list = []
        for branch in self.branch_qs:
            branch_users = branch.users.all()
            number_of_collectors = (
                self.ajo_users_qs.filter(
                    user__in=branch_users,
                )
                .distinct("user")
                .count()
            )
            number_of_savers = self.ajo_users_qs.filter(user__in=branch_users).count()

            branches_details = {
                "id": branch.id,
                "branch": branch.name,
                "number_of_collectors": number_of_collectors,
                "number_of_savers": number_of_savers,
                "email": "<EMAIL>",
                "sub_location_num": 0,
            }
            branch_details_list.append(branches_details)
        data = {"branches": branch_details_list, "count": len(branch_details_list)}
        return data

    def get_performing_branch_list(self):
        branch_details_list = []
        for branch in self.branch_qs.filter(performance_status="PERFORMING"):
            branch_users = branch.users.all()
            number_of_savers = self.ajo_users_qs.filter(user__in=branch_users).count()
            number_collectors = branch.users.count()

            branches_details = {
                "id": branch.id,
                "branch": branch.name,
                "number_of_collectors": number_collectors,
                "number_of_savers": number_of_savers,
                "email": "<EMAIL>",
                "sub_location_num": 0,
            }
            branch_details_list.append(branches_details)
        data = {"branches": branch_details_list, "count": len(branch_details_list)}
        return data

    def get_under_performing_branch_list(self):

        branch_details_list = []
        for branch in self.branch_qs.filter(performance_status="UNDER_PERFORMING"):
            branch_users = branch.users.all()
            number_of_savers = self.ajo_users_qs.filter(user__in=branch_users).count()
            number_collectors = branch.users.count()

            branches_details = {
                "id": branch.id,
                "branch": branch.name,
                "number_of_collectors": number_collectors,
                "number_of_savers": number_of_savers,
                "email": "<EMAIL>",
                "sub_location_num": 0,
            }
            branch_details_list.append(branches_details)
        data = {"branches": branch_details_list, "count": len(branch_details_list)}
        return data


class BranchDetails:

    def __init__(self, request):
        self.week_start = DateUtility().week_start
        self.previous_week_start = DateUtility().previous_week_start
        self.previous_week_end = DateUtility().previous_week_end
        self.month_start = DateUtility().month_start
        self.previous_month_start = DateUtility().previous_month_start
        self.previous_month_end = DateUtility().previous_month_end
        self.year_start = DateUtility().year_start
        self.previous_year_start = DateUtility().previous_year_start
        self.previous_year_end = DateUtility().previous_year_end
        self.previous_day = DateUtility().previous_day
        self.start_of_all_transactions = DateUtility().start_of_all_transactions
        self.previous_year_current_month_start = (
            DateUtility().previous_year_current_month_start
        )
        self.months_list_names = DateUtility().months_list_names
        self.year_months_tuple_list = DateUtility().year_months_tuple_list

        self.date_filter = DateUtility().get_date_filter(request=request)
        self.request = request

        self.branch_qs = AjoBranch.objects.all()
        self.ajo_users_qs = AjoUser.objects.all()
        self.commission_qs = Commission.objects.filter(plan_type="AJO")
        self.transactions_qs = Transaction.objects.filter(plan_type="AJO")
        self.successful_transactions_qs = self.transactions_qs.filter(status="SUCCESS")
        self.ajo_savings_qs = AjoSaving.objects.all()

    def get_branch_details(self, id):
        branch = self.branch_qs.get(id=id)
        branch_users = branch.users.all()
        number_of_collectors = (
            self.ajo_users_qs.filter(user__in=branch_users).distinct("user").count()
        )
        number_of_savers = self.ajo_users_qs.filter(user__in=branch_users).count()
        savings_transactions_qs = self.successful_transactions_qs.filter(
            plan_type="AJO", wallet_type="AJO_USER", status="SUCCESS"
        )
        total_savings_amount = list(
            savings_transactions_qs.filter(user__in=branch_users)
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_savings_amount_today = list(
            savings_transactions_qs.filter(
                user__in=branch_users, date_created__date=timezone.now().date()
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_commission_amount = list(
            self.commission_qs.filter(user__in=branch_users, commission_type="AGENT")
            .aggregate(Sum("amount"))
            .values()
        )[0]
        total_commission_amount_today = list(
            self.commission_qs.filter(
                user__in=branch_users,
                commission_type="AGENT",
                created_at__date=timezone.now().date(),
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        cashout_transactions_amount = list(
            self.successful_transactions_qs.filter(
                transaction_form_type="CASHOUT", user__in=branch_users
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]
        cashout_transactions_amount_today = list(
            self.successful_transactions_qs.filter(
                date_created__date=timezone.now().date(),
                transaction_form_type="CASHOUT",
                user__in=branch_users,
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        total_transactions_amount = list(
            self.successful_transactions_qs.filter(user__in=branch_users)
            .aggregate(Sum("amount"))
            .values()
        )[0]

        total_transactions_amount_today = list(
            self.successful_transactions_qs.filter(
                date_created__date=timezone.now().date(), user__in=branch_users
            )
            .aggregate(Sum("amount"))
            .values()
        )[0]

        total_transactions_count = list(
            self.successful_transactions_qs.filter(user__in=branch_users)
            .aggregate(Count("amount"))
            .values()
        )[0]

        total_transactions_count_today = list(
            self.successful_transactions_qs.filter(
                date_created__date=timezone.now().date(), user__in=branch_users
            )
            .aggregate(Count("amount"))
            .values()
        )[0]

        active_savers_count = (
            self.ajo_savings_qs.filter(
                is_active=True, user__in=branch_users, updated_at__gte=self.month_start
            )
            .distinct("ajo_user")
            .count()
        )

        performing_collectors_count = (
            self.ajo_users_qs.filter(
                user__in=branch_users, user__performance_status="ACTIVE"
            )
            .distinct("user")
            .count()
        )

        data = {
            "branch_name": branch.name,
            "number_of_collectors": number_of_collectors,
            "number_of_savers": number_of_savers,
            "total_savings_amount": (
                total_savings_amount if total_savings_amount else 0.00
            ),
            "today_savings_amount": (
                total_savings_amount_today if total_savings_amount_today else 0.00
            ),
            "total_commission": (
                total_commission_amount if total_commission_amount else 0.00
            ),
            "today_commission": (
                total_commission_amount_today if total_commission_amount_today else 0.00
            ),
            "total_cashout": (
                cashout_transactions_amount if cashout_transactions_amount else 0.00
            ),
            "today_cashout": (
                cashout_transactions_amount_today
                if cashout_transactions_amount_today
                else 0.00
            ),
            "transaction_amount": (
                total_transactions_amount if total_transactions_amount else 0.00
            ),
            "transaction_count": (
                total_transactions_count if total_transactions_count else 0
            ),
            "today_transaction_count": (
                total_transactions_count_today if total_transactions_count_today else 0
            ),
            "active_savers": active_savers_count,
            "inactive_savers": number_of_savers - active_savers_count,
            "in-activated-agents": 0,
            "performing_collectors": performing_collectors_count,
            "non_performing_collectors": number_of_collectors
            - performing_collectors_count,
            "today_transaction": (
                total_transactions_amount_today
                if total_transactions_amount_today
                else 0.00
            ),
        }
        return data

    def get_branch_transaction_comparatives(self, id):
        branch = self.branch_qs.get(id=id)
        branch_users = branch.users.all()
        cashout_qs = self.successful_transactions_qs.filter(
            transaction_form_type="CASHOUT", user__in=branch_users
        )
        savings_transactions_qs = self.successful_transactions_qs.filter(
            plan_type="AJO", wallet_type="AJO_USER", status="SUCCESS"
        )

        past_year_commissions_amt_qs = (
            self.commission_qs.filter(
                user__in=branch_users,
                created_at__gte=self.previous_year_current_month_start,
            )
            .filter()
            .values("created_at__year", "created_at__month")
            .annotate(total_amount=Sum("amount"))
            .order_by("created_at__year")
        )
        past_year_commissions_list = [
            (data["created_at__year"], data["created_at__month"], data["total_amount"])
            for data in past_year_commissions_amt_qs
        ]

        # Commission
        past_year_commission_final_list = []
        for item in self.year_months_tuple_list:
            if item in [(tup[0], tup[1]) for tup in past_year_commissions_list]:
                for trans in past_year_commissions_list:
                    if item == (trans[0], trans[1]):
                        past_year_commission_final_list.append(trans)
                        break
            else:
                past_year_commission_final_list.append((0, 0, 0))
        commission_amount_values_list = [
            trans[2] for trans in past_year_commission_final_list
        ]

        # Withdrawals Values List
        past_year_cashout_amt_qs = (
            cashout_qs.filter(date_created__gte=self.previous_year_current_month_start)
            .values("date_created__year", "date_created__month")
            .annotate(total_amount=Sum("amount"))
            .order_by("date_created__year")
        )

        past_year_cashout_list = [
            (
                data["date_created__year"],
                data["date_created__month"],
                data["total_amount"],
            )
            for data in past_year_cashout_amt_qs
        ]

        past_year_cashout_final_list = []
        for item in self.year_months_tuple_list:
            if item in [(tup[0], tup[1]) for tup in past_year_cashout_list]:
                for trans in past_year_cashout_list:
                    if item == (trans[0], trans[1]):
                        past_year_cashout_final_list.append(trans)
                        break
            else:
                past_year_cashout_final_list.append((0, 0, 0))
        cashout_amount_values_list = [
            trans[2] for trans in past_year_cashout_final_list
        ]

        # Savings Values List
        past_year_savings_amt_qs = (
            savings_transactions_qs.filter(
                user__in=branch_users,
                date_created__gte=self.previous_year_current_month_start,
            )
            .values("date_created__year", "date_created__month")
            .annotate(total_amount=Sum("amount"))
            .order_by("date_created__year")
        )

        past_year_savings_list = [
            (
                data["date_created__year"],
                data["date_created__month"],
                data["total_amount"],
            )
            for data in past_year_savings_amt_qs
        ]

        past_year_savings_final_list = []
        for item in self.year_months_tuple_list:
            if item in [(tup[0], tup[1]) for tup in past_year_savings_list]:
                for trans in past_year_savings_list:
                    if item == (trans[0], trans[1]):
                        past_year_savings_final_list.append(trans)
                        break
            else:
                past_year_savings_final_list.append((0, 0, 0))
        savings_amount_values_list = [
            trans[2] for trans in past_year_savings_final_list
        ]

        data = {
            "months_list": self.months_list_names,
            "savings_values": savings_amount_values_list,
            "cashout_values": cashout_amount_values_list,
            "commission_values": commission_amount_values_list,
        }
        return data

    def get_branch_top5_supervisors(self, id):
        branch = self.branch_qs.get(id=id)
        collectors_list = branch.users.all()

        collectors_details_list = []
        for collector in collectors_list:
            transaction_amount = list(
                self.successful_transactions_qs.filter(
                    user=collector, **self.date_filter
                )
                .aggregate(Sum("amount"))
                .values()
            )[0]
            number_of_savers = self.ajo_users_qs.filter(user=collector).count()

            data = {
                "id": collector.id,
                "collector": collector.email,
                "savers": number_of_savers,
                "agents": 1,
                "transaction_amount": (
                    transaction_amount if transaction_amount else 0.00
                ),
                "rating": 5,
            }
            collectors_details_list.append(data)

        top_collectors_list = sorted(
            collectors_details_list, key=lambda d: d["transaction_amount"]
        )
        top5_performing = top_collectors_list[::-1][:5]
        top5_non_performing = top_collectors_list[:5]

        data = {
            "top5_performing": top5_performing,
            "top5_non_performing": top5_non_performing,
        }
        return data

    def get_branch_top_supervisors_ranking(self, id):
        branch = self.branch_qs.get(id=id)
        collectors_list = branch.users.all()

        collectors_details_list = []
        for collector in collectors_list:
            transaction_amount = list(
                self.successful_transactions_qs.filter(
                    user=collector, **self.date_filter
                )
                .aggregate(Sum("amount"))
                .values()
            )[0]
            number_of_savers = self.ajo_users_qs.filter(user=collector).count()

            data = {
                "id": collector.id,
                "collector": collector.email,
                "savers": number_of_savers,
                "agents": 1,
                "rating": 5,
                "transaction_amount": (
                    transaction_amount if transaction_amount else 0.00
                ),
            }
            collectors_details_list.append(data)

        top_collectors_list = sorted(
            collectors_details_list, key=lambda d: d["transaction_amount"]
        )
        top_performing = top_collectors_list[::-1]
        top_non_performing = top_collectors_list

        data = {
            "top5_performing": top_performing,
            "top5_non_performing": top_non_performing,
        }
        return data

    def get_branch_supervisors_list(self, id):
        branch = self.branch_qs.get(id=id)
        collector_list = branch.users.all()

        collectors_list = []
        for collector in collector_list:
            transaction_amount = list(
                self.successful_transactions_qs.filter(
                    user=collector, **self.date_filter
                )
                .aggregate(Sum("amount"))
                .values()
            )[0]
            number_of_savers = self.ajo_users_qs.filter(user=collector).count()

            data = {
                "id": collector.id,
                "branch": branch.name,
                "collector": collector.email,
                "savers": number_of_savers,
                "email": collector.email,
                "rating": 5,
                "transaction_amount": (
                    transaction_amount if transaction_amount else 0.00
                ),
            }
            collectors_list.append(data)

        data = {"collectors_list": collectors_list, "count": len(collectors_list)}
        return data

    def get_branch_inactivated_collectors_list(self, id):
        branch = self.branch_qs.get(id=id)
        collector_list = branch.users.all()

        collectors_list = []
        for collector in collector_list:
            transaction_amount = list(
                self.successful_transactions_qs.filter(
                    user=collector, **self.date_filter
                )
                .aggregate(Sum("amount"))
                .values()
            )[0]
            number_of_savers = self.ajo_users_qs.filter(user=collector).count()

            data = {
                "id": collector.id,
                "branch": branch.name,
                "collector": collector.email,
                "savers": number_of_savers,
                "email": collector.email,
                "rating": 5,
                "transaction_amount": transaction_amount or 0.00,
            }
            collectors_list.append(data)

        data = {"collectors_list": collectors_list, "count": len(collectors_list)}
        return data

    def get_supervisor_user_role(self):
        active_user = self.request.user
        retail_system = self.retail_system.distinct("supervisor")
        supervisors_list = [system.supervisor for system in retail_system]

        data = {
            "role": (
                "supervisor"
                if active_user in supervisors_list and active_user.is_staff == False
                else "admin" if active_user.is_staff else ""
            )
        }
        return data


# SUPERVISOR DATA
class SupervisorData:
    def __init__(self, request) -> None:
        self.month_start = DateUtility().month_start
        self.date_filter = DateUtility().get_date_filter(request=request)
        self.supervisors_qs = Supervisor.objects.all()
        self.transactions_qs = Transaction.objects.filter(
            plan_type="AJO", status="SUCCESS"
        )
        self.savings_transaction_qs = self.transactions_qs.filter(
            wallet_type="AJO_USER"
        )
        self.ajo_user_qs = AjoUser.objects.all()

    def get_supervisor_overview_data(self):
        number_of_supervisors = list(
            self.supervisors_qs.aggregate(Count("id")).values()
        )[0]
        number_of_active_supervisors = list(
            self.supervisors_qs.filter(performance_status="ACTIVE")
            .aggregate(Count("id"))
            .values()
        )[0]
        number_of_inactive_supervisors = list(
            self.supervisors_qs.filter(performance_status="INACTIVE")
            .aggregate(Count("id"))
            .values()
        )[0]
        number_of_new_supervisors = list(
            self.supervisors_qs.filter(date_created__gte=self.month_start)
            .aggregate(Count("id"))
            .values()
        )[0]

        data = {
            "number_of_supervisors": number_of_supervisors or 0,
            "active_supervisors": number_of_active_supervisors or 0,
            "inactive_supervisors": number_of_inactive_supervisors or 0,
            "number_of_new_supervisors": number_of_new_supervisors or 0,
        }
        return data

    def get_top10_supervisors(self):
        discrete_transaction_amounts_overall = []

        for supervisor in self.supervisors_qs:
            supervisor_collectors = supervisor.collectors.all()
            transactions_qs = self.transactions_qs.filter(
                user__in=supervisor_collectors
            )
            total_transactions_amount = list(
                transactions_qs.filter(**self.date_filter)
                .aggregate(Sum("amount"))
                .values()
            )[0]
            total_transactions_count = list(
                transactions_qs.filter(**self.date_filter)
                .aggregate(Count("amount"))
                .values()
            )[0]

            data = {
                "id": supervisor.id,
                "agent_id": supervisor.supervisor.customer_user_id,
                "agent_name": supervisor.supervisor.email,
                "agent_email": supervisor.supervisor.email,
                "transaction_amount": total_transactions_amount or 0.00,
                "transaction_count": total_transactions_count,
                "agent_location": supervisor.branch.name if supervisor.branch else "",
            }
            discrete_transaction_amounts_overall.append(data)
        amounts_sorted_list = sorted(
            discrete_transaction_amounts_overall, key=lambda d: d["transaction_amount"]
        )[::-1][:10]
        counts_sorted_list = sorted(
            discrete_transaction_amounts_overall, key=lambda d: d["transaction_count"]
        )[::-1][:10]

        data = {
            "transactions_amount": amounts_sorted_list,
            "transactions_count": counts_sorted_list,
        }
        return data

    def get_supervisors_list(self):
        supervisors_list = []

        for supervisor in self.supervisors_qs:
            supervisor_collectors = supervisor.collectors.all()

            supervisor_savings_qs = self.savings_transaction_qs.filter(
                user__in=supervisor_collectors
            )
            # print("-----------------------------")
            # print([field.name for field in supervisor.supervisor.__class__._meta.fields])
            # print("-----------------------------")

            if supervisor.supervisor:
                first_name = supervisor.supervisor.first_name or ""
                last_name = supervisor.supervisor.last_name or ""
            else:
                first_name = ""
                last_name = ""

            number_of_collectors = supervisor.collectors.count()
            total_savings_amount = list(
                supervisor_savings_qs.aggregate(Sum("amount")).values()
            )[0]
            total_savings_count = list(
                supervisor_savings_qs.aggregate(Count("amount")).values()
            )[0]

            data = {
                "id": supervisor.id,
                "name": f"{first_name} {last_name}",
                "email": supervisor.supervisor.email,
                "location": supervisor.branch.name if supervisor.branch else "",
                "number_of_collectors": (
                    number_of_collectors if number_of_collectors else 0
                ),
                "total_savings_amount": (
                    total_savings_amount if total_savings_amount else 0.00
                ),
                "total_savings_count": (
                    total_savings_count if total_savings_count else 0
                ),
            }
            supervisors_list.append(data)

        return {"supervisors_list": supervisors_list, "count": len(supervisors_list)}

    def get_inactive_supervisors_list(self):
        supervisors_list = []

        for supervisor in self.supervisors_qs.filter(performance_status="INACTIVE"):
            supervisor_collectors = supervisor.collectors.all()
            supervisor_savings_qs = self.savings_transaction_qs.filter(
                user__in=supervisor_collectors
            )

            if supervisor.supervisor:
                first_name = (
                    supervisor.supervisor.first_name
                    if supervisor.supervisor.first_name
                    else ""
                )
                last_name = (
                    supervisor.supervisor.last_name
                    if supervisor.supervisor.last_name
                    else ""
                )
            else:
                first_name = ""
                last_name = ""

            number_of_collectors = supervisor.collectors.count()
            total_savings_amount = list(
                supervisor_savings_qs.aggregate(Sum("amount")).values()
            )[0]
            total_savings_count = list(
                supervisor_savings_qs.aggregate(Count("amount")).values()
            )[0]

            data = {
                "id": supervisor.id,
                "name": f"{first_name} {last_name}",
                "email": supervisor.supervisor.email,
                "location": supervisor.branch.name or "",
                "number_of_collectors": number_of_collectors or 0,
                "total_savings_amount": total_savings_amount or 0.00,
                "total_savings_count": total_savings_count or 0,
            }
            supervisors_list.append(data)

        return {"supervisors_list": supervisors_list, "count": len(supervisors_list)}

    def get_active_supervisors_list(self):
        supervisors_list = []

        for supervisor in self.supervisors_qs.filter(performance_status="ACTIVE"):
            supervisor_collectors = supervisor.collectors.all()
            supervisor_savings_qs = self.savings_transaction_qs.filter(
                user__in=supervisor_collectors
            )

            if supervisor.supervisor:
                first_name = supervisor.supervisor.first_name or ""
                last_name = supervisor.supervisor.last_name or ""
            else:
                first_name = ""
                last_name = ""

            number_of_collectors = supervisor.collectors.count()
            total_savings_amount = list(
                supervisor_savings_qs.aggregate(Sum("amount")).values()
            )[0]
            total_savings_count = list(
                supervisor_savings_qs.aggregate(Count("amount")).values()
            )[0]

            data = {
                "id": supervisor.id,
                "name": f"{first_name} {last_name}",
                "email": supervisor.supervisor.email,
                "location": supervisor.branch.name if supervisor.branch else "",
                "number_of_collectors": (
                    number_of_collectors if number_of_collectors else 0
                ),
                "total_savings_amount": (
                    total_savings_amount if total_savings_amount else 0.00
                ),
                "total_savings_count": (
                    total_savings_count if total_savings_count else 0
                ),
            }
            supervisors_list.append(data)

        return {"supervisors_list": supervisors_list, "count": len(supervisors_list)}

    def get_supervisor_details_overview(self, id):
        supervisor = self.supervisors_qs.get(id=id)
        first_name = supervisor.supervisor.first_name or ""
        last_name = supervisor.supervisor.last_name or ""

        number_of_collectors = supervisor.collectors.count()
        number_of_active_collectors = supervisor.collectors.filter(
            performance_status="ACTIVE"
        ).count()
        number_of_inactive_collectors = supervisor.collectors.filter(
            performance_status="INACTIVE"
        ).count()

        supervisor_collectors = supervisor.collectors.all()
        supervisor_savings_qs = self.savings_transaction_qs.filter(
            user__in=supervisor_collectors
        )
        total_savings_amount = list(
            supervisor_savings_qs.aggregate(Sum("amount")).values()
        )[0]
        total_savings_count = list(
            supervisor_savings_qs.aggregate(Count("amount")).values()
        )[0]

        data = {
            "id": supervisor.id,
            "name": f"{first_name} {last_name}",
            "email": supervisor.supervisor.email,
            "location": supervisor.branch.name or "",
            "number_of_collectors": number_of_collectors or 0,
            "number_of_active_collectors": number_of_active_collectors or 0,
            "number_of_inactive_collectors": number_of_inactive_collectors or 0,
            "total_savings_amount": total_savings_amount or 0.00,
            "total_savings_count": total_savings_count or 0,
        }

        return data

    def get_supervisor_collectors_list(self, id):
        supervisor = self.supervisors_qs.get(id=id)
        collectors = supervisor.collectors.all()

        collectors_list = []
        for collector in collectors:
            collector_transactions_amount = list(
                self.transactions_qs.filter(user=collector, **self.date_filter)
                .aggregate(Sum("amount"))
                .values()
            )[0]
            number_of_savers = self.ajo_user_qs.filter(user=collector).count()
            collector_branch = collector.branch_users.last()

            agent_details = {
                "name": collector.email,
                "id": collector.id,
                "email": collector.email,
                "branch": collector_branch.name or "",
                "location": collector_branch.name or "",
                "phone_number": "",
                "savers": number_of_savers,
                "transactions": collector_transactions_amount or 0.00,
                "status": collector.performance_status,
            }
            collectors_list.append(agent_details)
        data = {"collectors": collectors_list, "count": len(collectors_list)}
        return data

    def get_supervisor_active_collectors_list(self, id):
        supervisor = self.supervisors_qs.get(id=id)
        collectors = supervisor.collectors.filter(performance_status="ACTIVE")

        collectors_list = []

        for collector in collectors:
            collector_transactions_amount = list(
                self.transactions_qs.filter(user=collector, **self.date_filter)
                .aggregate(Sum("amount"))
                .values()
            )[0]
            number_of_savers = self.ajo_user_qs.filter(user=collector).count()
            collector_branch = collector.branch_users.last()

            agent_details = {
                "name": collector.email,
                "id": collector.id,
                "email": collector.email,
                "branch": collector_branch.name or "",
                "location": collector_branch.name or "",
                "phone_number": "",
                "savers": number_of_savers,
                "transactions": collector_transactions_amount or 0.00,
                "status": collector.performance_status,
            }
            collectors_list.append(agent_details)

        data = {"collectors": collectors_list, "count": len(collectors_list)}
        return data

    def get_supervisor_inactive_collectors_list(self, id):
        supervisor = self.supervisors_qs.get(id=id)
        collectors = supervisor.collectors.filter(performance_status="INACTIVE")

        collectors_list = []

        for collector in collectors:
            collector_transactions_amount = list(
                self.transactions_qs.filter(user=collector, **self.date_filter)
                .aggregate(Sum("amount"))
                .values()
            )[0]
            number_of_savers = self.ajo_user_qs.filter(user=collector).count()
            collector_branch = collector.branch_users.last()

            agent_details = {
                "name": collector.email,
                "id": collector.id,
                "email": collector.email,
                "branch": collector_branch.name or "",
                "location": collector_branch.name or "",
                "phone_number": "",
                "savers": number_of_savers,
                "transactions": collector_transactions_amount or 0.00,
                "status": collector.performance_status,
            }
            collectors_list.append(agent_details)
        data = {"collectors": collectors_list, "count": len(collectors_list)}
        return data


class LoansDashboard:
    def __init__(self) -> None:
        pass

    def get_loan_repayment_overview(self):
        data = {
            "total_loan_repayment": 0.00,
            "total_repayment_today": 0.00,
            "total_repayment_week": 0.00,
            "total_repayment_month": 0.00,
            "total_repayment_year": 0.00,
            "due_loan_balance": 0.00,
        }

        return data

    def get_disbursement_data(self):
        data = {
            "disbursements": {
                "total_disbursement": 0.00,
                "total_disbursement_today": 0.00,
                "total_disbursement_week": 0.00,
                "total_disbursement_month": 0.00,
                "total_disbursement_today_year": 0.00,
            },
            "disbursements_target": {
                "daily_target": 0.00,
                "weekly_target": 0.00,
                "monthly_target": 0.00,
                "yearly_target": 0.00,
            },
        }

        return data

    def get_collections_data(self):
        data = {
            "collections": {
                "total_collections": 0.00,
                "total_collections_today": 0.00,
                "total_collections_week": 0.00,
                "total_collections_month": 0.00,
                "total_collections_today_year": 0.00,
            },
            "collections_target": {
                "daily_target": 0.00,
                "weekly_target": 0.00,
                "monthly_target": 0.00,
                "yearly_target": 0.00,
            },
        }

        return data

    def get_loans_request_overview(self):
        data = {"total_loans_in_progress": 0.00, "total_loan_fees": 0.00}

        return data
