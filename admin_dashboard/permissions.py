from django.contrib.auth.models import Group
from rest_framework.permissions import IsAuthenticated
from ajo.models import Supervisor
from accounts.agency_banking import AgencyBankingClass



class AjoIsAuthenticated(IsAuthenticated):
    """
    Permission to allow admin users use endpoints.
    """

    def has_permission(self, request, view):
        if not super().has_permission(request, view):
            return False

        group_names = ["Ajo Admin"]

        for group_name in group_names:
            try:
                group = Group.objects.get(name=group_name)
                if group in request.user.groups.all():
                    return True
            except Group.DoesNotExist:
                pass

        if request.user.is_superuser:
            return True

        supervisors = []
        agency_banking_supervisors = AgencyBankingClass.get_agency_supervisor_details()
        if "success" in agency_banking_supervisors and agency_banking_supervisors.get("success") == True:
            supervisors_list = agency_banking_supervisors.get("supervisors_teams_list")
            supervisors = [data.get("supervisor_user_id", None) for data in supervisors_list]

        if request.user.customer_user_id in supervisors:
            return True

        return False
