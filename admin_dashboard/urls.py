from django.urls import path
from admin_dashboard import views


loans_urls = [
    path("loans-repayment-data", views.LoanRepaymentView.as_view()),
    path("loans-disbursement-data", views.LoanDisbursementDataView.as_view()),
    path("loans-collections-data", views.LoanCollectionsDataView.as_view()),
    path("loans-request-overview", views.LoanRequestsOverView.as_view()),
]

urlpatterns = [
    path("get-revenue-metrics", views.MainDashboardRevenueMetricsView.as_view()),
    path("transaction-metrics", views.MainDashboardTransactionsMetricsView.as_view()),
    path("transaction-metrics-two", views.MainDashboardTransactionsMetricsTwoView.as_view()),
    path("savings-metrics", views.MainDashboardSavingsMetricsView.as_view()),
    path("main-dashboard-charts", views.MainDashboardChartView.as_view()),
    path("agents-overview", views.AgentOverviewView.as_view()),
    path("top-performing-agents", views.TopPerformingAgentsView.as_view()),
    path("agents-list", views.AgentsListView.as_view()),
    path("active-agents-list", views.ActiveAgentsListView.as_view()),
    path("inactive-agents-list", views.InactiveAgentsListView.as_view()),
    path("suspended-agents-list", views.SuspendedAgentsListView.as_view()),
    path("agents-details/<id>", views.AgentsDetailsOverView.as_view()),
    path("agents-wallet-details/<id>", views.AgentsWalletDetailsView.as_view()),
    path("agents-transactions/<id>", views.AgentsTransactionsView.as_view()),
    path("agents-transaction-details/<id>", views.TransactionsDetailView.as_view()),
    path("agents-customers-list/<id>", views.AgentCustomersListView.as_view()),
    path("agents-revenue-details/<id>", views.AgentsRevenueDetailsView.as_view()),
    path("agents-active-sessions/<id>", views.AgentsActiveSessionsView.as_view()),
    path("agents-log-history", views.AgentsLogHistoryListView.as_view()),
    path("customers-list", views.CustomersListView.as_view()),
    path("customers-overview", views.CustomersOverView.as_view()),
    path("customers-top5", views.Top5CustomersView.as_view()),
    path("customer-detail-overview/<id>", views.CustomerDetailOverView.as_view()),
    path("customer-wallet-detail/<id>", views.CustomerWalletDetailView.as_view()),
    path("customer-transactions-list/<id>", views.CustomerTransactionsListView.as_view()),
    path("overall-transactions-list", views.OverallTransactionsListView.as_view()),
    path("customer-active-savings-list/<id>", views.CustomerActiveSavingsListView.as_view()),
    path("customer-inactive-savings-list/<id>", views.CustomerInActiveSavingsListView.as_view()),
    path("customer-savings-plan-overview/<id>", views.CustomerSavingsPlanOverView.as_view()),
    path("branch-overview", views.BranchOverviewView.as_view()),
    path("branch-charts", views.BranchChartsView.as_view()),
    path("branch-top5", views.BranchTop5View.as_view()),
    path("branch-list", views.BranchListView.as_view()),
    path("performing-branch-list", views.PerformingBranchListView.as_view()),
    path("under-performing-branch-list", views.UnderPerformingBranchListView.as_view()),
    path("branch-details/<id>", views.BranchDetailsView.as_view()),
    path("branch-details-transaction-comparatives/<id>", views.BranchTransactionComparativesView.as_view()),
    path("branch-supervisors-list/<id>", views.BranchSupervisorsListView.as_view()),
    path("branch-inactivated-collectors-list/<id>", views.BranchInactivatedAgentsListView.as_view()),
    path("branch-top-supervisors/<id>", views.BranchTopSupervisorsView.as_view()),
    path("branch-top-supervisors-ranking/<id>", views.BranchTopSupervisorsRankingView.as_view()),
    path("add-new-branch", views.CreateBranchView.as_view()),
    path("assign-collector-terminal", views.AssignTerminalToCollector.as_view()),
    path("supervisors-overview-data", views.SupervisorsDataOverView.as_view()),
    path("top10-supervisors", views.Top10SupervisorsView.as_view()),
    path("supervisors-list", views.SupervisorsListView.as_view()),
    path("active-supervisors-list", views.ActiveSupervisorsListView.as_view()),
    path("inactive-supervisors-list", views.InactiveSupervisorsListView.as_view()),
    path("supervisor-details-overview/<int:id>", views.SupervisorDetailView.as_view()),
    path("supervisor-collectors-list/<int:id>", views.SupervisorCollectorsListView.as_view()),
    path("supervisor-active-collectors-list/<int:id>", views.SupervisorActiveCollectorsListView.as_view()),
    path("supervisor-inactive-collectors-list/<int:id>", views.SupervisorInactiveCollectorsListView.as_view()),

    *loans_urls
]