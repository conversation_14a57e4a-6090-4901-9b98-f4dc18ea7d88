# agency_banking_savings

## 📌 Savings App for Agency Banking

This application supports agency banking savings and is powered by **Celery** for background task processing.

---

## 🚀 Running Celery Workers

### **Linux / Unix**
Start the **Celery worker** and **Celery Beat scheduler** with:

```sh
celery -A savings beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler
```

Start a specific **Celery queue**:

```sh
celery -A savings.celery worker -Q queName --loglevel=INFO --concurrency 1 -P solo
```

🔗 **Error Log URL**: [Base URL]/core/server/logs/

---

### **Windows**
Run Celery with **Gevent**:

```sh
celery -A savings.celery worker --loglevel=INFO --concurrency 1 -P solo
```

### **Celery Services** 🚀
| Service Name | Description |
|-------------|-------------|
| `celery.service` | Main Celery service |
| `celery_account_manager.service` | Account manager service |
| `celery_communications.service` | Communication management (e.g., OpenAI Loan Review) |
| `celerybeat.service` | Celery Beat scheduler |
| `celerybeatdocumentation.service` | Documentation-related tasks |
| `celerycompliance.service` | Compliance-related tasks |
| `celerydocumentation.service` | Documentation tasks (e.g., `handle_image_upload_result_update_on_loan_eligiblity_verification`) |
| `celeryescrow.service` | Escrow-related tasks |
| `celerysavings_documentation.service` | Savings documentation tasks |
| `celerysavingselegibles.service` | Savings eligibility processing |
| `celery_insurance_updates.service` | Insurance updates (e.g., `create_update_renewal_summary`) |

---

### 📂 **Error Log Access**
For system logs and debugging, visit the **error log dashboard**:

🔗 **Log URL**: `[BASE_URL]/ajo/ajo-savings/logs/`

Replace `[BASE_URL]




# Loan Management AI Assistant

## Overview

The Loan Management AI Assistant is a Django-based API service that enables loan officers to interact with an AI assistant that provides insights and analysis about their loan portfolio. The system retrieves raw loan data from the database and uses OpenAI's large language models to analyze the data and answer questions.

## Key Features

- **Direct Data Access**: Instead of predefined functions for specific question types, the system provides raw data to the language model for flexible analysis
- **Comprehensive Portfolio Analysis**: Analyze loans, borrowers, repayments, and metrics to identify issues and opportunities
- **Natural Language Interface**: Loan officers can ask questions in natural language about their portfolio
- **Prioritization**: Identify high-priority borrowers who need follow-up based on risk factors

## Architecture

The system follows a modular architecture:

```
┌─────────────────┐     ┌───────────────────┐     ┌─────────────────┐
│                 │     │                   │     │                 │
│  Django Views   │────►│  Agent Handler    │────►│  Data Service   │
│  - Chat         │     │                   │     │                 │
│  - History      │     └───────────────────┘     └─────────────────┘
│  - Questions    │            │                          │
└─────────────────┘            │                          ▼
        │                      │                  ┌─────────────────┐
        ▼                      │                  │                 │
┌─────────────────┐            │                  │   Database      │
│                 │            │                  │  - Loans        │
│  Serializers    │            │                  │  - Users        │
│                 │            │                  │  - Conversations │
└─────────────────┘            │                  │  - Messages     │
        │                      │                  │  - Questions    │
        ▼                      │                  └─────────────────┘
┌─────────────────┐            │                          ▲
│                 │            │                          │
│  Admin Interface│            │                          │
│                 │            │                          │
└─────────────────┘            ▼                          │
                        ┌─────────────────┐               │
                        │                 │               │
                        │  OpenAI API     │───────────────┘
                        │                 │  (Store responses)
                        └─────────────────┘
```

## Components

### 1. Views (`ai_hub/views.py`)

- Handles HTTP requests for chat interactions
- Validates input data using serializers
- Manages authentication and permissions
- Passes messages to the Agent Handler

### 2. Agent Handler (`ai_hub/services/agent_handler.py`)

- Manages conversation with OpenAI's language models
- Retrieves loan data from the Data Service
- Formats data for the language model
- Processes responses for the loan officer

### 3. Data Service (`ai_hub/services/agent_data_service.py`)

- Retrieves comprehensive loan data from the database
- Calculates derived metrics and aggregations
- Provides structured data for analysis


## Sample Questions

The system can answer a wide range of questions about the loan portfolio, including but not limited to:

1. "How many missed loans do I have?"
2. "How many past maturities do I have?"
3. "How much is my missed repayment?"
4. "Which borrower should I visit today?"
5. "Which loans are past due?"
6. "Which borrowers need me to follow up?"
7. "Which loans have I not remitted?"
8. "What is my collection rate this month?"
9. "Who are my top 5 borrowers with the highest outstanding balances?"
10. "How many loans are past maturity by more than 30 days?"
11. "What's the total amount I've collected this month?"
12. "Which borrowers have multiple active loans?"
13. "What's my overall portfolio performance like?"
14. "What are my biggest risk areas right now?"
15. "Give me a breakdown of loans by status"



# Loan Checker System Documentation

## Overview

The Loan Checker system is a critical component of our loan management platform that determines whether loan officers (agents) are eligible to disburse new loans based on their repayment collection performance. This system ensures that our loan officers maintain healthy collection rates before they're allowed to disburse additional funds, which is essential for managing overall portfolio risk.

## How the System Works

The Loan Checker employs a two-tiered approach to evaluate loan officers:

1. **Overall Portfolio Check**: Evaluates the aggregate collection performance across all loans
2. **Individual Loan Check**: Assesses performance at the individual loan level to identify problematic patterns

### Overall Portfolio Check Process

1. **Agent Data Collection**:
   The system retrieves all loans associated with a particular agent to get a complete view of their portfolio.

2. **Buffer Handling**:
   Checks if the agent has a "buffer" - an amount allocated to agents who might be on leave or unable to collect repayments for valid reasons. The buffer gives agents some leeway in their collection requirements.

3. **Repayment Calculation**:
   Gathers all open loans for the agent and calculates the total repayments made against those loans.

4. **Total Repayment With Buffer**:
   The total repayment amount is calculated as the actual repayment collections plus any buffer amount. For example, if an agent has collected ₦5,000 in repayments and has a buffer of ₦2,000, their "effective" collection is considered to be ₦7,000.

5. **Due Loan Calculation**:
   For each loan, the system calculates the "due loan" amount, which is how much should have been repaid by now according to the repayment schedule (using the `due_yesterday` property).

6. **Balance Calculation**:
   If what has been repaid is less than what is due, the system calculates the balance (the shortfall) and adds it to a "balances" collection.

7. **Past Maturity Handling**:
   Loans that are past their maturity date are flagged as "past maturity" and tracked separately.

8. **Buffer Application to Balance**:
   If there's an agent buffer, it's subtracted from the calculated balance. So if the balance was ₦5,000 and the buffer is ₦2,000, the effective balance becomes ₦3,000.

9. **Collection Rate Calculation**:
   The system calculates the repayment percentage:
   - If total dues = 0, the agent has 100% collection rate
   - If total payments = 0, the agent has 0% collection rate
   - Otherwise, it's calculated as `(total payments/total dues) * 100`

10. **Tier-Based Threshold Checking**:
    The system compares the agent's collection rate against a threshold percentage determined by their "tier." Different tiers (based on loan volume or experience) have different minimum collection rate requirements.

11. **Loan Disbursement Eligibility (First Check)**:
    If the agent's collection percentage falls below their tier's threshold, they're marked as failing the check and cannot disburse new loans.

### Individual Loan Check Process

Even if an agent passes the overall portfolio check, the system performs a more granular analysis on a loan-by-loan basis:

1. **Individual Loan Evaluation**:
   Each loan in the portfolio is evaluated separately.

2. **Metrics for Individual Loans**:
   For each loan, the system calculates:
   - The collection percentage for that specific loan
   - The outstanding days (how many days the loan has been in arrears)
   - Other loan-specific metrics to evaluate performance

3. **Addressing the "Big Loan" Problem**:
   This individual checking prevents a scenario where an agent might have a good overall collection rate, but it's skewed by one or two very large loans that are performing well, while many smaller loans are delinquent.

4. **Combining Problem Loans**:
   After evaluating each loan individually, the system identifies all loans with collection issues and groups them together.

5. **Positive/Negative Counters**:
   The system keeps track of how many loans are performing well (positive count) versus how many have issues (negative count).

6. **Defaulting Loans Table**:
   The most problematic loans are identified and stored in a "defaulting loans" table within the Loan Analysis Log.

7. **Final Determination**:
   Based on both the overall check and the individual loan checks, the system makes a final determination on whether the agent can disburse new loans.

## Practical Example

Let's walk through a practical example to understand how the Loan Checker evaluates a loan officer's portfolio:

### Example: Loan Officer John

John has the following loan portfolio:

| Loan ID | Borrower | Loan Amount | Repayment Amount | Start Date | End Date | Loan Status | Due Yesterday | Repaid Amount |
|---------|----------|-------------|------------------|------------|----------|-------------|---------------|---------------|
| 101     | Alice    | ₦50,000     | ₦55,000          | 2023-01-01 | 2023-03-01 | OPEN      | ₦36,667       | ₦36,667       |
| 102     | Bob      | ₦30,000     | ₦33,000          | 2023-01-15 | 2023-03-15 | OPEN      | ₦19,800       | ₦15,000       |
| 103     | Charlie  | ₦100,000    | ₦110,000         | 2023-02-01 | 2023-05-01 | OPEN      | ₦44,000       | ₦22,000       |
| 104     | Diana    | ₦25,000     | ₦27,500          | 2022-12-01 | 2023-02-01 | OPEN      | ₦27,500       | ₦20,000       |

John has a buffer of ₦5,000 because he was on leave for a week last month.

### Overall Portfolio Check

1. **Total Due**: ₦36,667 + ₦19,800 + ₦44,000 + ₦27,500 = ₦127,967
2. **Total Repaid**: ₦36,667 + ₦15,000 + ₦22,000 + ₦20,000 = ₦93,667
3. **Effective Total Repaid** (with buffer): ₦93,667 + ₦5,000 = ₦98,667
4. **Loan-by-Loan Analysis**:
   - Loan 101: Due ₦36,667, Repaid ₦36,667, Balance ₦0 (fully paid)
   - Loan 102: Due ₦19,800, Repaid ₦15,000, Balance ₦4,800 (missed payment)
   - Loan 103: Due ₦44,000, Repaid ₦22,000, Balance ₦22,000 (significant missed payment)
   - Loan 104: Due ₦27,500, Repaid ₦20,000, Balance ₦7,500 (missed payment, past maturity)
5. **Total Balance**: ₦4,800 + ₦22,000 + ₦7,500 = ₦34,300
6. **Effective Balance** (with buffer): ₦34,300 - ₦5,000 = ₦29,300
7. **Collection Rate**: (₦98,667 / ₦127,967) * 100 = 77.1%
8. **Tier Threshold**: 
   - John has disbursed ₦205,000 in total, placing him in Tier 2 which requires 80% collection
   - Since his collection rate is 77.1%, which is below his tier's requirement of 80%, John fails the overall check

### Individual Loan Check

Even though John already failed the overall check, the system proceeds with individual loan checking:

| Loan ID | Borrower | Collection % | Outstanding Days | Status     |
|---------|----------|--------------|------------------|------------|
| 101     | Alice    | 100%         | 0                | Positive   |
| 102     | Bob      | 75.8%        | 8                | Negative   |
| 103     | Charlie  | 50%          | 15               | Negative   |
| 104     | Diana    | 72.7%        | 20               | Negative   |

**Results of Individual Checking**:
- **Positive count**: 1 loan
- **Negative count**: 3 loans
- **Average outstanding days**: 10.75
- **Defaulting loans**: #103 and #104 (worst performers)

### Final Determination

**Result**: John cannot disburse new loans because:
1. He failed the overall portfolio check (77.1% vs. required 80%)
2. 75% of his loans (3 out of 4) have collection issues
3. Two loans (#103 and #104) are severely underperforming with significant outstanding days

## Technical Implementation

The Loan Checker system is implemented in the following components:

1. **LoanAnalysisLog Model**: Stores the results of the loan checker analysis, including:
   - The agent being analyzed
   - The percentage of collections
   - The amount due
   - The total paid
   - Whether the agent can give loans
   - Positive/negative loan counts
   - Average outstanding days
   - Defaulting loans (many-to-many relationship)

2. **AjoLoan Properties**: Utilizes properties on the AjoLoan model such as:
   - `due_yesterday`: The amount that should have been repaid as of yesterday
   - `outstanding_days`: The number of days a loan is overdue
   - `outstanding_due`: The amount that is overdue

3. **Buffer System**: Implemented to give agents some leeway when they are on leave or facing other legitimate challenges that affect their collection performance.

## Business Context

This system is designed to:

1. Encourage agents to maintain healthy collection rates
2. Prevent agents with poor collection performance from disbursing new loans and increasing risk
3. Identify problematic loans that need attention
4. Account for special circumstances through the buffer system
5. Balance overall portfolio performance with individual loan performance
6. Provide data-driven insights into agent performance

By enforcing both overall and individual loan collection requirements, the Loan Checker ensures that agents are managing their entire portfolio effectively rather than focusing only on large loans while neglecting smaller ones.

