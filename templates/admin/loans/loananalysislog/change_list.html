{% extends "admin/change_list.html" %}

{% block content %}
<div>
    <label for="items_per_page">Items per page:</label>
    <select id="items_per_page" onchange="location.href='{% url 'admin:set_items_per_page' num=0 %}'.replace('0', this.value)">
        <option value="25">25</option>
        <option value="50">50</option>
        <option value="100" selected>100</option>
        <option value="200">200</option>
        <option value="300">300</option>
        <option value="999999">All</option>
    </select>
</div>
{% endblock %}
