<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html dir="ltr" lang="en">
  <head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" />
    <link
      href="https://fonts.googleapis.com/css2?family=Outfit:wght@400;700&amp;display=swap"
      rel="stylesheet"
    />
    <style>
      @font-face {
        font-family: "Outfit";
        font-style: normal;
        font-weight: 400;
        mso-font-alt: "Arial";
        src: url(https://fonts.googleapis.com/css2?family=Outfit:wght@400;700&display=swap)
          format("woff2");
      }

      * {
        font-family: "Outfit", Arial;
      }
    </style>
  </head>
  <div
    style="
      display: none;
      overflow: hidden;
      line-height: 1px;
      opacity: 0;
      max-height: 0;
      max-width: 0;
    "
  >
    Join Alan on Vercel
    <div>
       ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿
    </div>
  </div>

  <body
    style="
      background-color: rgb(255, 255, 255);
      margin-top: auto;
      margin-bottom: auto;
      margin-left: auto;
      margin-right: auto;
      font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont,
        'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif,
        'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
        'Noto Color Emoji';
      padding-left: 0.5rem;
      padding-right: 0.5rem;
    "
  >
    <table
      align="center"
      width="100%"
      border="0"
      cellpadding="0"
      cellspacing="0"
      role="presentation"
      style="
        max-width: 712px;
        background-color: rgb(255, 255, 255);
        border-radius: 0.25rem;
        margin-top: 40px;
        margin-bottom: 40px;
      "
    >
      <tbody>
        <tr style="width: 100%">
          <td>
            <table
              align="center"
              width="100%"
              border="0"
              cellpadding="0"
              cellspacing="0"
              role="presentation"
              style="margin-top: 32px"
            >
              <tbody>
                <tr>
                  <td>
                    <img
                      alt="logo"
                      height="69"
                      src="/static/liberty-logo.png"
                      style="
                        display: block;
                        outline: none;
                        border: none;
                        text-decoration: none;
                        margin-top: 27px;
                      "
                      width="145"
                    />
                  </td>
                </tr>
              </tbody>
            </table>
            <table
              align="center"
              width="100%"
              border="0"
              cellpadding="0"
              cellspacing="0"
              role="presentation"
              style="
                background-color: rgb(7, 61, 159);
                margin-top: 10px;
                display: flex;
                width: 100%;
                align-items: flex-end;
                justify-content: flex-end;
                padding-right: 90px;
              "
            >
              <tbody>
                <tr>
                  <td>
                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation"
                      style="
                        padding-left: 27px;
                        padding-right: 27px;
                        background-color: rgb(255, 255, 255);
                      "
                    >
                      <tbody>
                        <tr>
                          <td>
                            <p
                              class="font-outfit"
                              style="
                                font-size: 26px;
                                line-height: 20px;
                                margin: 16px 0;
                                color: rgb(3, 34, 130);
                              "
                            >
                              <strong>Contract agreement</strong>
                            </p>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
            <table
              align="center"
              width="100%"
              border="0"
              cellpadding="0"
              cellspacing="0"
              role="presentation"
              style="
                margin-top: 10px;
                background-color: rgb(245, 250, 255);
                padding-top: 12px;
                padding-bottom: 12px;
                padding-left: 30px;
                padding-right: 30px;
              "
            >
              <tbody>
                <tr>
                  <td>
                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation"
                    >
                      <tbody style="width: 100%">
                        <tr style="width: 100%">
                          <td align="right" data-id="__react-email-column">
                            <table
                              align="center"
                              width="100%"
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-outfit"
                                      style="
                                        font-size: 12px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        color: #00308780;
                                      "
                                    >
                                      Date:
                                    </p>
                                    <p
                                      class="font-outfit"
                                      style="
                                        font-size: 12px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        color: rgb(3, 34, 130);
                                        margin-top: -10px;
                                      "
                                    >
                                      19/03/2024
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                          <td align="center" data-id="__react-email-column">
                            <table
                              align="center"
                              width="100%"
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-outfit"
                                      style="
                                        font-size: 12px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        color: #00308780;
                                      "
                                    >
                                      Address:
                                    </p>
                                    <p
                                      class="font-outfit"
                                      style="
                                        font-size: 12px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        color: rgb(3, 34, 130);
                                        margin-top: -10px;
                                      "
                                    >
                                      27 Alara street onike, sabo yaba
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                          <td align="left" data-id="__react-email-column">
                            <table
                              align="center"
                              width="100%"
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-outfit"
                                      style="
                                        font-size: 12px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        color: #00308780;
                                      "
                                    >
                                      Date:
                                    </p>
                                    <p
                                      class="font-outfit"
                                      style="
                                        font-size: 12px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        color: rgb(3, 34, 130);
                                        margin-top: -10px;
                                      "
                                    >
                                      17/03/2024
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
            <table
              align="center"
              width="100%"
              border="0"
              cellpadding="0"
              cellspacing="0"
              role="presentation"
              style="text-align: left; margin-top: 12px"
            >
              <tbody>
                <tr>
                  <td>
                    <p
                      class="font-outfit"
                      style="
                        font-size: 16px;
                        line-height: 20px;
                        margin: 16px 0;
                        color: rgb(3, 34, 130);
                        font-weight: 700;
                      "
                    >
                      Contract details
                    </p>
                  </td>
                </tr>
              </tbody>
            </table>
            <table
              align="center"
              width="100%"
              border="0"
              cellpadding="0"
              cellspacing="0"
              role="presentation"
              style="margin-top: 5px"
            >
              <tbody>
                <tr>
                  <td>
                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation"
                      style="
                        background-color: rgb(7, 61, 159);
                        padding-top: 10px;
                        padding-bottom: 10px;
                        padding-left: 24px;
                        padding-right: 24px;
                      "
                    >
                      <tbody style="width: 100%">
                        <tr style="width: 100%">
                          <td
                            align="left"
                            data-id="__react-email-column"
                            style="width: 200px; max-width: 200px"
                          >
                            <table
                              align="center"
                              width="100%"
                              class=""
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        text-align: left;
                                        color: rgb(255, 255, 255);
                                      "
                                    >
                                      Investors (s)
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                          <td align="center" data-id="__react-email-column">
                            <table
                              align="center"
                              width="100%"
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                              style="width: 100%"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        color: rgb(255, 255, 255);
                                      "
                                    >
                                      Contract descriptions
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation"
                      style="
                        background-color: rgb(245, 250, 255);
                        padding-left: 24px;
                        padding-right: 24px;
                        border-width: 1px;
                        border-color: rgb(0, 0, 0);
                      "
                    >
                      <tbody style="width: 100%">
                        <tr style="width: 100%">
                          <td
                            align="left"
                            data-id="__react-email-column"
                            style="width: 200px; max-width: 200px"
                          >
                            <table
                              align="center"
                              width="100%"
                              class=""
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        text-align: left;
                                        color: #24242480;
                                      "
                                    >
                                      Name
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                          <td align="center" data-id="__react-email-column">
                            <table
                              align="center"
                              width="100%"
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                              style="width: 100%"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        color: rgb(36, 36, 36);
                                      "
                                    >
                                      {{ name }}
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <hr
                      style="
                        width: 100%;
                        border: none;
                        border-top: 1px solid #eaeaea;
                        margin-top: -0px;
                        margin-bottom: -0px;
                      "
                    />
                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation"
                      style="
                        background-color: rgb(245, 250, 255);
                        padding-left: 24px;
                        padding-right: 24px;
                      "
                    >
                      <tbody style="width: 100%">
                        <tr style="width: 100%">
                          <td
                            align="left"
                            data-id="__react-email-column"
                            style="width: 200px; max-width: 200px"
                          >
                            <table
                              align="center"
                              width="100%"
                              class=""
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        text-align: left;
                                        color: #24242480;
                                      "
                                    >
                                      Deposit
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                          <td align="center" data-id="__react-email-column">
                            <table
                              align="center"
                              width="100%"
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                              style="width: 100%"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        color: rgb(36, 36, 36);
                                      "
                                    >
                                      ₦{{ deposit }}
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <hr
                      style="
                        width: 100%;
                        border: none;
                        border-top: 1px solid #eaeaea;
                        margin-top: -0px;
                        margin-bottom: -0px;
                      "
                    />
                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation"
                      style="
                        background-color: rgb(245, 250, 255);
                        padding-left: 24px;
                        padding-right: 24px;
                      "
                    >
                      <tbody style="width: 100%">
                        <tr style="width: 100%">
                          <td
                            align="left"
                            data-id="__react-email-column"
                            style="width: 200px; max-width: 200px"
                          >
                            <table
                              align="center"
                              width="100%"
                              class=""
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        text-align: left;
                                        color: #24242480;
                                      "
                                    >
                                      Interest rate
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                          <td align="center" data-id="__react-email-column">
                            <table
                              align="center"
                              width="100%"
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                              style="width: 100%"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        color: rgb(36, 36, 36);
                                      "
                                    >
                                      {{ interest_rate }}%
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <hr
                      style="
                        width: 100%;
                        border: none;
                        border-top: 1px solid #eaeaea;
                        margin-top: -0px;
                        margin-bottom: -0px;
                      "
                    />
                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation"
                      style="
                        background-color: rgb(245, 250, 255);
                        padding-left: 24px;
                        padding-right: 24px;
                      "
                    >
                      <tbody style="width: 100%">
                        <tr style="width: 100%">
                          <td
                            align="left"
                            data-id="__react-email-column"
                            style="width: 200px; max-width: 200px"
                          >
                            <table
                              align="center"
                              width="100%"
                              class=""
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        text-align: left;
                                        color: #24242480;
                                      "
                                    >
                                      Tenure/duration
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                          <td align="center" data-id="__react-email-column">
                            <table
                              align="center"
                              width="100%"
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                              style="width: 100%"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        color: rgb(36, 36, 36);
                                      "
                                    >
                                      {{ duration }} days
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <hr
                      style="
                        width: 100%;
                        border: none;
                        border-top: 1px solid #eaeaea;
                        margin-top: -0px;
                        margin-bottom: -0px;
                      "
                    />
                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation"
                      style="
                        background-color: rgb(245, 250, 255);
                        padding-left: 24px;
                        padding-right: 24px;
                      "
                    >
                      <tbody style="width: 100%">
                        <tr style="width: 100%">
                          <td
                            align="left"
                            data-id="__react-email-column"
                            style="width: 200px; max-width: 200px"
                          >
                            <table
                              align="center"
                              width="100%"
                              class=""
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        text-align: left;
                                        color: #24242480;
                                      "
                                    >
                                      Plan start date
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                          <td align="center" data-id="__react-email-column">
                            <table
                              align="center"
                              width="100%"
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                              style="width: 100%"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        color: rgb(36, 36, 36);
                                      "
                                    >
                                      {{ start_date }}
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <hr
                      style="
                        width: 100%;
                        border: none;
                        border-top: 1px solid #eaeaea;
                        margin-top: -0px;
                        margin-bottom: -0px;
                      "
                    />
                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation"
                      style="
                        background-color: rgb(245, 250, 255);
                        padding-left: 24px;
                        padding-right: 24px;
                      "
                    >
                      <tbody style="width: 100%">
                        <tr style="width: 100%">
                          <td
                            align="left"
                            data-id="__react-email-column"
                            style="width: 200px; max-width: 200px"
                          >
                            <table
                              align="center"
                              width="100%"
                              class=""
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        text-align: left;
                                        color: #24242480;
                                      "
                                    >
                                      Maturity date
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                          <td align="center" data-id="__react-email-column">
                            <table
                              align="center"
                              width="100%"
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                              style="width: 100%"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        color: rgb(36, 36, 36);
                                      "
                                    >
                                      {{ maturity_date }}
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <hr
                      style="
                        width: 100%;
                        border: none;
                        border-top: 1px solid #eaeaea;
                        margin-top: -0px;
                        margin-bottom: -0px;
                      "
                    />
                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation"
                      style="
                        background-color: rgb(245, 250, 255);
                        padding-left: 24px;
                        padding-right: 24px;
                      "
                    >
                      <tbody style="width: 100%">
                        <tr style="width: 100%">
                          <td
                            align="left"
                            data-id="__react-email-column"
                            style="width: 200px; max-width: 200px"
                          >
                            <table
                              align="center"
                              width="100%"
                              class=""
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        text-align: left;
                                        color: #24242480;
                                      "
                                    >
                                      Reference number
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                          <td align="center" data-id="__react-email-column">
                            <table
                              align="center"
                              width="100%"
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                              style="width: 100%"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        color: rgb(36, 36, 36);
                                      "
                                    >
                                      {{ quotation_id }}
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <hr
                      style="
                        width: 100%;
                        border: none;
                        border-top: 1px solid #eaeaea;
                        margin-top: -0px;
                        margin-bottom: -0px;
                      "
                    />
                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation"
                      style="
                        background-color: rgb(245, 250, 255);
                        padding-left: 24px;
                        padding-right: 24px;
                      "
                    >
                      <tbody style="width: 100%">
                        <tr style="width: 100%">
                          <td
                            align="left"
                            data-id="__react-email-column"
                            style="width: 200px; max-width: 200px"
                          >
                            <table
                              align="center"
                              width="100%"
                              class=""
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        text-align: left;
                                        color: #24242480;
                                      "
                                    >
                                      Rollover
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                          <td align="center" data-id="__react-email-column">
                            <table
                              align="center"
                              width="100%"
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                              style="width: 100%"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        color: rgb(36, 36, 36);
                                      "
                                    >
                                      {{ rollover }}
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <hr
                      style="
                        width: 100%;
                        border: none;
                        border-top: 1px solid #eaeaea;
                        margin-top: -0px;
                        margin-bottom: -0px;
                      "
                    />
                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation"
                      style="
                        background-color: rgb(245, 250, 255);
                        padding-left: 24px;
                        padding-right: 24px;
                      "
                    >
                      <tbody style="width: 100%">
                        <tr style="width: 100%">
                          <td
                            align="left"
                            data-id="__react-email-column"
                            style="width: 200px; max-width: 200px"
                          >
                            <table
                              align="center"
                              width="100%"
                              class=""
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        text-align: left;
                                        color: #24242480;
                                      "
                                    >
                                      Interest + Principal
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                          <td align="center" data-id="__react-email-column">
                            <table
                              align="center"
                              width="100%"
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                              style="width: 100%"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        color: rgb(36, 36, 36);
                                      "
                                    >
                                      ₦{{ estimated_amount }}
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <hr
                      style="
                        width: 100%;
                        border: none;
                        border-top: 1px solid #eaeaea;
                        margin-top: -0px;
                        margin-bottom: -0px;
                      "
                    />
                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation"
                      style="
                        background-color: rgb(245, 250, 255);
                        padding-left: 24px;
                        padding-right: 24px;
                      "
                    >
                      <tbody style="width: 100%">
                        <tr style="width: 100%">
                          <td
                            align="left"
                            data-id="__react-email-column"
                            style="width: 200px; max-width: 200px"
                          >
                            <table
                              align="center"
                              width="100%"
                              class=""
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        text-align: left;
                                        color: #24242480;
                                      "
                                    >
                                      Interest option
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                          <td align="center" data-id="__react-email-column">
                            <table
                              align="center"
                              width="100%"
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                              style="width: 100%"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        color: rgb(36, 36, 36);
                                      "
                                    >
                                      {{ interest_type }}
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
            <table
              align="center"
              width="100%"
              border="0"
              cellpadding="0"
              cellspacing="0"
              role="presentation"
              style="text-align: left; margin-top: 12px"
            >
              <tbody>
                <tr>
                  <td>
                    <p
                      class="font-outfit"
                      style="
                        font-size: 16px;
                        line-height: 20px;
                        margin: 16px 0;
                        color: rgb(3, 34, 130);
                        font-weight: 700;
                      "
                    >
                      Terms and conditions
                    </p>
                  </td>
                </tr>
              </tbody>
            </table>
            <table
              align="center"
              width="100%"
              border="0"
              cellpadding="0"
              cellspacing="0"
              role="presentation"
              style="
                margin-top: 10px;
                background-color: rgb(245, 250, 255);
                padding-top: 12px;
                padding-bottom: 12px;
                padding-left: 30px;
                padding-right: 30px;
              "
            >
              <tbody>
                <tr>
                  <td>
                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation"
                    >
                      <tbody>
                        <tr>
                          <td>
                            <p
                              class="font-outfit"
                              style="
                                font-size: 16px;
                                line-height: 20px;
                                margin: 16px 0;
                                color: rgb(3, 34, 130);
                                font-weight: 700;
                              "
                            >
                              1- Interest
                            </p>
                            <p
                              class="font-outfit"
                              style="
                                font-size: 13px;
                                line-height: 20px;
                                margin: 16px 0;
                                color: rgb(78, 78, 78);
                                font-weight: 700;
                              "
                            >
                              <span
                                style="font-weight: 900; color: rgb(0, 0, 0)"
                                >1.1</span
                              >
                              The COMPANY pays interest for onlending plans
                              based on the plan duration
                            </p>
                            <p
                              class="font-outfit"
                              style="
                                font-size: 13px;
                                line-height: 20px;
                                margin: 16px 0;
                                color: rgb(78, 78, 78);
                                margin-top: -5px;
                                font-weight: 700;
                              "
                            >
                              <span
                                style="font-weight: 900; color: rgb(0, 0, 0)"
                                >1.2</span
                              >
                              Interest will be calculated by the duration
                              selected and can be assessed depending on the
                              interest withdrawal plan option selected by the
                              ONLENDER on plan creation.
                            </p>
                            <p
                              class="font-outfit"
                              style="
                                font-size: 13px;
                                line-height: 20px;
                                margin: 16px 0;
                                color: rgb(78, 78, 78);
                                margin-top: -5px;
                                font-weight: 700;
                              "
                            >
                              <span
                                style="font-weight: 900; color: rgb(0, 0, 0)"
                                >1.3</span
                              >
                              The accrued interest on the onlending plan shall
                              be subjected to withholding tax of 10% in line
                              with Federal Government of Nigeria.
                            </p>
                            <p
                              class="font-outfit"
                              style="
                                font-size: 16px;
                                line-height: 20px;
                                margin: 16px 0;
                                color: rgb(3, 34, 130);
                                font-weight: 700;
                              "
                            >
                              2- Final provisions
                            </p>
                            <p
                              class="font-outfit"
                              style="
                                font-size: 13px;
                                line-height: 20px;
                                margin: 16px 0;
                                color: rgb(78, 78, 78);
                                font-weight: 700;
                              "
                            >
                              The COMPANY may decline the renewal of the plan at
                              its own discretion.
                            </p>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
            <table
              align="center"
              width="100%"
              border="0"
              cellpadding="0"
              cellspacing="0"
              role="presentation"
              style="width: 100%; margin-top: 24px"
            >
              <tbody>
                <tr>
                  <td>
                    <table
                      align="center"
                      width="100%"
                      class=""
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation"
                    >
                      <tbody style="width: 100%">
                        <tr style="width: 100%">
                          <td
                            align="left"
                            data-id="__react-email-column"
                            style="
                              border-width: 1px;
                              border-style: solid;
                              border-color: rgb(234, 234, 234);
                              padding-top: 15px;
                              padding-bottom: 15px;
                              padding-left: 24px;
                              padding-right: 24px;
                            "
                          >
                            <table
                              align="center"
                              width="100%"
                              class=""
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        text-align: left;
                                        color: rgb(36, 36, 36);
                                      "
                                    >
                                      <strong> Company:</strong> Liberty Assured
                                      limited
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                          <td
                            align="center"
                            data-id="__react-email-column"
                            style="
                              border-width: 1px;
                              border-style: solid;
                              border-color: rgb(234, 234, 234);
                              padding-top: 15px;
                              padding-bottom: 15px;
                              padding-left: 24px;
                              padding-right: 24px;
                            "
                          >
                            <table
                              align="center"
                              width="100%"
                              class=""
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                            >
                              <tbody>
                                <tr>
                                  <td>
                                    <p
                                      class="font-bolder font-outfit"
                                      style="
                                        font-size: 14px;
                                        line-height: 15.12px;
                                        margin: 16px 0;
                                        color: rgb(36, 36, 36);
                                      "
                                    >
                                      <strong> Onlender:</strong> {{ onlender }}
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
  </body>
</html>
