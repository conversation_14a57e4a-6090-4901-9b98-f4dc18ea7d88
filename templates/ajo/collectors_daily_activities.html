<!DOCTYPE html
	PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
	xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
	<!--[if gte mso 9]>
	<xml>
		<o:OfficeDocumentSettings>
		<o:AllowPNG/>
		<o:PixelsPerInch>96</o:PixelsPerInch>
		</o:OfficeDocumentSettings>
	</xml>
	<![endif]-->
	<meta http-equiv="Content-type" content="text/html; charset=utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="format-detection" content="date=no" />
	<meta name="format-detection" content="address=no" />
	<meta name="format-detection" content="telephone=no" />
	<meta name="x-apple-disable-message-reformatting" />
	<!--[if !mso]><!-->
	<link href="https://fonts.googleapis.com/css?family=Muli:400,400i,700,700i" rel="stylesheet" />
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Nunito&display=swap" rel="stylesheet">
	<!--<![endif]-->
	<title>*|Password|*</title>
	<!--[if gte mso 9]>
	<style type="text/css" media="all">
		sup { font-size: 100% !important; }
	</style>
	<![endif]-->


	<style type="text/css" media="screen">
		/* Linked Styles */
		body {
			padding: 0 !important;
			margin: 0 !important;
			display: block !important;
			min-width: 100% !important;
			width: 100% !important;
			background: #D2DAFF;
			-webkit-text-size-adjust: none
		}

		a {
			color: #66c7ff;
			text-decoration: none
		}

		p {
			padding: 0 !important;
			margin: 0 !important
		}

		img {
			-ms-interpolation-mode: bicubic;
			/* Allow smoother rendering of resized image in Internet Explorer */
		}

		.mcnPreviewText {
			display: none !important;
		}

		.cke_editable,
		.cke_editable a,
		.cke_editable span,
		.cke_editable a span {
			color: #000001 !important;
		}

		/* Mobile styles */
		@media only screen and (max-device-width: 480px),
		only screen and (max-width: 480px) {
			.mobile-shell {
				width: 100% !important;
				min-width: 100% !important;
			}

			.text-header,
			.m-center {
				text-align: center !important;
			}

			.center {
				margin: 0 auto !important;
			}

			.container {
				padding: 20px 10px !important
			}

			.td {
				width: 100% !important;
				min-width: 100% !important;
			}

			.m-br-15 {
				height: 15px !important;
			}

			.p30-15 {
				padding: 30px 15px !important;
			}

			.m-td,
			.m-hide {
				display: none !important;
				width: 0 !important;
				height: 0 !important;
				font-size: 0 !important;
				line-height: 0 !important;
				min-height: 0 !important;
			}

			.m-block {
				display: block !important;
			}

			.fluid-img img {
				width: 100% !important;
				max-width: 100% !important;
				height: auto !important;
			}

			.column,
			.column-top,
			.column-empty,
			.column-empty2,
			.column-dir-top {
				float: left !important;
				width: 100% !important;
				display: block !important;
			}

			.column-empty {
				padding-bottom: 10px !important;
			}

			.column-empty2 {
				padding-bottom: 30px !important;
			}

			.content-spacing {
				width: 15px !important;
			}
		}
	</style>
</head>



<body class="body"
	style="padding:0 !important; margin:0 !important; display:block !important; min-width:100% !important; width:100% !important;  -webkit-text-size-adjust:none;">
	<table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#D2DAFF">
		<tr>
			<td align="center" valign="top">
				<table width="750" border="0" cellspacing="0" cellpadding="0" class="mobile-shell" bgcolor="#F0F0F0"
					style="padding: 20px;">
					<tr>
						<td class="td container"
							style="width:650px; min-width:650px; font-size:0pt; line-height:0pt; margin:0; font-weight:normal; padding:5px 0px;">

							<!-- HEADER -->
							<div mc:repeatable="Select" mc:variant="Intro">
								<table width="100%" border="0" cellspacing="0" cellpadding="0">
									<tr>
										<td style="padding-bottom: 10px;">
											<table width="100%" border="0" cellspacing="0" cellpadding="0">
												<tr>
													<td class="tbrr p30-15">
														<img
															src="https://res.cloudinary.com/eddiewurld/image/upload/v1650555036/Group_7455_ubtd7b.png" />
													</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
							</div>
							<!-- END HEADER -->

							<!-- Intro -->
							<div mc:repeatable="Select" mc:variant="Intro">
								<table width="100%" border="0" cellspacing="0" cellpadding="0"
									style="margin-top: -10px;">
									<tr>
										<td style="padding-bottom: 10px;">
											<table width="100%" border="0" cellspacing="0" cellpadding="0">
												<tr>
													<td class="tbrr p30-15"
														style="padding: 30px 30px 20px 30px; border-radius:0px 0px 0px 0px; margin-top: -20px;"
														bgcolor="#ffffff">
														<table width="100%" border="0" cellspacing="0" cellpadding="0">
															<tr>
																<td class="h1 pb25"
																	style="color:#000000; font-family:'Muli', Arial,sans-serif; font-size:14px; line-height:35px; text-align:left; padding-bottom:25px;">
																	<div mc:edit="text_2">
																					$message
																			 <br />
																		
																		<span
																			style="color:#000000; font-family:'Nunito', Arial,sans-serif; font-size:15px; line-height:35px; text-align:left; padding-bottom:30px; font-weight: bolder;"></span>

																	</div>
																</td>
															</tr>
															<tr>
																<td class="text-center pb25"
																	style="color:#000000; font-family:'Nunito', Arial,sans-serif; font-size:16px; line-height:25px; text-align:left; padding-bottom:25px;">
																	<div mc:edit="text_3" align="left">
																	 <br /> <br />
																		<span
																			style="padding-top: 30px; color:#000000; font-family:'Nunito', Arial,sans-serif; font-size:35px; line-height:35px; text-align:left; padding-bottom:30px; font-weight: bolder; letter-spacing: 3px;"></span>
																	</div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
							</div>
							<!-- END Intro -->
						</td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
</body>

</html>
