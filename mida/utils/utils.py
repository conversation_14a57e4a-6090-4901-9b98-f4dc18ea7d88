# from cryptography.fernet import Fe<PERSON>t
# from decouple import config

# ENCRYPTION_KEY = config("MIDA_ENCRYPTION_KEY", None)

# if ENCRYPTION_KEY is None:
#     raise ValueError("Missing MIDA_ENCRYPTION_KEY! Are you sure sure you've set it in the environment?")

# cipher = Fernet(ENCRYPTION_KEY)

# def encrypt_password(password):
#     return cipher.encrypt(password.encode()).decode()

# def decrypt_password(encrypted_password):
#     return cipher.decrypt(encrypted_password.encode()).decode()