from rest_framework import permissions
from rest_framework.exceptions import PermissionDenied
from decouple import config
from mida.models import MidaRequestsLog
from .helpers.enums import MidaRequestType

class HostWhitelistPermission(permissions.BasePermission):
    def has_permission(self, request, view):
        host = request.get_host()

        if host in config("MIDA_URLS"):
            MidaRequestsLog.objects.create(
                url=host,
                request_type=MidaRequestType.DISPOSITION
            )

            return host
        else:
            raise PermissionDenied()