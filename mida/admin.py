from django.contrib import admin
from mida.models import MidaDisposition, MidaRequestsLog
from .resources import MidaDispositionResource, MidaRequestsLogResource
from import_export.admin import ImportExportModelAdmin

class MidaDispositionResourceAdmin(ImportExportModelAdmin):
    resource_class = MidaDispositionResource
    search_fields = ["payload"]
    list_filter = ["created_at"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class MidaRequestsLogResourceAdmin(ImportExportModelAdmin):
    resource_class = MidaRequestsLogResource
    search_fields = ["payload", "response", "request_type"]
    list_filter = ["created_at", "request_type"]

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields] + ["mida_payload"]
        data.remove("payload")
        data.remove("response")
        data.remove("request_type")
        return data

admin.site.register(MidaDisposition, MidaDispositionResourceAdmin)
admin.site.register(MidaRequestsLog, MidaRequestsLogResourceAdmin)
