from mida.serializers import MidaAjoLoanSerializer
from savings.pagination import CustomPagination
from rest_framework.response import Response
from rest_framework import status
from rest_framework.views import APIView
from loans.models import AjoLoan
from .models import MidaAuthCredentials, MidaDisposition
from .permissions import HostWhitelistPermission
# from mida.utils.utils import encrypt_password
# from .tasks import send_loan_to_mida

class MidaLoanDataView(APIView):
    pagination_classes = CustomPagination

    def get(self, request):
        loan_details = AjoLoan.objects.all()
        serialized_loan_details = MidaAjoLoanSerializer(
            loan_details, 
            context={"request": request}, 
            many=True
        )
        return Response(
            {
                "message": "Loan data retrieved successfully",
                "loan_details": serialized_loan_details.data,
            }, status=status.HTTP_200_OK
        )
    
    # def post(self, request):
    #     domain = request.META.get("HTTP_HOST", "unknown")
    #     try:
    #         loan_details = send_loan_to_mida()
    #         return Response(
    #             {
    #                 "message": "Daily loans data sent successfully!",
    #                 "loan_data": loan_details,
    #                 "domain": domain
    #             }, status=status.HTTP_200_OK
    #         )
    #     except Exception as e:
    #         return Response(
    #             {
    #                 "message": "An error occurred while trying to send load data",
    #                 "error": e,
    #             }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
    #         )
    

class PushDispositionView(APIView):
    permission_classes = [HostWhitelistPermission]

    def post(self, request, **kwargs):
        disposition = request.data

        mida_disposition = MidaDisposition.objects.create(
            payload=str(disposition)
        )

        try:
            return Response(
                {
                    "message": "Disposition sent successfully!",
                    "disposition": disposition,
                }, status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {
                    "message": "Disposition receipt failed!",
                    "error": str(e),
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# class MidaEmailView(APIView):
#     def post(self, request):
#         email = request.data.get("email")
#         password = request.data.get("password")
#         username = request.data.get("username")

#         password = encrypt_password(password=password)

#         mida_account, created = MidaAuthCredentials.objects.update_or_create(
#             defaults={
#                 "email": email,
#                 "username": username,
#                 "password": password,
#             }
#         )

#         return Response(
#             {
#                 "message": "Mida account details saved successfully!",
#                 "status": created
#             }
#         )