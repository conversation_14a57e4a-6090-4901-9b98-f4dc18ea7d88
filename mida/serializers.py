from datetime import datetime
import datetime as dt
from rest_framework import serializers
from loans.models import <PERSON>joLoan, AjoLoanSchedule, AjoUser
from ajo.models import BankAccountDetails

LAGOS_TZ = "Africa/Lagos"

class MidaAjoLoanSerializer(serializers.ModelSerializer):
    class Meta:
        model = AjoLoan
        exclude = [
                "id",
                "agent",
                "eligibility",
                "guarantor",
                "guarantor_full_name",
                "guarantor_phone_number",
                "guarantor_bvn_nin_name",
                "guarantor_bvn_nin_phone",  
                "amount_saved",
                "bnpl_amount",  
                "application_type",            
                "verification_stage",
                "loan_behavior",
                "daily_health_allocation",
                "processing_fee",
                "nem_fee",
                "plan_created",
                "previous_renewal_amount",
                "total_renewal_amount",
                "expected_repayment_count",
                "count_of_recorded_repayments",
                "const_insurance_value",
                "insurance_fee_on_repayment",
                "loandisk_loan_id",
                "tenor",
                "repayment_type",
                "tenor_in_days",
                "branch_loandisk_loan_id",
                "repayment_savings",
                "supervisor_disbursement_status",
                "loan_disbursement_buddy_response",
                "unique_loan_ref",
                "loan_ref",
                "loan_topup_from",
                "ussd_code",
                "kyc_doc_ref",
                "processing_fee_settlement_status",
                "processing_fee_settlement_response",
                "checked_by",
                "supervisor_checks_completed",
                "repeat_loan_id",
                "escrow_settled",
                "escrow_liquidated",
                "repayment_health_score",
                "repayment_percent",
                "timeliness_score",
                "escrow_amount",
                "escrow_offset",
                "failed_to_disbursed_via_vfd",
                "consent_given",
                "last_disbursement_attempt",
                "last_failed_disbursement_attempt",
                "failed_disbursement_payload",
                "synced_with_repayments",
                "missed_repayment_tables_resolved",
                "number_of_spend_disbursements",
                "assigned_agent",
                "loan_due_date",
                "moved_to_recovery",
                "recovered",
                "promise_to_pay",
                "promise_status",
                "promise_to_pay_amount",
                "additional_instruction",
                "comment",
                "is_fraud",
                "active",
                "resolved",
                "recovery_reason",   
                "message",     
                "updated_at",
                "created_at",
                "borrower_full_name",
                "processing_fee_charged",
                "face_match",
                "is_disbursed",
                "date_completed",
                "end_date_issue",
                "principal_daily_repayment",
                "agent_disbursement_amount",
                "daily_repayment_amount",
                "expected_end_date",
                "actual_end_date",
                "principal_repayment",
                "start_date",
                "end_date",
                "performance_status",
                "due_today_amount",
                "borrower_phone_number",
                "amount_disbursed",
                "loan_type",
                "total_paid_amount",
                "total_outstanding_balance",
                "last_paid_amount",
                "interest_rate",
                "interest_amount",
                "date_disbursed",
                "repayment_amount",
                "status",
                "amount",
                "sent_to_mida",
                "borrower",
            ]

    def to_representation(self, instance:AjoLoan):
        representation = super().to_representation(instance)

        borrower = instance.borrower

        if borrower:
            representation["customerLastName"] = borrower.last_name
            representation["customerDOB"] = borrower.dob
            representation["customerOccupation"] = borrower.trade
            if borrower.image:
                request = self.context.get('request')
                if request is not None:
                    representation["customerImage"] = request.build_absolute_uri(borrower.image.url)
                else:
                    representation["customerImage"] = borrower.image.url
            else:
                representation["customerImage"] = None
                
            representation["customer"] = {
                "fullname": borrower.first_name + borrower.last_name,
                "BVN": borrower.bvn,
                "sex": borrower.gender,
                "phoneNumber": borrower.phone_number,
            }
            representation["address"] = {
                "address": borrower.address,
                "state": borrower.state,
                "lga": borrower.lga,
            }

        schedules = instance.ajoloanschedule_set.all()
        schedule_data = []
        for schedule in schedules:
            if instance.date_disbursed < datetime.now(dt.timezone.utc) + dt.timedelta(days=1):
                representation["isNewLoan"] = False
            else:
                representation["isNewLoan"] = True

            schedule_data.append(
                {
                    "dueDate": str(schedule.due_date.strftime("%Y-%m-%d")) if schedule.due_date else None,
                    "amount": schedule.due_amount,
                    "isNewLoan": representation["isNewLoan"]
                }
            )
    
        # bank_account_details = instance.bankaccountdetails_set.all()
        # account_details = []
        # for account in bank_account_details:
        #     account_details.append(
        #         {
        #             "accountName": account.account_name,
        #             "accountNumber": account.account_number,
        #             "bankName": account.bank_name,
        #         }
        #     )

        
        loan_account_dict = instance.get_repayment_account_info()
        loan_account_number = loan_account_dict["account_number"]
        bank_name = loan_account_dict["bank_name"]
        account_name = loan_account_dict["account_name"]
        representation["account"] = {
            "bankName": bank_name,
            "accountNumber" : loan_account_number,
            "accountName" : account_name,
        }
        representation["loanAccountNumber"] = loan_account_number
        representation["loanAmount"] = instance.amount_disbursed
        representation["bucket"] = instance.bucket
        representation["loanId"] = instance.loan_ref
        representation["loanType"] = instance.loan_type
        representation["loanTotalOutstandingAmount"] = instance.due_today_amount
        representation["loanTotalOutstandingPrincipal"] = instance.repayment_amount
        representation["loanTotalOutstandingInterest"] = instance.interest_rate
        representation["loanDisbursmentDate"] = str(instance.date_disbursed)
        representation["state"] = instance.performance_status
        representation["status"] = instance.status
        representation["schedule"] = schedule_data

        representation["outstandingDueToday"] = instance.outstanding_due_today
        representation["amountDisbursed"] = instance.amount_disbursed
        representation["totalPaidAmount"] = instance.total_paid_amount
        representation["totalOutstandingBalance"] = instance.total_outstanding_balance
        representation["lastPaidAmount"] = instance.last_paid_amount
        representation["interestRate"] = instance.interest_rate
        representation["interestAmount"] = instance.interest_amount
        representation["repaymentAmount"] = instance.repayment_amount

        return representation
