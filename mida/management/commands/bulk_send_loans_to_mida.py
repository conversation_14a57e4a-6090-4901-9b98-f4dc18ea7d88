from django.core.management.base import BaseCommand
from loans.models import AjoLoan
from loans.enums import LoanStatus
from decouple import config
from django.conf import settings
import requests
from mida.models import MidaRequestsLog
from mida.helpers.enums import MidaRequestType

class Command(BaseCommand):
    help = ''

    def handle(self, *args, **kwargs):

        BATCH_SIZE = 100
        url = f"{config('MIDA_LIVE_BASE_URL')}/external/bulk-loan"
        token = config("MIDA_LIVE_API_KEY")
        headers = {
            "Content-type": "application/json",
            "Authorization": f"Bearer {token}"
        }
        all_loans = AjoLoan.objects.filter(
            status=LoanStatus.OPEN,
            sent_to_mida=False
        )

        loan_objects = list(all_loans)
        all_disbursed_loans = {"loans": [loan_object.mida_loan for loan_object in loan_objects]}
        loans_list = all_disbursed_loans["loans"]

        for i in range(0, len(loans_list), BATCH_SIZE):
            batch_data = {"loans":loans_list[i:i + BATCH_SIZE]}
            batch_loans = loan_objects[i:i + BATCH_SIZE]
            try:
                response = requests.post(url=url, headers=headers, json=batch_data)
                
                response.raise_for_status()

                MidaRequestsLog.objects.create(
                    payload=batch_data,
                    url=url,
                    response=response.text,
                    request_type=MidaRequestType.LOAN
                )

                for loan in batch_loans:
                    loan.sent_to_mida = True
                    loan.save()

                print(f"Successfully sent batch {i//BATCH_SIZE + 1}: {response.json()}")
            except requests.exceptions.RequestException as e:
                print(f"EXCEPTION>>>>>>>>>>>>>{str(e)}\n JSON_RESPONSE: {response.json()}","\n\n\n\n\n")
        