from django.db.models import TextChoices

class Interaction(TextChoices):
    CALL = "CALL", "CALL"
    SMS = "SMS", "SMS"
    WHATSAPP = "WHATSAPP", "WHATSAPP"
    EMAIL = "EMAIL", "EMAIL"
    HOME_VISIT = "HOME_VISIT", "HOME_VISIT"
    OFFICE_VISIT =  "OFFICE_VISIT", "OFFICE_VISIT"
    BUSINESS_VISIT = "BUSINESS_VISIT", "BUSINESS_VISIT"


class Rpc(TextChoices):
    YES = "YES", "YES"
    NO = "NO", "NO"
    LANGUAGE_BARRIER = "LANGUAGE_BARRIER", "LANGUAGE_BARRIER"


class No_Rpc_Reason(TextChoices):
    NUMBER_BELONGS_TO_SOMEONE_ELSE = "NUMBER_BELONGS_TO_SOMEONE_ELSE", "NUMBER_BELONGS_TO_SOMEONE_ELSE"
    NUMBER_DOES_NOT_EXIST_OR_DID_NOT_RING = "NUMBER_DOES_NOT_EXIST_OR_DID_NOT_RING", "NUMBER_DOES_NOT_EXIST_OR_DID_NOT_RING"
    NUMBER_BARRED = "NUMBER_BARRED", "NUMBER_BARRED"
    NUMBER_SWITCHED_OFF = "NUMBER_SWITCHED_OFF", "NUMBER_SWITCHED_OFF"
    RINGING_NO_RESPONSE = "RINGING_NO_RESPONSE", "RINGING_NO_RESPONSE"
    INAUDIBLE_CONVERSATION_OR_POOR_NETWORK_OR_NOISY_AREA = "INAUDIBLE_CONVERSATION_OR_POOR_NETWORK_OR_NOISY_AREA", "INAUDIBLE_CONVERSATION_OR_POOR_NETWORK_OR_NOISY_AREA"


class Default_Reason(TextChoices):
    APP_ISSUES = "APP_ISSUES", "APP_ISSUES"
    BANK_TRANSFER_ISSUES = "BANK_TRANSFER_ISSUES", "BANK_TRANSFER_ISSUES"
    CHANGED_JOBS = "CHANGED_JOBS", "CHANGED_JOBS"
    DELAYED_SALARY = "DELAYED_SALARY", "DELAYED_SALARY"
    FAILED_DEBIT = "FAILED_DEBIT", "FAILED_DEBIT"
    FINANCIAL_OR_PERSONAL_ISSUES = "FINANCIAL_OR_PERSONAL_ISSUES", "FINANCIAL_OR_PERSONAL_ISSUES"
    HEALTH_ISSUES = "HEALTH_ISSUES", "HEALTH_ISSUES"
    MATERNITY = "MATERNITY", "MATERNITY"
    OUT_OF_JOB_OR_SUSPENSION = "OUT_OF_JOB_OR_SUSPENSION", "OUT_OF_JOB_OR_SUSPENSION"
    OUT_OF_TOWN_OR_OFFSHORE = "OUT_OF_TOWN_OR_OFFSHORE", "OUT_OF_TOWN_OR_OFFSHORE"
    SCHOOL_FEES = "SCHOOL_FEES", "SCHOOL_FEES"
    DELAYED_FUNDS = "DELAYED_FUNDS", "DELAYED_FUNDS"
    OUT_OF_BUSINESS_OR_SLOW_BUSINESS = "OUT_OF_BUSINESS_OR_SLOW_BUSINESS", "OUT_OF_BUSINESS_OR_SLOW_BUSINESS"
    NO_OR_LESS_INTENTION_TO_PAY = "NO_OR_LESS_INTENTION_TO_PAY", "NO_OR_LESS_INTENTION_TO_PAY"
    LOAN_OR_INSTALMENT_ALREADY_PAID = "LOAN_OR_INSTALMENT_ALREADY_PAID", "LOAN_OR_INSTALMENT_ALREADY_PAID"
    CHANGE_IN_SALARY_DATE = "CHANGE_IN_SALARY_DATE", "CHANGE_IN_SALARY_DATE"
    UNAWARE_OF_DISBURSED_LOAN = "UNAWARE_OF_DISBURSED_LOAN", "UNAWARE_OF_DISBURSED_LOAN"
    REQUEST_FOR_RESTRUCTURE = "REQUEST_FOR_RESTRUCTURE", "REQUEST_FOR_RESTRUCTURE"
    FRAUD = "FRAUD", "FRAUD"
    OTHERS = "OTHERS", "OTHERS"


class Disposition(TextChoices):
    PAYMENT_PROMISE = "PAYMENT_PROMISE", "PAYMENT_PROMISE" 
    EVADING_CALLS = "EVADING_CALLS", "EVADING_CALLS"
    AGGRESSIVE_RESPONSE = "AGGRESSIVE_RESPONSE", "AGGRESSIVE_RESPONSE"
    FINANCIALLY_UNABLE_TO_PAY = "FINANCIALLY_UNABLE_TO_PAY", "FINANCIALLY_UNABLE_TO_PAY"
    CONTESTING_OVERDUE_AMOUNT = "CONTESTING_OVERDUE_AMOUNT", "CONTESTING_OVERDUE_AMOUNT"
    RESCHEDULE_VISIT = "RESCHEDULE_VISIT", "RESCHEDULE_VISIT"
    NEED_MORE_CUSTOMER_INFORMATION = "NEED_MORE_CUSTOMER_INFORMATION", "NEED_MORE_CUSTOMER_INFORMATION"
    CUSTOMER_ASKED_TO_BE_CONTACTED_LATER = "CUSTOMER_ASKED_TO_BE_CONTACTED_LATER", "CUSTOMER_ASKED_TO_BE_CONTACTED_LATER"
    OTHER = "OTHER", "OTHER"


class MidaLoanStatus(TextChoices):
    PENDING = "PENDING", "PENDING"
    PARTIALLY_PAID = "PARTIALLY_PAID", "PARTIALLY_PAID"
    PAID = "PAID", "PAID"
    INACTIVE ="INACTIVE", "INACTIVE"

class MidaRequestType(TextChoices):
    REPAYMENT = "REPAYMENT", "REPAYMENT"
    LOAN = "LOAN", "LOAN"
    DISPOSITION = "DISPOSITION", "DISPOSITION"


class MidaRequestStatus(TextChoices):
    FAILED = "FAILED", "FAILED"
    SUCCESSFUL = "SUCCESSFUL", "SUCCESSFUL" 