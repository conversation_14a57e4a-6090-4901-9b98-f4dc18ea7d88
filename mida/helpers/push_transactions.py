import pytz
import requests
from decouple import config
from helper_methods import format_api_response
from django.conf import settings

import json
from ..models import MidaAuthCredentials
from datetime import datetime
# from mida.utils.utils import decrypt_password 

LAGOS_TIMEZONE = pytz.timezone("Africa/Lagos")


class MidaPushNotification:
    @classmethod
    def notify_mida_of_repayment(self, **kwargs):
        if settings.ENVIRONMENT == "development":
            url = f"{config('MIDA_STAGING_BASE_URL')}/external/saveTransaction"
            mida_api_key = config("MIDA_STAGING_API_KEY")
            headers = {
            "Authorization": f"Bearer {mida_api_key}",
            "Content-Type": "application/json",
        }
        else:
            url = f"{config('MIDA_LIVE_BASE_URL')}/external/saveTransaction"
            mida_api_key = config("MIDA_LIVE_API_KEY")
            headers = {
            "Authorization": f"Bearer {mida_api_key}",
            "Content-Type": "application/json",
        }

        # url = f"{config('MIDA_STAGING_BASE_URL')}/external/saveTransaction"

        # access_token = MidaTokens.store_tokens()
        # auth_tokens = MidaTokens.retrieve_tokens()
        # access_token = auth_tokens["access_token"]

        # mida_api_key = f"{config('MIDA_API_KEY')}"
        
        # headers = {
        #     "Authorization": f"Bearer {mida_api_key}",
        #     "Content-Type": "application/json",
        # }
        
        payload = kwargs
        response = requests.post(url=url, headers=headers, json=payload)

        # if response == 401:
        #     mida_tokens = MidaTokens.refresh_tokens()

        return format_api_response(payload=payload, response=response, url=url)

# class MidaTokens:

#     @classmethod
#     def store_tokens(cls):
#         url = f"{config('MIDA_AUTH_URL')}"

#         headers = {
#             "Content-type": "application/json"
#         }

#         mida_email_instance = MidaAuthCredentials.objects.all().last()
#         email = mida_email_instance.email
#         password = mida_email_instance.password
#         password = decrypt_password(encrypted_password=password)
#         username = email

#         payload = {
#                 "email": email,
#                 "username": username,
#                 "password": password
#             }                   
#         response = requests.post(url=url, headers=headers, json=payload)

#         if response.status_code != 201:
#             print(response.text)
#             return "Not Successful"

#         response_data = response.json()

#         print("STATUS_CODE::::", response.status_code, "\n\n\n\n\n")

#         access_token=response_data["token"]

#         refresh_token=response_data["refreshToken"]

#         expires_at = datetime.fromtimestamp(datetime.now().timestamp() + 900, tz=LAGOS_TIMEZONE).replace(microsecond=0)

#         try:
#             mida_account, created = MidaAuthCredentials.objects.update_or_create(
#                 email="<EMAIL>",
#                 defaults={
#                     "access_token": response_data["token"],
#                     "refresh_token": response_data["refreshToken"],
#                     "organisation_id": "db2f2223-a322-4313-b7ea-8a43270c0e02",
#                     "expires_at": expires_at,
#                 }
#             )

#             mida_account_json = {
#                 "email": mida_account.email,
#                 "access_token": mida_account.access_token,
#                 "refresh_token": mida_account.refresh_token,
#                 "status": created,
#             }
#             print("MIDA_ACCOUNT_JSON:::::", mida_account_json, "\n\n\n\n\n")

#             return access_token
#         except Exception as e:
#             return {
#                 "message": "An error occurred while attempting to save Mida email instance!",
#                 "error": str(e)
#             }
    
    # @classmethod
    # def retrieve_tokens(cls):
    #     mida_email_instance = MidaAuthCredentials.objects.all().last()
    #     access_token = mida_email_instance.access_token
    #     refresh_token = mida_email_instance.refresh_token

    #     return {
    #         "access_token": access_token,
    #         "refresh_token": refresh_token,
    #     }
    
    # @classmethod
    # def refresh_tokens(cls):
    #     url = f"{config('MIDA_AUTH_URL')}"
        
    #     mida_email_instance = MidaAuthCredentials.objects.all().last()
    #     email = mida_email_instance.email
    #     password = mida_email_instance.password
    #     password = decrypt_password(password)
    #     username = email

    #     response = requests.post()