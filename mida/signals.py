from datetime import datetime
from django.db.models.signals import post_save
from django.dispatch import receiver
from loans.models import AjoLoanRepayment, AjoLoan

# from django.db import transaction
# from mida.helpers.push_transactions import MidaPushNotification
from mida.tasks import notify_mida_on_repayment


@receiver(post_save, sender=AjoLoanRepayment)
def mida_repayment_signal(sender, instance, created, **kwargs):
    if created:

        ajo_loan = instance.ajo_loan
        mida_loan_data = ajo_loan.mida_loan
        loan_account_dict = ajo_loan.get_repayment_account_info()
        loan_account_number = loan_account_dict["account_number"]
        raw_paid_date = instance.paid_date
        paid_date = datetime.strftime(raw_paid_date, "%Y-%m-%d")
        mida_payload = {
            "loanAccountNumber": loan_account_number,
            "amountPaid": ajo_loan.last_paid_amount,
            "amountRequested": ajo_loan.total_outstanding_balance,
            "loanId": ajo_loan.id,
            "transactionDate": paid_date,
            "narration": str(instance.repayment_ref),
        }
        notify_mida_on_repayment.apply_async(
            queue="documentation",
            kwargs={
                "mida_payload": mida_payload,
            },
        )

