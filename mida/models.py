from django.db import models
from loans.models import <PERSON><PERSON><PERSON><PERSON>
from ajo.models import <PERSON><PERSON><PERSON>ser
from mida.helpers.enums import (
        Interaction, Rpc, No_Rpc_Reason,
        Default_Reason, Disposition, MidaRequestType, MidaRequestStatus
    )
import json
from django.template.defaultfilters import truncatechars

class MidaAuthCredentials(models.Model):
    email = models.EmailField(max_length=255)
    access_token = models.TextField(max_length=255)
    refresh_token = models.TextField(max_length=255)
    organisation_id = models.Char<PERSON>ield(max_length=255)
    username = models.CharField(max_length=255)
    password = models.Char<PERSON>ield(max_length=255)
    expires_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.email}"
    
    class Meta:
        verbose_name = "MIDA EMAIL"
        verbose_name_plural = "MIDA EMAIL"


class MidaRequestsLog(models.Model):
    payload = models.JSONField(default=dict, blank=True, null=True)
    response = models.JSONField(default=dict, blank=True, null=True)
    request_type = models.CharField(max_length=255, blank=True, null=True, choices=MidaRequestType.choices)
    url = models.CharField(max_length=255, blank=True, null=True)
    status = models.CharField(max_length=255, blank=True, null=True, choices=MidaRequestStatus.choices)
    created_at = models.DateTimeField(auto_now_add=True)

    @property
    def mida_payload(self):
        payload = json.dumps(self.payload)
        return truncatechars(payload, 100)

    def __str__(self):
        return f"{self.pk}"
    
    class Meta:
        verbose_name = "MIDA REQUESTS LOG"
        verbose_name_plural = "MIDA REQUESTS LOGS"


class MidaDisposition(models.Model):
    payload = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.pk

    class Meta:
        verbose_name = "MIDA DISPOSITION"
        verbose_name_plural = "MIDA DISPOSITIONS"
