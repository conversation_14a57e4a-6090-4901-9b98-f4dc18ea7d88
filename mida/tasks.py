from datetime import datetime
import json
from celery import shared_task
from loans.models import Ajo<PERSON>oan, AjoLoanRepayment
from loans.enums import <PERSON>an<PERSON>tatus, LoanBehavior
from mida.helpers.push_transactions import MidaPushNotification
# from mida.serializers import MidaAjoLoanSerializer
from mida.models import MidaRequestsLog
import requests
from decouple import config
from .helpers.enums import MidaLoanStatus, MidaRequestStatus, MidaRequestType
from django.conf import settings


@shared_task
def send_loan_to_mida():
    loans_queryset = (
        AjoLoan.objects.filter(
            loan_behavior__in=[
                LoanBehavior.PAST_MATURITY,
                LoanBehavior.PARTIALPAYMENT,
                LoanBehavior.DEFAULTED,
                LoanBehavior.LATE,
            ],
            sent_to_mida=False,
        )
        .select_related("borrower")
        .prefetch_related("borrower__ajo_user_vfd_accounts")
    )

    for loan in loans_queryset:
        if settings.ENVIRONMENT == "development":
            url = f"{config('MIDA_STAGING_BASE_URL')}/external/loan"
            token = config("MIDA_STAGING_API_KEY")
            headers = {
                "Content-type": "application/json",
                "Authorization": f"Bearer {token}",
            }
        else:
            url = f"{config('MIDA_LIVE_BASE_URL')}/external/loan"
            token = config("MIDA_LIVE_API_KEY")
            headers = {
                "Content-type": "application/json",
                "Authorization": f"Bearer {token}",
            }

        mida_loan_data = loan.mida_loan

        try:
            init_response = requests.post(url=url, headers=headers, json=mida_loan_data)
            response = init_response.text
        except Exception as e:
            response = str(e)

        mida_request_instance = MidaRequestsLog.objects.create(
            payload=mida_loan_data,
            url=url,
            response=response,
            request_type=MidaRequestType.LOAN,
        )

        if isinstance(response, dict) and init_response.status in [201, 200]:
            loan.sent_to_mida = True
            loan.save()
            mida_request_instance.status == MidaRequestStatus.SUCCESSFUL
            mida_request_instance.save()
        else:
            mida_request_instance.status == MidaRequestStatus.FAILED
            mida_request_instance.save()
            print(
                f"Failed to send loan ID {loan.pk} to MIDA. Status code: {init_response.status_code}"
            )
    return str(response)


@shared_task
def notify_mida_on_repayment(mida_payload):
    
    notify_mida = MidaPushNotification()

    mida_notification = notify_mida.notify_mida_of_repayment(**mida_payload)

    MidaRequestsLog.objects.create(
        payload=mida_payload,
        url=mida_notification.get("url"),
        response=mida_notification.get("response"),
        request_type=MidaRequestType.REPAYMENT,
    )

    return mida_notification