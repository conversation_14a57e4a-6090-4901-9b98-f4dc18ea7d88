from bnpl_app.models import Category, Subcategory, Brand, Product, ProductPrice


class ProductManager:

    @staticmethod
    def create_product(
        main_category, subcategory, name, title, product_model, product_price,image
    ):
        """
        Creates instances for category, subcategory, brand, product, and price.

        Parameters:
        main_category (str): The name of the main category.
        subcategory (str): The title of the subcategory.
        name (str): The name of the brand.
        title (str): The specification of the product.
        product_model (str): The name of the product model.
        product_price (float): The price of the product.

        Returns:
        None
        """
        category_instance = Category.get_or_create_category_instance(name=main_category)
        subcategory_instance = Subcategory.get_or_create_subcategory_instance(
            subcategory_title=subcategory, category_name=main_category
        )
        brand_instance = Brand.get_or_create_brand_instance(
            brand_name=name, category_instance=category_instance
        )
        product_instance = Product.get_or_create_product_instance(
            brand_instance=brand_instance,
            subcategory_instance=subcategory_instance,
            specification=title,
            product_name=product_model,
            image=image
        )
        ProductPrice.get_or_create_price_instance(
            product_instance=product_instance, amount=product_price
        )
