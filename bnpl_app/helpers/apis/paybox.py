from dataclasses import dataclass
import json
from django.conf import settings

from helper_methods import format_api_response
import requests


@dataclass
class PayboxMgr:

    environment = settings.ENVIRONMENT
    if environment == "development":
        # base_url = "https://requisitiondev.libertypayng.com"
        base_url = "https://dragon-humble-blatantly.ngrok-free.app"
    else:
        base_url = "https://management.libertypayng.com"

    headers = {"Content-Type": "application/json"}

    @classmethod
    def get_category_and_subcategory_details(
        cls,
        company_id: str,
        category_name: str,
        subcategory_name: str,
        brand_name: str,
    ) -> dict:

        url = f"{cls.base_url}/api/v1/stock/external/query_category_subcategoty/"

        payload = json.dumps(
            {
                "company_id": company_id,
                "category_name": category_name,
                "subcategory_name": subcategory_name,
                "brand_name": brand_name,
            }
        )

        response = requests.post(url, headers=cls.headers, data=payload)
        return format_api_response(payload=payload, response=response, url=url)

    @classmethod
    def create_product(
        cls,
        company_id: str,
        brand_id: str,
        category_id: str,
        sub_category_id: str,
        product_details: dict,
        supplier,
    ):

        url = f"{cls.base_url}/api/v1/stock/suppliers/bnpl-product"

        data = {
            "company_id": company_id,
            "products": [
                {
                    "brand_id": brand_id,
                    "product_name": product_details["product_name"],
                    "category_id": category_id,
                    "sub_category_id": sub_category_id,
                    "description": product_details["description"],
                    "tags": "good,better,best",
                    "model": product_details["item_model"],
                    "image_urls": [product_details["image_urls"]],
                    "product_details": [
                        {
                            "price": product_details["price"],
                            "stock": product_details["stock"],
                            # "sku": "Test123",
                            # "size": "M",
                            # "color": "Black",
                            # "weight": "0.2",
                            # "length": "1.2",
                            # "width": "1.1",
                            # "height": "1.4",
                            # "discount": "10",
                            "low_stock_threshold": "1",
                            "shipping_days": "3",
                        }
                    ],
                }
            ],
        }

        if isinstance(supplier, dict):
            data["supplier_data"] = [
                {
                    "phone_number": supplier.get("phone_number"),
                    "first_name": supplier.get("first_name"),
                    "last_name": supplier.get("last_name"),
                    "business_name": supplier.get("business_name"),
                    "business_email": supplier.get("business_email"),
                    "address": supplier.get("address"),
                }
            ]
        else:
            data["supplier_id"] = supplier

        payload = json.dumps(data)
        response = requests.request("POST", url, headers=cls.headers, data=payload)
        return format_api_response(payload=payload, response=response, url=url)

    @classmethod
    def get_products(
        cls,
    ):

        url = f"{cls.base_url}/api/v1/stock/suppliers/bnpl-product?request_type=product"

        response = requests.request("GET", url, headers=cls.headers)
        return format_api_response(payload=None, response=response, url=url)
