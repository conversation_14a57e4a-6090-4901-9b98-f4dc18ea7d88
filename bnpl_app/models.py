import uuid
from django.db import models
from django.core.cache import cache
from accounts.helpers import BaseModel
from django.contrib.auth import get_user_model
from bnpl_app.enums import VendorItemRequestStatus
from datetime import datetime

from bnpl_app.helpers.apis.paybox import PayboxMgr

CustomUser = get_user_model()


class Category(BaseModel):
    name = models.CharField(
        max_length=255, unique=True
    )  # e.g., "Electronics", "Home Appliances"
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Category"
        verbose_name_plural = "Categories"

    @classmethod
    def get_or_create_category_instance(cls, name: str):
        if not name:
            return None
        else:
            instance, created = cls.objects.get_or_create(name=name.title())
            return instance

    @classmethod
    def get_categories(cls):
        """
        Retrieves a list of active categories from the cache or the database.

        Steps:
        1. Attempt to fetch the list of active categories from the cache using the key "bnpl_item_categories".
        2. If the categories are not in the cache:
           a. Query the database to retrieve active categories (`is_active=True`).
           b. Store the query result in the cache for future requests (with a timeout of 30 minutes).
        3. Return the list of active categories.

        Returns:
            QuerySet: A QuerySet of active categories.
        """
        cache_key = "bnpl_item_categories"
        cache_timeout = 60 * 30  # 30 minutes

        # Attempt to fetch cached categories
        category_qs = cache.get(cache_key)

        if category_qs is None:
            # Query the database if categories are not in the cache
            category_qs = cls.objects.filter(is_active=True)
            # Store the result in the cache
            cache.set(cache_key, category_qs, timeout=cache_timeout)

        return category_qs


class Subcategory(BaseModel):
    category = models.ForeignKey(
        Category, on_delete=models.CASCADE, related_name="subcategories"
    )
    name = models.CharField(max_length=255)  # e.g., "Phones", "Air Conditioners"

    def __str__(self):
        return f"({self.category.name}) {self.name}"

    class Meta:
        verbose_name = "Subcategory"
        verbose_name_plural = "Subcategories"
        unique_together = ("category", "name")

    @classmethod
    def get_or_create_subcategory_instance(
        cls, category_name: str, subcategory_title: str
    ):
        if not category_name or not subcategory_title:
            return None
        else:
            category_name = category_name.title()
            try:

                category_instance = Category.objects.get(name=category_name)
            except Category.DoesNotExist:
                category_instance = Category.objects.create(name=category_name)

            instance, created = cls.objects.get_or_create(
                name=subcategory_title.title(), category=category_instance
            )
            return instance

    @classmethod
    def get_sub_categories(cls, category_id):

        cache_key = f"bnpl_item_subcategories{category_id}"
        cache_timeout = 60 * 30  # 30 minutes

        # Attempt to fetch cached categories
        subcategory_qs = cache.get(cache_key)

        if subcategory_qs is None:
            # Query the database if categories are not in the cache
            subcategory_qs = cls.objects.filter(category_id=category_id)
            # Store the result in the cache
            cache.set(cache_key, subcategory_qs, timeout=cache_timeout)

        return subcategory_qs


class Brand(BaseModel):
    """
    Represents a brand or manufacturer of a product within a given category.

    This model is used to categorize products by the brand or company that produces them.
    Each brand belongs to a specific category (e.g., Electronics, Fashion, etc.) and contains
    information such as the brand name and an optional description.

    Fields:
        - category: A foreign key linking this brand to a specific category,
          indicating which type of products the brand is associated with (e.g., Electronics,
          Fashion, etc.). This ensures that brands are grouped according to their respective
          product categories.

        - name: The name of the brand, such as "Samsung", "Apple", or "Nike". This is the
          main identifier for the brand.

        - description: A detailed text description of the brand, which can include information
          about the brand's history, values, or other relevant details. This field is optional
          and can be left blank.

    Methods:
        __str__: Returns the name of the brand when the instance is represented as a string.

    Example:
        A Brand instance might represent "Samsung" under the category "Electronics".
    """

    category = models.ForeignKey(
        Category, on_delete=models.CASCADE, related_name="brands"
    )
    name = models.CharField(max_length=255)  # e.g., "Samsung", "LG"
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Brand"
        verbose_name_plural = "Brand"
        unique_together = ("category", "name")

    @classmethod
    def get_or_create_brand_instance(
        cls, brand_name: str, category_instance: Category, description=None
    ):
        if not brand_name or not category_instance:
            return None
        else:

            instance, created = cls.objects.get_or_create(
                name=brand_name.title(), category=category_instance
            )
            return instance


class Product(BaseModel):
    """
    Represents a product offered for sale, associated with a specific subcategory and brand.

    This model is used to store information about a product in the system, including its
    name, associated brand, subcategory, and any optional description or model number.
    It provides the necessary details to link a product to its respective brand and
    subcategory for easy categorization and searchability.

    Fields:
        - subcategory: A foreign key linking this product to a specific subcategory,
          which helps classify the product under a more specific group within its broader
          category (e.g., "Mobile Phones" under the "Electronics" category).

        - brand: A foreign key linking this product to a specific brand,
          which represents the manufacturer or company behind the product (e.g., "Samsung", "Apple").

        - name: The name of the product (e.g., "Galaxy S10", "MacBook Pro"). This is the primary identifier
          for the product and will be displayed in user interfaces and listings.

        - model_number: An optional field to store the product's model number or identifier.
          This is useful for distinguishing between different versions of the same product (e.g., "iPhone 13 Pro").

        - description: A detailed description of the product, which could include key features, specifications,
          or other relevant information. This field is optional and can be left blank.

    Methods:
        __str__: Returns a string representation of the product, including its name, brand name, and subcategory name.

    Example:
        A Product instance might represent "Samsung Galaxy S10" under the subcategory "Mobile Phones",
        and the brand "Samsung".
    """

    subcategory = models.ForeignKey(
        Subcategory,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="products",
    )
    brand = models.ForeignKey(Brand, on_delete=models.CASCADE, related_name="products")
    name = models.CharField(max_length=255)
    model_number = models.CharField(max_length=255, null=True, blank=True)
    specification = models.CharField(max_length=500, null=True, blank=True)
    image_url = models.CharField(max_length=500, null=True, blank=True)
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"BRAND: {self.brand.name} DETAILS: {self.specification}"

    class Meta:
        verbose_name = "Product"
        verbose_name_plural = "Product"
        unique_together = ("brand", "name", "specification", "subcategory")

    @classmethod
    def get_or_create_product_instance(
        cls,
        brand_instance: str,
        subcategory_instance: Subcategory,
        specification: str,
        product_name: str,
        image: str,
    ):
        if not product_name or not brand_instance:
            return None
        else:

            instance, created = cls.objects.get_or_create(
                name=product_name.title(),
                brand=brand_instance,
                specification=specification,
                subcategory=subcategory_instance,
            )
            instance.image_url = image
            instance.save()
            return instance


class ProductPrice(BaseModel):
    product = models.ForeignKey(
        Product, on_delete=models.CASCADE, related_name="prices"
    )
    # amount = models.DecimalField(max_digits=10, decimal_places=2)  # Product price
    amount = models.FloatField(default=0)  # Product price
    effective_date = models.DateTimeField(
        auto_now_add=True
    )  # When this price became effective

    def __str__(self):
        return f"{self.product.name} - {self.amount} (Effective: {self.effective_date})"

    class Meta:
        verbose_name = "Price"
        verbose_name_plural = "Price"

    @property
    def amount_in_naira(self):
        if isinstance(self.amount, (int, float)):
            formatted_amount = "{:,.2f}".format(self.amount)
            return f"₦{formatted_amount}"
        else:
            return "Invalid amount"

    @classmethod
    def get_or_create_price_instance(
        cls,
        product_instance: Product,
        amount: float,
    ):
        if not product_instance:
            return None
        else:

            instance, created = cls.objects.get_or_create(
                product=product_instance,
                amount=amount,
            )
            return instance


class Vendor(BaseModel):
    agent = models.ForeignKey(
        CustomUser, on_delete=models.CASCADE, null=True, blank=True
    )
    business_name = models.CharField(max_length=100)
    email = models.EmailField(max_length=100, unique=True)
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    phone_number = models.CharField(max_length=15)
    state = models.CharField(max_length=100)
    local_government = models.CharField(max_length=100)
    nearest_landmark = models.CharField(max_length=300, blank=True, null=True)
    shop_number = models.CharField(max_length=100, blank=True, null=True)
    full_shop_address = models.CharField(max_length=300, null=True, blank=True)
    paybox_supplier_id = models.CharField(max_length=300, null=True, blank=True)

    def __str__(self):
        return f"{self.business_name} - {self.first_name} {self.last_name}"

    class Meta:
        verbose_name = "Vendor"
        verbose_name_plural = "Vendor"

    @classmethod
    def get_or_create_vendor(cls, email, **kwargs):
        instance, created = cls.objects.get_or_create(email=email, defaults=kwargs)
        # return {
        #     "id": instance.id,
        #     "created": created,
        #     "business_name": instance.business_name,
        #     "email": instance.email,
        #     "first_name": instance.first_name,
        #     "last_name": instance.last_name,
        #     "phone_number": instance.phone_number,
        #     "state": instance.state,
        #     "local_government": instance.local_government,
        #     "nearest_landmark": instance.nearest_landmark,
        #     "shop_number": instance.shop_number,
        #     "full_shop_address": instance.full_shop_address,
        #     "paybox_supplier_id": instance.paybox_supplier_id,
        # }
        return instance


class VendorRequest(BaseModel):
    vendor = models.ForeignKey(
        Vendor,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="vendor_requests",
    )
    item = models.ForeignKey(
        ProductPrice, on_delete=models.SET_NULL, null=True, blank=True
    )
    item_brand = models.CharField(max_length=100)
    item_model = models.CharField(max_length=100, blank=True, null=True)
    # Status Field
    status = models.CharField(
        max_length=20,
        choices=VendorItemRequestStatus.choices,
        default=VendorItemRequestStatus.PENDING,  # Default status
    )
    additional_details = models.CharField(max_length=300, blank=True, null=True)
    current_cost_price = models.FloatField(default=0.0)
    item_image = models.TextField(null=True, blank=True)
    product_id = models.CharField(max_length=100, unique=True, default="")
    paybox_company_id = models.CharField(
        max_length=300, editable=False, blank=True, null=True
    )
    category_name = models.CharField(
        max_length=300, editable=False, blank=True, null=True
    )
    subcategory_name = models.CharField(
        max_length=300, editable=False, blank=True, null=True
    )

    def save(self, *args, **kwargs):
        if not self.product_id:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            unique_id = str(uuid.uuid4()).split("-")[0]
            self.product_id = f"{timestamp}{unique_id}"
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Request for {self.item_brand} {self.item_model}"

    @classmethod
    def create_request(cls, vendor_details: dict, item_details: dict):
        # get or create vendor
        email = vendor_details["email"]
        vendor_data = {
            "agent_id": vendor_details["agent_id"],
            "first_name": vendor_details["first_name"],
            "business_name": vendor_details["business_name"],
            "last_name": vendor_details["last_name"],
            "phone_number": vendor_details["phone_number"],
            "state": vendor_details["state"],
            "local_government": vendor_details["local_government"],
            "nearest_landmark": vendor_details["nearest_landmark"],
            "shop_number": vendor_details["shop_number"],
            "full_shop_address": vendor_details["full_shop_address"],
        }
        vendor = Vendor.get_or_create_vendor(email=email, **vendor_data)

        request_instance = cls.objects.create(
            vendor=vendor,
            item_id=item_details["product_price_id"],
            item_brand=item_details["item_brand"],
            item_model=item_details["item_model"],
            additional_details=item_details["additional_details"],
            current_cost_price=item_details["current_cost_price"],
            item_image=item_details["item_image"],
            paybox_company_id=item_details["paybox_company_id"],
            category_name=item_details["category_name"],
            subcategory_name=item_details["subcategory_name"],
        )
        item_model = item_details["item_model"]
        item = request_instance.item
        if not item:
            item_brand = item_details["item_brand"]
            product_details = {
                "product_name": f"{item_brand}-{item_model}",
                "description": item_details["additional_details"],
                "image_urls": item_details["item_image"],
                "stock": 1,
                "price": item_details["current_cost_price"],
                "item_model": item_model,
            }
        else:
            product = item.product
            product_details = {
                "product_name": product.name,
                "description": (
                    product.specification
                    if not product.description
                    else product.description
                ),
                "image_urls": [product.image_url],
                "stock": 1,
                "price": item.amount,
                "item_model": item_model,
            }

        pbx_mgr = PayboxMgr()

        if not vendor.paybox_supplier_id:
            supplier = {
                "business_email": email,
                "phone_number": vendor_details["phone_number"],
                "first_name": vendor_details["first_name"],
                "last_name": vendor_details["last_name"],
                "business_name": vendor_details["business_name"],
                "address": vendor_details["full_shop_address"],
            }
        else:
            supplier = vendor.paybox_supplier_id

        request_response = pbx_mgr.create_product(
            company_id=item_details["paybox_company_id"],
            brand_id=item_details["brand_id"],
            category_id=item_details["category_id"],
            sub_category_id=item_details["subcategory_id"],
            product_details=product_details,
            supplier=supplier,
        )

        RequestLog.objects.create(
            vendor_item_request=request_instance, payload=request_response
        )

        if request_response["status"] == "success" and request_response[
            "status_code"
        ] in [200, 201]:

            response = request_response["response"]
            data = response["data"]
            supplier = data["supplier"]
            vendor.paybox_supplier_id = supplier["id"]
            vendor.save(update_fields=["paybox_supplier_id"])
            msg = response["message"]
            return True, msg

        else:
            request_instance.status = VendorItemRequestStatus.FAILED
            request_instance.save(update_fields=["status"])
            if request_response["status_code"] >= 500:
                return False, "Unable to process request. Please contact support"
            else:

                response = request_response["response"]
                return (
                    False,
                    response.get("message")
                    or response.get("detail")
                    or "Unable to process request. Please contact support",
                )


class RequestLog(BaseModel):
    vendor_item_request = models.ForeignKey(
        VendorRequest, on_delete=models.CASCADE, null=True, blank=True
    )
    payload = models.TextField(null=True, blank=True)

    # def __str__(self):
    #     return f"Request for {self.sub_category} by {self.business_name}"
