from django.db.models.signals import post_save
from django.core.cache import cache
from django.dispatch import receiver

from bnpl_app.models import Category, Subcategory


# Signal receiver to clear the cache when the instance or model is updated
@receiver(post_save, sender=Category)
def clear_constant_table_cache(sender, instance, **kwargs):
    cache_key = "bnpl_item_categories"
    cache.delete(cache_key)


@receiver(post_save, sender=Subcategory)
def clear_constant_table_cache(sender, instance, **kwargs):
    
    cache_key = f"bnpl_item_subcategories{instance.category.id}"
    cache.delete(cache_key)
