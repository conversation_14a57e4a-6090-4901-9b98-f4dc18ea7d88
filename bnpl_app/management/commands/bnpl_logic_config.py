import requests
from django.core.management import BaseCommand


from bnpl_app.helpers.apis.paybox import PayboxMgr

from django.conf import settings
from web_scrap import LLMEnhancedScraper

logger = settings.LOGGER
from pprint import pprint
from loans.tasks import *


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **options):

        # liberty_lifemgr = LibertyLifeMgr()

        # i = liberty_lifemgr.update_credit_health_request_status_stages(
        #     loan_access_code="ZB50G9M8D8TBTHQ", stage="", request_status="PENDING"
        # )

        # i = liberty_lifemgr.get_credit_health_details(
        #     loan_access_code="ZB50G9M8D8TBTHQ"
        # )
        # print(i, "\n\n")

        # scraper = LLMEnhancedScraper(
        #     "", "***************************************************", ""
        # )
        # # jumia_urls = "https://www.jumia.com.ng/catalog/?q=phones"
        # jumia_urls = "https://www.jumia.com.ng/health-beauty"

        # scrapped_file_jumia = scraper.scrape_product(jumia_urls)

        # # url = "https://savingsdev.libertypayng.com/api/v1/bnpl/wbhook/update-product/"
        # url = "https://dragon-humble-blatantly.ngrok-free.app/api/v1/bnpl/wbhook/update-product/"

        # # payload = normalize_data(scrapped_file_jumia)
        # payload = json.dumps(scrapped_file_jumia)
        # headers = {}

        # response = requests.request("POST", url, headers=headers, json=payload)

        i = PayboxMgr().get_products()
        # i = PayboxMgr.create_product()
        pprint(i)
