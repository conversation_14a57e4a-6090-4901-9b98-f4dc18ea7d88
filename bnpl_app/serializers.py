from rest_framework import serializers

from bnpl_app.helpers.apis.paybox import PayboxMgr
from bnpl_app.models import <PERSON>, ProductPrice, Vendor, VendorRequest


class VendorRequestSerializer(serializers.Serializer):

    paybox_company_id = serializers.Char<PERSON>ield(max_length=255)
    category_name = serializers.CharField(max_length=255)
    subcategory_name = serializers.CharField(max_length=255)
    item_brand = serializers.CharField(max_length=255)
    item_model = serializers.CharField(max_length=255)
    business_name = serializers.CharField(max_length=255)
    item_image = serializers.CharField(max_length=255)
    product_price_id = serializers.CharField(
        max_length=255, allow_null=True, allow_blank=True
    )

    additional_details = serializers.CharField(
        max_length=500, default="No additional details provided"
    )
    current_cost_price = serializers.FloatField()

    email = serializers.EmailField()
    phone_number = serializers.Char<PERSON><PERSON>(max_length=15)
    first_name = serializers.Char<PERSON>ield(max_length=15)
    last_name = serializers.CharField(max_length=15)
    state = serializers.CharField(max_length=255, default="Unknown State")
    local_government = serializers.CharField(max_length=255, default="Unknown LGA")
    nearest_landmark = serializers.CharField(
        max_length=255, default="No landmark provided"
    )
    shop_number = serializers.CharField(max_length=50, default="N/A")
    full_shop_address = serializers.CharField(
        max_length=500, default="No address provided"
    )
    item_image = serializers.CharField(default="")

    def validate(self, data):

        item_brand = data.get("item_brand")
        item_model = data.get("item_model")
        current_cost_price = data.get("current_cost_price")
        category_name = data.get("category_name")
        subcategory_name = data.get("subcategory_name")
        paybox_company_id = data.get("paybox_company_id")
        email = data.get("email")

        # get vendor
        vendor = Vendor.objects.filter(email=email).last()

        vendor_request = VendorRequest.objects.filter(
            vendor=vendor,
            item_brand=item_brand,
            item_model=item_model,
            current_cost_price=current_cost_price,
        )
        if vendor_request.exists():
            raise serializers.ValidationError(
                "Item request exist. Kindly confirm details"
            )

        paybox_category_subcategory_details = (
            PayboxMgr().get_category_and_subcategory_details(
                category_name=category_name,
                subcategory_name=subcategory_name,
                company_id=paybox_company_id,
                brand_name=item_brand,
            )
        )
        paybox_company_status_code = paybox_category_subcategory_details["status_code"]
        if paybox_company_status_code != 200:
            raise serializers.ValidationError(
                f"Unable to fectch company details ({paybox_company_status_code})"
            )

        company_data = paybox_category_subcategory_details["response"]["data"]

        print(company_data)

        data["category_id"] = company_data["category_id"]
        data["subcategory_id"] = company_data["sub_category_id"]
        data["brand_id"] = company_data["brand_id"]
        return data


class GetProductsSerializer(serializers.Serializer):

    def to_representation(self, instance: ProductPrice):
        representation = super().to_representation(instance)
        product_instance = instance.product

        subcategory = product_instance.subcategory
        category = subcategory.category

        representation["product_id"] = instance.id
        representation["price"] = instance.amount
        representation["amount_in_naira"] = instance.amount_in_naira
        representation["brand"] = product_instance.brand.name
        representation["model"] = product_instance.name
        representation["model_number"] = product_instance.model_number
        representation["specification"] = product_instance.specification
        representation["category_id"] = category.id
        representation["category"] = category.name
        representation["subcategory"] = subcategory.name
        representation["subcategory_id"] = subcategory.id
        representation["image_url"] = product_instance.image_url

        return representation


class GetBrandsSerializer(serializers.Serializer):

    def to_representation(self, instance: Brand):
        representation = super().to_representation(instance)

        category = instance.category

        representation["id"] = instance.id
        representation["name"] = instance.name
        representation["description"] = instance.description
        representation["category"] = category.name
        representation["category_id"] = category.id

        return representation
