# import json
# from typing import Dict

# from django import forms
# from django.conf import settings
from django.contrib import admin, messages

# from django.http import HttpResponseRedirect
# from django.urls import path
from import_export.admin import ImportExportModelAdmin

from .resources import *


class CategoryResourceAdmin(ImportExportModelAdmin):
    resource_classes = [CategoryResource]
    # search_fields = []
    # list_filter = []

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class SubcategoryResourceAdmin(ImportExportModelAdmin):
    resource_classes = [SubcategoryResource]
    # search_fields = []
    # list_filter = []

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class BrandResourceAdmin(ImportExportModelAdmin):
    resource_classes = [BrandResource]
    # search_fields = []
    # list_filter = []

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class ProductResourceAdmin(ImportExportModelAdmin):
    resource_classes = [ProductResource]
    # search_fields = []
    # list_filter = []

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class ProductPriceResourceAdmin(ImportExportModelAdmin):
    resource_classes = [ProductPriceResource]
    search_fields = [
        "product__specification",
    ]
    # list_filter = ["product__category"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields] + ["amount_in_naira"]


class VendorRequestResourceAdmin(ImportExportModelAdmin):
    search_fields = ("email",)

    list_filter = ("status",)

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.fields]
        return data


class RequestLogResourceAdmin(ImportExportModelAdmin):
    # resource_classes = []
    # search_fields = ("email",)

    # list_filter = ("status",)

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.fields]
        return data
class VendorResourceAdmin(ImportExportModelAdmin):
    # resource_classes = []
    # search_fields = ("email",)

    # list_filter = ("status",)

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.fields]
        return data


admin.site.register(Category, CategoryResourceAdmin)
admin.site.register(Subcategory, SubcategoryResourceAdmin)
admin.site.register(Brand, BrandResourceAdmin)
admin.site.register(Product, ProductResourceAdmin)
admin.site.register(ProductPrice, ProductPriceResourceAdmin)
admin.site.register(VendorRequest, VendorRequestResourceAdmin)
admin.site.register(RequestLog, RequestLogResourceAdmin)
admin.site.register(Vendor, VendorResourceAdmin)
