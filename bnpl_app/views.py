# import json
import json
from pprint import pprint

# from django.core.cache import cache
# from django.db.models import F, Sum
# from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django_filters.rest_framework import DjangoFilter<PERSON>ackend

# from drf_yasg import openapi
# from drf_yasg.utils import swagger_auto_schema
from rest_framework import filters, generics, permissions, status, authentication
from rest_framework.exceptions import NotFound
from rest_framework.generics import GenericAPIView
from rest_framework.response import Response
from rest_framework.views import APIView

from accounts.permissions import IsBlackListed, IsLoanAvailable
from bnpl_app.helpers.apis.paybox import PayboxMgr
from bnpl_app.models import Brand, Category, ProductPrice, Subcategory, VendorRequest
from bnpl_app.serializers import (
    GetBrandsSerializer,
    GetProductsSerializer,
    VendorRequestSerializer,
)
from bnpl_app.services import ProductManager
from helper_methods import <PERSON>NPLProductHelper
from savings.pagination import CustomPagination


class GetBNPLProductCategorieAndSubcategories(GenericAPIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    # serializer_class = LoanTopUpSerializer

    def get(self, request):
        categories = Category.get_categories()
        queryset_values = list(categories.values("id", "name"))

        return Response(
            data={"status": True, "message": "success", "data": queryset_values},
            status=status.HTTP_200_OK,
        )

    def post(self, request):
        category_id = request.GET.get("category_id")
        if not category_id:
            return Response(
                data={
                    "status": False,
                    "message": "Category ID is required",
                    "data": None,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:

            subcategories_qs = Subcategory.get_sub_categories(category_id=category_id)
            queryset_values = list(subcategories_qs.values("id", "name"))
            # filtered_data =
            return Response(
                data={
                    "status": True,
                    "message": "success",
                    "category_id": category_id,
                    "data": queryset_values,
                },
                status=status.HTTP_200_OK,
            )


class VendorRequestAPIView(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)

    def get(self, request):

        _vendor_products = PayboxMgr().get_products()
        response = _vendor_products.get("response")
        status_code = _vendor_products.get("status_code")

        if status_code == 200:
            return Response(data=response, status=status.HTTP_200_OK)
        else:
            return Response(data=_vendor_products, status=status.HTTP_400_BAD_REQUEST)

    def post(self, request):
        serializer = VendorRequestSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        vendor_details = {
            "email": validated_data["email"],
            "agent_id": request.user.id,
            "first_name": validated_data["first_name"],
            "business_name": validated_data["business_name"],
            "last_name": validated_data["last_name"],
            "phone_number": validated_data["phone_number"],
            "state": validated_data["state"],
            "local_government": validated_data["local_government"],
            "nearest_landmark": validated_data["nearest_landmark"],
            "shop_number": validated_data["shop_number"],
            "full_shop_address": validated_data["full_shop_address"],
        }

        item_details = {
            "paybox_company_id": validated_data["paybox_company_id"],
            "category_name": validated_data["category_name"],
            "subcategory_name": validated_data["subcategory_name"],
            "item_brand": validated_data["item_brand"],
            "item_model": validated_data["item_model"],
            "item_image": validated_data["item_image"],
            "product_price_id": validated_data["product_price_id"],
            "additional_details": validated_data["additional_details"],
            "current_cost_price": validated_data["current_cost_price"],
            "category_id": validated_data["category_id"],
            "subcategory_id": validated_data["subcategory_id"],
            "brand_id": validated_data["brand_id"],
        }

        create_vendor_request = VendorRequest.create_request(
            vendor_details=vendor_details, item_details=item_details
        )

        result = {
            "status": create_vendor_request[0],
            "message": create_vendor_request[1],
            "data": serializer.data,
        }
        return Response(
            data=result,
            status=(
                status.HTTP_200_OK
                if create_vendor_request[0] is True
                else status.HTTP_400_BAD_REQUEST
            ),
        )


class PostProductWebHook(APIView):

    def post(self, request):
        data = request.data
        try:
            payload = json.loads(data)
        except json.JSONDecodeError as err:
            # print("Failed to parse JSON:", e)
            pass

        for i in payload:
            title = i.get("title")
            price = i.get("price")
            image = i.get("image")
            bnpl_product_description_handler = BNPLProductHelper(
                item_description=title, price=price, image=image
            )
            product_details = bnpl_product_description_handler.get_product_details()
            name = product_details.get("name")
            main_category = product_details.get("main_category")
            subcategory = product_details.get("subcategory")
            product_model = product_details.get("model")
            product_price = product_details.get("product_price")

            ProductManager.create_product(
                main_category=main_category,
                title=title,
                subcategory=subcategory,
                name=name,
                product_model=product_model,
                product_price=product_price,
                image=image,
            )

        return Response(data={}, status=status.HTTP_200_OK)


class GetProductsView(generics.ListAPIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    pagination_class = CustomPagination
    serializer_class = GetProductsSerializer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    search_fields = ("product__specification",)

    def get_queryset(self):
        # request_user = self.request.user
        product_prices = ProductPrice.objects.all()

        return product_prices

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)

        response_data = {
            "status": True,
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class GetBrandsView(generics.ListAPIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    pagination_class = CustomPagination
    serializer_class = GetBrandsSerializer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)
    search_fields = ("name",)

    def get_queryset(self):
        # request_user = self.request.user
        product_prices = Brand.objects.all()

        return product_prices

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)

        response_data = {
            "status": True,
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)
