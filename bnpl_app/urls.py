from django.urls import path

from .views import *

product = [
    path(
        "product-category/",
        GetBNPLProductCategorieAndSubcategories.as_view(),
        name="product_category",
    ),
    path(
        "product-price/",
        GetProductsView.as_view(),
        name="product_prices",
    ),
    path(
        "product-brands/",
        GetBrandsView.as_view(),
        name="product_brand",
    ),
]

vendor = [
    path("vendor-request/", VendorRequestAPIView.as_view(), name="vendor_request"),
]

webhook = [
    path(
        "wbhook/update-product/",
        PostProductWebHook.as_view(),
        name="product_list_webhook",
    ),
]


urlpatterns = [
    *product,
    *vendor,
    *webhook,
]
