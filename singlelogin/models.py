from datetime import datetime, timedelta
from hashlib import pbkdf2_hmac

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.db import models

User = get_user_model()


class BlackListedJWT(models.Model):
    date_created = models.DateTimeField(auto_now_add=True)
    user_id = models.PositiveIntegerField()
    blacklisted = models.BooleanField(default=False)
    login_type = models.CharField(max_length=150, null=True, blank=True)
    ip_addr = models.Char<PERSON>ield(max_length=150, null=True, blank=True)
    token = models.CharField(max_length=1000)
    user_agent = models.TextField(null=True, blank=True)
    email = models.CharField(max_length=1000, null=True, blank=True)
    serial_no = models.Char<PERSON><PERSON>(max_length=1000, null=True, blank=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def hash_jwt(cls, init_token):
        salt = settings.SECRET_KEY
        iterations = 5000  # Application specific, read above.
        dk = pbkdf2_hmac("sha256", init_token.encode(), salt.encode() * 2, iterations)
        return dk.hex()

    @classmethod
    def create_hashed_jwt(cls, user_id, init_token, ip_addr, user_agent, email=None, serial_no=None):
        list_of_exluded_user = BlacklistConstant.get_constant_table_instance().whitelisted_ids

        # get the current datetime
        now = datetime.now()

        # calculate the start and end of the current minute
        start = now.replace(second=0, microsecond=0)
        end = start + timedelta(seconds=59)

        login_num_of_times = 3
        login_num_of_times_exempted_users = 10

        if (
            cls.objects.exclude(user_id__in=list_of_exluded_user)
            .filter(user_id=user_id, date_created__gte=start, date_created__lte=end)
            .count()
            > login_num_of_times
            or cls.objects.filter(user_id__in=list_of_exluded_user)
            .filter(user_id=user_id, date_created__gte=start, date_created__lte=end)
            .count()
            > login_num_of_times_exempted_users
        ):

            if user_id in list_of_exluded_user:
                user_num_times = login_num_of_times_exempted_users
            else:
                user_num_times = login_num_of_times

            return False

        else:

            token = cls.hash_jwt(init_token=init_token)

            cls.objects.exclude(user_id__in=list_of_exluded_user).filter(user_id=user_id).update(blacklisted=True)

            login_date = datetime.now()
            login_type = "Logged in"

            new = cls.objects.create(
                user_id=user_id,
                token=token,
                login_type=login_type,
                ip_addr=ip_addr,
                user_agent=user_agent,
                email=email,
                serial_no=serial_no,
            )

            return True


class BlacklistConstant(models.Model):
    whitelisted_ids = models.JSONField(default=list)
    is_active = models.BooleanField(default=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def clean(self):
        for i in self.whitelisted_ids:
            try:
                int(i)
            except:
                raise ValidationError("JSON elements must be integers")

    @classmethod
    def get_constant_table_instance(cls):
        """ "
        This function always returns an instance of the constant table
        """
        get_multiplier = cls.objects.filter(is_active=True)
        if get_multiplier:
            constant_instance = get_multiplier.latest("last_updated")
        else:
            constant_instance = cls.objects.create()

        return constant_instance
