from enum import Enum
from django.db import models

class AvailableBankType(models.TextChoices):
    MONIEPOINT = "moniepoint", "MONIEPOINT"
    OPAY =  "opay", "OPAY"
    PALMPAY = "palmpay", "PALMPAY"

class ClusterRequestStatusType(models.TextChoices):
    AT_REQUEST = "at_request", "At Request"
    PENDING =  "pending", "Pending"
    PROCESSED =  "processed", "Processed"
    DUE = "due", "Due"
    ACTIVE = "active", "Active"
    INELIGIBLE = "ineligible", "Ineligible"

class BankMapping(Enum):
    MONIEPOINT = "090405"
    OPAY = "100004"
    PALMPAY = "100033"