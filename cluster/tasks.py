from celery import shared_task

from cluster.enums import ClusterRequestStatusType
from cluster.models import ClusterCustomer
from cluster.services.parser.interface import pdf_parser_factory

@shared_task
def process_statement_task(self_id: int):
    self = ClusterCustomer.objects.get(id=self_id)
    
    data = pdf_parser_factory(
        self.get_statement_content(),
        self.bank
    ).execute()

    self.statement_data = data
    self.request_status = ClusterRequestStatusType.PROCESSED
    self.save()



    return True
