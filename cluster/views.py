from django.db import IntegrityError

from rest_framework import generics
from rest_framework.response import Response
from rest_framework.filters import Base<PERSON><PERSON>erBackend
from rest_framework.parsers import <PERSON><PERSON>art<PERSON>arser, FormParser

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

from cluster.models import AgentC<PERSON>, ClusterCustomer
from cluster.permissions import ClusterIsAuthenticated
from cluster.serializers import (
    ClusterCustomerPatchIMAGESerializer, ClusterCustomerPatchPOASerializer, ClusterCustomerPatchSerializer, ClusterCustomerPatchStatementSerializer, ClusterCustomerSerializer,
    ClusterGetSerializer, ClusterPostSerializer
)
from savings.pagination import CustomPagination



class AgentClusterGenericAPIView(generics.GenericAPIView):
    class RequestStatusFilterBackend(BaseFilterBackend):
        def filter_queryset(self, request, queryset, view):
            request_status = request.query_params.get('request_status')
            if request_status:
                queryset = queryset.filter(customers__request_status=request_status)

            return queryset
        
    permission_classes = [ClusterIsAuthenticated]
    serializer_class = ClusterGetSerializer
    pagination_class = CustomPagination
    filter_backends = [RequestStatusFilterBackend]


        
    def get_queryset(self):
        agent = self.request.user
        return AgentCluster.objects.select_related("agent") \
            .filter(agent_id=agent.id) \
                .order_by("-created_at")


    def get_serializer_class(self):
        if self.request.method == 'GET':
            return self.serializer_class
        elif self.request.method in ['POST', 'PATCH']:
            return ClusterPostSerializer
        
        return super().get_serializer_class()
    
    def custom_get_object(self, instance_id):
        try:
            cluster = self.get_queryset().get(id=int(instance_id))
            return cluster
        except Exception as e:
            raise Exception(f"Invalid Instance ID {e}")

            
       
    @swagger_auto_schema(
        request_body=ClusterPostSerializer,
        responses={201: ClusterGetSerializer}
    )
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.filter_queryset
        
        try:
            instance = AgentCluster.objects.create(
                agent=self.request.user,
                name=serializer.validated_data["name"]
            )
        except IntegrityError:
            return Response(
                {
                    "status": False,
                    "error": "400",
                    "message": f"Name used already",
                },
                400,
            )
        
        data = self.serializer_class(instance).data
        return Response(data, 201)
    
    @swagger_auto_schema(
        request_body=ClusterPostSerializer,
        responses={200: ClusterGetSerializer},
        manual_parameters=[
            openapi.Parameter(
                "id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=True,
                description="insert id of the instance",
            ),
        ],
    )
    def patch(self, request, pk=None, *args, **kwargs):
        instance_id = self.request.query_params.get("id")
        try:
            instance = self.custom_get_object(instance_id)
        except Exception as e:
            return Response(
                {
                    "status": False,
                    "error": "400",
                    "message": f"{e}",
                },
                400,
            )

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            instance.name = serializer.validated_data["name"]
            instance.save()
        except IntegrityError:
            return Response(
                {
                    "status": False,
                    "error": "400",
                    "message": f"Name used already",
                },
                400,
            )

        
        data = self.serializer_class(instance).data
        return Response(data, 200)

       
    @swagger_auto_schema(
        responses={200: ClusterGetSerializer},
        manual_parameters=[
            openapi.Parameter(
                "id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=False,
                description="insert id of the instance",
            ),
            openapi.Parameter(
                "request_status",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="insert request status of the instance",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        instance_id = self.request.query_params.get("id")
        if instance_id:
            try:
                instance = self.custom_get_object(instance_id)
            except Exception as e:
                return Response(
                {
                    "status": False,
                    "error": "400",
                    "message": f"{e}",
                },
                400,
            )
            data = self.serializer_class(instance).data
        
        else:
            queryset = self.filter_queryset(self.get_queryset())
            paginator = self.pagination_class()
            page = paginator.paginate_queryset(queryset, request)
            serializer = self.serializer_class(page, many=True)

            data = {
                "status": "success",
                "message": "Clusters found",
                "data": serializer.data
            }

        return Response(data, 200)

    @swagger_auto_schema(
        responses={204: None},
        manual_parameters=[
            openapi.Parameter(
                "id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=True,
                description="insert id of the instance",
            ),
        ],
    )
    def delete(self, request, *args, **kwargs):
        instance_id = self.request.query_params.get("id")
        try:
            instance = self.custom_get_object(instance_id)
        except Exception as e:
            return Response(
                {
                    "status": False,
                    "error": "400",
                    "message": f"{e}",
                },
                400,
            )
        
        instance.archive()

        return Response(204)
    


class AgentClusterClusterGenericAPIView(generics.GenericAPIView):
    permission_classes = [ClusterIsAuthenticated]
    serializer_class = ClusterCustomerSerializer
    pagination_class = CustomPagination
    parser_classes = [MultiPartParser, FormParser]


    def get_queryset(self):
        agent = self.request.user
        return ClusterCustomer.objects.select_related("cluster", "ajo_user") \
            .filter(cluster__agent_id=agent.id) \
                .order_by("-created_at")

    
    def custom_get_object(self, instance_id):
        try:
            cluster = self.get_queryset().get(id=int(instance_id))
            return cluster
        except Exception as e:
            raise Exception(f"Invalid Instance ID {e}")


    def get_serializer_class(self):
        file_type = self.request.query_params.get("file_type")
        STATEMENT = "statement"
        POA = "poa"
        IMAGE = "image"

        if self.request.method == 'PATCH':
            if file_type:
                if file_type == STATEMENT:
                    print("returning")
                    return ClusterCustomerPatchStatementSerializer
                elif file_type == POA:
                    return ClusterCustomerPatchPOASerializer
                elif file_type == IMAGE:
                    return ClusterCustomerPatchIMAGESerializer
                
            return ClusterCustomerPatchSerializer

        return super().get_serializer_class()
        
    
       
    @swagger_auto_schema(
        request_body=ClusterCustomerSerializer,
        responses={201: ClusterCustomerSerializer},
    )
    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            instance: ClusterCustomer = ClusterCustomer.objects. \
                create(**serializer.validated_data)
            
            instance.refresh_from_db()
            
        except Exception as e:
            return Response(
                {
                    "status": False,
                    "error": "400",
                    "message": f"Error Occured {e}",
                },
                400,
            )
        
        data = self.serializer_class(instance).data
        return Response(data, 201)
    
     
    @swagger_auto_schema(
        request_body=ClusterCustomerPatchSerializer,
        responses={200: ClusterCustomerSerializer},
        manual_parameters=[
            openapi.Parameter(
                "id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=True,
                description="insert id of the instance",
            ),
            openapi.Parameter(
                "file_type",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="insert file type to be patched. can be 'statement', 'poa', 'image' ",
            ),
        ],
    )
    def patch(self, request, pk=None, *args, **kwargs):
        instance_id = self.request.query_params.get("id")
        try:
            instance = self.custom_get_object(instance_id)
        except Exception as e:
            return Response(
                {
                    "status": False,
                    "error": "400",
                    "message": f"{e}",
                },
                400,
            )

        serializer = self.get_serializer(data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)


        try:
            ClusterCustomer.objects. \
                update(**serializer.validated_data)
            
        except Exception as e:
            return Response(
                {
                    "status": False,
                    "error": "400",
                    "message": f"Error Occured {e}",
                },
                400,
            )

        data = self.serializer_class(instance).data
        return Response(data, 200)

       
    @swagger_auto_schema(
        responses={200: ClusterCustomerSerializer},
        manual_parameters=[
            openapi.Parameter(
                "id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=False,
                description="insert id of the instance",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        instance_id = self.request.query_params.get("id")
        try:
            instance = self.custom_get_object(instance_id)
        except Exception as e:
            return Response(
            {
                "status": False,
                "error": "400",
                "message": f"{e}",
            },
            400,
        )
        data = self.serializer_class(instance).data

        return Response(data, 200)

    @swagger_auto_schema(
        responses={204: None},
        manual_parameters=[
            openapi.Parameter(
                "id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=True,
                description="insert id of the instance",
            ),
        ],
    )
    def delete(self, request, *args, **kwargs):
        instance_id = self.request.query_params.get("id")
        try:
            instance = self.custom_get_object(instance_id)
        except Exception as e:
            return Response(
                {
                    "status": False,
                    "error": "400",
                    "message": f"{e}",
                },
                400,
            )
        
        instance.archive()

        return Response(204)
    