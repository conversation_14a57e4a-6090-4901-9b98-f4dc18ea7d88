import os
import re

from django.conf import settings
from cluster.enums import AvailableBankType
import pytesseract
import pdfrw
import tempfile
import pprint


from pdf2image import convert_from_path
from datetime import datetime
from dateutil.relativedelta import relativedelta


# On OS - 
# - Ubuntu - sudo apt-get update sudo apt-get install poppler-utils
# sudo apt-get install libleptonica-dev tesseract-ocr tesseract-ocr-dev libtesseract-dev python3-pil tesseract-ocr-eng tesseract-ocr-script-latn

# Maybes
# sudo apt install libgl1-mesa-glx 

# - Mac - brew install poppler brew install tesseract





class PDFParser:

    def __init__(self, file_path, bank_type: AvailableBankType):
        self.file_path = file_path
        self.bank_type = bank_type

        # expected_data_format = {
        #     'Account_Number': '*********',
        #     'Agent_Name': 'Business Limited',
        #     'Closing_Balance': 94.52,
        #     'Credit_Count': 69,
        #     'Current_Balance': 94.52,
        #     'Debit_Count': 33,
        #     'End_Date': '13/03/2024',
        #     'Opening_Balance': 101.52,
        #     'Start_Date': '14/12/2023',
        #     'Statement_Range': 3,
        #     'Statement_Range_Valid': True,
        #     'Total_Credit': 9038691.0,
        #     'Total_Debit': 9038698.0,
        #     'statement_closed': True
        # }

    def convert_date_range(self, start_date: str, end_date: str):

        start_date_obj = datetime.strptime(start_date, "%d/%m/%Y")
        end_date_obj = datetime.strptime(end_date, "%d/%m/%Y")

        # Get the current date
        current_date = datetime.now()

        six_months_ago = current_date - relativedelta(months=7)

        # Check if the start date is after 6 months ago and the end date is before the current date

        covers_last_six_months = start_date_obj >= six_months_ago and end_date_obj <= current_date


        num_months = (end_date_obj.year - start_date_obj.year) * 12 + (end_date_obj.month - start_date_obj.month)

        return covers_last_six_months, num_months


    def extract_moniepoint_info(self, text):
        # Define regex patterns to match the required information
        agent_name_pattern = r"Business Name\s+(.*)"
        account_number_pattern = r"Account Number\s+(\d+)"
        current_balance_pattern = r"Closing Balance\s+([\d,]+\.\d+)"
        total_credit_pattern = r"Total Credits\s+([\d,]+\.\d+)"
        total_debit_pattern = r"Total Debits\s+([\d,]+\.\d+)"
        opening_balance_pattern = r"Opening Balance\s+([\d,]+\.\d+)"
        date_range_pattern = r"(\d{2}/\d{2}/\d{4}) - (\d{2}/\d{2}/\d{4})"


        # Search for patterns in the text
        agent_name = re.search(agent_name_pattern, text).group(1).strip()
        account_number = re.search(account_number_pattern, text).group(1)
        current_balance = float(re.search(current_balance_pattern, text).group(1).replace(',', ''))
        total_credit = float(re.search(total_credit_pattern, text).group(1).replace(',', ''))
        total_debit = float(re.search(total_debit_pattern, text).group(1).replace(',', ''))
        
        credit_count = len(re.findall(r"CREDIT_0", text))
        debit_count = len(re.findall(r"DEBIT_0", text))

        opening_balance = float(re.search(opening_balance_pattern, text).group(1).replace(',', ''))
        closing_balance = float(re.search(current_balance_pattern, text).group(1).replace(',', ''))

        date_range_match = re.search(date_range_pattern, text)
        start_date = date_range_match.group(1) if date_range_match else None
        end_date = date_range_match.group(2) if date_range_match else None


        no_of_months_valid, no_of_months = self.convert_date_range(start_date, end_date)

        return {
            "Agent_Name": agent_name,
            "Account_Number": account_number,
            "Current_Balance": current_balance,
            "Total_Credit": total_credit,
            "Total_Debit": total_debit,
            "Credit_Count": credit_count,
            "Debit_Count": debit_count,
            "Opening_Balance": opening_balance,
            "Closing_Balance": closing_balance,
            "Start_Date": start_date,
            "End_Date": end_date,
            "Statement_Range_Valid": no_of_months_valid,
            "Statement_Range": no_of_months
        }


    def extract_opay_info(self, text):
        # Define regex patterns to extract data
        account_name_pattern = r"Account Name\s+(.*?)\s+Address"
        account_number_pattern = r"Account Number\s+(\d+)"
        current_balance_pattern = r"Current Balance\s+([\d,]+\.\d{2})"
        total_credit_pattern = r"Total Credit\s+(.*?)\n"
        total_debit_pattern = r"Total Debit\s+(.*?)\n"
        credit_count_pattern = r"Credit Count\s+(\d+)"
        debit_count_pattern = r"Debit Count\s+(\d+)"

        other_details_header = "Opening Balance Closing Balance Date Printed start Date"
        other_details_pattern = re.escape(other_details_header) + r"\s*([\d,]+\.\d{2})\s*([\d,]+\.\d{2})\s*(\d{2} \w{3} \d{4})\s*(\d{2} \w{3} \d{4})"

        end_date_pattern = r"End Date\s+(\d{1,2}\s+\w+\s+\d{4})"

        
        # Start Search
        agent_name = re.search(account_name_pattern, text, re.DOTALL).group(1)
        account_number = re.search(account_number_pattern, text).group(1)
        
        current_balance_str = re.search(current_balance_pattern, text).group(1)
        current_balance = float(current_balance_str.replace(',', ''))

        total_credit_str = re.search(total_credit_pattern, text).group(1)
        total_credit = float(total_credit_str.replace(',', '')[1:])

        total_debit_str = re.search(total_debit_pattern, text).group(1)
        total_debit = float(total_debit_str.replace(',', '')[1:])

        credit_count = int(re.search(credit_count_pattern, text).group(1))
        debit_count = int(re.search(debit_count_pattern, text).group(1))


        # Search for the pattern in the text
        other_details_match = re.search(other_details_pattern, text)
        opening_balance = float(other_details_match.group(1).replace(',', ''))

        closing_balance = float(other_details_match.group(2).replace(',', ''))
        start_date = datetime.strptime(other_details_match.group(4).replace(',', ''), "%d %b %Y").strftime("%d/%m/%Y")

        end_date_match = re.search(end_date_pattern, text)
        end_date = datetime.strptime(end_date_match.group(1).replace(',', ''), "%d %b %Y").strftime("%d/%m/%Y")

        no_of_months_valid, no_of_months = self.convert_date_range(start_date, end_date)

        return {
            "Agent_Name": agent_name,
            "Account_Number": account_number,
            "Current_Balance": current_balance,
            "Total_Credit": total_credit,
            "Total_Debit": total_debit,
            "Credit_Count": credit_count,
            "Debit_Count": debit_count,
            "Opening_Balance": opening_balance,
            "Closing_Balance": closing_balance,
            "Start_Date": start_date,
            "End_Date": end_date,
            "Statement_Range_Valid": no_of_months_valid,
            "Statement_Range": no_of_months
        }



    def count_palmpay_debits_and_credit(self, text):
        lines = text.split('\n')
        debit_count = 0
        total_count = 0
        opening_balance = None

        date_format = r"\d{4}/\d{2}/\d{2}"

        for line in lines:
            if "Closing Balance" in line:
                match = re.search(r"Closing Balance:\s*([\d,.]+\.\d{2})", line)
                if match:
                    opening_balance = match.group(1)
                    if '¥' in opening_balance:
                        opening_balance = opening_balance.replace('¥', '')
                    opening_balance = float(opening_balance)

                continue  # Skip lines containing "Closing Balance"
            if re.search(date_format, line):
                total_count += 1
                if '-' in line:
                    debit_count += 1

        credit_count = total_count - debit_count
        return total_count, debit_count, credit_count, opening_balance


    def extract_palmpay_info(self, text):
        # Define regex patterns to extract data
        account_name_pattern = r"Agent Name:\s+(\w+)"
        account_number_pattern = r"Account ID:\s+(\d+)"
        current_balance_pattern = r"Balance:¥([\d,]+\s*[\d,]+\.\d{2})"

        total_credit_pattern = r"In:\s*([\d,.]+)"
        total_debit_pattern = r"Out:\s*([\d,.]+)"

        start_date_pattern = r"from\s+(\d{1,2}(?:st|nd|rd|th),[a-zA-Z]+,\d{4})"
        end_date_pattern = r"to\s+(\d{1,2}(?:st|nd|rd|th),[a-zA-Z]+,\d{4})"
    
        agent_name = re.search(account_name_pattern, text).group(1)
        account_number = re.search(account_number_pattern, text).group(1)

        current_balance_str = re.search(current_balance_pattern, text).group(1)
        current_balance = float(current_balance_str.replace(',', '').replace(' ', ''))

        total_credit_str = re.search(total_credit_pattern, text).group(1)
        total_credit = float(total_credit_str.replace(',', ''))

        total_debit_str = re.search(total_debit_pattern, text).group(1)
        total_debit = float(total_debit_str.replace(',', ''))
        

        all_transactions, debit_count, credit_count, opening_balance = self.count_palmpay_debits_and_credit(text=text)

        start_date_match = re.search(start_date_pattern, text).group(1)
        end_date_match = re.search(end_date_pattern, text).group(1)

        start_date = datetime.strptime(start_date_match, "%dth,%B,%Y").strftime("%d/%m/%Y")
        end_date = datetime.strptime(end_date_match, "%dth,%B,%Y").strftime("%d/%m/%Y")

        no_of_months_valid, no_of_months = self.convert_date_range(start_date, end_date)

        return {
            "Agent_Name": agent_name,
            "Account_Number": account_number,
            "Current_Balance": current_balance,
            "Total_Credit": total_credit,
            "Total_Debit": total_debit,
            "Credit_Count": credit_count,
            "Debit_Count": debit_count,
            "Opening_Balance": opening_balance,
            "Closing_Balance": current_balance,
            "Start_Date": start_date,
            "End_Date": end_date,
            "Statement_Range_Valid": no_of_months_valid,
            "Statement_Range": no_of_months
        }



    def rotate_pdf_with_tempfile(self, input_path, output_folder="temp_dir"):
        """
        Rotates a PDF file by 90 degrees clockwise and saves it temporarily.

        Args:
            input_path (str): Path to the input PDF file.
            output_folder (str, optional): Path to a folder for temporary files. Defaults to None.

        Returns:
            str: Path to the temporary rotated PDF file.
        """

        # Create a temporary file with a unique name in the specified folder
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)

        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False, dir=output_folder) as temp_file:
            pdf = pdfrw.PdfReader(input_path)
            pages = pdf.pages
            new_pdf = pdfrw.PdfWriter()

            for page in range(len(pages)):
                pages[page].Rotate = 90
                new_pdf.addpage(pages[page])

            new_pdf.write(temp_file.name)

        return temp_file.name  




    def execute(self, remove_file=True):
        """
        This function attempts to extract text data from a PDF file.

        Args:
            pdf_file (str): Path to the PDF file.
            remove_file (bool): Whether to remove temporary images after extraction.

        Returns:
            str or None: Extracted text data or None if extraction fails.
        """
        try:
            pdf_file = self.file_path
            bank_type: AvailableBankType = self.bank_type

            CLUSTER_BASE_DIR = os.path.join(settings.BASE_DIR, "cluster", "services", "parser", "temp_media")
            output_dir = os.path.join(CLUSTER_BASE_DIR, "output_images")
            tempfile_path = os.path.join(CLUSTER_BASE_DIR, "temp_dir")

            # Create output directory if it does not exist
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            if not os.path.exists(tempfile_path):
                os.makedirs(tempfile_path)

            if pdf_file.endswith(".ppm"):
                return pytesseract.image_to_string(pdf_file)

            # Convert the PDF to images
            if bank_type == AvailableBankType.PALMPAY:
                pdf_file = self.rotate_pdf_with_tempfile(pdf_file, output_folder=tempfile_path)

            images = convert_from_path(pdf_file, output_folder=output_dir)

            extracted_text = ""

            # Loop through the images and extract text using OCR
            for image in images:
                extracted_text += pytesseract.image_to_string(image)

            # Clean up temporary images
            for image in images:
                image.close()

            # Remove temporary images and pdf if specified
            if remove_file:
                for file in os.listdir(output_dir):
                    os.remove(os.path.join(output_dir, file))

                for file in os.listdir(tempfile_path):
                    os.remove(os.path.join(tempfile_path, file))

            all_extracted_text = extracted_text.strip() if extracted_text.strip() else None

            data = {}

            if extracted_text is not None:
                match bank_type:
                    case AvailableBankType.MONIEPOINT:
                        data = self.extract_moniepoint_info(all_extracted_text)
                    case AvailableBankType.OPAY:
                        data = self.extract_opay_info(all_extracted_text)
                    case AvailableBankType.PALMPAY:
                        data = self.extract_palmpay_info(all_extracted_text)
                    case _:
                        raise Exception(f"Invalid bank type {bank_type}")

                statement_closed = self.check_statement_closed(data)
                data.update({"statement_closed": statement_closed})

        except Exception as e:
            raise Exception(f"{e}")

        finally:
            if pdf_file and os.path.exists(pdf_file):
                os.remove(pdf_file)

        return data


    def check_statement_closed(self, kwargs: dict) -> bool:
        statement_change = (kwargs["Opening_Balance"] + kwargs["Total_Credit"]) - kwargs["Total_Debit"]
        closing_balance = kwargs["Closing_Balance"]

        def round_equals(a, b, decimals=2):
            return round(a, decimals) == round(b, decimals)
    
        return round_equals(statement_change, closing_balance)




    
