from django.db.models import Q
from rest_framework import serializers


from cluster.enums import AvailableBankType, ClusterRequestStatusType
from cluster.models import AgentCluster, ClusterCustomer



class ClusterCustomerSerializer(serializers.ModelSerializer):
    paid = serializers.BooleanField(read_only=True)
    request_status = serializers.CharField(read_only=True)
    statement_data = serializers.CharField(read_only=True)
    account_name = serializers.CharField(read_only=True)
    account_number = serializers.CharField(read_only=True)

    class Meta:
        model = ClusterCustomer
        exclude = ["payment_id", "updated_at", "archived"]

class ClusterCustomerPatchStatementSerializer(serializers.ModelSerializer):
    class Meta:
        model = ClusterCustomer
        fields = ["statement"]

    
class ClusterCustomerPatchPOASerializer(serializers.ModelSerializer):

    class Meta:
        model = ClusterCustomer
        fields = ["proof_of_address"]
        
class ClusterCustomerPatchIMAGESerializer(serializers.ModelSerializer):
 
     class Meta:
        model = ClusterCustomer
        fields = ["live_image"]

class ClusterCustomerPatchSerializer(serializers.ModelSerializer):
    bvn = serializers.CharField(required=False)
    bank = serializers.ChoiceField(required=False, choices=AvailableBankType.choices)

    class Meta:
        model = ClusterCustomer
        fields = ["bvn", "bank"]



class ClusterPostSerializer(serializers.Serializer):
    name = serializers.CharField()

class ClusterGetSerializer(serializers.ModelSerializer):
    cluster_link = serializers.SerializerMethodField()
    due_cluster_loan_balance = serializers.SerializerMethodField()
    total_agents = serializers.SerializerMethodField()
    total_pending_requests = serializers.SerializerMethodField()
    customers = ClusterCustomerSerializer(many=True)

    class Meta:
        model = AgentCluster
        fields = ["id", "agent", "name", "num_of_customers",
                    "cluster_link", "balance", "due_cluster_loan_balance", 
                    "total_agents", "total_pending_requests", "customers"]
    
    def get_cluster_link(self, instance: AgentCluster):
        return instance.cluster_link
    
    def get_due_cluster_loan_balance(self, instance: AgentCluster):
        return instance.due_cluster_loan_balance()
    
    def get_total_agents(self, instance: AgentCluster):
        return instance.all_customers(
            q_objects=(
                Q(request_status=ClusterRequestStatusType.ACTIVE)
                | Q(request_status=ClusterRequestStatusType.DUE)
            )
        ).count()

    
    def get_total_pending_requests(self, instance: AgentCluster):
        return instance.all_customers(request_status=ClusterRequestStatusType.AT_REQUEST).count()
    
    def get_customers(self, instance: AgentCluster):
        return instance.all_customers()

