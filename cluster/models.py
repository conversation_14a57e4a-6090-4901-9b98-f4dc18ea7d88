import os
from pathlib import Path
from django.conf import settings
from django.db import models
from django.forms import ValidationError
from accounts.helpers import BaseModel
import random
import string
import typing
from cluster.enums import AvailableBankType, ClusterRequestStatusType
from django.utils import timezone
from django.core.validators import FileExtensionValidator



from django_lifecycle import (
    LifecycleModel,
)

from typing import TypeVar

from savings.custom_storages import ConditionalStorage
from savings.utils import random_bytes
from savings.validators import validate_file_size

if typing.TYPE_CHECKING:
    from ajo.models import AjoUser
    from accounts.models import CustomUser

T = TypeVar("T", bound=BaseModel)


cluster_base_link = settings.CLUSTER_INVITE_BASE_LINK

def generate_cluster_code(length=8):
    characters = string.ascii_uppercase + string.digits
    code = ''.join(random.choices(characters, k=length))
    return code



class ClusterBaseModel(BaseModel, LifecycleModel):
    class _BaseModel(models.Manager):
        def get_queryset(self):
            return (
                super()
                .get_queryset()
                .filter(archived__isnull=True)
                .filter(archived=None)
            )


    class BaseModelQuerySet(models.QuerySet):
        pass


    archived = models.DateTimeField(blank=True, null=True, editable=False)
    objects =  _BaseModel.from_queryset(BaseModelQuerySet)()
    super_objects = models.Manager()

    
    def archive(self, using=None, keep_parents=False):
        self.archived = timezone.now()
        super().save(using=using)
    
    class Meta:
        abstract = True
    

class AgentCluster(ClusterBaseModel):
    agent: models.ForeignKey["CustomUser"] = models.ForeignKey("accounts.CustomUser", on_delete=models.CASCADE)
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=8, editable=False, default=generate_cluster_code, unique=True)
    num_of_customers = models.PositiveSmallIntegerField(default=0)
    balance = models.FloatField(default=0)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['agent', 'name'], condition=models.Q(archived__isnull=True), name='unique_active_agent_name')
        ]

    def __str__(self) -> str:
        return f"{self.agent} - {self.name} - {self.code}"

    @property
    def due_loans_balance(self):
        return 0
    
    @property
    def total_disbursed(self):
        return 0
    
    @property
    def cluster_link(self):
        return f"{cluster_base_link}/{self.code}"
    
    def due_cluster_loan_balance(self):
        return 0

    def all_customers(
        self,
        q_objects: typing.Optional[models.Q] = None,
        **kwargs
    ) -> models.QuerySet[T]:
        
        if q_objects is None:
            q_objects = models.Q()

        return self.customers.select_related("ajo_user").filter(q_objects, **kwargs)
    
class UploadTo:
    def __init__(self, name):
        self.name = name

    def __call__(self, instance, filename):
        """
        Construct a dynamic path for files
        """
        random_string = random_bytes()
        field_name = self.name


        base_filename, file_extension = os.path.splitext(filename)
        sanitized_filename = f"{base_filename}_{random_string}{file_extension}"

        return (
            "cluster/"
            f"{instance.cluster.agent.id}/"
            f"{instance.unique_id}/{field_name}/"
            f"{sanitized_filename}"
        )
    

    def deconstruct(self):
        return ('cluster.models.UploadTo', [self.name], {})



class ClusterCustomer(ClusterBaseModel):

    cluster = models.ForeignKey(AgentCluster, on_delete=models.CASCADE, related_name="customers", db_index=True)
    ajo_user: models.ForeignKey["AjoUser"] = models.ForeignKey("ajo.AjoUser", on_delete=models.CASCADE, db_index=True)
    bvn = models.CharField(max_length=100)
    bank = models.CharField(max_length=100, choices=AvailableBankType.choices)

    statement = models.FileField(
        upload_to=UploadTo("statement"),
        storage=ConditionalStorage(),
        validators=[validate_file_size, FileExtensionValidator(["pdf"])]
    )
    proof_of_address = models.FileField(
        upload_to=UploadTo("poa"),
        storage=ConditionalStorage(),
        validators=[validate_file_size]
    )
    live_image = models.FileField(
        upload_to=UploadTo("image"),
        storage=ConditionalStorage(),
        validators=[validate_file_size, FileExtensionValidator(["jpg", "jpeg"])]
    )

    paid = models.BooleanField(default=False)
    request_status = models.CharField(
        max_length=100,
        choices=ClusterRequestStatusType.choices,
        default=ClusterRequestStatusType.AT_REQUEST
    )
    unique_id = models.CharField(max_length=100, editable=False, default=random_bytes)
    payment_id = models.CharField(max_length=256, null=True, blank=True)
    statement_data = models.JSONField(default=dict, null=True, blank=True)
    account_name = models.CharField(max_length=100, null=True, blank=True)
    account_number = models.CharField(max_length=100, null=True, blank=True)



    def save(self, *args, **kwargs):
        expected_keys = {
            'Account_Number',
            'Agent_Name',
            'Closing_Balance',
            'Credit_Count',
            'Current_Balance',
            'Debit_Count',
            'End_Date',
            'Opening_Balance',
            'Start_Date',
            'Statement_Range',
            'Statement_Range_Valid',
            'Total_Credit',
            'Total_Debit',
            'statement_closed'
        }

        if self.statement_data and not all(key in self.statement_data for key in expected_keys):
            missing_keys = expected_keys - self.statement_data.keys()
            raise ValidationError(f"The following keys are missing in the 'data' field: {missing_keys}")

        self.__class__.validate_cluster_customer(self.ajo_user, self.cluster)

        super().save(*args, **kwargs)

    @staticmethod
    def validate_cluster_customer(ajo_user: "AjoUser", cluster: AgentCluster) -> bool:
        if ajo_user.user != cluster.agent:
            raise ValidationError("Ajo User does not belong to agent")
        return True


    @property
    def customer_first_name(self):
        return self.ajo_user.first_name
    
    @property
    def customer_last_name(self):
        return self.ajo_user.last_name
    
    @property
    def customer_full_name(self):
        return f"{self.customer_first_name} {self.customer_last_name}"
    
   
    def get_statement_content(self):
        output_dir = os.path.join(settings.BASE_DIR, "cluster", "services", "parser", "temp_media", "temp_files")
        os.makedirs(output_dir, exist_ok=True)

        try:
            with self.statement.open(mode='rb') as uploaded_file:
                content = uploaded_file.read()

            original_filename = os.path.basename(self.statement.name)

            copied_file_path = os.path.join(output_dir, original_filename)
            with open(copied_file_path, 'wb') as copied_file:
                copied_file.write(content)
        except Exception as e:
            raise e

        return copied_file_path

    

    @staticmethod
    def pay_for_cluster_spot():
        return True





