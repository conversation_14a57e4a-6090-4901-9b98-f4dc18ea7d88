from typing import Dict
from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin
from savings.admin import set_readonly_fields
from django.db.models.query import QuerySet


from cluster.models import AgentCluster, ClusterCustomer


class AgentClusterResource(resources.ModelResource):
    class Meta:
        model = AgentCluster


class ClusterCustomerResource(resources.ModelResource):
    class Meta:
        model = ClusterCustomer



####################################################################################
# RESOURCE ADMINS




class AgentClusterResourceAdmin(ImportExportModelAdmin):
    resource_class = AgentClusterResource
    search_fields = []
    readonly_fields = set_readonly_fields(
        "balance",
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ClusterCustomerResourceAdmin(ImportExportModelAdmin):
    resource_class = ClusterCustomerResource
    search_fields = []

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
    def process_statement_parser(self, request, queryset: QuerySet[ClusterCustomer]):
        from cluster.tasks import process_statement_task

        response: Dict[str, str] = {}

        for query_instance in queryset:
            response_key = query_instance.customer_full_name
            try:
                process_statement_task(query_instance.id)
                response[response_key] = "statement processed successfully."

            except Exception as err:
                response[response_key] = str(err)
            
        self.message_user(request, str(response))

    process_statement_parser.short_description = "STATEMENT: Process Statement"
    process_statement_parser.allow_tags = True

    actions = [
        process_statement_parser,
    ]



####################################################################################
# REGISTER MODELS

admin.site.register(AgentCluster, AgentClusterResourceAdmin)
admin.site.register(ClusterCustomer, ClusterCustomerResourceAdmin)
