from django.core.management import BaseCommand
from django.conf import settings
from rest_framework_simplejwt.tokens import RefreshToken

from ajo.models import AjoUser

logger = settings.LOGGER


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **options):
        phone_number="08118085412"
        password="123456"
        saver = AjoUser.objects.get(phone_number=phone_number)
        print(saver, "\n\n")

        refresh = RefreshToken.for_user(saver.user)
        result = {"refresh": str(refresh), "access": str(refresh.access_token)}
        print(result,"\n\n")
