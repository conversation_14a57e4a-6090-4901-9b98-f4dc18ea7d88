import os
from rest_framework import serializers
from django.core.validators import Reg<PERSON>V<PERSON>da<PERSON>
from ajo.model_choices import Gender
from ajo.models import AjoUser
from ajo.utils.otp_utils import verify_sms_voice_otp
from ajo.utils.ussd_utils import verify_ussd_otp
from django.contrib.auth.hashers import check_password

from django.conf import settings


_ENIRONMENT = settings.ENVIRONMENT


class OnboardAjoUserWithPhoneNumberSerializer(serializers.ModelSerializer):
    phone_number = serializers.CharField(
        max_length=11,
        validators=[
            RegexValidator(
                regex=r"^\d{11}$",
                message="phone number must be 11 digits",
                code="invalid_phone_number",
            )
        ],
    )

    class Meta:
        model = AjoUser
        fields = ["phone_number"]

    def validate(self, attrs):

        agent = self.context["agent"]
        phone_number = attrs.get("phone_number")

        ajo_user = AjoUser.objects.filter(
            user=agent, phone_number__startswith=phone_number
        ).last()

        if (
            ajo_user
            and ajo_user.onboarding_verified is True
            and ajo_user.onboarding_complete is True
            and ajo_user.used_web_via_agent is False
        ):
            raise serializers.ValidationError(
                "This user is already onboarded. Please create a passcode to proceed."
            )

        if (
            ajo_user
            and ajo_user.onboarding_verified is True
            and ajo_user.onboarding_complete is True
            and ajo_user.used_web_via_agent is True
        ):
            raise serializers.ValidationError(
                "The provided phone number is already associated with another user. Please use a different phone number."
            )
        ajo_user_exist_under_a_different_agent = AjoUser.objects.filter(
            phone_number__startswith=phone_number
        ).exclude(user=agent)

        if ajo_user_exist_under_a_different_agent.exists():
            agent_email = ajo_user_exist_under_a_different_agent.first().user.email
            raise serializers.ValidationError(
                f"The provided phone number is already associated with another user under the agent with email: {agent_email}. Please use a different phone number."
            )

        attrs["ajo_user_instance"] = ajo_user
        return super().validate(attrs)


class VerifyOTPSerializer(serializers.Serializer):
    otp = serializers.CharField(max_length=6, min_length=6)
    # otp_type = serializers.CharField(default=OTPType.SMS)
    # verification_stage = serializers.CharField(default=VerificationStages.ONBOARDING)
    phone_number = serializers.CharField(
        max_length=11,
        required=True,
        validators=[
            RegexValidator(
                regex=r"^\d{11}$",
                message="phone number must be 11 digits",
                code="invalid_phone_number",
            )
        ],
    )

    def validate(self, attrs):
        agent = self.context["agent"]
        phone_number = attrs.get("phone_number")
        otp = attrs.get("otp")

        ajo_user = AjoUser.objects.filter(user=agent, phone_number=phone_number).last()
        if not ajo_user:
            raise serializers.ValidationError("This User Does Not Exist")

        if _ENIRONMENT == "development":
            pass

        else:
            ussd_verification = verify_ussd_otp(
                otp=otp, phone_number=ajo_user.phone_number
            )

            if ussd_verification is True:
                pass
            elif (
                verify_sms_voice_otp(otp=otp, phone_number=ajo_user.phone_number)
                is True
            ):
                pass
            else:
                raise serializers.ValidationError("invalid OTP, please try again")

        attrs["ajo_user"] = ajo_user
        return attrs


class PersonalDetailsSerializer(serializers.Serializer):
    gender = serializers.CharField()
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    marital_status = serializers.CharField()
    phone_number = serializers.CharField()
    trade = serializers.CharField()
    trade_location = serializers.CharField()
    alias = serializers.CharField(required=False, allow_null=True)
    bvn = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    nin = serializers.CharField(required=False, allow_null=True, allow_blank=True)

    def validate(self, attrs):
        agent = self.context["agent"]
        first_name = attrs.get("first_name")
        last_name = attrs.get("last_name")
        gender = attrs.get("gender", "")

        phone_number = attrs.get("phone_number")

        ajo_user = AjoUser.objects.filter(user=agent, phone_number=phone_number).last()
        if not ajo_user:
            raise serializers.ValidationError("User not found")

        if not first_name.isalpha() and last_name.isalpha():
            raise serializers.ValidationError(
                "please ensure that first name and last name are only alphabets with no space or special characters"
            )

        if gender.upper() not in Gender:
            raise serializers.ValidationError("please choose male or female for gender")
        else:
            attrs["gender"] = getattr(Gender, gender.upper())

        attrs["ajo_user"] = ajo_user
        return attrs


class NextOfKinSerializer(serializers.Serializer):
    saver_phone_number = serializers.CharField()
    full_name = serializers.CharField()
    phone_number = serializers.CharField()
    email = serializers.CharField()
    address = serializers.CharField()
    relationship = serializers.CharField()

    def validate(self, attrs):
        agent = self.context["agent"]

        saver_phone_number = attrs.get("saver_phone_number")

        ajo_user = AjoUser.objects.filter(
            user=agent, phone_number=saver_phone_number
        ).last()
        if not ajo_user:
            raise serializers.ValidationError("User not found")

        attrs["ajo_user"] = ajo_user
        return attrs


class ImageCapturingSerializer(serializers.ModelSerializer):
    image = serializers.FileField(
        max_length=None, allow_empty_file=False, use_url=False
    )

    class Meta:
        model = AjoUser
        fields = ["image"]

    def validate_image(self, value):

        valid_extensions = [".jpg", ".jpeg", ".png", ".gif"]
        extension = os.path.splitext(value.name)[1].lower()
        if extension not in valid_extensions:
            raise serializers.ValidationError(
                "invalid image file. Only JPG, JPEG, PNG and GIF files are allowed"
            )
        return value


class PasscodeSerializer(serializers.Serializer):
    phone_number = serializers.CharField()
    password = serializers.CharField(write_only=True, max_length=6, min_length=6)
    re_password = serializers.CharField(write_only=True, max_length=6, min_length=6)

    def validate(self, attrs):
        agent = self.context["agent"]

        phone_number = attrs.get("phone_number")

        ajo_user = AjoUser.objects.filter(user=agent, phone_number=phone_number).last()
        if not ajo_user:
            raise serializers.ValidationError("User not found")

        if attrs["password"] != attrs["re_password"]:
            raise serializers.ValidationError("passwords do not match")

        try:
            ajo_user.validate_onboarding_stage()
        except NotImplementedError as err:
            raise serializers.ValidationError(str(err))

        attrs["ajo_user"] = ajo_user
        return attrs


class LoginSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=15)
    password = serializers.CharField(write_only=True, max_length=6, min_length=6)

    def validate(self, attrs):
        phone_number = attrs.get("phone_number")

        ajo_user = AjoUser.objects.filter(phone_number=phone_number).last()

        if not ajo_user:
            raise serializers.ValidationError("User not found")

        print(ajo_user.passcode,"\n\n")
        if not check_password(attrs["password"], ajo_user.passcode):
            raise serializers.ValidationError("invalid credentials")

        attrs["ajo_user"] = ajo_user
        return attrs
