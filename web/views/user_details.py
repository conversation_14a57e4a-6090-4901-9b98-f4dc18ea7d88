from rest_framework.response import Response
from rest_framework.views import APIView

from rest_framework import filters, generics, permissions, status


from ajo.models import AjoUser
from ajo.services import AjoUserService

from rest_framework import status
from rest_framework.generics import GenericAPIView


class SaversDetailsView(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):

        agent = request.user
        saver = request.saver

        saver_details = AjoUser.objects.get(id=saver.id).__dict__

        saver_details.pop("_state", None)
        saver_details.pop("passcode", None)
        saver_details.pop("pin", None)
        saver_details.pop("user_id", None)
        saver_details.pop("lite_user_id", None)
        saver_details.pop("lite_transaction_pin", None)
        saver_details.pop("branch_loandisk_borrower_id", None)

        response_data = {
            "status": "success",
            "data": {"personal_details": saver_details, "wallet": {}},
        }
        return Response(response_data, status=status.HTTP_200_OK)
