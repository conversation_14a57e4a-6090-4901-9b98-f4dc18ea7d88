from rest_framework.response import Response
from rest_framework.views import APIView

from accounts.models import NextOfKin

from ajo.model_choices import OnboardingStage
from ajo.models import AjoUser
from ajo.services import AjoUserService
from ajo.utils.otp_utils import send_out_sms_otp
from web.custom_permissions import IsValidReferral
from rest_framework import status
from rest_framework.generics import GenericAPIView
from django.contrib.auth.hashers import make_password

from rest_framework.parsers import MultiPartParser
from rest_framework_simplejwt.tokens import RefreshToken
from web.serializers.onboarding_auth import (
    ImageCapturingSerializer,
    LoginSerializer,
    NextOfKinSerializer,
    OnboardAjoUserWithPhoneNumberSerializer,
    PasscodeSerializer,
    PersonalDetailsSerializer,
    VerifyOTPSerializer,
)


from ajo.utils.ussd_utils import ussd_code_to_generate_otp


class OnboardAjoUserWithPhoneNumberAPIView(APIView):
    permission_classes = (IsValidReferral,)
    serializer_class = OnboardAjoUserWithPhoneNumberSerializer

    def post(self, request):

        agent = request.agent
        serializer = self.serializer_class(data=request.data, context={"agent": agent})
        serializer.is_valid(raise_exception=True)
        phone_number = serializer.validated_data["phone_number"]
        ajo_user_instance = serializer.validated_data["ajo_user_instance"]

        if not ajo_user_instance:

            ajo_user = AjoUser.objects.create(
                user=agent,
                phone_number=phone_number,
                onboarding_source="AGENT_REFERRAL",
            )

            ajo_user.onboarding_stage = OnboardingStage.OTP
            ajo_user.save()

        send_out_sms_otp(phone_number=phone_number)

        return Response(
            data={
                "status": True,
                "message": "OTP has been sent to the mobile number provided",
                "ussd_alternative": f"Dial {ussd_code_to_generate_otp} to generate an OTP",
                "onboarding_stage": (
                    ajo_user_instance.onboarding_stage
                    if ajo_user_instance
                    else OnboardingStage.OTP
                ),
            },
            status=status.HTTP_201_CREATED,
        )


class VerifyOTPAPIView(APIView):
    serializer_class = VerifyOTPSerializer
    permission_classes = (IsValidReferral,)

    def post(self, request, *args, **kwargs):

        agent = request.agent
        serializer = self.serializer_class(data=request.data, context={"agent": agent})

        serializer.is_valid(raise_exception=True)
        valiated_data = serializer.validated_data
        ajo_user = valiated_data.get("ajo_user")

        if ajo_user.onboarding_stage == "OTP":
            ajo_user.onboarding_stage = OnboardingStage.PERSONAL_DETAILS
            ajo_user.save()

        return Response(
            {
                "status": True,
                "onboarding_stage": ajo_user.onboarding_stage,
                "message": "user verified successfully",
            },
            status=status.HTTP_200_OK,
        )


class FillPersonalDetails(APIView):
    permission_classes = (IsValidReferral,)
    # serializer_class = VerifyOTPSerializer
    serializer_class = PersonalDetailsSerializer

    def post(self, request, *args, **kwargs):

        agent = request.agent
        serializer = self.serializer_class(data=request.data, context={"agent": agent})
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        ajo_user = validated_data["ajo_user"]

        for key, value in validated_data.items():
            setattr(ajo_user, key, value)

        ajo_user.onboarding_stage = OnboardingStage.NEXT_OF_KIN
        ajo_user.save()
        del validated_data["ajo_user"]
        return Response(
            data={
                "status": True,
                "onboarding_stage": ajo_user.onboarding_stage,
                "message": "details updated successfully",
                "data": validated_data,
            },
            status=status.HTTP_200_OK,
        )


class NextOfKinDetails(APIView):
    permission_classes = (IsValidReferral,)
    serializer_class = NextOfKinSerializer

    def post(self, request, *args, **kwargs):

        agent = request.agent
        serializer = self.serializer_class(data=request.data, context={"agent": agent})
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        ajo_user = validated_data["ajo_user"]

        ajo_user.onboarding_stage = OnboardingStage.IMAGE_UPLOAD
        ajo_user.save()

        next_of_kin, created = NextOfKin.objects.update_or_create(
            ajo_user=ajo_user, defaults=validated_data
        )
        del validated_data["ajo_user"]
        return Response(
            data={
                "status": True,
                "onboarding_stage": "",
                "message": "details updated successfully",
                "data": validated_data,
            },
            status=status.HTTP_200_OK,
        )


class ImageCapturingAPIView(GenericAPIView):
    serializer_class = ImageCapturingSerializer
    permission_classes = (IsValidReferral,)
    # serializer_class = NextOfKinSerializer
    parser_classes = (MultiPartParser,)

    def post(self, request, *args, **kwargs):

        agent = request.agent
        serializer = self.serializer_class(
            data=request.data, context={"agent": agent, "request": request}
        )

        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        phone_number = request.query_params.get("phone_number")

        ajo_user = AjoUser.objects.filter(user=agent, phone_number=phone_number).last()
        if not ajo_user:
            return Response(
                {
                    "status": False,
                    "message": "User was not found",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        save_image = AjoUserService(ajo_user=ajo_user).save_image_for_ajo_user(
            image=validated_data.get("image")
        )

        if save_image.get("status"):
            ajo_user.onboarding_stage = OnboardingStage.ADDRESS_VERIFICATION
            ajo_user.save()
            # call the celery task to verify the BVN
            # if ajo_user.bvn:
            #     verify_ajo_user_bvn.delay(
            #         phone_number=phone_number,
            #         user_id=request.user.id,
            #         base_url=obtain_the_current_site(request),
            #     )

            return Response(
                {
                    "status": True,
                    "message": "image uploaded successfully",
                    "data": {
                        "image": save_image.get("image"),
                    },
                },
                status=status.HTTP_200_OK,
            )
        return Response(
            {
                "error": "801",
                "status": False,
                "message": "there was error while uploading, please try again",
            },
            status=status.HTTP_400_BAD_REQUEST,
        )


### AUTH


class CreatePasscodeView(APIView):
    permission_classes = (IsValidReferral,)
    serializer_class = PasscodeSerializer

    def post(self, request):

        agent = request.agent
        serializer = self.serializer_class(data=request.data, context={"agent": agent})
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        ajo_user = validated_data["ajo_user"]
        password = validated_data["password"]

        ajo_user.passcode = make_password(password)
        ajo_user.used_web_via_agent = True
        ajo_user.save()

        token = RefreshToken.for_user(ajo_user.user)
        refresh_token = str(token)
        access_token = str(token.access_token)

        return Response(
            {
                "message": "Passcode created successfully",
                "access_token": access_token,
                "refresh_token": refresh_token,
            },
            status=status.HTTP_201_CREATED,
        )


class LoginView(APIView):
    # permission_classes = (IsValidReferral,)
    serializer_class = LoginSerializer

    def post(self, request):

        # agent = request.agent
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        ajo_user = validated_data["ajo_user"]

        token = RefreshToken.for_user(ajo_user.user)
        refresh_token = str(token)
        access_token = str(token.access_token)

        return Response(
            {
                "message": "Success",
                "access_token": access_token,
                "refresh_token": refresh_token,
            },
            status=status.HTTP_200_OK,
        )
