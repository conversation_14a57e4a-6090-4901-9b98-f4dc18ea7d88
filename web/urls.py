from django.urls import path

from web.views.onboarding import *
from web.views.user_details import SaversDetailsView

onboarding = [
    path(
        "onboarding/send-otp/",
        OnboardAjoUserWithPhoneNumberAPIView.as_view(),
        name="send_onboarding_otp",
    ),
    path(
        "onboarding/verify-otp/",
        VerifyOTPAPIView.as_view(),
        name="verify_onboarding_otp",
    ),
    path(
        "onboarding/fill-personal-details/",
        FillPersonalDetails.as_view(),
        name="fill_personal_details",
    ),
    path(
        "onboarding/next-of-kin/",
        NextOfKinDetails.as_view(),
        name="next_of_kin",
    ),
    path(
        "onboarding/image-upload/",
        ImageCapturingAPIView.as_view(),
        name="web_image_upload",
    ),
]

authentication = [
    path(
        "auth/create-passcode/",
        CreatePasscodeView.as_view(),
        name="web_image_upload",
    ),
    path(
        "auth/login/",
        LoginView.as_view(),
        name="web_image_upload",
    ),
]

user_details = [
    path(
        "saver/details/",
        SaversDetailsView.as_view(),
        name="saver_details",
    )
]


urlpatterns = [
    *onboarding,
    *authentication,
    *user_details,
]
