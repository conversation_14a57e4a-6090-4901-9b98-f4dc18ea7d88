from rest_framework.permissions import BasePermission
from django.contrib.auth import get_user_model
from django.utils.functional import SimpleLazyObject
from web.custom_exceptions import IsValidReferralException

Agent = get_user_model()


def get_agent_from_referral(referral_code):
    """
    Fetches the Agent instance associated with the referral code.
    """
    if referral_code:
        return Agent.objects.filter(referral_code=referral_code).last()
    return None


class IsValidReferral(BasePermission):
    def has_permission(self, request, view):
        referral_code = request.GET.get("referral_code")

        if not referral_code:
            raise IsValidReferralException

        lazy_agent = SimpleLazyObject(lambda: get_agent_from_referral(referral_code))
        _agent = request.agent = lazy_agent

        if not _agent:
            raise IsValidReferralException

        return True
