from django.contrib import admin, messages
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from payment.model_choices import Status, TransactionFormType, WalletTypes
from payment.models import Transaction
from savings.admin import set_readonly_fields

from .models import (
    ChestLock,
    LoanRequests,
    Onlending,
    OnlendingCommissionHistory,
    OnlendingCommissionSetting,
)

####################################################################################
# RESOURCES


class ChestlockResource(resources.ModelResource):
    class Meta:
        model = ChestLock


class OnlendingResource(resources.ModelResource):
    class Meta:
        model = Onlending


class OnlendingCommissionSettingResource(resources.ModelResource):
    class Meta:
        model = OnlendingCommissionSetting


class OnlendingCommissionHistoryResource(resources.ModelResource):
    class Meta:
        model = OnlendingCommissionHistory


class LoanRequestsResource(resources.ModelResource):

    class Meta:
        model = LoanRequests


####################################################################################
# RESOURCE ADMINS


class ChestlockResourceAdmin(ImportExportModelAdmin):
    resource_class = ChestlockResource
    search_fields = [
        "user__email",
        "plan",
        "quotation_id",
    ]
    list_filter = [
        "is_activated",
        "is_active",
        ("date_created", admin.DateFieldListFilter),
        "recurrent_saving_status",
        "payment_method",
    ]
    readonly_fields = set_readonly_fields(
        "duration",
        "target",
        "interest_rate",
        "periodic_amount",
        "estimated_amount",
        "amount_saved",
        "plan_balance_before",
        "plan_balance_after",
        "total_interest_earned",
    )

    date_hierarchy = "date_created"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class OnlendingResourceAdmin(ImportExportModelAdmin):
    resource_class = OnlendingResource
    search_fields = [
        "user__email",
        "name",
        "quotation_id",
        "ajo_user__phone_number",
    ]
    list_filter = [
        "is_activated",
        "is_active",
        ("created_at", admin.DateFieldListFilter),
        "recurrent_saving_status",
        "payment_method",
    ]
    readonly_fields = set_readonly_fields(
        "duration",
        "target",
        "interest_rate",
        "periodic_amount",
        "estimated_amount",
        "amount_saved",
        "plan_balance_before",
        "plan_balance_after",
        "total_interest_earned",
    )

    date_hierarchy = "created_at"
    
    actions = ("resolve_onlending_amount_saved",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
    @admin.action(description="RESOLVE ONLENDING AMOUNT SAVED")
    def resolve_onlending_amount_saved(self, request, queryset):
        if request.user.email != "<EMAIL>":
            self.message_user(request, level=40, message="You dont have privilege to perform this action")
            return
        
        for q in queryset:
            transactions = Transaction.objects.filter(
            quotation_id=q.quotation_id,
            status=Status.SUCCESS,
            transaction_type="CREDIT",
            wallet_type=WalletTypes.ONLENDING,
            # transaction_form_type=TransactionFormType.WALLET_DEPOSIT
        )
            amounts = [transaction.amount for transaction in transactions]
            total = sum(amounts)
            q.amount_saved = total 
            q.save()
       
        messages.success(request, "Successfully set tickets to drawn!")


class OnlendingCommissionHistoryResourceAdmin(ImportExportModelAdmin):
    resource_class = OnlendingCommissionHistoryResource
    search_fields = [
        "paid_to",
        "paid_from",
        "rate",
        "plan__quotation_id",
        "amount",
    ]
    list_filter = [
        ("created_at", admin.DateFieldListFilter),
    ]
    readonly_fields = set_readonly_fields(
        "plan",
        "amount",
        "rate",
        "paid_to",
        "paid_from",
    )

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class OnlendingCommissionSettingResourceAdmin(ImportExportModelAdmin):
    resource_class = OnlendingCommissionSettingResource
    search_fields = ["duration", "rate"]
    list_filter = [
        "duration",
        ("created_at", admin.DateFieldListFilter),
    ]

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LoanRequestsResourceAdmin(ImportExportModelAdmin):
    resource_class = LoanRequestsResource
    search_fields = [
        "plan__ajo_user__phone_number",
        "plan__user__email",
        "plan__quotation_id",
    ]
    list_filter = [
        ("created_at", admin.DateFieldListFilter),
    ]

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


####################################################################################
# REGISTER MODELS

admin.site.register(ChestLock, ChestlockResourceAdmin)
admin.site.register(Onlending, OnlendingResourceAdmin)
admin.site.register(OnlendingCommissionSetting, OnlendingCommissionSettingResourceAdmin)
admin.site.register(OnlendingCommissionHistory, OnlendingCommissionHistoryResourceAdmin)
admin.site.register(LoanRequests, LoanRequestsResourceAdmin)
