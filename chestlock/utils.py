import datetime
import os
from typing import Any, Dict, List

from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.db import IntegrityError
from django.db.models import QuerySet, Sum
from django.utils import timezone
from rest_framework import serializers

from ajo import payment_actions as APA
from accounts.liberty_ussd import LibertyUSSD
from accounts.models import ConstantTable
from ajo.models import AjoUser
from ajo.third_party import TextMessages
from payment.model_choices import PlanType, Status, TransactionFormType
from payment.models import WalletSystem
from payment.services import TransactionService
from payment.payment_actions import PlanPayments
from payment.utils import format_currency

from .calculations import (
    OnlendingCalculations,
    calculate_estimated_amount_for_chestlock_plan,
    calculate_frequency_breakdowns,
    calculate_interest_earned_for_chestlock_plan,
)
from .model_choices import Frequency, InterestType, OnlendingType
from .models import LoanRequests, Onlending


def extended_plan_details_for_recurrent_chestlock(
    duration: int,
    amount: float,
    frequency: Frequency,
) -> dict:
    """
    takes in duration in days(int), amount in float, and one of the Frequency choices and returns the following dict:
    {
        "target": float,
        "interest_rate": float,
        "frequency": one of the choices of the Frequency choices from models_choices,
        "periodic_amount": float,
        "maturity_date": datetime.date() object,
        "estimated_amount": float
    }
    """
    constants = ConstantTable.get_constant_table_instance()

    details = {}

    details["target"] = round(amount, 2)
    # TODO: The interest rates will change according to values of amount
    interest_rate = float(constants.chestlock_interest_rate)
    details["interest_rate"] = interest_rate

    if frequency.upper() in Frequency:
        details["frequency"] = getattr(Frequency, frequency.upper())
    else:
        raise serializers.ValidationError("Choose from daily, weekly or monthly")

    breakdown = calculate_frequency_breakdowns(amount=round(float(amount), 2), duration=duration)
    details["periodic_amount"] = breakdown[frequency.lower()]

    details["maturity_date"] = datetime.date.today() + datetime.timedelta(days=duration)

    details["estimated_amount"] = calculate_estimated_amount_for_chestlock_plan(
        initial_deposit=amount, duration=duration
    )
    # estimated_amount = round(
    #     amount + (amount * (interest_rate / 100)),
    #     2,
    # )

    # details["estimated_amount"] = estimated_amount(total_amount=amount, duration=duration, period=Frequency.DAILY)

    return details


def generate_recurrent_chestlock_plan_details(duration: int, amount: float) -> dict:
    """
    This works for Recurrent Chestlock Plan
    takes in duration in days(int), amount in float, and returns the following dict:
    {
        "target": float,
        "interest_rate": float,
        "frequency_choices": list,
        "daily_breakdown": float,
        "weekly_breakdown": float,
        "monthly_breakdown": float,
        "maturity_date": "Month, Day",
        "estimated_amount": float
    }
    """
    constants = ConstantTable.get_constant_table_instance()

    details = {}

    details["target"] = round(amount, 2)
    # TODO: The interest rates will change according to values of amount
    interest_rate = constants.chestlock_interest_rate
    details["interest_rate"] = interest_rate

    details["frequency_choices"] = list(Frequency.values)

    breakdown = calculate_frequency_breakdowns(amount=round(float(amount), 2), duration=duration)
    details["daily_breakdown"] = breakdown["daily"]
    details["weekly_breakdown"] = breakdown["weekly"]
    details["monthly_breakdown"] = breakdown["monthly"]

    maturity_date = datetime.date.today() + datetime.timedelta(days=duration)
    details["maturity_date"] = f"{maturity_date.strftime('%B')}, {maturity_date.strftime('%d')}"

    # estimated_amount = round(
    #     amount + (amount * (interest_rate / 100)),
    #     2,
    # )

    # details["estimated_amount"] = estimated_amount
    # details["estimated_amount"] = estimated_amount(total_amount=amount, duration=duration, period=Frequency.DAILY)
    details["estimated_amount"] = calculate_estimated_amount_for_chestlock_plan(
        initial_deposit=amount, duration=duration
    )

    return details


def get_interest_for_period_of_time_for_chestlock(duration: int) -> float:
    """
    Returns the interest for a period of time
    eg 30 days will return 1.23...

    Args:
        duration (int): the number of days

    Returns:
        float: the interest
    """
    constants = ConstantTable.get_constant_table_instance()
    # get the chestlock interest with the %
    chestlock_interest = constants.chestlock_interest_rate
    daily_interest_rate = chestlock_interest / 365
    return daily_interest_rate * duration


def generate_oneoff_chestlock_plan_details(duration: int, amount: float) -> dict:
    """
    Generate a dictionary of information for a oneoff chestlock plan

    Args:
        duration (int): the number of days
        amount (float): the amount to be saved

    Returns:
        dict: returns a dictionary:
            interest rate, amount_to_lock, maturity_date, interest_earned, estimated_amount
    """
    details = {}

    # obtain the interest for the duration
    interest_for_duration = get_interest_for_period_of_time_for_chestlock(duration=duration)
    details["interest_rate"] = round(interest_for_duration, 1)

    details["amount_to_lock"] = round(amount, 2)

    # obtain the maturity date
    maturity_date = datetime.date.today() + datetime.timedelta(days=duration)
    details["maturity_date"] = f"{maturity_date.strftime('%B')}, {maturity_date.strftime('%d')}"

    # get the interest earned
    interest_earned = calculate_interest_earned_for_chestlock_plan(
        initial_deposit=amount,
        duration=duration,
    )
    details["interest_earned"] = interest_earned
    # get the estimated amount
    details["estimated_amount"] = round(amount + interest_earned, 2)

    return details


def extended_plan_details_for_oneoff_chestlock(duration: int, amount: float) -> dict:
    details = {}
    generated = generate_oneoff_chestlock_plan_details(duration=duration, amount=amount)
    details["interest_rate"] = generated.get("interest_rate")
    details["total_interest_earned"] = generated.get("interest_earned")
    details["estimated_amount"] = generated.get("estimated_amount")

    details["maturity_date"] = datetime.date.today() + datetime.timedelta(days=duration)

    return details


# def estimated_amount(total_amount: float, duration: int, period: Frequency):
#     constants = ConstantTable.get_constant_table_instance()
#     interest_rate = float(constants.chestlock_interest_rate) / 100
#     if period == Frequency.DAILY:
#         n = duration
#         savings_per_period = round(total_amount / n, 2)
#     elif period == Frequency.WEEKLY:
#         n = duration // 7
#         savings_per_period = round(total_amount / n, 2)
#     elif period == Frequency.MONTHLY:
#         n = duration // 30
#         savings_per_period = round(total_amount / n, 2)

#     r = (
#         interest_rate / 52
#         if period == "weekly"
#         else interest_rate / 12
#         if period == "monthly"
#         else interest_rate / 365
#     )

#     savings = [savings_per_period for i in range(n)]

#     interest = [sum(savings[:i]) * r for i in range(1, n + 1)]
#     savings_with_interest = [sum(savings[:i]) + sum(interest[:i]) for i in range(1, n + 1)]

#     total_amount_with_interest = savings_with_interest[-1]
#     return round(total_amount_with_interest, 2)

# print("Total estimated amount after {}: {}".format(period, round(total_amount_with_interest, 2)))


class OnlendingUtils:

    @classmethod
    def constants(cls):
        return ConstantTable.get_constant_table_instance()

    @classmethod
    def generate_onlending_plan_details(cls, duration: int, amount: float) -> Dict[str, Any]:
        """
        This works for Onlending plan
        Args:
            duration (int): duration in days
            amount (float): amount to save towards
        Returns:
            Dict[str, Any]:
                    {
                        "target": float,
                        "interest_rate": float,
                        "frequency_choices": list,
                        "daily_breakdown": float,
                        "weekly_breakdown": float,
                        "monthly_breakdown": float,
                        "maturity_date": "Month, Day",
                        "estimated_amount": float
                    }
        """

        details = {}

        details["target"] = round(amount, 2)
        interest_rate = cls.constants().chestlock_interest_rate
        details["interest_rate"] = interest_rate

        details["frequency_choices"] = list(Frequency.values)

        breakdown = calculate_frequency_breakdowns(amount=round(float(amount), 2), duration=duration)
        details["daily_breakdown"] = breakdown["daily"]
        details["weekly_breakdown"] = breakdown["weekly"]
        details["monthly_breakdown"] = breakdown["monthly"]

        maturity_date = timezone.localdate() + datetime.timedelta(days=duration)
        details["maturity_date"] = f"{maturity_date.strftime('%B')}, {maturity_date.strftime('%d')}"

        details["estimated_amount"] = calculate_estimated_amount_for_chestlock_plan(
            initial_deposit=amount,
            duration=duration,
        )

        return details

    @classmethod
    def get_interest_for_time_period(cls, duration: int) -> float:
        """
        Returns the interest for a time period
        eg 30 days will return 1.23...

        Args:
            duration (int): the number of days

        Returns:
            float: the interest
        """
        # get the chestlock interest with the %
        chestlock_interest = cls.constants().chestlock_interest_rate
        daily_interest_rate = chestlock_interest / 365
        return daily_interest_rate * duration

    @classmethod
    def generate_oneoff_onlending_plan_details(
        cls,
        duration: int,
        amount: float,
    ) -> Dict[str, Any]:
        """
        Generate a dictionary of information for a oneoff onlending plan

        Args:
            duration (int): the number of days
            amount (float): the amount to be saved

        Returns:
            Dict[str, Any]: returns a dictionary:
                interest rate, amount_to_lock, maturity_date, interest_earned, estimated_amount
        """
        details = {}

        # obtain the interest for the duration
        interest_for_duration = cls.get_interest_for_time_period(duration=duration)
        details["interest_rate"] = round(interest_for_duration, 1)

        details["amount_to_lock"] = round(amount, 2)

        # obtain the maturity date
        maturity_date = datetime.date.today() + datetime.timedelta(days=duration)
        details["maturity_date"] = f"{maturity_date.strftime('%B')}, {maturity_date.strftime('%d')}"

        # get the interest earned
        interest_earned = calculate_interest_earned_for_chestlock_plan(
            initial_deposit=amount,
            duration=duration,
        )
        details["interest_earned"] = interest_earned
        # get the estimated amount
        details["estimated_amount"] = round(amount + interest_earned, 2)

        return details

    @classmethod
    def create_oneoff_plan_information(
        cls,
        duration: int,
        amount: float,
    ) -> Dict[str, Any]:
        """
        Generate the information used to create the oneoff onlending plan

        Args:
            duration (int): the duration in days
            amount (float): the amount to be saved

        Returns:
            Dict[str, Any]: returns interest_rate, total_interest_earned, estimated_amount
                            and maturity_date in the dictionary returned
        """
        details = {}
        generated = cls.generate_oneoff_onlending_plan_details(
            duration=duration,
            amount=amount,
        )
        details["interest_rate"] = generated.get("interest_rate")
        details["total_interest_earned"] = generated.get("interest_earned")
        details["estimated_amount"] = generated.get("estimated_amount")
        details["maturity_date"] = timezone.localdate() + timezone.timedelta(days=duration)

        # calculate interest
        commission_amount, commission_rate = OnlendingCalculations.calculate_interests_for_referals(duration, amount)
        details["commission_amount"] = commission_amount
        details["commission_rate"] = commission_rate

        return details
     

    @classmethod
    def generate_recurrent_onlending_plan_details(
        cls,
        duration: int,
        amount: float
        ) -> dict:
        """
        This works for Recurrent Onlending Plan
        takes in duration in days(int), amount in float, and returns the following dict:
        {
            "target": float,
            "interest_rate": float,
            "frequency_choices": list,
            "daily_breakdown": float,
            "weekly_breakdown": float,
            "maturity_date": "Month, Day"
        }
        """
        constants = ConstantTable.get_constant_table_instance()

        details = {}

        details["target"] = round(amount, 2)
        interest_rate = constants.chestlock_interest_rate
        details["interest_rate"] = interest_rate

        details["frequency_choices"] = list(Frequency.values)

        breakdown = calculate_frequency_breakdowns(amount=round(float(amount), 2), duration=duration)
        details["daily_breakdown"] = breakdown["daily"]
        details["weekly_breakdown"] = breakdown["weekly"]
        # details["monthly_breakdown"] = breakdown["monthly"]

        maturity_date = datetime.date.today() + datetime.timedelta(days=duration)
        # details["maturity_date"] = f"{maturity_date.strftime('%B')}, {maturity_date.strftime('%d')}"
        details["maturity_date"] = f"{maturity_date.strftime('%B %d, %Y')}"

        # estimated_amount = round(
        #     amount + (amount * (interest_rate / 100)),
        #     2,
        # )

        # details["estimated_amount"] = estimated_amount
        # details["estimated_amount"] = estimated_amount(total_amount=amount, duration=duration, period=Frequency.DAILY)
        # details["estimated_amount"] = calculate_estimated_amount_for_chestlock_plan(
        #     initial_deposit=amount, duration=duration
        # )

        return details

    @classmethod
    def extended_plan_details_for_recurrent_onlending(
        cls,
        duration: int,
        amount: float,
        frequency: Frequency,
    ) -> dict:
        """
        takes in duration in days(int), amount in float, and one of the Frequency choices and returns the following dict:
        {
            "target": float,
            "interest_rate": float,
            "frequency": one of the choices of the Frequency choices from models_choices,
            "periodic_amount": float,
            "maturity_date": datetime.date() object,
            "estimated_amount": float
        }
        """
        constants = ConstantTable.get_constant_table_instance()

        details = {}

        details["target"] = round(amount, 2)
        # TODO: The interest rates will change according to values of amount
        interest_rate = float(constants.onlending_interest_rate)
        details["interest_rate"] = interest_rate

        if frequency.upper() in Frequency:
            details["frequency"] = getattr(Frequency, frequency.upper())
        else:
            raise serializers.ValidationError("Choose from daily or weekly")

        breakdown = calculate_frequency_breakdowns(amount=round(float(amount), 2), duration=duration)
        details["periodic_amount"] = breakdown[frequency.lower()]

        details["maturity_date"] = datetime.date.today() + datetime.timedelta(days=duration)

        # details["estimated_amount"] = calculate_estimated_amount_for_chestlock_plan(
        #     initial_deposit=amount, duration=duration
        # )
        details["onlending_type"] = OnlendingType.RECURRENT

        return details

    
    @classmethod
    def create_plan_information(
        cls,
        duration: int,
        amount: float,
        frequency: Frequency,
    ) -> Dict[str, Any]:
        """
        Generate the details for creating onlending plan

        Args:
            duration (int): _description_
            amount (float): _description_
            frequency (Frequency): _description_

        Raises:
            serializers.ValidationError: _description_

        Returns:
            Dict[str, Any]: _description_
        """
        constants = ConstantTable.get_constant_table_instance()

        details = {}

        details["target"] = round(amount, 2)
        interest_rate = float(constants.chestlock_interest_rate)
        details["interest_rate"] = interest_rate

        if frequency.upper() in Frequency:
            details["frequency"] = getattr(Frequency, frequency.upper())
        else:
            raise serializers.ValidationError("Choose from daily, weekly or monthly")

        breakdown = calculate_frequency_breakdowns(amount=round(float(amount), 2), duration=duration)
        details["periodic_amount"] = breakdown[frequency.lower()]

        details["maturity_date"] = timezone.localdate() + datetime.timedelta(days=duration)

        details["estimated_amount"] = calculate_estimated_amount_for_chestlock_plan(
            initial_deposit=amount,
            duration=duration,
        )

        return details

    @classmethod
    def get_rollover_name(cls, name: str) -> str:
        """
        Checks if a name has 'rollover' in it,
        if it doesn't, it will append 'rollover' to it.

        If it has rollover in it, but no number, it will append 2
        if it has rollover and a number eg rollover 2, it will increment
        the number by 1

        Args:
            name (str): the name of the plan

        Returns:
            str: the rollover name
        """
        parts = name.split()
        # Check if the input string has "rollover" in it
        if "rollover" in parts and "rollover" in [parts[-1], parts[-2]]:
            # Split the string by spaces
            if len(parts) > 1:
                # Extract the last part which should be the number or "rollover"
                last_part = parts[-1]
                if last_part.isdigit():
                    # If the last part is a number, increment it by 1
                    incremented_number = int(last_part) + 1
                    # Replace the last part with the incremented number
                    parts[-1] = str(incremented_number)
                else:
                    # If the last part is "rollover", append "2"
                    parts.append(" 2")
            else:
                # If there's only one part, append "2"
                parts.append(" 2")
            # Join the parts to form the updated string
            return " ".join(parts)
        else:
            # If the input string doesn't end with "rollover", append it
            return name + " rollover"

    @staticmethod
    def send_onlending_creation_email(email: str, data: Dict[str, Any], task: bool = True):
        """

        data argument should have the following keys
            - name
            - deposit
            - interest_rate
            - duration
            - start_date
            - maturity_date
            - quotation_id
            - rollover (boolean)
            - estimated_amount
            - interest_type
            - onlender

        Args:
            email (str): the email is should be sent to
            data (Dict[str, Any]): should contain the fields in the summary section

        Returns:
            _type_: _description_
        """
        email_subject = "Onlending Plan Creation"
        email_recipient_list = [email]

        start_date: datetime.datetime = data.get("start_date")
        maturity_date: datetime.date = data.get("maturity_date")

        rollover = data.get("rollover")
        if rollover is None or rollover is True:
            rollover = "Optional"
        else:
            rollover = "No rollover"

        interest_type: str = data.get("interest_type")
        if interest_type == InterestType.MATURITY_DATE:
            interest_type = "Maturity Date Payment"
        else:
            interest_type = "Upfront Payment"

        data["deposit"] = format_currency(data.get("deposit"))
        data["estimated_amount"] = format_currency(data.get("estimated_amount"))

        data["rollover"] = rollover
        data["start_date"] = start_date.date().strftime("%Y-%m-%d")
        data["maturity_date"] = maturity_date.strftime("%Y-%m-%d")
        data["interest_type"] = interest_type

        email_context = data
        email_template_dir = os.path.join(settings.BASE_DIR, "templates", "onlending", "emailContractAgreement.html")

        from accounts.tasks import send_custom_email_task

        if task:
            send_custom_email_task.delay(
                email_subject,
                email_recipient_list,
                email_context,
                email_template_dir,
            )
        else:
            send_custom_email_task(
                email_subject,
                email_recipient_list,
                email_context,
                email_template_dir,
            )
    

class EnrollmentLink:
    base_url: str = settings.ENROLLMENT_BASE_URL

    @classmethod
    def build_enrollment_link(cls, referral_code: str) -> str:
        return f"{cls.base_url}get-started?referral_code={referral_code}"

    @classmethod
    def send_enrollment_link_sms(
        cls,
        referral_code: str,
        phone_number: str,
        user: AbstractUser,
    ) -> bool:
        enrollment_link = cls.build_enrollment_link(referral_code=referral_code)

        try:

            placeholders = {
                "message": f"Use this link to enroll to become an onlending partner: {enrollment_link}",
            }

            sms = TextMessages.dynamic_send_sms(
                user=user,
                phone_number=phone_number,
                template_id=settings.EMPTY_TEMPLATE_ID,
                placeholders=placeholders,
            )

        except:
            return False

        return sms.get("status")


class LoanRequestsManager:
    def __init__(self, plan: Onlending):
        self.plan = plan

    @classmethod
    def get_all_loan_requests(cls) -> QuerySet[LoanRequests]:
        """
        All the loan requests assigned to plans

        Returns:
            QuerySet[LoanRequests]: QuerySet of assigned loan requests
        """
        return LoanRequests.objects.all()

    def get_new_requests_not_existing(self) -> List[Dict[str, Any]]:
        # try to get the loan requests from Liberty USSD
        loan_requests = LibertyUSSD.get_processing_loans()

        if not loan_requests.get("status"):
            raise ValueError(f"issue obtaining loan requests: {loan_requests}")

        requests = loan_requests.get("data")

        existing_requests = self.get_all_loan_requests()
        existing_requests_references = list(existing_requests.values_list("mandate_reference", flat=True))

        new_requests = [
            request for request in requests if request.get("mandate_reference") not in existing_requests_references
        ]

        return new_requests

    def process_loan_requests_for_plan(self):
        """check for loan requests and add to a plan"""
        new_requests = self.get_new_requests_not_existing()
        sorted_requests = sorted(
            new_requests,
            key=lambda x: x["loan_amount"],
            reverse=True,
        )

        loan_amount_aggregation = (
            self.get_existing_plan_loan_requests().aggregate(total=Sum("loan_amount"))["total"] or 0
        )

        # Process the remaining new requests
        if sorted_requests:
            target_amount = self.plan.target - loan_amount_aggregation

            # Iterate through new requests and try to allocate funds
            for request in sorted_requests:
                loan_amount = request.get("loan_amount")

                if loan_amount <= target_amount:
                    # Allocate funds to the request
                    self.allocate_funds_to_request(request)
                    target_amount -= loan_amount

    def get_existing_plan_loan_requests(self) -> QuerySet[LoanRequests]:
        return self.get_all_loan_requests().filter(plan=self.plan)

    def allocate_funds_to_request(self, loan_request: Dict[str, Any]):
        try:
            LoanRequests.objects.create(
                plan=self.plan,
                loan_amount=loan_request.get("loan_amount"),
                mandate_reference=loan_request.get("mandate_reference"),
                status=loan_request.get("status"),
                borrowers_name=loan_request.get("borrowers_name"),
                borrowers_ministry=loan_request.get("borrowers_ministry"),
                request_date=loan_request.get("request_date"),
                loan_duration=loan_request.get("loan_duration"),
            )
        except IntegrityError as err:
            raise ValueError(f"error: {err}")
        

class OnlendingDynamicTransactions:

    @staticmethod
    def dynamic_onlending_payment_transactions(
        ajo_user: AjoUser,
        amount: float,
        from_wallet: WalletSystem,
        onlending_plan: Onlending,
        onlending_wallet: WalletSystem,
    ):

        APA.debit_wallet_through_transfer(
            from_wallet=from_wallet,
            amount=amount,
            description=f"Payment of `{amount}` made for onlending plan `{onlending_plan.name}`",
            plan_type=PlanType.ONLENDING
        )

        fund_transaction = PlanPayments.create_funding_transaction(
            onlending_wallet=onlending_wallet,
            amount=amount,
            onlending_plan=onlending_plan,
        )
        
        fund_wallet = APA.fund_wallet_and_update_transaction(
            wallet=onlending_wallet,
            amount=amount,
            transaction_instance=fund_transaction,
            ajo_user=ajo_user,
        )

        fund_transaction.refresh_from_db()

        PlanPayments.increment_positions(
            plan=onlending_plan,
            fund_transaction=fund_transaction,
            amount=amount
        )

        return {
            "debit_credit_info": fund_wallet,
            "transaction": fund_transaction
        }
        
    @staticmethod
    def dynamic_onlending_transactions_record(
        user,
        ajo_user: AjoUser,
        amount: float,
        from_wallet: WalletSystem,
        onlending_plan: Onlending,
        onlending_wallet: WalletSystem,
    ):
        fund_transaction = TransactionService.create_deposit_by_wallet_transaction(
            user=user,
            amount=amount,
            wallet_type=onlending_wallet.wallet_type,
            quotation_id=onlending_plan.quotation_id,
            plan_type=PlanType.ONLENDING,
            transaction_form_type=TransactionFormType.WALLET_DEPOSIT,
            ajo_user=ajo_user,
        )
        transfer_trn = APA.debit_wallet_through_transfer(
            from_wallet=from_wallet,
            amount=amount,
            description=f"Payment of `{amount}` made for recurrent onlending `{onlending_plan.name}`",
            plan_type=PlanType.ONLENDING,
        )

        if transfer_trn.get("status") is False:
            fund_transaction.status = Status.FAILED
            fund_transaction.failure_reason = transfer_trn.get("message")
            fund_transaction.save()
            # fund_transaction = PlanPayments.create_funding_transaction(
            #     onlending_wallet=onlending_wallet,
            #     amount=amount,
            #     onlending_plan=onlending_plan,
            # )
            return {"status": False, "message": transfer_trn.get("message")}
        
        else:   
            
            fund_wallet = APA.fund_wallet_and_update_transaction(
                wallet=onlending_wallet,
                amount=amount,
                transaction_instance=fund_transaction,
                ajo_user=ajo_user,
            )

            fund_transaction.refresh_from_db()
            onlending_plan.amount_saved += amount
            onlending_plan.save()

            return {
                "status": True,
                "debit_credit_info": fund_wallet,
                "transaction": fund_transaction
            }
        

def cal_missed_amount(today: datetime.datetime, onlending_plan: Onlending):
            
    onlending_creation_date = onlending_plan.activated_at
    amount_saved = onlending_plan.amount_saved
    periodic_amount = onlending_plan.periodic_amount
    frequency = onlending_plan.frequency
    durations = today - onlending_creation_date
            
    if frequency == "DAILY":
        expected_amount = periodic_amount * durations.days
        missed_amount = expected_amount - amount_saved
    elif frequency == "WEEKLY":
        durations = durations.days // 7
        expected_amount = periodic_amount * durations
        missed_amount = expected_amount - amount_saved
        
    return {
            "frequency": onlending_plan.frequency,
            "expected_amount": expected_amount,
            "amount_missed": 0 if missed_amount < 0 else missed_amount
        }
