from django.utils import timezone
import uuid

from django.conf import settings
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models
from django.utils.translation import gettext as _

from accounts.helpers import BaseModel, RoundedFloatField
from ajo.models import AjoUser
from payment.model_choices import PaymentMethod, RecurrentSavingStatus, Status

from .model_choices import ChestlockType, Frequency, InterestType, OnlendingType


class ChestLock(models.Model):
    ######INFORMATION ABOUT THE PLAN#############
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="chestlock", db_index=True
    )
    plan = models.CharField(max_length=100, null=True, blank=True)
    # Chestlock type
    chestlock_type = models.CharField(max_length=100, choices=ChestlockType.choices, default=ChestlockType.RECURRENT)
    duration = models.IntegerField(
        blank=True,
        null=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="duration should be >= 0"),
        ],
    )
    target = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="target should be >= 0"),
        ],
    )
    interest_rate = models.CharField(max_length=100, null=True, blank=True)
    periodic_amount = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="periodic amount should be >= 0"),
        ],
    )
    frequency = models.CharField(max_length=100, null=True, blank=True, choices=Frequency.choices)
    hour = models.PositiveIntegerField(
        null=True,
        blank=True,
        validators=[
            MaxValueValidator(limit_value=23, message="hour should be between 0-23"),
        ],
    )
    maturity_date = models.DateField(null=True, blank=True, db_index=True)
    estimated_amount = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="estimated amount should be >= 0"),
        ],
    )
    quotation_id = models.CharField(max_length=100, null=True, blank=True, unique=True)
    amount_saved = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="periodic amount should be >= 0"),
        ],
    )

    # plan information
    is_activated = models.BooleanField(default=False, null=True, blank=True, db_index=True)
    activated_at = models.DateTimeField(verbose_name=_("date activated"), null=True, blank=True, db_index=True)
    completed = models.BooleanField(default=False, null=True, blank=True)
    completed_at = models.DateTimeField(verbose_name=_("date completed"), null=True, blank=True, db_index=True)

    plan_balance_before = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="plan balance before should be >= 0"),
        ],
    )
    plan_balance_after = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="plan balance after should be >= 0"),
        ],
    )
    total_interest_earned = RoundedFloatField(
        default=0.00, validators=[MinValueValidator(limit_value=0.0, message="interest paid should be >= 0")]
    )

    # metadata
    is_active = models.BooleanField(default=True, null=True, blank=True, db_index=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    # payment method and fields
    payment_method = models.CharField(max_length=200, null=True, blank=True, choices=PaymentMethod.choices)
    recurrent_saving_status = models.CharField(
        max_length=100, choices=RecurrentSavingStatus.choices, default=RecurrentSavingStatus.NONE
    )
    withdrawn = models.BooleanField(default=False)

    # email
    email_sent = models.BooleanField(default=False)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["user", "plan"],
                name="unique_user_chestlock_plan",
                violation_error_message="A plan with this name already exists",
            ),
        ]

        verbose_name = "ChestLock"
        verbose_name_plural = "ChestLock"

    def __str__(self) -> str:
        return f"plan:{self.plan} amount:{self.amount_saved} quotation_id:{self.quotation_id}"


def generate_quotation_id() -> str:
    """
    Generate a unique quotation id
    """
    id = f"LI-ONL-{uuid.uuid4()}"

    if Onlending.objects.filter(quotation_id=id).exists():
        return generate_quotation_id
    else:
        return id


class Onlending(BaseModel):
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="onlending",
        db_index=True,
    )
    ajo_user = models.ForeignKey(
        to=AjoUser,
        on_delete=models.CASCADE,
        related_name="onlending",
        null=True,
        blank=True,
        db_index=True,
    )
    name = models.CharField(max_length=100, null=True, blank=True)
    onlending_type = models.CharField(
        max_length=100,
        choices=OnlendingType.choices,
        default=OnlendingType.ONEOFF,
    )
    duration = models.IntegerField(
        blank=True,
        null=True,
        validators=[
            MinValueValidator(
                limit_value=0.0,
                message="duration should be >= 0",
            ),
        ],
    )
    target = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.00,
                message="target should be >= 0",
            )
        ],
    )
    interest_rate = RoundedFloatField(
        default=20,
        validators=[
            MinValueValidator(
                limit_value=0.00,
                message="interest rate should be >= 0",
            ),
        ],
    )
    periodic_amount = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0,
                message="periodic amount should be >= 0",
            ),
        ],
    )
    frequency = models.CharField(max_length=100, null=True, blank=True, choices=Frequency.choices, db_index=True)
    hour = models.PositiveIntegerField(
        null=True,
        blank=True,
        validators=[
            MaxValueValidator(limit_value=23, message="hour should be between 0-23"),
        ],
    )
    maturity_date = models.DateField(
        null=True,
        blank=True,
        db_index=True,
    )
    estimated_amount = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="estimated amount should be >= 0"),
        ],
    )
    quotation_id = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        unique=True,
        default=generate_quotation_id,
    )
    amount_saved = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.00,
                message="amount_saved should be >= 0",
            )
        ],
    )
    interest_type = models.CharField(
        max_length=100,
        choices=InterestType.choices,
        default=InterestType.UPFRONT,
    )
    plan_balance_before = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="plan balance before should be >= 0"),
        ],
    )
    plan_balance_after = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="plan balance after should be >= 0"),
        ],
    )
    total_interest_earned = RoundedFloatField(
        default=0.00,
        validators=[
            MinValueValidator(
                limit_value=0.0,
                message="interest paid should be >= 0",
            )
        ],
    )

    # metadata
    is_activated = models.BooleanField(
        default=False,
        null=True,
        blank=True,
        db_index=True,
    )
    activated_at = models.DateTimeField(
        verbose_name=_("date activated"),
        null=True,
        blank=True,
        db_index=True,
    )
    completed = models.BooleanField(
        default=False,
        null=True,
        blank=True,
    )
    automate_payments = models.BooleanField(default=False)
    completed_at = models.DateTimeField(
        verbose_name=_("date completed"),
        null=True,
        blank=True,
        db_index=True,
    )
    is_active = models.BooleanField(
        default=True,
        null=True,
        blank=True,
        db_index=True,
    )
    payment_method = models.CharField(
        max_length=200,
        null=True,
        blank=True,
        choices=PaymentMethod.choices,
    )
    
    recurrent_saving_status = models.CharField(
        max_length=100,
        choices=RecurrentSavingStatus.choices,
        default=RecurrentSavingStatus.NONE,
    )
    withdrawn = models.BooleanField(default=False)
    interest_paid = models.BooleanField(default=False)

    # email
    email_sent = models.BooleanField(default=False)

    # commission
    commission_amount = models.FloatField(default=0)
    commission_rate = models.FloatField(default=0)
    commission_paid = models.BooleanField(default=False)
    
    date_created = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    last_updated = models.DateTimeField(auto_now=True, null=True, blank=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=[
                    "user",
                    "ajo_user",
                    "name",
                ],
                name="unique_user_ajo_user_onlending_creation",
                violation_error_message="a plan with this name already exists",
            ),
        ]

        verbose_name = "Onlending"
        verbose_name_plural = "Onlending"

    def __str__(self) -> str:
        base = f"Onlending {self.name} - {self.user.email}"

        if self.ajo_user:
            base += f" - {self.ajo_user.phone_number}"

        return base


class OnlendingCommissionSetting(BaseModel):
    DURATION_CHOICES = [
        (12, "12 months"),
        (9, "9 months"),
        (6, "6 months"),
        (3, "3 months"),
    ]

    duration = models.PositiveSmallIntegerField(choices=DURATION_CHOICES)
    rate = models.DecimalField(max_digits=5, decimal_places=3)

    def __str__(self):
        return f"{self.duration} - {self.rate}% Commission"

    @classmethod
    def get_interpolated_rate(cls, target_duration):
        """
        Calculate the interpolated rate based on the rates of the nearest durations.

        Parameters:
        - target_duration (int): The target duration in days for which to calculate the interpolated rate.

        Returns:
        - float: The interpolated rate based on the rates of the nearest durations.
        """

        target_duration_in_months = target_duration // 30
        # Get the nearest durations to the target duration
        nearest_durations = cls.objects.filter(duration__lte=target_duration_in_months).order_by("-duration")[:2]

        # If only one nearest duration is found, return its rate
        if len(nearest_durations) == 1:
            return float(nearest_durations[0].rate / 100)

        # Calculate the interpolated rate based on the rates of the nearest durations
        rate_diff = abs(float(nearest_durations[1].rate - nearest_durations[0].rate))
        duration_diff = abs(float(nearest_durations[1].duration - nearest_durations[0].duration))
        interpolated_rate = (
            float(nearest_durations[0].rate)
            + float(target_duration - nearest_durations[0].duration) / duration_diff * rate_diff
        )

        return float(interpolated_rate / 100)


class OnlendingCommissionHistory(BaseModel):
    plan = models.OneToOneField("chestlock.Onlending", on_delete=models.CASCADE)
    amount = models.FloatField()
    rate = models.FloatField(default=0)
    paid_to = models.EmailField()
    paid_from = models.EmailField()


class LoanRequests(BaseModel):
    plan = models.ForeignKey(
        to=Onlending,
        on_delete=models.CASCADE,
        related_name="loan_requests",
    )
    loan_amount = models.FloatField()
    mandate_reference = models.CharField(max_length=100, unique=True)
    status = models.CharField(max_length=100)
    borrowers_name = models.CharField(max_length=100)
    borrowers_ministry = models.CharField(max_length=100)
    request_date = models.CharField(max_length=100)
    loan_duration = models.IntegerField(_("loan duration in months"))
