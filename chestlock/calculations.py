from typing import Any, Dict, List, Optional, Tuple

from django.core.cache import cache
from django.core.exceptions import ObjectDoesNotExist

from accounts.model_choices import UserType
from accounts.models import ConstantTable
from chestlock.models import OnlendingCommissionSetting


def calculate_frequency_breakdowns(amount: float, duration: int) -> Dict[str, float]:
    """
    calculates how much would be paid to achieve a target on a frequency
    basis

    Args:
        amount (float): the target that is expected to be reached
        duration (int): how long the person wants to save for

    Returns:
        Dict[str, float]: A dictionary of the different amounts per frequency
    """
    breakdowns = {}

    breakdowns["daily"] = round(amount / duration, 2)
    breakdowns["weekly"] = round(amount / (duration / 7), 2)
    breakdowns["monthly"] = round(amount / (duration / 30), 2)

    return breakdowns


def calculate_daily_chestlock_interest_rate(
    interest_rate: Optional[float] = None,
) -> float:
    """
    This calculates the daily interest rate based off
    the constant Chestlock interest rate.
    note that this is x/100 already, i.e. no percentage
    it is in its raw form already so do not divide by 100

    Returns:
        float: the raw daily interest rate
    """
    if not interest_rate:
        constants = ConstantTable.get_constant_table_instance()
        interest_rate = constants.chestlock_interest_rate

    interest_rate = float(interest_rate) / 100
    return interest_rate / 365


def calculate_estimated_amount_for_chestlock_plan(initial_deposit: float, duration: int):
    """
    Calculates the amount to be returned at the end of saving a chestlock plan,
    with interest.

    Args:
        initial_deposit (float): the initial amount.
        duration (int): how long the user want save for.

    Returns:
        float: the estimated amount to receive at the end of saving
    """
    daily_interest_rate = calculate_daily_chestlock_interest_rate()
    interest = initial_deposit * daily_interest_rate * duration
    return round(interest + initial_deposit, 2)


def calculate_interest_earned_for_chestlock_plan(initial_deposit: float, duration: int) -> float:
    """
    Calculates the amount earned on an initial amount based
    on the number of days the plan will be saved

    Args:
        amount (float): How much to be deposited
        duration (int): How long this amount will be saved for

    Returns:
        float: the amount of interest earned
    """
    daily_interest_rate = calculate_daily_chestlock_interest_rate()
    interest_earned = initial_deposit * daily_interest_rate * duration
    return round(interest_earned, 2)


def calculate_interests_for_large_day_intervals():
    # call the ConstantTable method to get the first instance
    constants = ConstantTable.get_constant_table_instance()
    # obtain the interest rate from the first instance with the percentage
    interest_rate = float(constants.chestlock_interest_rate)

    # calculate the daily interest rate
    daily_interest_rate = interest_rate / 365

    interests_range = [
        {
            "min_days": interest_range.get("min"),
            "max_days": interest_range.get("max"),
            "rate": f"~{round(daily_interest_rate * interest_range['max'], 1)}%",
        }
        for interest_range in [
            {"min": 90, "max": 120},
            {"min": 121, "max": 180},
            {"min": 181, "max": 240},
            {"min": 241, "max": 300},
            {"min": 301, "max": 365},
        ]
    ]

    return interests_range


def find_x_to_make_divisible_by_five(n):
    remainder = n % 5
    if remainder == 0:
        return 0
    else:
        x = 5 - remainder
        return x


def calculate_interests_for_days_in_range(min_days: int, max_days: int) -> List[Dict[str, int | float]]:
    if max_days < min_days:
        raise ValueError("min_days should be less than max_days")

    cache_key = f"interests_{min_days}_{max_days}"
    cached_result = cache.get(cache_key)

    if cached_result:
        return cached_result

    # call the ConstantTable method to get the first instance
    constants = ConstantTable.get_constant_table_instance()
    # obtain the interest rate from the first instance with the percentage
    interest_rate = float(constants.chestlock_interest_rate)

    # calculate the daily interest rate
    daily_interest_rate = interest_rate / 365

    interests_range = []
    days = min_days

    while days <= max_days:
        interest_rate_for_days = {
            "days": days,
            "rate": round(daily_interest_rate * days, 2),
        }
        interests_range.append(interest_rate_for_days)
        if days % 5 != 0:
            days += find_x_to_make_divisible_by_five(days)
        else:
            days += 5

    cache.set(cache_key, interests_range)  # Cache the result

    # return the outcome
    return interests_range


class OnlendingCalculations:

    @classmethod
    def calculate_interests_for_days_in_range(cls, min_days: int, max_days: int) -> List[Dict[str, int | float]]:
        if max_days < min_days:
            raise ValueError("min_days should be less than max_days")

        cache_key = f"interests_{min_days}_{max_days}"
        cached_result = cache.get(cache_key)

        if cached_result:
            return cached_result

        # call the ConstantTable method to get the first instance
        constants = ConstantTable.get_constant_table_instance()
        # obtain the interest rate from the first instance with the percentage
        interest_rate = float(constants.chestlock_interest_rate)

        # calculate the daily interest rate
        daily_interest_rate = interest_rate / 365

        interests_range = []
        days = min_days

        while days <= max_days:
            interest_rate_for_days = {
                "days": days,
                "rate": round(daily_interest_rate * days, 2),
            }
            interests_range.append(interest_rate_for_days)
            if days % 5 != 0:
                days += find_x_to_make_divisible_by_five(days)
            else:
                days += 5

        cache.set(cache_key, interests_range)  # Cache the result

        # return the outcome
        return interests_range

    @classmethod
    def calculate_interests_for_large_day_intervals(
        cls,
        user_type: Optional[UserType] = None,
    ) -> List[Any]:
        # call the ConstantTable method to get the first instance
        constants = ConstantTable.get_constant_table_instance()
        # obtain the interest rate from the first instance with the percentage
        interest_rate = float(constants.chestlock_interest_rate)

        # calculate the daily interest rate
        daily_interest_rate = interest_rate / 365

        ranges = [
            {"min": 90, "max": 120},
            {"min": 121, "max": 180},
            {"min": 181, "max": 240},
            {"min": 241, "max": 300},
            {"min": 301, "max": 365},
        ]

        print(user_type)
        if user_type in [UserType.DMO_AGENT, UserType.STAFF_AGENT]:
            day_ranges = [day_range for day_range in ranges if day_range.get("min") >= 180]
        else:
            day_ranges = ranges

        interests_range = [
            {
                "min_days": interest_range.get("min"),
                "max_days": interest_range.get("max"),
                "rate": f"~{round(daily_interest_rate * interest_range['max'], 1)}%",
            }
            for interest_range in day_ranges
        ]

        return interests_range

    @classmethod
    def calculate_interests_for_referals(
        cls,
        duration: int,
        deposit: float,
    ) -> Tuple[float, float]:

        interest_rate = OnlendingCommissionSetting.get_interpolated_rate(target_duration=duration)

        return round(deposit * interest_rate, 2), interest_rate
