from typing import Any, Dict, List, Optional

from django.contrib.auth.models import AbstractUser
from django.db.models import Count, Q, QuerySet, Sum
from django.utils import timezone

from payment.model_choices import PlanType, WalletTypes
from payment.models import Transaction, WalletSystem

from .models import <PERSON>jo<PERSON>ser, ChestLock, Onlending


class ChestlockMethods:
    def __init__(self, user: AbstractUser):
        self.query_set: QuerySet[ChestLock] = ChestLock.objects.filter(user=user)

    @staticmethod
    def get_chestlock_instance(id: int, user: AbstractUser) -> ChestLock:
        try:
            chestlock = ChestLock.objects.get(id=id, user=user)
            return chestlock
        except ChestLock.DoesNotExist:
            raise ValueError("this chestlock plan does not exist")

    def get_all_user_chestlock_plans(self) -> List[ChestLock]:
        return self.query_set

    def get_all_user_chestlock_type_plans(self, chestlock_type: str) -> List[ChestLock]:
        return self.query_set.filter(chestlock_type=chestlock_type)

    def get_amount_saved_from_all_user_chestlock_plans(self) -> float:
        return self.query_set.aggregate(total=Sum("amount_saved"))["total"] or 0

    def get_number_of_chestlock_plans_for_user(self) -> int:
        return self.query_set.count()


def get_transaction_history_for_chestlock_plan(quotation_id: str, descending_order: bool = None) -> List[Transaction]:
    qs = Transaction.objects.filter(quotation_id=quotation_id)

    if descending_order:
        qs = qs.order_by("-id")

    return qs


class OnlendingSelector:
    def __init__(self, user: AbstractUser, ajo_user: Optional[AjoUser] = None) -> None:
        """
        If this selector is for an ajo user, the ajo user instance must be passed.
        Else only the user instance can be passed

        Args:
            user (AbstractUser): the user instance
            ajo_user (Optional[AjoUser], optional): the ajo user instance. Defaults to None.
        """
        self.user = user
        self.ajo_user = ajo_user
        self.query_set: QuerySet[Onlending] = Onlending.objects.filter(
            user=user,
            ajo_user=ajo_user,
        )

    def get_onlending_instance(self, id: int = None, quotation_id: str = None) -> Onlending:
        """
        Retrieve a particular onlending instance.
        Use either id or quotation_id

        Args:
            id (int): the ID of the onlending plan
            quotation_id (str): the quotation ID of the onlending plan

        Raises:
            ValueError: this onlending plan does not exist

        Returns:
            Onlending: the onlending instance
        """
        if id:
            instance = self.query_set.filter(id=id).last()
        elif quotation_id:
            instance = self.query_set.filter(quotation_id=quotation_id).last()
        else:
            raise ValueError("provide either 'id' or 'quotation_id'")

        if not instance:
            raise ValueError("this onlending plan does not exist")

        return instance

    def get_all_onlending_plans(self) -> QuerySet[Onlending]:
        """
        Get the onlending queryset belonging to a user or ajo user

        Returns:
            QuerySet[Onlending]: A queryset of onlending plans
        """
        return self.query_set

    def get_amount_saved_in_onlending_plans(self) -> float:
        """
        Get the amount saved in all the plans a user has

        Returns:
            float: the amount saved
        """
        return self.query_set.aggregate(total=Sum("amount_saved"))["total"] or 0

    def get_number_of_onlending_plans_for_user(self) -> int:
        """
        Get the number of onlending plans that a user has

        Returns:
            int: the count
        """
        return self.query_set.count()

    def get_any_onlending_wallet(self, wallet_type: WalletTypes) -> WalletSystem:
        """
        Get any wallet from the WalletSystem

        Args:
            wallet_type (WalletTypes): the wallet type to retrieve

        Returns:
            WalletSystem: the retrieved wallet system
        """
        user = self.user
        ajo_user = self.ajo_user

        wallet = WalletSystem.get_or_create_wallet(
            user=user,
            wallet_type=wallet_type,
            ajo_user=ajo_user,
        )

        return wallet

    def get_onlending_wallet(self) -> WalletSystem:
        """
        Get the onlending savings wallet for the user or ajo user

        Returns:
            WalletSystem: Onlending Wallet
        """
        return self.get_any_onlending_wallet(wallet_type=WalletTypes.ONLENDING)

    def get_main_onlending_wallet(self) -> WalletSystem:
        """
        Get the ONLENDING_MAIN wallet for user or ajo user

        Returns:
            WalletSystem: ONLENDING_MAIN Wallet
        """
        return self.get_any_onlending_wallet(wallet_type=WalletTypes.ONLENDING_MAIN)

    def get_onlending_commission_wallet(self) -> WalletSystem:
        """
        Get the Commissions wallet for the user

        Returns:
            WalletSystem: ONLENDING_COMMISSION wallet
        """
        if self.ajo_user:
            raise ValueError("ajo users do not get commissions wallet")

        return self.get_any_onlending_wallet(wallet_type=WalletTypes.ONLENDING_COMMISSION)

    def get_transaction_history_for_onlending_plan(
        self,
        quotation_id: str,
        descending_order: Optional[bool] = None,
    ) -> QuerySet[Transaction]:
        """
        Obtain the transaction history for a particular onlending plan

        Args:
            quotation_id (str): the quotation id of the onlending plan
            descending_order (Optional[bool], optional): if the transaction history should be in descending order. Defaults to None.

        Returns:
            QuerySet[Transaction]: the transaction queryset
        """
        transactions = Transaction.objects.filter(quotation_id=quotation_id)

        if descending_order:
            transactions = transactions.order_by("-id")

        return transactions

    def get_all_plans_associated_with_user(self, is_active: Optional[bool] = None) -> QuerySet[Onlending]:
        """
        Get all the plans associated with a user, be it for the user
        or for the agent's ajo users

        Args:
            is_active (bool): to filter the queryset by is_active True/False

        Returns:
            QuerySet[Onlending]: queryset of onlending
        """
        user_plans = Onlending.objects.filter(user=self.user)

        if is_active is not None:
            user_plans = user_plans.filter(is_active=is_active)

        return user_plans

    def get_all_mature_plans_associated_with_user(self) -> QuerySet[Onlending]:
        """
        Get all the mature plans associated with a user, be it for the user or
        for the agent's ajo users

        Returns:
            QuerySet[Onlending]: queryset of mature onlending
        """
        plans = self.get_all_plans_associated_with_user(is_active=True)
        mature_plans = plans.filter(maturity_date__lte=timezone.localdate())

        return mature_plans

    def get_number_of_active_plans_associated_with_user(self) -> int:
        """
        Get the number of plans associated with a user, be it for the user or the user's
        ajo users

        Returns:
            int: the count
        """
        return self.get_all_plans_associated_with_user(is_active=True).count()

    def get_sum_saved_in_plans_associated_with_user(self) -> float:
        """
        Get the total in plans associated with user, be it for the user or the user's
        ajo users

        Returns:
            float: the sum total
        """
        return self.get_all_plans_associated_with_user().aggregate(total=Sum("amount_saved"))["total"] or 0

    def get_ajo_user_plan_details_associated_with_user(
        self,
        filter: Optional[str] = None,
        ajo_user: Optional[AjoUser] = None,
    ) -> Dict[str, Dict[str, Any]]:
        """_summary_

        Args:
            filter (Optional[str], optional): _description_. Defaults to None.

        Returns:
            Dict[str, Dict[str, Any]]: _description_
        """
        user_plans = self.get_all_plans_associated_with_user()

        plans_with_ajo_users = user_plans.exclude(ajo_user__isnull=True)

        valid_filters = ["active", "inactive", "mature"]

        if filter:
            if filter not in valid_filters:
                raise ValueError("invalid filter")

            if filter == "active":
                plans_with_ajo_users = plans_with_ajo_users.filter(
                    is_active=True,
                    maturity_date__gt=timezone.localdate(),
                )
            elif filter == "inactive":
                plans_with_ajo_users = plans_with_ajo_users.filter(is_active=False)
            elif filter == "mature":
                plans_with_ajo_users = plans_with_ajo_users.filter(maturity_date__lte=timezone.localdate())

        if ajo_user:
            plans_with_ajo_users = plans_with_ajo_users.filter(ajo_user=ajo_user)

        grouped_plans = (
            plans_with_ajo_users.values("ajo_user__phone_number")
            .annotate(
                plans_count=Count("id", distinct=True),
                total_amount_saved=Sum("amount_saved"),
            )
            .order_by("ajo_user")
        )

        grouped_data = {}

        for entry in grouped_plans:
            phone_number = entry["ajo_user__phone_number"]
            grouped_data[phone_number] = {
                "plans_count": entry["plans_count"],
                "total_amount_saved": entry["total_amount_saved"],
                "ajo_user": plans_with_ajo_users.filter(ajo_user__phone_number__startswith=phone_number).first().ajo_user,
                # If you need to include the actual plans queryset, you can do:
                # 'plans_queryset': plans_with_ajo_user.filter(ajo_user__phone_number=phone_number)
            }

        return grouped_data

    def get_general_transaction_history_for_user(self) -> QuerySet[Transaction]:
        """
        Gets all the transaction history associated with a user/agent for onlending

        Returns:
            QuerySet[Transaction]: Transaction Queryset
        """
        qs = Transaction.objects.filter(
            Q(plan_type=PlanType.ONLENDING) | Q(wallet_type__in=[WalletTypes.ONLENDING, WalletTypes.ONLENDING_MAIN]),
            user=self.user,
            # wallet_type__in=[WalletTypes.ONLENDING, WalletTypes.ONLENDING_MAIN],
        ).distinct()
        return qs

    def get_onlending_transaction_history(self) -> QuerySet[Transaction]:
        """
        Get the onlending transaction history for a user
        and subsequently, for an ajo user as well.

        Returns:
            QuerySet[Transaction]: a query set of the Transaction objects
        """
        qs = self.get_general_transaction_history_for_user()

        if self.ajo_user:
            qs = qs.filter(onboarded_user=self.ajo_user)

        return qs

    @staticmethod
    def does_plan_exist(name: str, user: AbstractUser, ajo_user: Optional[AjoUser] = None) -> bool:
        """
        Checks if an onlending plan exists

        Args:
            name (str): the name of the onlending plan
            user (AbstractUser): the user instance
            ajo_user (AjoUser): the ajo user instance

        Returns:
            bool: returns if the plan exists or not i.e. True/False
        """
        return Onlending.objects.filter(
            name=name,
            user=user,
            ajo_user=ajo_user,
        ).exists()

    @classmethod
    def get_onlending_plan_by_id(
        cls,
        id: int,
        user: Optional[AbstractUser] = None,
    ) -> Onlending:
        """
        Get an Onlending plan by ID

        Args:
            id (int): the ID of the onlending plan
            user (Optional[AbstractUser], optional): utilize this field if you want to filter by user. Defaults to None.

        Raises:
            ValueError: this onlending plan does not exist

        Returns:
            Onlending: the retrieved Onlending instance.
        """
        qs = Onlending.objects.filter(id=id)

        if user:
            qs = qs.filter(user=user)

        if not qs.exists():
            raise ValueError("this onlending plan does not exist")

        return qs.first()
