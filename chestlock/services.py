from typing import Any, Dict

from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.utils import timezone

from .calculations import calculate_interest_earned_for_chestlock_plan
from .models import ChestLock, Onlending


class ChestlockService:

    @classmethod
    def create_chestlock_instance(cls, validated_data_dict: dict) -> ChestLock:
        try:
            chestlock = ChestLock.objects.create(**validated_data_dict)
        except IntegrityError:
            # handles the unique constraint error
            raise ValueError(
                f"you already have a plan with this name: {validated_data_dict.get('plan')}",
            )

        return chestlock

    @classmethod
    def activate_and_recalculate_interest(cls, chestlock: ChestLock) -> None:
        time = timezone.localtime()
        maturity_date = chestlock.maturity_date

        new_duration = (maturity_date - time.date()).days
        if new_duration <= 0:
            raise ValueError("the maturity date is in the past")

        interest = calculate_interest_earned_for_chestlock_plan(
            initial_deposit=chestlock.target,
            duration=new_duration,
        )

        chestlock.activated_at = time
        chestlock.total_interest_earned = interest
        chestlock.completed_at = time
        chestlock.save()


class OnlendingService:

    @classmethod
    def create_onlending_instance(cls, data: Dict[str, Any]) -> Onlending:
        try:
            onlending = Onlending(**data)
            onlending.full_clean()
            onlending.save()

        except (ValidationError, IntegrityError) as err:
            raise ValueError(f"error occured: {str(err)}")

        return onlending

    @classmethod
    def update_onlending_instance(cls, onlending_instance: Onlending, data: Dict[str, Any]) -> Onlending:
        """
        Updates an onlending instance

        Args:
            onlending_instance (Onlending): the onlending instance to update
            data (Dict[str, Any]): the data being updated

        Returns:
            Onlending: the updated onlending instance
        """
        for key, value in data.items():
            setattr(onlending_instance, key, value)

        onlending_instance.save()

        return onlending_instance

    @classmethod
    def activate_and_recalculate_interest(cls, onlending: Onlending) -> None:
        """
        Recalculates the interest for an onlending plan and marks date of plan activation
        Args:
            onlending (Onlending): the onlending plan instance
        Raises:
            ValueError: the maturity date is in the past
        """
        time = timezone.localtime()
        maturity_date = onlending.maturity_date

        new_duration = (maturity_date - time.date()).days
        if new_duration <= 0:
            raise ValueError("the maturity date is in the past")

        interest = calculate_interest_earned_for_chestlock_plan(
            initial_deposit=onlending.target,
            duration=new_duration,
        )
        if onlending.onlending_type == "ONEOFF":
            onlending.total_interest_earned = interest
        else:
            pass
        onlending.activated_at = time
        onlending.completed_at = time
        onlending.save()
