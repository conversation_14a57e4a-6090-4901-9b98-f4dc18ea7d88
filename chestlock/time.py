import calendar
import datetime

from django.utils import timezone

# DO THE CELERY TASKS LIKE THIS
# @periodic_task(run_every=(crontab(hour=23, minute=55)), name="invoice_simulation", ignore_result=True)
# def invoice_simulation():
#     if timezone.now().day == get_last_date():
#         generate_invoice()

# Calculates the end of the month
def get_last_day_of_month():
    """
    Returns the last day of the month eg 31, 30, 28 etc
    """
    today = timezone.now()
    year = today.year
    month = today.month
    last_date = calendar.monthrange(year, month)[1]
    return str(last_date)


def get_first_day_of_the_week():
    """
    Returns the first day of the week i.e. Sunday which is denoted by 6
    It returns the closest sunday to the current day
    """
    today = datetime.date.today()
    sunday = today + datetime.timedelta((6 - today.weekday()) % 7)
    return sunday.day
