from typing import Any, Dict

from celery import shared_task
from django.db import transaction

from .models import Onlending
from .utils import LoanRequestsManager


@shared_task
@transaction.atomic
def create_loan_requests_for_onlending_plan(plan_id: int) -> Dict[str, Any]:
    try:
        onlending_plan = Onlending.objects.get(id=plan_id)
    except Onlending.DoesNotExist:
        return {
            "status": "failed",
            "message": "No onlending plan with this ID exists",
        }

    max_tries = 3
    for attempt in range(max_tries):
        try:
            LoanRequestsManager(plan=onlending_plan).process_loan_requests_for_plan()
            return {
                "status": "success",
                "message": f"Loan requests created for onlending plan with ID: {plan_id}",
            }
        except Exception as err:
            error_message = str(err)
            if "obtaining" not in error_message:
                return {
                    "status": "failed",
                    "message": "Error creating loan requests",
                    "data": error_message,
                }
            if attempt == max_tries - 1:
                return {
                    "status": "failed",
                    "message": "Maximum retries reached. Error creating loan requests",
                    "data": error_message,
                }
