from django.db import models
from django.utils.translation import gettext_lazy as _


class Frequency(models.TextChoices):
    DAILY = "DAILY", _("DAILY")
    WEEKLY = "WEEKLY", _("WEEKLY")
    MONTHLY = "MONTHLY", _("MONTHLY")


class ChestlockType(models.TextChoices):
    RECURRENT = "RECURRENT", _("RECURRENT")
    ONEOFF = "ONEOFF", _("ONEOFF")


class OnlendingType(models.TextChoices):
    RECURRENT = "RECURRENT", _("RECURRENT")
    ONEOFF = "ONEOFF", _("ONEOFF")


class InterestType(models.TextChoices):
    UPFRONT = "UPFRONT", _("UPFRONT")
    MATURITY_DATE = "MATURITY_DATE", _("MATURITY_DATE")


class FrequencyTypes(models.TextChoices):
    DAILY = "daily", _("daily")
    WEEKLY = "weekly", _("weekly")

