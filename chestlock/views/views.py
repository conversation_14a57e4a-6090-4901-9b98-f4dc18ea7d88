import uuid

from django.utils.decorators import method_decorator
from django.db import transaction as django_transaction
from django.views.decorators.cache import cache_page
from django.utils import timezone
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics, permissions, serializers, status
from rest_framework.exceptions import NotFound
from rest_framework.response import Response

from accounts.agency_banking import AgencyBankingClass
from accounts.email_templates.template_choices import EmailTemplates, EmailTypes
from accounts.models import ConstantTable
from accounts.tasks import send_html_email
from cards.utils import get_serializer_key_error
from payment.checks import verify_transaction_pin
from payment.model_choices import PlanType, WalletTypes
from payment.utils import format_currency
from savings.pagination import CustomPagination
from accounts.responses import (
    pagination_page_not_found_response,
    serializer_validation_error_response,
    value_error_response,
)
from accounts.utils import ResourceCreationLimiting

from ..calculations import (
    calculate_interests_for_days_in_range,
    calculate_interests_for_large_day_intervals,
)
from ..model_choices import ChestlockType
from ..models import ChestLock
from ..selectors import ChestlockMethods, get_transaction_history_for_chestlock_plan
from ..serializers.serializers import (
    AskForInterestsRatesInRangeSerializer,
    ChestlockSerializer,
    CreateChestlockPlanSerializer,
    CurrentPlanDetailsSerializer,
    DaysMagnitudeInterestRateSerializer,
    GeneratePlanDetailsSerializer,
    LargeDaysRangeInterestSerializer,
    ListOfAllChestlockSavingsSerializer,
    MakeChestlockPaymentSerializer,
    RetrieveChestlockInstanceSerializer,
    TotalBalanceAndSavingsSerializer,
    TotalBalanceSavingsListOfAllChestlockSavingsSerializer,
    TransactionHistorySerializer,
)
from ..services import ChestlockService
from ..utils import (
    extended_plan_details_for_oneoff_chestlock,
    extended_plan_details_for_recurrent_chestlock,
    generate_oneoff_chestlock_plan_details,
    generate_recurrent_chestlock_plan_details,
)


class GeneratePlanDetailsAPIView(generics.GenericAPIView):
    """
    This view intends to get the plan name, duration and target of the customer
    and returns the daily, weekly and monthly breakdowns
    """

    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = GeneratePlanDetailsSerializer

    def post(self, request, *args, **kwargs):
        # attempt to validate the data
        serializer = self.serializer_class(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        response_data = {}
        # get the plan name and check if it exists
        plan = serializer.validated_data.get("plan")
        # check if the plan exists
        if ChestLock.objects.filter(plan=plan, user=request.user).exists():
            return Response(
                {
                    "error": "614",
                    "status": False,
                    "message": "a plan with this name exists already",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # obtain the other fields needed
        duration = serializer.validated_data.get("duration")
        target = round(float(serializer.validated_data.get("target")), 2)
        chestlock_type = serializer.validated_data.get("chestlock_type")

        if chestlock_type is ChestlockType.RECURRENT:
            # Working based on the minimum duration being 3 months
            # Assuming that the duration comes in days
            response_data = {
                "status": True,
                "data": {
                    "plan": plan,
                    **generate_recurrent_chestlock_plan_details(
                        duration=duration,
                        amount=target,
                    ),
                },
            }

        else:
            response_data = {
                "status": True,
                "data": {
                    "plan": plan,
                    **generate_oneoff_chestlock_plan_details(
                        duration=duration,
                        amount=target,
                    ),
                },
            }

        return Response(response_data, status=status.HTTP_200_OK)


class InterestRatesForDayRangesAPIView(generics.GenericAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_classes = {
        "GET": LargeDaysRangeInterestSerializer,
        "POST": AskForInterestsRatesInRangeSerializer,
    }

    def get_serializer_class(self):
        return self.serializer_classes.get(self.request.method)

    @swagger_auto_schema(
        responses={
            200: DaysMagnitudeInterestRateSerializer(many=True),
        }
    )
    def post(self, request, *args, **kwargs):
        """
        Generates the list of 5 days interval alongside their interest rate
        e.g. 35 days: 7
        """
        # attempt to validate the data
        serializer = self.get_serializer_class()(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        # obtain the values and pass them to the function
        min_days: int = serializer.validated_data.get("min_days")
        max_days: int = serializer.validated_data.get("max_days")

        data_serializer = DaysMagnitudeInterestRateSerializer(
            calculate_interests_for_days_in_range(
                min_days=min_days,
                max_days=max_days,
            ),
            many=True,
        )

        return Response(
            {
                "status": True,
                "data": data_serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        responses={
            200: LargeDaysRangeInterestSerializer(many=True),
        }
    )
    @method_decorator(cache_page(300))
    def get(self, request, *args, **kwargs):
        """
        This returns the ranges for the interest rates for the OneOFF Chestlock
        eg 10 - 30 days: ~3%
        """
        serializer = self.get_serializer_class()(
            calculate_interests_for_large_day_intervals(),
            many=True,
        )
        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )


class CreateChestLockPlanAPIView(generics.GenericAPIView):
    """
    This view gets all the details for chestlock plan,
    generates a quotation id for the plan and saves it to the database.
    """

    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = CreateChestlockPlanSerializer

    def post(self, request, *args, **kwargs):
        """
        For RECURRENT Chestlock Plans, pass in the following data:
                {
                    "chestlock_type": "RECURRENT",
                    "plan": plan name,
                    "target": how much you want to save,
                    "duration": how long you intend to save,
                    "frequency": the frequency at which you want to save,
                    "hour": what time you want to save everyday automatically,
                }

        For ONEOFF Chestlock Plans, pass in the following data:
                {
                    "chestlock_type": "ONEOFF",
                    "plan": plan name,
                    "target": how much you want to save,
                    "duration": how long you intend to save,
                }
        """
        user = request.user
        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        if ResourceCreationLimiting.is_resource_created_recently(user_id=user.id):
            return Response(
                {
                    "error": "429",
                    "status": False,
                    "message": "wait a while before you create a new plan",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        duration = serializer.validated_data.get("duration")
        target = serializer.validated_data.get("target")
        chestlock_type = serializer.validated_data.get("chestlock_type")

        # generate a unique UUID 4 quotation id for the plan
        quotation_id = f"LI-CHK-{uuid.uuid4()}"

        # Perform a different flow if it is RECURRENT
        if chestlock_type is ChestlockType.RECURRENT:
            frequency = serializer.validated_data.get("frequency")

            # call the function to get the other calculated data
            details = extended_plan_details_for_recurrent_chestlock(
                duration=duration,
                amount=target,
                frequency=frequency,
            )

            # convert the validated_data to an ordinary dict and update it with fields
            validated_data_dict = dict(serializer.validated_data)
            validated_data_dict.update(
                {
                    "user": user,
                    "quotation_id": quotation_id,
                    **details,
                }
            )

        elif chestlock_type is ChestlockType.ONEOFF:
            # call the function to get the other calculated data
            details = extended_plan_details_for_oneoff_chestlock(
                duration=duration,
                amount=target,
            )

            # convert the validated_to an ordinary dict and update it with fields
            validated_data_dict = dict(serializer.validated_data)
            validated_data_dict.update(
                {
                    "user": user,
                    "quotation_id": quotation_id,
                    **details,
                }
            )

        # attempt to create an instance in the database
        try:
            chestlock: ChestLock = ChestlockService.create_chestlock_instance(validated_data_dict)
            ResourceCreationLimiting.create_resource(user_id=user.id)
        except ValueError as err:
            return value_error_response(error=err)

        chestlock_serializer = ChestlockSerializer(chestlock)

        return Response(
            {
                "status": True,
                "data": chestlock_serializer.data,
            },
            status=status.HTTP_201_CREATED,
        )


class CurrentPlanDetailsAPIView(generics.GenericAPIView):
    """
    Shows the amount saved so far, savings amount and maturity date for plans in progress

    """

    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = CurrentPlanDetailsSerializer

    @swagger_auto_schema(
        query_serializer=RetrieveChestlockInstanceSerializer,
        responses={
            "200": CurrentPlanDetailsSerializer,
            "400": "Bad Request",
        },
    )
    def get(self, request, *args, **kwargs):
        id = request.query_params.get("id")
        user = request.user

        try:
            chestlock = ChestlockMethods.get_chestlock_instance(id=id, user=user)
        except ValueError as err:
            return value_error_response(err)

        serializer = self.serializer_class(chestlock)
        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )


class TransactionHistoryAPIView(generics.GenericAPIView):
    """
    Shows the list of transactions done for a particular chestlock plan
    """

    permission_classes = (permissions.IsAuthenticated,)
    pagination_class = CustomPagination
    serializer_class = TransactionHistorySerializer

    @swagger_auto_schema(
        query_serializer=RetrieveChestlockInstanceSerializer,
        responses={
            "200": TransactionHistorySerializer,
            "400": "Bad Request",
        },
    )
    def get(self, request, *args, **kwargs):
        id = request.query_params.get("id")
        user = request.user

        try:
            chestlock = ChestlockMethods.get_chestlock_instance(id=id, user=user)
        except ValueError as err:
            return value_error_response(err)

        try:
            paginated_results = self.paginate_queryset(
                get_transaction_history_for_chestlock_plan(quotation_id=chestlock.quotation_id, descending_order=True)
            )
        except NotFound:
            return pagination_page_not_found_response()

        # serialize the paginated results
        serializer = self.serializer_class(paginated_results, many=True)

        # return the paginated response
        return self.get_paginated_response(serializer.data)


class TotalBalanceAndSavingsAPIView(generics.GenericAPIView):
    """
    This shows the total balance of all the savings, the number of savings
    and a list of the chestlock savings a user has.
    """

    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = TotalBalanceSavingsListOfAllChestlockSavingsSerializer

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "filter",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="insert RECURRENT or ONEOFF",
            )
        ]
    )
    @method_decorator(cache_page(20))
    def get(self, request, *args, **kwargs):
        # declare the list of valid filters
        valid_list_of_filters = list(ChestlockType.__members__.keys())

        # obtain the filter query parameter
        filter_param = request.query_params.get("filter")
        filter_param = filter_param.upper() if filter_param else None

        # validate the type_filter
        if not filter_param:
            return Response(
                {
                    "error": "603",
                    "status": False,
                    "message": "'filter' query parameter is null",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        elif filter_param not in valid_list_of_filters:
            return Response(
                {
                    "error": "603",
                    "status": False,
                    "message": f"the filter value is not in this list {valid_list_of_filters}",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        # convert the filter param to a chestlock type
        filter_param = getattr(ChestlockType, filter_param.upper())

        # instantiate the ChestlockMethods class
        chestlock_methods = ChestlockMethods(user=request.user)

        # obtain the the balances and summary
        data = {
            "total_balance": chestlock_methods.get_amount_saved_from_all_user_chestlock_plans(),
            "savings": chestlock_methods.get_number_of_chestlock_plans_for_user(),
        }
        # serialize the data
        serializer = TotalBalanceAndSavingsSerializer(data)

        # obtain the list of chestlock plans based on types
        chestlock_serializer = ListOfAllChestlockSavingsSerializer(
            chestlock_methods.get_all_user_chestlock_type_plans(chestlock_type=filter_param),
            many=True,
        )

        return Response(
            {
                "status": "success",
                "data": {"summary": serializer.data, "chestlock_savings_list": chestlock_serializer.data},
            },
            status=status.HTTP_200_OK,
        )
    
class ManualCheslockPayments(generics.GenericAPIView):
    """
    This view is responsible for making payments for chestlock recurrent plans.
    """

    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = MakeChestlockPaymentSerializer

    def post(self, request):
        auth_header = request.headers.get("Authorization", "")
        token_type, _, access_token = auth_header.partition(" ")
        user = request.user

        # Regulator
        if (
            ConstantTable.get_constant_table_instance().pay_from_wallet_regulator
            == False
        ):
            response = {
                "error": "324",
                "status": False,
                "message": "service currently unavailable, please try again later",
            }
            return Response(response, status=status.HTTP_403_FORBIDDEN)

        serializer = self.serializer_class(data=request.data)
    
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError:

            data = {
                "status": "failed",
                "message": get_serializer_key_error(serializer.errors),
            }
            
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        
        transaction_pin = serializer.validated_data.get("transaction_pin")

        check_pin = verify_transaction_pin(
            transaction_pin=transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )

        if check_pin is True:
            pass
        else:
            data = {
                "status": "failed",
                "message": "Incorrect transaction pin",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        
        plan_instance = serializer.validated_data.get("chestlock_plan")
        amount = serializer.validated_data.get("amount")
        charge = AgencyBankingClass.charge_user_wallet(
            user=user,
            plan_type=PlanType.CHESTLOCK,
            quotation_id=plan_instance.quotation_id,
            transaction_pin=transaction_pin,
            amount=amount,
            access_token=access_token,
        )
        
        if (charge.get("status") == True) and ("debit_credit_info" in charge.keys()):
            
            debit_credit = charge.get("debit_credit_info")
            transaction = charge.get("transaction")

            # set the transaction wallet_balance_before, plan_balance_before and plan instance's balance_before
            transaction.wallet_balance_before = debit_credit.get("balance_before")
            transaction.plan_balance_before = plan_instance.amount_saved
            plan_instance.plan_balance_before = plan_instance.amount_saved

            # increase the amount saved
            plan_instance.amount_saved += transaction.amount

            # set the transaction balance after
            transaction.wallet_balance_after = debit_credit.get("balance_after")
            transaction.plan_balance_after = (
                transaction.plan_balance_before + transaction.amount
            )
            plan_instance.plan_balance_after = (
                plan_instance.plan_balance_before + transaction.amount
            )
            
            with django_transaction.atomic():
                plan_instance.save()
                transaction.save()
                
            now = timezone.localtime(timezone.now())
            date_string = now.strftime("%b %d, %Y %H:%M")
                
            send_html_email.delay(
                template_name=EmailTemplates.SAVEDFROMWALLET,
                user_email=user.email,
                email_type=EmailTypes.SAVING,
                plan_type=PlanType.CHESTLOCK,
                plan_transaction_object_id=transaction.id,
                email_subject="CHESTLOCK PLAN PAYMENTS MADE MANUALLY",
                plan_name=plan_instance.plan,
                amount=format_currency(transaction.amount),
                date_and_time=date_string,
                savings_wallet=getattr(WalletTypes, "CHESTLOCK")
            )

            data = {
                "status": "success",
                "message": "Chestlock Plan funded successfully.",
            }
            
            return Response(data, status=status.HTTP_200_OK)
        
        if (charge.get("status") == False) and (
            "insufficient" in charge.get("message")
        ):
            return Response(
                {
                    "status": "failed",
                    "message": charge.get("message"),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        # error in amount or user_id
        else:
            return Response(
                {
                    "status": "failed",
                    "message": charge.get("message"),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        
        
        

        
        
