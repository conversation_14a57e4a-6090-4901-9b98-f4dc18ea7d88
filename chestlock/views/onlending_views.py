import json
from typing import Optional
import uuid

from django.conf import settings
from django.db.models import Q
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.generics import GenericAPIView
from rest_framework.pagination import NotFound
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework.serializers import Serializer, ValidationError

from accounts.agency_banking import AgencyBankingClass
from accounts.liberty_ussd import LibertyUSSD
from accounts.model_choices import UserType
from accounts.responses import (
    pagination_page_not_found_response,
    serializer_validation_error_response,
    value_error_response,
)
from accounts.services import UserService
from accounts.utils import ResourceCreationLimiting
from ajo import payment_actions
from ajo.selectors import AccountFormType, AjoUserSelector, BankAccountSelector
from ajo.services import AjoUserService, BankAccountService
from ajo.utils.otp_utils import verify_sms_voice_otp
from ajo.utils.ussd_utils import verify_ussd_otp
from cards.utils import get_serializer_key_error
from payment.model_choices import Status
from savings.pagination import CustomPagination

from ..calculations import OnlendingCalculations
from ..model_choices import InterestType, OnlendingType
from ..selectors import OnlendingSelector
from ..serializers.onlending_serializers import (
    AjoUserBalancesSummarySerializer,
    CreateOnlendingPlanSerializer,
    EditOnlendingPlanSerializer,
    GenerateOnlendingPlanDetailsSerializer,
    GetEnrollmentLinkSerializer,
    InterestsRatesInRangeSerializer,
    ListOnlendingPlansSerializer,
    MakeManualPaymentSerializer,
    OnlendingPlanDetailsSerializer,
    OnlendingSerializer,
    OnlendingTransactionHistorySerializer,
    PendingLoanRequestSerializer,
    PlanSummarySerializer,
    RolloverOnlendingSerializer,
    SendEnrollmentLinkThroughSMSSerializer,
    TotalBalanceSavingsSerializer,
    VirtualAccountDetailsSerializer,
)
from ..serializers.serializers import (
    DaysMagnitudeInterestRateSerializer,
    LargeDaysRangeInterestSerializer,
)
from ..services import OnlendingService
from ..utils import EnrollmentLink, OnlendingDynamicTransactions, OnlendingUtils, cal_missed_amount
from .views import status


class InterestRatesForDayRangesAPIView(GenericAPIView):
    permission_classes = (AllowAny,)
    serializer_classes = {
        "GET": LargeDaysRangeInterestSerializer,
        "POST": InterestsRatesInRangeSerializer,
    }

    def get_serializer_class(self):
        return self.serializer_classes.get(self.request.method)

    @swagger_auto_schema(
        responses={
            200: DaysMagnitudeInterestRateSerializer(many=True),
        }
    )
    def post(self, request, *args, **kwargs):
        """
        Generates the list of 5 days interval alongside their interest rate
        """
        # attempt to validate the data
        serializer = self.get_serializer_class()(data=request.data, context={"user": request.user})
        try:
            serializer.is_valid(raise_exception=True)
        except ValidationError as err:
            return serializer_validation_error_response(error=err)

        # obtain the values and pass them to the function
        min_days: int = serializer.validated_data.get("min_days")
        max_days: int = serializer.validated_data.get("max_days")

        data_serializer = DaysMagnitudeInterestRateSerializer(
            OnlendingCalculations.calculate_interests_for_days_in_range(
                min_days=min_days,
                max_days=max_days,
            ),
            many=True,
        )

        return Response(
            {
                "status": True,
                "data": data_serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        responses={
            200: LargeDaysRangeInterestSerializer(many=True),
        }
    )
    @method_decorator(cache_page(90))
    def get(self, request, *args, **kwargs):
        """
        This returns the ranges for the interest rates for Onlending
        eg 90 - 180 days: ~3%
        """
        user = request.user

        if not user.is_authenticated:
            user_type = UserType.STAFF_AGENT
        else:
            user_type = user.user_type

        serializer = self.get_serializer_class()(
            OnlendingCalculations.calculate_interests_for_large_day_intervals(user_type=user_type),
            many=True,
        )
        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )
    
        
class GenerateOnlendingPlanDetailsAPIView(GenericAPIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = GenerateOnlendingPlanDetailsSerializer

    @swagger_auto_schema(
        responses={
            200: "Generated one off plan details",
            400: "error",
            422: "request data error",
        }
    )
    def post(self, request, *args, **kwargs):
        """
        Generate the details for the onlending plans whether oneoff or recurrent.
        """
        serializer = self.serializer_class(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except ValidationError as err:
            return serializer_validation_error_response(error=err)

        user = request.user
        name = serializer.validated_data.get("name")
        duration = serializer.validated_data.get("duration")
        target = round(serializer.validated_data.get("target"), 2)
        phone_number = serializer.validated_data.get("phone_number", None)
        onlending_type = serializer.validated_data.get("onlending_type")

        if phone_number:
            try:
                ajo_user = AjoUserSelector(phone_number=phone_number, user=user).get_ajo_user()
            except ValueError as err:
                return value_error_response(error=err)
        else:
            ajo_user = None

        if OnlendingSelector.does_plan_exist(
            name=name,
            user=user,
            ajo_user=ajo_user,
        ):
            return Response(
                {
                    "error": "614",
                    "status": False,
                    "message": "a plan with this name already exists",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        if onlending_type is OnlendingType.ONEOFF:
            response_data = {
                "status": True,
                "data": {
                    "name": name,
                    **OnlendingUtils.generate_oneoff_onlending_plan_details(
                        duration=duration,
                        amount=target,
                    ),
                },
            }
        else:
            response_data = {
                "status": True,
                "data": {
                    "name": name,
                    **OnlendingUtils.generate_recurrent_onlending_plan_details(
                        duration=duration,
                        amount=target,
                    ),
                },
            }

        return Response(
            data=response_data,
            status=status.HTTP_200_OK,
        )


class CreateEditOnlendingPlanAPIView(GenericAPIView):
    permission_classes = (IsAuthenticated,)
    serializer_classes = {
        "POST": CreateOnlendingPlanSerializer,
        "PUT": EditOnlendingPlanSerializer,
    }

    def get_serializer_class(self) -> Serializer:
        return self.serializer_classes.get(self.request.method)

    @swagger_auto_schema(
        responses={
            201: "created",
            400: "error",
            422: "request data error",
        }
    )
    def post(self, request, *args, **kwargs):
        """
        Create an Onlending Plan.

        For interest_type: "UPFRONT" or "MATURITY_DATE"
        """

        user = request.user
        serializer = self.get_serializer_class()(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except ValidationError as err:
            return serializer_validation_error_response(error=err)

        if ResourceCreationLimiting.is_resource_created_recently(user_id=user.id):
            return Response(
                {
                    "error": "429",
                    "status": False,
                    "message": "wait a while before you create a new plan",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        duration = serializer.validated_data.get("duration")
        target = serializer.validated_data.get("target")
        phone_number = serializer.validated_data.get("phone_number", None)
        interest_type = serializer.validated_data.get("interest_type")
        onlending_type = serializer.validated_data.get("onlending_type")
    
        if user.user_type == UserType.STAFF_AGENT and not phone_number:
            return Response(
                {
                    "error": "425",
                    "status": False,
                    "message": "staff agent cannot create for self",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        ajo_user = None
        if phone_number:
            try:
                ajo_user = AjoUserSelector(
                    phone_number=phone_number,
                    user=user,
                ).get_ajo_user()
            except ValueError as err:
                return value_error_response(error=err)
            
        if onlending_type == OnlendingType.ONEOFF:

            # generate plan details
            details = OnlendingUtils.create_oneoff_plan_information(
                duration=duration,
                amount=target,
            )

        else: 
            frequency = serializer.validated_data.get("frequency")
            details = OnlendingUtils.extended_plan_details_for_recurrent_onlending(
                duration=duration,
                amount=target,
                frequency=frequency
            )

        data = dict(serializer.validated_data)
        data.pop("phone_number", None)
        data.update(
            {
                "user": user,
                "ajo_user": ajo_user,
                "is_active": False,
                "interest_type": InterestType.MATURITY_DATE if onlending_type == "RECURRENT" else interest_type,
                # defaults recurrent interest type to be maturity date.
                **details,
            }
        )
       
        try:
            onlending = OnlendingService.create_onlending_instance(data=data)
            ResourceCreationLimiting.create_resource(user_id=user.id)
        except ValueError as err:
            return value_error_response(error=err)

        # prepare for email sending
        email_data = {
            "name": onlending.name,
            "deposit": onlending.target,
            "interest_rate": onlending.interest_rate,
            "duration": onlending.duration,
            "start_date": onlending.created_at,
            "maturity_date": onlending.maturity_date,
            "quotation_id": onlending.quotation_id,
            "rollover": True,
            "estimated_amount": onlending.estimated_amount,
            "interest_type": onlending.interest_type,
            "onlender": f"{user.first_name} {user.last_name}",
        }
        OnlendingUtils.send_onlending_creation_email(
            email=user.email,
            data=email_data,
            task=False,
        )

        onlending_serializer = OnlendingSerializer(onlending)

        return Response(
            {
                "status": True,
                "data": onlending_serializer.data,
            },
            status=status.HTTP_201_CREATED,
        )

    def put(self, request, *args, **kwargs):
        """
        Edit an existing Onlending plan
        """
        user = request.user
        serializer = self.get_serializer_class()(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except ValidationError as err:
            return serializer_validation_error_response(err)

        data = serializer.validated_data
        plan_id = data.get("id")
        target = data.get("target")
        duration = data.get("duration")
        interest_type = data.get("interest_type")
        onlending_selector = OnlendingSelector(user=user)

        try:
            onlending_plan = OnlendingSelector.get_onlending_plan_by_id(
                id=plan_id,
                user=user,
            )
        except ValueError as err:
            return value_error_response(error=err)

        if onlending_plan.is_activated:
            return Response(
                {
                    "status": False,
                    "error": "877",
                    "message": "this plan has already been activated",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not onlending_plan.is_active and onlending_plan.is_activated:
            return Response(
                {
                    "status": False,
                    "error": "981",
                    "message": "this plan is not active",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # generate plan details
        details = OnlendingUtils.create_oneoff_plan_information(
            duration=duration,
            amount=target,
        )

        data = dict(serializer.validated_data)
        data.pop("id", None)
        data.update(
            {
                **details,
            }
        )

        try:
            onlending = OnlendingService.update_onlending_instance(
                onlending_instance=onlending_plan,
                data=data,
            )
        except ValueError as err:
            return value_error_response(error=err)

        onlending_serializer = OnlendingSerializer(onlending)

        return Response(
            {
                "status": True,
                "data": onlending_serializer.data,
            },
            status=status.HTTP_201_CREATED,
        )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                name="id",
                in_=openapi.IN_QUERY,
                description="the plan ID",
                required=True,
                type=openapi.TYPE_INTEGER,
            ),
        ],
        responses={204: "deleted"},
    )
    def delete(self, request, *args, **kwargs):
        """delete an inactive, never activated onlending plan"""
        user = request.user
        plan_id = request.query_params.get("id")

        try:
            onlending_plan = OnlendingSelector.get_onlending_plan_by_id(
                id=plan_id,
                user=user,
            )
        except ValueError as err:
            return value_error_response(error=err)

        if onlending_plan.is_activated or onlending_plan.is_active:
            return Response(
                {
                    "status": False,
                    "error": "949",
                    "message": "this plan cannot be deleted because it has been activated before",
                },
                status.HTTP_400_BAD_REQUEST,
            )

        onlending_plan.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

class MakeManualPayments(GenericAPIView):

    permission_classes = (IsAuthenticated,)
    serializer_class = [MakeManualPaymentSerializer]

    def post(self, request):
        user = request.user
        serializer = MakeManualPaymentSerializer(data=request.data, context={"request": request})
        if serializer.is_valid():
            validated_data = serializer.validated_data
            onlending_plan = validated_data.get("onlending_plan")
            ajo_user = validated_data.get("ajo_user")
            from_wallet = validated_data.get("from_wallet")
            onlending_wallet = validated_data.get("onlending_wallet")
            amount = validated_data.get("amount")
            
            # try:
            #     OnlendingDynamicTransactions.dynamic_onlending_payment_transactions(
            #         ajo_user=ajo_user,
            #         amount=amount,
            #         from_wallet=from_wallet,
            #         onlending_plan=onlending_plan,
            #         onlending_wallet=onlending_wallet
            #     )
                
            # except Exception as err:
            #     data = {
            #         "status": "failed",
            #         "message": "Something went wrong with the transactions"
            #     }
            #     return Response(data, status=status.HTTP_400_BAD_REQUEST)

            # resp = {
            #     "status": "success",
            #     "messages": f"Successfully paid `{amount}` to recurrent onlending plan."
            # }
            # return Response(data=resp, status=status.HTTP_200_OK)
            transaction = OnlendingDynamicTransactions.dynamic_onlending_transactions_record(
                user=user,
                ajo_user=ajo_user,
                amount=amount,
                from_wallet=from_wallet,
                onlending_plan=onlending_plan,
                onlending_wallet=onlending_wallet
            )
            if transaction.get("status") is False:
                data = {
                    "status": "failed",
                    "message": transaction.get("message")
                }
            else:
                data = {
                    "status": "success",
                    "messages": f"Successfully paid `{amount}` to recurrent onlending plan."
                } 
            return Response(data, status=status.HTTP_200_OK)
        
        else:
            data = {
                "status": "failed",
                "message": get_serializer_key_error(serializer.errors)
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)



class OnlendingPlanDetailsAPIView(GenericAPIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = OnlendingPlanDetailsSerializer
    pagination_class = CustomPagination

    @swagger_auto_schema(
        responses={200: OnlendingPlanDetailsSerializer},
        manual_parameters=[
            openapi.Parameter(
                name="id",
                in_=openapi.IN_QUERY,
                description="the plan ID",
                required=True,
                type=openapi.TYPE_INTEGER,
            ),
            openapi.Parameter(
                name="phone_number",
                in_=openapi.IN_QUERY,
                description="the phone number of the Ajo User",
                required=False,
                type=openapi.TYPE_STRING,
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """
        Get the current plan details as well as its transaction history
        """
        plan_id = request.query_params.get("id")
        phone_number = request.query_params.get("phone_number", None)
        user = request.user

        ajo_user = None
        if phone_number:
            try:
                ajo_user = AjoUserSelector(
                    phone_number=phone_number,
                    user=user,
                ).get_ajo_user()

            except ValueError as err:
                return value_error_response(error=err)

        onlending_selector = OnlendingSelector(
            user=user,
            ajo_user=ajo_user,
        )

        try:
            onlending_plan = onlending_selector.get_onlending_instance(id=plan_id)
        except ValueError as err:
            return value_error_response(error=err)

        try:
            paginated_transactions = self.paginate_queryset(
                queryset=onlending_selector.get_transaction_history_for_onlending_plan(
                    quotation_id=onlending_plan.quotation_id,
                    descending_order=True,
                )
            )
        except NotFound:
            return pagination_page_not_found_response()

        serializer = OnlendingPlanDetailsSerializer(onlending_plan)
        transactions_serializer = OnlendingTransactionHistorySerializer(paginated_transactions, many=True)

        response = serializer.data
        response["transactions"] = transactions_serializer.data
        return self.get_paginated_response(response)
    

class OnlendingMissedPayments(GenericAPIView):
    permission_classes = (IsAuthenticated,)
    
    def get(self, request):
        user = request.user
        plan_id = request.query_params.get("id")
        phone_number = request.query_params.get("phone_number", None)
        
        ajo_user = None
        if phone_number:
            try:
                ajo_user = AjoUserSelector(
                    phone_number=phone_number,
                    user=user,
                ).get_ajo_user()

            except ValueError as err:
                return value_error_response(error=err)

        onlending_selector = OnlendingSelector(
            user=user,
            ajo_user=ajo_user,
        )

        try:
            onlending_plan = onlending_selector.get_onlending_instance(id=plan_id)
        except ValueError as err:
            return value_error_response(error=err)
        
        today = timezone.localtime()
        missed_payments = cal_missed_amount(today=today, onlending_plan=onlending_plan)
             
        resp = {
                "status": "success",
                "messages": "Successfully Retrieved Missed Durations",
                "details": missed_payments
        }
        return Response(data=resp, status=status.HTTP_200_OK)
    
        
class TotalBalanceAndSavingsAPIView(GenericAPIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = TotalBalanceSavingsSerializer

    def get(self, request, *args, **kwargs):
        """
        Get the total amount in the main onlending wallet, total saved in the all the plans
        associated with a user and the number of active plans
        """
        user = request.user
        onlending_selector = OnlendingSelector(user=user)
        access_token = request.headers.get("Authorization", "").split()[-1]

        try:
            UserService.update_user_fields_from_agency(
                user=user,
                access_token=access_token,
            )
        except ValueError as err:
            pass

        data = {
            "main_wallet_balance": onlending_selector.get_main_onlending_wallet().available_balance,
            "total_balance": onlending_selector.get_sum_saved_in_plans_associated_with_user(),
            "total_savings": onlending_selector.get_number_of_active_plans_associated_with_user(),
        }

        if user.user_type == UserType.DMO_AGENT:
            data["commissions_balance"] = onlending_selector.get_onlending_commission_wallet().available_balance
            data["referrals"] = user.referrals_count

        serializer = self.serializer_class(data)

        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )


class ListOnlendingPlansAPIView(GenericAPIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = ListOnlendingPlansSerializer
    pagination_class = CustomPagination

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                name="filter",
                in_=openapi.IN_QUERY,
                description="'active', 'mature', 'inactive' filters",
                required=False,
                type=openapi.TYPE_STRING,
            ),
            openapi.Parameter(
                name="phone_number",
                in_=openapi.IN_QUERY,
                description="phone number of ajo user",
                required=False,
                type=openapi.TYPE_STRING,
            ),
            openapi.Parameter(
                name="search",
                in_=openapi.IN_QUERY,
                description="search for string in plan name and ajo user",
                required=False,
                type=openapi.TYPE_STRING,
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        """
        Get the list of onlending plans associated to
        a user.
        """
        valid_filters = [
            "active",
            "mature",
            "inactive",
        ]

        base_filter = request.query_params.get("filter", None)
        phone_number = request.query_params.get("phone_number", None)
        search = request.query_params.get("search", None)
        user = request.user

        onlending_selector = OnlendingSelector(user=user)

        if base_filter:
            base_filter = base_filter.lower()

            if base_filter not in valid_filters:
                return Response(
                    {
                        "error": "603",
                        "status": False,
                        "message": f"the filter should be one of the ff: {valid_filters}",
                    },
                    status.HTTP_400_BAD_REQUEST,
                )

            if base_filter == "active":
                onlending_plans = onlending_selector.get_all_plans_associated_with_user(
                    is_active=True,
                ).filter(
                    maturity_date__gt=timezone.localdate(),
                )
            elif base_filter == "inactive":
                onlending_plans = onlending_selector.get_all_plans_associated_with_user(is_active=False)
            elif base_filter == "mature":
                onlending_plans = onlending_selector.get_all_mature_plans_associated_with_user()

        else:
            onlending_plans = onlending_selector.get_all_plans_associated_with_user()

        if phone_number:
            try:
                ajo_user = AjoUserSelector(
                    phone_number=phone_number,
                    user=user,
                ).get_ajo_user()
            except ValueError as err:
                return value_error_response(error=err)

            onlending_plans = onlending_plans.filter(ajo_user=ajo_user)

        if search:
            onlending_plans = onlending_plans.filter(
                Q(name__icontains=search)
                | Q(ajo_user__first_name__icontains=search)
                | Q(ajo_user__last_name__icontains=search)
            )

        try:
            paginated_plans = self.paginate_queryset(queryset=onlending_plans.order_by("-id"))
        except NotFound:
            return pagination_page_not_found_response()

        serializer = ListOnlendingPlansSerializer(paginated_plans, many=True)

        return self.get_paginated_response(serializer.data)


class AjoUserPlansAPIView(GenericAPIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = PlanSummarySerializer

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                name="filter",
                in_=openapi.IN_QUERY,
                description="'active', 'mature' or 'inactive'",
                required=False,
                type=openapi.TYPE_STRING,
            ),
            openapi.Parameter(
                name="phone_number",
                in_=openapi.IN_QUERY,
                description="phone number of ajo user",
                required=False,
                type=openapi.TYPE_STRING,
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        """
        Get the plans summary of each ajo user belonging to an agent
        """
        user = request.user
        valid_filters = [
            "active",
            "mature",
            "inactive",
        ]

        base_filter = request.query_params.get("filter", None)
        phone_number = request.query_params.get("phone_number", None)

        onlending_selector = OnlendingSelector(user=user)
        if base_filter:
            base_filter = base_filter.lower()
            if base_filter not in valid_filters:
                return Response(
                    {
                        "error": "603",
                        "status": False,
                        "message": f"the filter should be one of the ff: {valid_filters}",
                    },
                    status.HTTP_400_BAD_REQUEST,
                )

        ajo_user = None
        if phone_number:
            try:
                ajo_user = AjoUserSelector(
                    phone_number=phone_number,
                    user=user,
                ).get_ajo_user()
            except ValueError as err:
                return value_error_response(error=err)

        data = onlending_selector.get_ajo_user_plan_details_associated_with_user(filter=base_filter)
        data_list = [{"phone_number": key, **value} for key, value in data.items()]

        serializer = PlanSummarySerializer(data_list, many=True)

        return Response(
            data={
                "status": True,
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )


class RolloverOnlendingAPIView(GenericAPIView):
    serializer_class = RolloverOnlendingSerializer
    permission_classes = (IsAuthenticated,)

    def failed_response(
        self,
        message: str,
        code: str,
        error_message: Optional[str] = None,
    ) -> Response:
        data = {
            "status": False,
            "error": code,
            "message": message,
        }

        if error_message:
            data["error_message"] = error_message

        return Response(
            data=data,
            status=status.HTTP_400_BAD_REQUEST,
        )

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except ValidationError as err:
            return serializer_validation_error_response(err)

        user = request.user
        plan_id = serializer.validated_data.get("plan_id")
        duration = serializer.validated_data.get("duration")
        interest_type = serializer.validated_data.get("interest_type")
        rollover_interest = serializer.validated_data.get("rollover_interest", None)
        otp = serializer.validated_data.get("otp", None)

        try:
            onlending_plan = OnlendingSelector.get_onlending_plan_by_id(id=plan_id, user=user)
        except ValueError as err:
            return value_error_response(error=err)

        ajo_user = onlending_plan.ajo_user
        if ajo_user and not otp:
            return self.failed_response(
                message="OTP required in request data",
                code="422",
            )

        if settings.ENVIRONMENT != "development":
            if otp and ajo_user:
                verify_otp: bool = verify_sms_voice_otp(
                    otp=otp,
                    phone_number=onlending_plan.ajo_user.phone,
                ) or verify_ussd_otp(
                    otp=otp,
                    phone_number=onlending_plan.ajo_user.phone,
                )

                if not verify_otp:
                    return self.failed_response(
                        code="304",
                        message="invalid OTP, please try again",
                    )

        if ResourceCreationLimiting.is_resource_created_recently(user_id=user.id):
            return self.failed_response(code="429", message="wait a while before you continue to create a new plan")

        if onlending_plan.maturity_date > timezone.localdate():
            return self.failed_response(
                message="maturity date for onlending plan has not reached",
                code="400",
            )

        target = onlending_plan.target

        if onlending_plan.interest_type == InterestType.MATURITY_DATE:
            if rollover_interest is None:
                return self.failed_response(
                    message="rollover interest was not indicated",
                    code="454",
                )

            if rollover_interest:
                target = onlending_plan.amount_saved

        onlending_wallet = OnlendingSelector(
            user=onlending_plan.user,
            ajo_user=onlending_plan.ajo_user,
        ).get_onlending_wallet()

        if target > onlending_wallet.available_balance:
            return self.failed_response(
                message="please reach out to customer service to look at your plan",
                code="555",
            )

        details = OnlendingUtils.create_oneoff_plan_information(
            duration=duration,
            amount=target,
        )

        plan_data = {
            "name": OnlendingUtils.get_rollover_name(name=onlending_plan.name),
            "duration": duration,
            "target": target,
            "interest_type": interest_type,
            "user": user,
            "ajo_user": onlending_plan.ajo_user,
            "is_active": False,
            **details,
        }

        try:
            new_onlending_plan = OnlendingService.create_onlending_instance(data=plan_data)
            ResourceCreationLimiting.create_resource(user_id=user.id)
        except ValueError as err:
            return value_error_response(error=err, code="811")

        try:
            payment_actions.PlanPayments.rollover_operation(
                old_onlending_plan=onlending_plan,
                new_onlending_plan=new_onlending_plan,
                amount=target,
                request_data=json.dumps(request.data),
            )

            if ajo_user:
                interest_wallet = OnlendingSelector(
                    user=user,
                    ajo_user=ajo_user,
                ).get_main_onlending_wallet()

            if new_onlending_plan.interest_type == InterestType.UPFRONT:
                payment_actions.PlanPayments.pay_upfront_interest_into_wallet(
                    quotation_id=new_onlending_plan.quotation_id,
                    plan_type=payment_actions.PlanType.ONLENDING,
                    wallet=interest_wallet,
                )
            else:
                onlending_wallet.refresh_from_db()
                payment_actions.PlanPayments.pay_interest_into_plan(
                    plan_instance=new_onlending_plan,
                    wallet=onlending_wallet,
                )

            if onlending_plan.interest_type == InterestType.MATURITY_DATE:
                if not rollover_interest:
                    onlending_wallet.refresh_from_db()
                    payment_actions.PlanPayments.debit_wallet_and_pay_interest(
                        plan=onlending_plan,
                        from_wallet=onlending_wallet,
                        to_wallet=interest_wallet,
                    )

            onlending_plan.withdrawn = True
            onlending_plan.is_active = False
            onlending_plan.save()

            return Response(
                data={
                    "status": True,
                    "message": "successfully rolled over the plan",
                },
                status=status.HTTP_200_OK,
            )

        except Exception as err:
            return self.failed_response(
                message="encountered an error while attempting to rollover the plan",
                code="912",
                error_message=str(err),
            )


class EnrollmentLinkAPIView(GenericAPIView):
    serializer_classes = {"GET": GetEnrollmentLinkSerializer, "POST": SendEnrollmentLinkThroughSMSSerializer}
    permission_classes = (IsAuthenticated,)

    def get_serializer_class(self) -> Serializer:
        return self.serializer_classes.get(self.request.method)

    def get_referral_code(self) -> Response | str:
        data = AgencyBankingClass.get_user_referral_code(access_token=self.access_token)

        if not data.get("status"):
            return Response(
                data={"error": "919", "status": False, "message": "an error occurred", "error": data},
                status=status.HTTP_400_BAD_REQUEST,
            )

        referral_code = data.get("message", {}).get("code")
        if not referral_code:
            return Response(
                data={"error": "811", "status": False, "message": "error retrieving the referral code"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        return referral_code

    def get(self, request, *args, **kwargs):
        """
        Get the enrollment link for an agent
        """
        self.access_token = request.headers.get("Authorization", "").split()[-1]

        referral_code = self.get_referral_code()

        if isinstance(referral_code, Response):
            return referral_code

        data = {"status": True, "enrollment_link": EnrollmentLink.build_enrollment_link(referral_code=referral_code)}

        serializer = self.get_serializer_class()(data)

        return Response(
            data=serializer.data,
            status=status.HTTP_200_OK,
        )

    def post(self, request, *args, **kwargs):
        """
        Send the enrollment link to a phone number via sms
        """
        serializer = self.get_serializer_class()(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except ValidationError as err:
            return serializer_validation_error_response(error=err)

        self.access_token = request.headers.get("Authorization", "").split()[-1]

        referral_code = self.get_referral_code()

        if isinstance(referral_code, Response):
            return referral_code

        sms_status = EnrollmentLink.send_enrollment_link_sms(
            referral_code=referral_code,
            phone_number=serializer.validated_data.get("phone_number"),
            user=request.user,
        )

        if not sms_status:
            return Response(
                data={
                    "status": sms_status,
                    "error": "712",
                    "message": "an error occurred, try again in some minutes",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response(
            data={
                "status": True,
                "message": "message sent successfully",
            },
            status=status.HTTP_200_OK,
        )


class TransactionHistoryAPIView(GenericAPIView):
    serializer_class = OnlendingTransactionHistorySerializer
    permission_classes = (IsAuthenticated,)
    pagination_class = CustomPagination

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                name="phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="phone number of the ajo user",
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        """
        Retrieves the Transaction History of Onlending for a user
        or Ajo User
        """
        user = request.user
        phone_number: str | None = request.query_params.get("phone_number", None)

        ajo_user = None
        if phone_number:
            try:
                ajo_user = AjoUserSelector(
                    phone_number=phone_number,
                    user=user,
                ).get_ajo_user()
            except ValueError as err:
                return value_error_response(error=err)

        onlending_selector = OnlendingSelector(user=user, ajo_user=ajo_user)
        if not phone_number:
            transactions = onlending_selector.get_general_transaction_history_for_user()
        else:
            transactions = onlending_selector.get_onlending_transaction_history()

        try:
            paginated_transactions = self.paginate_queryset(transactions.order_by("-id"))
        except NotFound:
            return pagination_page_not_found_response()

        serializer = self.serializer_class(paginated_transactions, many=True)

        return self.get_paginated_response(serializer.data)


class OnlendingMainBankAccountAPIView(GenericAPIView):

    serializer_class = VirtualAccountDetailsSerializer
    permission_classes = (IsAuthenticated,)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="phone number of the ajo user",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """
        Get the Onlending Main Wallet Account Number
        """
        phone_number = request.query_params.get("phone_number")
        user = request.user

        if phone_number:
            # obtain the bank details for an ajo user onlending main

            ajo_user_selector = AjoUserSelector(phone_number=phone_number, user=user)

            try:
                ajo_user = ajo_user_selector.get_ajo_user()
            except ValueError as err:
                return value_error_response(error=err)

            bank_selector = BankAccountSelector(ajo_user=ajo_user)

        else:
            bank_selector = BankAccountSelector(user=user)

        bank_details = None
        try:
            if phone_number:
                bank_details = bank_selector.get_ajo_user_account_details(form_type=AccountFormType.ONLENDING_MAIN)
            else:
                bank_details = bank_selector.get_user_account_details(form_type=AccountFormType.ONLENDING_MAIN)

        except ValueError as err:
            if "onlending" in str(err):
                if phone_number:
                    if ajo_user.bvn and not ajo_user.dob:
                        set_dob = AjoUserService.set_dob_from_bvn(ajo_user=ajo_user)
                        if not set_dob.get("status"):
                            return Response(
                                {
                                    "status": False,
                                    "error": "452",
                                    "message": "cannot update the DOB from BVN, please try again later",
                                },
                                status.HTTP_400_BAD_REQUEST,
                            )

                    ajo_user.refresh_from_db()

                    data = {
                        "phone_number": ajo_user.phone_number,
                        "first_name": ajo_user.first_name,
                        "last_name": ajo_user.last_name,
                        "dob": ajo_user.dob,
                        "address": f"{ajo_user.address}, {ajo_user.lga}, {ajo_user.state}.",
                        "gender": ajo_user.gender,
                        "bvn": ajo_user.bvn,
                    }

                    onl_account_details = BankAccountService.create_account_for_ajo_user(
                        ajo_user=ajo_user,
                        acct_form_type=AccountFormType.ONLENDING_MAIN,
                        bvn_present=True if ajo_user.bvn else False,
                        data=data,
                    )

                    if onl_account_details.get("status"):
                        bank_details = onl_account_details.get("data")

                    else:
                        return Response(
                            {
                                "status": False,
                                "error": "507",
                                "message": "please contact customer care to help create your account",
                            },
                            status.HTTP_400_BAD_REQUEST,
                        )

                else:
                    onl_account_details = BankAccountService.create_agent_virtual_account(
                        user=user,
                        form_type=AccountFormType.ONLENDING_MAIN,
                        new=True,
                    )

                    if onl_account_details.get("status"):
                        bank_details = onl_account_details.get("data")

                    else:
                        return Response(
                            {
                                "status": False,
                                "error": "507",
                                "message": "please contact customer care to help create your account",
                            },
                            status.HTTP_400_BAD_REQUEST,
                        )

        serializer = self.serializer_class(bank_details)

        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status.HTTP_200_OK,
        )


class AjoUserBalancesSummaryAPIView(GenericAPIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = AjoUserBalancesSummarySerializer

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """
        Get the total amount in the main onlending wallet, total saved in the all the plans
        """
        user = request.user
        phone_number = request.query_params.get("phone_number")

        try:
            ajo_user = AjoUserSelector(
                phone_number=phone_number,
                user=user,
            ).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err)

        onlending_selector = OnlendingSelector(user=user, ajo_user=ajo_user)

        data = {
            "wallet_balance": onlending_selector.get_main_onlending_wallet().available_balance,
            "lending_balance": onlending_selector.get_amount_saved_in_onlending_plans(),
        }

        serializer = self.serializer_class(data)

        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )


class PendingLoanRequestsAPIView(GenericAPIView):
    serializer_classes = {
        "GET": PendingLoanRequestSerializer,
    }
    permission_classes = (IsAuthenticated,)

    def get_serializer_class(self):
        return self.serializer_classes.get(self.request.method)

    def get(self, request, *args, **kwargs):
        """
        Get all the pending/processing loan requests
        """

        loan_requests = LibertyUSSD.get_processing_loans()

        if not loan_requests.get("status"):
            return Response(
                {
                    "status": False,
                    "error": "120",
                    "message": "there was an issue obtaining the pending loans",
                    "error_message": loan_requests,
                },
                status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.get_serializer_class()(loan_requests.get("data"), many=True)

        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status.HTTP_200_OK,
        )
