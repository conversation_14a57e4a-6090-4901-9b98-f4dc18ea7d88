import re
from typing import Any, Dict
from datetime import datetime

from django.contrib.auth import get_user_model
from django.core.validators import MaxV<PERSON>ueValidator, MinValueValidator, RegexValidator
from rest_framework import serializers
from django.utils import timezone

from accounts.model_choices import UserType
from accounts.models import ConstantTable
from ajo.payment_actions import check_if_agent_can_pay
from ajo.selectors import AjoAgentSelector, AjoUserSelector
from ajo.serializers.ajo_serializers import VirtualAccountDetailsSerializer
from chestlock.selectors import OnlendingSelector
from payment.checks import verify_transaction_pin
from payment.model_choices import WalletTypes

from ..model_choices import InterestType, OnlendingType
from ..models import AjoUser, Onlending
from ..utils import LoanRequests, LoanRequestsManager
from .serializers import Frequency, Transaction, pattern


class PhoneNumberField(serializers.CharField):
    def __init__(self, **kwargs):
        super().__init__(
            max_length=11,
            validators=[
                RegexValidator(
                    regex=r"^\d{11}$",
                    message="Phone number must be 11 digits",
                    code="invalid_phone_number",
                )
            ],
            **kwargs,
        )


class GenerateOnlendingPlanDetailsSerializer(serializers.Serializer):
    """
    Collects the name, duration and target from the user
    """

    name = serializers.CharField(min_length=2)
    duration = serializers.IntegerField(min_value=0)
    target = serializers.FloatField(min_value=0.00)
    phone_number = PhoneNumberField(allow_null=True, required=False)
    onlending_type = serializers.CharField()

    def validate(self, attrs):
        name = attrs.get("name", "")
        target = attrs.get("target", "")
        onlending_type = attrs.get("onlending_type")

        constants = ConstantTable.get_constant_table_instance()

        if not re.match(pattern=pattern, string=name):
            raise serializers.ValidationError(
                "the plan name should start with a letter and can have numbers and spaces"
            )

        if target < constants.onlending_minimum_target:
            raise serializers.ValidationError(f"minimum target of {constants.onlending_minimum_target} is required")
        
        if onlending_type.upper() not in OnlendingType:
            raise serializers.ValidationError(f"Choose one from the list: {list(OnlendingType.__members__.keys())}")
        else:
            attrs["onlending_type"] = getattr(OnlendingType, onlending_type.upper())

        return attrs


class CreateOnlendingPlanSerializer(serializers.ModelSerializer):
    name = serializers.CharField(min_length=2)
    phone_number = PhoneNumberField(allow_null=True, required=False)
    duration = serializers.IntegerField(min_value=0)
    target = serializers.FloatField(min_value=0.00)
    interest_type = serializers.CharField()
    onlending_type = serializers.CharField(required=True)
    frequency = serializers.CharField(required=False)
    automate_payments = serializers.BooleanField(required=False)
    
    # frequency = serializers.CharField(min_length=5)
    # hour = serializers.IntegerField(min_value=0, max_value=23)

    class Meta:
        model = Onlending
        fields = (
            "id",
            "name",
            "phone_number",
            "target",
            "duration",
            "interest_type",
            "onlending_type",
            "frequency",
            "hour",
            "automate_payments"
        )

    def validate(self, attrs):
        name = attrs.get("name", "")
        target = attrs.get("target", "")
        interest_type = attrs.get("interest_type", "")
        onlending_type = attrs.get("onlending_type", "")
        frequency = attrs.get("frequency", None)
        automate_payments = attrs.get("automate_payments", None)

        if not re.match(pattern=pattern, string=name):
            raise serializers.ValidationError(
                "the plan name should start with a letter and can have numbers and spaces"
            )

        constants = ConstantTable.get_constant_table_instance()
        if target < constants.onlending_minimum_target:
            raise serializers.ValidationError(f"minimum target of {constants.onlending_minimum_target} is required")

        if interest_type.upper() not in InterestType:
            raise serializers.ValidationError(f"choose one from the list: {list(InterestType.__members__.keys())}")
        else:
            attrs["interest_type"] = getattr(InterestType, interest_type.upper())

        if onlending_type.upper() == OnlendingType.RECURRENT:
            if frequency is None:
                raise serializers.ValidationError("'frequency' field is required")
            
            elif frequency.upper() not in [item for item in Frequency.values][:2]:
                raise serializers.ValidationError(f"Choose one from the list: {[item for item in Frequency.values][:2]}")
            else:
                attrs["frequency"] = getattr(Frequency, frequency.upper())

        if automate_payments is not None and automate_payments is True:
            attrs["automate_payments"] = True
        else:
            attrs["automate_payments"] = False

        attrs["onlending_type"] = onlending_type.upper()

        return attrs


class EditOnlendingPlanSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()
    target = serializers.FloatField()
    duration = serializers.IntegerField()
    interest_type = serializers.CharField()

    class Meta:
        model = Onlending
        fields = (
            "id",
            "target",
            "duration",
            "interest_type",
        )

    def validate(self, attrs):
        target = attrs.get("target", "")
        interest_type = attrs.get("interest_type", "")
        constants = ConstantTable.get_constant_table_instance()

        if target < constants.chestlock_minimum_target:
            raise serializers.ValidationError(f"minimum target of {constants.chestlock_minimum_target} is required")

        if interest_type.upper() not in InterestType:
            raise serializers.ValidationError(f"choose one from the list: {list(InterestType.__members__.keys())}")
        else:
            attrs["interest_type"] = getattr(InterestType, interest_type.upper())

        return attrs


class OnlendingSerializer(serializers.ModelSerializer):

    class Meta:
        model = Onlending
        fields = [
            "id",
            "name",
            "duration",
            "target",
            "quotation_id",
            "maturity_date",
            "interest_type",
        ]


class OnlendingPlanDetailsSerializer(serializers.ModelSerializer):

    loan_requests = serializers.SerializerMethodField()

    def get_loan_requests(self, obj: Onlending) -> Dict[str, Any]:
        return PendingLoanRequestSerializer(
            LoanRequestsManager(obj).get_existing_plan_loan_requests(),
            many=True,
        ).data

    class Meta:
        model = Onlending
        exclude = [
            "plan_balance_before",
            "plan_balance_after",
            "email_sent",
            "created_at",
            "updated_at",
            "recurrent_saving_status",
            "payment_method",
        ]


class OnlendingTransactionHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        fields = [
            "id",
            "status",
            "amount",
            "date_created",
            "transaction_id",
            "description",
            "quotation_id",
            "transaction_form_type",
            "transaction_date_completed",
            "transaction_type",
            "wallet_type",
            "wallet_balance_before",
            "wallet_balance_after",
        ]


class TotalBalanceSavingsSerializer(serializers.Serializer):
    main_wallet_balance = serializers.FloatField()
    total_balance = serializers.FloatField()
    total_savings = serializers.IntegerField()
    commissions_balance = serializers.FloatField(allow_null=True, required=False)
    referrals = serializers.IntegerField(allow_null=True, required=False)


class OnboardedUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = AjoUser
        fields = [
            "first_name",
            "last_name",
            "phone_number",
        ]


class OnlendingUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = get_user_model()
        fields = [
            "email",
        ]


class ListOnlendingPlansSerializer(OnlendingPlanDetailsSerializer):
    ajo_user = OnboardedUserSerializer()
    user = OnlendingUserSerializer()

    class Meta(OnlendingPlanDetailsSerializer.Meta):
        pass


class PlanSummarySerializer(serializers.Serializer):
    plans_count = serializers.IntegerField()
    total_amount_saved = serializers.FloatField()
    ajo_user = OnboardedUserSerializer()


class GroupedPlansSerializer(serializers.Serializer):
    phone_number = serializers.CharField()
    details = PlanSummarySerializer()


class RolloverOnlendingSerializer(serializers.Serializer):
    plan_id = serializers.IntegerField()
    duration = serializers.IntegerField(min_value=0)
    interest_type = serializers.CharField()
    rollover_interest = serializers.BooleanField(required=False)
    otp = serializers.CharField(required=False, allow_null=True)

    def validate(self, attrs):
        interest_type = attrs.get("interest_type", "")
        otp = attrs.get("otp", "")

        if interest_type.upper() not in InterestType:
            raise serializers.ValidationError(f"choose one from the list: {list(InterestType.__members__.keys())}")
        else:
            attrs["interest_type"] = getattr(InterestType, interest_type.upper())

        if otp:
            if not otp.isdigit():
                raise serializers.ValidationError("OTP field must be digits only")

        return attrs


class GetEnrollmentLinkSerializer(serializers.Serializer):
    status = serializers.BooleanField()
    enrollment_link = serializers.URLField()


class SendEnrollmentLinkThroughSMSSerializer(serializers.Serializer):
    phone_number = PhoneNumberField()


class InterestsRatesInRangeSerializer(serializers.Serializer):
    """Use this for the POST request to ask for the interest rates for days in a range"""

    min_days = serializers.IntegerField(
        validators=[
            MinValueValidator(limit_value=90, message="minimum number of days should not be less than 90"),
        ]
    )
    max_days = serializers.IntegerField(
        validators=[
            MaxValueValidator(limit_value=365, message="maximum number of days should not be more than 365"),
        ]
    )

    def validate(self, attrs):
        super().validate(attrs)
        user = self.context.get("user")
        min_days = attrs.get("min_days")
        if not user:
            raise serializers.ValidationError("no user passed in context")

        if not user.is_authenticated:
            user_type = UserType.STAFF_AGENT
        else:
            user_type = user.user_type

        if user_type in [UserType.DMO_AGENT, UserType.STAFF_AGENT]:
            if min_days < 180:
                # Maybe reduce this to allow reducing the minimum days request
                raise serializers.ValidationError("please input 180 or more")

        return attrs


class AjoUserBalancesSummarySerializer(serializers.Serializer):
    wallet_balance = serializers.FloatField()
    lending_balance = serializers.FloatField()


class PendingLoanRequestSerializer(serializers.Serializer):
    loan_amount = serializers.FloatField()
    status = serializers.CharField()
    borrowers_name = serializers.CharField()
    borrowers_ministry = serializers.CharField()
    request_date = serializers.DateTimeField()
    loan_duration = serializers.IntegerField(help_text="loan duration in months")


class MakeManualPaymentSerializer(serializers.Serializer):
    # ajo_user_phone = serializers.CharField(max_length=16)
    pin = serializers.CharField(min_length=4, max_length=4)
    amount = serializers.FloatField()
    plan_id = serializers.CharField()
    wallet_type = serializers.CharField()   

    def validate(self, attrs):
        request = self.context.get("request")
        user = request.user
        wallet_type = attrs.get("wallet_type")
        plan_id = attrs.get("plan_id")
        amount = attrs.get("amount")
        valid_wallet_types = [
            "AJO_AGENT",
            "AJO_SPENDING",
            "ONLENDING_MAIN",
        ]
        
        try:
            onlending_plan = OnlendingSelector.get_onlending_plan_by_id(id=plan_id, user=user)
    
            if not onlending_plan.is_active and onlending_plan.is_activated:
                raise serializers.ValidationError("Plan not active")
            if onlending_plan.onlending_type != "RECURRENT":
                raise serializers.ValidationError("Action only available for recurrent plans")
        except ValueError as err:
            raise serializers.ValidationError(err)
        
        if onlending_plan.maturity_date <= timezone.localdate():
            raise serializers.ValidationError("Plan already past maturity date.")
        
        if wallet_type not in valid_wallet_types:
            raise serializers.ValidationError("Unknown wallet type.")
    
        access_token = request.headers.get("Authorization", "").partition(" ")[-1]
        agent_transaction_pin = attrs.get("pin")

        verify_agent_pin = verify_transaction_pin(
            transaction_pin=agent_transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )
        
        if verify_agent_pin is True:
            pass
        else:
            raise serializers.ValidationError("Incorrect transaction pin")
        ajo_user = onlending_plan.ajo_user
        if wallet_type == WalletTypes.AJO_SPENDING:
            funding_wallet = AjoUserSelector(
                    ajo_user=ajo_user
                ).get_spending_wallet()
            
        elif wallet_type == WalletTypes.ONLENDING_MAIN:
                funding_wallet = OnlendingSelector(
                    user=user, ajo_user=ajo_user
                ).get_main_onlending_wallet()

        elif wallet_type == WalletTypes.AJO_AGENT:
            funding_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()

        if not funding_wallet:
            raise serializers.ValidationError("Unable to retrieve specified wallet. Please check the wallet exist.")
        
        if funding_wallet.available_balance < amount:
            raise serializers.ValidationError("Insufficient wallet balance.")

        onlending_wallet = OnlendingSelector(user=user, ajo_user=ajo_user).get_onlending_wallet()
       
        attrs["onlending_plan"] = onlending_plan
        attrs["from_wallet"] = funding_wallet
        attrs["ajo_user"] = ajo_user
        attrs["onlending_wallet"] = onlending_wallet
        
        return attrs

    


    