from django.utils import timezone

import re

from django.core.validators import MaxV<PERSON>ueValidator, MinValueValidator
from rest_framework import serializers

from accounts.models import ConstantTable
from payment.checks import check_if_chestlock_and_if_oneoff
from payment.models import Transaction

from ..model_choices import ChestlockType, Frequency
from ..models import ChestLock

pattern = r"^[a-zA-Z][a-zA-Z0-9 ]*$"


class ChestlockSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChestLock
        fields = [
            "id",
            "plan",
            "duration",
            "target",
            "quotation_id",
        ]


class GeneratePlanDetailsSerializer(serializers.Serializer):
    """Collects the Plan, Duration and Target from the user"""

    plan = serializers.CharField(min_length=2)
    duration = serializers.IntegerField(
        validators=[
            MinValueValidator(limit_value=0, message="duration should be greater than 0"),
        ]
    )
    target = serializers.FloatField()
    chestlock_type = serializers.CharField(min_length=5)

    def validate(self, attrs):
        plan = attrs.get("plan", "")
        target = attrs.get("target", "")
        chestlock_type = attrs.get("chestlock_type", "")

        constants = ConstantTable.get_constant_table_instance()

        if not re.match(pattern=pattern, string=plan):
            raise serializers.ValidationError(
                "the plan name should start with a letter and can have numbers and spaces"
            )

        if target < constants.chestlock_minimum_target:
            raise serializers.ValidationError(f"minimum target of {constants.chestlock_minimum_target} is required")

        if chestlock_type.upper() not in ChestlockType:
            raise serializers.ValidationError(f"Choose one from the list: {list(ChestlockType.__members__.keys())}")
        else:
            attrs["chestlock_type"] = getattr(ChestlockType, chestlock_type.upper())

        return attrs


class LargeDaysRangeInterestSerializer(serializers.Serializer):
    """For the interest rates for ranges e.g. 10 - 30: ~6%"""

    min_days = serializers.IntegerField()
    max_days = serializers.IntegerField()
    rate = serializers.CharField()


class AskForInterestsRatesInRangeSerializer(serializers.Serializer):
    """Use this for the POST request to ask for the interest rates for days in a range"""

    min_days = serializers.IntegerField(
        validators=[
            MinValueValidator(limit_value=90, message="minimum number of days should not be less than 90"),
        ]
    )
    max_days = serializers.IntegerField(
        validators=[
            MaxValueValidator(limit_value=365, message="maximum number of days should not be more than 365"),
        ]
    )


class DaysMagnitudeInterestRateSerializer(serializers.Serializer):
    """Use this to serialize the output for the Days-Rate e.g. 35 days - 5%"""

    days = serializers.IntegerField()
    rate = serializers.FloatField()


# Serializer that creates the chestlock instance
class CreateChestlockPlanSerializer(serializers.ModelSerializer):
    plan = serializers.CharField(min_length=2, required=True)
    duration = serializers.IntegerField(
        validators=[
            MinValueValidator(limit_value=0, message="duration should be greater than 0"),
        ],
        required=True,
    )
    target = serializers.FloatField(required=True)
    frequency = serializers.CharField(min_length=5, required=False)
    hour = serializers.IntegerField(
        validators=[
            MinValueValidator(limit_value=0, message="hour should not be less than 0"),
            MaxValueValidator(limit_value=23, message="hour should not be more than 23"),
        ],
        required=False,
    )
    chestlock_type = serializers.CharField(min_length=6, required=True)

    class Meta:
        model = ChestLock
        fields = (
            "id",
            "plan",
            "target",
            "duration",
            "frequency",
            "hour",
            "chestlock_type",
        )

    def validate(self, attrs):
        plan = attrs.get("plan", "")
        target = attrs.get("target", "")
        chestlock_type = attrs.get("chestlock_type", "")
        frequency = attrs.get("frequency", "")
        hour = attrs.get("hour", "")

        if chestlock_type.upper() not in ChestlockType:
            raise serializers.ValidationError(f"Choose one from the list: {list(ChestlockType.__members__.keys())}")
        else:
            chestlock_type = getattr(ChestlockType, chestlock_type.upper())
            attrs["chestlock_type"] = chestlock_type

        if not re.match(pattern=pattern, string=plan):
            raise serializers.ValidationError(
                "the plan name should start with a letter and can have numbers and spaces"
            )

        constants = ConstantTable.get_constant_table_instance()
        if target < constants.chestlock_minimum_target:
            raise serializers.ValidationError(f"minimum target of {constants.chestlock_minimum_target} is required")

        if chestlock_type is ChestlockType.RECURRENT:
            if not frequency:
                raise serializers.ValidationError("'frequency' field is required")
            elif frequency.upper() not in Frequency:
                raise serializers.ValidationError(f"Choose one from the list: {list(Frequency.__members__.keys())}")
            else:
                attrs["frequency"] = getattr(Frequency, frequency.upper())

            if not hour:
                raise serializers.ValidationError("'hour' field is required")

        elif chestlock_type is ChestlockType.ONEOFF:
            if hour or frequency:
                raise serializers.ValidationError("'hour' and 'frequency' fields should not be present")

        return attrs


class CurrentPlanDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChestLock
        fields = [
            "id",
            "plan",
            "amount_saved",
            "target",
            "duration",
            "maturity_date",
            "periodic_amount",
            "frequency",
            "hour",
            "is_activated",
            "completed",
            "is_active",
            "recurrent_saving_status",
            "chestlock_type",
        ]


class ListOfAllChestlockSavingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChestLock
        fields = [
            "id",
            "plan",
            "target",
            "amount_saved",
            "target",
            "periodic_amount",
            "duration",
            "maturity_date",
            "is_activated",
            "completed",
            "is_active",
            "recurrent_saving_status",
            "chestlock_type",
        ]


class RetrieveChestlockInstanceSerializer(serializers.Serializer):
    id = serializers.IntegerField()


class TransactionHistorySerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()
    amount = serializers.FloatField()

    class Meta:
        model = Transaction
        fields = [
            "id",
            "amount",
            "transaction_id",
            "transaction_form_type",
            "status",
            "date_created",
            "transaction_date_completed",
        ]


class TotalBalanceAndSavingsSerializer(serializers.Serializer):
    total_balance = serializers.FloatField()
    savings = serializers.IntegerField()


class TotalBalanceSavingsListOfAllChestlockSavingsSerializer(serializers.ModelSerializer):
    total_balance = serializers.FloatField()
    savings = serializers.IntegerField()

    class Meta:
        model = ChestLock
        fields = [
            "total_balance",
            "savings",
            "id",
            "plan",
            "target",
            "amount_saved",
            "duration",
            "maturity_date",
        ]

class MakeChestlockPaymentSerializer(serializers.Serializer):
    amount = serializers.FloatField()
    plan_id = serializers.IntegerField()
    transaction_pin = serializers.CharField(min_length=4, max_length=4)
    
    def validate(self, attrs):
        plan_id = attrs.get("plan_id")
        try:
            chestlock_plan = ChestLock.objects.get(id=plan_id)
        except ChestLock.DoesNotExist:  
            raise serializers.ValidationError("The plan does not exist")
        
        if chestlock_plan.chestlock_type == ChestlockType.ONEOFF:
            raise serializers.ValidationError("You cannot make manual payments for a one-off plan")
        
        if chestlock_plan.is_activated == False:
            raise serializers.ValidationError("The plan is not activated yet")
        
        if chestlock_plan.is_active == False:
            raise serializers.ValidationError("The plan is not active, contact support") 
        
        if chestlock_plan.completed == True:
            raise serializers.ValidationError("The plan is marked as completed")
        
        if chestlock_plan.maturity_date <= timezone.localdate():
            raise serializers.ValidationError("The plan has passed its maturity date")
        
        attrs["chestlock_plan"] = chestlock_plan
        return attrs