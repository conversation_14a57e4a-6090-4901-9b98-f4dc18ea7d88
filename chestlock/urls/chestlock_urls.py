from django.urls import path

from ..views.views import (  # FrequencyForSavingAPIView,
    CreateChestLockPlanAPIView,
    CurrentPlanDetailsAPIView,
    GeneratePlanDetailsAPIView,
    InterestRatesForDayRangesAPIView,
    TotalBalanceAndSavingsAPIView,
    TransactionHistoryAPIView,
    ManualCheslockPayments,
)

urlpatterns = [
    # URL collects the plan, duration and target
    path("generate-plan-details/", GeneratePlanDetailsAPIView.as_view(), name="plan_details"),
    # URL to get the interest rates for ONEOFF chestlock
    path("interest-for-ranges/", InterestRatesForDayRangesAPIView.as_view(), name="interest_rates_for_ranges"),
    # URL to submit the chestlock details
    path("initiate-chestlock-plan/", CreateChestLockPlanAPIView.as_view(), name="chestlock_details"),
    # URL to obtain the current plan details of a chestlock instance
    path("current-plan-details/", CurrentPlanDetailsAPIView.as_view(), name="current_plan_details"),
    # URL to get the transaction history of a plan
    path("transaction-history/", TransactionHistoryAPIView.as_view(), name="transaction_history"),
    # URL to get total balance and savings of the chestlock and to list all the chestlock plans a user has
    path("total-list-chestlock/", TotalBalanceAndSavingsAPIView.as_view(), name="total_balance_savings"),
    # URL to manually pay for a chestlock plan
    path("manual-chestlock-payment/", ManualCheslockPayments.as_view(), name="manual_chestlock_payment"),
]
