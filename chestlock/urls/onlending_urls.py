from django.urls import path

from ..views.onlending_views import (
    AjoUserBalancesSummaryAPIView,
    AjoUserPlansAPIView,
    CreateEditOnlendingPlanAPIView,
    EnrollmentLinkAPIView,
    GenerateOnlendingPlanDetailsAPIView,
    InterestRatesForDayRangesAPIView,
    ListOnlendingPlansAPIView,
    OnlendingMainBankAccountAPIView,
    OnlendingPlanDetailsAPIView,
    PendingLoanRequestsAPIView,
    RolloverOnlendingAPIView,
    TotalBalanceAndSavingsAPIView,
    TransactionHistoryAPIView,
    MakeManualPayments,
    OnlendingMissedPayments,
)

urlpatterns = [
    path("interest-for-ranges/", InterestRatesForDayRangesAPIView.as_view(), name="interest_rates_for_ranges"),
    path("generate-plan-details/", GenerateOnlendingPlanDetailsAPIView.as_view(), name="plan-details"),
    path("create-edit-plan/", CreateEditOnlendingPlanAPIView.as_view(), name="create-edit-onlending-plan"),
    path("plan-details/", OnlendingPlanDetailsAPIView.as_view(), name="onlending-plan-details"),
    path("missed-payments/", OnlendingMissedPayments.as_view(), name="missed_payments"),
    path("summary/", TotalBalanceAndSavingsAPIView.as_view(), name="total-balance-summary"),
    path("ajo-user-balances/", AjoUserBalancesSummaryAPIView.as_view(), name="ajo-user-balance-summary"),
    path("plans/", ListOnlendingPlansAPIView.as_view(), name="list-of-onlending-plans"),
    path("plans/ajo-users/summary/", AjoUserPlansAPIView.as_view(), name="ajo-users-plans"),
    path("rollover-plan/", RolloverOnlendingAPIView.as_view(), name="rollover-plan"),
    path("enrollment-link/", EnrollmentLinkAPIView.as_view(), name="enrollment-link"),
    path("transaction-history/", TransactionHistoryAPIView.as_view(), name="transaction-history"),
    path("bank-account/", OnlendingMainBankAccountAPIView.as_view(), name="onlending-main-bank-account"),
    path("loan-requests/", PendingLoanRequestsAPIView.as_view(), name="pending-loan-requests"),
    path("make-payments/", MakeManualPayments.as_view(), name="make_payments")
]
