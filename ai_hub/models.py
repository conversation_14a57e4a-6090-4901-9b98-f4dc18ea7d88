# ai_hub/models.py
from django.db import models
from django.conf import settings
from django.utils import timezone

class TokenUsageLog(models.Model):
    """
    Model to track token usage data from AI assistant interactions.
    Records prompt and completion tokens along with usage metadata.
    """
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='token_usage_logs',
        help_text="The loan officer/agent who made the request"
    )
    
    # Token usage counts
    prompt_tokens = models.PositiveIntegerField(
        default=0,
        help_text="Number of tokens used in the prompt"
    )
    completion_tokens = models.PositiveIntegerField(
        default=0,
        help_text="Number of tokens used in the completion/response"
    )
    total_tokens = models.PositiveIntegerField(
        default=0,
        help_text="Total number of tokens used in the interaction"
    )
    
    # Request metadata
    request_message = models.TextField(
        blank=True, 
        null=True,
        help_text="The message sent by the loan officer (truncated if necessary)"
    )
    response_summary = models.TextField(
        blank=True,
        null=True,
        help_text="Brief summary or first part of the response"
    )
    
    # Request context
    session_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="Optional conversation session identifier"
    )
    client_ip = models.GenericIPAddressField(
        blank=True,
        null=True,
        help_text="IP address of the client making the request"
    )
    
    # Model and pricing data
    model_name = models.CharField(
        max_length=50,
        default="gpt-4-turbo",
        help_text="The AI model used for this interaction"
    )
    estimated_cost_usd = models.DecimalField(
        max_digits=10,
        decimal_places=6,
        default=0,
        help_text="Estimated cost in USD based on token usage"
    )
    
    # Timing information
    request_time = models.DateTimeField(
        default=timezone.now,
        help_text="When the request was made"
    )
    response_time = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When the response was completed"
    )
    processing_time_ms = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="Processing time in milliseconds"
    )
    
    # Success tracking
    is_successful = models.BooleanField(
        default=True,
        help_text="Whether the request was processed successfully"
    )
    error_message = models.TextField(
        blank=True,
        null=True,
        help_text="Error message if the request failed"
    )
    
    class Meta:
        verbose_name = "Token Usage Log"
        verbose_name_plural = "Token Usage Logs"
        ordering = ['-request_time']
        indexes = [
            models.Index(fields=['user', 'request_time']),
            models.Index(fields=['model_name']),
            models.Index(fields=['is_successful']),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.total_tokens} tokens - {self.request_time.strftime('%Y-%m-%d %H:%M')}"
    
    def save(self, *args, **kwargs):
        # Ensure total_tokens is the sum of prompt and completion tokens
        if not self.total_tokens:
            self.total_tokens = self.prompt_tokens + self.completion_tokens
            
        # Calculate cost if not set (using approximate rates)
        if not self.estimated_cost_usd and self.total_tokens > 0:
            if self.model_name == 'gpt-4-turbo':
                # Approximate rates for GPT-4 Turbo
                prompt_cost_per_token = 0.00001  # $0.01 per 1K tokens
                completion_cost_per_token = 0.00003  # $0.03 per 1K tokens
            else:
                # Default rates for other models
                prompt_cost_per_token = 0.000005  # $0.005 per 1K tokens
                completion_cost_per_token = 0.000015  # $0.015 per 1K tokens
                
            self.estimated_cost_usd = (
                self.prompt_tokens * prompt_cost_per_token +
                self.completion_tokens * completion_cost_per_token
            )
            
        # Calculate processing time if response_time is set but processing_time_ms is not
        if self.response_time and not self.processing_time_ms:
            time_diff = self.response_time - self.request_time
            self.processing_time_ms = int(time_diff.total_seconds() * 1000)
            
        # Truncate message and response summary if too long
        if self.request_message and len(self.request_message) > 1000:
            self.request_message = self.request_message[:997] + "..."
            
        if self.response_summary and len(self.response_summary) > 1000:
            self.response_summary = self.response_summary[:997] + "..."
            
        super().save(*args, **kwargs)
    
    @classmethod
    def log_usage(cls, user, usage_data, request_message=None, response=None, session_id=None, 
                 client_ip=None, model_name="gpt-4-turbo", is_successful=True, error_message=None):
        """
        Helper method to create a token usage log entry.
        
        Args:
            user: The user who made the request
            usage_data: Dict containing prompt_tokens, completion_tokens, and total_tokens
            request_message: The message sent by the user
            response: The response from the AI assistant
            session_id: Optional conversation session ID
            client_ip: IP address of the client making the request
            model_name: The AI model used
            is_successful: Whether the request was processed successfully
            error_message: Error message if the request failed
            
        Returns:
            The created TokenUsageLog instance
        """
        response_time = timezone.now()
        
        # Create a new log entry
        log_entry = cls(
            user=user,
            prompt_tokens=usage_data.get('prompt_tokens', 0),
            completion_tokens=usage_data.get('completion_tokens', 0),
            total_tokens=usage_data.get('total_tokens', 0),
            request_message=request_message,
            response_summary=response[:500] if response else None,  # Store first 500 chars
            session_id=session_id,
            client_ip=client_ip,
            model_name=model_name,
            response_time=response_time,
            is_successful=is_successful,
            error_message=error_message
        )
        
        log_entry.save()
        return log_entry
    

# ai_hub/models.py
# Add these models to your existing models.py file

class Conversation(models.Model):
    """
    Model to track conversations between loan officers and AI assistant.
    """
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='conversations',
        help_text="The loan officer/agent who initiated the conversation"
    )
    
    created_at = models.DateTimeField(
        default=timezone.now,
        help_text="When the conversation was created"
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        help_text="When the conversation was last updated"
    )
    
    session_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="Optional conversation session identifier"
    )
    
    class Meta:
        verbose_name = "Conversation"
        verbose_name_plural = "Conversations"
        ordering = ['-updated_at']
        indexes = [
            models.Index(fields=['user', 'updated_at']),
            models.Index(fields=['session_id']),
        ]
    
    def __str__(self):
        return f"Conversation {self.id} - {self.user.email} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"
    
    def save(self, *args, **kwargs):
        """Override save to generate a session_id if not provided."""
        if not self.session_id:
            import uuid
            self.session_id = str(uuid.uuid4())
        super().save(*args, **kwargs)
        
    def trim_messages(self, max_messages=50):
        """
        Keeps only the most recent messages (up to max_messages).
        Deletes oldest messages if the count exceeds max_messages.
        """
        messages = self.messages.order_by('-created_at')
        if messages.count() > max_messages:
            # Get the oldest messages to delete
            messages_to_delete = messages[max_messages:]
            # Delete them
            Message.objects.filter(id__in=[m.id for m in messages_to_delete]).delete()


class Message(models.Model):
    """
    Model to store individual messages within a conversation.
    """
    ROLE_CHOICES = (
        ('user', 'User'),
        ('assistant', 'Assistant'),
    )
    
    conversation = models.ForeignKey(
        Conversation,
        on_delete=models.CASCADE,
        related_name='messages',
        help_text="The conversation this message belongs to"
    )
    
    role = models.CharField(
        max_length=10,
        choices=ROLE_CHOICES,
        help_text="Whether this message is from the user or assistant"
    )
    
    content = models.TextField(
        help_text="The content of the message"
    )
    
    created_at = models.DateTimeField(
        default=timezone.now,
        help_text="When the message was created"
    )
    
    token_usage = models.PositiveIntegerField(
        null=True, 
        blank=True,
        help_text="Tokens used for this message if it's from the assistant"
    )
    
    class Meta:
        verbose_name = "Message"
        verbose_name_plural = "Messages"
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['conversation', 'created_at']),
        ]
    
    def __str__(self):
        return f"Message {self.id} - {self.role} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"


class PredefinedQuestion(models.Model):
    """
    Model to store predefined questions that loan officers can easily ask.
    """
    question = models.CharField(
        max_length=255,
        help_text="The predefined question text"
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        help_text="Optional description of what this question does"
    )
    
    order = models.PositiveIntegerField(
        default=0,
        help_text="Display order for the question"
    )
    
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this question is currently active"
    )
    
    class Meta:
        verbose_name = "Predefined Question"
        verbose_name_plural = "Predefined Questions"
        ordering = ['order', 'question']
    
    def __str__(self):
        return self.question