# loans/services/agent_service.py


from ai_hub.agent_templates import LoanAgentTemplates
from ai_hub.services.agent_queries import LoanAgentQueries


class LoanAgentService:
    """
    Service layer that connects the AI agent to the database.
    This class provides methods that the agent can call to retrieve
    loan information and generate structured responses.
    """
    
    @staticmethod
    def handle_missed_loans_query(agent_id):
        """
        Handles queries about missed loans.
        
        Args:
            agent_id: The ID of the loan officer/agent
            
        Returns:
            Formatted response about missed loans
        """
        data = LoanAgentQueries.get_missed_loans(agent_id)
        return data
    
    @staticmethod
    def handle_past_maturity_query(agent_id):
        """
        Handles queries about past maturity loans.
        
        Args:
            agent_id: The ID of the loan officer/agent
            
        Returns:
            Formatted response about past maturity loans
        """
        data = LoanAgentQueries.get_past_maturity_summary(agent_id)
        return LoanAgentTemplates.past_maturity_response(data)
    
    @staticmethod
    def handle_borrowers_followup_query(agent_id):
        """
        Handles queries about which borrowers need follow-up.
        
        Args:
            agent_id: The ID of the loan officer/agent
            
        Returns:
            Formatted response with prioritized borrowers
        """
        data = LoanAgentQueries.get_borrowers_needing_followup(agent_id)
        return LoanAgentTemplates.borrowers_followup_response(data)
    
    @staticmethod
    def handle_non_remitted_loans_query(agent_id):
        """
        Handles queries about loans that haven't been remitted.
        
        Args:
            agent_id: The ID of the loan officer/agent
            
        Returns:
            Formatted response about non-remitted loans
        """
        data = LoanAgentQueries.get_non_remitted_loans(agent_id)
        return LoanAgentTemplates.non_remitted_loans_response(data)
    
    @staticmethod
    def handle_performance_summary_query(agent_id):
        """
        Handles queries about the agent's overall performance.
        
        Args:
            agent_id: The ID of the loan officer/agent
            
        Returns:
            Formatted response with performance metrics
        """
        data = LoanAgentQueries.get_agent_performance_summary(agent_id)
        
        # Create a custom response based on performance data
        collection_rate = data['collection_rate']
        rating = "Excellent" if collection_rate >= 90 else \
                "Good" if collection_rate >= 80 else \
                "Average" if collection_rate >= 70 else \
                "Needs Improvement"
        
        return f"""
# Performance Summary

## Portfolio Overview
- Active loans: {data['active_loans_count']}
- Total disbursed: ₦{data['total_disbursed']:,.2f}
- Total collected: ₦{data['total_collected']:,.2f}
- Outstanding balance: ₦{data['total_outstanding']:,.2f}

## Collection Performance
- Collection rate: {collection_rate:.1f}%
- Performance rating: {rating}

## Risk Indicators
- Missed payment loans: {data['missed_loans_count']}
- Past maturity loans: {data['past_maturity_count']}

{"Your portfolio is performing well. Keep up the good work!" if rating in ["Excellent", "Good"] else 
 "Your portfolio needs attention. Focus on improving collections and reducing past-due loans."}
"""