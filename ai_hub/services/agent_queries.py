# loans/services/agent_queries.py

from django.db.models import Sum, F, Q, Count, Case, When, Value, IntegerField, FloatField
from django.db.models.functions import Coalesce
from django.utils import timezone
from datetime import timedelta

from loans.models import Ajo<PERSON>oan, AjoLoanRepayment, MissedRepaymentsTable
from ajo.models import AjoUser
from accounts.models import CustomUser

class LoanAgentQueries:
    """
    Query service class for the Loan Management AI Agent.
    Provides structured database access for common loan officer queries.
    """
    
    @staticmethod
    def get_missed_loans(agent_id):
        """
        Returns loans where borrowers have missed payments.
        
        Args:
            agent_id: The ID of the loan officer/agent
            
        Returns:
            QuerySet of loans with missed payments, including relevant metrics
        """
        return AjoLoan.objects.filter(
            agent_id=agent_id
        ).select_related('borrower')
    
    @staticmethod
    def get_missed_loans_summary(agent_id):
        """
        Provides a summary of all missed loans in the agent's portfolio.
        
        Args:
            agent_id: The ID of the loan officer/agent
            
        Returns:
            dict with aggregated statistics on missed loans
        """
        missed_loans = LoanAgentQueries.get_missed_loans(agent_id)
        
        return {
            "count": missed_loans.count(),
            "total_missed_amount": missed_loans.aggregate(
                amount=Coalesce(Sum('missed_amount'), 0)
            )['amount'],
            "avg_days_missed": missed_loans.aggregate(
                avg=Coalesce(Sum('days_missed') / Count('id'), 0)
            )['avg'],
            "highest_priority": list(
                missed_loans.order_by('-days_missed', '-missed_amount')[:5].values(
                    'id', 'borrower__fullname', 'borrower__phone_number', 
                    'days_missed', 'missed_amount', 'amount'
                )
            )
        }
    
    @staticmethod
    def get_past_maturity_loans(agent_id):
        """
        Returns loans that have passed their maturity date but aren't fully repaid.
        
        Args:
            agent_id: The ID of the loan officer/agent
            
        Returns:
            QuerySet of past maturity loans with relevant metrics
        """
        today = timezone.now().date()
        
        return AjoLoan.objects.filter(
            agent_id=agent_id,
            status="OPEN",
            end_date__lt=today,
        ).annotate(
            days_past_maturity=(today - F('end_date')),
            remaining_balance=F('total_outstanding_balance'),
        ).select_related('borrower')
    
    @staticmethod
    def get_past_maturity_summary(agent_id):
        """
        Provides summary statistics for loans past maturity date.
        
        Args:
            agent_id: The ID of the loan officer/agent
            
        Returns:
            dict with aggregated statistics on past maturity loans
        """
        past_maturity = LoanAgentQueries.get_past_maturity_loans(agent_id)
        
        return {
            "count": past_maturity.count(),
            "total_outstanding": past_maturity.aggregate(
                amount=Coalesce(Sum('remaining_balance'), 0)
            )['amount'],
            "highest_risk": list(
                past_maturity.order_by('-days_past_maturity', '-remaining_balance')[:5].values(
                    'id', 'borrower__fullname', 'borrower__phone_number', 
                    'days_past_maturity', 'remaining_balance', 'amount'
                )
            )
        }
    
    @staticmethod
    def get_borrowers_needing_followup(agent_id):
        """
        Identifies borrowers who need follow-up based on multiple risk factors.
        
        Args:
            agent_id: The ID of the loan officer/agent
            
        Returns:
            List of borrowers ranked by urgency of follow-up
        """
        # First get borrowers with missed payments
        missed_loans = LoanAgentQueries.get_missed_loans(agent_id)
        
        # Then get borrowers with loans past maturity
        past_maturity = LoanAgentQueries.get_past_maturity_loans(agent_id)
        
        # Combine and prioritize
        borrower_ids = set(missed_loans.values_list('borrower_id', flat=True)) | \
                      set(past_maturity.values_list('borrower_id', flat=True))
        
        results = []
        for borrower_id in borrower_ids:
            # Get borrower details
            borrower = AjoUser.objects.get(id=borrower_id)
            
            # Get their missed loans
            borrower_missed = missed_loans.filter(borrower_id=borrower_id)
            borrower_past_maturity = past_maturity.filter(borrower_id=borrower_id)
            
            # Calculate risk score (higher = more urgent follow-up)
            risk_score = (
                borrower_missed.count() * 5 +  # Each missed loan adds 5 points
                borrower_missed.aggregate(Sum('days_missed'))['days_missed__sum'] or 0 +  # Each day missed adds 1 point
                borrower_past_maturity.count() * 10  # Each past maturity adds 10 points
            )
            
            results.append({
                "borrower_id": borrower_id,
                "fullname": borrower.fullname,
                "phone_number": borrower.phone_number,
                "risk_score": risk_score,
                "missed_loans_count": borrower_missed.count(),
                "past_maturity_count": borrower_past_maturity.count(),
                "total_outstanding": (
                    borrower_missed.aggregate(Sum('missed_amount'))['missed_amount__sum'] or 0
                ) + (
                    borrower_past_maturity.aggregate(Sum('remaining_balance'))['remaining_balance__sum'] or 0
                )
            })
        
        # Sort by risk score (descending)
        results.sort(key=lambda x: x['risk_score'], reverse=True)
        
        return results[:10]  # Return top 10 borrowers needing follow-up
    
    @staticmethod
    def get_non_remitted_loans(agent_id):
        """
        Identifies loans that have been repaid by borrowers but not remitted to the system.
        
        Returns:
            QuerySet of loans with unremitted payments
        """
        # This is a simplified version - the actual implementation would depend
        # on how you track remittances in your system
        return AjoLoan.objects.filter(
            agent_id=agent_id,
            status="OPEN",
        ).annotate(
            total_repayments=Coalesce(Sum('ajoloanrepayment__repayment_amount'), 0),
            remitted_amount=F('total_paid_amount'),
            unremitted_amount=F('total_repayments') - F('remitted_amount'),
        ).filter(
            unremitted_amount__gt=0
        ).select_related('borrower')
    
    @staticmethod
    def get_agent_performance_summary(agent_id):
        """
        Provides a summary of the agent's loan portfolio performance.
        
        Args:
            agent_id: The ID of the loan officer/agent
            
        Returns:
            dict with performance metrics
        """
        # Get all active loans
        active_loans = AjoLoan.objects.filter(
            agent_id=agent_id,
            status="OPEN",
        )
        
        # Get total disbursed
        total_disbursed = active_loans.aggregate(
            amount=Coalesce(Sum('amount'), 0)
        )['amount']
        
        # Get total collected
        total_collected = active_loans.aggregate(
            amount=Coalesce(Sum('total_paid_amount'), 0)
        )['amount']
        
        # Get total outstanding
        total_outstanding = active_loans.aggregate(
            amount=Coalesce(Sum('total_outstanding_balance'), 0)
        )['amount']
        
        # Calculate collection rate
        collection_rate = 0
        if total_disbursed > 0:
            collection_rate = (total_collected / (total_collected + total_outstanding)) * 100
        
        return {
            "active_loans_count": active_loans.count(),
            "total_disbursed": total_disbursed,
            "total_collected": total_collected,
            "total_outstanding": total_outstanding,
            "collection_rate": collection_rate,
            "missed_loans_count": LoanAgentQueries.get_missed_loans(agent_id).count(),
            "past_maturity_count": LoanAgentQueries.get_past_maturity_loans(agent_id).count(),
        }