# ai_hub/services/agent_handler.py
# Update the existing LoanAgentHandler class with these changes

import json
import logging
from typing import Dict, Any, List, Optional
import html
from loans.models import LoanAnalysisLog, PastMaturityBuffer
from django.utils import timezone

from ai_hub.models import Conversation, Message, TokenUsageLog
from ai_hub.services.agent_data_service import LoanDataService
from django.conf import settings
from openai import OpenAI


logger = logging.getLogger(__name__)

class LoanAgentHandler:
    """
    Handler class that integrates OpenAI's ChatGPT with the loan management system.
    This class fetches raw loan data from the database, sends it to the LLM,
    and processes responses for the loan officer.
    """
    
    def __init__(self, agent_id: int, session_id: str = None):
        """
        Initialize the loan agent handler.
        
        Args:
            agent_id: ID of the agent/loan officer using the system
            session_id: Optional session identifier for the conversation
        """
        self.agent_id = agent_id
        self.client = OpenAI(api_key=settings.OPENAI_API_KEY)
        self.session_id = session_id
        
        # Get or create a conversation for this session
        self._get_or_create_conversation()
    
    def _get_or_create_conversation(self):
        """
        Get an existing conversation or create a new one.
        """
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        # Get the user object
        try:
            user = User.objects.get(id=self.agent_id)
        except User.DoesNotExist:
            logger.error(f"User with ID {self.agent_id} not found")
            raise ValueError(f"User with ID {self.agent_id} not found")
        
        # Get or create the conversation
        if self.session_id:
            # If session_id is provided, try to find an existing conversation
            self.conversation, created = Conversation.objects.get_or_create(
                user=user,
                session_id=self.session_id,
                defaults={'created_at': timezone.now()}
            )
        else:
            # If no session_id, create a new conversation
            self.conversation = Conversation.objects.create(
                user=user,
                created_at=timezone.now()
            )
            self.session_id = str(self.conversation.id)  # Use conversation ID as session ID
    
    def get_conversation_history(self) -> List[Dict]:
        """
        Retrieve the conversation history from the database.
        
        Returns:
            List of message dictionaries with role and content
        """
        messages = Message.objects.filter(conversation=self.conversation).order_by('created_at')
        return [{'role': msg.role, 'content': msg.content} for msg in messages]
    
    def _get_system_prompt(self) -> str:
        """
        Returns the system prompt for the AI agent.
        """
        return """
            # Loan Management Assistant

            You are an intelligent loan officer assistant specialized in analyzing loan portfolios and providing insights about borrowers, repayments, and loan status via text conversation. Your purpose is to help loan officers track their portfolio, identify issues, and prioritize follow-ups with borrowers.

            ## Domain Knowledge

            - **AjoLoan**: Represents individual loans with status (OPEN, COMPLETED, REJECTED, etc.), amounts, dates, and performance metrics
            - **AjoLoanRepayment**: Tracks repayment transactions including amounts, dates, and methods
            - **AjoUser**: Represents borrowers with contact information and financial details
            - **CustomUser**: Represents agents/loan officers who manage borrowers
            - **"Checker"** refers to the loan checker system that determines if agents can disburse new loans based on their collection performance. Questions about being "in checker", "blocked by checker", or "who is putting me in checker" are VALID loan portfolio questions.

            ### Key Terms
            - **Missed Loan**: A loan where the borrower has missed one or more scheduled repayments
            - **Past Maturity**: A loan that has passed its end date but is not fully repaid
            - **Outstanding Balance**: The remaining amount due on a loan
            - **Exposure**: The financial risk associated with a loan, especially those in delinquency

            ## The Loan Checker System

            The Loan Checker system determines whether loan officers can disburse new loans based on their repayment collection performance. It uses a two-tiered approach:

            ### 1. Overall Portfolio Check
            - Calculates the overall collection rate: (Total Repaid / Total Due) * 100
            - Compares this rate against a tier-specific threshold percentage
            - Tiers are based on the total amount disbursed (higher tiers have higher collection requirements)
            - Agents with collection rates below their tier threshold cannot disburse new loans

            ### 2. Individual Loan Check
            - Evaluates each loan separately to prevent the "big loan problem" (where one large well-performing loan masks issues with many smaller loans)
            - Tracks positive loans (good performance) vs. negative loans (poor performance)
            - Loans with high outstanding days or low collection rates are flagged as defaulting 
            - Even if the overall check passes, too many individual loan issues can block disbursement

            ### Agent Buffers
            - Agents may receive "buffers" - additional amounts added to their effective repaid total
            - Buffers are typically granted when agents are on leave or have legitimate reasons for collection difficulties
            - A buffer of ₦5,000 means the agent is treated as if they collected an additional ₦5,000

            ### Key Metrics for Loan Checking
            - **Due Yesterday**: Amount that should have been repaid by yesterday for each loan
            - **Outstanding Days**: Number of days a loan repayment is overdue
            - **Outstanding Due**: Amount that is currently overdue
            - **Collection Rate**: Percentage of due amount that has been collected
            - **Past Maturity Loans**: Loans beyond their end date that aren't fully repaid

            ## Your Role

            1. **Data Analyst**: You'll receive raw loan data and need to analyze it to answer the loan officer's questions.
            2. **Prioritizer**: Help identify which borrowers/loans need immediate attention.
            3. **Insight Provider**: Extract meaningful patterns and provide actionable insights.
            4. **Checker Explainer**: Help officers understand their checker status and what they need to do to improve it.
            
            ## Response Guidelines for Mobile Optimization

            When answering questions, optimize for mobile consumption:
            1. Keep responses detailed but concise - prioritize critical information
            2. Use simple, clear language without technical jargon
            3. Format with short paragraphs and small bullet lists
            4. Avoid tables, charts, or complex formatting that won't display well on mobile
            5. Use headings (<h3>) for clear sections but keep them brief
            6. Bold only the most critical information a loan officer needs to see immediately
            7. Use simple numerical formats with currency symbol (₦20,000 not ₦20,000.00)
            8. For prioritization, use numbered lists with the most urgent first
            9. Include only phone numbers and essential identifiers for immediate action
            10. Do not explain concepts or give definitions unless asked for it


            Always maintain a professional tone with actionable insights. For limited data, acknowledge constraints briefly and provide streamlined information based on what's available.
            
            ## Data Structure

            You'll be provided with raw loan portfolio data including:
            - Loans: Individual loan records with status, amounts, dates, etc.
            - Repayments: Recent repayment records
            - Metrics: Aggregated portfolio statistics
            - Borrowers: Information about borrowers, especially high-priority ones
            - Context: Additional information to help interpret the data
            
            Use this comprehensive data to provide informed, accurate, and helpful responses to the loan officer's questions.
            If the question is out of your duty scope as stated above, respond with "I'm sorry, I can only help with loan-related queries."
            """
    
    def _retrieve_loan_data(self) -> Dict[str, Any]:
        """
        Retrieves loan data for the agent from the database.
        
        Returns:
            Dict containing comprehensive loan data
        """
        try:
            return LoanDataService.get_agent_loan_data(self.agent_id)
        except Exception as e:
            logger.exception(f"Error retrieving loan data: {str(e)}")
            return {"error": str(e)}
    
    def _format_loan_data_for_llm(self, data: Dict[str, Any]) -> str:
        """
        Formats the loan data into a string suitable for the LLM.
        
        Args:
            data: The loan data dictionary
            
        Returns:
            A formatted string representation of the loan data
        """
        try:
            # Safety check for problematic data
            if "error" in data:
                return f"ERROR RETRIEVING DATA: {data['error']}"
            
            # Current date section
            date_str = f"## Current Date\n{data.get('context', {}).get('date', 'Unknown')}\n"
            
            # Basic metrics section
            metrics = data.get("metrics", {})
            metrics_str = (
                "## Portfolio Metrics\n"
                f"- Active loans: {metrics.get('active_loans_count', 0)}\n"
                f"- Total disbursed: ₦{metrics.get('total_disbursed', 0):,.2f}\n"
                f"- Total collected: ₦{metrics.get('total_collected', 0):,.2f}\n"
                f"- Total outstanding: ₦{metrics.get('total_outstanding', 0):,.2f}\n"
                f"- Collection rate: {metrics.get('collection_rate', 0):.1f}%\n"
                f"- Missed repayment count: {metrics.get('missed_repayment_count', 0)}\n"
                f"- Past maturity count: {metrics.get('past_maturity_count', 0)}\n"
            )
            
            # Get loan checker status - This is the new section we're adding
            # Try to get LoanAnalysisLog for the agent
            from loans.models import LoanAnalysisLog, PastMaturityBuffer
            from django.utils import timezone
            
            agent_id = self.agent_id
            today = timezone.now().date()
            
            # Get the latest loan analysis log for this agent
            loan_analysis = LoanAnalysisLog.objects.filter(
                agent_id=agent_id, 
                created_at__date=today
            ).first()
            
            # Get any buffer information
            buffer_info = PastMaturityBuffer.objects.filter(agent_id=agent_id).first()
            
            # Format the loan checker status section
            checker_str = "## Loan Checker Status\n"
            
            if loan_analysis:
                # Basic checker information
                checker_str += (
                    f"- **Can Give Loans**: {'Yes' if loan_analysis.can_give_loan else 'No'}\n"
                    f"- **Collection Rate**: {loan_analysis.percent:.1f}%\n"
                    f"- **Amount Due**: ₦{float(loan_analysis.amount_due):,.2f}\n"
                    f"- **Total Paid**: ₦{float(loan_analysis.total_paid):,.2f}\n"
                )
                
                # Include due to repayment ratio if available
                if loan_analysis.due_to_repayment_ratio is not None:
                    checker_str += f"- **Individual Loan Payment Ratio**: {loan_analysis.due_to_repayment_ratio:.1f}%\n"
                
                # Include reason for blocking if applicable
                if not loan_analysis.can_give_loan and loan_analysis.block_reason:
                    checker_str += f"- **Block Reason**: {loan_analysis.block_reason}\n"
                    
                # Include counts of positive and negative loans
                checker_str += (
                    f"- **Positive Loan Count**: {loan_analysis.positive_count}\n"
                    f"- **Negative Loan Count**: {loan_analysis.negative_count}\n"
                )
                
                # Include missed amount
                if loan_analysis.missed_amount > 0:
                    checker_str += f"- **Missed Amount**: ₦{loan_analysis.missed_amount:,.2f}\n"
                    
                # Include past maturity information if available
                if loan_analysis.total_past_maturity_amount:
                    checker_str += f"- **Past Maturity Amount**: ₦{loan_analysis.total_past_maturity_amount:,.2f}\n"
                
                if loan_analysis.average_past_maturity_days:
                    checker_str += f"- **Average Past Maturity Days**: {loan_analysis.average_past_maturity_days:.1f}\n"
                    
                # Include defaulting loans count
                defaulting_loans_count = loan_analysis.defaulting_loans.count()
                if defaulting_loans_count > 0:
                    checker_str += f"- **Number of Defaulting Loans**: {defaulting_loans_count}\n"
                    
                    # List the top 3 defaulting loans
                    checker_str += "- **Top Defaulting Loans**:\n"
                    for i, loan in enumerate(loan_analysis.defaulting_loans.all()[:3], 1):
                        checker_str += (
                            f"  {i}. Loan ID: {loan.id}, Borrower: {loan.borrower.fullname}\n"
                            f"     Outstanding Days: {loan.outstanding_days_today}, "
                            f"Outstanding Amount: ₦{loan.outstanding_due_today:,.2f}\n"
                        )
            else:
                checker_str += "No loan checker status available for today.\n"
                
            # Add buffer information section if available
            buffer_str = ""
            if buffer_info:
                buffer_str = (
                    "## Buffer Information\n"
                    f"- **Agent Buffer Amount**: ₦{buffer_info.agent_buffer_amount:,.2f}\n"
                    f"- **Branch Buffer Amount**: ₦{buffer_info.branch_buffer_amount:,.2f}\n"
                    f"- **Total Amount Paid**: ₦{buffer_info.total_amount_paid:,.2f}\n"
                    f"- **Number of Repayment Months**: {buffer_info.number_of_repayment_months}\n"
                    f"- **Number of Months Paid**: {buffer_info.number_of_months_paid}\n"
                )
                if buffer_info.due_date:
                    buffer_str += f"- **Due Date**: {buffer_info.due_date}\n"
                    
            # High priority borrowers section
            high_priority = data.get("high_priority_borrowers", [])
            high_priority_str = "## High Priority Borrowers\n"
            
            if high_priority:
                for i, borrower in enumerate(high_priority[:5], 1):  # Top 5 for brevity
                    high_priority_str += (
                        f"{i}. {borrower.get('borrower_name')} (Phone: {borrower.get('borrower_phone')})\n"
                        f"   - Outstanding: ₦{borrower.get('outstanding_amount', 0):,.2f}, "
                        f"Days missed: {borrower.get('outstanding_days', 0)}, "
                        f"Days past maturity: {borrower.get('days_past_maturity', 0)}\n"
                    )
            else:
                high_priority_str += "No high priority borrowers found.\n"
            
            # Loans section (limited to 10 for brevity)
            loans = data.get("loans", [])
            loans_str = "## Loan Records (Sample)\n"
            
            if loans:
                for i, loan in enumerate(loans[:10], 1):
                    loans_str += (
                        f"{i}. Loan ID: {loan.get('id')}, Borrower: {loan.get('borrower_name')}\n"
                        f"   - Amount: ₦{loan.get('amount', 0):,.2f}, "
                        f"Status: {loan.get('status')}, "
                        f"Outstanding: ₦{loan.get('total_outstanding_balance', 0):,.2f}\n"
                        f"   - Start: {loan.get('start_date')}, End: {loan.get('end_date')}\n"
                        f"   - Due Today: ₦{loan.get('due_today', 0):,.2f}, "
                        f"Outstanding Days: {loan.get('outstanding_days', 0)}\n"
                    )
            else:
                loans_str += "No loans found.\n"
            
            # Combine all sections
            formatted_data = f"{date_str}\n{metrics_str}\n{checker_str}\n"
            
            # Add buffer section if available
            if buffer_str:
                formatted_data += f"{buffer_str}\n"
                
            formatted_data += f"{high_priority_str}\n{loans_str}\n"
            
            # Add missed repayments if available
            missed_repayments = data.get("missed_repayments", [])
            if missed_repayments:
                missed_str = "## Missed Repayments Summary\n"
                missed_str += f"Total missed repayments: {len(missed_repayments)}\n"
                
                # Add information about the top 5 missed repayments
                for i, mr in enumerate(missed_repayments[:5], 1):
                    missed_str += (
                        f"{i}. Borrower: {mr.get('borrower_name')} (Phone: {mr.get('borrower_phone')})\n"
                        f"   - Missed amount: ₦{mr.get('missed_amount', 0):,.2f}, "
                        f"Missed days: {mr.get('missed_days', 0)}\n"
                    )
                
                formatted_data += f"\n{missed_str}\n"
            
            # Add multi-loan borrowers if available
            multi_loan_borrowers = data.get("multi_loan_borrowers", [])
            if multi_loan_borrowers:
                multi_str = "## Borrowers with Multiple Loans\n"
                
                for i, mlb in enumerate(multi_loan_borrowers[:5], 1):
                    multi_str += (
                        f"{i}. {mlb.get('borrower_name')} (Phone: {mlb.get('borrower_phone')}): "
                        f"{mlb.get('loan_count')} active loans\n"
                    )
                
                formatted_data += f"\n{multi_str}\n"
            
            # Add recent repayments if available
            recent_repayments = data.get("repayments", [])
            if recent_repayments:
                repay_str = "## Recent Repayments (Last 30 days)\n"
                repay_str += f"Total recent repayments: {len(recent_repayments)}\n"
                
                # Aggregate repayment amount
                total_recent = sum(repay.get('repayment_amount', 0) for repay in recent_repayments)
                repay_str += f"Total amount: ₦{total_recent:,.2f}\n"
                
                formatted_data += f"\n{repay_str}\n"
            
            return formatted_data
            
        except Exception as e:
            logger.exception(f"Error formatting loan data: {str(e)}")
            return f"Error formatting data: {str(e)}"
    
    def process_message(self, user_message: str) -> Dict[str, Any]:
        """
        Process a message from the loan officer and return a response in HTML format.
        
        Args:
            user_message: The message from the loan officer
            
        Returns:
            The AI's response as HTML
        """
        # Save the user message to the database
        user_msg = Message.objects.create(
            conversation=self.conversation,
            role='user',
            content=user_message
        )
        
        try:
            # Retrieve loan data
            loan_data = self._retrieve_loan_data()
            
            # Format the loan data for the LLM
            formatted_data = self._format_loan_data_for_llm(loan_data)
            
            # Get conversation history from database
            history = self.get_conversation_history()
            
            # Create the messages array for the API call
            messages = [
                {"role": "system", "content": self._get_system_prompt() + "\n\nIMPORTANT: Format your response using HTML tags. Use <h2>, <h3>, <ul>, <li>, <p>, <strong>, <table>, <tr>, <td> tags as appropriate to create a well-structured, readable response. Make sure all numbers are properly formatted with commas for thousands (e.g., ₦25,000.00 instead of ₦25000). Do not use Markdown formatting."},
                {"role": "user", "content": "Here is my current loan portfolio data:\n\n" + formatted_data}
            ]
            
            # Add conversation history (up to last 5 messages to keep context manageable)
            # Skip the first one as it's the instruction about portfolio data
            for i, msg in enumerate(history[-10:]):
                if i == 0 and "loan portfolio data" in msg['content']:
                    continue
                messages.append(msg)
                
            # Add the current user message if it's not already in the history
            if history and history[-1]['role'] != 'user' or history[-1]['content'] != user_message:
                messages.append({"role": "user", "content": user_message})
            
            # Make the API call
            response = self.client.chat.completions.create(
                model="gpt-4-turbo",  # or your preferred model
                messages=messages,
                temperature=0.3,  # Lower temperature for more consistent responses
                max_tokens=1500,  # Adjust as needed
            )
            
            # Get the response message
            response_message = response.choices[0].message.content

            # Get token usage
            token_usage = {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            }
            
            # Save the assistant message to the database
            assistant_msg = Message.objects.create(
                conversation=self.conversation,
                role='assistant',
                content=response_message,
                token_usage=token_usage["completion_tokens"]
            )
            
            # Update the conversation's updated_at time
            self.conversation.updated_at = timezone.now()
            self.conversation.save()
            
            # Trim messages if needed to prevent excessive storage
            # self.conversation.trim_messages(max_messages=50)
            
            # Return the response
            return {
                "response": response_message,
                "status": "success",
                "usage": token_usage,
                "session_id": self.session_id
            }
                
        except Exception as e:
            logger.exception(f"Error processing message: {str(e)}")
            error_message = f"<p class='error'>I'm sorry, I encountered an error while processing your request: {str(e)}</p>"
            
            # Save the error response
            Message.objects.create(
                conversation=self.conversation,
                role='assistant',
                content=error_message
            )
            
            return {
                "response": error_message,
                "status": "error",
                "error": str(e),
                "session_id": self.session_id
            }
    
    def reset_conversation(self) -> Dict[str, str]:
        """
        Reset the conversation by creating a new conversation object.
        
        Returns:
            Dict indicating success with the new session ID
        """
        # Create a new conversation
        old_session_id = self.session_id
        self.session_id = None
        self._get_or_create_conversation()
        
        return {
            "status": "success",
            "message": "Conversation history has been reset.",
            "old_session_id": old_session_id,
            "new_session_id": self.session_id
        }