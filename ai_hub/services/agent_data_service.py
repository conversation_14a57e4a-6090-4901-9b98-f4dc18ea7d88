# ai_hub/services/agent_data_service.py

import json
import logging
from datetime import <PERSON><PERSON><PERSON>
from typing import Dict, List, Any

from django.db.models import Sum, F, Q, Count, Case, When, Value, FloatField
from django.db.models.functions import Coalesce
from django.utils import timezone

from loans.models import AjoLoan, AjoLoanRepayment, MissedRepaymentsTable
from ajo.models import AjoUser
from accounts.models import CustomUser

logger = logging.getLogger(__name__)

class LoanDataService:
    """
    Service for fetching comprehensive loan data directly from the database
    for AI analysis. This provides raw data that the language model can reason over 
    rather than pre-defined function responses.
    """
    
    @staticmethod
    def get_agent_loan_data(agent_id: int) -> Dict[str, Any]:
        """
        Get comprehensive loan data for an agent.
        
        Args:
            agent_id: The ID of the loan officer/agent
            
        Returns:
            Dict containing loans, metrics, high priority borrowers, and context
        """
        try:
            # Get agent information
            try:
                agent = CustomUser.objects.get(id=agent_id)
                agent_info = {
                    "id": agent.id,
                    "email": agent.email,
                    "phone": agent.user_phone,
                    "user_type": agent.user_type
                }
            except CustomUser.DoesNotExist:
                logger.error(f"Agent with ID {agent_id} not found")
                agent_info = {"id": agent_id, "error": "Agent not found"}
            
            # Get active loans with related borrower information
            active_loans = AjoLoan.objects.filter(
                agent_id=agent_id,
                status__in=["OPEN", "APPROVED", "IN_PROGRESS", "OPEN_TO_SUPERVISOR"]
            ).select_related('borrower')
            
            today = timezone.now().date()
            
            # Process loan data
            loans_data = []
            for loan in active_loans:
                # Calculate derived metrics
                is_past_maturity = loan.end_date and loan.end_date < today
                days_past_maturity = (today - loan.end_date).days if is_past_maturity else 0
                
                # Add loan data with calculated fields
                loan_data = {
                    "id": loan.id,
                    "status": loan.status,
                    "performance_status": loan.performance_status,
                    "loan_type": loan.loan_type,
                    "amount": loan.amount,
                    "repayment_amount": loan.repayment_amount,
                    "daily_repayment_amount": loan.daily_repayment_amount,
                    "total_paid_amount": loan.total_paid_amount,
                    "total_outstanding_balance": loan.total_outstanding_balance,
                    "start_date": loan.start_date.isoformat() if loan.start_date else None,
                    "end_date": loan.end_date.isoformat() if loan.end_date else None,
                    "tenor": loan.tenor,
                    "borrower_id": loan.borrower.id,
                    "borrower_name": loan.borrower.fullname,
                    "borrower_phone": loan.borrower.phone,
                    "is_past_maturity": is_past_maturity,
                    "days_past_maturity": days_past_maturity,
                    "missed_repayment": loan.outstanding_due_today > 0,
                    "outstanding_amount": loan.outstanding_due_today,
                    "outstanding_days": loan.outstanding_days_today,
                    "loan_age": loan.loan_age if hasattr(loan, 'loan_age') else None,
                    "due_today": loan.due_today if hasattr(loan, 'due_today') else None,
                }
                loans_data.append(loan_data)
            
            # Get completed loans (for historical context)
            completed_loans = AjoLoan.objects.filter(
                agent_id=agent_id,
                status="COMPLETED"
            ).values('id', 'borrower__id', 'amount', 'total_paid_amount')
            
            # Get repayment history
            recent_repayments = AjoLoanRepayment.objects.filter(
                agent_id=agent_id,
                created_at__gte=today - timedelta(days=30)  # Last 30 days
            ).select_related('ajo_loan', 'borrower')
            
            repayment_data = [{
                "id": repayment.id,
                "loan_id": repayment.ajo_loan.id,
                "borrower_name": repayment.borrower.fullname,
                "repayment_amount": repayment.repayment_amount,
                "repayment_type": repayment.repayment_type,
                "paid_date": repayment.paid_date.isoformat() if repayment.paid_date else None,
                "created_at": repayment.created_at.isoformat(),
            } for repayment in recent_repayments]
            
            # Calculate summary metrics
            loan_metrics = {
                'total_outstanding': sum(loan['total_outstanding_balance'] for loan in loans_data),
                'total_disbursed': sum(loan['amount'] for loan in loans_data),
                'total_collected': sum(loan['total_paid_amount'] for loan in loans_data),
                'past_maturity_count': sum(1 for loan in loans_data if loan['is_past_maturity']),
                'missed_repayment_count': sum(1 for loan in loans_data if loan['missed_repayment']),
                'active_loans_count': len(loans_data),
                'completed_loans_count': completed_loans.count(),
                'total_loans_count': len(loans_data) + completed_loans.count(),
                'collection_rate': 0,  # Will calculate below if possible
            }
            
            # Calculate collection rate
            total_expected = loan_metrics['total_collected'] + loan_metrics['total_outstanding']
            if total_expected > 0:
                loan_metrics['collection_rate'] = (loan_metrics['total_collected'] / total_expected) * 100
            
            # Get high priority borrowers - those with missed repayments or past maturity
            high_priority = sorted(
                [loan for loan in loans_data if loan['missed_repayment'] or loan['is_past_maturity']],
                key=lambda x: (x['days_past_maturity'], x['outstanding_amount']),
                reverse=True
            )
            
            # Get borrowers with multiple active loans
            borrower_loan_counts = {}
            for loan in loans_data:
                borrower_id = loan['borrower_id']
                if borrower_id in borrower_loan_counts:
                    borrower_loan_counts[borrower_id] += 1
                else:
                    borrower_loan_counts[borrower_id] = 1
                    
            multi_loan_borrowers = [
                {
                    "borrower_id": borrower_id,
                    "borrower_name": next(loan['borrower_name'] for loan in loans_data if loan['borrower_id'] == borrower_id),
                    "borrower_phone": next(loan['borrower_phone'] for loan in loans_data if loan['borrower_id'] == borrower_id),
                    "loan_count": count
                }
                for borrower_id, count in borrower_loan_counts.items()
                if count > 1
            ]
            
            # Try to get missed repayments data if available
            missed_repayments_data = []
            try:
                missed_repayments = MissedRepaymentsTable.objects.filter(
                    loan_officer_id=agent_id
                ).select_related('loan', 'borrower')
                
                missed_repayments_data = [{
                    "id": mr.id,
                    "loan_id": mr.loan.id,
                    "borrower_name": mr.borrower.fullname,
                    "borrower_phone": mr.borrower_phone,
                    "guarantor_phone": mr.guarantor_phone,
                    "missed_days": mr.missed_days,
                    "defaulting_days": mr.defaulting_days,
                    "loan_amount": mr.loan_amount,
                    "missed_amount": mr.missed_amount,
                    "paid_amount": mr.paid_amount,
                    "outstanding_loan_balance": mr.outstanding_loan_balance,
                } for mr in missed_repayments]
            except Exception as e:
                logger.error(f"Error fetching missed repayments: {str(e)}")
                # Continue without this data if there's an issue
            
            # Compile the result
            result = {
                'agent': agent_info,
                'loans': loans_data,
                'repayments': repayment_data,
                'metrics': loan_metrics,
                'high_priority_borrowers': high_priority[:10],  # Top 10 priority borrowers
                'multi_loan_borrowers': multi_loan_borrowers,
                'missed_repayments': missed_repayments_data,
                'context': {
                    'date': today.isoformat(),
                    'loan_status_definitions': {
                        'OPEN': 'Active loan in good standing',
                        'COMPLETED': 'Fully repaid loan',
                        'PAST_MATURITY': 'Loan that has reached end date but isn\'t fully repaid',
                        'DEFAULTED': 'Loan with significant missed payments',
                        'APPROVED': 'Loan that has been approved but not yet disbursed',
                        'IN_PROGRESS': 'Loan in the process of being approved',
                        'OPEN_TO_SUPERVISOR': 'Loan waiting for supervisor approval',
                        'REJECTED': 'Loan that was rejected',
                        'CLOSED': 'Loan that was closed without being completed',
                        'NOT_TAKEN': 'Loan that was approved but not taken by the borrower',
                        'DECLINED_BY_SUPERVISOR': 'Loan that was declined by the supervisor'
                    },
                    'loan_performance_status_definitions': {
                        'PERFORMING': 'Loan with timely repayments',
                        'PAST_MATURITY': 'Loan past its end date',
                        'DEFAULTED': 'Loan with significant missed payments',
                        'OWED_BALANCE': 'Loan with outstanding balance',
                        'LOST': 'Loan considered uncollectible'
                    },
                    'loan_types': {
                        'AJO': 'Standard loan product',
                        'BNPL': 'Buy Now Pay Later loan',
                        'BOOSTA': 'Boosta loan product',
                        'BOOSTA_2X': 'Boosta 2X loan product',
                        'PROSPER_LOAN': 'Prosper loan product'
                    }
                }
            }
            
            return result
        
        except Exception as e:
            logger.exception(f"Error in get_agent_loan_data: {str(e)}")
            return {
                "error": str(e),
                "message": "Failed to retrieve loan data. Please try again or contact support."
            }