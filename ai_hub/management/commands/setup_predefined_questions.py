# ai_hub/management/commands/setup_predefined_questions.py
import logging
from django.core.management.base import BaseCommand
from django.db import transaction
from ai_hub.models import PredefinedQuestion

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Sets up the initial predefined questions for the loan assistant chat'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreation of all predefined questions (deletes existing ones)',
        )
        parser.add_argument(
            '--update',
            action='store_true',
            help='Update existing questions if they exist (by order)',
        )

    @transaction.atomic
    def handle(self, *args, **options):
        force = options.get('force', False)
        update = options.get('update', False)
        
        # Define the initial predefined questions
        questions = [
            {
                'question': 'Who is putting me on the checker?',
                'description': 'Shows which borrowers are most responsible for pushing you into checker block status',
                'order': 1
            },
            {
                'question': 'How many missed loans do I have?',
                'description': 'Provides count and details about loans with missed repayments',
                'order': 2
            },
            {
                'question': 'Which loans will put me on checker tomorrow?',
                'description': 'Identifies loans that will trigger checker block if not addressed today',
                'order': 3
            },
            {
                'question': 'How many past maturities do I have?',
                'description': 'Shows loans that have passed their end date but aren\'t fully repaid',
                'order': 4
            },
            {
                'question': 'How much is my missed repayment?',
                'description': 'Calculates total amount of missed repayments in your portfolio',
                'order': 5
            },
            {
                'question': 'Which borrower should I visit?',
                'description': 'Prioritizes borrowers based on missed repayments and risk',
                'order': 6
            },
            {
                'question': 'Which loans are past due?',
                'description': 'Lists loans that have missed repayment deadlines',
                'order': 7
            },
            {
                'question': 'Which borrowers need me to follow up?',
                'description': 'Identifies borrowers requiring attention based on repayment patterns',
                'order': 8
            },
            {
                'question': 'Which loans have I not remitted?',
                'description': 'Shows loans where repayments haven\'t been properly recorded',
                'order': 9
            },
            {
                'question': 'Which loans are putting me at risk of being checked?',
                'description': 'Identifies loans that are close to triggering checker block',
                'order': 10
            },
        ]
        
        # Check if we need to delete existing questions
        if force:
            existing_count = PredefinedQuestion.objects.count()
            PredefinedQuestion.objects.all().delete()
            self.stdout.write(self.style.WARNING(f"Deleted {existing_count} existing predefined questions"))
        
        created_count = 0
        updated_count = 0
        skipped_count = 0

        # Create the predefined questions
        for question_data in questions:
            # Check if question with this order already exists
            existing_question = PredefinedQuestion.objects.filter(order=question_data['order']).first()
            
            if existing_question:
                if update:
                    # Update existing question
                    for key, value in question_data.items():
                        setattr(existing_question, key, value)
                    existing_question.save()
                    self.stdout.write(f"Updated question: {question_data['question']}")
                    updated_count += 1
                else:
                    # Skip if we're not updating
                    self.stdout.write(f"Skipped existing question (order {question_data['order']}): {question_data['question']}")
                    skipped_count += 1
            else:
                # Create new question
                PredefinedQuestion.objects.create(**question_data)
                self.stdout.write(f"Created question: {question_data['question']}")
                created_count += 1
        
        # Summary message
        self.stdout.write(
            self.style.SUCCESS(
                f"Predefined questions setup complete: {created_count} created, "
                f"{updated_count} updated, {skipped_count} skipped"
            )
        )