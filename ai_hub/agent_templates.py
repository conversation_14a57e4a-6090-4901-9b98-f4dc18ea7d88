# loans/services/agent_templates.py

class LoanAgentTemplates:
    """
    Response templates for the Loan Management AI Agent.
    """
    
    @staticmethod
    def missed_loans_response(data):
        """Format response for missed loans query"""
        template = f"""
# Missed Loan Summary

You currently have **{data['count']} loans** with missed payments, totaling **₦{data['total_missed_amount']:,.2f}** in outstanding repayments.

## Priority Borrowers for Follow-up

{"" if not data['highest_priority'] else "Here are the borrowers you should contact immediately:"}
"""
        
        if data['highest_priority']:
            for i, loan in enumerate(data['highest_priority'], 1):
                template += f"""
{i}. **{loan['borrower__fullname']}** ({loan['borrower__phone_number']})
   - Days missed: {loan['days_missed']}
   - Missed amount: ₦{loan['missed_amount']:,.2f}
   - Original loan: ₦{loan['amount']:,.2f}
"""
        
        template += f"""
## Recommended Actions

1. Contact the top {min(3, len(data['highest_priority']))} borrowers today to discuss repayment
2. Schedule follow-ups with the remaining borrowers this week
3. Consider restructuring loans that are more than 14 days past due

The average number of days missed across your portfolio is {data['avg_days_missed']:.1f} days.
"""
        return template
    
    @staticmethod
    def past_maturity_response(data):
        """Format response for past maturity loans query"""
        template = f"""
# Past Maturity Loans

You have **{data['count']} loans** that have passed their maturity date but haven't been fully repaid. The total outstanding balance is **₦{data['total_outstanding']:,.2f}**.

## High Risk Loans

{"" if not data['highest_risk'] else "These loans require immediate attention:"}
"""
        
        if data['highest_risk']:
            for i, loan in enumerate(data['highest_risk'], 1):
                template += f"""
{i}. **{loan['borrower__fullname']}** ({loan['borrower__phone_number']})
   - Days past maturity: {loan['days_past_maturity']}
   - Outstanding balance: ₦{loan['remaining_balance']:,.2f}
   - Original loan: ₦{loan['amount']:,.2f}
"""
        
        template += f"""
## Recommended Actions

1. Initiate recovery process for loans more than 30 days past maturity
2. Consider restructuring for borrowers with good payment history
3. Escalate to supervisor loans more than 60 days past maturity

Past maturity loans require urgent attention as they significantly impact your portfolio performance.
"""
        return template
    
    @staticmethod
    def borrowers_followup_response(data):
        """Format response for borrowers needing follow-up"""
        template = """
# Priority Borrowers for Follow-up

Based on risk analysis, these borrowers need your immediate attention:
"""
        
        for i, borrower in enumerate(data[:5], 1):
            template += f"""
{i}. **{borrower['fullname']}** ({borrower['phone_number']})
   - Risk score: {borrower['risk_score']}
   - Missed loans: {borrower['missed_loans_count']}
   - Past maturity loans: {borrower['past_maturity_count']}
   - Total outstanding: ₦{borrower['total_outstanding']:,.2f}
"""
        
        template += """
## Follow-up Strategy

1. **Call immediately**: Top 3 borrowers
2. **Schedule visits**: All borrowers with risk score > 20
3. **Send reminders**: Remaining borrowers

For the highest risk borrowers, consider involving your supervisor in recovery discussions.
"""
        return template
    
    @staticmethod
    def non_remitted_loans_response(data):
        """Format response for non-remitted loans"""
        total_unremitted = sum(loan.unremitted_amount for loan in data)
        count = len(data)
        
        template = f"""
# Unremitted Payments

You have **{count} loans** with payments collected but not yet remitted in the system, totaling **₦{total_unremitted:,.2f}**.

## Details
"""
        
        for i, loan in enumerate(data[:5], 1):
            template += f"""
{i}. Loan #{loan.id} - **{loan.borrower.fullname}**
   - Collected: ₦{loan.total_repayments:,.2f}
   - Remitted: ₦{loan.remitted_amount:,.2f}
   - Unremitted: ₦{loan.unremitted_amount:,.2f}
"""
        
        template += """
## Action Required

Please remit all collected payments to maintain accurate system records. Unremitted payments can affect your performance metrics and borrower account status.
"""
        return template