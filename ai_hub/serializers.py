# ai_hub/serializers.py
from rest_framework import serializers
from ai_hub.models import Conversation, Message, PredefinedQuestion

class ChatMessageSerializer(serializers.Serializer):
    """Serializer for chat message requests."""
    message = serializers.CharField(required=True)
    session_id = serializers.CharField(required=False, allow_null=True, allow_blank=True)


class MessageSerializer(serializers.ModelSerializer):
    """Serializer for individual message objects - now used for flat message list."""
    
    class Meta:
        model = Message
        fields = ['id', 'role', 'content', 'created_at', 'token_usage']


class ConversationSerializer(serializers.ModelSerializer):
    """Serializer for conversation objects with included messages."""
    messages = MessageSerializer(many=True, read_only=True)
    message_count = serializers.SerializerMethodField()
    last_message = serializers.SerializerMethodField()
    
    class Meta:
        model = Conversation
        fields = ['id', 'created_at', 'updated_at', 'session_id', 'messages', 'message_count', 'last_message']
    
    def get_message_count(self, obj):
        """Return the number of messages in this conversation."""
        return obj.messages.count()
    
    def get_last_message(self, obj):
        """Return the last message in this conversation."""
        last_message = obj.messages.order_by('-created_at').first()
        if last_message:
            return MessageSerializer(last_message).data
        return None

    def to_representation(self, instance):
        """
        Customize the serialized output based on context.
        If 'summary_only' is in context, don't include full messages list.
        """
        representation = super().to_representation(instance)
        
        # Check if we're in list mode (multiple conversations)
        request = self.context.get('request')
        if request and not request.query_params.get('session_id'):
            # Remove the full messages list to reduce payload size
            representation.pop('messages', None)
        
        return representation


class PredefinedQuestionSerializer(serializers.ModelSerializer):
    """Serializer for predefined questions."""
    class Meta:
        model = PredefinedQuestion
        fields = ['id', 'question', 'description', 'order']