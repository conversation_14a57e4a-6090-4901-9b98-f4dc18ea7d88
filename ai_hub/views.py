# ai_hub/views.py
from ai_hub.serializers import (
    ChatMessageSerializer, ConversationSerializer, MessageSerializer, 
    PredefinedQuestionSerializer
)
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.generics import ListAPIView
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import J<PERSON><PERSON>uthentication

from ai_hub.services.agent_handler import LoanAgentHandler
from ai_hub.models import TokenUsageLog, Conversation, Message, PredefinedQuestion
import logging

logger = logging.getLogger(__name__)

class LoanAssistantChatAPIView(APIView):
    """
    API endpoint for the loan officer to chat with the AI assistant.
    The assistant analyzes raw loan data to answer questions about the portfolio.
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def post(self, request, format=None):
        """
        Handle POST requests to the API.
        
        Receives a message from the loan officer, processes it through the AI assistant,
        and returns the response.
        """
        # Validate the input data
        serializer = ChatMessageSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        # Get the current user's ID (the loan officer/agent)
        agent = request.user
        
        # Get the validated message and optional session ID
        message = serializer.validated_data['message']
        session_id = serializer.validated_data.get('session_id')
        
        try:
            # Log the incoming request (excluding sensitive data)
            logger.info(f"Processing message for agent {agent.id}")
            
            # Create a handler and process the message
            handler = LoanAgentHandler(agent.id, session_id=session_id)
            result = handler.process_message(message)
            
            # Log token usage data
            if result.get("status") == "success" and "usage" in result:
                client_ip = self.get_client_ip(request)
                TokenUsageLog.log_usage(
                    user=agent,
                    usage_data=result.get("usage"),
                    request_message=message,
                    response=result.get("response"),
                    session_id=result.get("session_id"),
                    client_ip=client_ip,
                    is_successful=True
                )
            
            # Return the response
            response_status = status.HTTP_200_OK if result.get("status") == "success" else status.HTTP_500_INTERNAL_SERVER_ERROR
            return Response({
                "response": result.get("response"),
                "status": result.get("status", "error"),
                "token_usage": result.get("usage"),
                "session_id": result.get("session_id")
            }, status=response_status)
            
        except Exception as e:
            logger.exception(f"Error in LoanAssistantChatAPIView: {str(e)}")
            
            # Log failed request
            client_ip = self.get_client_ip(request)
            TokenUsageLog.log_usage(
                user=agent,
                usage_data={"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0},
                request_message=message,
                response=None,
                session_id=session_id,
                client_ip=client_ip,
                is_successful=False,
                error_message=str(e)
            )
            
            return Response(
                {"error": str(e), "status": "error"}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def delete(self, request, format=None):
        """
        Handle DELETE requests to reset a conversation.
        
        Resets the conversation history for the loan officer.
        """
        # Get the current user's ID (the loan officer/agent)
        agent = request.user
        
        # Extract session_id from query parameters if available
        session_id = request.query_params.get('session_id')
        
        try:
            # Create a handler and reset the conversation
            handler = LoanAgentHandler(agent.id, session_id=session_id)
            result = handler.reset_conversation()
            
            # Return the response
            return Response(result)
            
        except Exception as e:
            logger.exception(f"Error resetting conversation: {str(e)}")
            return Response(
                {"error": str(e), "status": "error"}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def get_client_ip(self, request):
        """
        Get client IP address from request.
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class ConversationHistoryAPIView(ListAPIView):
    """
    API endpoint for retrieving all messages as a flat list.
    Returns all user/assistant messages for the authenticated user in chronological order.
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = MessageSerializer 
    
    from rest_framework.pagination import PageNumberPagination
    
    class ConversationPagination(PageNumberPagination):
        page_size = 20  
        page_size_query_param = 'page_size'
        max_page_size = 100
    
    pagination_class = ConversationPagination
    
    def get_queryset(self):
        """
        Get all messages for the current user as a flat list.
        """
        user = self.request.user
        
        # Order by newest first 
        queryset = Message.objects.filter(
            conversation__user=user
        ).select_related('conversation').order_by('-created_at')
        
        return queryset


class PredefinedQuestionsAPIView(ListAPIView):
    """
    API endpoint for retrieving predefined questions.
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = PredefinedQuestionSerializer
    queryset = PredefinedQuestion.objects.filter(is_active=True).order_by('order', 'question')