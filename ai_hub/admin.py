# ai_hub/admin.py
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.db.models import Count, Sum

from .models import (
    TokenUsageLog, 
    Conversation, 
    Message, 
    PredefinedQuestion
)

@admin.register(TokenUsageLog)
class TokenUsageLogAdmin(admin.ModelAdmin):
    list_display = ('user', 'model_name', 'total_tokens', 'estimated_cost_usd', 
                   'request_time', 'processing_time_ms', 'is_successful')
    list_filter = ('is_successful', 'model_name', 'request_time')
    search_fields = ('user__email', 'request_message', 'error_message')
    readonly_fields = ('total_tokens', 'estimated_cost_usd', 'processing_time_ms')
    fieldsets = (
        ('User Information', {
            'fields': ('user', 'client_ip', 'session_id')
        }),
        ('Token Usage', {
            'fields': ('prompt_tokens', 'completion_tokens', 'total_tokens', 'model_name', 'estimated_cost_usd')
        }),
        ('Request Details', {
            'fields': ('request_message', 'response_summary', 'is_successful', 'error_message')
        }),
        ('Timing', {
            'fields': ('request_time', 'response_time', 'processing_time_ms')
        }),
    )
    date_hierarchy = 'request_time'


# Inline admin for messages within a conversation
class MessageInline(admin.TabularInline):
    model = Message
    readonly_fields = ('created_at', 'token_usage')
    fields = ('role', 'content', 'created_at', 'token_usage')
    extra = 0
    max_num = 50  # Limit the number of inline messages shown
    can_delete = True
    show_change_link = True
    
    def get_queryset(self, request):
        # Order messages by creation time
        return super().get_queryset(request).order_by('created_at')

@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    list_display = ('id', 'conversation_link', 'role', 'short_content', 'created_at', 'token_usage')
    list_filter = ('role', 'created_at')
    search_fields = ('content',)
    readonly_fields = ('created_at', 'token_usage')
    raw_id_fields = ('conversation',)
    
    def short_content(self, obj):
        # Limit the content display to 100 characters
        if len(obj.content) > 100:
            return obj.content[:100] + '...'
        return obj.content
    short_content.short_description = 'Content'
    
    def conversation_link(self, obj):
        # Create a link to the conversation
        url = reverse('admin:ai_hub_conversation_change', args=[obj.conversation.id])
        return format_html('<a href="{}">{}</a>', url, obj.conversation.id)
    conversation_link.short_description = 'Conversation'
    
    def get_queryset(self, request):
        # Order messages by creation time
        return super().get_queryset(request).order_by('-created_at')
    
@admin.register(Conversation)
class ConversationAdmin(admin.ModelAdmin):
    list_display = ('id', 'user_link', 'session_id', 'message_count', 'created_at', 'updated_at')
    list_filter = ('created_at', 'updated_at')
    search_fields = ('session_id', 'user__email')
    readonly_fields = ('created_at', 'updated_at', 'message_count')
    raw_id_fields = ('user',)
    inlines = [MessageInline]
    actions = ['trim_old_messages']
    
    def message_count(self, obj):
        # Get the number of messages in the conversation
        return obj.messages.count()
    message_count.short_description = 'Messages'
    
    def user_link(self, obj):
        # Create a link to the user
        url = reverse('admin:accounts_customuser_change', args=[obj.user.id])
        return format_html('<a href="{}">{}</a>', url, obj.user.email)
    user_link.short_description = 'User'
    
    def get_queryset(self, request):
        # Annotate the queryset with message count for more efficient display
        return super().get_queryset(request).annotate(
            msg_count=Count('messages')
        ).order_by('-updated_at')
    
    def trim_old_messages(self, request, queryset):
        """Admin action to trim old messages from selected conversations."""
        for conversation in queryset:
            conversation.trim_messages(max_messages=50)
        self.message_user(request, f"Successfully trimmed old messages for {queryset.count()} conversations.")
    trim_old_messages.short_description = "Trim old messages (keep latest 50)"
    
    def has_delete_permission(self, request, obj=None):
        # Only allow deletion of entire conversations if the user has staff permissions
        if not request.user.is_staff:
            return False
        return super().has_delete_permission(request, obj)

@admin.register(PredefinedQuestion)
class PredefinedQuestionAdmin(admin.ModelAdmin):
    list_display = ('question', 'description_short', 'order', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('question', 'description')
    list_editable = ('order', 'is_active')
    ordering = ('order', 'question')
    
    def description_short(self, obj):
        # Limit the description display to 100 characters
        if obj.description and len(obj.description) > 100:
            return obj.description[:100] + '...'
        return obj.description
    description_short.short_description = 'Description'

