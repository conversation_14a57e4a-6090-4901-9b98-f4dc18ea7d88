from django.db.models import Count
from django.shortcuts import render
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, serializers, status
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone

from rest_framework.generics import GenericAPIView, ListAPIView
from rest_framework import filters

from rest_framework.response import Response
from rest_framework.views import APIView

from accounts.models import CustomUser
from accounts.permissions import CustomAPIKeyPermission
from accounts.responses import (
    serializer_validation_error_response,
    value_error_response,
)
from ajo.permissions import AjoUserOnboardedPermission
from ajo.selectors import AjoUserSelector
from ajo.services import AjoUserService
from location_app.models import (
    AddressVerificationLog,
    AgentLocation,
    UserTerminalAddressVerification,
)
from location_app.serializers import (
    AddressVerificationLogSerializer,
    UserTerminalAddressVerificationSerializer,
    V2FillPersonalDetailsSerializer,
    V2PingAgentLocationSerializer,
    V2VerifyAgentLocationSerializer,
    V2VerifyUserLocationSerializer,
)
from location_app.tasks import (
    get_agent_current_location,
    get_agent_location_on_registration,
)
from location_app.utils.location_utils import PingDevices
from savings.pagination import CustomPagination

# Create your views here.


class V2FillPersonalDetailsAPIView(GenericAPIView):
    serializer_class = V2FillPersonalDetailsSerializer
    permission_classes = (
        permissions.IsAuthenticated,
        AjoUserOnboardedPermission,
    )

    @swagger_auto_schema(
        request_body=V2FillPersonalDetailsSerializer,
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
        ],
    )
    def post(self, request, *args, **kwargs):
        """
        Fill in the personal details of the Ajo User
        """
        # obtain the phone number from the query params
        phone_number = request.query_params.get("phone_number")

        try:
            ajo_user = AjoUserSelector(
                phone_number=phone_number, user=request.user
            ).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        if ajo_user.onboarding_complete:
            return Response(
                data={
                    "status": False,
                    "message": "User Already Onboarded",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        response_serializer = self.serializer_class(
            AjoUserService(ajo_user=ajo_user).update_ajo_user_fields(
                dict(serializer.validated_data)
            )
        )
        longitude = serializer.validated_data.get("longitude")
        latitude = serializer.validated_data.get("latitude")
        get_agent_location_on_registration.delay(ajo_user.id, longitude, latitude)
        get_agent_current_location.delay(ajo_user.id, longitude, latitude)

        return Response(
            data={
                "status": True,
                "message": "details updated successfully",
                "data": response_serializer.data,
            },
            status=status.HTTP_200_OK,
        )


class V2VerifyAgentLocationAPIView(GenericAPIView):
    serializer_class = V2VerifyAgentLocationSerializer
    permission_classes = (
        permissions.IsAuthenticated,
        AjoUserOnboardedPermission,
    )

    @swagger_auto_schema(
        request_body=V2VerifyAgentLocationSerializer,
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
        ],
    )
    def post(self, request, *args, **kwargs):
        """
        Fill in the personal details of the Ajo User
        """
        # obtain the phone number from the query params
        phone_number = request.query_params.get("phone_number")

        try:
            ajo_user = AjoUserSelector(
                phone_number=phone_number, user=request.user
            ).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")
        if not ajo_user.onboarding_complete:
            return Response(
                data={
                    "status": False,
                    "message": "User not onboarded",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        if ajo_user.location_is_verified:
            return Response(
                data={
                    "status": False,
                    "message": "User address already verified",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = self.serializer_class(
            data=request.data, context={"ajo_user": ajo_user}
        )

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        longitude = serializer.validated_data.get("longitude")
        latitude = serializer.validated_data.get("latitude")
        get_agent_current_location.delay(ajo_user.id, longitude, latitude)
        address_status = serializer.validated_data.get("address_status")
        user_address = serializer.validated_data.get("user_address")
        phone_number = serializer.validated_data.get("phone_number")
        loan_id = serializer.validated_data.get("loan_id")
        if address_status == "CONFIRMED":
            return Response(
                data={
                    "status": True,
                    "user_address": user_address,
                    "loan_id": loan_id,
                    "phone_number": phone_number,
                    "message": "address verified successfully",
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                data={
                    "status": False,
                    "user_address": user_address,
                    "loan_id": loan_id,
                    "phone_number": phone_number,
                    "message": f"address verification {address_status}",
                },
                status=status.HTTP_200_OK,
            )


class V2VerifyUserLocationAPIView(GenericAPIView):
    serializer_class = V2VerifyUserLocationSerializer
    permission_classes = (
        permissions.IsAuthenticated,
        AjoUserOnboardedPermission,
    )

    @swagger_auto_schema(
        request_body=V2VerifyUserLocationSerializer,
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
        ],
    )
    def post(self, request, *args, **kwargs):
        """
        Fill in the personal details of the Ajo User
        """
        # obtain the phone number from the query params
        phone_number = request.query_params.get("phone_number")

        try:
            ajo_user = AjoUserSelector(
                phone_number=phone_number, user=request.user
            ).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        if ajo_user.location_is_verified:
            return Response(
                data={
                    "status": False,
                    "message": "User address already verified",
                    "location_verified": True,
                    "user_address": {
                        "lga": ajo_user.lga,
                        "state": ajo_user.state,
                        "landmark": ajo_user.landmark,
                        "bus_stop": ajo_user.bus_stop,
                        "address": ajo_user.address,
                    },
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = self.serializer_class(
            data=request.data, context={"ajo_user": ajo_user}
        )

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        except ValueError as err:
            return Response(
                {
                    "error": "603",
                    "status": False,
                    "verification_exceeded": True,
                    "message": str(err),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        longitude = serializer.validated_data.get("longitude")
        latitude = serializer.validated_data.get("latitude")
        get_agent_current_location.delay(ajo_user.id, longitude, latitude)
        address_status = serializer.validated_data.get("address_status")
        user_address = serializer.validated_data.get("user_address")
        phone_number = serializer.validated_data.get("phone_number")
        if address_status == "CONFIRMED":
            return Response(
                data={
                    "status": True,
                    "user_address": user_address,
                    "phone_number": phone_number,
                    "message": "address verified successfully",
                    "location_verified": True,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                data={
                    "status": False,
                    "user_address": user_address,
                    "phone_number": phone_number,
                    "message": f"address verification {address_status}",
                    "location_verified": False,
                },
                status=status.HTTP_200_OK,
            )


class V2PingAgentLocationAPIView(GenericAPIView):
    serializer_class = V2PingAgentLocationSerializer
    permission_classes = (permissions.IsAuthenticated,)

    @swagger_auto_schema(
        request_body=V2PingAgentLocationSerializer,
    )
    def post(self, request, *args, **kwargs):
        """
        Ping All Agents locations
        """
        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        longitude = serializer.validated_data.get("longitude")
        latitude = serializer.validated_data.get("latitude")
        terminal_serial_number = serializer.validated_data.get("terminal_serial_number")
        agent_email = request.user.email

        agent = CustomUser.objects.filter(email=agent_email).last()

        AgentLocation.create_or_update_agent_location(
            agent=agent,
            longitude=longitude,
            latitude=latitude,
            terminal_serial_number=terminal_serial_number,
        )

        # get_agent_current_location.delay(agent_email, longitude, latitude)

        return Response(
            data={
                "status": True,
                "message": "success",
            },
            status=status.HTTP_200_OK,
        )


class V2GetAllAgentLocationAPIView(GenericAPIView):
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request, *args, **kwargs):
        """
        Get all agent current location
        """
        locations_data = AgentLocation.objects.all()
        unique_locations_data = locations_data.values("user").annotate(
            total_records=Count("id")
        )
        agents_locations = []
        for agent in unique_locations_data:
            user = agent["user"]
            agent_data = locations_data.filter(user__id=user).last()
            if agent_data:
                agents_locations.append(
                    {
                        "email": agent_data.user.email,
                        "first_name": agent_data.user.first_name,
                        "last_name": agent_data.user.last_name,
                        "user_type": agent_data.user.user_type,
                        "user_branch": agent_data.user.user_branch,
                        "user_phone": agent_data.user.user_phone,
                        "longitude": agent_data.longitude,
                        "latitude": agent_data.latitude,
                    }
                )
        return Response(
            data={
                "status": True,
                "message": "success",
                "agent_locations": agents_locations,
            },
            status=status.HTTP_200_OK,
        )


class UserTerminalAddressVerificationView(APIView):
    permission_classes = [CustomAPIKeyPermission]
    serializer_class = UserTerminalAddressVerificationSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)

        try:

            serializer.is_valid(raise_exception=True)

        except serializers.ValidationError as err:

            return Response(
                {
                    "status": False,
                    "message": str(err),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        business_address = serializer.validated_data["business_address"]
        home_address = serializer.validated_data["home_address"]
        terminal_serial_number = serializer.validated_data["terminal_serial_number"]
        user_email = serializer.validated_data["user_email"]

        verification_result = UserTerminalAddressVerification.verify_address(
            business_address=business_address,
            home_address=home_address,
            terminal_serial_number=terminal_serial_number,
            user_email=user_email,
        )

        if verification_result["is_valid"]:

            return Response(
                {
                    "status": True,
                    "message": verification_result["messages"],
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {
                    "status": False,
                    "message": verification_result["messages"],
                    "data": serializer.data,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )


class GetAgentAddressVerificationLog(ListAPIView):
    pagination_class = CustomPagination
    serializer_class = AddressVerificationLogSerializer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "status",
        "verification_method",
    )
    search_fields = ("ajo_user__phone_number", "ajo_user__user__email")

    def get_queryset(self):

        address_log = AddressVerificationLog.objects.all().order_by("-id")
        return address_log

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)

        response_data = {
            "status": True,
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }

        return Response(response_data, status=status.HTTP_200_OK)
