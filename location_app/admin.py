from django.contrib import admin
from django.db.models.query import QuerySet
from import_export.admin import ImportExportModelAdmin

from location_app.enums import VerificationMethod
from location_app.resources import *

# Register your models here.


class LocationConstantTableResourceAdmin(ImportExportModelAdmin):
    resource_class = LocationConstantTableResource

    # date_hierarchy = 'date_created'
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LocationLongitudeLatitudeInline(admin.TabularInline):
    model = LocationLongitudeLatitude
    readonly_fields = ('created_at', 'updated_at')
    fields = ('longitude', 'latitude', 'created_at', 'updated_at')


class AgentLocationResourceAdmin(ImportExportModelAdmin):
    resource_class = AgentLocationResource
    search_fields = (
        "user__email",
        "terminal_serial_number",
    )
    list_filter = (
        "created_at", 
        "updated_at",
    )
    inlines = [LocationLongitudeLatitudeInline]

    # date_hierarchy = 'date_created'
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AddressVerificationLogResourceAdmin(ImportExportModelAdmin):
    resource_class = AddressVerificationLogResource
    search_fields = (
        "ajo_user__user__email",
        "ajo_user__phone_number",
        "verified_address",
        "address",
    )
    list_filter = (
        "verification_method",
        "status",
        "created_at",
    )

    # date_hierarchy = 'date_created'
    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        data.remove("supporting_image")
        data.remove("result")
        return data

    def manual_verification(self, request, queryset: QuerySet[AddressVerificationLog]):
        message = "Invalid action"
        for instance in queryset:
            if not instance.supporting_image:
                message = "Failed: kindly provide a supporting image"
                continue
            if not instance.verified_address:
                message = "Please enter verified address"
                continue
            if not instance.reason:
                message = "Please provide a valid reason."
                continue
            if AddressVerificationLog.objects.filter(
                status="CONFIRMED",
                verified_address__icontains=instance.verified_address,
            ).exists():
                message = "Address already CONFIRMED for another user"
                continue

            if instance.status == "CONFIRMED":
                message = "address has being verified"
            else:
                ajo_user_instance = instance.ajo_user
                instance.verified_by = request.user
                instance.status = "CONFIRMED"
                instance.verification_method = VerificationMethod.MANUAL
                instance.save()

                ajo_user_instance.onboarding_stage = "ADDRESS_VERIFIED"
                ajo_user_instance.onboarding_complete = True
                ajo_user_instance.verification_retries += 1
                ajo_user_instance.verified_location_lga = instance.lga
                ajo_user_instance.verified_location_state = instance.state
                ajo_user_instance.verified_location_city = instance.city
                ajo_user_instance.verified_location_country = instance.country
                ajo_user_instance.verified_location_address = instance.verified_address

                ajo_user_instance.verification_landmark_list = instance.all_landmark
                ajo_user_instance.save()

                message = "success"

        self.message_user(request, str(message))

    manual_verification.short_description = "verify address"
    manual_verification.allow_tags = True

    actions = [
        manual_verification,
    ]


class UserTerminalAddressVerificationAdmin(ImportExportModelAdmin):
    # resource_class = AddressVerificationLogResource
    search_fields = (
        "terminal_serial_number",
        "user_email",
    )
    # list_filter = ( )

    # date_hierarchy = 'date_created'
    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        # data.remove("")
        return data


admin.site.register(LocationConstantTable, LocationConstantTableResourceAdmin)
admin.site.register(AgentLocation, AgentLocationResourceAdmin)
admin.site.register(AddressVerificationLog, AddressVerificationLogResourceAdmin)
admin.site.register(
    UserTerminalAddressVerification, UserTerminalAddressVerificationAdmin
)
