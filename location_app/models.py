from datetime import timedelta

from django.conf import settings
from django.db import models
from django.utils import timezone
from django.core.cache import cache
from accounts.helpers import BaseModel
from accounts.models import CustomUser
from ajo.models import AjoUser
from helper_methods import validate_address_pattern
from location_app.enums import LocationVerificationStatus, VerificationMethod
from location_app.utils.location_utils import LocationVerificationAPI

# Create your models here.


class LocationConstantTable(models.Model):

    LOCATION_API = (
        ("WEATHER_API", "WEATHER_API"),
        ("OPEN_CAGE_API", "OPEN_CAGE_API"),
        ("OFF", "OFF"),
    )
    ADDRESS_VALIDATION_API = (("GEOCODE_API_KEY", "GEOCODE_API_KEY"),)
    location_api = models.CharField(
        max_length=255, choices=LOCATION_API, default="OPEN_CAGE_API"
    )
    verification_api = models.CharField(
        max_length=255, choices=ADDRESS_VALIDATION_API, default="GEOCODE_API_KEY"
    )
    location_is_active = models.BooleanField(default=True)
    verification_is_active = models.BooleanField(default=True)
    verification_retries = models.IntegerField(default=3)
    score_range = models.FloatField(default=0.7)
    verification_duration = models.DurationField(default=timedelta(minutes=180))
    verification_duration_in_min = models.PositiveBigIntegerField(default=3)
    wait_duration = models.PositiveBigIntegerField(default=2)
    verification_second_address = models.BooleanField(default=False)
    verification_utility_bill = models.BooleanField(default=False)
    user_address_match_percentage = models.PositiveBigIntegerField(default=70)
    allowed_utility_month = models.PositiveBigIntegerField(default=6)
    verification_landmark = models.BooleanField(default=False)
    ping_devices_title = models.CharField(max_length=255, null=True, blank=True)
    ping_devices_body = models.CharField(max_length=255, null=True, blank=True)
    address_pattern = models.CharField(max_length=255, null=True, blank=True)
    uttility_bill_address_pattern = models.CharField(
        max_length=255, null=True, blank=True
    )
    ping_agents = models.BooleanField(default=False)
    use_pattern_match = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "LOCATION VARIABLE"
        verbose_name_plural = "LOCATION VARIABLES"

    @classmethod
    def get_constant_instance(cls):
        """
        This function always returns an instance of the constant table
        """
        # Try to retrieve the cached data
        constant_instance = cache.get("address_verification_constant_table_instance")

        if not constant_instance:
            instance = cls.objects.last()
            if not instance:
                constant_instance = cls.objects.create()
            else:
                constant_instance = instance

        cache.set(
            key="address_verification_constant_table_instance",
            value=constant_instance,
            timeout=60 * 60 * 3,  # Increased timeout to 3 hours
        )

        return constant_instance

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        cache.delete("address_verification_constant_table_instance")


class AgentLocation(models.Model):
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="user_location",
        db_index=True,
        null=True,
        blank=True,
    )
    request_user_email = models.CharField(max_length=300, null=True, blank=True)
    terminal_serial_number = models.CharField(
        max_length=300, null=True, blank=True, editable=False
    )
    longitude = models.FloatField(null=True, blank=True, default=0.0)
    latitude = models.FloatField(null=True, blank=True, default=0.0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "AGENT LOCATION HISTORY"
        verbose_name_plural = "AGENT LOCATION HISTORIES"

    def __str__(self):
        return f"User: ({self.user}, Terminal id: {self.terminal_serial_number})"

    @classmethod
    def create_or_update_agent_location(
        cls, agent, longitude, latitude, terminal_serial_number=None
    ):
        today = timezone.now().date()
        instance = cls.objects.filter(user=agent, created_at__date=today).last()
        if instance:
            instance.longitude = longitude
            instance.latitude = latitude
            # instance.terminal_serial_number = terminal_serial_number
            # instance.request_user_email = agent.email if agent else None
            instance.save(
                update_fields=[
                    "longitude",
                    "latitude",
                ]
            )
        else:
            instance = cls.objects.create(
                user=agent,
                created_at=today,
                longitude=longitude,
                latitude=latitude,
                terminal_serial_number=terminal_serial_number,
                request_user_email=agent.email if agent else None,
            )

       
        # Create location longitude latitude record
        LocationLongitudeLatitude.objects.create(
            user_location=instance, longitude=longitude, latitude=latitude
        )

        return instance


class LocationLongitudeLatitude(models.Model):
    user_location = models.ForeignKey(
        to=AgentLocation,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    longitude = models.FloatField(null=True, blank=True, default=0.0)
    latitude = models.FloatField(null=True, blank=True, default=0.0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "LOCATION LONGITUDE AND LATITUDE"
        verbose_name_plural = "LOCATION LONGITUDE AND LATITUDE"
        ordering = ["-updated_at"]  # Orders by updated_at in descending order

    def __str__(self):
        return (
            f"Location ({self.latitude}, {self.longitude}) - Updated: {self.updated_at}"
        )


class AddressVerificationLog(BaseModel):
    ajo_user = models.ForeignKey(
        AjoUser, editable=False, on_delete=models.CASCADE, null=True, blank=True
    )
    terminal_user = models.ForeignKey(
        "UserTerminalAddressVerification",
        editable=False,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    verified_by = models.ForeignKey(
        CustomUser, on_delete=models.CASCADE, editable=False, null=True, blank=True
    )
    longitude = models.CharField(max_length=300, null=True, blank=True)
    latitude = models.CharField(max_length=300, null=True, blank=True)
    status = models.CharField(
        max_length=100,
        choices=LocationVerificationStatus.choices,
        null=True,
        blank=True,
        editable=False,
    )
    verification_method = models.CharField(
        max_length=100,
        choices=VerificationMethod.choices,
        default=VerificationMethod.AUTO,
        editable=False,
    )
    address = models.CharField(max_length=300, editable=False, null=True, blank=True)
    street = models.CharField(max_length=300, editable=False, null=True, blank=True)
    lga = models.CharField(max_length=300, editable=False, null=True, blank=True)
    city = models.CharField(max_length=300, editable=False, null=True, blank=True)
    state = models.CharField(max_length=300, editable=False, null=True, blank=True)
    country = models.CharField(max_length=300, editable=False, null=True, blank=True)
    score = models.CharField(max_length=300, editable=False, null=True, blank=True)
    all_landmark = models.CharField(
        max_length=300, editable=False, null=True, blank=True
    )
    address_line = models.CharField(
        max_length=300, editable=False, null=True, blank=True
    )
    position_lat = models.CharField(
        max_length=300, editable=False, null=True, blank=True
    )
    position_lng = models.CharField(
        max_length=300, editable=False, null=True, blank=True
    )
    access_lat = models.CharField(max_length=300, editable=False, null=True, blank=True)
    access_lng = models.CharField(max_length=300, editable=False, null=True, blank=True)
    verified_address = models.CharField(max_length=300, null=True, blank=True)
    supporting_image = models.TextField(null=True, blank=True)
    result = models.TextField(null=True, blank=True)
    reason = models.TextField(null=True, blank=True)

    def __str__(self):
        return f"status: {self.status}, score: {self.score}"

    class Meta:
        verbose_name = "Address Verification Log"
        verbose_name_plural = "Address Verification Log"

    @classmethod
    def openai_log(
        cls,
        address,
        longitude,
        latitude,
        bus_stop,
        landmark,
        lga,
        state,
        ajo_user,
        supporting_doc,
        ai_result,
    ):
        address_line = f"{address} {bus_stop} {landmark} {lga} {state}, Nigeria"
        log_instance = cls.objects.create(
            address_line=address_line,
            longitude=longitude,
            latitude=latitude,
            address=address,
            # bus_stop=bus_stop,
            verification_method="OPEN_AI",
            # landmark=landmark,
            state=state,
            lga=lga,
            ajo_user=ajo_user,
            supporting_image=supporting_doc,
            result=ai_result,
        )
        return log_instance

    @classmethod
    def verify_address(
        cls,
        address,
        bus_stop,
        landmark,
        lga,
        state,
        ajo_user: AjoUser,
        longitude,
        latitude,
    ):
        address_line = f"{address} {bus_stop} {landmark} {lga} {state}, Nigeria"
        result = LocationVerificationAPI().geocode_verification_api(
            address_line=address_line
        )
        const = LocationConstantTable.get_constant_instance()
        log_instance = cls.objects.create(
            ajo_user=ajo_user, longitude=longitude, latitude=latitude, result=result
        )
        if result.get("status") == "success":
            response = result.get("response")
            all_address_validation = response.get("items")

            all_landmark = []
            # confirmed_validation = None
            # plausible_validation = None
            verification_result = None

            for validation in all_address_validation:
                title = validation.get("title")
                position = validation.get("position")
                access = validation.get("access")
                street = validation.get("address").get("street")
                lga = validation.get("address").get("county")
                city = validation.get("address").get("city")
                state = validation.get("address").get("state")
                country = validation.get("address").get("countryName")
                score = validation.get("scoring").get("queryScore") or 0
                all_landmark.append(lga)

                if score >= const.score_range:
                    verification_result = {
                        "status": "CONFIRMED",
                        "street": street,
                        "city": city,
                        "position": position,
                        "access": access,
                        "lga": lga,
                        "state": state,
                        "country": country,
                        "address": title,
                        "score": score,
                        "all_landmark": all_landmark,
                        "message": "address verified successfully",
                        # "data": response,
                    }

                else:
                    verification_result = {
                        "status": "UNCONFIRMED",
                        "street": street,
                        "city": city,
                        "lga": lga,
                        "position": position,
                        "access": access,
                        "state": state,
                        "country": country,
                        "address": title,
                        "score": score,
                        "all_landmark": all_landmark,
                        "message": "low address data for verification",
                        # "data": response,
                    }
            if verification_result is not None:

                position = verification_result.get("position")
                access = verification_result.get("access")

                if isinstance(access, list) and len(access) > 0:
                    log_instance.access_lat = access[0].get("lat")
                    log_instance.access_lng = access[0].get("lng")
                elif isinstance(access, dict):
                    log_instance.access_lat = access.get("lat")
                    log_instance.access_lng = access.get("lng")

                if isinstance(position, list) and len(position) > 0:
                    log_instance.position_lat = position[0].get("lat")
                    log_instance.position_lng = position[0].get("lng")
                elif isinstance(position, dict):
                    log_instance.position_lat = position.get("lat")
                    log_instance.position_lng = position.get("lng")

                log_instance.status = verification_result.get("status")
                log_instance.address = verification_result.get("address")
                log_instance.street = verification_result.get("street")
                log_instance.lga = verification_result.get("lga")
                log_instance.city = verification_result.get("city")
                log_instance.state = verification_result.get("state")
                log_instance.country = verification_result.get("country")
                log_instance.score = verification_result.get("score")
                log_instance.address_line = address_line
                log_instance.all_landmark = verification_result.get("all_landmark")
                log_instance.save()

                if verification_result["status"] == "CONFIRMED":

                    ajo_user.verification_retries += 1
                    ajo_user.verified_location_lga = verification_result.get("lga")
                    ajo_user.verified_location_state = verification_result.get("state")
                    ajo_user.verified_location_city = verification_result.get("city")
                    ajo_user.verified_location_country = verification_result.get(
                        "country"
                    )
                    ajo_user.verified_location_address = verification_result.get(
                        "address"
                    )
                    ajo_user.verification_status = verification_result.get("status")
                    ajo_user.verification_data = verification_result.get("data")
                    ajo_user.verification_score = verification_result.get("score")
                    ajo_user.verification_message = verification_result.get("message")
                    ajo_user.verification_landmark_list = verification_result.get(
                        "all_landmark"
                    )

                    ajo_user.location_verification_is_started = True
                    ajo_user.save(
                        update_fields=[
                            "verification_retries",
                            "verified_location_lga",
                            "verified_location_state",
                            "verified_location_city",
                            "verified_location_country",
                            "verified_location_address",
                            "verification_status",
                            "verification_data",
                            "verification_score",
                            "verification_message",
                            "verification_landmark_list",
                            "location_verification_is_started",
                        ]
                    )
                else:

                    ajo_user.verification_retries += 1
                    ajo_user.location_address = verification_result.get("address")
                    ajo_user.location_state = verification_result.get("state")
                    ajo_user.location_city = verification_result.get("city")
                    ajo_user.location_country = verification_result.get("country")

                    ajo_user.longitude = longitude
                    ajo_user.latitude = latitude

                    ajo_user.save(
                        update_fields=[
                            "verification_retries",
                            "location_address",
                            "location_state",
                            "location_city",
                            "location_country",
                            "longitude",
                            "latitude",
                        ]
                    )
            return verification_result
        else:
            return None


class UserTerminalAddressVerification(models.Model):
    terminal_serial_number = models.CharField(max_length=300, unique=True)
    user_email = models.EmailField()
    business_address = models.CharField(max_length=300, null=True, blank=True)
    home_address = models.CharField(max_length=300, null=True, blank=True)
    verification_status = models.CharField(
        max_length=100,
        choices=[
            ("PENDING", "PENDING"),
            ("CONFIRMED", "CONFIRMED"),
            ("UNCONFIRMED", "UNCONFIRMED"),
        ],
        default="PENDING",
    )
    business_address_verification_score = models.FloatField(default=0.0)
    home_address_verification_score = models.FloatField(default=0.0)
    validation_message = models.CharField(max_length=300, null=True, blank=True)
    verification_count = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return f"Email: {self.user_email} Serial Number: {self.terminal_serial_number}"

    class Meta:
        verbose_name = "User Terminal Address Verification"
        verbose_name_plural = "User Terminal Address Verifications"

    @classmethod
    def create_or_update(
        cls,
        terminal_serial_number: str,
        user_email: str,
        business_address: str,
        home_address: str,
    ) -> tuple:

        obj, created = cls.objects.update_or_create(
            terminal_serial_number=terminal_serial_number,
            defaults={
                "user_email": user_email,
                "business_address": business_address,
                "home_address": home_address,
            },
        )
        return obj, created

    @classmethod
    def verify_address(
        cls, business_address, home_address, terminal_serial_number, user_email
    ):
        existing_verification_instance = cls.objects.filter(
            terminal_serial_number=terminal_serial_number
        ).first()

        if (
            existing_verification_instance
            and existing_verification_instance.verification_status == "CONFIRMED"
        ):
            return {
                "is_valid": True,
                "messages": ["Address verification is already confirmed."],
            }

        instance, created = cls.create_or_update(
            terminal_serial_number=terminal_serial_number,
            user_email=user_email,
            business_address=business_address,
            home_address=home_address,
        )

        const = LocationConstantTable.get_constant_instance()
        addresses = [business_address, home_address]
        verification_result = {"is_valid": False, "messages": []}
        valid_address_pattern = []

        # Validate addresses first
        for address in addresses:
            try:
                validate_address_pattern(address=address, pattern=const.address_pattern)
                valid_address_pattern.append(
                    address
                )  # Only append if validation succeeds
            except ValueError as err:
                msg = str(err)
                instance.validation_message = msg
                instance.verification_status = "UNCONFIRMED"
                instance.save(
                    update_fields=["validation_message", "verification_status"]
                )
                return {"is_valid": False, "messages": [msg]}

        valid_checks = []
        all_landmarks = []

        # Process validated addresses
        for index, address in enumerate(valid_address_pattern):
            try:
                result = LocationVerificationAPI().geocode_verification_api(
                    address_line=address
                )
                if result.get("status") != "success":
                    raise ValueError(
                        "Unable to verify address. Please contact support."
                    )

                response = result.get("response", {})
                all_address_validation = response.get("items", [])

                # Extract first validation result (assuming single best match)
                validation = all_address_validation[0] if all_address_validation else {}
                address_data = validation.get("address", {})

                score = validation.get("scoring", {}).get("queryScore", 0)
                is_valid = score >= const.score_range

                valid_checks.append(is_valid)
                all_landmarks.append(address_data.get("county", ""))

                verification_status = "CONFIRMED" if is_valid else "UNCONFIRMED"
                message = (
                    "Address verified successfully"
                    if is_valid
                    else "Low address data for verification"
                )
                verification_result["messages"].append(message)

                log_instance = AddressVerificationLog.objects.create(
                    terminal_user=instance,
                    result=result,
                    status=verification_status,
                    score=score,
                    address=validation.get("title", ""),
                    street=address_data.get("street", ""),
                    city=address_data.get("city", ""),
                    lga=address_data.get("county", ""),
                    state=address_data.get("state", ""),
                    country=address_data.get("countryName", ""),
                    all_landmark=all_landmarks,
                )

                # Handle position and access coordinates
                for key in ["position", "access"]:
                    value = validation.get(key, [])
                    if isinstance(value, list) and value:
                        setattr(log_instance, f"{key}_lat", value[0].get("lat"))
                        setattr(log_instance, f"{key}_lng", value[0].get("lng"))
                    elif isinstance(value, dict):
                        setattr(log_instance, f"{key}_lat", value.get("lat"))
                        setattr(log_instance, f"{key}_lng", value.get("lng"))

                log_instance.save()

                # Update verification scores efficiently
                if index == 0:
                    instance.business_address_verification_score = score
                else:
                    instance.home_address_verification_score = score

            except Exception as err:
                verification_result["messages"].append(str(err))
                verification_result["is_valid"] = False
                return verification_result  # Fail fast if a critical issue occurs

        # Final verification status check
        instance.verification_count += 1
        if all(valid_checks):
            verification_result["is_valid"] = True
            instance.verification_status = "CONFIRMED"
        else:
            instance.verification_status = "UNCONFIRMED"

        instance.validation_message = verification_result["messages"]
        instance.save(
            update_fields=[
                "verification_status",
                "validation_message",
                "verification_count",
                "business_address_verification_score",
                "home_address_verification_score",
            ]
        )

        return verification_result
