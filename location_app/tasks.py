from celery import shared_task
from accounts.models import CustomUser
from ajo.models import AjoUser
from location_app.models import AgentLocation, LocationConstantTable

from location_app.utils.location_utils import LocationAPI, PingDevices
from django.contrib.auth import get_user_model

CustomUser = get_user_model()


@shared_task
def get_agent_location_on_registration(agent_id, longitude, latitude):
    agent = AjoUser.objects.get(id=agent_id)
    const_data = LocationConstantTable.get_constant_instance()
    if const_data.location_is_active:
        location_api = LocationAPI()
        get_api = const_data.location_api
        if get_api == "WEATHER_API":
            response = location_api.weather_location_api(longitude, latitude)
            agent.longitude = longitude
            agent.latitude = latitude
            if response.get("status") == "success":
                agent.location_city = response.get("city")
                agent.location_state = response.get("state")
                agent.location_country = response.get("country")
                agent.location_address = response.get("address")
            agent.onboarding_complete = True
            agent.save()

        elif get_api == "OPEN_CAGE_API":
            response = location_api.open_cage_location_api(longitude, latitude)
            agent.longitude = longitude
            agent.latitude = latitude
            if response.get("status") == "success":
                agent.location_city = response.get("city")
                agent.location_state = response.get("state")
                agent.location_country = response.get("country")
                agent.location_address = response.get("address")
            agent.onboarding_complete = True
            agent.save()
        else:
            agent.longitude = longitude
            agent.latitude = latitude
            agent.onboarding_complete = True
            agent.save()


@shared_task
def get_agent_current_location(agent_email, longitude, latitude):
    agent = CustomUser.objects.filter(email=agent_email).last()
    if agent:
        AgentLocation.create_agent_location(agent, longitude, latitude)
        return "success"
    else:
        return "failed"


@shared_task
def ping_devices_location():
    const_data = LocationConstantTable.get_constant_instance()
    if const_data.ping_agents:
        title = const_data.ping_devices_title
        body = const_data.ping_devices_body

        all_agents = CustomUser.objects.all()
        for agent in all_agents:
            email = agent.email
            ping_data = PingDevices.ping_ajo_user_device(email, title, body)

        const_data.ping_agents = False
        const_data.save()
    return "PING AJO USERS SUCCESSFULLY"
