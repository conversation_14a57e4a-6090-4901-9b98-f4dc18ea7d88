from django.urls import path
from location_app import views

location = [
    path(
        "agent/onboarding/fill-personal-details/",
        views.V2FillPersonalDetailsAPIView.as_view(),
        name="v2_fill_personal_details",
    ),
    path(
        "agent/onboarding/validate-address/",
        views.V2VerifyAgentLocationAPIView.as_view(),
        name="v2_verify_personal_details",
    ),
    path(
        "agent/onboarding/validate-user-address/",
        views.V2VerifyUserLocationAPIView.as_view(),
        name="v2_verify_personal_details",
    ),
    path(
        "agent/onboarding/ping-location/",
        views.V2PingAgentLocationAPIView.as_view(),
        name="v2_ping_location",
    ),
]
get_all_agent_location = [
    path(
        "agent/all_agent_location/",
        views.V2GetAllAgentLocationAPIView.as_view(),
        name="v2_get_agent_locations",
    ),
]

liberty_pay_terminal = [
    path(
        "liberty-pay-user/address-verification/",
        views.UserTerminalAddressVerificationView.as_view(),
        name="",
    ),
]
admin_calls = [
    path(
        "get-address-logs/",
        views.GetAgentAddressVerificationLog.as_view(),
        name="",
    ),
]
urlpatterns = [
    *location,
    *get_all_agent_location,
    *liberty_pay_terminal,
    *admin_calls,
]
