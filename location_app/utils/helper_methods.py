import base64
from django.utils import timezone
from datetime import datetime, timedelta

import requests
from dateutil.relativedelta import relativedelta
from django.core.cache import cache
from django.conf import settings
from fuzzywuzzy import fuzz, process
from rest_framework.exceptions import ValidationError
import json


def handle_user_request(user_id, initial_request_duration=2, wait_duration=2):
    """
    Handles user requests with a time-limited first request period and an enforced wait period.

    Parameters:
    - user_id (str): The unique identifier for the user.
    - initial_request_duration (int): Duration in minutes for the initial request period before wait.
    - wait_duration (int): Duration in minutes for the wait period after the initial period.
    """

    # Set unique cache keys based on the user_id
    start_time_key = f"user_request_start_time_{user_id}"
    wait_time_key = f"user_wait_time_{user_id}"

    # Check if the user is in the wait period
    if cache.get(wait_time_key):
        raise ValidationError(f"wait for {wait_duration} minutes and try again")

    # Retrieve the start time from the cache
    start_time = cache.get(start_time_key)

    # If there's no start time, this is the first request attempt
    if not start_time:
        start_time = timezone.now()
        cache.set(start_time_key, start_time)
    else:
        # Calculate the elapsed time since the initial start time
        elapsed_time = timezone.now() - start_time

        # If more than the initial request period has passed, set the wait time
        if elapsed_time >= timedelta(minutes=initial_request_duration):
            # Clear the start time and set the wait time
            cache.delete(start_time_key)
            cache.set(
                wait_time_key, True, timeout=wait_duration * 60
            )  # Set the wait time for `wait_duration`
            raise ValidationError(f"wait for {wait_duration} minutes and try again")

    # If within the first initial request duration, continue with the request
    return "Request processed successfully."


def is_utility_bill_expired(utility_bill_date, allowed_utility_month):

    # Convert the date string to a datetime object
    date_obj = datetime.strptime(utility_bill_date, "%Y-%m-%d")

    # Get the current date
    current_date = datetime.now()

    # Calculate the date 6 months ago from today
    six_months_ago = current_date - relativedelta(months=allowed_utility_month)

    # Check if the date is more than 6 months ago
    if date_obj < six_months_ago:
        is_expired = True
    else:
        is_expired = False
    return is_expired


def standardize_address(address):
    address = address.upper().replace(".", "").replace(",", "")
    address = " ".join(address.split())  # remove extra spaces
    return address


def address_match_percentage(utility_bill_address, user_home_address):
    standardized_address1 = standardize_address(utility_bill_address)
    standardized_address2 = standardize_address(user_home_address)

    # Compare addresses
    similarity_ratio = fuzz.ratio(standardized_address1, standardized_address2)

    return similarity_ratio


def base_64_image_encoder(image):
    try:
        encoded_string = base64.b64encode(image.read())
        data = encoded_string.decode("utf-8")
    except Exception as err:
        raise ValueError(str(err))

    return data


def validate_utility_bill_with_chagpt(base64_image):
    """
    Makes a call to OpenAI's API to validate the ID document type using an image.
    """

    # Get the list of API keys from settings, split by comma
    api_key = str(settings.OPENAI_API_KEY)

    # OpenAI API request headers
    headers = {"Content-Type": "application/json", "Authorization": f"Bearer {api_key}"}

    # Construct the request payload
    payload = {
        "model": "gpt-4o-mini",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": f"""Extract information from the provided utility bill image and return a single JSON object with the following structure:

                                {{
                                    "Date": "The date when the bill was issued in YYYY-MM-DD format (correct month spelling, exclude time).",
                                    "Vendor": "The name of the utility company (e.g., Ikeja Electric, Eko Electricity Distribution, etc.).",
                                    "Address": "The full service address where the utility is being provided. This should include the house/building number, street name, city, and state. **Do NOT include account holder names, honorifics (e.g., ALH., MR., MRS.), or personal identifiers**. Only extract the geographical location."

                                    "Due Date": "The due date of the bill in YYYY-MM-DD format.",
                                    "URL": "The company website or payment link if available, else return N/A."
                                }}

                                Rules for Extraction:
                                1. **Address Completeness**: Ensure house/building number and full street name are captured.
                                2. **Date Correction**: Convert the month to the correct spelling if needed.
                                3. **Vendor Identification**: Extract vendor names from logos, headers, or URLs.

                                Return only the JSON object, with no markdown formatting or additional explanations.
                                """,
                        # "Address": "The full service address. Ensure the extraction includes all parts: name, house/building number, street name, city, and state. Do NOT truncate the first part of the address if it's a name or number (e.g., 'Alhi. Balogun / 58 Ajenifuja Street' should be extracted in full).",
                        # "text": f"What document type of Utility Bill is this in Nigeria?"
                        # "You have to extract information from it to return a json only data, below are the keys needed to be extracted if found otherwise keep N/A\n"
                        # "Your entire response/output is going to consist of a single JSON object {}, and you will NOT wrap it within JSON md markers or in a list\n"
                        # '''Example of the response structure:\n
                        # {\n
                        #     "Date": "The date when the expense was incurred. (do correction in spelling of month if required and do not include time) the format for the date should be "YYYY-MM-DD",\n
                        #     "Vendor": "The name of the vendor or supplier from whom the goods or services were purchased (this information can also extract from URL and might be before .com or .org and not include address in it)",\n
                        #     "Address": "The location where the expense occurred (e.g address)".\n
                        # }\n\
                        # '''
                    },
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"},
                    },
                ],
            }
        ],
        # "max_tokens": 300
    }

    try:
        # Make the API call
        response = requests.post(
            "https://api.openai.com/v1/chat/completions", headers=headers, json=payload
        )
        # Check if the response was successful
        if response.status_code == 200:
            response_data = response.json()

            # The response content might be a string, so parse it into JSON if necessary
            content = (
                response_data.get("choices", [{}])[0]
                .get("message", {})
                .get("content", {})
            )
            if isinstance(content, str):
                try:
                    content = json.loads(content)  # Parse the string as JSON
                except json.JSONDecodeError:
                    return {
                        "status": False,
                        "message": "an error occurred, try again!!!",
                        "image_data": "",
                    }

            content["status"] = True
            content["message"] = "success"
            content["image_data"] = f"data:image/jpeg;base64,{base64_image}"
            return content  # Return the parsed JSON response

        else:
            print(
                f"Failed to validate document type with API key {api_key}. Status code: {response.status_code}"
            )
            print(f"Response: {response.text}")

    except requests.RequestException as e:
        # Log any request errors and try the next key
        print(f"Error during API call with API key {api_key}: {e}")

    # If all API keys fail, return an unknown document type error
    return {
        "status": False,
        "message": "an error occurred, try again!!!",
        "image_data": "",
    }
