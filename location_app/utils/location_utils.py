import json
import os

import requests
from django.conf import settings


class LocationAPI:

    @classmethod
    def weather_location_api(cls, longitude, latitude):
        WEATHER_API_KEY = settings.WEATHER_API_KEY
        response = requests.get(
            f"https://api.weatherapi.com/v1/forecast.json?key={WEATHER_API_KEY}&q={latitude},{longitude}&days=1&aqi=no&alerts=no"
        )
        try:
            resp = response.json()
            location = resp.get("location")
            name = location.get("name")
            region = location.get("region")
            country = location.get("country")
            return {
                "status": "success",
                "city": name,
                "state": region,
                "country": country,
                "address": f"{name},{region},{country}",
            }
        except requests.exceptions.RequestException as e:
            return {
                "status": "failed",
                "error": "Unknown Weather API error",
                "message": f"{e}",
            }

    @classmethod
    def open_cage_location_api(cls, longitude, latitude):
        OPEN_CAGE_API_KEY = settings.OPEN_CAGE_API_KEY
        response = requests.get(
            f"https://api.opencagedata.com/geocode/v1/json?q={latitude},{longitude}&key={OPEN_CAGE_API_KEY}&language=en&pretty=1"
        )
        try:
            resp = response.json()
            if resp.get("status").get("code") == 200:
                results = resp.get("results")[0]
                city = results.get("components").get("county")
                state = results.get("components").get("state")
                country = results.get("components").get("country")
                address = results.get("formatted")
                return {
                    "status": "success",
                    "city": city,
                    "state": state,
                    "country": country,
                    "address": address,
                }
            else:
                return {
                    "status": "failed",
                    "error": "OpenCage API broken",
                    "message": "No response from OpenCage API",
                }
        except requests.exceptions.RequestException as e:
            return {
                "status": "failed",
                "error": "Unknown OpenCage API error",
                "message": f"{e}",
            }


class LocationVerificationAPI:

    @classmethod
    def request_handler(cls, payload, response, url):
        """
        Handles the response from API requests, returning a structured result.

        Args:
            payload (dict): The data sent with the request.
            response (requests.Response): The response object returned by the API.
            url (str): The URL that was called.

        Returns:
            dict: A dictionary containing the request result, including status, response,
                  and other related information.
        """
        try:
            response = {
                "url": url,
                "status_code": response.status_code,
                "status": "success",
                "response": response.json(),
                "method": response.request.method,
                "payload": payload,
            }
        except (
            requests.exceptions.RequestException,
            Exception,
            json.decoder.JSONDecodeError,
        ) as e:
            response = {
                "url": url,
                "error": str(e),
                "status_code": response.status_code,
                "status": "failed",
                "method": response.request.method,
                "response": response.text,
                "payload": payload,
            }
        return response

    @classmethod
    def google_verification_api(cls, address_line, state):
        GOOGLE_VERIFICATION_API_KEY = settings.GOOGLE_VERIFICATION_API_KEY
        header = {"Content-Type": "application/json"}
        data = {
            "address": {"locality": f"{state}", "addressLines": [f"{address_line}"]}
        }
        response = requests.post(
            f"https://addressvalidation.googleapis.com/v1:validateAddress?key={GOOGLE_VERIFICATION_API_KEY}",
            headers=header,
            json=data,
        )
        try:
            resp = response.json()
            if resp.get("result").get("address").get("addressComponents"):
                try:
                    address_validation = (
                        resp.get("result").get("address").get("addressComponents")[0]
                    )

                    if address_validation.get("confirmationLevel") == "CONFIRMED":
                        return {
                            "status": "CONFIRMED",
                            "message": "address verified successfully",
                            "data": address_validation,
                        }
                    elif (
                        address_validation.get("confirmationLevel")
                        == "UNCONFIRMED_BUT_PLAUSIBLE"
                    ):
                        return {
                            "status": "UNCONFIRMED_BUT_PLAUSIBLE",
                            "message": "address could not be confirmed, but it is plausible that it exists",
                            "data": address_validation,
                        }
                    elif (
                        address_validation.get("confirmationLevel")
                        == "CONFIRMATION_LEVEL_UNSPECIFIED"
                    ):
                        return {
                            "status": "CONFIRMATION_LEVEL_UNSPECIFIED",
                            "message": "address was not verified",
                            "data": address_validation,
                        }
                    elif (
                        address_validation.get("confirmationLevel")
                        == "UNCONFIRMED_AND_SUSPICIOUS"
                    ):
                        return {
                            "status": "UNCONFIRMED_AND_SUSPICIOUS",
                            "message": "address is suspicious",
                            "data": address_validation,
                        }
                    else:
                        return {
                            "status": "UNKNOWN",
                            "message": "an error occurred",
                            "data": address_validation,
                        }
                except AttributeError as e:
                    return {"status": "UNKNOWN", "message": f"{e}", "data": {}}
            else:
                return {"status": "UNKNOWN", "message": "an error occurred", "data": {}}
        except requests.exceptions.RequestException as e:
            return {"status": "FAILED", "message": f"{e}", "data": {}}
        except AttributeError as e:
            return {"status": "FAILED", "message": f"{e}", "data": {}}

    @classmethod
    def geocode_verification_api(cls, address_line):
        GEOCODE_API_KEY = settings.GEOCODE_API_KEY
        url = f"https://geocode.search.hereapi.com/v1/geocode?apiKey={GEOCODE_API_KEY}&q={address_line}"
        response = requests.get(url)
        return cls.request_handler(payload={}, response=response, url=url)


class PingDevices:

    @classmethod
    def ping_ajo_user_device(cls, email, title, body):
        AGENCY_BANKING_BASE_URL = settings.AGENCY_BANKING_BASE_URL
        header = {"Content-Type": "application/json"}
        data = {"email": email, "title": title, "body": body}
        url = f"{AGENCY_BANKING_BASE_URL}/agency/admin/ping_devices/"
        response = requests.post(url=url, headers=header, json=data)
        try:
            resp = response.json()
            if resp.get("status") == "success":
                return {
                    "status": "success",
                    "message": "devices pinged successfully",
                    "data": resp,
                }
            else:
                return {
                    "status": "failed",
                    "message": "could not ping devices",
                    "data": resp,
                }
        except requests.exceptions.RequestException as e:
            return {"status": "failed", "message": f"{e}", "data": {}}
        except AttributeError as e:
            return {"status": "failed", "message": f"{e}", "data": {}}


def upload_utility_image(utility_bill, ajo_user):
    from ajo.services import AjoUserService

    valid_extensions = [".jpg", ".jpeg", ".png", ".gif"]
    extension = os.path.splitext(utility_bill.name)[1].lower()
    if extension not in valid_extensions:
        data = {
            "status": False,
            "is_valid": False,
            "message": "invalid image file. Only JPG, JPEG, PNG and GIF files are allowed",
        }
    save_util = AjoUserService(ajo_user=ajo_user).save_utility_bill_for_ajo_user(
        image=utility_bill
    )
    if save_util.get("status"):
        data = {
            "status": True,
            "is_valid": True,
            "message": "image uploaded successfully",
        }
    else:
        data = {
            "status": False,
            "is_valid": True,
            "message": "an error occurred while uploading image",
        }
    return data
