import os

from django.utils import timezone
from rest_framework import serializers

from ajo.model_choices import Gender
from ajo.models import AjoUser
from ajo.services import AjoUserService
from helper_methods import validate_address_pattern
from loans.models import <PERSON>jo<PERSON>oan
from location_app.models import (
    AddressVerificationLog,
    LocationConstantTable,
)
from location_app.utils.helper_methods import (
    base_64_image_encoder,
    handle_user_request,
    is_utility_bill_expired,
    validate_utility_bill_with_chagpt,
)
from location_app.utils.location_utils import (
    LocationVerificationAPI,
    upload_utility_image,
)


class AddressVerificationLogSerializer(serializers.ModelSerializer):
    class Meta:
        model = AddressVerificationLog
        fields = "__all__"

    def to_representation(self, instance: AddressVerificationLog):
        representation = super().to_representation(instance)
        ajo_user: AjoUser = instance.ajo_user

        representation["borrower_fullname"] = ajo_user.fullname
        representation["borrower_phone"] = ajo_user.phone

        return representation


class V2FillPersonalDetailsSerializer(serializers.ModelSerializer):
    gender = serializers.CharField()
    first_name = serializers.CharField()
    gender = serializers.CharField()
    alias = serializers.Char<PERSON>ield(required=False, allow_null=True)
    bvn = serializers.CharField(required=False, allow_null=True)
    nin = serializers.CharField(required=False, allow_null=True)

    def validate(self, attrs):
        first_name = attrs.get("first_name")
        last_name = attrs.get("last_name")
        gender = attrs.get("gender", "")
        longitude = attrs.get("longitude")
        latitude = attrs.get("latitude")

        if not longitude:
            raise serializers.ValidationError("longitude is required")
        if not latitude:
            raise serializers.ValidationError("latitude is required")

        if not first_name.isalpha() or not last_name.isalpha():
            raise serializers.ValidationError(
                "please ensure that first name and last name are only alphabets with no space or special characters"
            )

        if gender.upper() not in Gender:
            raise serializers.ValidationError("please choose male or female for gender")
        else:
            attrs["gender"] = getattr(Gender, gender.upper())
        return attrs

    class Meta:
        model = AjoUser
        fields = [
            "first_name",
            "last_name",
            "alias",
            "gender",
            "marital_status",
            "state",
            "lga",
            "address",
            "trade",
            "trade_location",
            "bvn",
            "nin",
            "longitude",
            "latitude",
        ]


class V2VerifyAgentLocationSerializer(serializers.Serializer):
    longitude = serializers.CharField(required=True)
    latitude = serializers.CharField(required=True)
    loan_id = serializers.CharField(required=True)
    state = serializers.CharField(required=False, allow_null=True)
    lga = serializers.CharField(required=False, allow_null=True)
    address = serializers.CharField(required=False, allow_null=True)
    landmark = serializers.CharField(required=False, allow_null=True)
    bus_stop = serializers.CharField(required=False, allow_null=True)
    util_image = serializers.FileField(
        required=False, max_length=None, allow_empty_file=False, use_url=False
    )

    def validate(self, attrs):
        loan_id = attrs.get("loan_id")
        const_data = LocationConstantTable.get_constant_instance()
        ajo_user: AjoUser = self.context.get("ajo_user")
        try:
            ajo_loan = AjoLoan.objects.get(id=loan_id)
        except AjoLoan.DoesNotExist:
            raise serializers.ValidationError("no loan record found for this user")
        user_verification_stage = ajo_loan.verification_stage
        if user_verification_stage not in [
            "ADDRESS_VERIFICATION",
            "ADDRESS_VALIDATION",
            "NEW_ADDRESS_VERIFICATION",
        ]:
            raise serializers.ValidationError("user verification stage is invalid")
        if not ajo_user.location_verification_is_started:
            if not ajo_user.address or not ajo_user.lga or not ajo_user.state:
                ajo_user.location_verification_is_started = True
                ajo_user.verification_retries += 1
                ajo_user.save()
                ajo_loan.verification_stage = "ADDRESS_VALIDATION"
                ajo_loan.save()
                attrs["address_status"] = "FAILED"
                attrs["user_address"] = {
                    "lga": ajo_user.lga,
                    "state": ajo_user.state,
                    "landmark": ajo_user.landmark,
                    "bus_stop": ajo_user.bus_stop,
                    "address": ajo_user.address,
                }
                attrs["phone_number"] = ajo_user.phone_number
            else:
                end_date = timezone.localtime()
                address_line = (
                    f"{ajo_user.address} {ajo_user.lga} {ajo_user.state} Nigeria"
                )
                verified_data = AddressVerificationLog.verify_address(
                    address=ajo_user.address,
                    bus_stop=bus_stop,
                    landmark=landmark,
                    lga=ajo_user.lga,
                    state=ajo_user.state,
                    ajo_user=ajo_user,
                )
                if verified_data is None:
                    raise serializers.ValidationError("Unable to verify address")

                if verified_data.get("status") == "CONFIRMED":
                    ajo_user.verification_retries += 1
                    ajo_user.verified_location_lga = verified_data.get("lga")
                    ajo_user.verified_location_state = verified_data.get("state")
                    ajo_user.verified_location_city = verified_data.get("city")
                    ajo_user.verified_location_country = verified_data.get("country")
                    ajo_user.verified_location_address = verified_data.get("address")
                    ajo_user.verification_status = verified_data.get("status")
                    ajo_user.verification_data = verified_data.get("data")
                    ajo_user.verification_score = verified_data.get("score")
                    ajo_user.verification_message = verified_data.get("message")
                    ajo_user.verification_landmark_list = verified_data.get(
                        "all_landmark"
                    )
                    ajo_user.verification_date = end_date
                    ajo_user.location_is_verified = True
                    ajo_user.location_verification_is_started = True
                    ajo_user.save()
                    attrs["address_status"] = "CONFIRMED"
                    ajo_loan.verification_stage = "BORROWER_INFO"
                    ajo_loan.save()
                    attrs["user_address"] = {
                        "lga": ajo_user.lga,
                        "state": ajo_user.state,
                        "landmark": ajo_user.landmark,
                        "bus_stop": ajo_user.bus_stop,
                        "address": ajo_user.address,
                    }
                    attrs["phone_number"] = ajo_user.phone_number
                elif verified_data.get("status") == "UNCONFIRMED":
                    ajo_user.verification_retries += 1
                    ajo_user.verified_location_lga = verified_data.get("lga")
                    ajo_user.verified_location_state = verified_data.get("state")
                    ajo_user.verified_location_city = verified_data.get("city")
                    ajo_user.verified_location_country = verified_data.get("country")
                    ajo_user.verified_location_address = verified_data.get("address")
                    ajo_user.verification_status = verified_data.get("status")
                    ajo_user.verification_data = verified_data.get("data")
                    ajo_user.verification_score = verified_data.get("score")
                    ajo_user.verification_message = verified_data.get("message")
                    ajo_user.verification_landmark_list = verified_data.get(
                        "all_landmark"
                    )
                    ajo_user.verification_date = end_date
                    ajo_user.location_verification_is_started = True
                    ajo_user.save()

                    if const_data.verification_second_address:
                        ajo_loan.verification_stage = "NEW_ADDRESS_VERIFICATION"
                        ajo_loan.save()
                    else:
                        ajo_loan.verification_stage = "ADDRESS_VALIDATION"
                        ajo_loan.save()

                    attrs["address_status"] = "UNCONFIRMED"
                    attrs["user_address"] = {
                        "lga": ajo_user.lga,
                        "state": ajo_user.state,
                        "landmark": ajo_user.landmark,
                        "bus_stop": ajo_user.bus_stop,
                        "address": ajo_user.address,
                    }
                    attrs["phone_number"] = ajo_user.phone_number
                else:
                    ajo_user.verification_retries += 1
                    ajo_user.verification_status = verified_data.get("status")
                    ajo_user.verification_data = verified_data.get("data")
                    ajo_user.verification_message = verified_data.get("message")
                    ajo_user.verification_date = end_date
                    ajo_user.location_verification_is_started = True
                    ajo_user.save()

                    if const_data.verification_second_address:
                        ajo_loan.verification_stage = "NEW_ADDRESS_VERIFICATION"
                        ajo_loan.save()
                    else:
                        ajo_loan.verification_stage = "ADDRESS_VALIDATION"
                        ajo_loan.save()

                    attrs["address_status"] = "FAILED"
                    attrs["user_address"] = {
                        "lga": ajo_user.lga,
                        "state": ajo_user.state,
                        "landmark": ajo_user.landmark,
                        "bus_stop": ajo_user.bus_stop,
                        "address": ajo_user.address,
                    }
                    attrs["phone_number"] = ajo_user.phone_number
        else:
            if ajo_user.verification_retries == 1:
                end_date = timezone.localtime()
                address_line = (
                    f"{ajo_user.address} {ajo_user.lga} {ajo_user.state} Nigeria"
                )
                verified_data = AddressVerificationLog.verify_address(
                    address=ajo_user.address,
                    bus_stop=bus_stop,
                    landmark=landmark,
                    lga=ajo_user.lga,
                    state=ajo_user.state,
                    ajo_user=ajo_user,
                )
                if verified_data is None:
                    raise serializers.ValidationError("Unable to verify address")
                if verified_data.get("status") == "CONFIRMED":
                    ajo_user.verification_retries += 1
                    ajo_user.verified_location_lga = verified_data.get("lga")
                    ajo_user.verified_location_state = verified_data.get("state")
                    ajo_user.verified_location_city = verified_data.get("city")
                    ajo_user.verified_location_country = verified_data.get("country")
                    ajo_user.verified_location_address = verified_data.get("address")
                    ajo_user.verification_status = verified_data.get("status")
                    ajo_user.verification_data = verified_data.get("data")
                    ajo_user.verification_score = verified_data.get("score")
                    ajo_user.verification_message = verified_data.get("message")
                    ajo_user.verification_landmark_list = verified_data.get(
                        "all_landmark"
                    )
                    ajo_user.verification_date = end_date
                    ajo_user.location_is_verified = True
                    ajo_user.location_verification_is_started = True
                    ajo_user.save()
                    attrs["address_status"] = "CONFIRMED"
                    ajo_loan.verification_stage = "BORROWER_INFO"
                    ajo_loan.save()
                    attrs["user_address"] = {
                        "lga": ajo_user.lga,
                        "state": ajo_user.state,
                        "landmark": ajo_user.landmark,
                        "bus_stop": ajo_user.bus_stop,
                        "address": ajo_user.address,
                    }
                    attrs["phone_number"] = ajo_user.phone_number
                elif verified_data.get("status") == "UNCONFIRMED":
                    ajo_user.verification_retries += 1
                    ajo_user.verified_location_lga = verified_data.get("lga")
                    ajo_user.verified_location_state = verified_data.get("state")
                    ajo_user.verified_location_city = verified_data.get("city")
                    ajo_user.verified_location_country = verified_data.get("country")
                    ajo_user.verified_location_address = verified_data.get("address")
                    ajo_user.verification_status = verified_data.get("status")
                    ajo_user.verification_data = verified_data.get("data")
                    ajo_user.verification_score = verified_data.get("score")
                    ajo_user.verification_message = verified_data.get("message")
                    ajo_user.verification_landmark_list = verified_data.get(
                        "all_landmark"
                    )
                    ajo_user.verification_date = end_date
                    ajo_user.location_verification_is_started = True
                    ajo_user.save()

                    if const_data.verification_second_address:
                        ajo_loan.verification_stage = "NEW_ADDRESS_VERIFICATION"
                        ajo_loan.save()
                    else:
                        ajo_loan.verification_stage = "ADDRESS_VALIDATION"
                        ajo_loan.save()

                    attrs["address_status"] = "UNCONFIRMED"
                    attrs["user_address"] = {
                        "lga": ajo_user.lga,
                        "state": ajo_user.state,
                        "landmark": ajo_user.landmark,
                        "bus_stop": ajo_user.bus_stop,
                        "address": ajo_user.address,
                    }
                    attrs["phone_number"] = ajo_user.phone_number
                else:
                    ajo_user.verification_retries += 1
                    ajo_user.verification_status = verified_data.get("status")
                    ajo_user.verification_data = verified_data.get("data")
                    ajo_user.verification_message = verified_data.get("message")
                    ajo_user.verification_date = end_date
                    ajo_user.location_verification_is_started = True
                    ajo_user.save()

                    if const_data.verification_second_address:
                        ajo_loan.verification_stage = "NEW_ADDRESS_VERIFICATION"
                        ajo_loan.save()
                    else:
                        ajo_loan.verification_stage = "ADDRESS_VALIDATION"
                        ajo_loan.save()

                    attrs["address_status"] = "FAILED"
                    attrs["user_address"] = {
                        "lga": ajo_user.lga,
                        "state": ajo_user.state,
                        "landmark": ajo_user.landmark,
                        "bus_stop": ajo_user.bus_stop,
                        "address": ajo_user.address,
                    }
                    attrs["phone_number"] = ajo_user.phone_number
            else:
                if ajo_loan.verification_stage == "NEW_ADDRESS_VERIFICATION":
                    state = attrs.get("state")
                    lga = attrs.get("lga")
                    address = attrs.get("address")
                    landmark = attrs.get("landmark")
                    bus_stop = attrs.get("bus_stop")
                    utility_bill = attrs.get("util_image")
                    if not state:
                        raise serializers.ValidationError("state is required")
                    if not lga:
                        raise serializers.ValidationError("lga is required")
                    if not address:
                        raise serializers.ValidationError("address is required")
                    if not landmark:
                        raise serializers.ValidationError("landmark is required")
                    if not bus_stop:
                        raise serializers.ValidationError("bus_stop is required")
                    if const_data.verification_utility_bill and not utility_bill:
                        raise serializers.ValidationError("util_image is required")

                    ajo_user.second_location_lga = lga
                    ajo_user.second_location_state = state
                    ajo_user.second_location_address = address
                    ajo_user.second_location_landmark = landmark
                    ajo_user.second_bus_stop = bus_stop
                    ajo_user.save()

                    if utility_bill:
                        upload_img = upload_utility_image(utility_bill, ajo_user)
                        if not upload_img.get("is_valid"):
                            message = upload_img.get("message")
                            raise serializers.ValidationError(message)

                        if upload_img.get("status"):
                            attrs["address_status"] = "CONFIRMED"
                            ajo_user.location_is_verified = True
                            ajo_user.save()

                            ajo_loan.verification_stage = "BORROWER_INFO"
                            ajo_loan.save()
                            attrs["user_address"] = {
                                "lga": lga,
                                "state": state,
                                "landmark": landmark,
                                "bus_stop": bus_stop,
                                "address": address,
                            }
                            attrs["phone_number"] = ajo_user.phone_number
                        else:
                            attrs["address_status"] = "FAILED"
                            attrs["user_address"] = {
                                "lga": lga,
                                "state": state,
                                "landmark": landmark,
                                "bus_stop": bus_stop,
                                "address": address,
                            }
                            attrs["phone_number"] = ajo_user.phone_number
                    else:
                        attrs["address_status"] = "CONFIRMED"
                        ajo_user.location_is_verified = True
                        ajo_user.save()

                        ajo_loan.verification_stage = "BORROWER_INFO"
                        ajo_loan.save()
                        attrs["user_address"] = {
                            "lga": lga,
                            "state": state,
                            "landmark": landmark,
                            "bus_stop": bus_stop,
                            "address": address,
                        }
                        attrs["phone_number"] = ajo_user.phone_number

                elif ajo_loan.verification_stage == "ADDRESS_VALIDATION":
                    state = attrs.get("state")
                    lga = attrs.get("lga")
                    address = attrs.get("address")
                    landmark = attrs.get("landmark")
                    bus_stop = attrs.get("bus_stop")
                    utility_bill = attrs.get("util_image")
                    if not ajo_user.state and not state:
                        raise serializers.ValidationError("state is required")
                    if not ajo_user.lga and not lga:
                        raise serializers.ValidationError("lga is required")
                    if not ajo_user.address and not address:
                        raise serializers.ValidationError("address is required")
                    if not bus_stop:
                        raise serializers.ValidationError("bus_stop is required")
                    if not landmark:
                        raise serializers.ValidationError("landmark is required")
                    if const_data.verification_utility_bill and not utility_bill:
                        raise serializers.ValidationError("util_image is required")

                    if not ajo_user.address or not ajo_user.lga or not ajo_user.state:
                        if utility_bill:
                            upload_img = upload_utility_image(utility_bill, ajo_user)
                            if not upload_img.get("is_valid"):
                                message = upload_img.get("message")
                                raise serializers.ValidationError(message)
                            if not upload_img.get("status"):
                                raise serializers.ValidationError("image upload failed")

                        end_date = timezone.localtime()
                        address_line = (
                            f"{address} {bus_stop} {landmark} {lga} {state}, Nigeria"
                        )
                        verified_data = AddressVerificationLog.verify_address(
                            address=address,
                            bus_stop=bus_stop,
                            landmark=landmark,
                            lga=lga,
                            state=state,
                            ajo_user=ajo_user,
                        )
                        if verified_data is None:
                            raise serializers.ValidationError(
                                "Unable to verify address"
                            )

                        if verified_data.get("status") == "CONFIRMED":
                            ajo_user.lga = lga
                            ajo_user.state = state
                            ajo_user.address = address
                            ajo_user.bus_stop = bus_stop
                            ajo_user.landmark = landmark

                            ajo_user.verification_retries += 1
                            ajo_user.verified_location_lga = verified_data.get("lga")
                            ajo_user.verified_location_state = verified_data.get(
                                "state"
                            )
                            ajo_user.verified_location_city = verified_data.get("city")
                            ajo_user.verified_location_country = verified_data.get(
                                "country"
                            )
                            ajo_user.verified_location_address = verified_data.get(
                                "address"
                            )
                            ajo_user.verified_landmark = landmark
                            ajo_user.verified_bus_stop = bus_stop
                            ajo_user.verification_status = verified_data.get("status")
                            ajo_user.verification_score = verified_data.get("score")
                            ajo_user.verification_data = verified_data.get("data")
                            ajo_user.verification_message = verified_data.get("message")
                            ajo_user.verification_landmark_list = verified_data.get(
                                "all_landmark"
                            )
                            ajo_user.verification_date = end_date
                            ajo_user.location_is_verified = True
                            ajo_user.save()

                            ajo_loan.verification_stage = "BORROWER_INFO"
                            ajo_loan.save()

                            attrs["address_status"] = "CONFIRMED"
                            attrs["user_address"] = {
                                "lga": ajo_user.lga,
                                "state": ajo_user.state,
                                "landmark": landmark,
                                "bus_stop": bus_stop,
                                "address": ajo_user.address,
                            }
                            attrs["phone_number"] = ajo_user.phone_number
                        elif verified_data.get("status") == "UNCONFIRMED":
                            ajo_user.lga = lga
                            ajo_user.state = state
                            ajo_user.address = address
                            ajo_user.bus_stop = bus_stop
                            ajo_user.landmark = landmark

                            ajo_user.verification_retries += 1
                            ajo_user.verified_location_lga = lga
                            ajo_user.verified_location_state = state
                            ajo_user.verified_location_city = verified_data.get("city")
                            ajo_user.verified_location_country = verified_data.get(
                                "country"
                            )
                            ajo_user.verified_location_address = address
                            ajo_user.verification_status = verified_data.get("status")
                            ajo_user.verification_score = verified_data.get("score")
                            ajo_user.verification_data = verified_data.get("data")
                            ajo_user.verification_message = verified_data.get("message")
                            ajo_user.verification_landmark_list = verified_data.get(
                                "all_landmark"
                            )
                            ajo_user.verified_landmark = landmark
                            ajo_user.verified_bus_stop = bus_stop
                            ajo_user.verification_date = end_date
                            ajo_user.save()
                            attrs["address_status"] = "UNCONFIRMED"
                            attrs["user_address"] = {
                                "lga": ajo_user.lga,
                                "state": ajo_user.state,
                                "landmark": landmark,
                                "bus_stop": bus_stop,
                                "address": ajo_user.address,
                            }
                            attrs["phone_number"] = ajo_user.phone_number
                        else:
                            ajo_user.lga = lga
                            ajo_user.state = state
                            ajo_user.address = address
                            ajo_user.bus_stop = bus_stop
                            ajo_user.landmark = landmark

                            ajo_user.verification_retries += 1
                            ajo_user.verification_status = verified_data.get("status")
                            ajo_user.verification_data = verified_data.get("data")
                            ajo_user.verification_message = verified_data.get("message")
                            ajo_user.verification_date = end_date
                            ajo_user.save()
                            attrs["address_status"] = "FAILED"
                            attrs["user_address"] = {
                                "lga": ajo_user.lga,
                                "state": ajo_user.state,
                                "landmark": landmark,
                                "bus_stop": bus_stop,
                                "address": ajo_user.address,
                            }
                            attrs["phone_number"] = ajo_user.phone_number
                        if const_data.verification_second_address:
                            ajo_loan.verification_stage = "NEW_ADDRESS_VERIFICATION"
                            ajo_loan.save()
                        else:
                            ajo_loan.verification_stage = "ADDRESS_VALIDATION"
                            ajo_loan.save()
                    else:
                        start_date = timezone.localtime(
                            ajo_user.verification_date, timezone.get_fixed_timezone(60)
                        )
                        end_date = timezone.localtime()
                        # print(start_date, "START DATE")
                        # print(end_date, "END DATE")

                        # Calculate duration
                        duration = end_date - start_date

                        duration_seconds = duration.total_seconds()
                        verification_duration = (
                            const_data.verification_duration.total_seconds()
                        )

                        waiting_duration = verification_duration - duration_seconds

                        # print(waiting_duration, "waiting_duration")
                        # print(verification_duration, "verification_duration")
                        # print(duration_seconds, "duration seconds")
                        if (
                            ajo_user.verification_retries
                            >= const_data.verification_retries
                        ):
                            raise serializers.ValidationError(
                                "You have exceed your verification retries, contact support"
                            )
                        else:
                            if duration_seconds >= verification_duration:
                                if utility_bill:
                                    upload_img = upload_utility_image(
                                        utility_bill, ajo_user
                                    )
                                    if not upload_img.get("is_valid"):
                                        message = upload_img.get("message")
                                        raise serializers.ValidationError(message)
                                    if not upload_img.get("status"):
                                        raise serializers.ValidationError(
                                            "image upload failed"
                                        )

                                address_line = f"{ajo_user.address} {bus_stop} {landmark} {ajo_user.lga} {ajo_user.state}, Nigeria"
                                # verified_data = (
                                #     LocationVerificationAPI.geocode_verification_api(
                                #         f"{address_line}"
                                #     )
                                # )
                                verified_data = AddressVerificationLog.verify_address(
                                    address=ajo_user.address,
                                    bus_stop=bus_stop,
                                    landmark=landmark,
                                    lga=ajo_user.lga,
                                    state=ajo_user.state,
                                    ajo_user=ajo_user,
                                )
                                if verified_data is None:
                                    raise serializers.ValidationError(
                                        "Unable to verify address"
                                    )

                                if verified_data.get("status") == "CONFIRMED":
                                    ajo_user.verification_retries += 1
                                    ajo_user.verified_location_lga = verified_data.get(
                                        "lga"
                                    )
                                    ajo_user.verified_location_state = (
                                        verified_data.get("state")
                                    )
                                    ajo_user.verified_location_city = verified_data.get(
                                        "city"
                                    )
                                    ajo_user.verified_location_country = (
                                        verified_data.get("country")
                                    )
                                    ajo_user.verified_location_address = (
                                        verified_data.get("address")
                                    )
                                    ajo_user.verified_landmark = landmark
                                    ajo_user.verified_bus_stop = bus_stop
                                    ajo_user.verification_status = verified_data.get(
                                        "status"
                                    )
                                    ajo_user.verification_score = verified_data.get(
                                        "score"
                                    )
                                    ajo_user.verification_data = verified_data.get(
                                        "data"
                                    )
                                    ajo_user.verification_message = verified_data.get(
                                        "message"
                                    )
                                    ajo_user.verification_landmark_list = (
                                        verified_data.get("all_landmark")
                                    )
                                    ajo_user.verification_date = end_date
                                    ajo_user.location_is_verified = True
                                    ajo_user.save()

                                    ajo_loan.verification_stage = "BORROWER_INFO"
                                    ajo_loan.save()

                                    attrs["address_status"] = "CONFIRMED"
                                    attrs["user_address"] = {
                                        "lga": ajo_user.lga,
                                        "state": ajo_user.state,
                                        "landmark": landmark,
                                        "bus_stop": bus_stop,
                                        "address": ajo_user.address,
                                    }
                                    attrs["phone_number"] = ajo_user.phone_number
                                elif verified_data.get("status") == "UNCONFIRMED":
                                    ajo_user.verification_retries += 1
                                    ajo_user.verified_location_lga = verified_data.get(
                                        "lga"
                                    )
                                    ajo_user.verified_location_state = (
                                        verified_data.get("state")
                                    )
                                    ajo_user.verified_location_city = verified_data.get(
                                        "city"
                                    )
                                    ajo_user.verified_location_country = (
                                        verified_data.get("country")
                                    )
                                    ajo_user.verified_location_address = (
                                        verified_data.get("address")
                                    )
                                    ajo_user.verification_status = verified_data.get(
                                        "status"
                                    )
                                    ajo_user.verification_score = verified_data.get(
                                        "score"
                                    )
                                    ajo_user.verification_data = verified_data.get(
                                        "data"
                                    )
                                    ajo_user.verification_message = verified_data.get(
                                        "message"
                                    )
                                    ajo_user.verification_landmark_list = (
                                        verified_data.get("all_landmark")
                                    )
                                    ajo_user.verified_landmark = landmark
                                    ajo_user.verified_bus_stop = bus_stop
                                    ajo_user.verification_date = end_date
                                    ajo_user.save()
                                    attrs["address_status"] = "UNCONFIRMED"
                                    attrs["user_address"] = {
                                        "lga": ajo_user.lga,
                                        "state": ajo_user.state,
                                        "landmark": landmark,
                                        "bus_stop": bus_stop,
                                        "address": ajo_user.address,
                                    }
                                    attrs["phone_number"] = ajo_user.phone_number
                                else:
                                    ajo_user.verification_retries += 1
                                    ajo_user.verification_status = verified_data.get(
                                        "status"
                                    )
                                    ajo_user.verification_data = verified_data.get(
                                        "data"
                                    )
                                    ajo_user.verification_message = verified_data.get(
                                        "message"
                                    )
                                    ajo_user.verification_date = end_date
                                    ajo_user.save()
                                    attrs["address_status"] = "FAILED"
                                    attrs["user_address"] = {
                                        "lga": ajo_user.lga,
                                        "state": ajo_user.state,
                                        "landmark": landmark,
                                        "bus_stop": bus_stop,
                                        "address": ajo_user.address,
                                    }
                                    attrs["phone_number"] = ajo_user.phone_number

                            else:
                                raise serializers.ValidationError(
                                    f"wait for {waiting_duration} seconds and try again"
                                )

        return attrs


class V2VerifyUserLocationSerializer(serializers.Serializer):
    longitude = serializers.CharField(required=True)
    latitude = serializers.CharField(required=True)
    state = serializers.CharField(required=True, allow_null=False)
    lga = serializers.CharField(required=True, allow_null=False)
    address = serializers.CharField(required=True, allow_null=False)
    landmark = serializers.CharField(required=True, allow_null=False)
    bus_stop = serializers.CharField(required=True, allow_null=False)
    util_image = serializers.FileField(
        required=False, max_length=None, allow_empty_file=False, use_url=False
    )

    def validate(self, attrs):
        const_data = LocationConstantTable.get_constant_instance()
        ajo_user: AjoUser = self.context.get("ajo_user")
        user_verification_stage = ajo_user.verification_stage
        if user_verification_stage not in ["ADDRESS_VERIFICATION"]:
            raise serializers.ValidationError("user verification stage is invalid")

        state = attrs.get("state")
        lga = attrs.get("lga")
        address = attrs.get("address")
        landmark = attrs.get("landmark")
        bus_stop = attrs.get("bus_stop")
        utility_bill = attrs.get("util_image")
        longitude = attrs.get("longitude")
        latitude = attrs.get("latitude")

        if const_data.verification_utility_bill and not utility_bill:
            raise serializers.ValidationError("util_image is required")

        # start_date = timezone.localtime(
        #     ajo_user.verification_date, timezone.get_fixed_timezone(60)
        # )
        # end_date = timezone.localtime()
        # # print(start_date, "START DATE")
        # # print(end_date, "END DATE")

        # # Calculate duration
        # duration = end_date - start_date

        # duration_seconds = duration.total_seconds()
        # verification_duration = const_data.verification_duration.total_seconds()
        verification_duration_count = const_data.verification_duration_in_min
        wait_duration = const_data.wait_duration

        # waiting_duration = verification_duration - duration_seconds

        try:
            result = handle_user_request(
                user_id=ajo_user.id,
                wait_duration=wait_duration,
                initial_request_duration=verification_duration_count,
            )
            # print(result)
        except ValueError as err:
            raise serializers.ValidationError(str(err))

        # print(waiting_duration, "waiting_duration")
        # print(verification_duration, "verification_duration")
        # print(duration_seconds, "duration seconds")

        if ajo_user.verification_retries >= const_data.verification_retries:

            if not utility_bill:
                raise ValueError(
                    "You have exceed your verification retries, kindly provide a supporting document"
                )

            try:
                convert_image_to_bas64 = base_64_image_encoder(image=utility_bill)
            except ValueError as err:
                raise serializers.ValidationError(str(err))

            image_data = validate_utility_bill_with_chagpt(
                base64_image=convert_image_to_bas64
            )

            utility_status = image_data.get("status")
            utility_message = image_data.get("message")
            verification_image_data = image_data.get("image_data")
            utility_date = image_data.get("Date")
            utility_address = image_data.get("Address")
            log_instance = AddressVerificationLog.openai_log(
                longitude=longitude,
                latitude=latitude,
                address=address,
                bus_stop=bus_stop,
                landmark=landmark,
                lga=lga,
                state=state,
                ajo_user=ajo_user,
                supporting_doc=convert_image_to_bas64,
                ai_result=image_data,
            )
            if utility_status is False:
                log_instance.status = "UNCONFIRMED"
                log_instance.save()
                raise ValueError(utility_message)

            if utility_date == "N/A":
                log_instance.status = "UNCONFIRMED"
                log_instance.save()
                raise ValueError("utility bill should have a valid purchase date")

            is_expired = is_utility_bill_expired(
                utility_date, const_data.allowed_utility_month
            )
            if is_expired:
                log_instance.status = "UNCONFIRMED"
                log_instance.save()
                raise ValueError("utility bill should be within 6 month of purchase")

            if utility_address == "N/A":
                log_instance.status = "UNCONFIRMED"
                log_instance.save()
                raise ValueError("utility bill has no valid address")

            const = LocationConstantTable.get_constant_instance()

            try:
                validate_address_pattern(
                    address=utility_address,
                    pattern=const.address_pattern,
                    uttility_bill_address_pattern=const.uttility_bill_address_pattern,
                )
            except ValueError as err:

                raise ValueError(
                    f"Invalid Utility Bill Address Format: {utility_address}. Please contact support."
                )

            if AddressVerificationLog.objects.filter(
                status="CONFIRMED", verified_address=utility_address
            ).exists():
                raise ValueError(
                    f"An address with the status 'CONFIRMED' already exists for the utility address: {utility_address}."
                )

            attrs["address_status"] = "CONFIRMED"
            attrs["user_address"] = {
                "lga": lga,
                "state": state,
                "landmark": landmark,
                "bus_stop": bus_stop,
                "address": address,
            }

            verified_data = {
                "bus_stop": bus_stop,
                "lga": lga,
                "state": state,
                "landmark": landmark,
                "address": utility_address,
            }

            ajo_user.mark_address_verified(verification_data=verified_data)

            log_instance.status = "CONFIRMED"
            log_instance.supporting_image = verification_image_data
            log_instance.verified_address = utility_address
            log_instance.save()

            ajo_user.verification_retries += 1
            ajo_user.verified_location_lga = verified_data.get("lga")
            ajo_user.verified_location_state = verified_data.get("state")
            ajo_user.verified_location_city = verified_data.get("city")
            ajo_user.verified_location_country = verified_data.get("country")
            ajo_user.verified_location_address = verified_data.get("address")
            ajo_user.verification_status = verified_data.get("status")
            ajo_user.verification_data = verified_data.get("data")
            ajo_user.verification_score = verified_data.get("score")
            ajo_user.verification_message = verified_data.get("message")
            ajo_user.verification_landmark_list = verified_data.get("landmark")
            ajo_user.location_verification_is_started = True
            ajo_user.location_address = verified_data.get("message")

            ajo_user.save(
                update_fields=[
                    "verification_retries",
                    "verified_location_lga",
                    "verified_location_state",
                    "verified_location_city",
                    "verified_location_country",
                    "verified_location_address",
                    "verification_status",
                    "verification_data",
                    "verification_score",
                    "verification_message",
                    "verification_landmark_list",
                    "location_verification_is_started",
                ]
            )

        else:

            verified_data = AddressVerificationLog.verify_address(
                longitude=longitude,
                latitude=latitude,
                address=address,
                bus_stop=bus_stop,
                landmark=landmark,
                lga=lga,
                state=state,
                ajo_user=ajo_user,
            )

            if verified_data is None:
                raise serializers.ValidationError("Unable to verify address")

            if AddressVerificationLog.objects.filter(
                status="CONFIRMED", address=verified_data.get("address")
            ).exists():
                raise serializers.ValidationError(
                    f"An address with the status 'CONFIRMED' already exists: {address}."
                )

            if verified_data.get("status") == "CONFIRMED":
                verified_data["landmark"] = landmark
                verified_data["bus_stop"] = bus_stop
                ajo_user.mark_address_verified(verification_data=verified_data)

                attrs["address_status"] = "CONFIRMED"
                attrs["user_address"] = {
                    "lga": lga,
                    "state": state,
                    "landmark": landmark,
                    "bus_stop": bus_stop,
                    "address": address,
                }
                attrs["phone_number"] = ajo_user.phone_number
            elif verified_data.get("status") == "UNCONFIRMED":
                ajo_user.verification_retries += 1
                ajo_user.lga = verified_data.get("lga")
                ajo_user.state = verified_data.get("state")
                ajo_user.city = verified_data.get("city")
                ajo_user.country = verified_data.get("country")
                ajo_user.address = verified_data.get("address")
                ajo_user.verification_status = verified_data.get("status")
                ajo_user.verification_score = verified_data.get("score")
                ajo_user.verification_data = verified_data.get("data")
                ajo_user.verification_message = verified_data.get("message")
                ajo_user.verification_landmark_list = verified_data.get("all_landmark")
                ajo_user.landmark = landmark
                ajo_user.bus_stop = bus_stop
                # ajo_user.verification_date = end_date
                ajo_user.location_verification_is_started = True
                ajo_user.save()
                attrs["address_status"] = "UNCONFIRMED"
                attrs["user_address"] = {
                    "lga": lga,
                    "state": state,
                    "landmark": landmark,
                    "bus_stop": bus_stop,
                    "address": address,
                }
                attrs["phone_number"] = ajo_user.phone_number
            else:
                ajo_user.verification_retries += 1
                ajo_user.lga = lga
                ajo_user.state = state
                ajo_user.landmark = landmark
                ajo_user.address = address
                ajo_user.bus_stop = bus_stop
                ajo_user.verification_status = verified_data.get("status")
                ajo_user.verification_data = verified_data.get("data")
                ajo_user.verification_message = verified_data.get("message")
                # ajo_user.verification_date = end_date
                ajo_user.location_verification_is_started = True
                ajo_user.save()
                attrs["address_status"] = "FAILED"
                attrs["user_address"] = {
                    "lga": lga,
                    "state": state,
                    "landmark": landmark,
                    "bus_stop": bus_stop,
                    "address": address,
                }
                attrs["phone_number"] = ajo_user.phone_number

        return attrs


class V2PingAgentLocationSerializer(serializers.Serializer):
    longitude = serializers.CharField(required=True)
    latitude = serializers.CharField(required=True)
    terminal_serial_number = serializers.CharField(required=False)


class UserTerminalAddressVerificationSerializer(serializers.Serializer):
    terminal_serial_number = serializers.CharField(required=True)
    user_email = serializers.EmailField(required=True)
    business_address = serializers.CharField(required=True)
    home_address = serializers.CharField(required=True)
