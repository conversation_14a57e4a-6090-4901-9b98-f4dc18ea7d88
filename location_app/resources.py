from import_export import fields, resources

from location_app.models import (
    AddressVerificationLog,
    AgentLocation,
    LocationConstantTable,
    LocationLongitudeLatitude,
    UserTerminalAddressVerification,
)


class LocationConstantTableResource(resources.ModelResource):
    class Meta:
        model = LocationConstantTable


class AgentLocationResource(resources.ModelResource):
    class Meta:
        model = AgentLocation


class AddressVerificationLogResource(resources.ModelResource):
    class Meta:
        model = AddressVerificationLog


class UserTerminalAddressVerificationResource(resources.ModelResource):
    class Meta:
        model = UserTerminalAddressVerification


class LocationLongitudeLatitudeResource(resources.ModelResource):
    class Meta:
        model = LocationLongitudeLatitude
