# import math
# from typing import Optional, Type

# from django.conf import settings
# from django.db import models
# from django.db.models import F

# logger = settings.LOGGER


# class LoanRepaymentProcessor:
#     def __init__(
#         self,
#         const,
#         loan: Optional[models.Model] = None,
#         health_insurance_model_class: Optional[models.Model] = None,
#         repayment_distribution_model_class: Optional[models.Model] = None,
#     ):

#         self.loan = loan
#         self.const = const
#         self.health_insurance_model_class = health_insurance_model_class
#         self.repayment_distribution_model_class = repayment_distribution_model_class

#     def process_health_insurance_distribution(self, repayment_instance):
#         if not repayment_instance:
#             logger.debug("Repayment instance is required")
#             logger.debug("Repayment instance is required")
#             logger.debug("Repayment instance is required")
#             return

#         self.loan = repayment_instance.ajo_loan

#         month = self.loan.month_count
#         # get what the insurance fee was or is from the total_repayment and the principal repayment
#         repayment_amount = self.loan.repayment_amount
        
#         loan_amount = self.loan.amount
#         interest_rate = self.loan.interest_rate / 100
        
#         principal_repayment = (interest_rate * loan_amount) + loan_amount


#         insurance_fee_on_total_repayment = repayment_amount - principal_repayment

#         # health_insurance_fee = self.const.health_insurance_fee
#         health_insurance_fee = insurance_fee_on_total_repayment / month

#         logger.debug(f"repayment amount: {repayment_amount}")
#         logger.debug(f"principal repayment: {principal_repayment}")
#         logger.debug(
#             f"insurance fee on total repayment: {insurance_fee_on_total_repayment}"
#         )

#         total_renewal_fee = health_insurance_fee * month
#         num_of_days = self.loan.tenor_in_days
#         fee_on_repayment = total_renewal_fee / num_of_days
#         round_amount = math.ceil(fee_on_repayment * 100) / 100
#         insurance_fee_on_repayment = 150 if round_amount > 100 else round_amount
#         amount_paid = repayment_instance.repayment_amount

#         logger.debug(f"Amount Paid: {amount_paid}")
#         logger.debug(f"Month Count: {month}")
#         logger.debug(f"Health Insurance Fee: {health_insurance_fee}")
#         logger.debug(f"Total Renewal Fee: {total_renewal_fee}")
#         logger.debug(f"Number of Days in Tenor: {num_of_days}")
#         logger.debug(f"Insurance Fee on Repayment: {insurance_fee_on_repayment}")

#         insurance_instance = self.health_insurance_model_class.objects.filter(
#             loan=self.loan
#         ).last()
#         if not insurance_instance:
#             logger.debug(f"No Insurace record")
#             return
#         logger.debug(f"Total paid loan amount: {self.loan.total_paid_amount}")
#         total_paid = self.loan.total_paid_amount
#         if self.loan.total_paid_amount == 0:
#             total_paid = amount_paid

#         try:

#             logger.debug(
#                 f"Total Paid Amount (including previous unapplied balance): {total_paid}"
#             )

#             # Calculate fully covered days based on total payment
#             if self.loan.daily_repayment_amount > 0:

#                 daily_repayment_count = total_paid / self.loan.daily_repayment_amount
#                 actual_count = math.floor(daily_repayment_count)  # Days covered
#                 excess_balance = total_paid % self.loan.daily_repayment_amount
#             else:
#                 actual_count, excess_balance = 0, 0

#             logger.debug(f"Daily Repayment Amount: {self.loan.daily_repayment_amount}")
#             logger.debug(f"Actual Count of Covered Days: {actual_count}")
#             logger.debug(f"Excess Balance: {excess_balance}")
#         except ZeroDivisionError:
#             actual_count, excess_balance = 0, 0
#             logger.debug(
#                 "ZeroDivisionError encountered. Setting actual_count and excess_balance to 0."
#             )

#         if actual_count > 0:
#             # Determine insurance fee allocation per day and total allocation

#             # if total paid is zero meaning it is te first repyment resord of the user
#             # get the principal daily repayment

#             total_allocation = insurance_fee_on_repayment * actual_count
#             logger.debug(f"Total Allocation for Covered Days: {total_allocation}")
#             logger.debug(
#                 f"Loan daily health allocation: {self.loan.daily_health_allocation}"
#             )

#             if total_allocation >= self.loan.daily_health_allocation:
#                 allocation_amount = max(
#                     total_allocation - self.loan.daily_health_allocation, 0
#                 )

#                 logger.debug(
#                     f"Daily Health Allocation: {self.loan.daily_health_allocation}"
#                 )
#                 logger.debug(f"Calculated Allocation Amount: {allocation_amount}")
#                 total_allocation = (math.floor(allocation_amount),)
#                 # Only create distribution if there is a positive allocation amount

#                 if allocation_amount > 0:
#                     # self.repayment_distribution_model_class.objects.create(
#                     #     amount_paid=amount_paid,
#                     #     expected_daily_fee=insurance_fee_on_repayment,
#                     #     repayment_instance=repayment_instance,
#                     #     loan_id=self.loan.id,
#                     #     loan_daily_repayment=self.loan.daily_repayment_amount,
#                     #     total_paid_loan_amount=total_paid,
#                     #     allocation_amount=allocation_amount,
#                     #     distribution_type="HEALTH_INSURANCE",
#                     #     unapplied_balance=excess_balance,
#                     # )

#                     logger.debug(
#                         f"Created LoanRepaymentDistribution with Allocation Amount: {allocation_amount}"
#                     )

#                     # Update repayment instance with new health insurance allocation
#                     repayment_instance.health_insurance = allocation_amount
#                     repayment_instance.save()
#                     logger.debug(
#                         "Updated repayment instance with new health insurance allocation."
#                     )

#                     # Atomically increment loan's daily health allocation
#                     self.loan.daily_health_allocation = (
#                         F("daily_health_allocation") + allocation_amount
#                     )
#                     # self.loan.total_paid_amount = F("total_paid_amount") + amount_paid
#                     self.loan.save(update_fields=["daily_health_allocation"])
#                     logger.debug("Updated loan's daily health allocation.")

#                     # Update the latest insurance instance's allocation
                    
#                     insurance_instance.daily_allocation = (
#                         F("daily_allocation") + allocation_amount
#                     )
#                     insurance_instance.save(update_fields=["daily_allocation"])
#                     logger.debug(
#                         "Updated the latest insurance instance's daily allocation."
#                     )
