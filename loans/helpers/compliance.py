import ast
import json
from typing import Any, Dict, List, Tuple

from django.core.exceptions import ObjectDoesNotExist
from django.db.models import Avg, Q
from django.utils import timezone

from accounts.models import (
    ConstantTable,
    CustomUser,
    IDVerificationDumps,
    IDVerificationSources,
)

from ..enums import BiometricsRequestType, UserVerificationType, VerificationType
from ..models import (
    AjoLoan,
    AjoUser,
    BiometricsMetaData,
    BorrowerInfo,
    ComplianceChecksLogs,
    ComplianceChecksTypes,
    ComplianceTypes,
    LoanAnalysisLog,
    LoanGuarantor,
    LoanKYCDocumentation,
    LoanStatus,
)


class ComplianceChecks:

    def __init__(
        self,
        loan: <PERSON>jo<PERSON>oan | None = None,
        agent: CustomUser | None = None,
    ) -> None:
        self.loan = loan
        self.agent = agent

    def documentation_checks(self):
        """
        This method is for calling all the documentation check methods to perform a check flow
        """
        if not self.loan:
            raise TypeError("please instantiate the class with a loan object")

        self.borrower_guarantor_relationship_check()
        self.guarantor_borrower_relationship_check()
        self.agent_as_guarantor_check()
        self.agent_as_borrower_check()
        self.family_relationship_check()
        self.repeated_surnames_check()
        self.supervisor_borrower_relationship_check()
        self.borrower_guarantor_same_branch_surname_check()
        self.guarantor_surname_match_check()
        self.verify_liveliness_check()

    def disbursement_checks(self):
        """
        This method is for calling all the disbursement check methods to perform a check flow
        """
        if not self.loan and not self.agent:
            raise TypeError("please instantiate the class with a loan or agent object")

        self.individual_agent_portfolio_health_disbursement_check()
        self.branch_agent_portfolio_health_disbursement_check()
        self.branch_portfolio_overall_health_disbursement_check()

    def repayment_checks(self):
        """
        this method is for calling all the repayment checks method
        """
        self.monitor_repayment_patterns_check()
        self.missed_partial_repayments_check()
        self.agents_not_following_up_delinquent_accounts_check()

    @staticmethod
    def get_names_from_ID_dumps(borrower: AjoUser) -> Tuple[str | None, str | None]:
        dumps = IDVerificationDumps.objects.filter(ajo_user=borrower)
        if not dumps.exists():
            return (None, None)

        for dump in dumps:
            if dump.source == IDVerificationSources.LIBERTYBVN:
                try:
                    body = json.loads(dump.dump)
                except json.JSONDecodeError:
                    body = ast.literal_eval(dump.dump)

                name_body = body.get("data", {}).get("data", {}).get("data", {})
                if not name_body:
                    continue

                first_name = name_body.get("firstName")
                last_name = name_body.get("lastName")
                return (first_name, last_name)

        return (None, None)

    @staticmethod
    def create_compliance_log(
        loan: AjoLoan,
        compliance_description: str,
        check_type: ComplianceChecksTypes,
        compliance_type: ComplianceTypes = ComplianceTypes.DOCUMENTATION,
        branch: str | None = None,
        agent: CustomUser | None = None,
        borrower: AjoUser | None = None,
        guarantor: LoanGuarantor | None = None,
        exists_before_create: bool = False,
    ):
        if exists_before_create:
            compliances = ComplianceChecksLogs.objects.filter(
                loan=loan,
                guarantor=guarantor,
                borrower=borrower,
                agent=agent,
                compliance_description=compliance_description,
                compliance_type=compliance_type,
                check_type=check_type,
            )

            if compliances.exists():
                return compliances.last()

        compliance_log = ComplianceChecksLogs.objects.create(
            agent=agent,
            borrower=borrower,
            loan=loan,
            guarantor=guarantor,
            compliance_description=compliance_description,
            compliance_type=compliance_type,
            check_type=check_type,
            branch=branch,
        )

        return compliance_log

    @staticmethod
    def compliance_log_exist(
        agent: CustomUser,
        compliance_type,
        check_type,
    ):

        return ComplianceChecksLogs.objects.filter(
            agent=agent,
            compliance_type=compliance_type,
            check_type=check_type,
        ).exists()

    def borrower_guarantor_relationship_check(self) -> bool:
        """
        This check ensures that a borrower is not used as a guarantor

        Args:
            loan (AjoLoan): the loan object
        """
        # borrower_id: int = loan.borrower.id
        # guarantor_id: int = loan.guarantor.id if loan.guarantor else None

        # if borrower_id == guarantor_id:
        #     # Log the compliance issue
        #     print(f"Compliance Issue: Borrower ID {borrower_id} is also listed as Guarantor ID.")
        #     return {"status": "FAILED", "reason": "Borrower cannot act as their own guarantor."}

        # agent_surname = loan.agent.last_name if hasattr(loan.agent, 'surname') else None
        # borrower_surname = loan.borrower.surname if hasattr(loan.borrower, 'surname') else None

        # if agent_surname and borrower_surname and agent_surname.lower() == borrower_surname.lower():
        #         print(f"Compliance Issue: Agent Surname {agent_surname} matches Borrower Surname {borrower_surname}.")

        # let us utilize the IDVerificationDumps

        # check if this borrower has the same NIN or BVN with any Loan Guarantor
        borrower = self.loan.borrower

        if borrower.bvn or borrower.nin:
            existing_instances = LoanGuarantor.objects.filter(
                Q(verification_number=borrower.bvn) | Q(verification_number=borrower.nin)
            )

            extraction_instances = existing_instances.filter(you_verify_metadata__isnull=False)
            if extraction_instances.exists():
                instance_to_extract = extraction_instances.last()
                borrower_first_name = instance_to_extract.you_verify_metadata.get("data").get("firstName")
                borrower_last_name = instance_to_extract.you_verify_metadata.get("data").get("lastName")
            else:
                self.create_compliance_log(
                    loan=self.loan,
                    borrower=borrower,
                    check_type=ComplianceChecksTypes.GUARANTOR_SURNAME_MATCH,
                    compliance_type=ComplianceTypes.DOCUMENTATION,
                    compliance_description="no names could be obtained from existing LoanGuarantor matches to perform this check",
                    exists_before_create=True,
                )
                return False

        else:
            borrower_first_name, borrower_last_name = self.get_names_from_ID_dumps(borrower=borrower)
            if not borrower_first_name or not borrower_last_name:
                self.create_compliance_log(
                    loan=self.loan,
                    borrower=borrower,
                    check_type=ComplianceChecksTypes.GUARANTOR_SURNAME_MATCH,
                    compliance_type=ComplianceTypes.DOCUMENTATION,
                    compliance_description="no names could be obtained from ID Verification dumps to perform this check",
                    exists_before_create=True,
                )
                return False

            # search the LoanGuarantor table for similar info
            existing_instances = LoanGuarantor.objects.filter(
                Q(you_verify_metadata__data__firstName=borrower_first_name)
                & Q(you_verify_metadata__data__lastName=borrower_last_name)
            )

        if existing_instances.exists():
            existing_instances_ids = existing_instances.values_list("id", flat=True)
            self.create_compliance_log(
                borrower=borrower,
                loan=self.loan,
                compliance_type=ComplianceTypes.DOCUMENTATION,
                check_type=ComplianceChecksTypes.BORROWER_GUARANTOR_RELATIONSHIP,
                compliance_description=f"borrower's names: {borrower_first_name} {borrower_last_name}, exists with {existing_instances_ids}",
                exists_before_create=True,
            )
            return True

        return False

    def guarantor_borrower_relationship_check(self):
        """
        Checks that the guarantor of an active loan is not used as a borrower
        """

        active_status_list = LoanKYCDocumentation.active_loan_status()
        # get all active loans
        active_loans = AjoLoan.objects.filter(status__in=active_status_list).select_related("guarantor")

        # try to compare the details of the borrower to the loan guarantors of the active loans
        borrower = self.loan.borrower

        guarantors: List[LoanGuarantor] = [loan.guarantor for loan in active_loans if loan.guarantor is not None]

        if borrower.bvn or borrower.nin:
            for guarantor in guarantors:
                if borrower.bvn:
                    exists = borrower.bvn == guarantor.verification_number
                else:
                    exists = borrower.nin == guarantor.verification_number

                if exists:
                    existing_guarantor = guarantor
                    break

        else:
            borrower_first_name, borrower_last_name = self.get_names_from_ID_dumps(borrower=borrower)

            if not borrower_first_name or not borrower_last_name:
                return False

            for guarantor in guarantors:
                if not guarantor.you_verify_metadata:
                    return False

                # check for you verify metadata
                you_verify_data: Dict[str, Any] = guarantor.you_verify_metadata.get("data", {})
                if not you_verify_data:
                    return False

                verification_first_name = you_verify_data.get("firstName")
                verification_last_name = you_verify_data.get("lastName")

                if borrower_first_name == verification_first_name and borrower_last_name == verification_last_name:
                    exists = True
                    existing_guarantor = guarantor
                    break

        if exists:
            self.create_compliance_log(
                borrower=borrower,
                guarantor=existing_guarantor,
                loan=self.loan,
                check_type=ComplianceChecksTypes.GUARANTOR_BORROWER_RELATIONSHIP,
                compliance_type=ComplianceTypes.DOCUMENTATION,
                compliance_description=f"{existing_guarantor} exists as a borrower",
                exists_before_create=True,
            )
            return True

        return False

    def agent_as_guarantor_check(self):
        """check if the agent's surname matches the loan guarantor's surname"""
        guarantor = self.loan.guarantor
        agent: CustomUser = self.loan.agent

        if not guarantor.you_verify_metadata:
            self.create_compliance_log(
                loan=self.loan,
                guarantor=guarantor,
                check_type=ComplianceChecksTypes.GUARANTOR_SURNAME_MATCH,
                compliance_type=ComplianceTypes.DOCUMENTATION,
                compliance_description="no data in guarantor's youverify metadata to perform this check",
                exists_before_create=True,
            )
            return False

        you_verify_data: Dict[str, Any] = guarantor.you_verify_metadata.get("data", {})
        if not you_verify_data:
            self.create_compliance_log(
                loan=self.loan,
                guarantor=guarantor,
                check_type=ComplianceChecksTypes.GUARANTOR_SURNAME_MATCH,
                compliance_type=ComplianceTypes.DOCUMENTATION,
                compliance_description="no data in guarantor's youverify metadata to perform this check",
                exists_before_create=True,
            )
            return False

        if agent.last_name == you_verify_data.get("lastName"):
            self.create_compliance_log(
                loan=self.loan,
                guarantor=guarantor,
                agent=agent,
                check_type=ComplianceChecksTypes.AGENT_AS_GUARANTOR,
                compliance_type=ComplianceTypes.DOCUMENTATION,
                compliance_description=f"agent, {agent}, has the same surname as guarantor, {guarantor}",
                exists_before_create=True,
            )
            return True

        return False

    def agent_as_borrower_check(self):
        """
        check if the agent's first name and last name is the same as the borrower's first name and last name
        """
        agent: CustomUser = self.loan.agent
        borrower = self.loan.borrower

        if agent.first_name == borrower.first_name and agent.last_name == borrower.last_name:
            self.create_compliance_log(
                loan=self.loan,
                agent=agent,
                borrower=borrower,
                check_type=ComplianceChecksTypes.AGENT_AS_BORROWER,
                compliance_type=ComplianceTypes.DOCUMENTATION,
                compliance_description=f"agent, {agent}, has the same surname and first name as borrower, {borrower}.",
                exists_before_create=True,
            )
            return True

        return False

    def family_relationship_check(self):
        """
        check if the agent's surname matches the borrower's surname
        """
        borrower = self.loan.borrower
        agent: CustomUser = self.loan.agent

        if borrower.last_name == agent.last_name:
            self.create_compliance_log(
                loan=self.loan,
                borrower=borrower,
                agent=agent,
                check_type=ComplianceChecksTypes.FAMILY_RELATIONSHIP,
                compliance_type=ComplianceTypes.DOCUMENTATION,
                compliance_description=f"agent, {agent}, has the same surname as borrower, {borrower}",
                exists_before_create=True,
            )
            return True

        return False

    def repeated_surnames_check(self):
        """
        check if more than two customers that belong to the same agent share the same surname
        """
        borrower = self.loan.borrower
        agent: CustomUser = self.loan.agent

        agent_borrowers = AjoLoan.objects.filter(agent=agent).order_by("borrower").distinct("borrower")

        existing_instances = agent_borrowers.filter(
            Q(borrower__last_name=borrower.last_name) & ~Q(borrower__first_name=borrower.first_name)
        )

        if existing_instances.count() > 2:
            instance_ids = existing_instances.values_list("id", flat=True)
            self.create_compliance_log(
                loan=self.loan,
                agent=agent,
                check_type=ComplianceChecksTypes.REPEATED_SURNAMES,
                compliance_type=ComplianceTypes.DOCUMENTATION,
                compliance_description=f"the same surname is shared between the following borrowers IDs {instance_ids}",
                exists_before_create=True,
            )
            return True

        return False

    def supervisor_borrower_relationship_check(self):
        """
        check if the supervisor's surname matches the borrower's surname
        """
        borrower = self.loan.borrower
        supervisor: CustomUser = self.loan.checked_by

        if not supervisor:
            self.create_compliance_log(
                loan=self.loan,
                borrower=borrower,
                check_type=ComplianceChecksTypes.GUARANTOR_SURNAME_MATCH,
                compliance_type=ComplianceTypes.DOCUMENTATION,
                compliance_description="no supervisor is set for this loan yet",
                exists_before_create=True,
            )
            return False

        if supervisor.last_name == borrower.last_name:
            self.create_compliance_log(
                loan=self.loan,
                borrower=borrower,
                check_type=ComplianceChecksTypes.SUPERVISOR_BORROWER_RELATIONSHIP,
                compliance_type=ComplianceTypes.DOCUMENTATION,
                compliance_description=f"supervisor, {supervisor}, has the same surname as borrower, {borrower}",
                exists_before_create=True,
            )
            return True

        return False

    def borrower_guarantor_same_branch_surname_check(self):
        """
        check if the borrower's surname matches any guarantor's surname in the same loan branch
        """
        borrower = self.loan.borrower
        borrower_branch = borrower.branch_name

        if not borrower_branch:
            self.create_compliance_log(
                loan=self.loan,
                borrower=borrower,
                check_type=ComplianceChecksTypes.BORROWER_GUARANTOR_IN_SAME_BRANCH,
                compliance_type=ComplianceTypes.DOCUMENTATION,
                compliance_description=f"no branch set for borrower {borrower}",
                exists_before_create=True,
            )
            return True

        # obtain all the borrowers with the same branch
        borrowers_in_branch = AjoUser.objects.filter(branch_name=borrower_branch)

        # get the guarantors for these borrowers
        guarantors_in_branch = LoanGuarantor.objects.filter(borrower__in=borrowers_in_branch)

        borrower_surname = borrower.last_name

        existing_instances = guarantors_in_branch.filter(you_verify_metadata__data__lastName=borrower_surname)

        if existing_instances.exists():
            instance_ids = existing_instances.values_list("id", flat=True)
            self.create_compliance_log(
                loan=self.loan,
                borrower=borrower,
                check_type=ComplianceChecksTypes.BORROWER_GUARANTOR_IN_SAME_BRANCH,
                compliance_type=ComplianceTypes.DOCUMENTATION,
                compliance_description=f"borrower's name: {borrower.last_name} exists with guarantors in the same branch, IDs: {instance_ids}",
                exists_before_create=True,
            )
            return True

        return False

    def guarantor_surname_match_check(self):
        """
        check if the guarantor's surname matches the borrower's or supervisor's surname
        (surname matching between guarantor, borrower and supervisor)
        """
        guarantor = self.loan.guarantor
        borrower = self.loan.borrower
        supervisor: CustomUser | None = self.loan.checked_by

        if not guarantor.you_verify_metadata:
            self.create_compliance_log(
                loan=self.loan,
                guarantor=guarantor,
                check_type=ComplianceChecksTypes.GUARANTOR_SURNAME_MATCH,
                compliance_type=ComplianceTypes.DOCUMENTATION,
                compliance_description="no data in guarantor's youverify metadata to perform this check",
                exists_before_create=True,
            )
            return False

        guarantor_last_name = guarantor.you_verify_metadata.get("data", {}).get("lastName")

        matches_borrower = False
        if guarantor_last_name == borrower.last_name:
            matches_borrower = True

        matches_supervisor = False
        if not supervisor:
            self.create_compliance_log(
                loan=self.loan,
                guarantor=guarantor,
                borrower=borrower,
                check_type=ComplianceChecksTypes.GUARANTOR_SURNAME_MATCH,
                compliance_type=ComplianceTypes.DOCUMENTATION,
                compliance_description="no supervisor is set for this loan yet",
                exists_before_create=True,
            )

        else:
            if guarantor_last_name == supervisor.last_name:
                matches_supervisor = True

        if matches_borrower or matches_supervisor:
            matches = []
            if matches_borrower:
                matches.append("borrower")
            if matches_supervisor:
                matches.append("supervisor")
            message = f"guarantor's surname {guarantor_last_name} matches {matches}"
            self.create_compliance_log(
                loan=self.loan,
                guarantor=guarantor,
                borrower=borrower,
                check_type=ComplianceChecksTypes.GUARANTOR_SURNAME_MATCH,
                compliance_type=ComplianceTypes.DOCUMENTATION,
                compliance_description=message,
                exists_before_create=True,
            )

            return True

        return False

    def verify_liveliness_check(self):
        """
        Verify that the liveliness check went through
        """
        try:
            borrower_info: BorrowerInfo = self.loan.borrowerinfo
        except ObjectDoesNotExist as err:
            return False

        liveliness_check = BiometricsMetaData.objects.filter(
            borrower_info=borrower_info,
            user_type=UserVerificationType.BORROWER,
            request_type=BiometricsRequestType.LIVELINESS,
            status=True,
        )

        if not liveliness_check.exists():
            self.create_compliance_log(
                loan=self.loan,
                borrower=borrower_info.borrower,
                check_type=ComplianceChecksTypes.VERIFY_LIVELINESS,
                compliance_type=ComplianceTypes.DOCUMENTATION,
                compliance_description="this user does not have a verified liveliness check",
                exists_before_create=True,
            )
            return True

        return False

    def individual_agent_portfolio_health_disbursement_check(self):
        """
        check if an agents status remains unable to disburse for up 3 days
        """
        if self.loan:
            agent = self.loan.agent
        else:
            agent = self.agent

        today = timezone.localtime()
        three_days_ago = today - timezone.timedelta(days=3)

        loan_analysis_logs = LoanAnalysisLog.objects.filter(
            agent=agent,
            created_at__gte=three_days_ago,
        )

        cannot_give_loans_logs = loan_analysis_logs.filter(positive_count=0)

        if loan_analysis_logs.count() == cannot_give_loans_logs.count():

            if self.compliance_log_exist(
                agent=agent,
                check_type=ComplianceChecksTypes.INDIVIDUAL_AGENT_PORTFOLIO_HEALTH,
                compliance_type=ComplianceTypes.DISBURSEMENT,
            ):
                return True

            self.create_compliance_log(
                loan=self.loan,
                agent=agent,
                check_type=ComplianceChecksTypes.INDIVIDUAL_AGENT_PORTFOLIO_HEALTH,
                compliance_type=ComplianceTypes.DISBURSEMENT,
                compliance_description="this agent could not give loans for three days in a row",
            )
            return True

        return False

    def branch_agent_portfolio_health_disbursement_check(self):
        """
        check if branch agents' collective default rate exceeds threshold
        """
        if self.loan:
            agent = self.loan.agent
        else:
            agent = self.agent

        agent_branch = agent.user_branch

        loans_in_branch = AjoLoan.objects.filter(
            agent__user_branch=agent_branch,
        )

        loans_count = loans_in_branch.count()

        if loans_count < 1:
            # can't flag for compliance
            return False

        outstanding_loans = loans_in_branch.filter(status=LoanStatus.DEFAULTED)

        performance_rating = (outstanding_loans.count() / loans_count) * 100

        constant_table = ConstantTable.get_constant_table_instance()

        if performance_rating > constant_table.defaulting_loan_threshold:
            self.create_compliance_log(
                loan=self.loan,
                agent=agent,
                check_type=ComplianceChecksTypes.BRANCH_AGENT_PORTFOLIO_HEALTH,
                compliance_type=ComplianceTypes.DISBURSEMENT,
                compliance_description=f"this user's branch has a performance rating of {performance_rating}, lower than threshold",
            )
            return True

        return False

    def branch_portfolio_overall_health_disbursement_check(self):
        """
        If branch portfolio health metrics fall below standards
        """
        if self.loan:
            agent = self.loan.agent
        else:
            agent = self.agent

        agent_branch = agent.user_branch

        loans_in_branch = AjoLoan.objects.filter(
            agent__user_branch=agent_branch,
        )

        average_health_score = loans_in_branch.aggregate(average=Avg("repayment_health_score"))["average"]

        constant_table = ConstantTable.get_constant_table_instance()

        if average_health_score > constant_table.average_rhs_threshold:
            self.create_compliance_log(
                loan=self.loan,
                agent=agent,
                check_type=ComplianceChecksTypes.BRANCH_PORTFOLIO_OVERALL_HEALTH,
                compliance_type=ComplianceTypes.DISBURSEMENT,
                compliance_description=f"this user's branch has an average health score of {average_health_score}, lower than threshold",
            )
            return True

        return False

    def monitor_repayment_patterns_check(self):
        """
        If repayment patterns show irregularities or delays
        """
        ...

    def missed_partial_repayments_check(self):
        """
        Set up alerts for early warning signs of potential defaults, such as missed payments or partial repayments.
        """
        ...

    def agents_not_following_up_delinquent_accounts_check(self):
        """
        Ensure that agents are following up on repayments and taking necessary actions for delinquent accounts.
        """
        ...
