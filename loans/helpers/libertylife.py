import base64
import json

import requests
from django.conf import settings

from ajo.models import AjoSaving
from health_insurance.models import SaveForHealthInsurance
from health_insurance.models_choices import SavingsType


class LibertyLifeMgr:
    def __init__(self):
        self.environment = settings.ENVIRONMENT
        if self.environment == "development":
            self.base_url = "https://requisitiondev.libertypayng.com/libertylive"
        else:
            self.base_url = "https://backend.libertylifeplus.com"

        self.username = settings.LIBERTY_LIFE_USERNAME
        self.password = settings.LIBERTY_LIFE_PASSWORD
        string_value = f"{self.username}:{self.password}"
        auth_token = base64.b64encode(string_value.encode("ascii")).decode("utf-8")

        self.headers = {
            "Authorization": f"Basic {auth_token}",
            "Content-Type": "application/json",
        }

    def request_result_handler(self, payload, response, url):
        """
        Handles the response from API requests, returning a structured result.

        Args:
            payload (dict): The data sent with the request.
            response (requests.Response): The response object returned by the API.
            url (str): The URL that was called.

        Returns:
            dict: A dictionary containing the request result, including status, response,
                  and other related information.
        """
        try:
            response = {
                "url": url,
                "status_code": response.status_code,
                "status": "success",
                "response": response.json(),
                "method": response.request.method,
                "payload": payload,
            }
        except (
            requests.exceptions.RequestException,
            Exception,
            json.decoder.JSONDecodeError,
        ) as e:
            response = {
                "url": url,
                "error": str(e),
                "status_code": response.status_code,
                "status": "failed",
                "method": response.request.method,
                "response": response.text,
                "payload": payload,
            }
        return response

    def create_plan(
        self,
        phone_number,
        first_name: str,
        loan_id: str,
        expected_renewal: int,
        amount: float,
        last_name: str,
        type_of_id: str,
        id_value: str,
        health_plan_duration: int,
        renew_health_plan: bool = False,
    ):
        url = f"{self.base_url}/create_health_insurance_for_borrowers/"

        payload = json.dumps(
            {
                "loan_id": loan_id,
                "expected_renewal": expected_renewal,
                "amount": amount,
                "phone_number": phone_number,
                "first_name": first_name,
                "last_name": last_name,
                "type_of_id": type_of_id,
                "id_value": id_value,
                "health_plan_duration": health_plan_duration,
                "renew_health_plan": renew_health_plan,
            }
        )

        response = requests.post(url, headers=self.headers, data=payload)
        return self.request_result_handler(payload=payload, response=response, url=url)
    
    def create_plan_for_savings(
        self,
        activation_fee: float,
        policy_fee: float,
        savings_type: SavingsType,
        plan_for: str,
        plan_type: str,
        payment_reference: str,
        health_savings: SaveForHealthInsurance = None,
        ajo_savings: AjoSaving = None
        
    ):
        url = f"{self.base_url}/seeds_and_pennies_savings/health_plan/create/"
        
        ajo_user = ajo_savings.ajo_user if savings_type == "AJO_SAVINGS" else health_savings.ajo_user
        payload = {
            "first_name": ajo_user.first_name,
            "middle_name": ajo_user.alias,
            "last_name": ajo_user.last_name,
            "phone_number": ajo_user.phone_number,
            "gender": ajo_user.gender,
            "date_of_birth": ajo_user.dob.strftime("%Y-%m-%d"),
            "amount": policy_fee,
            "activation_amount": activation_fee,
            "activation_payment_reference": payment_reference,
            "savings_id": ajo_savings.id if savings_type == "AJO_SAVINGS" else health_savings.id,
            "plan_type": plan_for,
            "provider_plan_code": plan_type
        }
        
        response = requests.post(url, data=payload)
        return self.request_result_handler(payload=payload, response=response, url=url)

    def get_recent_plan(self, phone_number: str):
        url = f"{self.base_url}/customer_recent_health_plan/{phone_number}"

        response = requests.get(url, headers=self.headers)
        return self.request_result_handler(payload={}, response=response, url=url)

    def get_pending_credit_health_request(self, access_code_or_phone_no):

        if not access_code_or_phone_no:
            url = f"{self.base_url}/credit-health/pending-credit-health-request/"
        else:
            url = f"{self.base_url}/credit-health/pending-credit-health-request/?search={access_code_or_phone_no}"

        response = requests.get(url, headers=self.headers, data={})

        return self.request_result_handler(payload={}, response=response, url=url)

    def update_credit_health_request_status_stages(
        self, loan_access_code, request_status=None, stage=None
    ):

        url = f"{self.base_url}/credit-health/pending-credit-health-request/"
        payload = payload = json.dumps(
            {
                "loan_access_code": f"{loan_access_code}",
                "status": request_status,
                "stage": stage,
            },
        )

        response = requests.patch(url, headers=self.headers, data=payload)

        return self.request_result_handler(payload=payload, response=response, url=url)

    def get_credit_health_details(self, loan_access_code):

        url = f"{self.base_url}/credit-health/fetch-credit-health-details/{loan_access_code}/"

        response = requests.get(url, headers=self.headers, data={})

        return self.request_result_handler(payload={}, response=response, url=url)
