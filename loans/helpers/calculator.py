import json
import ast
import math
import re
import os
from collections import defaultdict

# from itertools import groupby

from datetime import datetime, timedelta, timezone
from pprint import pprint

from dateutil.relativedelta import relativedelta
from dateutil.rrule import DAILY, rrule

# from loans.helpers.apis.agency_banking import LibertyPayMgr


def to_valid_float(value):
    try:
        # If the value is already a float, return it
        if isinstance(value, float):
            return value

        # Convert the input to a string and remove all non-numeric characters except for the decimal point
        clean_value = re.sub(r"[^\d.]", "", str(value))

        # Convert the cleaned string to a float
        return float(clean_value)
    except ValueError:
        # Return None if the value cannot be converted
        return None


def parse_to_dict(data):
    """
    Parse input data to a Python dictionary.

    Args:
        data (str | dict): Input data which could be a JSON string or dictionary.

    Returns:
        dict: Parsed dictionary.

    Raises:
        ValueError: If the input is neither a dictionary nor a JSON string.
    """
    # print(type(data))
    if isinstance(data, dict):
        # Already a dictionary, return it as is.
        return data
    elif isinstance(data, str):
        try:
            # Attempt to convert the JSON string to a dictionary.
            return json.loads(data)
        except json.JSONDecodeError as e:
            try:
                return ast.literal_eval(data)
            except (ValueError, SyntaxError) as e:
                raise ValueError(f"Invalid JSON string: {e}")
    else:
        # Raise error for unsupported types.
        raise ValueError("Input data must be a dictionary or a JSON string.")


class DictToObject:
    """
    A utility class that converts a dictionary into an object,
    allowing access to dictionary keys as if they were attributes.

    Attributes are dynamically created based on the keys of the dictionary
    passed to the constructor.

    Example:
        data = {"name": "Alice", "age": 25}
        person = DictToObject(data)
        print(person.name)  # Output: Alice
        print(person.age)   # Output: 25
    """

    def __init__(self, config_data):
        self.__dict__.update(config_data)

    def to_dict(self):
        """
        Converts the object back to a dictionary representation.

        Returns:
            dict: A dictionary where each attribute of the object is a key-value pair.
        """
        return self.__dict__

    def __str__(self):
        """Defines the user-friendly string representation of the object."""
        data_len = len(self.__dict__)
        return f"DictToObject({data_len})"


class LoanCalculator:

    def __init__(
        self,
        constant,  # ConstantTable model
        time_line=None,
        principal=None,
        stock_price=None,  # for BNPL loans
        duration=None,
        holidays=None,
        extra_days=None,
        loan_type=None,
        eligibility_checker_data=None,
        business_suite=None,
        default_start_date=None,
        topup=False,
    ):

        if principal is not None and principal <= 0:
            raise ValueError("Principal amount must be greater than zero.")

        self.principal = principal
        self.stock_price = stock_price
        self.extra_days = extra_days
        self.const = constant
        self.duration = duration
        self.loan_type = loan_type
        self.holidays = holidays
        self.default_start_date = default_start_date
        self.eligibility_checker = eligibility_checker_data
        self.business_suite = business_suite
        self.topup = topup
        self.time_line = (
            time_line  # returns tuple of the start date end date and duration or None
        )

    def round_up_to_cents(self, amount):
        """
        Rounds an amount to two decimal places, with a bias towards rounding up.

        Args:
            amount (float): The amount to be rounded.

        Returns:
            float: The rounded amount.
        """
        return round(amount + 0.005, 2)

    def get_duration_label_by_days(self):
        """
        Returns a string representation of the tenure in months or weeks
        based on the `duration` attribute of the instance.

        The method uses a dictionary mapping, `duration_dict`, where:
        - Keys are string representations of durations in days (e.g., "30" for 1 month).
        - Values are strings indicating the tenure period (e.g., "1_MONTH", "6_WEEKS").

        Returns:
            str: A string representing the tenure in months or weeks
                if `self.duration` is found in `duration_dict`.
                Returns `None` if the duration is not in the dictionary.

        Example:
            If `self.duration` is 60, this will return "2_MONTH".
        """

        duration_dict = {
            "30": "1_MONTH",
            "45": "6_WEEKS",
            "60": "2_MONTH",
            "90": "3_MONTH",
            "120": "4_MONTH",
            "150": "5_MONTH",
            "180": "6_MONTH",
        }
        return duration_dict.get(str(self.duration))

    def get_startdt_enddt(self):
        """This is a a result of fixing wekeends durations fixes
        NB:"""
        # print(self.time_line, "\n\n")
        if not self.time_line:

            days_offset = self.const.repayment_grace_period

            if not self.default_start_date:
                start_date = datetime.now().date() + timedelta(days=days_offset)
            else:
                start_date = self.default_start_date
            future_date = start_date + timedelta(days=self.duration)

            weekends = sum(
                1
                for dt in rrule(
                    DAILY, dtstart=start_date, until=future_date + relativedelta(days=1)
                )
                if dt.weekday() in [5, 6]
            )

            num_of_days = (
                (future_date - start_date).days - (self.holidays + weekends)
            ) + self.extra_days
            end_date = future_date + timedelta(days=self.extra_days)

            return start_date, end_date, num_of_days

        else:
            return self.time_line

    def months(self):
        """Converts a number of days into the corresponding number of months."""
        # Define ranges for each month
        data = {
            (1, 30): 1,  # Up to 30 days is considered 1 month
            (31, 60): 2,  # 31 to 60 days is considered 2 months
            (61, 90): 3,  # 61 to 90 days is considered 3 months
            (91, 120): 4,  # 91 to 120 days is considered 4 months
            (121, 150): 5,  # 121 to 150 days is considered 5 months
            (151, 180): 6,  # 151 to 180 days is considered 6 months
        }

        for days_range, months in data.items():
            if days_range[0] <= int(self.duration) <= days_range[1]:
                return months

        raise ValueError(
            f"Invaid Loan Duration {self.duration}. Expected count from 1 - 180"
        )

    def _summary(
        self,
        deposit,
        interest_rate,
        interest_amount,
        loan_processing_fee,
        daily_payments,
        total_payments,
        start_date,
        end_date,
        num_of_days,
        actual_deposit=0,
        bnpl_amount=0.0,
    ):

        charge_health_insurance = self.const.charge_health_insurance_fee
        health_insurance_fee = self.const.health_insurance_fee
        # health_insurance_daily_fee = self.const.health_insurance_daily_fee

        principal_repayment = total_payments
        principal_daily_repayment = daily_payments
        # Check if health insurance should be charged
        to_exclude_insurance_fee = [
            "CREDIT_HEALTH",
            "BNPL",
            "MERCHANT",
            "MERCHANT_OVERDRAFT",
        ]
        insurance_fee_on_repayment = 0
        if charge_health_insurance and self.loan_type not in to_exclude_insurance_fee:

            total_renewal_fee = health_insurance_fee * self.months()
            insurance_fee_on_repayment = total_renewal_fee / num_of_days
            insurance_fee_on_repayment = (
                math.ceil(insurance_fee_on_repayment * 100) / 100
            )

            daily_payments += insurance_fee_on_repayment

            # Add the total health insurance fee to the repayment amount if tenure is more than one month
            # repayment_amount += health_insurance_fee * (months - 1)
            total_payments += health_insurance_fee * self.months()

        weekly_payments = daily_payments * 5

        summary = {
            "actual_deposit": actual_deposit,
            "deposit": deposit,
            "duration_label": self.get_duration_label_by_days(),
            "insurance_renewal_fee": health_insurance_fee,
            "insurance_fee_on_repayment": insurance_fee_on_repayment,
            "bnpl_amount": bnpl_amount,
            "tenor_in_month": self.months(),
            "interest_rate": interest_rate,
            "principal_amount": self.principal,
            "interest_amount": round(interest_amount, 2),
            "loan_processing_fee": loan_processing_fee,
            "daily_repayment_amount": self.round_up_to_cents(daily_payments),
            "weekly_repayment": self.round_up_to_cents(weekly_payments),
            "repayment_amount": total_payments,
            "start_date": start_date,
            "end_date": end_date,
            "tenor_in_days": num_of_days,
            "principal_daily_repayment": self.round_up_to_cents(
                principal_daily_repayment
            ),
            "principal_repayment": math.floor(principal_repayment),
            "health_insurance_fee": (
                self.const.health_insurance_activation_fee
                if charge_health_insurance
                else 0
            ),
            "insurance_duration": (
                self.const.insurance_duration if charge_health_insurance else 0
            ),
        }

        return summary

    def _boosta_payments(self):

        boosta_loan_config = self.const.boosta_loan_config

        usable_const = boosta_loan_config.get(str(self.duration))

        try:
            interest_percent_value = usable_const["spreadable_interest"]
            interest_rate = interest_percent_value / 100
            processing_fee_percent = usable_const["processing_fee_percent"] / 100
        except (TypeError, KeyError):
            return None

        start_date, end_date, num_of_days = self.get_startdt_enddt()
        deposit = self.principal * interest_rate

        loan_processing_fee = self.principal * processing_fee_percent
        daily_payments = self.principal / num_of_days

        return self._summary(
            deposit=deposit,
            actual_deposit=deposit,
            interest_rate=interest_percent_value,
            interest_amount=0,
            loan_processing_fee=loan_processing_fee,
            daily_payments=daily_payments,
            total_payments=self.principal,
            start_date=start_date,
            end_date=end_date,
            num_of_days=num_of_days,
        )

    def _5x_payments(self):
        staff_loans_config = self.const.staff_loans_config
        usable_const = staff_loans_config.get(str(self.duration))

        try:
            interest_percent_value = usable_const["spreadable_interest"]
            interest_rate = interest_percent_value / 100
            processing_fee_percent = usable_const["processing_fee_percent"] / 100
        except (TypeError, KeyError) as err:
            # print(err)
            return None

        start_date, end_date, num_of_days = self.get_startdt_enddt()

        loan_processing_fee = self.principal * processing_fee_percent
        interest_amount = self.principal * interest_rate
        total_payments = self.principal + interest_amount
        daily_payments = total_payments / num_of_days
        multiplier = self.const.savings_multiplier
        actual_deposit = self.principal / multiplier

        return self._summary(
            deposit=actual_deposit,
            actual_deposit=actual_deposit,
            interest_rate=interest_percent_value,
            interest_amount=interest_amount,
            loan_processing_fee=loan_processing_fee,
            daily_payments=daily_payments,
            total_payments=total_payments,
            start_date=start_date,
            end_date=end_date,
            num_of_days=num_of_days,
        )

    def _bnpl_payments(self):

        staff_config = self.const.staff_loans_config
        # profit_margin = staff_config.get("profit_margin", 15) / 100
        bnpl_interest = staff_config.get("bnpl_interest", 20) / 100
        bnpl_processing_fee = staff_config.get("bnpl_processing_fee", 4.89)
        bnpl_deposit_percentage = staff_config.get("bnpl_deposit_percentage", 40) / 100

        # Calculate amounts for non-savings created scenario

        # 40% of the selling price plus interest
        forty_percent_expected_amt = bnpl_deposit_percentage * self.principal

        # Principal amount is set to 40% of the selling price plus interest
        principal_amount = forty_percent_expected_amt  # the principal amount in this case act ast the expected deposit

        # Exposure is the difference between the stock price and the 40% expected amount
        try:
            exposure = self.stock_price - forty_percent_expected_amt
        except Exception:
            exposure = 0

        # Repayment amount is the difference between selling price plus interest and the 40% expected amount
        repayment_amt = self.principal - forty_percent_expected_amt

        # Agent disbursement amount is the sum of exposure and the 40% expected amount
        agent_disbursement_amount = exposure + forty_percent_expected_amt

        # Interest amount is the difference between selling price plus interest and agent disbursement amount
        interest_amount = self.principal - agent_disbursement_amount

        # Interest rate is 40% expressed as a percentage
        interest_rate = bnpl_deposit_percentage * 100

        tenor_in_month = 3
        bnpl_amount = self.principal

        ##############################################################################
        ##############################################################################
        ##############################################################################
        ##############################################################################

        start_date, end_date, num_of_days = self.get_startdt_enddt()

        # Calculate daily repayment amount by dividing the repayment amount by the number of days in the loan tenor
        daily_repayment = round(repayment_amt / num_of_days, 2)

        # Calculate loan processing fee as 2% of the agent disbursement amount
        # processing_fee = (processing_fee_percent / 100) * agent_disbursement_amount

        processing_fee = (bnpl_processing_fee / 100) * bnpl_amount
        processing_fee = round(processing_fee, 2)

        # print("EXPOSURE EXPOSURE", exposure)
        # print("EXPOSURE EXPOSURE", exposure)

        principal_amount = principal_amount + processing_fee
        principal_amount = round(principal_amount, 2)
        # return summary
        data = self._summary(
            deposit=principal_amount,
            actual_deposit=principal_amount,
            interest_rate=int(interest_rate),
            interest_amount=interest_amount,
            loan_processing_fee=processing_fee,
            daily_payments=daily_repayment,
            total_payments=repayment_amt,
            start_date=start_date,
            end_date=end_date,
            num_of_days=num_of_days,
            bnpl_amount=bnpl_amount,
        )

        data["agent_disbursement_amount"] = repayment_amt
        data["tenor_in_month"] = tenor_in_month
        data["principal_amount"] = principal_amount

        return data

    def get_interest_summary(self):
        boosta_2x_loans = ["BOOSTA_2X", "BOOSTA_2X_MINI", "CREDIT_HEALTH"]
        if self.loan_type == "BOOSTA":
            return self._boosta_payments()
        elif self.loan_type == "AJO":
            return self._5x_payments()
        elif self.loan_type in boosta_2x_loans:
            return self._boosta_2x_payments()
        elif self.loan_type == "BNPL":
            return self._bnpl_payments()
        elif self.loan_type == "MERCHANT_OVERDRAFT":
            return self._merchant_loan_payments()
        else:
            return None

    def calculate_percentage(self, amount, total_amount):
        if total_amount == 0:
            raise ValueError("Total amount cannot be zero")
        else:
            percentage = (amount / total_amount) * 100
            return round(percentage, 2)

    def _format_amount_to_naira(self, amount):
        return f"N{amount:,.0f}"

    def evaluate_loan_affordability(self, eligible_amount):
        """
        Verifies loan eligibility based on user's financial data and eligibility configurations.
        Returns a dictionary with financial metrics and eligibility information.
        """

        # User's financial data
        checker_data = self.eligibility_checker
        duration = int(checker_data.duration)  # Loan duration in months
        monthly_expenses = checker_data.monthly_expenses
        existing_loan_repayment = checker_data.existing_loan_repayment
        items = checker_data.items

        # Aggregate financial values from items
        total_selling_price = total_unit_sold_per_week = total_profit_per_unit = 0

        for item in items:
            total_selling_price += item["selling_price"]
            total_unit_sold_per_week += item["unit_sold_per_week"]
            total_profit_per_unit += item["profit_per_unit"]

        # Averages
        item_count = len(items)
        avg_selling_price = total_selling_price / item_count
        avg_units_sold_per_week = total_unit_sold_per_week / item_count
        avg_profit_per_unit = total_profit_per_unit / item_count

        # Weekly revenue and profit
        weekly_revenue = avg_selling_price * avg_units_sold_per_week
        weekly_profit = avg_units_sold_per_week * avg_profit_per_unit

        # Daily revenue and profit (assuming 5 days of operation)
        daily_revenue = weekly_revenue / 5
        daily_profit = weekly_profit / 5

        # Net monthly profit after expenses and repayments
        net_monthly_profit = (
            (weekly_profit * 4) - monthly_expenses - existing_loan_repayment
        )

        # Comfortable repayment amounts
        comfortable_daily_repayment = daily_profit * 0.5
        comfortable_weekly_repayment = comfortable_daily_repayment * 5
        alt_comfortable_daily_repayment = net_monthly_profit / duration

        # Eligibility configuration
        boosta_config = self.const.boosta_2x_config
        profit_percentage_for_repayment = boosta_config.get(
            "profit_percentage_for_repayment", 70
        )

        try:
            interest_percent_value = boosta_config[str(duration)]["interest"]
            percentage = interest_percent_value / 100
            day_count = boosta_config.get("day_count", 25) * self.months()
        except (TypeError, KeyError, AttributeError) as err:
            raise ValueError(str(err))

        # Repayment amounts
        total_repayment_amount = comfortable_daily_repayment * day_count
        alt_total_repayment_amount = alt_comfortable_daily_repayment * day_count
        proposed_loan_amount = total_repayment_amount / (percentage + 1)
        alt_proposed_loan_amount = alt_total_repayment_amount / (percentage + 1)

        # Eligibility and percentage difference calculations
        percentage_difference = self.calculate_percentage(
            amount=proposed_loan_amount, total_amount=eligible_amount
        )
        profit_per_unit = avg_profit_per_unit
        earnings_per_unit_percentage = (profit_per_unit / total_selling_price) * 100
        actual_data = {
            "percentage_difference": percentage_difference,
            "able_to_repay": 100 <= percentage_difference <= 110
            and earnings_per_unit_percentage <= 35,
            "duration": duration,
            "proposed_loan_amount": proposed_loan_amount,
            "alt_proposed_loan_amount": alt_proposed_loan_amount,
            "alt_total_repayment_amount": alt_total_repayment_amount,
            "total_repayment_amount": total_repayment_amount,
            "earnings_per_unit_percentage": round(earnings_per_unit_percentage, 2),
        }

        # Result dictionary
        result = {
            "weekly_revenue": weekly_revenue,
            "total_unit_sold_per_week": total_unit_sold_per_week,
            "existing_loan_repayment": existing_loan_repayment,
            "monthly_expenses": monthly_expenses,
            "weekly_profit": weekly_profit,
            "comfortable_weekly_repayment": comfortable_weekly_repayment,
            "comfortable_daily_repayment": comfortable_daily_repayment,
            "daily_revenue": daily_revenue,
            "daily_profit": daily_profit,
            "actual_data": actual_data,
            "eligible_amount": eligible_amount,
            "profit_per_unit": profit_per_unit,
            "total_selling_price": total_selling_price,
            "loan_duration_months": duration,
            "profit_percentage_for_repayment": profit_percentage_for_repayment,
            "interest_rate_per_month": interest_percent_value / duration,
            "item_name": items[0]["item_name"] if items else "",
        }

        return result

    def eligibility_verification(self, eligible_amount):
        """
        This method evaluates loan eligibility based on the user's financial data (such as sales, expenses,
        and loan repayments) and predefined eligibility configurations. It calculates the user's weekly and
        daily revenue, profit, comfortable repayment amounts, and suggests a proposed loan amount using
        reverse percentage calculations.

        The result is a dictionary containing key financial metrics, such as weekly revenue, weekly profit,
        comfortable repayment amounts, and proposed loan amounts.

        Returns:
            dict: A dictionary containing calculated financial metrics and eligibility data.
        """

        from django.db.models.manager import Manager

        # Fetch the user's financial data needed for eligibility evaluation
        checker_data = self.eligibility_checker

        # Fetch predefined eligibility configuration (like day count, percentage) based on the loan duration
        # const_variable = self.const.eligibility_Config

        # Retrieve key data: loan duration, monthly expenses, and existing loan repayment
        duration = int(checker_data.duration)  # Duration of the loan in months

        existing_loan_repayment = (
            checker_data.existing_loan_repayment
        )  # Monthly repayment on existing loans
        monthly_expenses = (
            checker_data.monthly_expenses
        )  # Monthly expenses that reduce net profit

        # Initialize variables for aggregating total selling price, units sold, and profit per unit across items

        # Assume `related_field` is the ManyToMany field
        if isinstance(checker_data.items, Manager):
            item_type = "OBJMGR"
            items = checker_data.items.all()
        else:
            item_type = "PYTHON_LIST"
            items = checker_data.items

        total_selling_price = 0
        total_unit_sold_per_week = 0
        total_profit_per_unit = 0
        item_name = ""
        # Loop through all items sold by the user, accumulate the financial data (price, units sold, profit)
        for item_object in items:

            if item_type == "PYTHON_LIST":
                selling_price = item_object.get(
                    "selling_price"
                )  # Selling price of each item
                unit_sold_per_week = item_object.get(
                    "unit_sold_per_week"
                )  # Number of units sold per week
                profit_per_unit = item_object.get(
                    "profit_per_unit"
                )  # Profit earned on each unit sold
            else:
                selling_price = item_object.selling_price  # Selling price of each item
                unit_sold_per_week = (
                    item_object.unit_sold_per_week
                )  # Number of units sold per week
                profit_per_unit = item_object.profit_per_unit
                item_name = item_object.item_name

            # Add the selling price, units sold, and profit per unit to the total values

            total_selling_price += selling_price
            total_unit_sold_per_week += unit_sold_per_week
            total_profit_per_unit += profit_per_unit

        item_count = len(items)
        avr_total_selling_price = total_selling_price / item_count
        avr_total_unit_sold_per_week = total_unit_sold_per_week / item_count
        avr_total_profit_per_unit = total_profit_per_unit / item_count

        # Calculate the user's total weekly revenue and total weekly profit
        weekly_revenue = (
            avr_total_selling_price * avr_total_unit_sold_per_week
        )  # Weekly revenue = price * units sold
        weekly_profit = (
            avr_total_unit_sold_per_week * avr_total_profit_per_unit
        )  # Weekly profit = profit/unit * units sold

        # Calculate daily revenue and daily profit, assuming the user operates 5 days a week
        daily_revenue = weekly_revenue / 5  # Spread weekly revenue across 5 days
        daily_profit = weekly_profit / 5  # Spread weekly profit across 5 days

        # Calculate the user's net monthly profit after deducting monthly expenses and existing loan repayments
        net_monthly_profit = (
            (weekly_profit * 4) - monthly_expenses - existing_loan_repayment
        )  # Multiply weekly profit by 4 (weeks in a month), subtract expenses and loan repayments

        # Calculate the comfortable daily and weekly repayment amounts
        # 50% of the daily profit is used to calculate the comfortable repayment
        comfortable_daily_repayment = (
            daily_profit * 0.5
        )  # User can afford to repay 50% of their daily profit
        comfortable_weekly_repayment = (
            comfortable_daily_repayment * 5
        )  # Multiply by 5 to get weekly repayment

        # Alternatively, calculate the comfortable daily repayment based on net monthly profit and loan duration
        alt_comfortable_daily_repayment = (
            net_monthly_profit / duration
        )  # Spread net monthly profit over the loan duration

        # Retrieve the eligibility configuration for the given loan duration (from const_variable)

        boosta_2x_config = self.const.boosta_2x_config
        # print(self.months())
        profit_percentage_for_repayment = boosta_2x_config.get(
            "profit_percentage_for_repayment", 70
        )
        try:
            interest_percent_value = boosta_2x_config.get(str(self.duration)).get(
                "interest"
            )
            percentage = interest_percent_value / 100
            day_count = boosta_2x_config.get("day_count", 25) * self.months()

        except (TypeError, KeyError, AttributeError) as err:
            # return None
            raise ValueError(str(err))

        # Calculate the total repayment amount based on comfortable daily repayment and day count
        total_repayment_amount = comfortable_daily_repayment * day_count

        # Calculate the alternative total repayment amount based on the alternative daily repayment
        alt_total_repayment_amount = alt_comfortable_daily_repayment * day_count

        # **Reverse Percentage Calculation**:
        # The `proposed_loan_amount` is derived by reversing a percentage calculation.
        # Normally, you'd calculate the total repayment amount by adding a certain percentage to the loan amount.
        # In this case, to get the proposed loan amount, we reverse that logic:
        #    total_repayment_amount = loan_amount + (percentage * loan_amount)
        # To solve for `loan_amount`, we divide by `(percentage + 1)`, because:
        #    loan_amount = total_repayment_amount / (percentage + 1)
        proposed_loan_amount = total_repayment_amount / (percentage + 1)

        # Similarly, calculate the alternative proposed loan amount using the alternative total repayment
        alt_proposed_loan_amount = alt_total_repayment_amount / (percentage + 1)
        percentage_difference = self.calculate_percentage(
            amount=proposed_loan_amount, total_amount=eligible_amount
        )

        # Construct the actual data dictionary that will be returned
        earnings_per_unit_percentage = (profit_per_unit / total_selling_price) * 100
        actual_data = {
            "percentage_difference": percentage_difference,
            "able_to_repay": percentage_difference >= 100
            and percentage_difference <= 110
            and earnings_per_unit_percentage <= 35,
            "duration": duration,  # The loan duration in months
            "proposed_loan_amount": proposed_loan_amount,  # The loan amount that can be proposed
            "alt_proposed_loan_amount": alt_proposed_loan_amount,  # Alternative proposed loan amount
            "alt_total_repayment_amount": alt_total_repayment_amount,  # Alternative total repayment amount
            "total_repayment_amount": total_repayment_amount,  # Total repayment amount for the main calculation
            "earnings_per_unit_percentage": round(earnings_per_unit_percentage, 2),
        }

        # Compile the final result dictionary containing financial metrics and eligibility-related data
        result = {
            "weekly_revenue": weekly_revenue,  # Weekly revenue (total revenue from selling items)
            "total_unit_sold_per_week": total_unit_sold_per_week,  # Weekly revenue (total revenue from selling items)
            "existing_loan_repayment": existing_loan_repayment,  # Weekly revenue (total revenue from selling items)
            "monthly_expenses": monthly_expenses,  # Weekly revenue (total revenue from selling items)
            "weekly_profit": weekly_profit,  # Weekly profit (total profit from selling items)
            "comfortable_weekly_repayment": comfortable_weekly_repayment,  # Comfortable weekly loan repayment amount
            "comfortable_daily_repayment": comfortable_daily_repayment,  # Comfortable daily loan repayment amount
            "daily_revenue": daily_revenue,  # Daily revenue (revenue spread over 5 business days)
            "daily_profit": daily_profit,  # Daily profit (profit spread over 5 business days)
            "actual_data": actual_data,  # Eligibility data including proposed loan amounts and repayment amounts
            "eligible_amount": eligible_amount,  # Eligible amount
            "profit_per_unit": profit_per_unit,  # Eligible amount
            "total_selling_price": total_selling_price,  # Eligible amount
            "loan_duration_months": duration,
            "profit_percentage_for_repayment": profit_percentage_for_repayment,
            "interest_rate_per_month": interest_percent_value / duration,
            "item_name": item_name,
        }

        # Return the result with all calculated data
        return result

    def _boosta_2x_payments(self):

        boosta_2x_config = self.const.boosta_2x_config
        max_loan_amount = self.const.max_loan_amount
        mini_config = boosta_2x_config.get("mini_config")
        business_suite_amount = boosta_2x_config.get("business_suite", 150000)
        deposit_rate = boosta_2x_config.get("deposit_rate")
        x2_extra_rate = boosta_2x_config.get("extra_rate", 2)
        total_deposit_rate = (deposit_rate + x2_extra_rate) / 100
        insurance_rate = boosta_2x_config.get("insurance_rate") / 100

        checker = boosta_2x_config.get("checker", False)

        # pprint(boosta_2x_config)
        _mini_2x = ["BOOSTA_2X_MINI", "CREDIT_HEALTH"]
        if self.loan_type in _mini_2x:
            max_offer = mini_config.get("max_offer")
            # print(self.topup)
            if not self.topup:
                if self.principal > max_offer:
                    raise ValueError(
                        f"The amount you are trying to apply for, N({self.principal}) exceeds the maximum loan amount currently available, N({max_offer}). Please adjust your requested amount to meet the limit."
                    )
        # usable_const = boosta_loan_config.get(str(self.duration))

        try:
            interest_percent_value = boosta_2x_config.get(str(self.duration)).get(
                "interest"
            )
            interest_rate = interest_percent_value / 100
        except (TypeError, KeyError, AttributeError):
            return None

        start_date, end_date, num_of_days = self.get_startdt_enddt()
        if self.loan_type == "CREDIT_HEALTH":
            health_insurance_fee = 0
        else:
            health_insurance_fee = self.const.health_insurance_activation_fee

        actual_deposit = self.principal * total_deposit_rate

        deposit = actual_deposit / 2

        loan_processing_fee = actual_deposit / 2

        interest_amount = self.principal * interest_rate
        total_payments = self.principal + interest_amount
        daily_payments = total_payments / num_of_days

        insurance_percentage_amount = self.principal * insurance_rate
        data = self._summary(
            actual_deposit=actual_deposit,
            deposit=deposit + health_insurance_fee,
            interest_rate=interest_percent_value,
            interest_amount=interest_amount,
            loan_processing_fee=loan_processing_fee,
            daily_payments=daily_payments,
            total_payments=total_payments,
            start_date=start_date,
            end_date=end_date,
            num_of_days=num_of_days,
        )
        if self.business_suite:
            data["business_suite"] = business_suite_amount

        data["disbursement_amount"] = self.principal - insurance_percentage_amount
        data["boosta_insurance"] = insurance_percentage_amount
        data["checker"] = (
            checker if checker is True else self.principal > max_loan_amount
        )
        return data

    def _merchant_loan_payments(self):

        merchant_loan_config = self.const.merchant_loan_config
        interest_rate = merchant_loan_config.get("interest", 5)
        loan_interest = interest_rate / 100
        loan_processing_fee = merchant_loan_config.get("loan_processing_fee", 500.0)

        start_date, end_date, num_of_days = self.get_startdt_enddt()

        interest_amount = self.principal * loan_interest
        total_payments = self.principal + interest_amount
        daily_payments = total_payments / num_of_days

        data = self._summary(
            # actual_deposit=actual_deposit,
            deposit=0.0,
            interest_rate=interest_rate,
            interest_amount=interest_amount,
            loan_processing_fee=loan_processing_fee,
            daily_payments=daily_payments,
            total_payments=total_payments,
            start_date=start_date,
            end_date=end_date,
            num_of_days=num_of_days,
        )

        return data

    def evaluate_merchant_eligibility(
        self,
        daily_total_payment_volume,
        transaction_count,
        actual_day_count,
        merchant_has_terminal,
    ):
        """
        This method evaluates the eligibility of a merchant for a loan based on their daily payment volume
        and the loan configuration parameters.
        """

        merchant_loan_config = self.const.merchant_loan_config

        # Retrieve loan configuration parameters
        min_amount = merchant_loan_config.get("min_amount", 150000)
        max_amount = merchant_loan_config.get("max_amount", 400000)
        has_terminal = merchant_loan_config.get("has_terminal", True)
        target_percentage = merchant_loan_config.get("target_percentage", 25) / 100
        duration = merchant_loan_config.get("duration", 14)
        interest = merchant_loan_config.get("interest", 5) / 100

        # Calculate the expected daily repayment and total repayment
        expected_daily_repayment = daily_total_payment_volume * target_percentage
        total_repayment = expected_daily_repayment * duration

        # Calculate interest amount
        total_repayment_percent = 1 + interest
        interest_amount = (total_repayment * interest) / total_repayment_percent

        # Calculate actual principal and adjust based on the maximum allowable principal
        actual_principal_allowable = round(total_repayment - interest_amount, 2)
        principal = (
            max_amount
            if actual_principal_allowable > max_amount
            else actual_principal_allowable
        )

        principal = math.floor(principal / 1000) * 1000

        # Check eligibility
        # eligible = principal > min_amount and transaction_count >= days_in_range
        # eligible = principal > min_amount and transaction_count >= actual_day_count
        eligible = principal > min_amount and has_terminal == merchant_has_terminal

        # Return the calculation results
        interest_on_principal = principal * interest
        total_repayment_on_actual_principal = principal + interest_on_principal
        score = (actual_principal_allowable / max_amount) * 100
        return {
            "eligible": eligible,
            "daily_total_payment_volume": daily_total_payment_volume,
            "principal": principal,
            "score": score,
            "target_percentage": target_percentage,
            "interest_amount": round(interest_on_principal, 2),
            "total_repayment_percent": total_repayment_percent,
            "min_loan_amount": min_amount,
            "max_loan_amount": max_amount,
            "actual_principal_allowable": actual_principal_allowable,
            "expected_daily_repayment": total_repayment_on_actual_principal / duration,
            "total_repayment": round(total_repayment_on_actual_principal, 2),
            "duration": duration,
        }


def get_constants():

    # Get the current working directory
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # Construct the file path dynamically
    file_path = os.path.join(current_dir, "const.json")

    # Open and read the JSON file
    with open(file_path, "r") as file:
        data = json.load(file)  # Parses the JSON file into a Python dictionary or list

    # Print the data
    # print(data)
    return DictToObject(data[0])


# const = get_constants()
# # # pprint(const.__dict__)
# loancalc = LoanCalculator(
#     principal=300000,
#     duration=90,
#     loan_type="BNPL",
#     constant=const,
#     # eligibility_checker_data=Config(const_data2),
#     extra_days=5,
#     holidays=3,
#     stock_price=200000,
# )

# # stdt = loancalc.eligibility_verification(eligible_amount=1000000)
# # # stdt = loancalc.calculate_percentage(amount=11000, total_amount=10000)
# stdt = loancalc.get_interest_summary()
# # stdt = loancalc._boosta_2x_payments()
# # stdt = loancalc._bnpl_payments()

# pprint(stdt)
