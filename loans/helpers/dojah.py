import datetime
import random
import time
from dataclasses import dataclass

import requests
from django.conf import settings


@dataclass
class DojahAPI:
    environment = settings.ENVIRONMENT

    if environment == "production":
        base_url = "https://api.dojah.io"
        token = settings.DOJAH_PROD_TOKEN
    else:
        base_url = "https://sandbox.dojah.io"
        token = settings.DOJAH_SANDBOX_TOKEN

    app_id = settings.DOJAH_APP

    headers = {"accept": "application/json", "Authorization": token, "AppId": app_id}

    def verify_nin(self, nin):
        url = f"{self.base_url}/api/v1/kyc/nin?nin={nin}"
        response = requests.get(url, headers=self.headers)
        return response

    @staticmethod
    def generate_request_id() -> str:
        timestamp = int(time.time() * 1000)  # Current timestamp in milliseconds
        random_number = random.randint(1000, 9999)  # Random 4-digit number
        unique_ref = f"{timestamp}-{random_number}"
        return unique_ref

    def pvc_verification(self, pvc: str):

        url = f"{self.base_url}/api/v1/kyc/vin?vin={pvc}"
        response = requests.get(url, headers=self.headers)
        return response

    def verify_bvn(self, bvn):

        url = f"{self.base_url}/api/v1/kyc/bvn/full?bvn={bvn}"
        response = requests.get(url, headers=self.headers)
        return response


# Example usage:
if __name__ == "__main__":
    # token = ''
    # verifier = YouVerifyAPI(token)
    # nin = "11111111111"
    # response = verifier.verify_nin(nin)
    # print(response)
    #
    pass
