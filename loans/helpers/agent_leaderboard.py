import math

from django.contrib.auth import get_user_model
from django.db.models import Count, Sum
from django.utils import timezone

from accounts.agency_banking import AgencyBankingClass
from accounts.model_choices import UserType
from admin_dashboard.tasks import DateUtility
from ajo.models import AjoSaving
from payment.model_choices import (
    PlanType,
    Status,
    TransactionFormType,
    TransactionTypeCreditOrDebitChoices,
    WalletTypes,
)
from payment.models import Transaction

from ..models import AjoLoan, AjoLoanRepayment
from .helpers import get_percentages


def mask_winners_phone_number(phone_number):
    if phone_number:
        if len(phone_number) >= 10:
            formatted_num = "234" + phone_number[-10:]
            first_covered = formatted_num[0:5]
            second_covered = formatted_num[-3:]
            total_covered = first_covered + "*****" + second_covered
            return total_covered
        else:
            return ""
    else:
        return ""


def truncate_percent(amount):
    return math.floor(amount * 10**2) / 10**2


def custom_sort(item):
    return (
        -item["overall_performance"],
        -item["savings_performance"],
        -item["loans_performance"],
        -item["all_time_total_disbursed_amount"],

    )
def staff_agent_custom_sort(item):
    return (
        -item["total_disbursed_amount"],
        -item["total_disbursed_count"],
        -item["overall_performance"],
        -item["all_time_total_disbursed_amount"],
    )


def get_agent_leaderboard(date_type, branch_filter):

    User = get_user_model()

    disbursed_loans_status_list = ["COMPLETED", "OPEN"]
    previous_day = DateUtility().previous_day
    today = DateUtility().today_date
    week_start = DateUtility().week_start
    month_start = DateUtility().month_start

    # Write queries
    loan_officers_qs = User.objects.filter(user_type=UserType.STAFF_AGENT)
    ajo_loans_qs = AjoLoan.objects.filter()
    ajo_loan_repayment_qs = AjoLoanRepayment.objects.all()
    ajo_savings_table_qs = AjoSaving.objects.all()
    loans_disbursed_status = ajo_loans_qs.filter(status__in=disbursed_loans_status_list)
    ajo_savings_transactions_qs = Transaction.objects.filter(
        plan_type=PlanType.AJO,
        wallet_type=WalletTypes.AJO_USER,
        status=Status.SUCCESS,
        transaction_form_type=TransactionFormType.WALLET_DEPOSIT,
    )
    loan_officers_report_list = []

    if date_type == "daily":
        date_filter = today
        loans_disbursed = loans_disbursed_status.filter(created_at__date=previous_day.date())
        previous_day_agent_repayment = ajo_loan_repayment_qs.filter(paid_date__date=previous_day.date())
        ajo_savings_transactions = ajo_savings_transactions_qs.filter(date_created__date=previous_day.date())

        avg_savings_sum = 3826
        avg_loan_sum = 15306

    elif date_type == "weekly":
        date_filter = week_start
        loans_disbursed = loans_disbursed_status.filter(created_at__gte=week_start)
        previous_day_agent_repayment = ajo_loan_repayment_qs.filter(paid_date__gte=week_start)
        ajo_savings_transactions = ajo_savings_transactions_qs.filter(date_created__gte=week_start)

        avg_savings_sum = 187500
        avg_loan_sum = 750000

    elif date_type == "monthly":
        date_filter = month_start
        loans_disbursed = loans_disbursed_status.filter(created_at__gte=month_start)
        previous_day_agent_repayment = ajo_loan_repayment_qs.filter(paid_date__gte=month_start)
        ajo_savings_transactions = ajo_savings_transactions_qs.filter(date_created__gte=month_start)

        avg_savings_sum = 187500
        avg_loan_sum = 750000

    else:
        date_filter = today
        loans_disbursed = loans_disbursed_status.filter(created_at__date=previous_day.date())
        previous_day_agent_repayment = ajo_loan_repayment_qs.filter(paid_date__date=previous_day.date())
        ajo_savings_transactions = ajo_savings_transactions_qs.filter(date_created__date=previous_day.date())

        avg_savings_sum = 3826
        avg_loan_sum = 15306

    ### Define Dates

    # Calculate performance metrics
    for officer in loan_officers_qs:
        loans_disbursed_by_agent_qs = loans_disbursed_status.filter(agent=officer)
        loans_disbursed_by_agent_yesterday_week_qs = loans_disbursed.filter(agent=officer)
        agent_loans_repayment_qs = ajo_loan_repayment_qs.filter(agent=officer)
        previous_day_agent_repayment_qs = previous_day_agent_repayment.filter(agent=officer)
        agent_savings_qs = ajo_savings_transactions_qs.filter(user=officer)
        agent_yesterday_week_savings_qs = ajo_savings_transactions.filter(user=officer)
        agent_ajo_savings_table_qs = ajo_savings_table_qs.filter(user=officer)

        # Loans Disbursed
        agent_total_loan_disbursed_amount = list(loans_disbursed_by_agent_qs.aggregate(Sum("amount")).values())[0]
        agent_total_loan_disbursed_count = list(loans_disbursed_by_agent_qs.aggregate(Count("amount")).values())[0]
        agent_total_loan_disbursed_amount_yesterday_week = list(
            loans_disbursed_by_agent_yesterday_week_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_loan_disbursed_count_yesterday_week = list(
            loans_disbursed_by_agent_yesterday_week_qs.aggregate(Count("amount")).values()
        )[0]
        agent_total_loan_disbursed_amount = (
            agent_total_loan_disbursed_amount if agent_total_loan_disbursed_amount else 0.00
        )
        agent_total_loan_disbursed_count = agent_total_loan_disbursed_count if agent_total_loan_disbursed_count else 0
        agent_total_loan_disbursed_amount_yesterday_week = (
            agent_total_loan_disbursed_amount_yesterday_week
            if agent_total_loan_disbursed_amount_yesterday_week
            else 0.00
        )
        agent_total_loan_disbursed_count_yesterday_week = (
            agent_total_loan_disbursed_count_yesterday_week if agent_total_loan_disbursed_count_yesterday_week else 0
        )
        # Loan Repayments
        agent_total_loan_repayment_amount = list(agent_loans_repayment_qs.aggregate(Sum("repayment_amount")).values())[
            0
        ]
        agent_total_loan_repayment_count = list(
            agent_loans_repayment_qs.aggregate(Count("repayment_amount")).values()
        )[0]
        agent_total_loan_repayment_amount_yesterday_week = list(
            previous_day_agent_repayment_qs.aggregate(Sum("repayment_amount")).values()
        )[0]

        agent_total_loan_repayment_count_yesterday_week = list(
            previous_day_agent_repayment_qs.aggregate(Count("repayment_amount")).values()
        )[0]
        expected_repayment_amount_yesterday_week = list(
            loans_disbursed_by_agent_qs.filter(created_at__lt=date_filter)
            .aggregate(Sum("daily_repayment_amount"))
            .values()
        )[0]
        overall_expected_repayment_amount = list(
            loans_disbursed_by_agent_qs.filter(created_at__lt=date_filter).aggregate(Sum("repayment_amount")).values()
        )[0]
        expected_repayment_amount_yesterday_week = (
            expected_repayment_amount_yesterday_week if expected_repayment_amount_yesterday_week else 0.00
        )
        agent_total_loan_repayment_amount = (
            agent_total_loan_repayment_amount if agent_total_loan_repayment_amount else 0.00
        )
        agent_total_loan_repayment_count = agent_total_loan_repayment_count if agent_total_loan_repayment_count else 0
        agent_total_loan_repayment_amount_yesterday_week = (
            agent_total_loan_repayment_amount_yesterday_week
            if agent_total_loan_repayment_amount_yesterday_week
            else 0.00
        )
        agent_total_loan_repayment_count_yesterday_week = (
            agent_total_loan_repayment_count_yesterday_week if agent_total_loan_repayment_count_yesterday_week else 0
        )

        # Due Repayment Amount Yesterday
        due_loans_qs = ajo_loans_qs.filter(agent=officer, start_date__lte=timezone.now().date())
        loans_due_amount = 0
        for loan in due_loans_qs:
            days_due = (timezone.now().date() - loan.start_date).days
            amount_due = days_due * loan.daily_repayment_amount
            balance = amount_due - loan.total_paid_amount
            loans_due_amount += balance

        outstanding_repayment_amount_yesterday_week = (
            expected_repayment_amount_yesterday_week - agent_total_loan_repayment_amount_yesterday_week
        )
        overall_expected_repayment_amount = (
            overall_expected_repayment_amount if overall_expected_repayment_amount else 0.00
        )
        overall_outstanding_repayment_amount = overall_expected_repayment_amount - agent_total_loan_repayment_amount
        agent_repayment_vs_expected_percentage_yesterday_week = get_percentages(
            agent_total_loan_repayment_amount_yesterday_week,
            loans_due_amount,
        )
        overall_agent_repayment_vs_expected_percentage = get_percentages(
            agent_total_loan_repayment_amount, overall_expected_repayment_amount
        )
        # Savings
        agent_total_savings_amount = list(agent_savings_qs.aggregate(Sum("amount")).values())[0]
        agent_total_active_savings_amount = list(
            agent_ajo_savings_table_qs.filter(is_active=True).aggregate(Sum("amount_saved")).values()
        )[0]
        agent_total_savings_count = list(agent_savings_qs.aggregate(Count("amount")).values())[0]
        agent_total_savings_amount_yesterday_week = list(
            agent_yesterday_week_savings_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_savings_count_yesterday_week = list(
            agent_yesterday_week_savings_qs.aggregate(Count("amount")).values()
        )[0]
        agent_total_savings_amount = agent_total_savings_amount if agent_total_savings_amount else 0.00
        agent_total_savings_count = agent_total_savings_count if agent_total_savings_count else 0
        agent_total_active_savings_amount = (
            agent_total_active_savings_amount if agent_total_active_savings_amount else 0.00
        )
        agent_total_savings_amount_yesterday_week = (
            agent_total_savings_amount_yesterday_week if agent_total_savings_amount_yesterday_week else 0.00
        )
        agent_total_savings_count_yesterday_week = (
            agent_total_savings_count_yesterday_week if agent_total_savings_count_yesterday_week else 0
        )
        expected_repayment_amount_yesterday_week = (
            expected_repayment_amount_yesterday_week if expected_repayment_amount_yesterday_week else 0.00
        )

        # Extra data from Vico
        transactions_count = list(
            ajo_savings_transactions_qs.filter(
                user=officer,
                transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
                # date_created__date=previous_day.date()
            )
            .aggregate(Count("amount"))
            .values()
        )[0]

        login_counts = AgencyBankingClass.get_agency_user_details(
            user_id=officer.customer_user_id,
            start_date=previous_day.date(),
        )

        if "success" in login_counts and login_counts.get("success") == True:
            supervisor = login_counts.get("supervisor")
            branch = login_counts.get("branch")
        else:
            supervisor = ""
            branch = ""

        savings_performance = truncate_percent(agent_total_savings_amount_yesterday_week / avg_savings_sum)
        loans_performance = truncate_percent(agent_total_loan_disbursed_amount_yesterday_week / avg_loan_sum)
        # overall_performance = round((savings_performance + loans_performance) / 2)

        if savings_performance >= 100:
            savings_performance = 100
        else:
            savings_performance = savings_performance

        if loans_performance >= 100:
            loans_performance = 100
        else:
            loans_performance = loans_performance

        overall_performance = truncate_percent((savings_performance + loans_performance) / 2)

        data = {
            "user_id": officer.get_username(),
            "user_name": f"{officer.first_name} {officer.last_name}",
            "email": officer.get_username(),
            "first_name": officer.first_name,
            "last_name": officer.last_name,
            "phone": mask_winners_phone_number(officer.user_phone),
            "phone_number": branch,
            "supervisor": supervisor,
            "coin": agent_total_loan_disbursed_amount_yesterday_week,
            "transaction_count": f"{agent_total_loan_disbursed_amount_yesterday_week}-{branch}",
            "branch": branch,
            "cash_back_amount": agent_total_loan_disbursed_amount_yesterday_week,
            "eligible_transaction_count": agent_total_loan_disbursed_count_yesterday_week,
            "all_time_total_disbursed_amount": agent_total_loan_disbursed_amount,
            "all_time_total_disbursed_count": agent_total_loan_disbursed_count,
            "total_disbursed_amount": agent_total_loan_disbursed_amount_yesterday_week,
            "total_disbursed_count": agent_total_loan_disbursed_count_yesterday_week,
            "overall_expected_repayment_amount": overall_expected_repayment_amount,
            "all_time_total_repayment_amount": agent_total_loan_repayment_amount,
            "all_time_total_repayment_count": agent_total_loan_repayment_count,
            "total_repayment_amount": agent_total_loan_repayment_amount_yesterday_week,
            "total_repayment_count": agent_total_loan_repayment_count_yesterday_week,
            "due_repayment_amount": loans_due_amount,
            "loan_balance_amount": overall_outstanding_repayment_amount,
            "repayment_vs_expected_percentage": f"{round(agent_repayment_vs_expected_percentage_yesterday_week)}%",
            "overall_repayment_vs_expected_percentage": f"{round(overall_agent_repayment_vs_expected_percentage)}%",
            "all_time_total_savings_amount": agent_total_savings_amount,
            "all_time_total_savings_count": agent_total_savings_count,
            "total_active_savings_amount": agent_total_active_savings_amount,
            "total_savings_amount": agent_total_savings_amount_yesterday_week,
            "total_savings_count": agent_total_savings_count_yesterday_week,
            "transaction_count": transactions_count if transactions_count else 0,
            "savings_performance": savings_performance,
            "loans_performance": loans_performance,
            "overall_performance": overall_performance,
        }
        loan_officers_report_list.append(data)

    # Sort the list of dictionaries by the "Overall Performance" key in descending order
    sorted_loan_officers_report_list = sorted(loan_officers_report_list, key=custom_sort)
    if branch_filter:
        loan_officers_data = [item for item in sorted_loan_officers_report_list if item.get("branch") == branch_filter]
    else:
        loan_officers_data = sorted_loan_officers_report_list

    return loan_officers_data

def get_staff_agent_leaderboard(date_type, branch_filter, email):

    User = get_user_model()

    disbursed_loans_status_list = ["COMPLETED", "OPEN"]
    previous_day = DateUtility().previous_day
    today = DateUtility().today_date
    week_start = DateUtility().week_start
    month_start = DateUtility().month_start

    # Write queries
    loan_officers_qs = User.objects.filter(user_type=UserType.STAFF_AGENT)
    ajo_loans_qs = AjoLoan.objects.filter()
    ajo_loan_repayment_qs = AjoLoanRepayment.objects.all()
    ajo_savings_table_qs = AjoSaving.objects.all()
    loans_disbursed_status = ajo_loans_qs.filter(status__in=disbursed_loans_status_list)
    ajo_savings_transactions_qs = Transaction.objects.filter(
        plan_type=PlanType.AJO,
        wallet_type=WalletTypes.AJO_USER,
        status=Status.SUCCESS,
        transaction_form_type=TransactionFormType.WALLET_DEPOSIT,
    )
    loan_officers_report_list = []

    if date_type == "daily":
        date_filter = today
        loans_disbursed = loans_disbursed_status.filter(created_at__date=previous_day.date())
        previous_day_agent_repayment = ajo_loan_repayment_qs.filter(paid_date__date=previous_day.date())
        ajo_savings_transactions = ajo_savings_transactions_qs.filter(date_created__date=previous_day.date())

        avg_savings_sum = 3826
        avg_loan_sum = 15306

    elif date_type == "weekly":
        date_filter = week_start
        loans_disbursed = loans_disbursed_status.filter(created_at__gte=week_start)
        previous_day_agent_repayment = ajo_loan_repayment_qs.filter(paid_date__gte=week_start)
        ajo_savings_transactions = ajo_savings_transactions_qs.filter(date_created__gte=week_start)

        avg_savings_sum = 187500
        avg_loan_sum = 750000

    elif date_type == "monthly":
        date_filter = month_start
        loans_disbursed = loans_disbursed_status.filter(created_at__gte=month_start)
        previous_day_agent_repayment = ajo_loan_repayment_qs.filter(paid_date__gte=month_start)
        ajo_savings_transactions = ajo_savings_transactions_qs.filter(date_created__gte=month_start)

        avg_savings_sum = 187500
        avg_loan_sum = 750000

    else:
        date_filter = today
        loans_disbursed = loans_disbursed_status.filter(created_at__date=previous_day.date())
        previous_day_agent_repayment = ajo_loan_repayment_qs.filter(paid_date__date=previous_day.date())
        ajo_savings_transactions = ajo_savings_transactions_qs.filter(date_created__date=previous_day.date())

        avg_savings_sum = 3826
        avg_loan_sum = 15306

    ### Define Dates

    # Calculate performance metrics
    for officer in loan_officers_qs:
        loans_disbursed_by_agent_qs = loans_disbursed_status.filter(agent=officer)
        loans_disbursed_by_agent_yesterday_week_qs = loans_disbursed.filter(agent=officer)
        agent_loans_repayment_qs = ajo_loan_repayment_qs.filter(agent=officer)
        previous_day_agent_repayment_qs = previous_day_agent_repayment.filter(agent=officer)
        agent_savings_qs = ajo_savings_transactions_qs.filter(user=officer)
        agent_yesterday_week_savings_qs = ajo_savings_transactions.filter(user=officer)
        agent_ajo_savings_table_qs = ajo_savings_table_qs.filter(user=officer)

        # Loans Disbursed
        agent_total_loan_disbursed_amount = list(loans_disbursed_by_agent_qs.aggregate(Sum("amount")).values())[0]
        agent_total_loan_disbursed_count = list(loans_disbursed_by_agent_qs.aggregate(Count("amount")).values())[0]
        agent_total_loan_disbursed_amount_yesterday_week = list(
            loans_disbursed_by_agent_yesterday_week_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_loan_disbursed_count_yesterday_week = list(
            loans_disbursed_by_agent_yesterday_week_qs.aggregate(Count("amount")).values()
        )[0]
        agent_total_loan_disbursed_amount = (
            agent_total_loan_disbursed_amount if agent_total_loan_disbursed_amount else 0.00
        )
        agent_total_loan_disbursed_count = agent_total_loan_disbursed_count if agent_total_loan_disbursed_count else 0
        agent_total_loan_disbursed_amount_yesterday_week = (
            agent_total_loan_disbursed_amount_yesterday_week
            if agent_total_loan_disbursed_amount_yesterday_week
            else 0.00
        )
        agent_total_loan_disbursed_count_yesterday_week = (
            agent_total_loan_disbursed_count_yesterday_week if agent_total_loan_disbursed_count_yesterday_week else 0
        )
        # Loan Repayments
        agent_total_loan_repayment_amount = list(agent_loans_repayment_qs.aggregate(Sum("repayment_amount")).values())[
            0
        ]
        agent_total_loan_repayment_count = list(
            agent_loans_repayment_qs.aggregate(Count("repayment_amount")).values()
        )[0]
        agent_total_loan_repayment_amount_yesterday_week = list(
            previous_day_agent_repayment_qs.aggregate(Sum("repayment_amount")).values()
        )[0]

        agent_total_loan_repayment_count_yesterday_week = list(
            previous_day_agent_repayment_qs.aggregate(Count("repayment_amount")).values()
        )[0]
        expected_repayment_amount_yesterday_week = list(
            loans_disbursed_by_agent_qs.filter(created_at__lt=date_filter)
            .aggregate(Sum("daily_repayment_amount"))
            .values()
        )[0]
        overall_expected_repayment_amount = list(
            loans_disbursed_by_agent_qs.filter(created_at__lt=date_filter).aggregate(Sum("repayment_amount")).values()
        )[0]
        expected_repayment_amount_yesterday_week = (
            expected_repayment_amount_yesterday_week if expected_repayment_amount_yesterday_week else 0.00
        )
        agent_total_loan_repayment_amount = (
            agent_total_loan_repayment_amount if agent_total_loan_repayment_amount else 0.00
        )
        agent_total_loan_repayment_count = agent_total_loan_repayment_count if agent_total_loan_repayment_count else 0
        agent_total_loan_repayment_amount_yesterday_week = (
            agent_total_loan_repayment_amount_yesterday_week
            if agent_total_loan_repayment_amount_yesterday_week
            else 0.00
        )
        agent_total_loan_repayment_count_yesterday_week = (
            agent_total_loan_repayment_count_yesterday_week if agent_total_loan_repayment_count_yesterday_week else 0
        )

        # Due Repayment Amount Yesterday
        due_loans_qs = ajo_loans_qs.filter(agent=officer, start_date__lte=timezone.now().date())
        loans_due_amount = 0
        for loan in due_loans_qs:
            days_due = (timezone.now().date() - loan.start_date).days
            amount_due = days_due * loan.daily_repayment_amount
            balance = amount_due - loan.total_paid_amount
            loans_due_amount += balance

        outstanding_repayment_amount_yesterday_week = (
            expected_repayment_amount_yesterday_week - agent_total_loan_repayment_amount_yesterday_week
        )
        overall_expected_repayment_amount = (
            overall_expected_repayment_amount if overall_expected_repayment_amount else 0.00
        )
        overall_outstanding_repayment_amount = overall_expected_repayment_amount - agent_total_loan_repayment_amount
        agent_repayment_vs_expected_percentage_yesterday_week = get_percentages(
            agent_total_loan_repayment_amount_yesterday_week,
            loans_due_amount,
        )
        overall_agent_repayment_vs_expected_percentage = get_percentages(
            agent_total_loan_repayment_amount, overall_expected_repayment_amount
        )
        # Savings
        agent_total_savings_amount = list(agent_savings_qs.aggregate(Sum("amount")).values())[0]
        agent_total_active_savings_amount = list(
            agent_ajo_savings_table_qs.filter(is_active=True).aggregate(Sum("amount_saved")).values()
        )[0]
        agent_total_savings_count = list(agent_savings_qs.aggregate(Count("amount")).values())[0]
        agent_total_savings_amount_yesterday_week = list(
            agent_yesterday_week_savings_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_savings_count_yesterday_week = list(
            agent_yesterday_week_savings_qs.aggregate(Count("amount")).values()
        )[0]
        agent_total_savings_amount = agent_total_savings_amount if agent_total_savings_amount else 0.00
        agent_total_savings_count = agent_total_savings_count if agent_total_savings_count else 0
        agent_total_active_savings_amount = (
            agent_total_active_savings_amount if agent_total_active_savings_amount else 0.00
        )
        agent_total_savings_amount_yesterday_week = (
            agent_total_savings_amount_yesterday_week if agent_total_savings_amount_yesterday_week else 0.00
        )
        agent_total_savings_count_yesterday_week = (
            agent_total_savings_count_yesterday_week if agent_total_savings_count_yesterday_week else 0
        )
        expected_repayment_amount_yesterday_week = (
            expected_repayment_amount_yesterday_week if expected_repayment_amount_yesterday_week else 0.00
        )

        # Extra data from Vico
        transactions_count = list(
            ajo_savings_transactions_qs.filter(
                user=officer,
                transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
                # date_created__date=previous_day.date()
            )
            .aggregate(Count("amount"))
            .values()
        )[0]

        login_counts = AgencyBankingClass.get_agency_user_details(
            user_id=officer.customer_user_id,
            start_date=previous_day.date(),
        )

        if "success" in login_counts and login_counts.get("success") == True:
            supervisor = login_counts.get("supervisor")
            branch = login_counts.get("branch")
        else:
            supervisor = ""
            branch = ""

        savings_performance = truncate_percent(agent_total_savings_amount_yesterday_week / avg_savings_sum)
        loans_performance = truncate_percent(agent_total_loan_disbursed_amount_yesterday_week / avg_loan_sum)
        # overall_performance = round((savings_performance + loans_performance) / 2)

        if savings_performance >= 100:
            savings_performance = 100
        else:
            savings_performance = savings_performance

        if loans_performance >= 100:
            loans_performance = 100
        else:
            loans_performance = loans_performance

        overall_performance = truncate_percent((savings_performance + loans_performance) / 2)

        data = {
            "user_id": officer.get_username(),
            "user_name": f"{officer.first_name} {officer.last_name}",
            "email": officer.get_username(),
            "first_name": officer.first_name,
            "last_name": officer.last_name,
            "phone": mask_winners_phone_number(officer.user_phone),
            "phone_number": branch,
            "supervisor": supervisor,
            "transaction_count": f"{agent_total_loan_disbursed_amount_yesterday_week}-{branch}",
            "branch": branch,
            "coin": int(agent_total_loan_disbursed_amount_yesterday_week),
            "cash_back_amount": agent_total_loan_disbursed_amount_yesterday_week,
            "eligible_transaction_count": agent_total_loan_disbursed_count_yesterday_week,
            "all_time_total_disbursed_amount": agent_total_loan_disbursed_amount,
            "all_time_total_disbursed_count": agent_total_loan_disbursed_count,
            "total_disbursed_amount": agent_total_loan_disbursed_amount_yesterday_week,
            "total_disbursed_count": agent_total_loan_disbursed_count_yesterday_week,
            "overall_expected_repayment_amount": overall_expected_repayment_amount,
            "all_time_total_repayment_amount": agent_total_loan_repayment_amount,
            "all_time_total_repayment_count": agent_total_loan_repayment_count,
            "total_repayment_amount": agent_total_loan_repayment_amount_yesterday_week,
            "total_repayment_count": agent_total_loan_repayment_count_yesterday_week,
            "due_repayment_amount": loans_due_amount,
            "loan_balance_amount": overall_outstanding_repayment_amount,
            "repayment_vs_expected_percentage": f"{round(agent_repayment_vs_expected_percentage_yesterday_week)}%",
            "overall_repayment_vs_expected_percentage": f"{round(overall_agent_repayment_vs_expected_percentage)}%",
            "all_time_total_savings_amount": agent_total_savings_amount,
            "all_time_total_savings_count": agent_total_savings_count,
            "total_active_savings_amount": agent_total_active_savings_amount,
            "total_savings_amount": agent_total_savings_amount_yesterday_week,
            "total_savings_count": agent_total_savings_count_yesterday_week,
            "transaction_count": transactions_count if transactions_count else 0,
            "savings_performance": savings_performance,
            "loans_performance": loans_performance,
            "overall_performance": overall_performance,
        }
        loan_officers_report_list.append(data)

    # Sort the list of dictionaries by the "Overall Performance" key in descending order
    sorted_loan_officers_report_list = sorted(loan_officers_report_list, key=staff_agent_custom_sort)
    if branch_filter:
        loan_officers_data = [item for item in sorted_loan_officers_report_list if item.get("branch") == branch_filter]
    else:
        loan_officers_data = sorted_loan_officers_report_list

    for index, item in enumerate(loan_officers_data, start=1):
        item['rank'] = index
    
    if email:
        user_data = next((item for item in loan_officers_data if item["email"] == email), None)
        this_user_data = [user_data] if user_data else []
    else:
        this_user_data = []

    staff_agent_data = {
        "all_ranks": loan_officers_data,
        "user_rank": this_user_data
    }

    return staff_agent_data
