from datetime import timedelta


class HolidayHelper:

    def is_weekend(self, date):
        return date.weekday() >= 5

    def count_holidays_between_dates(self, start_date, end_date, agent=None):
        from loans.models import Holiday

        """Count the number of holidays between the given start and end dates for a specific agent."""
        holidays = Holiday.objects.filter(date__range=(start_date, end_date))
        agent_holidays = []
        branch_holidays = []

        if agent:
            agent_holidays = holidays.filter(
                agent=agent, type="agent"
            ).values_list("date", flat=True)
            branch_holidays = holidays.filter(
                branch__name=agent.user_branch,
                type="branch",
            ).values_list("date", flat=True)

        company_holidays = holidays.filter(type="company").values_list("date", flat=True)
        all_holidays = list(agent_holidays) + list(branch_holidays) + list(company_holidays)
        
        return len(set(all_holidays))

    def extend_end_date(self, start_date, end_date, agent=None):
        """Extend the end date of the loan based on holidays and weekends."""

        # Count holidays in the current range
        total_days_to_add = (
            self.count_holidays_between_dates(start_date, end_date, agent)
        )
        # Extend the end date by the number of holidays found
        days_added = 0
        while days_added < total_days_to_add:
            end_date += timedelta(days=1)
            if not self.is_weekend(end_date) and not self.holiday_exists(
                end_date, agent
            ):
                days_added += 1
        print("Total days added:", days_added)

        return end_date

    def cal_duration(self, start, end, agent=None):
        from loans.models import Holiday

        total_days = (end - start).days
        total_weekends = sum(
            1
            for day in range(total_days + 1)
            if (start + timedelta(days=day)).weekday() >= 5
        )

        total_holidays = self.count_holidays_between_dates(start, end, agent)
        working_days = total_days - total_weekends
        duration = working_days - total_holidays

        return duration

    def holiday_exists(self, date, agent=None):
        from loans.models import Holiday

        if agent:
            return (
                Holiday.objects.filter(date=date, type="company").exists()
                or Holiday.objects.filter(date=date, type="agent", agent=agent).exists()
                or Holiday.objects.filter(
                    date=date, type="branch", branch__name=agent.user_branch
                ).exists()
            )
        else:
            return Holiday.objects.filter(date=date, type="company").exists()
