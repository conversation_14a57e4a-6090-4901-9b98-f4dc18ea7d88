from typing import Any, Dict

from django.db import transaction as django_transaction
from django.utils import timezone
from django.db.models import F

from ajo.selectors import AjoUser, AjoUserSelector
from payment.model_choices import Status, TransactionFormType, WalletTypes
from payment.models import WalletSystem, Transaction
from payment.services import TransactionService

from .. import models, tasks
from ..enums import  RepaymentType, VerificationStage


def eligibility_more_than_3_and_no_loan(ajo_user: AjoUser) -> bool:
    """
    This function checks if a user has had a loan eligibility
    for more than 3 days and has not taken a loan

    Args:
        ajo_user (AjoUser): the ajo user to check

    Returns:
        bool: T/F
    """
    eligibility = models.LoanEligibility.objects.filter(
        ajo_user=ajo_user,
        active=True,
    ).last()
    if not eligibility:
        return False

    if models.AjoLoan.objects.filter(eligibility=eligibility).exists():
        return False

    today = timezone.localdate()

    days_since_eligibility = (today - eligibility.created_at.date()).days

    if days_since_eligibility > 3:
        return True

    return False


def paid_processing_fee_no_crc_user(ajo_user: Ajo<PERSON>ser) -> bool:
    """
    This function checks if the ajo user has paid processing fee but
    the CRC user is not eligible

    Args:
        ajo_user (AjoUser): the ajo user to check

    Returns:
        bool: T/F
    """

    loan = models.AjoLoan.objects.filter(borrower=ajo_user).last()
    if not loan:
        return False

    if loan.verification_stage == VerificationStage.CREDIT_BUREAU:
        return True

    return False


def does_ajo_user_have_repayment_outstanding(ajo_user: AjoUser) -> None:
    """
    Raises a ValueError if ajo user has an outstanding repayment

    Args:
        ajo_user (AjoUser): the ajo user to check

    Raises:
        ValueError: user has an outstanding loan repayment
    """
    loan = models.AjoLoan.objects.filter(borrower=ajo_user).last()
    if not loan:
        return

    start_date = loan.start_date
    if not start_date:
        return

    if loan.outstanding_days_today > 0:
        raise ValueError("user has an outstanding loan repayment")

    # if loan.repayment_amount > loan.total_paid_amount:
    #     raise ValueError("user has an outstanding loan repayment")

    # today = str(timezone.localdate())
    # expected_repayment_amount_till_date = tasks.custom_difference_between_days(str(start_date), today)

    # if loan.status == LoanStatus.DEFAULTED or expected_repayment_amount_till_date >= 5:
    #     raise ValueError("user has an outstanding loan repayment")


@staticmethod
@django_transaction.atomic
def process_loan_repayment_from_spend_wallet(loan: models.AjoLoan, amount: float | None = None):
    """
    processes repayments for amount due as at yesterday or set amount from the spend wallet of an ajo user
    """
    if not amount:
        amount_to_deduct = loan.due_yesterday - loan.total_paid_amount
    else:
        amount_to_deduct = amount

    ajo_user = loan.borrower
    ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
    wallet = ajo_user_selector.get_spending_wallet()
    # savings = AjoSaving.objects.filter(
    #     ajo_user=ajo_user,
    #     is_active=True,
    #     is_activated=True,
    #     is_loan_repayment=False,
    #     loan=False,
    #     amount_saved__gte=amount_to_deduct,
    # ).first()

    if wallet.available_balance >= amount_to_deduct:
        # debit_transaction = TransactionService.dynamic_deduction_from_wallet_transaction(
        #     user=ajo_user.user,
        #     amount=amount_to_deduct,
        #     wallet_type=wallet.wallet_type,
        #     description=f"Repayment for loan with ID {loan.id}",
        #     transaction_form_type=TransactionFormType.LOAN_REPAYMENT,
        #     ajo_user=ajo_user,
        #     quotation_id=savings.quotation_id,
        # )

        # debit_ajo_plan(
        #     ajo_savings=savings,
        #     amount=amount_to_deduct,
        #     wallet=wallet,
        #     transaction_instance=debit_transaction,
        # )

        repayment_status, _ = models.AjoLoanRepayment.loan_repayment(
            loan=loan,
            amount=amount_to_deduct,
            from_wallet=wallet,
            repayment_type=RepaymentType.SPEND_DEBIT,
        )

        # savings.plan_balance_before = savings.amount_saved
        # savings.amount_saved -= amount_to_deduct
        # savings.plan_balance_after = savings.plan_balance_before - amount_to_deduct
        # savings.save()
        # savings.refresh_from_db()
        # savings.calculate_frequency_paid(save=True)

        # AjoUserService(ajo_user=ajo_user).increment_total_withdrawn_in_ajo_user(amount=amount_to_deduct)

        if repayment_status:
            """Post to loan disk when ready"""

            # POST REPAYMENT RECORD TO LOANDISK
            tasks.celery_handle_loandisk_loan_repayment.delay(
                ajo_loan_id=loan.id,
                amount=amount_to_deduct,
                repayment_type=RepaymentType.SPEND_DEBIT,
            )

            return {"status": True, "message": "repayment made successfully"}

    else:
        return {"status": False, "message": "insufficient funds"}


def process_loan_repayment_from_escrow_wallet(loan: models.AjoLoan):
    """processes repayments for amount due as at yesterday from the savings of an ajo user"""
    amount_to_deduct = loan.repayment_amount - loan.total_repayment
    ajo_user = loan.borrower
    ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
    wallet = ajo_user_selector.get_ajo_escrow_wallet()

    try:
        if wallet.available_balance <= amount_to_deduct and wallet.available_balance > 0:
            amount_to_deduct = wallet.available_balance
            repayment_status, _ = models.AjoLoanRepayment.loan_repayment(
                loan=loan,
                amount=amount_to_deduct,
                from_wallet=wallet,
                repayment_type=RepaymentType.ESCROW_DEBIT,
            )

            if repayment_status:
                """Post to loan disk when ready"""

                # POST REPAYMENT RECORD TO LOANDISK
                tasks.celery_handle_loandisk_loan_repayment.delay(
                    ajo_loan_id=loan.id,
                    amount=amount_to_deduct,
                    repayment_type=RepaymentType.ESCROW_DEBIT,
                )

                loan.escrow_offset = True
                loan.save()

        elif wallet.available_balance > amount_to_deduct:
            repayment_status, _ = models.AjoLoanRepayment.loan_repayment(
                loan=loan,
                amount=amount_to_deduct,
                from_wallet=wallet,
                repayment_type=RepaymentType.ESCROW_DEBIT,
            )

            if repayment_status:
                """Post to loan disk when ready"""

                # POST REPAYMENT RECORD TO LOANDISK
                tasks.celery_handle_loandisk_loan_repayment.delay(
                    ajo_loan_id=loan.id,
                    amount=amount_to_deduct,
                    repayment_type=RepaymentType.ESCROW_DEBIT
                )
                loan.escrow_offset = True
                loan.save()
        else:
            quotation_id = loan.eligibility.saving.quotation_id
            existing_escrow_debit = Transaction.objects.filter(
                transaction_type=TransactionFormType.LOAN_REPAYMENT,
                wallet_type=WalletTypes.AJO_LOAN_ESCROW,
                quotation_id=quotation_id
            )
            existing_ledger = models.AjoLoanRepayment.objects.filter(
                ajo_loan=loan, ledger_amount__gt=0, repayment_type="ESCROW_DEBIT"
            )
            if existing_ledger:
                # clear ledger amount and update repayment amount
                existing_ledger.update(repayment_amount=F('ledger_amount'))
                existing_ledger.update(ledger_amount=0)
                models.EscrowDebitErrorLog.create_log(err="Ledger balance cleared ", loan_obj=loan)
                loan.escrow_offset = True
                loan.save()
                return "Ledger balance cleared"
            elif existing_escrow_debit:
                loan.escrow_offset = True
                loan.save()
                models.EscrowDebitErrorLog.create_log(err="Escrow wallet already empty", loan_obj=loan)
                return "Escrow debit already exists"
            return "insufficient funds in escrow wallet"
    except Exception as err:
        models.EscrowDebitErrorLog.create_log(err=err, loan_obj=loan)
    return "repayment created successfully"


def process_loan_repayment_from_spend_wallet_v2(loan: models.AjoLoan, amount: float) -> Dict[str, Any]:
    """processes repayments for amount due as at yesterday from the savings of an ajo user"""

    ajo_user = loan.borrower
    ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
    wallet = ajo_user_selector.get_spending_wallet()
    balance_amount = wallet.available_balance

    if balance_amount >= amount:
        debit_transaction = TransactionService.dynamic_deduction_from_wallet_transaction(
            user=ajo_user.user,
            amount=balance_amount,
            wallet_type=wallet.wallet_type,
            description="Escrow-Spend-Loans",
            transaction_form_type=TransactionFormType.MANUAL_CLEANUP_WALLET_DEDUCTION,
        )
        deduct_agent_wallet = WalletSystem().deduct_balance(
            wallet=wallet,
            amount=balance_amount,
            transaction_instance=debit_transaction,
            onboarded_user=ajo_user,
        )
        repayment_record = models.AjoLoanRepayment.objects.create(
            agent=loan.agent,
            ajo_loan=loan,
            borrower=loan.borrower,
            loan_amount=loan.amount,
            repayment_amount=balance_amount,
            repayment_type=RepaymentType.SPEND_DEBIT,
        )

        debit_transaction.status = Status.SUCCESS
        debit_transaction.transaction_date_completed = timezone.now()
        debit_transaction.wallet_balance_before = deduct_agent_wallet["balance_before"]
        debit_transaction.wallet_balance_after = deduct_agent_wallet["balance_after"]
        debit_transaction.save()
        repayment_record.handle_repayment_settlement()
        models.AjoLoanRepayment.settle_repayment_status(repayment=repayment_record)

        return {"status": True, "message": repayment_record}

    else:
        return {"status": False, "message": None}
