import os
from datetime import timed<PERSON>ta
from datetime import date
from string import Template

import requests
from celery import shared_task
from dateutil.rrule import DAILY, rrule
from django.conf import settings
from django.db.models import F
from django.utils import timezone

from accounts.models import ConstantTable, CustomUser
from loans.enums import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LoanStatus
from admin_dashboard.services import DateUtility
from collections_app.enums import UserTypes


def send_ajo_loan_officers_daily_check_email(
    email,
    name,
    date,
    message,
    email_subject,
    agent_total_loan_disbursed_amount,
    agent_total_loan_disbursed_count,
    agent_total_loan_disbursed_amount_yesterday,
    agent_total_loan_disbursed_count_yesterday,
    agent_total_loan_repayment_amount,
    agent_total_loan_repayment_count,
    agent_total_loan_repayment_amount_yesterday,
    agent_total_loan_repayment_count_yesterday,
    agent_total_savings_amount,
    agent_total_savings_count,
    agent_total_savings_amount_yesterday,
    agent_total_savings_count_yesterday,
    agent_current_month_earning,
    outstanding_repayment_amount_yesterday,
    overall_outstanding_repayment_amount,
    agent_repayment_vs_expected_percentage_yesterday,
    overall_agent_repayment_vs_expected_percentage,
    overall_expected_repayment_amount,
    agent_total_active_saving_amount,
    file="",
    file_name="",
):
    """For sending template emails"""

    template_dir = os.path.join(
        settings.BASE_DIR,
        "templates/loans/loan_officers_daily_performance_notification.html",
    )

    with open(template_dir) as temp_file:
        template = temp_file.read()
        temp_file.close()

    template = Template(template).safe_substitute(
        message=message,
        email_subject=email_subject,
        email=email,
        name=name,
        date=date,
        agent_total_loan_disbursed_amount=agent_total_loan_disbursed_amount,
        agent_total_loan_disbursed_count=agent_total_loan_disbursed_count,
        agent_total_loan_disbursed_amount_yesterday=agent_total_loan_disbursed_amount_yesterday,
        agent_total_loan_disbursed_count_yesterday=agent_total_loan_disbursed_count_yesterday,
        agent_total_loan_repayment_amount=agent_total_loan_repayment_amount,
        agent_total_loan_repayment_count=agent_total_loan_repayment_count,
        agent_total_loan_repayment_amount_yesterday=agent_total_loan_repayment_amount_yesterday,
        agent_total_loan_repayment_count_yesterday=agent_total_loan_repayment_count_yesterday,
        agent_total_savings_amount=agent_total_savings_amount,
        agent_total_savings_count=agent_total_savings_count,
        agent_total_savings_amount_yesterday=agent_total_savings_amount_yesterday,
        agent_total_savings_count_yesterday=agent_total_savings_count_yesterday,
        agent_current_month_earning=agent_current_month_earning,
        outstanding_repayment_amount_yesterday=outstanding_repayment_amount_yesterday,
        overall_outstanding_repayment_amount=overall_outstanding_repayment_amount,
        agent_repayment_vs_expected_percentage_yesterday=agent_repayment_vs_expected_percentage_yesterday,
        overall_agent_repayment_vs_expected_percentage=overall_agent_repayment_vs_expected_percentage,
        overall_expected_repayment_amount=overall_expected_repayment_amount,
        agent_total_active_saving_amount=agent_total_active_saving_amount,
    )

    data = {
        "from": "Liberty Pay <<EMAIL>>",
        "to": email,
        "subject": email_subject,
        "html": template,
    }
    message = requests.post(
        f"{settings.MAILGUN_URL}",
        auth=("api", f"{settings.MAILGUN_API_KEY}"),
        data=data,
        files=[(f"attachment", (f"{file_name}", file))],
    )

    return message.text


def send_ajo_loan_officers_weekly_check_email(
    email,
    name,
    date,
    message,
    email_subject,
    agent_total_loan_disbursed_amount,
    agent_total_loan_disbursed_count,
    agent_total_loan_disbursed_amount_week,
    agent_total_loan_disbursed_count_week,
    agent_total_loan_repayment_amount,
    agent_total_loan_repayment_count,
    agent_total_loan_repayment_amount_week,
    agent_total_loan_repayment_count_week,
    agent_total_savings_amount,
    agent_total_savings_count,
    agent_total_savings_amount_week,
    agent_total_savings_count_week,
    agent_earning_week,
    overall_expected_repayment_amount,
    outstanding_repayment_amount_week,
    overall_outstanding_repayment_amount,
    agent_repayment_vs_expected_percentage_week,
    overall_agent_repayment_vs_expected_percentage,
    file="",
    file_name="",
):
    """For sending template emails"""

    template_dir = os.path.join(
        settings.BASE_DIR,
        "templates/loans/loan_officers_weekly_performance_notification.html",
    )

    with open(template_dir) as temp_file:
        template = temp_file.read()
        temp_file.close()

    template = Template(template).safe_substitute(
        message=message,
        email_subject=email_subject,
        email=email,
        name=name,
        date=date,
        agent_total_loan_disbursed_amount=agent_total_loan_disbursed_amount,
        agent_total_loan_disbursed_count=agent_total_loan_disbursed_count,
        agent_total_loan_disbursed_amount_week=agent_total_loan_disbursed_amount_week,
        agent_total_loan_disbursed_count_week=agent_total_loan_disbursed_count_week,
        agent_total_loan_repayment_amount=agent_total_loan_repayment_amount,
        agent_total_loan_repayment_count=agent_total_loan_repayment_count,
        agent_total_loan_repayment_amount_week=agent_total_loan_repayment_amount_week,
        agent_total_loan_repayment_count_week=agent_total_loan_repayment_count_week,
        agent_total_savings_amount=agent_total_savings_amount,
        agent_total_savings_count=agent_total_savings_count,
        agent_total_savings_amount_week=agent_total_savings_amount_week,
        agent_total_savings_count_week=agent_total_savings_count_week,
        agent_earning_week=agent_earning_week,
        overall_agent_repayment_vs_expected_percentage=overall_agent_repayment_vs_expected_percentage,
        agent_repayment_vs_expected_percentage_week=agent_repayment_vs_expected_percentage_week,
        outstanding_repayment_amount_week=outstanding_repayment_amount_week,
        overall_outstanding_repayment_amount=overall_outstanding_repayment_amount,
    )

    data = {
        "from": "Liberty Pay <<EMAIL>>",
        "to": email,
        "subject": email_subject,
        "html": template,
    }
    message = requests.post(
        f"{settings.MAILGUN_URL}",
        auth=("api", f"{settings.MAILGUN_API_KEY}"),
        data=data,
        files=[(f"attachment", (f"{file_name}", file))],
    )

    return message.text


def send_ajo_loan_officers_monthly_check_email(
    email,
    name,
    date,
    message,
    email_subject,
    agent_total_loan_disbursed_amount,
    agent_total_loan_disbursed_count,
    agent_total_loan_disbursed_amount_month,
    agent_total_loan_disbursed_count_month,
    agent_total_loan_repayment_amount,
    agent_total_loan_repayment_count,
    agent_total_loan_repayment_amount_month,
    agent_total_loan_repayment_count_month,
    agent_total_savings_amount,
    agent_total_savings_count,
    agent_total_savings_amount_month,
    agent_total_savings_count_month,
    agent_earning_month,
    overall_expected_repayment_amount,
    outstanding_repayment_amount_month,
    overall_outstanding_repayment_amount,
    agent_repayment_vs_expected_percentage_month,
    overall_agent_repayment_vs_expected_percentage,
    file="",
    file_name="",
):
    """For sending template emails"""

    template_dir = os.path.join(
        settings.BASE_DIR,
        "templates/loans/loan_officers_monthly_performance_notification.html",
    )

    with open(template_dir) as temp_file:
        template = temp_file.read()
        temp_file.close()

    template = Template(template).safe_substitute(
        message=message,
        email_subject=email_subject,
        email=email,
        name=name,
        date=date,
        agent_total_loan_disbursed_amount=agent_total_loan_disbursed_amount,
        agent_total_loan_disbursed_count=agent_total_loan_disbursed_count,
        agent_total_loan_disbursed_amount_month=agent_total_loan_disbursed_amount_month,
        agent_total_loan_disbursed_count_month=agent_total_loan_disbursed_count_month,
        agent_total_loan_repayment_amount=agent_total_loan_repayment_amount,
        agent_total_loan_repayment_count=agent_total_loan_repayment_count,
        agent_total_loan_repayment_amount_month=agent_total_loan_repayment_amount_month,
        agent_total_loan_repayment_count_month=agent_total_loan_repayment_count_month,
        agent_total_savings_amount=agent_total_savings_amount,
        agent_total_savings_count=agent_total_savings_count,
        agent_total_savings_amount_month=agent_total_savings_amount_month,
        agent_total_savings_count_month=agent_total_savings_count_month,
        agent_earning_month=agent_earning_month,
        overall_agent_repayment_vs_expected_percentage=overall_agent_repayment_vs_expected_percentage,
        agent_repayment_vs_expected_percentage_month=agent_repayment_vs_expected_percentage_month,
        overall_outstanding_repayment_amount=overall_outstanding_repayment_amount,
        overall_expected_repayment_amount=overall_expected_repayment_amount,
        outstanding_repayment_amount_month=outstanding_repayment_amount_month,
    )

    data = {
        "from": "Liberty Pay <<EMAIL>>",
        "to": email,
        "subject": email_subject,
        "html": template,
    }
    message = requests.post(
        f"{settings.MAILGUN_URL}",
        auth=("api", f"{settings.MAILGUN_API_KEY}"),
        data=data,
        files=[(f"attachment", (f"{file_name}", file))],
    )

    return message.text


def get_percentages(numerator: int, denominator: int) -> int:
    """
    Description: A function to determine what percentage of another value one value is.

    Args:
        - numerator: An value that is to be compared against the denominator
        - denominator: An value that is to be compared against

    Example:
        perc = get_percentage(10, 200)
        perc = 10/200 * 100 = 5
    """
    try:
        perc = (numerator / denominator) * 100
    except ZeroDivisionError:
        perc = 0
    return perc


def send_ajo_loan_officers_daily_report_email(
    email,
    name,
    date,
    message,
    email_subject,
    # agent_total_loan_disbursed_amount,
    # agent_total_loan_disbursed_count,
    # agent_total_loan_disbursed_amount_yesterday,
    # agent_total_loan_disbursed_count_yesterday,
    # agent_total_loan_repayment_amount,
    # agent_total_loan_repayment_count,
    # agent_total_loan_repayment_amount_yesterday,
    # agent_total_loan_repayment_count_yesterday,
    # agent_total_savings_amount,
    # agent_total_savings_count,
    # agent_total_savings_amount_yesterday,
    # agent_total_savings_count_yesterday,
    # agent_current_month_earning,
    # outstanding_repayment_amount_yesterday,
    # overall_outstanding_repayment_amount,
    # agent_repayment_vs_expected_percentage_yesterday,
    # overall_agent_repayment_vs_expected_percentage,
    # overall_expected_repayment_amount,
    file="",
    file_name="",
):
    """For sending template emails"""

    template_dir = os.path.join(
        settings.BASE_DIR, "templates/loans/loan_officers_daily_report.html"
    )

    with open(template_dir) as temp_file:
        template = temp_file.read()
        temp_file.close()

    template = Template(template).safe_substitute(
        message=message,
        email_subject=email_subject,
        email=email,
        name=name,
        date=date,
        # agent_total_loan_disbursed_amount=agent_total_loan_disbursed_amount,
        # agent_total_loan_disbursed_count=agent_total_loan_disbursed_count,
        # agent_total_loan_disbursed_amount_yesterday=agent_total_loan_disbursed_amount_yesterday,
        # agent_total_loan_disbursed_count_yesterday=agent_total_loan_disbursed_count_yesterday,
        # agent_total_loan_repayment_amount=agent_total_loan_repayment_amount,
        # agent_total_loan_repayment_count=agent_total_loan_repayment_count,
        # agent_total_loan_repayment_amount_yesterday=agent_total_loan_repayment_amount_yesterday,
        # agent_total_loan_repayment_count_yesterday=agent_total_loan_repayment_count_yesterday,
        # agent_total_savings_amount=agent_total_savings_amount,
        # agent_total_savings_count=agent_total_savings_count,
        # agent_total_savings_amount_yesterday=agent_total_savings_amount_yesterday,
        # agent_total_savings_count_yesterday=agent_total_savings_count_yesterday,
        # agent_current_month_earning=agent_current_month_earning,
        # outstanding_repayment_amount_yesterday=outstanding_repayment_amount_yesterday,
        # overall_outstanding_repayment_amount=overall_outstanding_repayment_amount,
        # agent_repayment_vs_expected_percentage_yesterday=agent_repayment_vs_expected_percentage_yesterday,
        # overall_agent_repayment_vs_expected_percentage=overall_agent_repayment_vs_expected_percentage,
        # overall_expected_repayment_amount=overall_expected_repayment_amount
    )

    data = {
        "from": "Liberty Pay <<EMAIL>>",
        "to": email,
        "subject": email_subject,
        "html": template,
    }
    message = requests.post(
        f"{settings.MAILGUN_URL}",
        auth=("api", f"{settings.MAILGUN_API_KEY}"),
        data=data,
        files=[(f"attachment", (f"{file_name}", file))],
    )

    return message.text


def send_mail_notification_to_supervisor_to_approve_borrower_loan(
    message,
    email_subject,
    email,
    loan_officer_name,
    date,
    borrower_name,
    amount,
    borrower_phone_number,
):
    """For sending template emails"""

    template_dir = os.path.join(
        settings.BASE_DIR, "templates/loans/supervisor_approves_loan_notification.html"
    )

    with open(template_dir) as temp_file:
        template = temp_file.read()

    template = Template(template).safe_substitute(
        message=message,
        email_subject=email_subject,
        email=email,
        loan_officer_name=loan_officer_name,
        date=date,
        borrower_name=borrower_name,
        amount=amount,
        borrower_phone_number=borrower_phone_number,
    )

    data = {
        "from": "Liberty Pay <<EMAIL>>",
        "to": email,
        "subject": email_subject,
        "html": template,
    }

    message = requests.post(
        f"{settings.MAILGUN_URL}",
        auth=("api", f"{settings.MAILGUN_API_KEY}"),
        data=data,
    )

    return message.text


def get_days_without_holiday(start_date, end_date):
    """Returns the due amount as at today"""
    from loans.models import Holiday

    total_days = (end_date - start_date).days + 1

    # if self.tenor_in_days > 0:
    # Check for holidays and weekends
    holidays = Holiday.objects.filter(date__range=[start_date, end_date]).count()

    weekends = sum(
        1
        for dt in rrule(DAILY, dtstart=start_date, until=end_date)
        if dt.weekday() in [5, 6]
    )
    total_days -= holidays + weekends

    return total_days


def get_dates(start_date, end_date, skip_holiday=True, branch_name=None, agent=None):
    """Returns an array of dates that are neither weekends nor holidays"""
    from loans.models import Holiday

    dates_arr = []

    current_date = start_date + timedelta(days=1)
    while current_date <= end_date:
        if skip_holiday:
            is_weekend = current_date.weekday() in [5, 6]
            is_company_holiday = Holiday.objects.filter(
                date=current_date, type="company"
            ).exists()
            is_branch_holiday = (
                branch_name
                and Holiday.objects.filter(
                    date=current_date, type="branch", branch__name=branch_name
                ).exists()
            )
            is_agent_holiday = (
                agent
                and Holiday.objects.filter(
                    date=current_date, type="agent", agent=agent
                ).exists()
            )
            if (
                is_weekend
                or is_company_holiday
                or is_branch_holiday
                or is_agent_holiday
            ):
                pass
            else:
                dates_arr.append(current_date)

        else:
            dates_arr.append(current_date)

        current_date += timedelta(days=1)

    return dates_arr


@shared_task
def send_dynamic_email(email_subject, email, template_dir, extra_data):
    """For sending template emails"""
    with open(template_dir) as temp_file:
        template = temp_file.read()

    template = Template(template).safe_substitute(**extra_data)

    data = {
        "from": "Liberty Pay <<EMAIL>>",
        "to": email,
        "subject": email_subject,
        "html": template,
    }

    message = requests.post(
        f"{settings.MAILGUN_URL}",
        auth=("api", f"{settings.MAILGUN_API_KEY}"),
        data=data,
    )

    return message.text


from django.db.models import Sum


def performance_checker(agent: CustomUser):
    """
    Perform performance checking for the agent based on loan data.

    This function calculates the performance of an agent by analyzing the total amount paid for all open loans,
    the total due amount till yesterday for all open loans, and the outstanding days of the loans.
    It then determines if the agent can give a new loan based on predefined thresholds.

    Args:
        agent: The agent for whom the performance is being checked.

    Returns:
        bool: True if the agent can give a new loan, False otherwise.
    """
    from loans.tasks import disbursement_compliance_checks
    from ..models import LoanAnalysisLog, PastMaturityBuffer

    const = ConstantTable.get_constant_table_instance()
    today = timezone.now().date()

    # Get the total amount paid for all open loans
    loans = agent.ajoloans_set.filter(status=LoanStatus.OPEN)
    loans = loans.select_related(
        "agent",
        "borrower",
        "eligibility",
        "guarantor",
        "repayment_savings",
        "checked_by",
    )

    agent_buffer_instance = PastMaturityBuffer.objects.filter(agent=agent).last()
    agent_buffer_amount = (
        agent_buffer_instance and agent_buffer_instance.agent_buffer_amount or 0.0
    )

    repayments = agent.ajoloanrepayment_set.filter(ajo_loan__status="OPEN")
    total_paid = (
        repayments.aggregate(total_paid=Sum("repayment_amount"))["total_paid"] or 0
    ) + agent_buffer_amount

    # Get the total due amount till yesterday for all open loans
    total_due_yesterday = sum(
        loan.due_yesterday for loan in loans if loan.due_yesterday > 0
    )
    owing_loans = []
    average_days = None
    total_past_maturity_amount = None
    average_past_maturity_days = None
    reason = None
    balances = []
    repayments = []
    dues = []
    loans_past_maturity = []

    for loan in loans:
        repayment_total = (
            loan.ajoloanrepayment_set.aggregate(
                total_repayments=Sum("repayment_amount")
            )["total_repayments"]
            or 0
        )
        if loan.due_yesterday > repayment_total:
            balance = loan.due_yesterday - repayment_total

            if loan.performance_status == LoanBehavior.PAST_MATURITY:

                if agent_buffer_amount >= balance:
                    agent_buffer_amount -= balance
                else:
                    loans_past_maturity.append(loan)
                    owing_loans.append(loan)
                    balances.append(balance)
                    repayments.append(repayment_total)
                    dues.append(loan.due_yesterday)
            else:
                owing_loans.append(loan)
                balances.append(balance)
                repayments.append(repayment_total)
                dues.append(loan.due_yesterday)

    if total_due_yesterday == 0:
        percentage = 100
    elif total_paid == 0:
        percentage = 0
    else:
        percentage = abs((total_paid / total_due_yesterday) * 100)

    if sum(dues) == 0:
        repay_percent = 100
    else:
        repay_percent = sum(repayments) / sum(dues) * 100

    if percentage < const.get_agent_teir_percent(len(loans)):
        can_give_loan = False
        reason = "Failed overall collection percentage check"
    else:
        if repay_percent < const.percent_checker:
            can_give_loan = False
            reason = "Failed individual loan repayment percentage check"
        else:

            if len(loans_past_maturity) > 0:
                # Calculate the average amount of these loans
                total_past_maturity_amount = sum(
                    loan.due_yesterday for loan in loans_past_maturity
                )

                # Calculate the average outstanding days of these loans
                average_past_maturity_days = sum(
                    loan.outstanding_days for loan in loans_past_maturity
                ) / len(loans_past_maturity)

                if (
                    total_past_maturity_amount > const.past_maturity_amount_threshold
                    or average_past_maturity_days > const.past_maturity_days_threshold
                ):
                    can_give_loan = False
                    reason = "Failed past maturity loan check"
                else:
                    can_give_loan = True
            else:
                can_give_loan = True

    # Combine sets to ensure all entries are unique
    sorted_combined_loans = sorted(
        owing_loans, key=lambda loan: loan.created_at, reverse=True
    )
    disbursed_amount = (
        agent.ajoloans_set.filter(
            is_disbursed=True,
            date_disbursed__date__month=(timezone.now().month - 1),
        ).aggregate(Sum("amount"))["amount__sum"]
        or 0.0
    )
    commission_amount = disbursed_amount * (0.5 / 100)

    data = {
        "message": "success",
        "percent": percentage,
        "amount_due": total_due_yesterday,
        "total_paid": total_paid,
        "can_give_loan": can_give_loan,
        "show_warning": True if percentage < const.preliminary_checker else False,
        "possible_winning": commission_amount,
        "missing_count": len(sorted_combined_loans),
        "total_missing_loans": sum(balances),
        "total_past_maturity_amount": total_past_maturity_amount,
        "average_past_maturity_days": average_past_maturity_days,
        "block_reason": reason,
        "due_to_repayment_ratio": repay_percent,
        "sum_individual_due": sum(dues),
        "sum_individual_repayment": sum(repayments),
        "total_past_maturity_amount": total_past_maturity_amount,
    }

    log_entry = LoanAnalysisLog.objects.filter(
        agent=agent, created_at__date=today
    ).first()

    if data["amount_due"] == 0 and data["total_paid"] == 0:
        pass
    else:
        if log_entry:
            log_entry.message = data["message"]
            log_entry.prev_percent = log_entry.percent
            log_entry.percent = data["percent"]
            log_entry.amount_due = data["amount_due"]
            log_entry.total_paid = data["total_paid"]
            log_entry.missed_amount = sum(balances)
            log_entry.can_give_loan = data["can_give_loan"]
            log_entry.average_outstanding_days = average_days
            log_entry.total_past_maturity_amount = total_past_maturity_amount
            log_entry.average_past_maturity_days = average_past_maturity_days
            log_entry.defaulting_loans.clear()
            log_entry.defaulting_loans.add(*sorted_combined_loans)
            log_entry.block_reason = reason
            log_entry.prev_due_to_repayment_ratio = log_entry.due_to_repayment_ratio
            log_entry.due_to_repayment_ratio = repay_percent
            if can_give_loan:
                log_entry.positive_count = F("positive_count") + 1
            else:
                log_entry.negative_count = F("negative_count") + 1
            log_entry.save()
        else:
            # Create a new log entry
            log_entry = LoanAnalysisLog.objects.create(
                message=data["message"],
                percent=data["percent"],
                amount_due=data["amount_due"],
                total_paid=data["total_paid"],
                missed_amount=sum(balances),
                can_give_loan=data["can_give_loan"],
                agent=agent,
                average_outstanding_days=average_days,
                block_reason=reason,
                due_to_repayment_ratio=repay_percent,
            )
            log_entry.defaulting_loans.add(*sorted_combined_loans)
            if can_give_loan:
                log_entry.positive_count = 1
            else:
                log_entry.negative_count = 1
            log_entry.save()

    try:
        disbursement_compliance_checks.delay(agent_id=agent.id)
    except Exception:
        pass
    return can_give_loan


class SalaryDecisioning:
    FIRST_BAND_PERC = 0.05
    SECOND_BAND_PERC = 0.09
    THIRD_BAND_PERC = 0.08

    @classmethod
    def get_base_salary(cls, disbursement_amount_month):
        # Determine base salary
        if disbursement_amount_month >= 1000000:
            base_salary = 80000
        elif disbursement_amount_month >= 900000:
            base_salary = 70000
        elif disbursement_amount_month >= 800000:
            base_salary = 60000
        elif disbursement_amount_month >= 700000:
            base_salary = 50000
        elif disbursement_amount_month >= 600000:
            base_salary = 40000
        elif disbursement_amount_month >= 500000:
            base_salary = 30000
        elif disbursement_amount_month >= 400000:
            base_salary = 20000
        elif disbursement_amount_month >= 300000:
            base_salary = 10000
        else:
            base_salary = 0
        return base_salary

    @classmethod
    def get_salary(
        cls,
        disbursement_amount_month,
        total_interest,
        user_type=UserTypes.STAFF_AGENT,
        user=None,
    ):
        month_start = DateUtility().month_start
        days_since_month_start = (timezone.now().date() - month_start.date()).days
        # print("days since month start::::", days_since_month_start)

        base_salary = cls.get_base_salary(
            disbursement_amount_month=disbursement_amount_month
        )

        if user_type == UserTypes.STAFF_AGENT:
            if disbursement_amount_month >= 3100000:
                salary_earned = base_salary + (cls.THIRD_BAND_PERC * total_interest)
                # 0.8 of interest
            elif (
                disbursement_amount_month >= 2100000
                and disbursement_amount_month < 3100000
            ):
                salary_earned = base_salary + (cls.SECOND_BAND_PERC * total_interest)
                # 0.9 of interest
            elif disbursement_amount_month >= 1_200_000:
                # if 2 month - loan = base + 0.5of interest from disbursements
                salary_earned = base_salary + (cls.FIRST_BAND_PERC * total_interest)
            else:
                salary_earned = base_salary
        elif user_type == UserTypes.SUPERVISOR:
            salary_earned = 0.005 * disbursement_amount_month
        else:
            salary_earned = 0.00

        full_salary = salary_earned
        daily_salary = salary_earned / 30
        salary_earned = daily_salary * days_since_month_start

        return salary_earned, full_salary

    @classmethod
    def get_commission(
        cls, disbursement_amount_month, total_interest, user_type=UserTypes.STAFF_AGENT
    ):
        if user_type == UserTypes.STAFF_AGENT:
            if disbursement_amount_month >= 3_100_000:
                commission_earned = cls.THIRD_BAND_PERC * total_interest
            elif (
                disbursement_amount_month >= 2_100_000
                and disbursement_amount_month <= 3_000_000
            ):
                commission_earned = cls.SECOND_BAND_PERC * total_interest
            elif disbursement_amount_month >= 1_200_000:
                commission_earned = cls.FIRST_BAND_PERC * total_interest
            else:
                commission_earned = 0.00
        elif user_type == UserTypes.SUPERVISOR:
            commission_earned = 0.005 * disbursement_amount_month
        else:
            commission_earned = 0.00
        return commission_earned

    @classmethod
    def get_prorated_salary(
        cls, is_payroll=False, user: CustomUser = None, user_type=UserTypes.STAFF_AGENT
    ):
        """
        This function calculates the salary of a user based on the amount disbursed
        in the month and the total interest
        earned in the month, alongside attendance data.
        """
        from collections_app.models import AgentDailyAttendance
        from collections_app.helpers.helpers import SupervisorClass
        from loans.models import AjoLoan, UserEarnings, Holiday, AjoLoanRepayment
        from loans.enums import DisbursementStatus
        import calendar
        from datetime import datetime

        constants_inst = ConstantTable.objects.last()
        last_payroll_date = constants_inst.last_payroll_date
        month_start = last_payroll_date
        days_since_joined = (timezone.now().date() - user.created_at.date()).days
        today = timezone.now()

        # Decide month end
        tent_next_month_end = month_start + timedelta(days=30)

        if month_start.month == today.month:
            month_end = today.replace(
                day=calendar.monthrange(today.year, today.month)[1]
            )
        elif today < tent_next_month_end:
            month_end = today.replace(
                day=calendar.monthrange(today.year, today.month)[1]
            )
        else:
            month_end = today

        is_new_user = days_since_joined < 25

        attendance = AgentDailyAttendance.objects.filter(
            agent=user, created_at__gte=month_start
        )

        if user_type == UserTypes.SUPERVISOR:
            supervisor_users = SupervisorClass().get_supervisor_users(user)
            disbursed_loan_qs = AjoLoan.objects.filter(
                date_disbursed__gte=month_start,
                agent__customer_user_id__in=supervisor_users,
                is_disbursed=True,
                supervisor_disbursement_status=DisbursementStatus.SUCCESSFUL,
            )

            repayment_qs = AjoLoanRepayment.objects.filter(
                agent__customer_user_id__in=supervisor_users,
                paid_date__date__gte=month_start.date(),
            )
        else:
            disbursed_loan_qs = AjoLoan.objects.filter(
                date_disbursed__gte=month_start,
                agent=user,
                is_disbursed=True,
                supervisor_disbursement_status=DisbursementStatus.SUCCESSFUL,
            )

            repayment_qs = AjoLoanRepayment.objects.filter(
                agent=user,
                paid_date__date__gte=month_start.date(),
            )

        this_month_disbursed_amount = (
            disbursed_loan_qs.aggregate(amount=Sum("amount"))["amount"] or 0
        )

        this_month_total_interest_amount = (
            disbursed_loan_qs.aggregate(amount=Sum("interest_amount"))["amount"] or 0
        )

        this_month_total_collections = (
            repayment_qs.aggregate(rep_amount=Sum("repayment_amount"))["rep_amount"]
            or 0
        )

        if (
            is_new_user
            and user_type == UserTypes.STAFF_AGENT
            and this_month_disbursed_amount < 1_200_000
        ):
            salary_earned, full_salary = cls.get_salary(
                disbursement_amount_month=1_200_000,
                total_interest=this_month_total_interest_amount,
            )
        else:
            salary_earned, full_salary = cls.get_salary(
                disbursement_amount_month=this_month_disbursed_amount,
                total_interest=this_month_total_interest_amount,
            )

        number_of_days_absent = attendance.filter(activity="ABSENT").count()

        holidays = Holiday.objects.filter(
            date__gte=month_start.date(), date__lte=month_end.date(), salary_waver=False
        )
        holidays_till_today = holidays.filter(date__lte=today.date())

        # Company holidays
        company_holidays_in_month_qs = holidays.filter(type__icontains="Company")
        company_holidays_till_date_qs = holidays_till_today.filter(
            type__icontains="Company"
        )

        # Agent Holidays
        agent_holidays_in_month_qs = holidays.filter(
            type__icontains="Agent", agent=user
        )
        agent_holidays_till_date_qs = holidays_till_today.filter(
            type__icontains="Agent", agent=user
        )

        total_holidays_in_month = len(
            set(
                (company_holidays_in_month_qs | agent_holidays_in_month_qs).values_list(
                    "date", flat=True
                )
            )
        )

        total_holidays_in_month_till_date = len(
            set(
                (
                    company_holidays_till_date_qs | agent_holidays_till_date_qs
                ).values_list("date", flat=True)
            )
        )

        total_holidays_in_month = 0
        total_holidays_in_month_till_date = 0

        work_days = (
            sum(
                1
                for dt in rrule(
                    DAILY, dtstart=month_start.date(), until=month_end.date()
                )
                if dt.weekday() not in [5, 6]
            )
            - 1
        )

        work_days_till_today = (
            sum(
                1
                for dt in rrule(DAILY, dtstart=month_start.date(), until=today.date())
                if dt.weekday() not in [5, 6]
            )
            - 1
        )

        number_of_workdays_in_month = work_days - total_holidays_in_month
        number_of_days_present = number_of_workdays_in_month
        number_of_days_till_today = (
            work_days_till_today - total_holidays_in_month_till_date
        )
        number_of_days_present_till_today = number_of_days_till_today

        try:
            daily_salary = full_salary / number_of_workdays_in_month
        except ZeroDivisionError:
            daily_salary = 0

        pro_salary_earned = daily_salary * number_of_days_present
        salary_earned_till_today = daily_salary * number_of_days_present_till_today

        commission_earned = cls.get_commission(
            disbursement_amount_month=this_month_disbursed_amount,
            total_interest=this_month_total_interest_amount,
        )

        if is_payroll:
            UserEarnings.objects.create(
                user=user,
                email=user.email,
                full_name=user.get_full_name(),
                branch=user.user_branch,
                salary_earned=salary_earned_till_today,
                full_salary=full_salary,
                commission_earned=commission_earned,
                month_start=month_start,
                days_since_joined=days_since_joined,
                number_of_days_present=number_of_days_present,
                number_of_work_days=number_of_workdays_in_month,
                days_absent=number_of_days_absent,
                user_type=user_type,
                this_month_disbursed_amount=this_month_disbursed_amount,
                this_month_total_interest_amount=this_month_total_interest_amount,
                this_month_total_collections=this_month_total_collections,
            )

        data = {
            "full_month_salary_potential": pro_salary_earned,
            "full_salary": full_salary,
            "commission_earned": commission_earned,
            "today_earning": daily_salary,
            "prorated_earnings_for_the_month": salary_earned_till_today,
            "this_month_total_collections": this_month_total_collections,
            "number_of_days_present": number_of_days_present,
            "number_of_days_present_till_today": number_of_days_present_till_today,
            "days_absent": number_of_days_absent,
            "work_days_till_date": number_of_days_till_today,
            "days_present": number_of_days_present_till_today,
            "work_days_in_month": number_of_workdays_in_month,
            "last_payroll_date": month_start,
            "holidays": total_holidays_in_month,
            "holidays_till_today": total_holidays_in_month_till_date,
            "work_days_less_weekends": work_days,
            "this_month_total_interest_amount": this_month_total_interest_amount,
            "this_month_disbursed_amount": this_month_disbursed_amount,
            "user_type": user_type,
            "general_work_days_till_today": work_days_till_today,
        }
        return data


def get_due_collections_amount(open_loans_qs, agent=None):
    from loans.models import Holiday

    holidays = Holiday.objects.filter(type="company").values_list("date", flat=True)

    if agent and agent.user_branch:
        branch_holidays = Holiday.objects.filter(
            type="branch", branch__name__icontains=agent.user_branch
        ).values_list("date", flat=True)
        agent_holidays = Holiday.objects.filter(type="agent", agent=agent).values_list(
            "date", flat=True
        )
        holidays = list(holidays) + list(branch_holidays) + list(agent_holidays)
    elif agent:
        agent_holidays = Holiday.objects.filter(type="agent", agent=agent).values_list(
            "date", flat=True
        )
        holidays = list(holidays) + list(agent_holidays)
    else:
        holidays = list(holidays)

    today = timezone.now().date()

    if today.weekday() in [5, 6] or today in holidays:
        total_due_collection_amount_today = 0.00
    else:
        total_due_collection_amount_today = sum(
            [
                (
                    loan.daily_repayment_amount
                    if loan.start_date and loan.start_date <= timezone.now().date()
                    else 0
                )
                for loan in open_loans_qs
            ]
        )

    return total_due_collection_amount_today


def check_is_holiday(date: date, agent: CustomUser = None):
    """ ""
    A function to check that a provided date
    is a holiday for a given loan officer.
    """
    from loans.models import Holiday

    holidays = Holiday.objects.filter(type="company").values_list("date", flat=True)

    if agent and agent.user_branch:
        branch_holidays = Holiday.objects.filter(
            type="branch", branch__name__icontains=agent.user_branch
        ).values_list("date", flat=True)
        agent_holidays = Holiday.objects.filter(type="agent", agent=agent).values_list(
            "date", flat=True
        )
        holidays = list(holidays) + list(branch_holidays) + list(agent_holidays)
    elif agent:
        agent_holidays = Holiday.objects.filter(type="agent", agent=agent).values_list(
            "date", flat=True
        )
        holidays = list(holidays) + list(agent_holidays)
    else:
        holidays = list(holidays)

    today = timezone.now().date()

    if today.weekday() in [5, 6] or today in holidays:
        return True

    return False


def number_of_holidays(start_date: date, end_date: date, agent: CustomUser = None):
    """ ""
    A function to check that a provided date
    is a holiday for a given loan officer.
    """
    from loans.models import Holiday

    holidays = Holiday.objects.filter(type="company").values_list("date", flat=True)

    if agent and agent.user_branch:
        branch_holidays = Holiday.objects.filter(
            type="branch", branch__name__icontains=agent.user_branch
        ).values_list("date", flat=True)
        agent_holidays = Holiday.objects.filter(type="agent", agent=agent).values_list(
            "date", flat=True
        )
        holidays = list(holidays) + list(branch_holidays) + list(agent_holidays)
    elif agent:
        agent_holidays = Holiday.objects.filter(type="agent", agent=agent).values_list(
            "date", flat=True
        )
        holidays = list(holidays) + list(agent_holidays)
    else:
        holidays = list(holidays)

    today = timezone.now().date()

    return len(holidays)
