import ast
import datetime
import json
import random

import requests
from django.conf import settings

from loans.models import <PERSON><PERSON><PERSON><PERSON>, LoanDiskLoansMirror
from ajo.models import AjoUser
from loans.models import LoanDiskMetaData

# from datetime import datetime


def loan_disk_date_format(pass_date=None):
    """
    This function handles loan disk date format
    """
    # print(pass_date, "\n\n\n")
    if not pass_date:
        current_date = f"{datetime.datetime.now()}"
        input_date = datetime.datetime.strptime(current_date, "%Y-%m-%d %H:%M:%S.%f")

        date_format = input_date.strftime("%d/%m/%Y")
        return date_format

    if pass_date:
        formatted_date = pass_date.strftime("%d/%m/%Y")
        return formatted_date


def loan_disk_update_borrower(**args):
    payload_data = args
    payload = json.dumps(payload_data)

    base_url = "https://api-main.loandisk.com"
    url = f"{base_url}/{settings.LOANDISK_PUBLIC_KEY}/{settings.LOAN_DISK_BRANCH_ID}/borrower"

    headers = {
        "Authorization": f"Basic {settings.LOAN_DISK_SEC_KEY}",
        "Content-Type": "application/json",
    }

    response = requests.request("PUT", url, headers=headers, data=payload)
    return response.text


class LoandiskManager:
    def __init__(self) -> None:
        self.headers = {
            "Authorization": f"Basic {settings.LOAN_DISK_SEC_KEY}",
            "Content-Type": "application/json",
        }

        self.base_url = "https://api-main.loandisk.com"

        self.url = f"{self.base_url}/{settings.LOANDISK_PUBLIC_KEY}/{settings.LOAN_DISK_BRANCH_ID}"

        self.loan_url = f"{self.url}/loan"

    def get_borrower_using_phone(self, phone):
        url = f"{self.url}/borrower/borrower_mobile/{phone}"
        payload = {}
        response = requests.request("GET", url, headers=self.headers, data=payload)

        try:
            return response.json()
        except:
            return response.text

    def get_boorower_details(self, phone_number):

        """
        Method only aim at fixing current issues around posting borrowers.
        """

        response = self.get_borrower_using_phone(phone=phone_number)

        try:
            if response["response"]["Results"]:
                borrower_info = response["response"]["Results"][0][0]
                borrower_id = borrower_info.get("borrower_id")
                borrower_first_name = borrower_info.get("borrower_firstname")
                borrower_last_name = borrower_info.get("borrower_lastname")
                borrower_phone_number = borrower_info.get("borrower_mobile")

                data = {
                    "status": True,
                    "response": {
                        "borrower_id": borrower_id,
                        "borrower_first_name": borrower_first_name,
                        "borrower_last_name": borrower_last_name,
                        "borrower_phone": borrower_phone_number
                    }
                }

                return data
            
        except KeyError:

            if response["error"]["message"] == "Not Found":

                data = {
                    "status": False,
                    "message": "Borrower not on LoanDisk"
                }

            else:
                data = {
                    "status": False,
                    "message": response
                }

            return data


    def get_or_create_borrower(self, phone_number):
        """
        This function create borrower on loandisk
        Args: self: Initialized loandisk helper function
        employment instance: The instance of the employment table
        Returns: Dictionary of loandisk response
        """  # noqa
        # get User profile
        ajo_user_profile = AjoUser.objects.filter(phone_number__startswith=phone_number).last()

        if ajo_user_profile == None:
            result = {
                "response": {"errors": "ajo user profile not found when request was initiated", "url": ""},
                "http": {"code": 500, "message": "OK"},
            }
            return result

        """
        BEFORE POSTING A BORROWER ON
        LET'S CHECK IF THE BORROWER ALREADY EXIST ON LOANDISK

        """
        borrower_details_on_loandisk = self.get_borrower_using_phone(ajo_user_profile.phone_number)
        if isinstance(borrower_details_on_loandisk, dict):
            try:
                if borrower_details_on_loandisk["error"]["message"] == "Not Found":
                    # borrower does not exist on loandisk
                    pass
            except KeyError:
                if borrower_details_on_loandisk["response"]["Results"]:
                    borrower_id = borrower_details_on_loandisk["response"]["Results"][0][0]["borrower_id"]

                    result = {"response": {"borrower_id": borrower_id}, "http": {"code": 200, "message": "OK"}}

                    return result

        """
        IF IT GETS TO THIS POINT, IT MEANS BORROWER DIDN'T EXIST ON LOANDISK BEFORE NOW.
        IT'LL GO AHEAD AND CREATE THE BORROWER ON LOANDISK
        """

        date_of_birth = loan_disk_date_format(ajo_user_profile.dob)

        def get_age(ajo_user_profile: AjoUser):
            today = datetime.date.today()
            if ajo_user_profile.dob != None:
                age = today.year - ajo_user_profile.dob.year
                return int(age)
            else:
                return random.choice([45, 20, 49, 32, 67, 28, 98])

        age = get_age(ajo_user_profile)

        ajo_user_profile.age = age
        ajo_user_profile.save()

        if ajo_user_profile is None:
            return None

        gender = ajo_user_profile.gender
        if str(gender).lower() == "m":
            gender = "male"
        elif str(gender).lower() == "f":
            gender = "female"

        payload = {
            "borrower_country": "NG",
            "borrower_fullname": ajo_user_profile.fullname,
            "borrower_firstname": ajo_user_profile.first_name,
            "borrower_lastname": ajo_user_profile.last_name,
            "borrower_dob": date_of_birth,
            "borrower_address": ajo_user_profile.address,
            "borrower_age": age,
            "custom_field_5854": ajo_user_profile.alias,
            "borrower_business_name": ajo_user_profile.trade,
            "borrower_unique_number": f"LIB/{ajo_user_profile.id}-{ajo_user_profile.phone_number}/{ajo_user_profile.user.id}",  # noqa
            "borrower_gender": gender.capitalize() if gender else "",
            "borrower_title": "",
            "borrower_mobile": ajo_user_profile.phone_number,
            "borrower_email": ajo_user_profile.user.email,
            "custom_field_5037": ajo_user_profile.bvn,
            "custom_field_4220": "",
            "custom_field_4222": "",
            "custom_field_4221": "",
            "custom_field_6362": ajo_user_profile.trade,
        }

        url = f"{self.url}/borrower"
        """
        SAMPLE RESPONSE

            {
                "response": {
                    "borrower_id": "5080329"
                },
                "http": {
                    "code": 200,
                    "message": "OK"
                }
            } 

        """
        try:
            response = requests.request("POST", url, headers=self.headers, json=payload)
            result = response.json()
        except requests.exceptions.RequestException as e:
            result = {
                
                "response": {"errors": str(e), "url": url, "message": "Failed to create borrower on loan disk"},
                "http": {"code": 500, "message": "OK"},
            }

        except Exception as e:
            result = {
                "response": {"errors": str(e), "url": url, "message": "Failed to create borrower on loan disk"},
                "http": {"code": 500, "message": "OK"},
            }

        loan_disk_meta_data = {
            "from": "CREATE BORROWER",
            "payload": payload,
            "response": result
        }
        LoanDiskMetaData.objects.create(
            main_branch_response=loan_disk_meta_data,
            )
        
        return result

    def create_loan(self, loan_disk_borrower_id, ajo_loan_details, loan_status_id=8, rerun=1, loan_id=None, loan_mirror_id=None):
        """
        This handles posting of loans to loan disk
        """

        payload = {
            "loan_product_id": "236844",  # loanproduct id
            "borrower_id": loan_disk_borrower_id,
            "loan_application_id": ajo_loan_details.get("loan_id"),
            "loan_disbursed_by_id": "177632",
            "loan_principal_amount": ajo_loan_details.get("amount"),
            "loan_released_date": ajo_loan_details.get("loan_date"),
            "loan_first_repayment_date": ajo_loan_details.get(
                "loan_first_repayment_date"
            ),  # should be release date plus 4 days, include
            "loan_interest_method": "flat_rate",
            "loan_interest_type": "percentage",
            "loan_interest_period": "Day",
            "loan_interest": ajo_loan_details.get("interest_rate"),
            "loan_duration_period": "Days",
            "loan_duration": ajo_loan_details.get("loandisk_tenor_days"),  # should be in days
            "loan_payment_scheme_id": "6",
            "loan_num_of_repayments": ajo_loan_details.get(
                "loandisk_tenor_days"
            ),  # number of repayment should be the number of loan duration
            "loan_decimal_places": "round_up_to_five",
            "loan_status_id": loan_status_id,
            "custom_field_5262": "",
            "custom_field_4181": "",
            "custom_field_4178": "",
            "custom_field_5261": "",
            "custom_field_4361": "",
            "loan_fee_id_2746": 0,
            "loan_fee_id_3915": 2,  # PROCESSING FEE
            "loan_fee_id_4002": 0,
            "loan_fee_id_4003": 0,
            "loan_fee_id_4004": 0,
            "loan_fee_id_4005": 0,
            "loan_fee_id_4006": 0,
            "custom_field_5251": "",
            "custom_field_4385": "",
            "custom_field_6363": "",
            "custom_field_4219": "",
            "custom_field_4221": "",
            "custom_field_12642": "",
            "loan_interest_schedule": "charge_interest_normally",  # How should Interest be charged in Loan Schedule
        }

        url = f"{self.url}/loan"

        # print("url ---->", url, "\n")

        print("post loan to loandisk payload ::::::::::::::::: ", payload, "\n\n\n")
        
        if loan_mirror_id is not None:
            loan_disk_mirror = LoanDiskLoansMirror.objects.filter(id=loan_mirror_id).first()

        try:
            response = requests.request("POST", url, headers=self.headers, json=payload)
            # response = {
            #     "response": {
            #         "loan_id": "5906566"
            #     },
            #     "http": {
            #         "code": 200,
            #         "message": "OK"
            #     }

            # print(response.text)

            result = response.json()
            loan_disk_loan_id = result.get("response", {}).get("loan_id", None)
            if loan_mirror_id is not None:
                if loan_disk_loan_id is not None:
                    loan_disk_mirror.resolved = True
                    loan_disk_mirror.status = "COMPLETED"
                    loan_disk_mirror.main_branch_response = result
                    loan_disk_mirror.save()
                else:
                    loan_disk_mirror.main_branch_response = result
                    loan_disk_mirror.save()

        except requests.exceptions.RequestException as e:
            result = {
                "response": {"errors": str(e), "url": url, "message": "Failed to create loan on loan disk."},
                "http": {"code": 500, "message": "OK"},
            }

            if loan_mirror_id is not None:
                loan_disk_mirror.main_branch_response = result
                loan_disk_mirror.save()

        except Exception as e:
            result = {
                "response": {"errors": str(e), "url": url, "message": "Failed to create loan on loan disk."},
                "http": {"code": 500, "message": "OK"},
            }

            if loan_mirror_id is not None:
                loan_disk_mirror.main_branch_response = result
                loan_disk_mirror.save()

        
        loan_disk_meta_data = {
            "from": "CREATE LOANS",
            "payload": payload,
            "response": result
        }

        LoanDiskMetaData.objects.create(
            main_branch_response=loan_disk_meta_data,
            )

        if 'response' in result and 'Errors' in result['response']:
            errors = result['response']['Errors']
           
            unique_error = 'Loan #  must be unique. You already have an existing loan with the same Loan #'
            bad_borrower_error = 'This Borrower is suspended or does not exist'

            if unique_error in errors:
                loandisk_response = self.get_loan_by_application_id(payload.get("loan_application_id"))
                if loandisk_response.get("status") is True:
                    retrieved_loan_id = loandisk_response.get("data")["loan_id"]
                    try:
                        loan_obj = AjoLoan.objects.get(id=loan_id)
                        loan_obj.loandisk_loan_id = retrieved_loan_id
                        loan_obj.save()
                    except AjoLoan.DoesNotExist:
                        result = "Error saving loan disk ID: Unable to find loan disk ID"
                        if loan_mirror_id is not None:
                            loan_disk_mirror.main_branch_response = result
                            loan_disk_mirror.save()
                    
                    result = {"response": {"message": "Not unique Loan #, but SUCCESSFULLY saved loan ID on backend."}}
                    if loan_mirror_id is not None:
                        loan_disk_mirror.resolved = True
                        loan_disk_mirror.status = "COMPLETED"
                        loan_disk_mirror.main_branch_response = result
                        loan_disk_mirror.save()
                else:   
                    result = {"response": {"message": "Unique Loan # error, FAILED to retrieve loan id from loan disk"}}
                    if loan_mirror_id is not None:
                        loan_disk_mirror.main_branch_response = result
                        loan_disk_mirror.save()
            
            # elif bad_borrower_error in errors:
            elif any(bad_borrower_error in error for error in errors):
                try:
                    ajo_loan = AjoLoan.objects.get(id=loan_id)
                    ajo_user = ajo_loan.borrower
                except AjoLoan.DoesNotExist:
                    result = "Ajo Loan not found."
                    if loan_mirror_id is not None:
                        loan_disk_mirror.main_branch_response = result
                        loan_disk_mirror.save()

                parsed_dict = ast.literal_eval(ajo_user.loandisk_borrower_id)
                borrower_id = parsed_dict.get('response', {}).get('borrower_id')
                ajo_user.loandisk_borrower_id = borrower_id
                ajo_user.save()
                result = "SUCCESSFULLY Retrieved borrower ID from database within bad borrower id saved. Create Loan to run again"

                if borrower_id is None:
                    phone_number = ajo_user.phone_number
                    borrower_details = LoandiskManager().get_boorower_details(
                        phone_number=phone_number
                    )
                    if borrower_details.get("status") is True:
                        borrower_loandisk_id = borrower_details.get("response")["borrower_id"]
                        ajo_user.loandisk_borrower_id = borrower_loandisk_id
                        ajo_user.save()
                        result = "SUCCESSFULLY Retrieved borrower ID from LOAN DISK CALL."

                    result = "Tried to retrieved borrower ID from LOAN DISK CALL but FAILED."
                
                if loan_mirror_id is not None:
                    loan_disk_mirror.main_branch_response = result
                    loan_disk_mirror.save()

                if rerun == 1:
                    return self.create_loan(borrower_id, ajo_loan_details, loan_status_id=8, rerun=0, loan_id=loan_id, loan_mirror_id=loan_mirror_id)


        return result

    def update_loan(self, loan_disk_borrower_id, ajo_loan_details, loan_mirror_id=None):

        payload = {
            "loan_product_id": "236844",  # loanproduct id
            "borrower_id": loan_disk_borrower_id,
            "loan_application_id": ajo_loan_details.get("loan_id"),
            "loan_disbursed_by_id": "91595",  # disbursed by must be (libertypay)
            "loan_principal_amount": ajo_loan_details.get("loan_principal_amount"),
            "loan_released_date": ajo_loan_details.get("loan_date"),
            "loan_first_repayment_date": ajo_loan_details.get(
                "loan_first_repayment_date"
            ),  # should be release date plus 4 days
            "loan_interest_method": "flat_rate",
            "loan_interest_type": "percentage",
            "loan_interest_period": "Day",
            "loan_interest": ajo_loan_details.get("interest_rate"),
            "loan_duration_period": "Days",
            "loan_duration": ajo_loan_details.get("loandisk_tenor_days"),
            "loan_payment_scheme_id": "6",
            "loan_num_of_repayments": ajo_loan_details.get(
                "loandisk_tenor_days"
            ),  # number of repayment should be the number of loan duration
            "loan_decimal_places": "round_up_to_five",
            "loan_status_id": ajo_loan_details.get("loan_status_id"),
            "custom_field_5262": "",
            "custom_field_4181": "",
            "custom_field_5261": "",
            "custom_field_4361": "",
            "loan_fee_id_2746": 0,
            "loan_fee_id_3915": 2,  # PROCESSING FEE
            "loan_fee_id_4002": 0,
            "loan_fee_id_4003": 0,
            "loan_fee_id_4004": 0,
            "loan_fee_id_4005": 0,
            "loan_fee_id_4006": 0,
            "custom_field_5251": "",
            "custom_field_4385": "",
            "custom_field_6363": ajo_loan_details.get("ministry"),
            "custom_field_4219": "",
            "custom_field_4221": "",
            "custom_field_12642": ajo_loan_details.get("ministry"),
            "loan_interest_schedule": "charge_interest_normally",  # How should Interest be charged in Loan Schedule
            "loan_id": ajo_loan_details.get("branch_loandisk_loan_id"),
            "custom_field_4178": ajo_loan_details.get("borrower_bank_details"),
        }
        # payload = json.dumps(payload)

        # print("url ------- >", loan_url, "\n\n\n\n")
        # print(payload)
        if loan_mirror_id is not None:
            loan_disk_mirror = LoanDiskLoansMirror.objects.filter(id=loan_mirror_id).first()
        try:
            response = requests.request("PUT", self.loan_url, headers=self.headers, json=payload)

            # print(response.text)
            result = response.json()
            if response.status_code == 200:
                if loan_mirror_id is not None:
                    loan_disk_mirror.resolved = True
                    loan_disk_mirror.status = "COMPLETED"
                    loan_disk_mirror.main_branch_response = result
                    loan_disk_mirror.save()
            else:
                if loan_mirror_id is not None:
                    loan_disk_mirror.main_branch_response = result
                    loan_disk_mirror.save()
        except requests.exceptions.RequestException as e:
            result = {
                "response": {"errors": str(e), "url": self.loan_url},
                "http": {"code": 500, "message": "OK"},
            }
            if loan_mirror_id is not None:
                loan_disk_mirror.main_branch_response = result
                loan_disk_mirror.save()

        except Exception as e:
            # print(e)
            result = {
                "response": {"errors": str(e), "url": self.loan_url},
                "http": {"code": 500, "message": "OK"},
            }

            if loan_mirror_id is not None:
                loan_disk_mirror.main_branch_response = result
                loan_disk_mirror.save()

        loan_disk_meta_data = {
            "from": "UPDATE LOANS",
            "payload": payload,
            "response": result
        }
        LoanDiskMetaData.objects.create(
            main_branch_response=loan_disk_meta_data,
            )
        
        return result

    def post_repayment(self, loandisk_loan_id, amount, loan_repayment_method_id, loan_mirror_id=None):
        """
        Post repayment to loandisk
        """

        repayment_date = loan_disk_date_format()

        payload = {
            "loan_id": loandisk_loan_id,
            "repayment_amount": amount,
            "loan_repayment_method_id": loan_repayment_method_id,
            "repayment_collected_date": repayment_date,
            "collector_id": "10749",  # savings
            "custom_field_6035": "",
            "custom_field_6715": "",
        }
        payload = json.dumps(payload)

        url = f"{self.url}/repayment"
        # loan_disk_mirror = LoanDiskLoansMirror.objects.filter(id=loan_mirror_id).first()

        try:
            response = requests.request("POST", url, headers=self.headers, data=payload)
            # if response.status_code == 200:
                    
            #     loan_disk_mirror.resolved = True
            #     loan_disk_mirror.status = "COMPLETED"
            #     loan_disk_mirror.main_branch_response = response.json()
            #     loan_disk_mirror.save()
            # else:
            #     loan_disk_mirror.main_branch_response = response.json()
            #     loan_disk_mirror.save()
        except requests.exceptions.RequestException as e:
            # loan_disk_mirror.main_branch_response = e
            # loan_disk_mirror.save()
            return str(e)

        return response.json()

    def post_savings(self, savings_id, saving_details):
        """
        Post savings to loandisk
        """

        # if savings_instance:

        payload = {
            "savings_id": savings_id,
            "savings_product_id": "6355",
            "borrower_id": saving_details.get("borrower_id"),
            "savings_account_number": saving_details.get("savings_account_number"),
            "savings_fees": saving_details.get("savings_fees"),
            "savings_description": saving_details.get("savings_description"),
            "savings_balance": saving_details.get("savings_balance"),
            "savings_status": saving_details.get("savings_status"),
        }
        payload = json.dumps(payload)
        # print("------------------------------------------------------------")
        print(payload)
        # print("------------------------------------------------------------\n")

        url = f"{self.url}/saving"
        # print("------------------------------------------------------------")
        print(url)
        # print("------------------------------------------------------------\n")

        response = requests.request("POST", url, headers=self.headers, data=payload)
        # print("------------------------------------------------------------")
        print(response.text)
        # print("------------------------------------------------------------\n")
        """
        SAMPLE RESPONSE 
        {
            "response": {
                "savings_id": "1177383"
            },
            "http": {
                "code": 200,
                "message": "OK"
            }
        }
        """
        return response.json()
    
    def get_loan_by_application_id(self, loan_application_id):
        """
        Method to retrieve a particualr loan using the `loan_application_id`
        generated from our end and saved on loan disk.
        """

        url = f"{self.base_url}/{settings.LOANDISK_PUBLIC_KEY}/{settings.LOAN_DISK_BRANCH_ID}/loan//loan_application_id/{loan_application_id}"
        payload = {}
        

        try: 
            resp = requests.request("GET", url, headers=self.headers, data=payload)
            response = resp.json()
            loan_id = response.get("response")["Results"][0]["loan_id"]
            data = {
                "status": True,
                "data": {"loan_id": loan_id}
            }
            return data

        except:
            data = {
                "status": False,
                "message": resp.text
            }
            return data


class BranchLoanDiskManager:
    def __init__(self) -> None:
        self.headers = {
            "Authorization": f"Basic {settings.LOAN_DISK_SEC_KEY}",
            "Content-Type": "application/json",
        }

        self.base_url = "https://api-main.loandisk.com"
        self.url = f"{self.base_url}/{settings.LOANDISK_PUBLIC_KEY}"

    def get_borrower_using_phone(self, phone, branch_id):
        url = f"{self.url}/{branch_id}/borrower/borrower_mobile/{phone}"
        payload = {}
        response = requests.request("GET", url, headers=self.headers, data=payload)

        try:
            return response.json()
        except:
            return response.text

    def create_borrower_on_branch(self, borrower_details, branch_id):
        """
        This function create borrower on loandisk
        Args: self: Initialized loandisk helper function
        employment instance: The instance of the employment table
        Returns: Dictionary of loandisk response
        """

        gender = borrower_details.get("borrower_gender")
        if str(gender).lower() == "m":
            gender = "male"
        elif str(gender).lower() == "f":
            gender = "female"

        borrower_details_on_loandisk = self.get_borrower_using_phone(
            borrower_details.get("borrower_mobile"), branch_id=branch_id
        )
        if isinstance(borrower_details_on_loandisk, dict):
            try:
                if borrower_details_on_loandisk["error"]["message"] == "Not Found":
                    # borrower does not exist on loandisk
                    pass
            except KeyError:
                if borrower_details_on_loandisk["response"]["Results"]:
                    borrower_id = borrower_details_on_loandisk["response"]["Results"][0][0]["borrower_id"]

                    result = {"response": {"borrower_id": borrower_id}, "http": {"code": 200, "message": "OK"}}

                    return result

        payload = {
            "borrower_country": "NG",
            "borrower_fullname": borrower_details.get("borrower_fullname"),
            "borrower_firstname": borrower_details.get("borrower_firstname"),
            "borrower_lastname": borrower_details.get("borrower_lastname"),
            "borrower_dob": borrower_details.get("date_of_birth"),
            "borrower_address": borrower_details.get("address"),
            "borrower_age": borrower_details.get("age"),
            "custom_field_5854": borrower_details.get("alias"),
            "borrower_business_name": borrower_details.get("borrower_business_name"),
            "borrower_unique_number": borrower_details.get("borrower_unique_number"),  # noqa
            "borrower_gender": gender.capitalize(),
            "borrower_title": "",
            "borrower_mobile": borrower_details.get("borrower_mobile"),
            "borrower_email": borrower_details.get("borrower_email"),
            "custom_field_5037": borrower_details.get("bvn"),  # include
            "custom_field_4220": "",
            "custom_field_4222": "",
            "custom_field_4221": "",
            "custom_field_6362": borrower_details.get("borrower_business_name"),
        }
        # print(payload, "\n")
        url = f"{self.url}/{branch_id}/borrower"

        try:
            """
            SAMPLE RESPONSE

                {
                    "response": {
                        "borrower_id": "5080329"
                    },
                    "http": {
                        "code": 200,
                        "message": "OK"
                    }
                }

            """
            response = requests.request("POST", url, headers=self.headers, json=payload)

            result = response.json()
        except requests.exceptions.RequestException as e:
            # print("BRANCH LOANDISK BORROWER ERROR ---->", e, "\n\n")
            result = {
                "response": {"errors": str(e)},
                "http": {"code": 500, "message": "OK"},
            }

        except Exception as e:
            # print("BRANCH LOANDISK BORROWER ERROR ---->", e, "\n\n")
            result = {
                "response": {"errors": str(e), "url": url},
                "http": {"code": 500, "message": "OK"},
            }
        return result

    def create_loan_on_branch(self, loan_disk_borrower_id, ajo_loan_details, loan_status_id=8):
        """
        This handles posting of loans to loan disk
        """

        payload = {
            "loan_product_id": "236844",  # loanproduct id
            "borrower_id": loan_disk_borrower_id,
            "loan_application_id": ajo_loan_details.get("loan_ref_2"),
            "loan_disbursed_by_id": "177632",  # disbursed by must be (libertypay)
            "loan_principal_amount": ajo_loan_details.get("amount"),
            "loan_released_date": ajo_loan_details.get("loan_date"),
            "loan_first_repayment_date": ajo_loan_details.get(
                "loan_first_repayment_date"
            ),  # should be release date plus 4 days
            "loan_interest_method": "flat_rate",
            "loan_interest_type": "percentage",
            "loan_interest_period": "Day",
            "loan_interest": ajo_loan_details.get("interest_rate"),
            "loan_duration_period": "Days",
            "loan_duration": ajo_loan_details.get("loandisk_tenor_days"),
            "loan_payment_scheme_id": "6",
            "loan_num_of_repayments": ajo_loan_details.get(
                "loandisk_tenor_days"
            ),  # number of repayment should be the number of loan duration
            "loan_decimal_places": "round_up_to_five",
            "loan_status_id": loan_status_id,
            "custom_field_5262": "",
            "custom_field_4181": "",
            "custom_field_4178": "",
            "custom_field_5261": "",
            "custom_field_4361": "",
            "loan_fee_id_2746": 0,
            "loan_fee_id_3915": 2,  # PROCESSING FEE
            "loan_fee_id_4002": 0,
            "loan_fee_id_4003": 0,
            "loan_fee_id_4004": 0,
            "loan_fee_id_4005": 0,
            "loan_fee_id_4006": 0,
            "custom_field_5251": "",
            "custom_field_4385": "",
            "custom_field_6363": "",
            "custom_field_4219": "",
            "custom_field_4221": "",
            "custom_field_12642": "",
            "loan_interest_schedule": "charge_interest_normally",  # How should Interest be charged in Loan Schedule
        }

        branch_id = ajo_loan_details.get("branch_id")

        url = f"{self.url}/{branch_id}/loan"

        try:
            response = requests.request("POST", url, headers=self.headers, json=payload)

            result = response.json()
        except requests.exceptions.RequestException as e:
            result = {
                "response": {"errors": str(e), "url": url},
                "http": {"code": 500, "message": "OK"},
            }
        except Exception as e:
            result = {
                "response": {"errors": str(e), "url": url},
                "http": {"code": 500, "message": "OK"},
            }
        return result

    def update_loan_on_branch(self, loan_disk_borrower_id, ajo_loan_details):

        branch_id = ajo_loan_details.get("branch_id")

        payload = {
            "loan_product_id": "236844",  # loanproduct id
            "borrower_id": loan_disk_borrower_id,
            "loan_application_id": ajo_loan_details.get("loan_ref_2"),
            "loan_disbursed_by_id": "177632",  # disbursed by must be (libertypay)
            "loan_principal_amount": ajo_loan_details.get("loan_principal_amount"),
            "loan_released_date": ajo_loan_details.get("loan_date"),
            "loan_first_repayment_date": ajo_loan_details.get(
                "loan_first_repayment_date"
            ),  # should be release date plus 4 days
            "loan_interest_method": "flat_rate",
            "loan_interest_type": "percentage",
            "loan_interest_period": "Day",
            "loan_interest": ajo_loan_details.get("interest_rate"),
            "loan_duration_period": "Days",
            "loan_duration": ajo_loan_details.get("loandisk_tenor_days"),
            "loan_payment_scheme_id": "6",
            "loan_num_of_repayments": "30",  # number of repayment should be the number of loan duration
            "loan_decimal_places": "round_up_to_five",
            "loan_status_id": ajo_loan_details.get("loandisk_loan_status"),
            "custom_field_5262": "",
            "custom_field_4181": "",
            "custom_field_5261": "",
            "custom_field_4361": "",
            "loan_fee_id_2746": 0,
            "loan_fee_id_3915": 2,  # PROCESSING FEE
            "loan_fee_id_4002": 0,
            "loan_fee_id_4003": 0,
            "loan_fee_id_4004": 0,
            "loan_fee_id_4005": 0,
            "loan_fee_id_4006": 0,
            "custom_field_5251": "",
            "custom_field_4385": "",
            "custom_field_6363": ajo_loan_details.get("ministry"),
            "custom_field_4219": "",
            "custom_field_4221": "",
            "custom_field_12642": ajo_loan_details.get("ministry"),
            "loan_interest_schedule": "charge_interest_normally",  # How should Interest be charged in Loan Schedule
            "loan_id": ajo_loan_details.get("branch_loandisk_loan_id"),
            "custom_field_4178": ajo_loan_details.get("borrower_bank_details"),
        }

        url = f"{self.url}/{branch_id}/loan"

        try:
            response = requests.request("PUT", url, headers=self.headers, json=payload)
            result = response.json()
        except requests.exceptions.RequestException as e:
            result = {
                "response": {"errors": str(e), "url": url},
                "http": {"code": 500, "message": "OK"},
            }
        except Exception as e:
            result = {
                "response": {"errors": str(e), "url": url},
                "http": {"code": 500, "message": "OK"},
            }
        return result

    def post_repayment(self, loandisk_loan_id, amount, branch_id, loan_repayment_method_id):
        """
        Post repayment to loandisk
        """

        repayment_date = loan_disk_date_format()

        payload = {
            "loan_id": loandisk_loan_id,
            "repayment_amount": amount,
            "loan_repayment_method_id": loan_repayment_method_id,
            "repayment_collected_date": repayment_date,
            "collector_id": "10749",  # savings
            "custom_field_6035": "",
            "custom_field_6715": "",
        }

        payload = json.dumps(payload)

        url = f"{self.url}/{branch_id}/repayment"

        try:
            response = requests.request("POST", url, headers=self.headers, data=payload)
        except requests.exceptions.RequestException as e:
            return str(e)

        return response.json()

    def post_savings_to_branch(self, saving_details, branch_id):
        """
        Post savings to loandisk
        """

        # if savings_instance:

        payload = {
            "savings_id": saving_details.get("savings_id"),
            "savings_product_id": "6355",
            "borrower_id": saving_details.get("borrower_id"),
            "savings_account_number": saving_details.get("savings_account_number"),
            "savings_fees": saving_details.get("savings_fees"),
            "savings_description": saving_details.get("savings_description"),
            "savings_balance": saving_details.get("savings_balance"),
            "savings_status": saving_details.get("savings_status"),
        }
        payload = json.dumps(payload)
        # print("------------------------------------------------------------")
        print(payload)
        # print("------------------------------------------------------------\n")

        url = f"{self.url}/{branch_id}/saving"
        # url = f"{base_url}/{public_key}/{56926}/saving"
        # print("------------------------------------------------------------")
        print(url)
        # print("------------------------------------------------------------\n")

        response = requests.request("POST", url, headers=self.headers, data=payload)
        # print("------------------------------------------------------------")
        print(response.text)
        # print("------------------------------------------------------------\n")
        """
        SAMPLE RESPONSE 
        {
            "response": {
                "savings_id": "1177383"
            },
            "http": {
                "code": 200,
                "message": "OK"
            }
        }
        """
        return response.json()