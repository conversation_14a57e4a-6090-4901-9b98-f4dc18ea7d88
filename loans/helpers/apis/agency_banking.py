# import json
import json
import requests

from django.core.cache import cache
from datetime import timedelta

# from datetime import datetime, timedelta
from urllib.parse import urlencode
from helper_methods import format_api_response
from loans.helpers.apis.base_api import AuthType, BaseAPIManager


class LibertyPayMgr(BaseAPIManager):
    """
    A class to interact with the LibertyPay API for agency banking operations.

    This class provides methods to retrieve agency banking user details
    and transaction records from the LibertyPay API.
    """

    DEVELOPMENT_URL = "https://dev.libertypayng.com"
    PRODUCTION_URL = "https://backend.libertypayng.com"
    VALID_ENVIRONMENTS = {"development", "production"}
    CACHE_KEY_PREFIX = "liberty_pay_token"
    TOKEN_TIMEOUT = 60 * 60 * 6  # 6 hours in seconds

    def __init__(
        self,
        auth_type: AuthType = AuthType.BEARER,
        config=None,
    ):
        super().__init__(auth_type=auth_type)

        if config.ENVIRONMENT not in self.VALID_ENVIRONMENTS:
            raise ValueError(
                f"Invalid environment '{config.ENVIRONMENT}'. Choose from {self.VALID_ENVIRONMENTS}."
            )

        self.config = config
        self.base_url = (
            self.DEVELOPMENT_URL
            if config.ENVIRONMENT == "development"
            else self.PRODUCTION_URL
        )

        self.headers = self.get_auth_header(config.LIBERTY_CREDI_TOKEN)

    def get_headers(self, extra_headers: dict = None) -> dict:
        """
        Returns the request headers, optionally merging with extra headers.

        :param extra_headers: A dictionary of additional headers to include.
        :return: A dictionary of request headers.
        """
        headers = self.headers.copy()  # Avoid modifying the original headers
        if extra_headers:
            headers.update(extra_headers)
        return headers

    def _query_string(self, kwargs):
        """
        Converts keyword arguments into a URL-encoded query string.

        Args:
            kwargs (dict): Query parameters.

        Returns:
            str: URL-encoded query string.
        """
        return urlencode(kwargs)

    def get_agency_banking_users(self, **kwargs):
        """
        Fetches agency banking user details from LibertyPay API.

        Args:
            **kwargs: Query parameters for filtering user details.

        Returns:
            dict: Formatted API response containing user details.
        """
        url = f"{self.base_url}/agency/loans/all_user_details/"
        query_params = self._query_string(kwargs)
        absolute_url = f"{url}?{query_params}" if query_params else url

        response = requests.get(absolute_url, headers=self.headers)

        return format_api_response(url=absolute_url, payload=kwargs, response=response)

    def get_agency_banking_transactions(self, **kwargs):
        """
        Fetches agency banking transaction records from LibertyPay API.

        Args:
            **kwargs: Query parameters for filtering transactions.

        Returns:
            dict: Formatted API response containing transaction records.
        """
        url = f"{self.base_url}/agency/loans/all_transactions_list/"
        query_params = self._query_string(kwargs)
        absolute_url = f"{url}?{query_params}" if query_params else url

        response = requests.get(absolute_url, headers=self.headers)

        return format_api_response(url=absolute_url, payload=kwargs, response=response)

    def agent_login(self):

        url = f"{self.base_url}/user/login/create/"
        payload = {
            "email": self.config.AGENCY_BANKING_USEREMAIL,
            "password": self.config.AGENCY_BANKING_PASSWORD,
            "device_type": "MOBILE",
        }

        response = requests.post(url, headers=self.headers, json=payload)
        result = format_api_response(url=url, payload={}, response=response)

        response = result.get("response")
        if isinstance(response, dict) and result.get("status") == "success":
            access_token = response.get("access")
            cache_key = f"agent:token"
            cache.set(key=cache_key, value=access_token, timeout=self.TOKEN_TIMEOUT)
            return access_token
        else:
            return result

    def get_token(self):
        cache_key = f"agent:token"
        token = cache.get(cache_key)
        if token:
            return token
        else:
            return self.agent_login()

    def request_super_token(self):
        url = f"{self.base_url}/agency/generate_super_token/"

        _token = self.get_token()
        if isinstance(_token, str):
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {_token}",
            }

            response = requests.get(url, headers=headers)
            result = format_api_response(url=url, payload={}, response=response)

            if isinstance(result, dict) and result.get("status") == "success":
                super_token = result.get("response", {}).get("super_token")
                cache_key = f"merchant:super_token"
                cache.set(key=cache_key, value=super_token, timeout=self.TOKEN_TIMEOUT)
                return super_token
            else:
                return result

    def get_super_token(self):
        cache_key = f"merchant:super_token"
        super_token = cache.get(cache_key)
        if super_token:
            return super_token
        else:
            return self.request_super_token()

    def clear_super_token(self):
        cache_key = f"merchant:super_token"
        cache.delete(cache_key)
        return True

    def clear_agent_token(self):
        cache_key = f"agent:token"
        cache.delete(cache_key)
        return True

    def charge_user_wallet(self, user_id, unique_reference, narration, amount):
        """
        Charges a user's wallet through the LibertyPay API.

        Args:
            **kwargs: Payload data for the wallet charge request.

        Returns:
            dict: Formatted API response containing the charge result.
        """

        payload = {
            "service_name": "SAVINGS",
            "user_id": user_id,
            "unique_reference": unique_reference,
            "narration": narration,
            "total_amount": amount,
            "service_comm": amount,
            "agent_comm": 0,
            "transaction_pin": self.config.AGENCY_BANKING_TRANSACTION_PIN,
        }
        url = f"{self.base_url}/send/other_services_charge/"
        super_token = self.get_super_token()

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Token {self.config.AGENCY_AUTH_TOKEN}",
            "Super-Token": f"{super_token}",
        }

        response = requests.post(url, headers=headers, json=payload)
        payload.pop("transaction_pin")
        return format_api_response(
            url=url, payload=payload, response=response, params=super_token
        )

        # return {
        #     "url": "https://dev.libertypayng.com/send/other_services_charge/",
        #     "status_code": 202,
        #     "status": "success",
        #     "params": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************.48rt3M6bdAPSyrjVQ_a6KCs_a-eLL9W6FAA-rC_9fMQ",
        #     "response": {
        #         "message": "success",
        #         "data": {
        #             "message": "Transaction completed successfully",
        #             "amount_sent": 500.0,
        #             "escrow_id": "50cdc14f-b56f-4d37-87e9-d34d1f210a04_1744715248",
        #         },
        #         "date_completed": "2025-04-15T12:07:28.965950",
        #     },
        #     "method": "POST",
        #     "payload": {
        #         "service_name": "SAVINGS",
        #         "user_id": 246,
        #         "unique_reference": "da061c60-a7e8-41dc-bbc0-965fdf89b7ca",
        #         "narration": "Loan Processing Fee charged from the user's agent wallet for loan processing",
        #         "total_amount": 500.0,
        #         "service_comm": 500.0,
        #         "agent_comm": 0,
        #     },
        # }

    def get_agent_balance(self, user_ids):

        url = f"{self.base_url}/accounts/check_balance_levels/"

        payload = {"user_ids": user_ids}

        response = requests.post(url, headers=self.headers, json=payload)
        return format_api_response(url=url, payload=payload, response=response)

    def get_wallet_balance(self, response_data, wallet_type="COLLECTION"):
        """
        Extracts the available balance of the 'COLLECTION' wallet from the response data.

        :param response_data: List containing user wallet data.
        :return: Available balance of the 'COLLECTION' wallet or None if not found.
        """
        if response_data:
            for wallet in response_data[0].get("wallets", []):
                if wallet.get("wallet_type") == wallet_type:
                    return wallet.get("available_balance")
        return None  # Return None if COLLECTION wallet is not found
