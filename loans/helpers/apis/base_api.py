from enum import Enum
from typing import Optional

class AuthType(Enum):
    BEARER = "Bearer"
    TOKEN = "Token"
    HOOK = "Hook"
    BASIC = "Basic"

class BaseAPIManager:
    def __init__(self, auth_type: AuthType = AuthType.BEARER):
        self.auth_type = auth_type
    
    def get_auth_header(self, token: str, extra_headers: Optional[dict] = None) -> dict:
        """
        Generate headers with flexible authentication type
        
        Args:
            token: Authentication token
            extra_headers: Additional headers to include
        """
        headers = {
            "Content-Type": "application/json",
            # "Authorization": f"{self.auth_type.value} {token}"
            "Authorization": f"Bearer {token}"
        }
        
        if extra_headers:
            headers.update(extra_headers)
            
        return headers