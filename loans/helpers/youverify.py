import datetime
import random
import requests
import time

from accounts.utils import EmailUtil
from dataclasses import dataclass
from django.conf import settings

@dataclass
class YouVerifyAPI:
    environment = settings.ENVIRONMENT

    if environment == "production":
        base_url = "https://api.youverify.co/v2/api/identity/ng"
        token = settings.YOU_VERIFY_PROD_TOKEN
    else:
        base_url = "https://api.sandbox.youverify.co/v2/api/identity/ng"
        token = settings.YOU_VERIFY_SAN_BOX_TOKEN

    headers = {
        'token': token,
        'Content-Type': 'application/json'
    }

    def verify_nin(self, nin):
        payload = {
            "id": nin,
            "isSubjectConsent": True
        }
        url = f"{self.base_url}/nin"
        response = requests.post(url, headers=self.headers, json=payload)
        return response.json()

    @staticmethod
    def generate_request_id() -> str:
        timestamp = int(time.time() * 1000)  # Current timestamp in milliseconds
        random_number = random.randint(1000, 9999)  # Random 4-digit number
        unique_ref = f"{timestamp}-{random_number}"
        return unique_ref

    def pvc_verification(self, pvc: str, request_id: str):
        payload = {
            "id": pvc,
            "metadata": {
                "requestId": request_id
            },
            "isSubjectConsent": True
        }
        url = f"{self.base_url}/pvc"
        response = requests.post(url, headers=self.headers, json=payload)
        return response.json()

    def pvc_validation(self, pvc: str, request_id: str,
                       first_name: str,
                       last_name: str, date_of_birth: datetime):
        payload = {
            "id": pvc,
            "metadata": {
                "requestId": request_id
            },
            "isSubjectConsent": True,
            "validations": {
                "data": {
                    "firstName": first_name,
                    "lastName": last_name,
                    "dateOfBirth": date_of_birth
                }
            }
        }
        url = f"{self.base_url}/pvc"
        response = requests.post(url, headers=self.headers, json=payload)
        return response.json()

    def verify_bvn(self, bvn):
        payload = {
            "id": bvn,
            "metadata": {
                "requestId": self.generate_request_id()
            },
            "isSubjectConsent": True,
            "premiumBVN": True
        }
        url = f"{self.base_url}/bvn"
        response = requests.post(url, headers=self.headers, json=payload)
        return response.json()


# Example usage:
if __name__ == "__main__":
    # token = ''
    # verifier = YouVerifyAPI(token)
    # nin = "11111111111"
    # response = verifier.verify_nin(nin)
    # print(response)
    pass


def get_branch_id(branch_name): 
    location_codes = { 
        "AJAH": "000122", 
        "ALABA": "000129", 
        "OYINGBO": "000182", 
        "YABA": "000162", 
        "IWAYA": "000132", 
        "IKEJA": "21569", 
        "OSHODI": "000172", 
    } 

    key_name =  branch_name 
    if key_name in location_codes: 
        value = location_codes[key_name] 

        return value 



def check_you_verify_balance(object_instance, mails:list): 
    """
    SAMPLE RESPONSE
    {
    "data": {}, 
    "name": "Error", 
    "message": "9 FAILED_PRECONDITION: Insufficient fund or credit facility has expired!", 
    "success": "false", 
    "statusCode": 500
    } 
"""

    if object_instance.get("statusCode") == 500:
        for mail in mails: 
            data = {
                "to_email": mail,
                "email_subject": "You Verify Low Balance Report",
                "email_body": "Your YouVerify Balnce is low. Please Recharge you account" 
            }

            send_mail_to_receipient = EmailUtil.send_mailgun_text_email(data=data) 

    