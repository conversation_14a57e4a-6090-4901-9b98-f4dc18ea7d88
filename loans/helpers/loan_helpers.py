import ast
import json
import os
from datetime import datetime, timedelta
from json import JSONDecodeError

import redis
import requests
from django.conf import settings
from django.core.cache import cache

from ..enums import NinBvnRequestStatus


class CreditBearueChecks:

    base_url = "https://online.firstcentralcreditbureau.com/firstcentralrestv2"
    username = settings.CRC_USERNAME
    password = settings.CRC_PASSWORD

    def request_auth_token(self):

        url = f"{self.base_url}/login"

        payload = json.dumps({"username": self.username, "password": self.password})
        headers = {"Content-Type": "application/json"}
        response = requests.request("POST", url, headers=headers, data=payload)

        return response

    def get_token(self):
       
        cache_token = cache.get(key=self.password)
        if cache_token:
            return cache_token
        try:
            response = self.request_auth_token()
            token_res = response.json()
        except requests.exceptions.RequestException as e:
            token_res = None

        if isinstance(token_res, list) and len(token_res) > 0:
            token = token_res[0].get("DataTicket")
            cache.set(key=self.password, value=token, timeout=60 * 20)  # last 20min
            return token

        else:
            return None

    def consumer_match(self, Identification):

        url = f"{self.base_url}/connectConsumerMatch/"

        token = self.get_token()
        payload = json.dumps(
            {
                "DataTicket": token,
                "EnquiryReason": "Application for Credit by a borrower",
                "ConsumerName": "",
                "DateOfBirth": "",
                "Identification": Identification,
                "Accountno": "",
                "ProductID": "45",
            }
        )
        headers = {"Content-Type": "application/json"}
        response = requests.request("POST", url, headers=headers, data=payload)
        # print(response.text, "\n\n")

        try:
            result = response.json()
        except requests.exceptions.RequestException as e:
            result = None

        if isinstance(result, list) and len(result) > 0:
            matched_result = result[0].get("MatchedConsumer", [])
            if not matched_result:
                return None
            else:
                final_result = matched_result[0]
                final_result["data_ticket"] = token
                return final_result

    def get_credit_data(self, Identification):

        consumer_match_result = self.consumer_match(Identification=Identification)
        # print(consumer_match_result)
        data_ticket = consumer_match_result.get("data_ticket")
        MatchingEngineID = consumer_match_result.get("MatchingEngineID")
        consumerID = consumer_match_result.get("ConsumerID")
        EnquiryID = consumer_match_result.get("EnquiryID")

        if consumerID == "0":
            return [consumer_match_result]  # no record
        else:
            url = f"{self.base_url}/GetConsumerFullCreditReport/"

            payload = json.dumps(
                {
                    "DataTicket": data_ticket,
                    "consumerID": consumerID,
                    "EnquiryID": EnquiryID,
                    "consumerMergeList": consumerID,
                    "SubscriberEnquiryEngineID": MatchingEngineID,
                }
            )
            headers = {"Content-Type": "application/json"}

            try:
                response = requests.request(
                    "POST", url, headers=headers, data=payload, timeout=100
                )
                return response.json()
            except requests.exceptions.RequestException as err:
                return {"error": str(err), "status_code": 500}


class FirstcentralCreditCheck(CreditBearueChecks):
    def __init__(
        self,
        bvn,
        debt_threshold,
        ajo_user=None,
        max_debt_institution_count=3,
        phone_number=None,
        renew=False,
    ) -> None:
        self.bvn = bvn
        self.ajo_user = ajo_user
        self.phone_number = phone_number
        self.debt_threshold = debt_threshold
        self.max_debt_institution_count = max_debt_institution_count
        self.renew = renew

    @staticmethod
    def string_to_float(amount_str: str) -> float:
        value_str = amount_str.replace(",", "")
        parts = value_str.split(".")

        # Join the integer and decimal parts, and convert to float
        value_float = float(".".join(parts))
        return value_float

    def is_worthy(
        self, total_outstanding, open_loans, bad_loans, performing_loans, user_behaviour
    ) -> dict:

        if total_outstanding > self.debt_threshold:
            is_credit_worthy = False
            high_outstanding_debt = True
            reason = f"Borrower has an outstanding of {total_outstanding}"

        elif open_loans > self.max_debt_institution_count:
            is_credit_worthy = False
            high_outstanding_debt = False
            reason = f"Borrower has {open_loans} loans"

        elif bad_loans >= 2:
            good_to_bad_ratio = performing_loans / bad_loans
            if good_to_bad_ratio < 0.2 and performing_loans != bad_loans:
                is_credit_worthy = False
                high_outstanding_debt = False
                reason = "Ratio of good loans to bad loans is less than 20%"
            elif user_behaviour != "positive":
                is_credit_worthy = False
                high_outstanding_debt = False
                reason = f"Borrower has {user_behaviour} loan repayment behaviour"

            else:
                is_credit_worthy = True
                high_outstanding_debt = False
                reason = "Approved"

        else:
            is_credit_worthy = True
            high_outstanding_debt = False
            reason = "Approved"

        result = {
            "is_credit_worthy": is_credit_worthy,
            # "credit_decision": credit_decision,
            "high_outstanding_debt": high_outstanding_debt,
            "reason": reason,
        }
        return result

    def get_credit_status(self):
        from loans.models import CreditBureauMetaData

        failed_result = {
            "status": None,
            "reason": "An error occured",
            "debt_threshold": self.debt_threshold,
            "high_outstanding_debt": False,
            "max_debt_institution_count": self.max_debt_institution_count,
            "total_outstanding": 0,
            "count_of_open_loans": 0,
            "open_loan_institutions": [],
            "bad_loans_institions_count": 0,
            "bad_loans_institions": [],
        }

        ##################################################################################
        if settings.ENVIRONMENT == "development":
            return {
                "status": True,
                "reason": "Not Found On crc",
                "debt_threshold": self.debt_threshold,
                "high_outstanding_debt": False,
                "max_debt_institution_count": self.max_debt_institution_count,
                "total_outstanding": 0,
                "count_of_open_loans": 0,
                "open_loan_institutions": [],
                "bad_loans_institions_count": 0,
                "bad_loans_institions": [],
                "user_behaviour": "",
            }
        ##################################################################################

        try:
            existing_data = CreditBureauMetaData.get_existing_bureau_data(
                verification_id=self.bvn, renew=self.renew
            )
            if existing_data is None:
                credit_summary_response = super().get_credit_data(
                    Identification=self.bvn
                )

                if isinstance(credit_summary_response, dict):
                    status = NinBvnRequestStatus.FAILED
                else:
                    status = NinBvnRequestStatus.SUCCESS

                data = CreditBureauMetaData.objects.create(
                    ajo_user=self.ajo_user,
                    phone_number=self.phone_number,
                    verification_id=self.bvn,
                    status=status,
                    response=credit_summary_response,
                )
            else:
                credit_summary_response = existing_data[0]
                data = existing_data[1]

        except Exception as err:
            data = CreditBureauMetaData.objects.create(
                ajo_user=self.ajo_user,
                phone_number=self.phone_number,
                verification_id=self.bvn,
                status=NinBvnRequestStatus.FAILED,
                response=err,
            )
            return failed_result

        if isinstance(credit_summary_response, list):
            if credit_summary_response[0].get("MatchingEngineID", None):
                return {
                    "status": True,
                    "reason": "Not Found On crc",
                    "debt_threshold": self.debt_threshold,
                    "high_outstanding_debt": False,
                    "max_debt_institution_count": self.max_debt_institution_count,
                    "total_outstanding": 0,
                    "count_of_open_loans": 0,
                    "open_loan_institutions": [],
                    "bad_loans_institions_count": 0,
                    "bad_loans_institions": [],
                    "user_behaviour": "",
                }

        try:
            matched_consumer_response_type = credit_summary_response[0]
        except (IndexError, KeyError, ValueError):
            return failed_result

        # unique response for user with no loan record
        matched_consumer = matched_consumer_response_type.get("MatchedConsumer", None)

        if matched_consumer is None:
            try:
                account_summary = credit_summary_response[3]["CreditAccountSummary"][0]
                subject_list = credit_summary_response[0]["SubjectList"]
                credit_summary_list = credit_summary_response[5][
                    "CreditAgreementSummary"
                ]

            except (KeyError, IndexError) as err:
                return failed_result

            # update credit bureau metadata
            total_account_arrears = account_summary.get("TotalAccountarrear")
            over_all_monthly_istallment = account_summary.get("TotalMonthlyInstalment")
            total_monthly_instalment = self.string_to_float(
                amount_str=over_all_monthly_istallment
            )

            data.no_of_defaulted_loans = total_account_arrears
            data.monthly_repayment_amount = total_monthly_instalment
            data.save()

            total_outstanding = account_summary.get("TotalOutstandingdebt")
            performing_loans_institions: list = []
            good_loans = 0
            bad_loans = 0
            open_loans = 0
            open_loan_institutions: list = []
            bad_loans_institions: list = []
            institution_status = {}
            lost_loan_accs = []

            for credit_summary_by_institution in credit_summary_list:
                overdue = credit_summary_by_institution.get("AmountOverdue")
                account_status = credit_summary_by_institution.get("AccountStatus")
                account_num = credit_summary_by_institution.get("AccountNo")
                performance_status = credit_summary_by_institution.get(
                    "PerformanceStatus"
                )
                institution_name = credit_summary_by_institution.get("SubscriberName")
                date_open = datetime.strptime(
                    credit_summary_by_institution.get("DateAccountOpened"), "%d/%m/%Y"
                )

                credit_summary_by_institution["DateAccountOpened"] = date_open

                if performance_status != "Performing":
                    if performance_status == "Lost":
                        if overdue == 0.0 or account_num in lost_loan_accs:
                            continue
                        lost_loan_accs.append(account_num)
                    else:
                        if institution_name not in bad_loans_institions:
                            bad_loans_institions.append(institution_name)

                        try:
                            latest_date = max(
                                institution_status.get("institution_name")
                            )
                            if latest_date < date_open:
                                print("There is no date later than the current one.")
                                bad_loans += 1
                        except Exception:
                            bad_loans += 1

                elif performance_status == "Performing":
                    if account_status == "Open":
                        if institution_name not in open_loan_institutions:
                            open_loan_institutions.append(institution_name)

                    if institution_name not in performing_loans_institions:
                        performing_loans_institions.append(institution_name)
                    good_loans += 1
                    if institution_name in institution_status:
                        institution_status[institution_name].append(date_open)
                    else:
                        institution_status[institution_name] = [date_open]

                # elif account_status == "Open":
                #     if institution_name not in open_loan_institutions:
                #         open_loan_institutions.append(institution_name)
                #     open_loans += 1

            credit_summary_list.sort(key=lambda x: x["DateAccountOpened"])

            performing_at_start = 0
            performing_at_end = 0

            # Count performing loans at the beginning and at the end
            for agreement in credit_summary_list:
                if agreement["PerformanceStatus"] == "Performing":
                    performing_at_end += 1
                else:
                    performing_at_start = performing_at_end
                    performing_at_end = 0
            user_behaviour = ""
            if performing_at_end > performing_at_start:
                user_behaviour = "positive"
            else:
                user_behaviour = "negative"

            total_outstanding_to_float = self.string_to_float(total_outstanding)

            worthiness = self.is_worthy(
                total_outstanding=total_outstanding_to_float,
                open_loans=len(open_loan_institutions),
                bad_loans=len(bad_loans_institions),
                performing_loans=len(performing_loans_institions),
                user_behaviour=user_behaviour,
            )
            result = {
                "status": worthiness.get("is_credit_worthy"),
                "reason": worthiness.get("reason"),
                "debt_threshold": self.debt_threshold,
                "high_outstanding_debt": worthiness.get("high_outstanding_debt"),
                "max_debt_institution_count": self.max_debt_institution_count,
                "total_outstanding": total_outstanding_to_float,
                "count_of_open_loans": len(open_loan_institutions),
                "open_loan_institutions": open_loan_institutions,
                "bad_loans_institions_count": len(bad_loans_institions),
                "bad_loans_institions": bad_loans_institions,
            }
            return result

        else:
            result = {
                "status": True,
                "reason": "",
                "debt_threshold": self.debt_threshold,
                "high_outstanding_debt": False,
                "max_debt_institution_count": self.max_debt_institution_count,
                "total_outstanding": 0,
                "count_of_open_loans": 0,
                "open_loan_institutions": [],
                "bad_loans_institions_count": 0,
                "bad_loans_institions": [],
                "user_behaviour": "",
            }
            return result


class CreditDirectCrcCheck:
    def __init__(
        self, bvn, debt_threshold, ajo_user, max_debt_institution_count=3
    ) -> None:
        self.bvn = bvn
        self.ajo_user = ajo_user
        self.debt_threshold = debt_threshold
        self.max_debt_institution_count = max_debt_institution_count

    @staticmethod
    def crc_classic_standard(self):

        url = "https://webserver.creditreferencenigeria.net/JsonLiveRequest/JsonService.svc/CIRRequest/ProcessRequestJson"

        sb2_code = 2
        payload = json.dumps(
            {
                "Request": "{'@REQUEST_ID': '1','REQUEST_PARAMETERS': {'REPORT_PARAMETERS': {'@REPORT_ID': %s,'@SUBJECT_TYPE': '1','@RESPONSE_TYPE': '5'},'INQUIRY_REASON': {'@CODE': '1'},'APPLICATION': {'@PRODUCT': '017','@NUMBER': '232','@AMOUNT': '15000','@CURRENCY': 'NGN'}},'SEARCH_PARAMETERS': {'@SEARCH-TYPE':'4','BVN_NO': %s }}"
                % (f"'{sb2_code}'", f"'{self.bvn}'"),
                "UserName": settings.MAIN_CRC_USERNAME,
                "Password": settings.MAIN_CRC_PASSWORD,
            }
        )

        headers = {"Content-Type": "application/json"}

        try:
            response = requests.request("POST", url, data=payload, headers=headers)
            response.raise_for_status()  # Raise an error for non-200 status codes
            print("Response Status Code:", response.status_code)
            return response.json()
        except requests.exceptions.RequestException as e:
            response = {"error": str(e)}
            return response

    def is_worthy(
        self,
        total_outstanding,
        open_loans,
        bad_loans,
    ) -> dict:

        if total_outstanding > self.debt_threshold:
            is_credit_worthy = False
            high_outstanding_debt = True
            reason = f"Borrower has and outstanding of {total_outstanding}"

        elif open_loans > self.max_debt_institution_count:
            is_credit_worthy = False
            high_outstanding_debt = False
            reason = f"Borrower has {open_loans} loans"

        elif bad_loans >= 2:
            is_credit_worthy = False
            high_outstanding_debt = False
            reason = f"Borrower has {bad_loans} bad loans"

        else:
            is_credit_worthy = True
            high_outstanding_debt = False
            reason = "Approved"

        result = {
            "is_credit_worthy": is_credit_worthy,
            # "credit_decision": credit_decision,
            "high_outstanding_debt": high_outstanding_debt,
            "reason": reason,
        }
        return result

    def get_credit_status(self):
        from loans.models import CreditBureauMetaData

        if settings.ENVIRONMENT == "development":

            result = {
                "status": False,
                "reason": "",
                "debt_threshold": self.debt_threshold,
                "high_outstanding_debt": False,
                "max_debt_institution_count": self.max_debt_institution_count,
                "total_outstanding": 0,
                "count_of_open_loans": 0,
                "open_loan_institutions": [],
                "bad_loans_institions_count": 2,
                "bad_loans_institions": ["Fairmoney", "Lapo microfinance bank"],
            }
            return result

        failed_result = {
            "status": None,
            "reason": "",
            "debt_threshold": self.debt_threshold,
            "high_outstanding_debt": False,
            "max_debt_institution_count": self.max_debt_institution_count,
            "total_outstanding": 0,
            "count_of_open_loans": 0,
            "open_loan_institutions": [],
            "bad_loans_institions_count": 0,
            "bad_loans_institions": [],
        }

        try:
            credit_summary_response = self.crc_classic_standard(bvn=self.bvn)
            credit_summary_list = credit_summary_response["ConsumerHitResponse"][
                "BODY"
            ]["CreditFacilityHistory24"]
        except Exception as err:
            credit_bureau_metadata = CreditBureauMetaData.objects.create(
                ajo_user=self.ajo_user,
                verification_id=self.bvn,
                response=err,
            )
            return failed_result

        credit_bureau_metadata = CreditBureauMetaData.objects.create(
            ajo_user=self.ajo_user,
            verification_id=self.bvn,
            response=credit_summary_response,
        )
        if "ConsumerNoHitResponse" in credit_summary_response:
            response_type = credit_summary_response["ConsumerNoHitResponse"]["HEADER"][
                "RESPONSETYPE"
            ]["CODE"]
            if response_type == "2":
                return failed_result

        if "ConsumerSearchResultResponse" in credit_summary_response:
            response_type = credit_summary_response["ConsumerSearchResultResponse"][
                "HEADER"
            ]["RESPONSETYPE"]["CODE"]
            if response_type == "3":
                return failed_result

        if "ConsumerHitResponse" in credit_summary_response:
            response_type = credit_summary_response["ConsumerHitResponse"]["HEADER"][
                "RESPONSETYPE"
            ]["CODE"]
            if response_type == "1":

                account_summary = credit_summary_response["ConsumerHitResponse"][
                    "BODY"
                ]["SummaryOfPerformance"][0]
                total_account_arrears = account_summary.get("NONPERFORMING_FACILITY")
                over_all_monthly_installment = account_summary.get("ACCOUNT_BALANCE")
                total_monthly_instalment = account_summary.get("ACCOUNT_BALANCE")

                credit_bureau_metadata.no_of_defaulted_loans = total_account_arrears
                credit_bureau_metadata.monthly_repayment_amount = (
                    total_monthly_instalment
                )
                credit_bureau_metadata.save()

                total_outstanding = account_summary.get("ACCOUNT_BALANCE")
                performing_loans_institions: list = []
                good_loans = 0
                bad_loans = 0
                open_loans = 0
                open_loan_institutions: list = []
                bad_loans_institions: list = []
                institution_status = {}
                lost_loan_accs = []

                for credit_summary_by_institution in credit_summary_list:
                    overdue = credit_summary_by_institution.get("CURRENT_BALANCE_CAL")
                    account_status = credit_summary_by_institution.get(
                        "REASON_CODE_VALUE"
                    )
                    account_num = credit_summary_by_institution.get("ACCOUNT_NUMBER")
                    performance_status = credit_summary_by_institution.get(
                        "ASSET_CLASSIFICATION_CAL"
                    )
                    institution_name = credit_summary_by_institution.get(
                        "INSTITUTION_NAME"
                    )

                    date_open = datetime.strptime(
                        credit_summary_by_institution.get("ACC_OPEN_DISB_DT"),
                        "%d/%m/%Y",
                    )
                    credit_summary_by_institution["ACC_OPEN_DISB_DT"] = date_open

                    if performance_status != "Performing":
                        if performance_status == "Lost":
                            if overdue == 0.0 or account_num in lost_loan_accs:
                                continue
                            lost_loan_accs.append(account_num)
                        else:
                            if institution_name not in bad_loans_institions:
                                bad_loans_institions.append(institution_name)

                            latest_date = max(
                                institution_status.get("institution_name")
                            )
                            if latest_date < date_open:
                                print("There is no date later than the current one.")
                                bad_loans += 1

                    elif performance_status == "Performing":
                        if institution_name not in performing_loans_institions:
                            performing_loans_institions.append(institution_name)
                        good_loans += 1

                        if institution_name in institution_status:
                            institution_status[institution_name].append(date_open)
                        else:
                            institution_status[institution_name] = [date_open]

                    elif account_status == "Open":
                        if institution_name not in open_loan_institutions:
                            open_loan_institutions.append(institution_name)
                        open_loans += 1

                credit_summary_list.sort(key=lambda x: x["ACC_OPEN_DISB_DT"])
                performing_at_start = 0
                performing_at_end = 0

                for agreement in credit_summary_list:
                    if agreement["PerformanceStatus"] == "Performing":
                        performing_at_end += 1
                    else:
                        performing_at_start = performing_at_end
                        performing_at_end = 0
                user_behaviour = ""
                if performing_at_end > performing_at_start:
                    user_behaviour = "positive"
                else:
                    user_behaviour = "negative"

                total_outstanding_to_float = self.string_to_float(total_outstanding)

                worthiness = self.is_worthy(
                    total_outstanding=total_outstanding_to_float,
                    open_loans=len(open_loan_institutions),
                    bad_loans=len(bad_loans_institions),
                    performing_loans=len(performing_loans_institions),
                    user_behaviour=user_behaviour,
                )
                result = {
                    "status": worthiness.get("is_credit_worthy"),
                    "reason": worthiness.get("reason"),
                    "debt_threshold": self.debt_threshold,
                    "high_outstanding_debt": worthiness.get("high_outstanding_debt"),
                    "max_debt_institution_count": self.max_debt_institution_count,
                    "total_outstanding": total_outstanding_to_float,
                    "count_of_open_loans": len(open_loan_institutions),
                    "open_loan_institutions": open_loan_institutions,
                    "bad_loans_institions_count": len(bad_loans_institions),
                    "bad_loans_institions": bad_loans_institions,
                }
                return result

            else:
                result = {
                    "status": True,
                    "reason": "",
                    "debt_threshold": self.debt_threshold,
                    "high_outstanding_debt": False,
                    "max_debt_institution_count": self.max_debt_institution_count,
                    "total_outstanding": 0,
                    "count_of_open_loans": 0,
                    "open_loan_institutions": [],
                    "bad_loans_institions_count": 0,
                    "bad_loans_institions": [],
                    "user_behaviour": "",
                }
                return result
