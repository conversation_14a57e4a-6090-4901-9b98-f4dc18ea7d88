[{"SubjectList": [{"ConsumerID": 2848144, "SearchOutput": "TAIWO, ADUNI, , 7 Bakari Street Ikotun Lagos State", "Reference": 2848144}]}, {"PersonalDetailsSummary": [{"Header": "PERSONAL DETAILS SUMMARY: TAIWO ADUNI ", "ConsumerID": 2848144, "ReferenceNo": "None", "Nationality": "Nigeria", "NationalIDNo": "KN008193847", "PassportNo": "*********", "DriversLicenseNo": "None", "BankVerificationNo": "***********", "PencomIDNo": "", "otheridNo": "", "BirthDate": "12/04/1981", "Dependants": 0, "Gender": "Female", "MaritalStatus": "None", "ResidentialAddress1": "7 Bakari Street Ikotun Lagos State", "ResidentialAddress2": "", "ResidentialAddress3": "IKOTUN", "ResidentialAddress4": "Lagos Nigeria", "PostalAddress1": "7 Bakare Street Cele Egbe", "PostalAddress2": "", "PostalAddress3": "Lagos", "PostalAddress4": "Lagos ", "HomeTelephoneNo": "***********", "WorkTelephoneNo": "***********", "CellularNo": "***********", "EmailAddress": "", "EmployerDetail": "SELF EMPLOYED", "PropertyOwnedType": "", "Surname": "TAIWO", "FirstName": "ADUNI", "OtherNames": ""}]}, {"DeliquencyInformation": [{"SubscriberName": "MyCredit Invest", "AccountNo": "********", "PeriodNum": ********, "MonthsinArrears": 285}]}, {"CreditAccountSummary": [{"TotalMonthlyInstalment": "171,292.00", "TotalOutstandingdebt": "199,212.00", "TotalAccountarrear": "2", "Amountarrear": "178,150.00", "TotalAccounts": "13", "TotalAccounts1": "0", "TotalaccountinGodcondition": "11", "TotalaccountinGoodcondition": "11", "TotalNumberofJudgement": "0", "TotalJudgementAmount": "0", "LastJudgementDate": "-", "TotalNumberofDishonoured": "0", "TotalDishonouredAmount": "0.00", "LastBouncedChequesDate": "None", "TotalMonthlyInstalment1": "0.00", "TotalOutstandingdebt1": "0.00", "TotalAccountarrear1": "0", "Amountarrear1": "0.00", "TotalaccountinGodcondition1": "2", "TotalaccountinBadcondition": "2", "TotalNumberofJudgement1": "0", "TotalJudgementAmount1": "0", "LastJudgementDate1": "-", "TotalNumberofDishonoured1": "0", "TotalDishonouredAmount1": "0.00", "LastBouncedChequesDate1": "None", "Rating": "285"}]}, {"CreditAccountRating": [{"NoOfHomeLoanAccountsGood": 0, "NoOfHomeLoanAccountsBad": 0, "NoOfAutoLoanccountsGood": 0, "NoOfAutoLoanAccountsBad": 0, "NoOfStudyLoanAccountsGood": 0, "NoOfStudyLoanAccountsBad": 0, "NoOfPersonalLoanAccountsGood": 0, "NoOfPersonalLoanAccountsBad": 0, "NoOfCreditCardAccountsGood": 0, "NoOfCreditCardAccountsBad": 0, "NoOfRetailAccountsGood": 4, "NoOfRetailAccountsBad": 2, "NoOfJointLoanAccountsGood": 0, "NoOfJointLoanAccountsBad": 0, "NoOfTelecomAccountsGood": 0, "NoOfTelecomAccountsBad": 0, "NoOfOtherAccountsGood": 5, "NoOfOtherAccountsBad": 0}]}, {"CreditAgreementSummary": [{"DateAccountOpened": "28/05/2022", "SubscriberName": "AB Microfinance Bank Limited Lagos", "AccountNo": "001012011003479911", "SubAccountNo": "", "IndicatorDescription": "None", "OpeningBalanceAmt": "120,000.00", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "CurrentBalanceAmt": "0.00", "InstalmentAmount": "24,835.03", "AmountOverdue": "0.00", "ClosedDate": "05/12/2022", "LoanDuration": 6, "RepaymentFrequency": "Mo", "LastUpdatedDate": "22/03/2024", "PerformanceStatus": "Performing", "AccountStatus": "Closed"}, {"DateAccountOpened": "28/09/2022", "SubscriberName": "AB Microfinance Bank Limited Lagos", "AccountNo": "001012011003562264", "SubAccountNo": "", "IndicatorDescription": "None", "OpeningBalanceAmt": "200,000.00", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "CurrentBalanceAmt": "0.00", "InstalmentAmount": "35,988.74", "AmountOverdue": "0.00", "ClosedDate": "03/05/2023", "LoanDuration": 7, "RepaymentFrequency": "Mo", "LastUpdatedDate": "22/03/2024", "PerformanceStatus": "Performing", "AccountStatus": "Closed"}, {"DateAccountOpened": "07/05/2023", "SubscriberName": "MyCredit Invest", "AccountNo": "********", "SubAccountNo": "", "IndicatorDescription": "Personal fixed term loan", "OpeningBalanceAmt": "10,000.00", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "CurrentBalanceAmt": "13,150.00", "InstalmentAmount": "12,150.00", "AmountOverdue": "13,150.00", "ClosedDate": "22/05/2023", "LoanDuration": 15, "RepaymentFrequency": "Mo", "LastUpdatedDate": "20/03/2024", "PerformanceStatus": "Lost", "AccountStatus": "Open"}, {"DateAccountOpened": "02/09/2022", "SubscriberName": "Advans Lafayette Lagos", "AccountNo": "401-0012036", "SubAccountNo": "", "IndicatorDescription": "None", "OpeningBalanceAmt": "93,785.34", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "CurrentBalanceAmt": "0.00", "InstalmentAmount": "None", "AmountOverdue": "0.00", "ClosedDate": "02/03/2023", "LoanDuration": 6, "RepaymentFrequency": "Mo", "LastUpdatedDate": "12/03/2024", "PerformanceStatus": "Performing", "AccountStatus": "Open"}, {"DateAccountOpened": "04/08/2023", "SubscriberName": "NewEdge Finance", "AccountNo": "********", "SubAccountNo": "", "IndicatorDescription": "Personal fixed term loan", "OpeningBalanceAmt": "44,700.00", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "CurrentBalanceAmt": "0.00", "InstalmentAmount": "None", "AmountOverdue": "0.00", "ClosedDate": "04/02/2024", "LoanDuration": 180, "RepaymentFrequency": "Mo", "LastUpdatedDate": "25/03/2024", "PerformanceStatus": "Performing", "AccountStatus": "Closed"}, {"DateAccountOpened": "07/07/2022", "SubscriberName": "MyCredit Invest", "AccountNo": "7273486", "SubAccountNo": "", "IndicatorDescription": "Personal fixed term loan", "OpeningBalanceAmt": "5,000.00", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "CurrentBalanceAmt": "0.00", "InstalmentAmount": "5,750.00", "AmountOverdue": "0.00", "ClosedDate": "22/07/2022", "LoanDuration": 15, "RepaymentFrequency": "Mo", "LastUpdatedDate": "22/10/2022", "PerformanceStatus": "Performing", "AccountStatus": "Closed"}, {"DateAccountOpened": "13/07/2022", "SubscriberName": "MyCredit Invest", "AccountNo": "7331500", "SubAccountNo": "", "IndicatorDescription": "Personal fixed term loan", "OpeningBalanceAmt": "5,000.00", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "CurrentBalanceAmt": "0.00", "InstalmentAmount": "6,000.00", "AmountOverdue": "0.00", "ClosedDate": "28/07/2022", "LoanDuration": 15, "RepaymentFrequency": "Mo", "LastUpdatedDate": "28/01/2023", "PerformanceStatus": "Performing", "AccountStatus": "Closed"}, {"DateAccountOpened": "04/09/2022", "SubscriberName": "MyCredit Invest", "AccountNo": "7872343", "SubAccountNo": "", "IndicatorDescription": "Personal fixed term loan", "OpeningBalanceAmt": "5,000.00", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "CurrentBalanceAmt": "0.00", "InstalmentAmount": "6,500.00", "AmountOverdue": "0.00", "ClosedDate": "04/10/2022", "LoanDuration": 30, "RepaymentFrequency": "Mo", "LastUpdatedDate": "03/08/2023", "PerformanceStatus": "Performing", "AccountStatus": "Closed"}, {"DateAccountOpened": "06/10/2017", "SubscriberName": "Baobab Microfinance Bank Limited", "AccountNo": "LD1727980060", "SubAccountNo": "", "IndicatorDescription": "None", "OpeningBalanceAmt": "83,901.41", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "CurrentBalanceAmt": "0.00", "InstalmentAmount": "0.00", "AmountOverdue": "0.00", "ClosedDate": "06/04/2018", "LoanDuration": "None", "RepaymentFrequency": "Monthly", "LastUpdatedDate": "23/10/2018", "PerformanceStatus": "Performing", "AccountStatus": "Closed"}, {"DateAccountOpened": "13/09/2019", "SubscriberName": "Baobab Microfinance Bank Limited", "AccountNo": "LD1925620284", "SubAccountNo": "", "IndicatorDescription": "None", "OpeningBalanceAmt": "61,570.04", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "CurrentBalanceAmt": "0.00", "InstalmentAmount": "0.00", "AmountOverdue": "0.00", "ClosedDate": "20/02/2020", "LoanDuration": 115, "RepaymentFrequency": "Monthly", "LastUpdatedDate": "28/04/2020", "PerformanceStatus": "Performing", "AccountStatus": "Closed"}, {"DateAccountOpened": "20/02/2020", "SubscriberName": "Baobab Microfinance Bank Limited", "AccountNo": "LD2005186034", "SubAccountNo": "", "IndicatorDescription": "Commercial investment loan", "OpeningBalanceAmt": "82,093.38", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "CurrentBalanceAmt": "15,072.74", "InstalmentAmount": "16,203.20", "AmountOverdue": "0.00", "ClosedDate": "20/01/2021", "LoanDuration": 239, "RepaymentFrequency": "Mo", "LastUpdatedDate": "18/02/2021", "PerformanceStatus": "Performing", "AccountStatus": "Open"}, {"DateAccountOpened": "29/06/2021", "SubscriberName": "Baobab Microfinance Bank Limited", "AccountNo": "LD2118003027", "SubAccountNo": "", "IndicatorDescription": "Commercial investment loan", "OpeningBalanceAmt": "103,589.74", "Currency": "NGN", "CurrentBalanceDebitInd": "D", "CurrentBalanceAmt": "20,988.84", "InstalmentAmount": "22,615.48", "AmountOverdue": "0.00", "ClosedDate": "03/01/2022", "LoanDuration": 138, "RepaymentFrequency": "Mo", "LastUpdatedDate": "28/01/2022", "PerformanceStatus": "Performing", "AccountStatus": "Open"}, {"DateAccountOpened": "21/07/2023", "SubscriberName": "Zitra Investments", "AccountNo": "LD4849", "SubAccountNo": "", "IndicatorDescription": "Personal fixed term loan", "OpeningBalanceAmt": "300,000.00", "Currency": "NGN", "CurrentBalanceDebitInd": "D", "CurrentBalanceAmt": "150,000.00", "InstalmentAmount": "41,250.00", "AmountOverdue": "165,000.00", "ClosedDate": "04/10/2023", "LoanDuration": 2, "RepaymentFrequency": "We", "LastUpdatedDate": "28/02/2024", "PerformanceStatus": "Lost", "AccountStatus": "Open"}]}, {"AccountMonthlyPaymentHeader": [{"TableName": "Consumer24MonthlyPaymentHeader", "DisplayText": "Consumer 24 Monthly Payment Header", "Company": "Company", "MH24": "2022\nMAY", "MH23": "2022\nJUN", "MH22": "2022\nJUL", "MH21": "2022\nAUG", "MH20": "2022\nSEP", "MH19": "2022\nOCT", "MH18": "2022\nNOV", "MH17": "2022\nDEC", "MH16": "2023\nJAN", "MH15": "2023\nFEB", "MH14": "2023\nMAR", "MH13": "2023\nAPR", "MH12": "2023\nMAY", "MH11": "2023\nJUN", "MH10": "2023\nJUL", "MH09": "2023\nAUG", "MH08": "2023\nSEP", "MH07": "2023\nOCT", "MH06": "2023\nNOV", "MH05": "2023\nDEC", "MH04": "2024\nJAN", "MH03": "2024\nFEB", "MH02": "2024\nMAR", "MH01": "2024\nAPR"}]}, {"AccountMonthlyPaymentHistory": [{"Header": "Details of Credit Agreement with \"AB Microfinance Bank Limited Lagos\" for Account Number: 001012011003479911", "TableName": "Consumer24MonthlyPayment", "DisplayText": "Consumer 24 Monthly Payment", "DateAccountOpened": "28/05/2022", "SubscriberName": "AB Microfinance Bank Limited Lagos", "AccountNo": "001012011003479911", "SubAccountNo": "", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "IndicatorDescription": "None", "OpeningBalanceAmt": "120,000.00", "CurrentBalanceAmt": "0.00", "MonthlyInstalmentAmt": "24,835.03", "AmountOverdue": "0.00", "ClosedDate": "05/12/2022", "LastUpdatedDate": "22/03/2024", "LastPaymentDate": "27/09/2022", "PerformanceStatus": "Performing", "SubscriberTypeInd": "M", "AccountNote": "", "LoanDuration": "6 Day(s)", "RepaymentFrequencyCode": "Mo", "AccountStatus": "Closed", "M24": "0", "M23": "0", "M22": "#", "M21": "0", "M20": "0", "M19": "0", "M18": "0", "M17": "0", "M16": "0", "M15": "#", "M14": "0", "M13": "0", "M12": "0", "M11": "0", "M10": "0", "M09": "0", "M08": "0", "M07": "0", "M06": "0", "M05": "0", "M04": "#", "M03": "0", "M02": "#", "M01": "#"}, {"Header": "Details of Credit Agreement with \"AB Microfinance Bank Limited Lagos\" for Account Number: 001012011003562264", "TableName": "Consumer24MonthlyPayment", "DisplayText": "Consumer 24 Monthly Payment", "DateAccountOpened": "28/09/2022", "SubscriberName": "AB Microfinance Bank Limited Lagos", "AccountNo": "001012011003562264", "SubAccountNo": "", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "IndicatorDescription": "None", "OpeningBalanceAmt": "200,000.00", "CurrentBalanceAmt": "0.00", "MonthlyInstalmentAmt": "35,988.74", "AmountOverdue": "0.00", "ClosedDate": "03/05/2023", "LastUpdatedDate": "22/03/2024", "LastPaymentDate": "27/02/2024", "PerformanceStatus": "Performing", "SubscriberTypeInd": "M", "AccountNote": "", "LoanDuration": "7 Day(s)", "RepaymentFrequencyCode": "Mo", "AccountStatus": "Closed", "M24": "#", "M23": "#", "M22": "#", "M21": "#", "M20": "0", "M19": "0", "M18": "3", "M17": "0", "M16": "0", "M15": "#", "M14": "57", "M13": "59", "M12": "90", "M11": "123", "M10": "120", "M09": "151", "M08": "18100", "M07": "182", "M06": "212", "M05": "243", "M04": "#", "M03": "0", "M02": "#", "M01": "#"}, {"Header": "Details of Credit Agreement with \"Advans Lafayette Lagos\" for Account Number: 401-0012036", "TableName": "Consumer24MonthlyPayment", "DisplayText": "Consumer 24 Monthly Payment", "DateAccountOpened": "02/09/2022", "SubscriberName": "Advans Lafayette Lagos", "AccountNo": "401-0012036", "SubAccountNo": "", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "IndicatorDescription": "None", "OpeningBalanceAmt": "93,785.34", "CurrentBalanceAmt": "0.00", "MonthlyInstalmentAmt": "None", "AmountOverdue": "0.00", "ClosedDate": "02/03/2023", "LastUpdatedDate": "12/03/2024", "LastPaymentDate": "31/07/2023", "PerformanceStatus": "Performing", "SubscriberTypeInd": "M", "AccountNote": "", "LoanDuration": "6 Day(s)", "RepaymentFrequencyCode": "Mo", "AccountStatus": "Open", "M24": "#", "M23": "#", "M22": "#", "M21": "#", "M20": "0", "M19": "0", "M18": "0", "M17": "0", "M16": "0", "M15": "0", "M14": "58", "M13": "60", "M12": "91", "M11": "121", "M10": "0", "M09": "#", "M08": "0", "M07": "0", "M06": "0", "M05": "#", "M04": "0", "M03": "0", "M02": "#", "M01": "#"}, {"Header": "Details of Credit Agreement with \"Baobab Microfinance Bank Limited\" for Account Number: LD1727980060", "TableName": "Consumer24MonthlyPayment", "DisplayText": "Consumer 24 Monthly Payment", "DateAccountOpened": "06/10/2017", "SubscriberName": "Baobab Microfinance Bank Limited", "AccountNo": "LD1727980060", "SubAccountNo": "", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "IndicatorDescription": "None", "OpeningBalanceAmt": "83,901.41", "CurrentBalanceAmt": "0.00", "MonthlyInstalmentAmt": "0.00", "AmountOverdue": "0.00", "ClosedDate": "06/04/2018", "LastUpdatedDate": "23/10/2018", "LastPaymentDate": "None", "PerformanceStatus": "Performing", "SubscriberTypeInd": "M", "AccountNote": "", "LoanDuration": "Not Available", "RepaymentFrequencyCode": "Monthly", "AccountStatus": "Closed", "M24": "#", "M23": "#", "M22": "#", "M21": "#", "M20": "#", "M19": "#", "M18": "#", "M17": "#", "M16": "#", "M15": "#", "M14": "#", "M13": "#", "M12": "#", "M11": "#", "M10": "#", "M09": "#", "M08": "#", "M07": "#", "M06": "#", "M05": "#", "M04": "#", "M03": "#", "M02": "#", "M01": "#"}, {"Header": "Details of Credit Agreement with \"Baobab Microfinance Bank Limited\" for Account Number: LD1925620284", "TableName": "Consumer24MonthlyPayment", "DisplayText": "Consumer 24 Monthly Payment", "DateAccountOpened": "13/09/2019", "SubscriberName": "Baobab Microfinance Bank Limited", "AccountNo": "LD1925620284", "SubAccountNo": "", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "IndicatorDescription": "None", "OpeningBalanceAmt": "61,570.04", "CurrentBalanceAmt": "0.00", "MonthlyInstalmentAmt": "0.00", "AmountOverdue": "0.00", "ClosedDate": "20/02/2020", "LastUpdatedDate": "28/04/2020", "LastPaymentDate": "None", "PerformanceStatus": "Performing", "SubscriberTypeInd": "M", "AccountNote": "", "LoanDuration": "115 Day(s)", "RepaymentFrequencyCode": "Monthly", "AccountStatus": "Closed", "M24": "#", "M23": "#", "M22": "#", "M21": "#", "M20": "#", "M19": "#", "M18": "#", "M17": "#", "M16": "#", "M15": "#", "M14": "#", "M13": "#", "M12": "#", "M11": "#", "M10": "#", "M09": "#", "M08": "#", "M07": "#", "M06": "#", "M05": "#", "M04": "#", "M03": "#", "M02": "#", "M01": "#"}, {"Header": "Details of Credit Agreement with \"Baobab Microfinance Bank Limited\" for Account Number: LD2005186034", "TableName": "Consumer24MonthlyPayment", "DisplayText": "Consumer 24 Monthly Payment", "DateAccountOpened": "20/02/2020", "SubscriberName": "Baobab Microfinance Bank Limited", "AccountNo": "LD2005186034", "SubAccountNo": "", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "IndicatorDescription": "Commercial investment loan", "OpeningBalanceAmt": "82,093.38", "CurrentBalanceAmt": "15,072.74", "MonthlyInstalmentAmt": "16,203.20", "AmountOverdue": "0.00", "ClosedDate": "20/01/2021", "LastUpdatedDate": "18/02/2021", "LastPaymentDate": "20/03/2020", "PerformanceStatus": "Performing", "SubscriberTypeInd": "M", "AccountNote": "", "LoanDuration": "239 Day(s)", "RepaymentFrequencyCode": "Mo", "AccountStatus": "Open", "M24": "#", "M23": "#", "M22": "#", "M21": "#", "M20": "#", "M19": "#", "M18": "#", "M17": "#", "M16": "#", "M15": "#", "M14": "#", "M13": "#", "M12": "#", "M11": "#", "M10": "#", "M09": "#", "M08": "#", "M07": "#", "M06": "#", "M05": "#", "M04": "#", "M03": "#", "M02": "#", "M01": "#"}, {"Header": "Details of Credit Agreement with \"Baobab Microfinance Bank Limited\" for Account Number: LD2118003027", "TableName": "Consumer24MonthlyPayment", "DisplayText": "Consumer 24 Monthly Payment", "DateAccountOpened": "29/06/2021", "SubscriberName": "Baobab Microfinance Bank Limited", "AccountNo": "LD2118003027", "SubAccountNo": "", "Currency": "NGN", "CurrentBalanceDebitInd": "D", "IndicatorDescription": "Commercial investment loan", "OpeningBalanceAmt": "103,589.74", "CurrentBalanceAmt": "20,988.84", "MonthlyInstalmentAmt": "22,615.48", "AmountOverdue": "0.00", "ClosedDate": "03/01/2022", "LastUpdatedDate": "28/01/2022", "LastPaymentDate": "03/12/2021", "PerformanceStatus": "Performing", "SubscriberTypeInd": "M", "AccountNote": "", "LoanDuration": "138 Day(s)", "RepaymentFrequencyCode": "Mo", "AccountStatus": "Open", "M24": "#", "M23": "#", "M22": "#", "M21": "#", "M20": "#", "M19": "#", "M18": "#", "M17": "#", "M16": "#", "M15": "#", "M14": "#", "M13": "#", "M12": "#", "M11": "#", "M10": "#", "M09": "#", "M08": "#", "M07": "#", "M06": "#", "M05": "#", "M04": "#", "M03": "#", "M02": "#", "M01": "#"}, {"Header": "Details of Credit Agreement with \"MyCredit Invest\" for Account Number: ********", "TableName": "Consumer24MonthlyPayment", "DisplayText": "Consumer 24 Monthly Payment", "DateAccountOpened": "07/05/2023", "SubscriberName": "MyCredit Invest", "AccountNo": "********", "SubAccountNo": "", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "IndicatorDescription": "Personal fixed term loan", "OpeningBalanceAmt": "10,000.00", "CurrentBalanceAmt": "13,150.00", "MonthlyInstalmentAmt": "12,150.00", "AmountOverdue": "13,150.00", "ClosedDate": "22/05/2023", "LastUpdatedDate": "20/03/2024", "LastPaymentDate": "None", "PerformanceStatus": "Lost", "SubscriberTypeInd": "O", "AccountNote": "", "LoanDuration": "15 Day(s)", "RepaymentFrequencyCode": "Mo", "AccountStatus": "Open", "M24": "#", "M23": "#", "M22": "#", "M21": "#", "M20": "#", "M19": "#", "M18": "#", "M17": "#", "M16": "#", "M15": "#", "M14": "#", "M13": "#", "M12": "15", "M11": "43", "M10": "70", "M09": "#", "M08": "133", "M07": "165", "M06": "196", "M05": "225", "M04": "0", "M03": "285", "M02": "#", "M01": "#"}, {"Header": "Details of Credit Agreement with \"MyCredit Invest\" for Account Number: 7273486", "TableName": "Consumer24MonthlyPayment", "DisplayText": "Consumer 24 Monthly Payment", "DateAccountOpened": "07/07/2022", "SubscriberName": "MyCredit Invest", "AccountNo": "7273486", "SubAccountNo": "", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "IndicatorDescription": "Personal fixed term loan", "OpeningBalanceAmt": "5,000.00", "CurrentBalanceAmt": "0.00", "MonthlyInstalmentAmt": "5,750.00", "AmountOverdue": "0.00", "ClosedDate": "22/07/2022", "LastUpdatedDate": "22/10/2022", "LastPaymentDate": "13/07/2022", "PerformanceStatus": "Performing", "SubscriberTypeInd": "O", "AccountNote": "", "LoanDuration": "15 Day(s)", "RepaymentFrequencyCode": "Mo", "AccountStatus": "Closed", "M24": "#", "M23": "#", "M22": "0", "M21": "#", "M20": "0", "M19": "#", "M18": "#", "M17": "#", "M16": "#", "M15": "#", "M14": "#", "M13": "#", "M12": "#", "M11": "#", "M10": "#", "M09": "#", "M08": "#", "M07": "#", "M06": "#", "M05": "#", "M04": "#", "M03": "#", "M02": "#", "M01": "#"}, {"Header": "Details of Credit Agreement with \"MyCredit Invest\" for Account Number: 7331500", "TableName": "Consumer24MonthlyPayment", "DisplayText": "Consumer 24 Monthly Payment", "DateAccountOpened": "13/07/2022", "SubscriberName": "MyCredit Invest", "AccountNo": "7331500", "SubAccountNo": "", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "IndicatorDescription": "Personal fixed term loan", "OpeningBalanceAmt": "5,000.00", "CurrentBalanceAmt": "0.00", "MonthlyInstalmentAmt": "6,000.00", "AmountOverdue": "0.00", "ClosedDate": "28/07/2022", "LastUpdatedDate": "28/01/2023", "LastPaymentDate": "01/09/2022", "PerformanceStatus": "Performing", "SubscriberTypeInd": "O", "AccountNote": "", "LoanDuration": "15 Day(s)", "RepaymentFrequencyCode": "Mo", "AccountStatus": "Closed", "M24": "#", "M23": "#", "M22": "6", "M21": "#", "M20": "0", "M19": "#", "M18": "0", "M17": "0", "M16": "#", "M15": "#", "M14": "#", "M13": "#", "M12": "#", "M11": "#", "M10": "#", "M09": "#", "M08": "#", "M07": "#", "M06": "#", "M05": "#", "M04": "#", "M03": "#", "M02": "#", "M01": "#"}, {"Header": "Details of Credit Agreement with \"MyCredit Invest\" for Account Number: 7872343", "TableName": "Consumer24MonthlyPayment", "DisplayText": "Consumer 24 Monthly Payment", "DateAccountOpened": "04/09/2022", "SubscriberName": "MyCredit Invest", "AccountNo": "7872343", "SubAccountNo": "", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "IndicatorDescription": "Personal fixed term loan", "OpeningBalanceAmt": "5,000.00", "CurrentBalanceAmt": "0.00", "MonthlyInstalmentAmt": "6,500.00", "AmountOverdue": "0.00", "ClosedDate": "04/10/2022", "LastUpdatedDate": "03/08/2023", "LastPaymentDate": "25/03/2023", "PerformanceStatus": "Performing", "SubscriberTypeInd": "O", "AccountNote": "", "LoanDuration": "30 Day(s)", "RepaymentFrequencyCode": "Mo", "AccountStatus": "Closed", "M24": "#", "M23": "#", "M22": "#", "M21": "#", "M20": "1", "M19": "#", "M18": "59", "M17": "92", "M16": "129", "M15": "151", "M14": "0", "M13": "0", "M12": "0", "M11": "#", "M10": "0", "M09": "#", "M08": "#", "M07": "#", "M06": "#", "M05": "#", "M04": "#", "M03": "#", "M02": "#", "M01": "#"}, {"Header": "Details of Credit Agreement with \"NewEdge Finance\" for Account Number: ********", "TableName": "Consumer24MonthlyPayment", "DisplayText": "Consumer 24 Monthly Payment", "DateAccountOpened": "04/08/2023", "SubscriberName": "NewEdge Finance", "AccountNo": "********", "SubAccountNo": "", "Currency": "NGN", "CurrentBalanceDebitInd": "None", "IndicatorDescription": "Personal fixed term loan", "OpeningBalanceAmt": "44,700.00", "CurrentBalanceAmt": "0.00", "MonthlyInstalmentAmt": "None", "AmountOverdue": "0.00", "ClosedDate": "04/02/2024", "LastUpdatedDate": "25/03/2024", "LastPaymentDate": "09/02/2024", "PerformanceStatus": "Performing", "SubscriberTypeInd": "F", "AccountNote": "", "LoanDuration": "180 Day(s)", "RepaymentFrequencyCode": "Mo", "AccountStatus": "Closed", "M24": "#", "M23": "#", "M22": "#", "M21": "#", "M20": "#", "M19": "#", "M18": "#", "M17": "#", "M16": "#", "M15": "#", "M14": "#", "M13": "#", "M12": "#", "M11": "#", "M10": "0", "M09": "1", "M08": "1", "M07": "1", "M06": "1", "M05": "#", "M04": "0", "M03": "0", "M02": "#", "M01": "#"}, {"Header": "Details of Credit Agreement with \"Zitra Investments\" for Account Number: LD4849", "TableName": "Consumer24MonthlyPayment", "DisplayText": "Consumer 24 Monthly Payment", "DateAccountOpened": "21/07/2023", "SubscriberName": "Zitra Investments", "AccountNo": "LD4849", "SubAccountNo": "", "Currency": "NGN", "CurrentBalanceDebitInd": "D", "IndicatorDescription": "Personal fixed term loan", "OpeningBalanceAmt": "300,000.00", "CurrentBalanceAmt": "150,000.00", "MonthlyInstalmentAmt": "41,250.00", "AmountOverdue": "165,000.00", "ClosedDate": "04/10/2023", "LastUpdatedDate": "28/02/2024", "LastPaymentDate": "25/08/2023", "PerformanceStatus": "Lost", "SubscriberTypeInd": "O", "AccountNote": "", "LoanDuration": "2 Day(s)", "RepaymentFrequencyCode": "We", "AccountStatus": "Open", "M24": "#", "M23": "#", "M22": "#", "M21": "#", "M20": "#", "M19": "#", "M18": "#", "M17": "#", "M16": "#", "M15": "#", "M14": "#", "M13": "#", "M12": "#", "M11": "#", "M10": "14", "M09": "45", "M08": "61", "M07": "#", "M06": "#", "M05": "131", "M04": "171", "M03": "#", "M02": "#", "M01": "#"}]}, {"GuarantorCount": [{"GuarantorsSecured": 0, "Accounts": 0}]}, {"GuarantorDetails": [{"GuarantorFirstName": "<PERSON><PERSON><PERSON><PERSON>", "GuarantorOtherName": "", "GuarantorNationalIDNo": "None", "GuarantorPassport": "None", "GuarantorDriverLicenceNo": "None", "GuarantorPENCOMIDNo": "None", "GuarantorOtherID": "None", "GuarantorGender": "None", "GuarantorDateOfBirth": "Jan  1 1900 12:00AM", "GuarantorAddress1": "None", "GuarantorAddress2": "None", "GuarantorAddress3": "None", "GuarantorHomeTelephone": "***********", "GuarantorworkTelephone": "***********", "GuarantorMobileTelephone": "***********"}]}, {"EnquiryHistoryTop": []}, {"IdentificationHistory": [{"UpDateDate": "None", "UpDateOnDate": "None", "IdentificationNumber": "KN008193847", "IdentificationType": "National ID Number"}]}, {"AddressHistory": [{"UpDateDate": "30/04/2018", "UpDateOnDate": "30/04/2018", "Address1": "4 BAKARE STRT", "Address2": "None", "Address3": "<PERSON><PERSON><PERSON>", "Address4": "Lagos", "AddressTypeInd": "Residential"}, {"UpDateDate": "29/11/2022", "UpDateOnDate": "29/11/2022", "Address1": "7 BAKARE STREET AGODO IKOTUN LAGOS", "Address2": "None", "Address3": "None", "Address4": "None", "AddressTypeInd": "Residential"}, {"UpDateDate": "28/04/2020", "UpDateOnDate": "28/04/2020", "Address1": "4 BAKARE STRT", "Address2": "None", "Address3": "None", "Address4": "None", "AddressTypeInd": "Residential"}, {"UpDateDate": "28/02/2024", "UpDateOnDate": "28/02/2024", "Address1": "7 Bakari Street Ikotun Lagos State", "Address2": "None", "Address3": "IKOTUN", "Address4": "Lagos", "AddressTypeInd": "Residential"}, {"UpDateDate": "27/12/2019", "UpDateOnDate": "27/12/2019", "Address1": "4 BAKARE STRT", "Address2": "None", "Address3": "None", "Address4": "Lagos", "AddressTypeInd": "Residential"}, {"UpDateDate": "28/04/2020", "UpDateOnDate": "28/04/2020", "Address1": "TINUBU KAKAWA T", "Address2": "None", "Address3": "None", "Address4": "Lagos", "AddressTypeInd": "Postal"}, {"UpDateDate": "27/12/2019", "UpDateOnDate": "27/12/2019", "Address1": "TINUBU KAKAWA T", "Address2": "None", "Address3": "None", "Address4": "None", "AddressTypeInd": "Postal"}, {"UpDateDate": "05/10/2022", "UpDateOnDate": "05/10/2022", "Address1": "7 Bakare Street Cele Egbe", "Address2": "None", "Address3": "Lagos", "Address4": "Lagos", "AddressTypeInd": "Postal"}, {"UpDateDate": "01/12/2023", "UpDateOnDate": "01/12/2023", "Address1": "7 Bakare Street Cele Egbe", "Address2": "None", "Address3": "Lagos", "Address4": "Lagos", "AddressTypeInd": "Postal"}]}, {"EmploymentHistory": [{"UpDateDate": "28/01/2022", "UpDateOnDate": "28/01/2022", "EmployerDetail": "SELF EMPLOYED", "Occupation": "Secondary"}]}, {"TelephoneHistory": [{"HomeNoUpdatedonDate": "28/02/2024", "HomeTelephoneNumber": "***********", "WorkNoUpdatedonDate": "28/01/2022", "WorkTelephoneNumber": "***********", "MobileNoUpdatedonDate": "25/03/2024", "MobileTelephoneNumber": "***********"}, {"HomeNoUpdatedonDate": "None", "HomeTelephoneNumber": "None", "WorkNoUpdatedonDate": "None", "WorkTelephoneNumber": "None", "MobileNoUpdatedonDate": "22/03/2024", "MobileTelephoneNumber": "2348133036250"}]}, {"EnquiryDetails": [{"SubscriberEnquiryResultID": "None", "ProductID": "None", "MatchingRate": "None", "SubscriberEnquiryEngineID": "None"}]}]