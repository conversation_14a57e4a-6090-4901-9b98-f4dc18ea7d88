import ast
import base64
import binascii
import hashlib
import json
import logging
import urllib
from datetime import datetime
from itertools import islice
from math import floor

import pycountry
import requests
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from rest_framework import status
from rest_framework.response import Response

from ajo.model_choices import AccountFormType
from ajo.models import AjoUserWithdrawalAccount, BankAccountDetails
from ajo.selectors import AjoUserSelector, BankAccountSelector
from loans.enums import LoanStatus
from loans.models import AjoLoan, AjoLoanSchedule, BorrowerInfo, CRCInformationUpload

# secret_key = settings.FCCB_SECRET_KEY
# secret_key = " "
# key = hashlib.sha256(secret_key.encode()).digest()
# iv = b'\x00' * 16

# class MockRequest:
#     def __init__(self, data):
#         self.data = data


def date_format(date):
    date_obj = datetime.strptime(date, "%Y-%m-%d")
    formatted_date = date_obj.strftime("%Y/%m/%d")
    return formatted_date


def customerid_format(id):
    object_id_string = str(id)
    formatted_id = object_id_string.zfill(9)
    return formatted_id


def chunked_iterable(iterable, size):
    it = iter(iterable)
    return iter(lambda: tuple(islice(it, size)), ())


def upload_information(loan_list, url, headers, upload_type):

    for chunk in chunked_iterable(loan_list, 100):
        data = {"payload": chunk, "userid": settings.CRC_USER_ID}

        url_encoded_data = urllib.parse.urlencode(
            {"payload": json.dumps(data["payload"]), "userid": data["userid"]}
        )

        response = requests.post(url=url, headers=headers, data=url_encoded_data)
        response_data = response.json()

        upload = CRCInformationUpload.objects.create(
            upload_type=upload_type,
            request_data=data["payload"],
            response_data=response_data,
        )

    return {"status": "success", "message": "All loans uploaded"}


def get_days_past_due(loan):
    if loan.outstanding_days_today >= 0 and loan.days_after_end >= 0:
        days_past_due = loan.outstanding_days_today + loan.days_after_end
    elif loan.outstanding_days_today >= 0:
        days_past_due = loan.outstanding_days_today
    elif loan.days_after_end >= 0:
        days_past_due = loan.days_after_end
    else:
        days_past_due = 0

    if days_past_due <= 30:
        crc_equivalent = "Performing"
    elif days_past_due <= 90:
        crc_equivalent = "Watchlist"
    elif days_past_due <= 180:
        crc_equivalent = "Sub standard"
    elif days_past_due <= 360:
        crc_equivalent = "doubtful"
    else:
        crc_equivalent = "lost"

    return crc_equivalent


# def validate_borrower_dict(borrower_dict):
#     keys_to_validate = [
#         "CustomerID", "Surname", "Firstname", "DateofBirth", "BVNNo",
#         "Gender", "Nationality", "PrimaryAddressLine1", "Primarycity/LGA",
#         "PrimaryState"
#     ]
#     for key, value in borrower_dict.items():
#         if value != "" and value is None:
#             logging.warning(f"{key} has a None value in the database.")
#             # Additional processing or actions if necessary
#             continue


# logging.basicConfig(
#     filename="validation.log", level=logging.WARNING, format="%(asctime)s - %(message)s"
# )


def validate_borrower_info(borrower_dict, keys_to_validate):
    for key in keys_to_validate:
        if borrower_dict.get(key) in ["", None]:
            print(f"Borrower with CustomerID {borrower_dict['CustomerID']} is missing a value for {key}.")
            # logging.warning(
            #     f"Borrower with CustomerID {borrower_dict['CustomerID']} is missing a value for {key}."
            # )
            return False
    return True


class ConsumerRecord:
    # def encrypt_string(data, key=key, iv=iv):
    #     key = hashlib.sha256(secret_key.encode()).digest()
    #     iv = b'\x00' * 16
    #     cipher = Cipher(algorithms.AES(key), modes.CFB(iv), backend=default_backend())
    #     encryptor = cipher.encryptor()
    #     encrypted_data = encryptor.update(data.encode()) + encryptor.finalize()
    #     return base64.b64encode(encrypted_data).decode()

    # def encrypt(value, secret_key):
    #     try:
    #         iv_bytes = bytes([0x0] * 16)
    #         password = secret_key.encode('ascii')
    #         digest = hashlib.sha256()
    #         digest.update(password)
    #         hash_password = digest.digest()
    #         aes_key = hash_password[:32]
    #         cipher = AES.new(aes_key, AES.MODE_CBC, iv_bytes)
    #         encrypted = cipher.encrypt(pad(value.encode('utf-8'), AES.block_size))
    #         return base64.b64encode(encrypted).decode('utf-8')
    #     except Exception as ex:
    #         print(ex)
    #         return None

    def encrypt(plaintext, secret_key):
        method = "aes-256-cbc"
        password = secret_key
        # print("password")
        # print(password)
        # Hash the password to ensure it is 32 bytes long
        password = hashlib.sha256(password.encode()).digest()
        # print("hashed password")
        # print(password)
        # IV must be exact 16 bytes (128 bits)
        iv = bytes([0x0] * 16)
        # print('iv')
        # print(iv)
        # Create a cipher object using the password and IV
        cipher = Cipher(
            algorithms.AES(password), modes.CBC(iv), backend=default_backend()
        )
        # print('cipher')
        # print(cipher)
        encryptor = cipher.encryptor()
        # print('encryptor')
        # print(encryptor)
        # Pad the plaintext to be a multiple of the block size
        block_size = algorithms.AES.block_size // 8
        # print('block_size')
        # print(block_size)
        pad_length = block_size - (len(plaintext) % block_size)
        # print('pad_length')
        # print(pad_length)
        padded_plaintext = plaintext + chr(pad_length) * pad_length
        # print('padded_plaintext')
        # print(padded_plaintext)
        # Encrypt the padded plaintext
        encrypted = encryptor.update(padded_plaintext.encode()) + encryptor.finalize()
        # print('encrypted')
        # print(encrypted)
        # Encode the encrypted bytes in base64
        encrypted_base64 = base64.b64encode(encrypted).decode()
        # print("encrypted_base64")
        # print(encrypted_base64)
        return encrypted_base64

    # def encrypt(msg, secret_key):
    #     try:
    #         # iv = { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
    #         # iv = os.urandom(16)
    #         # hex_values = ['0x0', '0x0', '0x0', '0x0', '0x0', '0x0', '0x0', '0x0', '0x0', '0x0', '0x0', '0x0', '0x0', '0x0', '0x0', '0x0']
    #         # iv = bytes([int(x,0) for x in hex_values])
    #         hex_values = [ 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 ]
    #         hex_string = ''.join(f'{x:02x}' for x in hex_values)
    #         print("Hex string:", hex_string)

    #         # print(byte_array)
    #         print("iv 1")
    #         print(hex_string)
    #         # password = "3sc3RLrpd17"
    # #         msg = "[{\"DATA\":\"D\",\"CUSTOMERID\":\"32226218\",\"BRANCHCODE\":\"01\",\"SURNAME\":\"Odunlami\",\"FIRSTNAME\":\"Aramide\",\"MIDDLENAME\":\"\",\"DATEOFBIRTH\":\"19950204\",\"NATIONALIDENTITYNUMBER\":\"\",\"DRIVERSLICENSENUMBER\":\"\",\"BVN
    # # \",\"GUARANTOREMAIL\":\"\",\"FILLER\":\"\"}]"

    #         # print(f"IV: {iv}")
    #         # print(f"PWD: {password}")
    #         # print(f"MSG: {msg}")

    #         # Convert Hex String to Binary
    #         iv = unhexlify(hex_string)
    #         print("iv 2")
    #         print(iv)
    #         if len(secret_key) % 2 != 0:
    #             secret_key = '0' + secret_key

    #         password = binascii.unhexlify(secret_key)
    #         print("password")
    #         print(password)
    #         # Pad to AES Block Size
    #         msg = pad(msg.encode(), AES.block_size)
    #         print("msg")
    #         print(msg)
    #         # Encipher Text
    #         cipher = AES.new(password, AES.MODE_CBC, iv)
    #         print("cipher")
    #         print(cipher)
    #         cipher_text = cipher.encrypt(msg)
    #         print("cipher_text")
    #         print(cipher_text)

    #         # Encode Cipher_text as Base 64 and decode to String
    #         out = b64encode(cipher_text).decode('utf-8')
    #         print(f"OUT: {out}")
    #         return out

    #     except Exception as ex:
    #         print(ex)
    #         return None

    # def encrypt(value, secret_key):
    #     try:
    #         iv_bytes = os.urandom(16)
    #         password = secret_key.encode('utf-8')
    #         aes_key = hashlib.sha256(password).digest()[:32]
    #         cipher = AES.new(aes_key, AES.MODE_CBC, iv_bytes)
    #         padded_value = pad(value.encode('utf-8'), AES.block_size)
    #         encrypted = cipher.encrypt(padded_value)
    #         encrypted_message = iv_bytes + encrypted
    #         print(encrypted)
    #         print("****************")
    #         encoded_encrypted_message = base64.b64encode(encrypted_message).decode('utf-8')
    #         print("Encrypted message:", encoded_encrypted_message)
    #         print("****************")
    #         return encoded_encrypted_message
    #     except Exception as ex:
    #         print(ex)
    #         return None

    # Usage
    # secret_key = "3sc3RLrpd17"
    # enval = '[{"Username": "1234"}]'
    # encrypted_record = encrypt(enval, secret_key)
    # print(encrypted_record)

    def login_function(self, data):
        url = "https://iremedy.firstcentralcreditbureau.com/AutoUploaderRest/restapi/Login"
        data

        try:
            resp = requests.request("POST", url, data=data)
            data = resp.text
            data = ast.literal_eval(data)[0]
            self.data_ticket = data.get("DataTicket")
            # print('self.data_ticket')
            # print(data.get("DataTicket"))
            return data.get("DataTicket")
        except Exception as e:
            # print(e)
            return {}

    def consumer_function(self, **kwargs):
        url = "https://iremedy.firstcentralcreditbureau.com/AutoUploaderRest/restapi/Consumer"

        loan_dict = {}
        loans = AjoLoan.objects.filter(status=LoanStatus.OPEN)
        if not loans.exists():
            raise ValueError("No loans found with the specified criteria.")
        else:

            for loan in loans:
                borrower = loan.borrower
                bank_account_details = BankAccountDetails.objects.filter(
                    ajo_user=borrower
                ).last()
                if bank_account_details is None:
                    raise ValueError(f"{borrower} has no bank account Details.")

                schedule = AjoLoanSchedule.objects.filter(loan=loan).last()
                if schedule is None:
                    raise ValueError(f"{borrower} has no loan schedule.")

                info = BorrowerInfo.objects.filter(borrower=borrower, loan=loan).last()
                if info is None:
                    raise ValueError(f"{borrower} has no information.")

                you_verify_metadata = info.you_verify_metadata
                country_code = you_verify_metadata.get("data", {}).get("country")
                country = pycountry.countries.get(alpha_2=country_code)
                country_name = country.name

                data = {
                    "CUSTOMERID": (
                        borrower.loandisk_borrower_id
                        if borrower.loandisk_borrower_id
                        else ""
                    ),
                    "BRANCHCODE": (
                        borrower.branch_loandisk_borrower_id
                        if borrower.branch_loandisk_borrower_id
                        else ""
                    ),
                    "SURNAME": borrower.last_name if borrower.last_name else "",
                    "FIRSTNAME": borrower.first_name if borrower.first_name else "",
                    "MIDDLENAME": "",
                    "DATEOFBIRTH": str(borrower.dob) if borrower.dob else "",
                    "NATIONALIDENTITYNUMBER": "",
                    "DRIVERSLICENSENUMBR": "",
                    "BVNNUMBER": borrower.bvn if borrower.bvn else "",
                    "PASSPORTNUMBER": "",
                    "PENCOMIDNUMBER": "",
                    "OTHERID": "",
                    "GENDER": borrower.gender if borrower.gender else "",
                    "NATIONALITY": country_name if country_name else "",
                    "MARITALSTATUS": (
                        borrower.marital_status if borrower.marital_status else ""
                    ),
                    "MOBILENUMBER": borrower.phone if borrower.phone else "",
                    "PRIMARYADDRESSLINE1": (
                        borrower.verified_location_address
                        if borrower.verified_location_address
                        else ""
                    ),
                    "PRIMARYADDRESSLINE2": "",
                    "PRIMARYADDRESSCITY": (
                        borrower.verified_location_city
                        if borrower.verified_location_city
                        else ""
                    ),
                    "PRIMARYADDRESSSTATE": (
                        borrower.verified_location_state
                        if borrower.verified_location_state
                        else ""
                    ),
                    "PRIMARYADDRESSCOUNTRY": (
                        borrower.verified_location_country
                        if borrower.verified_location_country
                        else ""
                    ),
                    "PRIMARYADDRESSPOSTCODE": "",
                    "EMPLOYMENTSTATUS": "Self Employed",
                    "OCCUPATION": "OTHERS",
                    "BUSINESSCATEGORY": "N/A",
                    "BUSINESSSECTOR": "N/A",
                    "BORROWERTYPE": "Individual",
                    "TAXID": "",
                    "PICTUREFILEPATH": "",
                    "EMAILADDRESS": "",
                    "EMPLOYERNAME": "",
                    "EMPLOYERADDRESSLINE1": "",
                    "EMPLOYERADDRESSLINE2": "",
                    "EMPLOYERCITY": "",
                    "EMPLOYERSTATE": "",
                    "EMPLOYERCOUNTRY": "",
                    "TITLE": "",
                    "PLACEOFBIRTH": "",
                    "WORKTELEPHONE": "",
                    "HOMETELEPHONE": "",
                    "SECONDARYADDRESSLINE1": "",
                    "SECONDARYADDRESSLINE2": "",
                    "SECONDARYADDRESSCITYLGA": "",
                    "SECONDARYADDRESSSTATE": "",
                    "SECONDARYADDRESSCOUNTRY": "",
                    "SECONDARYADDRESSPOSTCODE": "",
                    "SPOUSESURNAME": "",
                    "SPOUSEFIRSTNAME": "",
                    "SPOUSEMIDDLENAME": "",
                    "ACCOUNTNUMBER": (
                        bank_account_details.account_number
                        if bank_account_details.account_number
                        else ""
                    ),
                    "ACCOUNTSTATUS": (
                        bank_account_details.is_active
                        if bank_account_details.is_active
                        else ""
                    ),
                    "ACCOUNTSTATUSDATE": (
                        str(bank_account_details.updated_at.date())
                        if bank_account_details.updated_at
                        else ""
                    ),
                    "LOANEFFECTIVEDATE": (
                        str(loan.start_date) if loan.start_date else ""
                    ),
                    "DEFEREDPAYMENTDATE": "",
                    "CREDITLIMIT": 500000,
                    "AVAILEDLIMIT": 500000,
                    "OUTSTANDINGBALANCE": (
                        loan.total_outstanding_balance
                        if loan.total_outstanding_balance
                        else ""
                    ),
                    "CURRENTBALANCEDEBITIND": "",
                    "INSTALLMENTAMOUNT": (
                        loan.daily_repayment_amount
                        if loan.daily_repayment_amount
                        else ""
                    ),
                    "CURRENCY": "NGN",
                    "DAYSINARREARS": schedule.late_days if schedule.late_days else "",
                    "OVERDUEAMOUNT": schedule.due_amount if schedule.due_amount else "",
                    "FACILITYTYPE": loan.loan_type if loan.loan_type else "",
                    "FACILITYTENOR": loan.tenor_in_days if loan.tenor_in_days else "",
                    "FACILITYOWNERSHIPTYPE": "",
                    "REPAYMENTFRREQUENCY": loan.tenor if loan.tenor else "",
                    "LASTPAYMENTDATE": (
                        schedule.last_paid_date if schedule.last_paid_date else ""
                    ),
                    "LASTPAYMENTAMOUNT": (
                        loan.last_paid_amount if loan.last_paid_amount else ""
                    ),
                    "MATURITYDATE": loan.end_date if loan.end_date else "",
                    "INCOME": "",
                    "INCOMEFREQUENCY": "",
                    "OWNERTENANT": "",
                    "NUMBEROFPARTICIPANTSINJOINTLOAN": "",
                    "DEPENDANTS": "",
                    "LOANCLASSIFICATION": (
                        loan.performance_status if loan.performance_status else ""
                    ),
                    "LEGALCHALLENGESTATUS": "NO",
                    "LITIGATIONDATE": "",
                    "CONSENTSTATUS": "YES",
                    "LOANSECURITYSTATUS": "NO",
                    "COLLATERALTYPE": "",
                    "COLLATERALDETAILS": "",
                    "PREVIOUSACCOUNTNUMBER": "",
                    "PREVIOUSNAME": "",
                    "PREVIOUSCUSTOMERID": "",
                    "PREVIOUSBRANCHCODE": "",
                    "CUSTOMERSACCOUNTNUMBER": "",
                    "GUARANTEESTATUSOFLOAN": "",
                    "TYPEOFGUARANTEE": "",
                    "NAMEOFCORPORATEGUARANTOR": "",
                    "BIZIDNUMBEROFCORPORATEGUARANTOR": "",
                    "INDIVIDUALGUARANTORSURNAME": "",
                    "INDIVIDUALGUARANTORFIRSTNAME": "",
                    "INDIVIDUALGUARNTORMIDDLENAME": "",
                    "GUARANTORDATEOFBIRTHINCORPORATION": "",
                    "GUARANTORGENDER": "",
                    "GUARANTORNATIONALIDNUMBER": "",
                    "GUARNATORINTLPASSPORTNUMBER": "",
                    "GUARANTORDRIVERSLICENCENUMBER": "",
                    "GUARANTORBVN": "",
                    "GUARANTOROTHERID": "",
                    "GUARANTORPRIMARYADDRESSLINE1": "",
                    "GUARANTORPRIMARYADDRESSLINE2": "",
                    "GUARANTORPRIMARYADDRESSCITYLGA": "",
                    "GUARANTORPRIMARYADDRESSSTATE": "",
                    "GUARANTORPRIMARYADDRESSCOUNTRY": "",
                    "GUARANTORPRIMARYPHONENUMBER": "",
                    "GUARANTOREMAIL": "",
                }

                # data = {"CUSTOMERID":"43231345",
                #     "BRANCHCODE":"12",
                #     "SURNAME":"Mayowa",
                #     "FIRSTNAME":"CHIKAODIRI",
                #     "MIDDLENAME":"Husman",
                #     "DATEOFBIRTH":"19780520",
                #     "NATIONALIDENTITYNUMBER":"",
                #     "DRIVERSLICENSENUMBER":"",
                #     "BVNNUMBER":"12345678901",
                #     "PASSPORTNUMBER":"",
                #     "PENCOMIDNUMBER":"",
                #     "OTHERID":"",
                #     "GENDER":"Female",
                #     "NATIONALITY":"Nigeria",
                #     "MARITALSTATUS":"Married",
                #     "MOBILENUMBER":"",
                #     "PRIMARYADDRESSLINE1":"13 AWOLOWO ROAD IKEJA",
                #     "PRIMARYADDRESSLINE2":"PO BOX 321 IKEJA",
                #     "PRIMARYADDRESSCITY":"LAGOS",
                #     "PRIMARYADDRESSSTATE":"",
                #     "PRIMARYADDRESSCOUNTRY":"",
                #     "PRIMARYADDRESSPOSTCODE":"",
                #     "EMPLOYMENTSTATUS":"Self Employed",
                #     "OCCUPATION":"OTHERS",
                #     "BUSINESSCATEGORY":"MEDI",
                #     "BUSINESSSECTOR":"",
                #     "BORROWERTYPE":"Individual",
                #     "TAXID":"",
                #     "PICTUREFILEPATH":"",
                #     "EMAILADDRESS":"",
                #     "EMPLOYERNAME":"",
                #     "EMPLOYERADDRESSLINE1":"",
                #     "EMPLOYERADDRESSLINE2":"",
                #     "EMPLOYERCITY":"",
                #     "EMPLOYERSTATE":"",
                #     "EMPLOYERCOUNTRY":"",
                #     "TITLE":"",
                #     "PLACEOFBIRTH":"",
                #     "WORKTELEPHONE":"",
                #     "HOMETELEPHONE":"",
                #     "SECONDARYADDRESSLINE1":"",
                #     "SECONDARYADDRESSLINE2":"",
                #     "SECONDARYADDRESSCITYLGA":"",
                #     "SECONDARYADDRESSSTATE":"",
                #     "SECONDARYADDRESSCOUNTRY":"",
                #     "SECONDARYADDRESSPOSTCODE":"",
                #     "SPOUSESURNAME":"",
                #     "SPOUSEFIRSTNAME":"",
                #     "SPOUSEMIDDLENAME":"",
                #     "ACCOUNTNUMBER":"*********",
                #     "ACCOUNTSTATUS":"1",
                #     "ACCOUNTSTATUSDATE":"********",
                #     "LOANEFFECTIVEDATE":"********",
                #     "DEFEREDPAYMENTDATE":"",
                #     "CREDITLIMIT":"0",
                #     "AVAILEDLIMIT":"0",
                #     "OUTSTANDINGBALANCE":"2578.67",
                #     "CURRENTBALANCEDEBITIND":"",
                #     "INSTALMENTAMOUNT":"0",
                #     "CURRENCY":"NGN",
                #     "DAYSINARREARS":"569",
                #     "OVERDUEAMOUNT":"2578.67",
                #     "FACILITYTYPE":"Personal Overdraft",
                #     "FACILITYTENOR":"0",
                #     "FACILITYOWNERSHIPTYPE":"",
                #     "REPAYMENTFREQUENCY":"Monthly",
                #     "LASTPAYMENTDATE":"********",
                #     "LASTPAYMENTAMOUNT":"0",
                #     "MATURITYDATE":"********",
                #     "INCOME":"",
                #     "INCOMEFREQUENCY":"",
                #     "OWNERTENANT":"",
                #     "NUMBEROFPARTICIPANTSINJOINTLOAN":"",
                #     "DEPENDANTS":"",
                #     "LOANCLASSIFICATION":"Lost",
                #     "LEGALCHALLENGESTATUS":"NO",
                #     "LITIGATIONDATE":"",
                #     "CONSENTSTATUS":"YES",
                #     "LOANSECURITYSTATUS":"NO",
                #     "COLLATERALTYPE":"",
                #     "COLLATERALDETAILS":"",
                #     "PREVIOUSACCOUNTNUMBER":"",
                #     "PREVIOUSNAME":"",
                #     "PREVIOUSCUSTOMERID":"",
                #     "PREVIOUSBRANCHCODE":"",
                #     "CUSTOMERSACCOUNTNUMBER":"",
                #     "GUARANTEESTATUSOFLOAN":"",
                #     "TYPEOFGUARANTEE":"",
                #     "NAMEOFCORPORATEGUARANTOR":"",
                #     "BIZIDNUMBEROFCORPORATEGUARANTOR":"",
                #     "INDIVIDUALGUARANTORSURNAME":"",
                #     "INDIVIDUALGUARANTORFIRSTNAME":"",
                #     "INDIVIDUALGUARNTORMIDDLENAME":"",
                #     "GUARANTORDATEOFBIRTHINCORPORATION":"",
                #     "GUARANTORGENDER":"",
                #     "GUARANTORNATIONALIDNUMBER":"",
                #     "GUARNATORINTLPASSPORTNUMBER":"",
                #     "GUARANTORDRIVERSLICENCENUMBER":"",
                #     "GUARANTORBVN":"",
                #     "GUARANTOROTHERID":"",
                #     "GUARANTORPRIMARYADDRESSLINE1":"",
                #     "GUARANTORPRIMARYADDRESSLINE2":"",
                #     "GUARANTORPRIMARYADDRESSCITYLGA":"",
                #     "GUARANTORPRIMARYADDRESSSTATE":"",
                #     "GUARANTORPRIMARYADDRESSCOUNTRY":"",
                #     "GUARANTORPRIMARYPHONENUMBER":"",
                #     "GUARANTOREMAIL":""}

                loan_dict[loan.id] = data

        data_submission_record = json.dumps(loan_dict)

        data = {"username": settings.FCCB_USERNAME, "password": settings.FCCB_PASSWORD}

        # request = MockRequest(data)

        consumer_record = ConsumerRecord()

        try:
            self.data_ticket = consumer_record.login_function(data)
        except Exception as e:
            # print(e)
            return {}

        # self.encrypted_update_record = ConsumerRecord.encrypt_string(data_submission_record, key, iv)
        # self.encrypted_update_record = ConsumerRecord.encrypt(data_submission_record, secret_key)
        self.encrypted_update_record = "B3WuNHlwsHF6xGUdVCyv7k6PezTH8ciarKXOEcdllRMdtrzRKzBYLBsqmspZhpg7KV9kiqx4hRWL5H3BR2XHQWvfkdEGDmd9SGe7/dRx55Ow+iQVXPlj/BoQcQBulplkXk+/J2xCUDgNSgNhZ1Jv/EcH1oBbE6FSsRkKd1lbSSSDhKv5Nl7h gkaVT+u8CNstD1W9Q4cDpjaEbwtv22kTGoIphM0JqFAAKtJ7mTLdJH6QHgXWDSFY9ubcZO46ZieJ1rJGI49uCViGWOMOuTnzmLwkYHrwJ7XpwxYNd64Ao0oKlATcZ9/W5xdw65I4VKoITjrge0rB/KSpm58BaYv35892qhcZzLBHo7UV/Cc0SWfm0o4+hTIMVam"
        self.batch_id = "1"

        if not hasattr(self, "headers"):
            self.headers = {}

        resp = requests.get(url, headers=self.headers)

        post_data = {
            "DataTicket": self.data_ticket,
            "EncryptedUpdateRecord": self.encrypted_update_record,
            "BatchId": self.batch_id,
        }
        # print("post_data", post_data)

        resp = requests.post(url, data=post_data, headers=self.headers)
        # print(resp)
        response_data = resp.json()
        # print('response_data')
        # print(response_data)
        return response_data


class CRCRecord:

    def credit_information():
        url = "https://files.creditreferencenigeria.net/crccreditbureau_Datasubmission_Webservice/JSON/api/nECreditInfo/"
        loan_list = []
        loans = AjoLoan.objects.filter(status=LoanStatus.OPEN)

        if not loans.exists():
            return Response(
                {"error": "No loans found with the specified criteria."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            for loan in loans:
                borrower = loan.borrower

                # oustanding_balance = loan.repayment_amount - loan.total_paid_amount

                bank_account_details = AjoUserWithdrawalAccount.objects.filter(
                    ajo_user=borrower
                ).first()
                if bank_account_details is None:
                    continue

                schedule = AjoLoanSchedule.objects.filter(loan=loan).last()
                if schedule is None:
                    continue

                dictionary = {
                    "CustomerID": customerid_format(borrower.id) if borrower.id else "",
                    "AccountNumber": f"{bank_account_details.account_number if bank_account_details and bank_account_details.account_number else ''}",
                    "AccountStatus": f"{'Open' if bank_account_details else 'Closed'}",
                    "AccountStatusDate": (
                        date_format(str(bank_account_details.updated_at.date()))
                        if bank_account_details and bank_account_details.updated_at
                        else ""
                    ),
                    "DateOfLoan(Facility)Disbursement/LoanEffectiveDate": (
                        date_format(str(loan.start_date)) if loan.start_date else ""
                    ),
                    "CreditLimit(Facility)Amount/GlobalLimit": "500000",
                    "Loan(Facility)Amount/AvailedLimit": "500000",
                    "OutstandingBalance": f"{round(loan.outstanding_due) if loan.outstanding_due > 0 else 0}",
                    "InstalmentAmount": f"{loan.daily_repayment_amount if loan.daily_repayment_amount else ''}",
                    "Currency": "Naira",
                    "DaysInArrears": f"{loan.outstanding_days_today if loan.outstanding_days_today > 0 else (1 if loan.outstanding_days_today == 0 and round(loan.outstanding_due_today) > 0 else 0)}",
                    "OverdueAmount": f"{round(loan.outstanding_due_today) if loan.outstanding_due_today > 0 else 0}",
                    "Loan(Facility)Type": f"{loan.loan_type if loan.loan_type else ''}",
                    "Loan(Facility)Tenor": f"{loan.tenor_in_days if loan.tenor_in_days else ''}",
                    "RepaymentFrequency": f"{loan.repayment_type if loan.repayment_type else ''}",
                    "LastPaymentDate": f"{date_format(str(schedule.paid_date)) if schedule.paid_date else ''}",
                    "LastPaymentAmount": f"{loan.last_paid_amount if loan.last_paid_amount else ''}",
                    "MaturityDate": f"{date_format(str(loan.end_date)) if loan.end_date else ''}",
                    "LoanClassification": f"{get_days_past_due(loan)}",
                    "LegalChallengeStatus": "NO",
                    "LitigationDate": "",
                    "ConsentStatus": "YES",
                    "LoanSecurityStatus": "NO",
                    "CollateralType": "",
                    "CollateralDetails": "",
                    "PreviousAccountNumber": "",
                    "PreviousName": "",
                    "PreviousCustomerID": "",
                    "PreviousBranchCode": "",
                }

                loan_list.append(dictionary)

        if len(loan_list) == 0:
            return {"error": "There are no open loans."}

        headers = {"Content-Type": "application/x-www-form-urlencoded"}

        upload_type = "CREDIT"

        return upload_information(loan_list, url, headers, upload_type)

    def borrower_information():
        url = "https://files.creditreferencenigeria.net/crccreditbureau_Datasubmission_Webservice/JSON/api/neIndividualborrower/"
        borrower_list = []
        loans = AjoLoan.objects.filter(status=LoanStatus.OPEN)

        if not loans.exists():
            return Response(
                {"error": "No loans found with the specified criteria."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:

            keys_to_validate = [
                "CustomerID",
                "Surname",
                "Firstname",
                "DateofBirth",
                "BVNNo",
                "Gender",
                "Nationality",
                "PrimaryAddressLine1",
                "Primarycity/LGA",
                "PrimaryState",
            ]

            for loan in loans:
                borrower = loan.borrower
                borrower_info = BorrowerInfo.objects.filter(borrower=borrower).last()
                if borrower_info is None:
                    continue

                country_name = "Nigeria"

                borrower_dict = {
                    "CustomerID": customerid_format(borrower.id) if borrower.id else "",
                    "BranchCode": "01",
                    "Surname": f"{borrower.last_name if borrower.last_name else ''}",
                    "Firstname": f"{borrower.first_name if borrower.first_name else ''}",
                    "Middlename": "",
                    "DateofBirth": (
                        date_format(str(borrower_info.date_of_birth))
                        if borrower_info and borrower_info.date_of_birth
                        else ""
                    ),
                    "NationalIdentityNumber": "",
                    "DriversLicenseNo": "",
                    "BVNNo": f"{borrower_info.verification_number if borrower_info.verification_number and borrower_info.verification_type == 'BVN' else borrower_info.borrower.phone}",
                    "PassportNo": "",
                    "Gender": f"{borrower.gender if borrower.gender else ''}",
                    "Nationality": f"{country_name if country_name else ''}",
                    "MaritalStatus": "",
                    "Mobilenumber": "",
                    "PrimaryAddressLine1": f"{borrower.address if borrower.address else ''}",
                    "PrimaryAddressLine2": "",
                    "Primarycity/LGA": f"{borrower.lga if borrower.lga else ''}",
                    "PrimaryState": f"{borrower.state if borrower.state else ''}",
                    "PrimaryCountry": "Nigeria",
                    "EmploymentStatus": "",
                    "Occupation": "",
                    "BusinessCategory": "",
                    "BusinessSector": "",
                    "BorrowerType": "",
                    "OtherID": "",
                    "TaxID": "",
                    "PictureFilePath": "",
                    "E-mailaddress": "",
                    "EmployerName": "",
                    "EmployerAddressLine1": "",
                    "EmployerAddressLine2": "",
                    "EmployerCity": "",
                    "EmployerState": "",
                    "EmployerCountry": "",
                    "Title": "",
                    "PlaceofBirth": "",
                    "Workphone": "",
                    "Homephone": "",
                    "SecondaryAddressLine1": "",
                    "SecondaryAddressLine2": "",
                    "SecondaryAddressCity/LGA": "",
                    "SecondaryAddressState": "",
                    "SecondaryAddressCountry": "",
                    "SpousesSurname": "",
                    "SpousesFirstname": "",
                    "SpousesMiddlename": "",
                }

                if not validate_borrower_info(borrower_dict, keys_to_validate):
                    continue

                borrower_list.append(borrower_dict)

        headers = {"Content-Type": "application/x-www-form-urlencoded"}

        upload_type = "BORROWER"

        return upload_information(borrower_list, url, headers, upload_type)
