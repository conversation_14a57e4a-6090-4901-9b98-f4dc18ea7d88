import json
import random

from django.conf import settings
from openai import OpenAI


def get_completion(prompt, client_instance, system_msg=None, model="gpt-3.5-turbo"):
    messages = [{"role": "user", "content": prompt}]
    if system_msg:
        messages.append({"role": "system", "content": system_msg})

    response = client_instance.chat.completions.create(
        model=model,
        messages=messages,
        temperature=0.0,
    )
    return response


def send_prompt_to_chatgpt(prompt, system_msg=None, model="gpt-3.5-turbo"):

    api_keys = str(settings.OPENAI_API_KEY).split(",")

    # Shuffle the API keys randomly
    random.shuffle(api_keys)

    # Try each API key until a successful request is made or all keys are exhausted
    for api_key in api_keys:
        client = OpenAI(api_key=api_key)

        try:
            response = get_completion(prompt, client, system_msg, model)

            # Extract relevant information from the response object
            response_data = response.choices[0].message.content

            return response_data
        except Exception as e:
            print(f"An error occurred with API key {api_key}: {e}")
            continue  # Try the next API key if the current one fails

    raise Exception("All API keys failed to get a successful response from OpenAI.")


def review_loan_application(
    product,
    selling_price,
    units_sold_per_week,
    profit_per_unit,
    monthly_expenses,
    existing_loan_repayment,
    loan_duration_months,
    interest_rate_per_month,
    profit_percentage_for_repayment,
):
    prompt = (
        f"Pretend to be a senior loan underwriter and review the following loan application data:\n\n"
        f"Product: {product}\n"
        f"Selling Price: ₦{selling_price}\n"
        f"Units Sold per Week: {units_sold_per_week}\n"
        f"Profit per Unit: ₦{profit_per_unit}\n"
        f"Monthly Expenses: ₦{monthly_expenses}\n"
        f"Existing Loan Repayment Monthly: ₦{existing_loan_repayment}\n"
        f"Loan Duration: {loan_duration_months} months\n"
        f"Interest Rate: {interest_rate_per_month}% per month\n"
        f"% of Profits for Loan Repayments: {profit_percentage_for_repayment}%\n\n"
        "Based on this information, provide a JSON response with your decision, including the reasons for approval or decline, "
        "and any recommendations for next steps.\n"
        "You MUST NOT wrap it within json md markers"
    )

    SYSTEM_MSG = (
        "You are acting as a senior loan underwriter. Your task is to review the financial feasibility of this loan application, "
        "evaluating the profitability, expenses, and repayment capacity. Consider the loan terms and provide a recommendation on "
        "whether to approve or decline the loan. Include your reasoning, any conditions for approval, and additional recommendations."
    )

    prompt += (
        "\nReturn the JSON response in the format:\n"
        "{\n"
        '  "loan_application_review": {\n'
        '    "decision": {\n'
        '      "status": "approve/decline",\n'
        '      "score": int,\n'
        '      "recommended_loan_amount": "float",\n'
        '      "repayment_period_months": int,\n'
        '      "monthly_repayment_amount": "float",\n'
        '      "conditions": [],\n'
        '      "reason": [],\n'
        '      "recommendations": []\n'
        "    }\n"
        "  }\n"
        "}"
    )

    # Simulate sending prompt to ChatGPT or another model.
    chatgpt_response = send_prompt_to_chatgpt(prompt, system_msg=SYSTEM_MSG)
    # print("---chatgpt_response---", chatgpt_response)
    try:
        response_data = json.loads(chatgpt_response)
    except json.JSONDecodeError:
        response_data = {"error": "Unable to parse response"}
        # print(response_data)

    return response_data
