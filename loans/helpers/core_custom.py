from hmac import compare_digest

from django.conf import settings
from rest_framework import permissions


class CoreCustomIsAuthenticated(permissions.BasePermission):
    """
    Grants custom access to Core Banking service provider.
    """
    def has_permission(self, request, view):
        web_token = request.headers.get("web-token")
        if not compare_digest(web_token, settings.WEB_TOKEN):
            return False
        return True
