import json
from datetime import datetime

import requests
from django.conf import settings
from django.core.cache import cache


class FirstCentralManager:
    # to use for api get buruea result
    # base_url = "https://online.firstcentralcreditbureau.com/firstcentralrestv2"
    base_url = "https://als.firstcentralcreditbureau.com/firstcentralrestv2"
    username = settings.CRC_USERNAME
    password = settings.CRC_PASSWORD

    def request_auth_token(self):

        url = f"{self.base_url}/login"

        payload = json.dumps({"username": self.username, "password": self.password})
        headers = {"Content-Type": "application/json"}
        response = requests.request("POST", url, headers=headers, data=payload)

        return response

    def get_token(self):
        cache_token = cache.get(key=self.password)
        if cache_token:
            return cache_token
        try:
            response = self.request_auth_token()
            token_res = response.json()
        except requests.exceptions.RequestException as e:
            token_res = None

        # print(type(token_res))
        if isinstance(token_res, list) and len(token_res) > 0:
            token = token_res[0].get("DataTicket")
            cache.set(key=self.password, value=token, timeout=60 * 20)
            return token

        else:
            return None

    def consumer_match(self, Identification):

        url = f"{self.base_url}/connectConsumerMatch/"

        token = self.get_token()

        # print(token, "\n\n")

        payload = json.dumps(
            {
                "DataTicket": token,
                "EnquiryReason": "Application for Credit by a borrower",
                "ConsumerName": "",
                "DateOfBirth": "",
                "Identification": Identification,
                "Accountno": "",
                "ProductID": "45",
            }
        )
        headers = {"Content-Type": "application/json"}

        response = requests.request("POST", url, headers=headers, data=payload)
        try:
            result = response.json()
        except requests.exceptions.RequestException as e:
            result = None

        if isinstance(result, list) and len(result) > 0:
            matched_result = result[0].get("MatchedConsumer", [])
            if not matched_result:
                return None
            else:
                final_result = matched_result[0]
                final_result["data_ticket"] = token
                return final_result

    def get_credit_report(self, Identification):

        consumer_match_result = self.consumer_match(Identification=Identification)
        data_ticket = consumer_match_result.get("data_ticket")
        MatchingEngineID = consumer_match_result.get("MatchingEngineID")
        consumerID = consumer_match_result.get("ConsumerID")
        EnquiryID = consumer_match_result.get("EnquiryID")

        if consumerID == "0":
            return [consumer_match_result]  # no record
        else:
            url = f"{self.base_url}/GetConsumerFullCreditReport/"

            payload = json.dumps(
                {
                    "DataTicket": data_ticket,
                    "consumerID": consumerID,
                    "EnquiryID": EnquiryID,
                    "consumerMergeList": consumerID,
                    "SubscriberEnquiryEngineID": MatchingEngineID,
                }
            )
            headers = {"Content-Type": "application/json"}

            response = requests.request("POST", url, headers=headers, data=payload)
            return response.json()


class RawFirstcentralCreditCheck(FirstCentralManager):
    def __init__(self, Identification, debt_threshold, max_debt_institution_count=3, renew=False) -> None:
        self.Identification = Identification
        self.debt_threshold = debt_threshold
        self.max_debt_institution_count = max_debt_institution_count
        self.renew = renew

    @staticmethod
    def string_to_float(amount_str: str) -> float:
        value_str = amount_str.replace(",", "")
        parts = value_str.split(".")

        # Join the integer and decimal parts, and convert to float
        value_float = float(".".join(parts))
        return value_float

    def is_worthy(self, total_outstanding, open_loans, bad_loans, performing_loans, user_behaviour) -> dict:

        if total_outstanding > self.debt_threshold:
            is_credit_worthy = False
            high_outstanding_debt = True
            reason = f"Borrower has an outstanding of {total_outstanding}"

        elif open_loans > self.max_debt_institution_count:
            is_credit_worthy = False
            high_outstanding_debt = False
            reason = f"Borrower has {open_loans} loans"

        elif bad_loans >= 2:
            good_to_bad_ratio = performing_loans / bad_loans
            if good_to_bad_ratio < 0.2 and performing_loans != bad_loans:
                is_credit_worthy = False
                high_outstanding_debt = False
                reason = "Ratio of good loans to bad loans is less than 20%"
            elif user_behaviour != "positive":
                is_credit_worthy = False
                high_outstanding_debt = False
                reason = f"Borrower has {user_behaviour} loan repayment behaviour"

            else:
                is_credit_worthy = True
                high_outstanding_debt = False
                reason = "Approved"

        else:
            is_credit_worthy = True
            high_outstanding_debt = False
            reason = "Approved"

        result = {
            "is_credit_worthy": is_credit_worthy,
            "high_outstanding_debt": high_outstanding_debt,
            "reason": reason,
        }
        return result

    def get_credit_status(self):

        failed_result = {
            "status": None,
            "reason": "",
            "debt_threshold": self.debt_threshold,
            "high_outstanding_debt": False,
            "max_debt_institution_count": self.max_debt_institution_count,
            "total_outstanding": 0,
            "count_of_open_loans": 0,
            "open_loan_institutions": [],
            "bad_loans_institions_count": 0,
            "bad_loans_institions": [],
        }

        credit_summary_response = super().get_credit_report(Identification=self.Identification)

        if isinstance(credit_summary_response, list):
            if credit_summary_response[0].get("MatchingEngineID", None):
                return {
                    "status": True,
                    "reason": "Not Found On crc",
                    "debt_threshold": self.debt_threshold,
                    "high_outstanding_debt": False,
                    "max_debt_institution_count": self.max_debt_institution_count,
                    "total_outstanding": 0,
                    "count_of_open_loans": 0,
                    "open_loan_institutions": [],
                    "bad_loans_institions_count": 0,
                    "bad_loans_institions": [],
                    "user_behaviour": "",
                    "first_central_Result": credit_summary_response,
                }

        try:
            matched_consumer_response_type = credit_summary_response[0]
        except (IndexError, KeyError, ValueError):
            return failed_result

        # unique response for user with no loan record
        matched_consumer = matched_consumer_response_type.get("MatchedConsumer", None)

        if matched_consumer is None:
            try:
                account_summary = credit_summary_response[3]["CreditAccountSummary"][0]
                subject_list = credit_summary_response[0]["SubjectList"]
                credit_summary_list = credit_summary_response[5]["CreditAgreementSummary"]

            except (KeyError, IndexError) as err:
                failed_result["first_central_Result"] = credit_summary_response,
                return failed_result

            # update credit bureau metadata
            total_account_arrears = account_summary.get("TotalAccountarrear")
            over_all_monthly_istallment = account_summary.get("TotalMonthlyInstalment")
            total_monthly_instalment = self.string_to_float(amount_str=over_all_monthly_istallment)

            total_outstanding = account_summary.get("TotalOutstandingdebt")
            performing_loans_institions: list = []
            good_loans = 0
            bad_loans = 0
            open_loans = 0
            open_loan_institutions: list = []
            bad_loans_institions: list = []
            institution_status = {}
            lost_loan_accs = []

            for credit_summary_by_institution in credit_summary_list:
                overdue = credit_summary_by_institution.get("AmountOverdue")
                account_status = credit_summary_by_institution.get("AccountStatus")
                account_num = credit_summary_by_institution.get("AccountNo")
                performance_status = credit_summary_by_institution.get("PerformanceStatus")
                institution_name = credit_summary_by_institution.get("SubscriberName")
                date_open = datetime.strptime(credit_summary_by_institution.get("DateAccountOpened"), "%d/%m/%Y")

                credit_summary_by_institution["DateAccountOpened"] = date_open

                if performance_status != "Performing":
                    if performance_status == "Lost":
                        if overdue == 0.0 or account_num in lost_loan_accs:
                            continue
                        lost_loan_accs.append(account_num)
                    else:
                        if institution_name not in bad_loans_institions:
                            bad_loans_institions.append(institution_name)

                        try:
                            latest_date = max(institution_status.get("institution_name"))
                            if latest_date < date_open:
                                print("There is no date later than the current one.")
                                bad_loans += 1
                        except Exception:
                            bad_loans += 1

                elif performance_status == "Performing":
                    if account_status == "Open":
                        if institution_name not in open_loan_institutions:
                            open_loan_institutions.append(institution_name)

                    if institution_name not in performing_loans_institions:
                        performing_loans_institions.append(institution_name)
                    good_loans += 1
                    if institution_name in institution_status:
                        institution_status[institution_name].append(date_open)
                    else:
                        institution_status[institution_name] = [date_open]

            credit_summary_list.sort(key=lambda x: x["DateAccountOpened"])

            performing_at_start = 0
            performing_at_end = 0

            # Count performing loans at the beginning and at the end
            for agreement in credit_summary_list:
                if agreement["PerformanceStatus"] == "Performing":
                    performing_at_end += 1
                else:
                    performing_at_start = performing_at_end
                    performing_at_end = 0
            user_behaviour = ""
            if performing_at_end > performing_at_start:
                user_behaviour = "positive"
            else:
                user_behaviour = "negative"

            total_outstanding_to_float = self.string_to_float(total_outstanding)

            worthiness = self.is_worthy(
                total_outstanding=total_outstanding_to_float,
                open_loans=len(open_loan_institutions),
                bad_loans=len(bad_loans_institions),
                performing_loans=len(performing_loans_institions),
                user_behaviour=user_behaviour,
            )
            result = {
                "status": worthiness.get("is_credit_worthy"),
                "reason": worthiness.get("reason"),
                "debt_threshold": self.debt_threshold,
                "high_outstanding_debt": worthiness.get("high_outstanding_debt"),
                "max_debt_institution_count": self.max_debt_institution_count,
                "total_outstanding": total_outstanding_to_float,
                "count_of_open_loans": len(open_loan_institutions),
                "open_loan_institutions": open_loan_institutions,
                "bad_loans_institions_count": len(bad_loans_institions),
                "bad_loans_institions": bad_loans_institions,
                "first_central_Result": credit_summary_response,
            }
            return result

        else:
            result = {
                "status": True,
                "reason": "",
                "debt_threshold": self.debt_threshold,
                "high_outstanding_debt": False,
                "max_debt_institution_count": self.max_debt_institution_count,
                "total_outstanding": 0,
                "count_of_open_loans": 0,
                "open_loan_institutions": [],
                "bad_loans_institions_count": 0,
                "bad_loans_institions": [],
                "first_central_Result": credit_summary_response,
            }
            return result


# result = FirstcentralCreditCheck().get_credit_status()
# print(result)
