from  loans.helpers.loandisk_helper import LoandiskManager

class LoanDiskAutomationManager:
    
    @staticmethod
    def process_loan_balance(phone):
        """
        Process the loan balance for a given phone number.
        Args:
            phone (str): The phone number of the borrower.
        Returns:
            dict: A dictionary containing the loan balance information.
        """
        borrower = LoandiskManager().get_borrower_using_phone(phone)
        if borrower.get("error"):
            if borrower.get("error").get("code") == 404:
                return {
                    "status": False,
                    "message": "Phone Number Not Found For Any Borrower",
                    "code": 404
                }
            else:
                return {
                    "status": False,
                    "message": borrower.get("error").get("message"),
                    "code": 400
                }
        else:
            loans = []
            response = borrower.get("response")
            borrower_id = response.get("Results")[0][0].get("borrower_id")
            
            loan_page = LoandiskManager().get_all_borrower_loans(
                borrower_id,
                page_number=1,
                number_results=20,
            )
            print(f"LOAN PAGE >> {loan_page}")
            # return {
            #     "status": True,
            #     "code": 200,
            #     "borrower_id": borrower_id
            # }
        
        

LOAN_DISK_LOAN_STATUS = {
    8: "Processing",
    143635: "Unsolicited Loan",
    143668: "Unsolicited Loan: resolved",
    216234: "Waiting-Draw",
    216235: "Awaiting Payment",
    366028: "Face-Match",
    366029: "Borrower Info",
    366030: "Guarantor",
    366031: "Guarantor capture",
    366032: "Credit Bureau Check",
    366033: "Approval",
    366034: "Disbursement",
    1: "Open",
    3: "Defaulted",
    44595: "Credit Counseling",
    44596: "Collection Agency",
    44597: "Sequestrate",
    44598: "Debt Review",
    44600: "Investigation",
    44599: "Fraud",
    44602: "Write-Off",
    44601: "Legal",
    255820: "Loan Closed in Error",
    255906: "Undisbursed Loan",
    255907: "Unsolicited",
    259988: "Multiple Disbursement",
    269041: "Treated",
    302204: "Not Taken up (Maybe)",
    310450: "Data Mismatch",
    9: "Denied",
    205879: "Loan Restructured",
    17: "Not Taken Up",
    216233: "Lost"
}
