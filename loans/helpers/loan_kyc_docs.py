import requests
import json


class DocumentationApiHandler:
    base_url = "https://savingsback.libertypayng.com/documentation/api/v1"
    url = "https://savingsback.libertypayng.com/documentation/api/v1/verify_user/"

    @classmethod
    def verify_user(cls, **payload):

        data = json.dumps(
            {
                "request_ref": payload["request_ref"],
                "entity_type": payload["entity_type"],
                "target_borrower_ref": payload["target_borrower_ref"],
                "bvn": payload["bvn"],
                "nin": payload["nin"],
                "id_type": payload["id_type"],
                "snap_img": payload["snap_img"],
                "agent_img": payload["agent_img"],
            }
        )
        headers = {"Content-Type": "application/json"}
        response = requests.request("POST", cls.url, headers=headers, data=data)
        return response

    @classmethod
    def get_verification_status(cls, verification_id):
        get_status_url = f"{cls.url}{verification_id}"

        payload = {}
        headers = {}

        response = requests.request("GET", get_status_url, headers=headers, data=payload)
        return response

    @classmethod
    def update_snap_img(cls, **payload):

        payload = json.dumps(
            {
                "request_ref": payload["request_ref"],
                "entity_type": payload["entity_type"],
                "snap_img": payload["snap_img"],
            }
        )
        headers = {"Content-Type": "application/json"}

        response = requests.request("PATCH", cls.url, headers=headers, data=payload)
        return response

    @classmethod
    def update_entity(cls, **payload):

        url = f"{cls.base_url}/entity_update/document"

        payload = json.dumps(
            {
                "request_ref": payload["request_ref"],
                "document_id": payload["document_id"],
                "id_type": payload["id_type"],
            }
        )
        headers = {"Content-Type": "application/json"}

        response = requests.request("PATCH", url, headers=headers, data=payload)

        return response
