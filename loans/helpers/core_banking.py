import json
import os
import uuid
import requests
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Dict

import redis
from decouple import config
from django.conf import settings
from requests import Response, exceptions, get, post

from helper_methods import format_api_response


class CoreBankingManager:
    headers = {
        "Content-Type": "application/json",
    }

    account_db = redis.StrictRedis(
        host="localhost",
        port=6379,
        db=7,
        decode_responses=True,
        encoding="utf-8",
    )

    if settings.ENVIRONMENT == "development":
        BASE_URL = "https://dev.banking.libertypayng.com"
        # BASE_URL = "https://dragon-humble-blatantly.ngrok-free.app"
        MODE = "TEST"
    else:
        BASE_URL = "https://banking.libertypayng.com"
        MODE = "LIVE"

    @classmethod
    def response_handler(cls, response: Response) -> Dict[str, Any]:
        """
        Handles the response from an API request.0
        Args:
            response (requests.Response): The response object from the API request.
        Returns:
            dict: A dictionary containing the status and response data.
        """
        try:
            data = {
                "status": True,
                "response": response.json(),
            }
        except json.JSONDecodeError:
            data = {
                "status": False,
                "response": response.content,
            }
        return data

    @staticmethod
    def request_result_handler(payload, response, url):
        """
        Handles the response from API requests, returning a structured result.

        Args:
            payload (dict): The data sent with the request.
            response (requests.Response): The response object returned by the API.
            url (str): The URL that was called.

        Returns:
            dict: A dictionary containing the request result, including status, response,
                  and other related information.
        """
        try:
            response = {
                "url": url,
                "status_code": response.status_code,
                "status": "success",
                "response": response.json(),
                "method": response.request.method,
                "payload": payload,
            }
        except (
            requests.exceptions.RequestException,
            Exception,
            json.decoder.JSONDecodeError,
        ) as e:
            response = {
                "url": url,
                "error": str(e),
                "status_code": response.status_code,
                "status": "failed",
                "method": response.request.method,
                "response": response.text,
                "payload": payload,
            }
        return response

    @classmethod
    def authenticate(cls):
        """
        Authenticates with the API to obtain a bearer token.
        Returns:
            requests.Response: The response object from the authentication request.
        """
        absolute_url = f"{cls.BASE_URL}/api/v1/companies/auth/login/"

        payload = json.dumps(
            {
                "email": settings.CORE_BANKING_USER,
                "password": settings.CORE_BANKING_PASS,
            }
        )

        # response = request(
        #     "POST",
        #     url=absolute_url,
        #     headers=cls.headers,
        #     data=payload,
        # )

        try:
            del cls.headers["Authorization"]
        except KeyError:
            pass

        response = post(
            url=absolute_url,
            headers=cls.headers,
            data=payload,
        )

        return response

    @classmethod
    def login(cls) -> str:
        """
        Logs in and retrieves a bearer token.
        Returns:
            str: The bearer token if successful, or None if the login fails.
        """
        bearer_token = cls.account_db.get("bearer_token")
        if bearer_token is None:
            authenticator = cls.authenticate()
            response = cls.response_handler(response=authenticator)
            if (
                response.get("status") is True
                and response.get("response").get("status_code") == 200
            ):
                bearer_token = response.get("response").get("data").get("access")
                cls.account_db.set(
                    "bearer_token", bearer_token, ex=timedelta(seconds=86400)
                )
                return bearer_token
            else:
                # return None
                raise ValueError("there is an issue logging in with CoreBanking")
        else:
            return bearer_token

    @classmethod
    def create_account(
        cls,
        first_name: str,
        last_name: str,
        middle_name: str,
        email: str,
        phone: str,
        bvn: str,
        date_of_birth: str,
    ):
        """
        Creates a virtual account.
        Returns:
            requests.Response: The response object from the account creation request.
        """
        absolute_url = f"{cls.BASE_URL}/api/v1/wema/app2/virtual_accounts/"

        bearer_token = cls.login()
        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        payload = json.dumps(
            {
                "first_name": first_name,
                "middle_name": middle_name,
                "last_name": last_name,
                "email": email,
                "phone": phone,
                "bvn": bvn,
                "date_of_birth": date_of_birth,
            }
        )

        try:

            response = post(
                url=absolute_url,
                headers=cls.headers,
                data=payload,
                timeout=10,
            )
            return response.json()
        except exceptions.RequestException as e:
            result = {"status": "failure", "status_code": 500, "data": {"message": e}}
            return result

    @classmethod
    def create_multiple_account(
        cls,
        first_name: str,
        last_name: str,
        middle_name: str,
        email: str,
        phone: str,
        bvn: str,
        date_of_birth: str,
    ):
        """
        Creates a virtual account.
        Returns:
            requests.Response: The response object from the account creation request.
        """
        absolute_url = f"{cls.BASE_URL}/api/v1/wema/virtual_accounts/"
        bearer_token = cls.login()

        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        payload = json.dumps(
            {
                "first_name": first_name,
                "middle_name": middle_name,
                "last_name": last_name,
                "email": email,
                "phone": phone,
                "bvn": bvn,
                "date_of_birth": date_of_birth,
            }
        )

        try:
            # response = request("POST", absolute_url, headers=cls.headers, data=payload, timeout=10)
            response = post(
                url=absolute_url,
                headers=cls.headers,
                data=payload,
                timeout=10,
            )
            return response.json()
        except exceptions.RequestException as e:
            result = {"status": "failure", "status_code": 500, "data": {"message": e}}
            return result

    @classmethod
    def create_wema_wallet(
        cls,
        first_name: str,
        last_name: str,
        middle_name: str,
        email: str,
        phone: str,
        bvn: str,
        date_of_birth: str,
    ):
        """
        Creates a virtual account.
        Returns:
            requests.Response: The response object from the account creation request.
        """
        absolute_url = f"{cls.BASE_URL}/api/v1/wema/virtual_accounts/"
        bearer_token = cls.login()

        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        payload = json.dumps(
            {
                "first_name": first_name,
                "middle_name": middle_name,
                "last_name": last_name,
                "email": email,
                "phone": phone,
                "bvn": bvn,
                "date_of_birth": date_of_birth,
            }
        )

        response = post(
            url=absolute_url,
            headers=cls.headers,
            data=payload,
            timeout=25,
        )

        return cls.request_result_handler(
            payload=payload, response=response, url=absolute_url
        )

    @classmethod
    def verify_transaction_reference(cls, reference: str):
        """
        Verifies Corebanking reference.
        """
        absolute_url = f"{cls.BASE_URL}/api/v1/wema/verify_event"

        # bearer_token = cls.authenticate()
        # response = cls.response_handler(response=bearer_token)
        # if response.get("status") is True:
        #     bearer_token = response.get("response").get("data").get("access")

        bearer_token = cls.login()
        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        params = {"reference": reference}

        try:
            # response = request("GET", absolute_url, headers=cls.headers, params=params, timeout=15)
            response = get(
                url=absolute_url,
                headers=cls.headers,
                params=params,
                timeout=15,
            )
            return response.json()
        except exceptions.RequestException as e:
            result = {"status": "failure", "status_code": 500, "data": {"message": e}}
            return result

    @classmethod
    def get_verification_result(cls, reference: str):
        """
        Verifies Corebanking reference.
        """
        absolute_url = f"{cls.BASE_URL}/api/v1/wema/verify_event"

        bearer_token = cls.login()
        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        params = {"session_id": reference}
        response = get(
            url=absolute_url,
            headers=cls.headers,
            params=params,
            timeout=15,
        )
        return format_api_response(
            payload="", response=response, url=absolute_url, params=params
        )

    @classmethod
    def transfer_from_wema(cls, wallet, data):
        url = f"{cls.BASE_URL}/accounts/transfer_money/"
        payload = {
            "source_account": config("WEMA_SOURCE_ACCOUNT"),
            "mode": cls.MODE,
            "account_name": data.get("account_name"),
            "account_number": data.get("account_number"),
            "bank_code": data.get("bank_code"),
            "request_reference": data.get("ref"),
            "amount": data.get("amount"),
            "narration": data.get("narration"),
        }
        headers = {
            "Authorization": f"Bearer {cls.login()}",
            "Content-Type": "application/json",
        }
        response = post(url, headers=headers, json=payload)
        return response

    @classmethod
    def verify_from_wema(cls, ref):
        url = f"{cls.BASE_URL}/accounts/verify_transfer?search={ref}"
        headers = {
            "Authorization": f"Bearer {cls.login()}",
            "Content-Type": "application/json",
        }
        response = get(url, headers=headers)
        data = response.json().get("data")
        transactions = data.get("transactions")
        response = {
            "status": False,
            "reversed": False,
            "transferred": False,
            "message": "",
            "data": None,
        }

        if len(transactions) > 0:
            res = transactions[0]

            if res.get("transaction_status", "").lower() == "successful":
                response = {
                    "status": True,
                    "reversed": False,
                    "transferred": True,
                    "message": "transfer successful",
                    "data": res,
                }
            elif res.get("transaction_status", "").lower() in ["failed", "reversed"]:
                response = {
                    "status": True,
                    "reversed": True,
                    "transferred": False,
                    "message": "transfer failed",
                    "data": res,
                }
            else:
                response = {
                    "status": False,
                    "reversed": False,
                    "transferred": False,
                    "message": "transaction status unknown",
                    "data": res,
                }

        else:
            response = {
                "status": False,
                "reversed": False,
                "transferred": False,
                "message": "transaction not found",
                "data": data,
            }

        return response

    @classmethod
    def check_account_details(cls):
        """
        Checks for the details of loans account.
        Returns:
            requests.Response: The response object from account details check.
        """
        absolute_url = f"{cls.BASE_URL}/accounts/details"
        bearer_token = cls.login()

        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        payload = {}

        try:
            response = get(
                url=absolute_url,
                headers=cls.headers,
                timeout=10,
            )
            return response.json()
        except exceptions.RequestException as e:
            result = {"status": "failure", "status_code": 500, "data": {"message": e}}
            return result

    @classmethod
    def get_cash_connect_company_account_details(cls):

        absolute_url = (
            f"{cls.BASE_URL}/api/v1/cash-connect/get-cash-connect-account-details/"
        )
        bearer_token = cls.login()

        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        payload = {}

        response = requests.request(
            "GET", absolute_url, headers=cls.headers, data=payload
        )

        return format_api_response(payload=payload, response=response, url=absolute_url)

    @classmethod
    def create_virtual_account_cash_connect(
        cls, first_name, last_name, phone_number, id_number, id_type, dob
    ):
        absolute_url = (
            f"{cls.BASE_URL}/api/v1/cash-connect/create-virtual-account-with-id/"
        )
        bearer_token = cls.login()

        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        payload = {}

        payload = json.dumps(
            {
                "first_name": first_name,
                "last_name": last_name,
                "phone_number": phone_number,
                "id_number": id_number,
                "type_of_id": id_type,
                "dob": dob,
            }
        )
        response = requests.request(
            "POST", absolute_url, headers=cls.headers, data=payload
        )
        return format_api_response(payload=payload, response=response, url=absolute_url)

    @classmethod
    def transfer_with_cash_connect(cls, data):
        absolute_url = f"{cls.BASE_URL}/api/v1/cash-connect/inter-transfer/"
        bearer_token = cls.login()

        if settings.ENVIRONMENT == "development":
            api_mode = "TEST"
        else:
            api_mode = "LIVE"

        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        payload = {}


        payload = json.dumps(
            {
                "transaction_reference": data.get("reference"),
                "amount": data.get("amount"),
                "beneficiary_account_number": data.get("account_number"),
                "beneficiary_account_name": data.get("account_name"),
                "beneficiary_bank_code": data.get("bank_code"),
                "narration": data.get("narration"),
                "mode": api_mode,
            }
        )

        try:
            response = requests.request(
                "POST", absolute_url, headers=cls.headers, data=payload
            )
            return {"status": True, "data": response.json()}
        except Exception as e:
            return {"status": False, "data": str(e)}

    @classmethod
    def verify_connect_transaction_status(cls, reference):
        absolute_url = f"{cls.BASE_URL}/api/v1/cash-connect/partner_disbursement_status/"
        bearer_token = cls.login()

        if settings.ENVIRONMENT == "development":
            api_mode = "TEST"
        else:
            api_mode = "LIVE"

        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        payload = {}

        payload = json.dumps(
            {
                "transaction_reference": reference
            }
        )

        try:
            response = requests.request(
                "POST", absolute_url, headers=cls.headers, data=payload
            )
        except Exception as e:
            return {
                "status": False,
                "data": str(e),
                "status_code": None,
                "transferred": False,
                "reversed": False
            }

        verf_data = response.json()
        if response.status_code == 200:
            if verf_data.get("data").get("responseMessage").lower() == "successful":
                response_data = {
                    "status": True,
                    "data": verf_data,
                    "status_code": verf_data.get("status_code"),
                    "transferred": True,
                    "reversed": False
                }
            elif verf_data.get("data").get("responseMessage").lower() == "failed":
                response_data = {
                    "status": True,
                    "data": verf_data,
                    "status_code": verf_data.get("status_code"),
                    "transferred": False,
                    "reversed": True
                }
            else:
                response_data = {
                    "status": False,
                    "data": verf_data,
                    "status_code": verf_data.get("status_code"),
                    "transferred": False,
                    "reversed": False
                }
        else:
            response_data = {
                "status": False,
                "data": verf_data,
                "status_code": verf_data.get("status_code"),
                "transferred": False,
                "reversed": False
            }

        return response_data
        