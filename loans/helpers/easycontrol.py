import json

import requests
from django.conf import settings
from django.core.cache import cache


class EasyCtrlMgr:
    BASE_URL = "https://mdm-platform.easycontrol.io/pub-mdm-platform/"
    OK_STATUS_CODE = 1000
    STATUS_CODE = 200
    LOGIN_NAME = settings.EASYCRTL_USERNAME
    PASSWORD = settings.EASYCRTL_PASSWORD

    @staticmethod
    def internal_server_error(error):
        """
        Returns an error response for internal server errors.
        """
        return {"error": {"code": 500, "message": "Internal Server Error", "details": str(error)}}

    @classmethod
    def get_token(cls):
        """
        Retrieves an authentication token from EasyControl API.
        """
        cache_token = cache.get(key=cls.PASSWORD)
        if cache_token:
            return cache_token

        url = f"{cls.BASE_URL}account/openLogin"
        payload = json.dumps({"loginName": cls.LOGIN_NAME, "password": cls.PASSWORD})
        headers = {"Content-Type": "application/json"}

        response = requests.post(url, headers=headers, data=payload)
        status_code = response.status_code

        try:
            data = response.json()
            response_code = data.get("code")
        except (requests.exceptions.RequestException, Exception) as err:
            return cls.internal_server_error(error=err)

        if status_code == cls.STATUS_CODE and response_code == cls.OK_STATUS_CODE:
            token = data.get("data")["token"]
            cache.set(key=cls.PASSWORD, value=token, timeout=60 * 15)  # last 15 minutes
            return token
        else:
            return data

    @classmethod
    def device_lookup(cls, imei=None, device_id=None):
        """
        Retrieves device details using either IMEI or device ID.

        :param imei: The IMEI number of the device (optional).
        :param device_id: The ID of the device (optional).
        :return: A list of device records or an error message.
        """
        url = f"{cls.BASE_URL}device/apiRegisterDevice"

        data = {
            "page": 0,
            "size": 10,
            "deviceId": device_id if device_id else "",
            "imei": imei if imei else "",
            "isCustom": False,
        }

        payload = json.dumps(data)
        token = cls.get_token()

        if isinstance(token, dict):
            return token

        headers = {"Content-Type": "application/json", "Authorization": f"Bearer {token}"}

        try:
            response = requests.post(url, headers=headers, data=payload)
            response.raise_for_status()
            data = response.json()
            response_code = data.get("code")
            return data
        except (requests.exceptions.RequestException, ValueError) as err:
            return cls.internal_server_error(error=err)

    @classmethod
    def query_all_group(cls):

        url = f"{cls.BASE_URL}groups/queryDeviceGroup"

        payload = ""
        token = cls.get_token()

        if isinstance(token, dict):
            return token

        headers = {"Content-Type": "application/json", "Authorization": f"Bearer {token}"}

        try:
            response = requests.request("GET", url, headers=headers, data=payload)
            response.raise_for_status()
            data = response.json()
            response_code = data.get("code")
            result = data.get("data")
            return result

        except (requests.exceptions.RequestException, ValueError) as err:
            return cls.internal_server_error(error=err)
    
    @classmethod
    def change_device_group(cls, device_id: str, new_group_id: int):

        url = f"{cls.BASE_URL}device/apiGroup"

        payload = json.dumps({"deviceId": f"{device_id}", "newDeviceGroupId": int(new_group_id)})
        token = cls.get_token()

        if isinstance(token, dict):
            return token

        headers = {"Content-Type": "application/json", "Authorization": f"Bearer {token}"}

        try:
            response = requests.request("PUT", url, headers=headers, data=payload)
            response.raise_for_status()
            data = response.json()
            response_code = data.get("code")
            return data

        except (requests.exceptions.RequestException, ValueError) as err:
            return cls.internal_server_error(error=err)
