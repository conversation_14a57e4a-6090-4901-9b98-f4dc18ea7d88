from ..models import AjoLoan

from ..helpers.helpers import check_is_holiday
from ..enums import LoanStatus, LoanPerformanceStatus

from datetime import datetime
import decimal

def create_or_update_missed_loan(loan: AjoLoan, model):
    if loan.status == LoanStatus.OPEN:
        missed_amount = loan.due_today - loan.total_repayment
    if loan.performance_status in [
        LoanPerformanceStatus.PAST_MATURITY,
        LoanPerformanceStatus.LOST,
        LoanPerformanceStatus.DEFAULTED
    ]:
        missed_amount = loan.repayment_amount - loan.total_repayment

    days = missed_amount/loan.daily_repayment_amount

    decimal_value = decimal.Decimal(str(days))

    rounded = decimal_value.quantize(decimal.Decimal("1"), rounding=decimal.ROUND_HALF_UP)

    escrow_value = loan.eligibility.saving.amount_saved

    is_holiday = check_is_holiday(date=datetime.now().date(), agent=loan.agent)

    if is_holiday:
        missed_repayments = model.objects.filter(loan=loan)
        missed_repayments.update(
            loan_supervisor=loan.checked_by,
            loan_officer=loan.agent,
            borrower=loan.borrower,
            borrower_phone=loan.borrower_phone_number,
            guarantor_phone=loan.guarantor_phone_number,
            missed_days=rounded,
            defaulting_days=loan.days_after_end,
            loan_amount=loan.amount,
            repayment_amount=loan.repayment_amount,
            missed_amount=missed_amount,
            paid_amount=loan.total_repayment,
            escrow_value=escrow_value,
            outstanding_loan_balance=loan.total_repayment - loan.repayment_amount,
            exposure=(loan.total_repayment - loan.repayment_amount) - escrow_value,
            principal_exposure=loan.amount - loan.total_repayment - escrow_value,
        )
        return

    model.objects.update_or_create(
        loan=loan,
        defaults={
            "loan_supervisor": loan.checked_by,
            "loan_officer": loan.agent,
            "borrower": loan.borrower,
            "borrower_phone": loan.borrower_phone_number,
            "guarantor_phone": loan.guarantor_phone_number,
            "missed_days": rounded,
            "defaulting_days": loan.days_after_end,
            "loan_amount": loan.amount,
            "repayment_amount": loan.repayment_amount,
            "missed_amount": missed_amount,
            "paid_amount": loan.total_repayment,
            "escrow_value": escrow_value,
            "outstanding_loan_balance": loan.total_repayment - loan.repayment_amount,
            "exposure": (loan.total_repayment - loan.repayment_amount) - escrow_value,
            "principal_exposure": loan.amount - loan.total_repayment - escrow_value,
        },
    )


def remove_missed_loan(loan: AjoLoan, model):
    model.objects.filter(loan=loan).delete()
