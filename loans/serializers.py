import json
from typing import Any, Dict, List

import pandas as pd
from django.core.cache import cache
from rest_framework import serializers

from ajo.models import AjoSepo
from ajo.payment_actions import check_if_agent_can_pay
from ajo.utils.general_utils import alt_format_phone_no, format_phone_number
from ajo.utils.otp_utils import verify_sms_voice_otp
from ajo.utils.ussd_utils import verify_ussd_otp
from loans.helpers.helpers import performance_checker
from loans.models import *
from loans.tasks import (
    notify_admin_borrower_img_mismatch,
    notify_admin_guarantor_img_mismatch,
)
from payment.checks import verify_transaction_pin
from .models import CreditWorthinessData

User = get_user_model()

environment = settings.ENVIRONMENT


class GetEligibleUserSerializer(serializers.Serializer):

    def to_representation(self, instance: LoanEligibility):
        representation = super().to_representation(instance)
        ajo_user: AjoUser = instance.ajo_user
        savings: AjoSaving = instance.saving

        total_eligible_amount = instance.amount
        total_amount_saved = savings.amount_saved

        representation["savings_id"] = instance.saving.id
        representation["has_documentation"] = instance.has_documentation
        representation["type"] = instance.loan_type
        representation["total_amount"] = instance.amount
        representation["business_suite"] = savings.business_suite
        representation["requires_approval"] = (
            instance.requires_approval and instance.approved is False
        )
        representation["active_savings"] = total_amount_saved
        representation["phone_number"] = ajo_user.phone
        representation["full_name"] = ajo_user.fullname

        if instance.eligibility_type == "TOP_UP" and not savings.tenure_str:
            try:
                existing_loan = AjoLoan.objects.get(id=instance.loan_topup_id)
            except AjoLoan.DoesNotExist:
                existing_loan = None
            
            if existing_loan:

                representation["savings_tenure"] = existing_loan.tenor
            else:
                representation["savings_tenure"] = savings.tenure_str
        else:
            representation["savings_tenure"] = savings.tenure_str

        representation["savings_eligibility"] = [
            {
                "id": instance.id,
                "eligibility_type": instance.eligibility_type,
                "eligible_amount": total_eligible_amount,
                "days_saved": savings.frequency_paid,
                "savings_name": savings.name,
                "amount_saved": total_amount_saved,
            }
        ]

        return representation


class SendVerificationOtpSerializer(serializers.Serializer):
    eligibility = serializers.CharField()

    def validate(self, data):
        request = self.context.get("request")
        user = request.user

        eligibility = data.get("eligibility")

        # agent_can_give_loan = performance_checker(agent=user)
        # if agent_can_give_loan is False:
        #     raise serializers.ValidationError("Loan processing cannot be completed due to poor repayment history")

        try:
            loan_eligibility = LoanEligibility.objects.get(id=eligibility, active=True)
            loan_eligibility.handle_eligibility_type_check()
        except LoanEligibility.DoesNotExist:
            raise serializers.ValidationError("Invalid eligibility request")
        except Exception as err:
            raise serializers.ValidationError(f"{err}")

        ajo_user = loan_eligibility.ajo_user

        try:
            ajo_user.validate_onboarding_stage()
        except NotImplementedError as err:
            raise serializers.ValidationError(str(err))

        agent = loan_eligibility.agent

        if agent != user:
            raise serializers.ValidationError("Invalid Agent")

        # user_branch = user.user_branch
        # if not user_branch:
        #     raise serializers.ValidationError("Kindly update Agent's branch")

        savings = loan_eligibility.saving

        if loan_eligibility.eligibility_type != "TOP_UP":

            if savings.amount_saved <= 0 or not savings.is_active:
                raise serializers.ValidationError(
                    "Savings eligibility is currently in-active. Savings closed"
                )

        data["loan_eligibility"] = loan_eligibility
        return data


class SendGuarantorVerificationOtpSerializer(serializers.Serializer):
    guarantor_phone_no = serializers.CharField(max_length=13, min_length=11)

    # def validate(self, data):

    #     return data


class LoanApplicationSerializer(serializers.Serializer):
    eligibility = serializers.CharField()
    loan_amount = serializers.FloatField()
    transaction_pin = serializers.CharField(max_length=4)
    loan_tenure = serializers.ChoiceField(
        choices=Tenure.choices, default=Tenure.ONE_MONTH
    )
    repayment_type = serializers.ChoiceField(
        choices=RepaymentFrequency.choices, default=RepaymentFrequency.DAILY
    )

    def validate(self, data):
        request = self.context.get("request")
        user = request.user
        eligibility = data.get("eligibility")
        loan_amount = data.get("loan_amount")
        loan_tenure = data.get("loan_tenure")
        const = ConstantTable.get_constant_table_instance()
        # print(data, "\n\n\n\n")
        try:
            eligibility_instance = LoanEligibility.objects.get(id=eligibility)
        except LoanEligibility.DoesNotExist:
            raise serializers.ValidationError("invalid Loan Eligibility")

        ajo_user = eligibility_instance.ajo_user

        try:
            ajo_user.validate_onboarding_stage()
        except NotImplementedError as err:
            raise serializers.ValidationError(str(err))

        if eligibility_instance.eligibility_type == EligibilityType.TOP_UP:
            raise serializers.ValidationError(
                "Cannot proceed with loan top eligibility type."
            )

        eligibility_type = eligibility_instance.loan_type
        savings_group = eligibility_instance.saving.group

        admin_min_loan_amount = const.min_loan_amount

        if eligibility_type in const.loan_types_minimum_required:
            if admin_min_loan_amount > eligibility_instance.amount:
                _savings_topup = eligibility_instance.calculate_savings_topup_amount()
                raise serializers.ValidationError(
                    f"Your eligible amount (₦{eligibility_instance.amount}) is below the minimum loan amount (₦{admin_min_loan_amount}). "
                    f"You need to save up at least ₦{_savings_topup} more to proceed with the loan application."
                )

        pending_documentation_status = [
            "PROCESSING",
            "PENDING",
            "COMPLETED",
            "INCOMPLETE",
            "FAILED",
            "SUCCESS",
        ]
        # check if savings has documentation
        kyc_queryset = LoanKYCDocumentation.objects.filter(
            savings=eligibility_instance.saving,
            documentation_status__in=pending_documentation_status,
        )
        if kyc_queryset.exists():
            raise serializers.ValidationError(
                "Your documentation is currently pending. To proceed with your loan, please complete the verification process for your documentation."
            )

        omit_processing_fee = ["BNPL", "BOOSTA_2X", "BOOSTA_2X_MINI", "CREDIT_HEALTH"]
        if savings_group is not None:
            raise serializers.ValidationError("Unable to Process Group Loan.")

        if (
            eligibility_instance.loan_type not in omit_processing_fee
            and loan_amount < 10000
        ):
            raise serializers.ValidationError("Minimum loan amount is N10,000")

        if (
            eligibility_instance.loan_type not in omit_processing_fee
            and loan_amount < 10000
        ):
            raise serializers.ValidationError("Minimum loan amount is N10,000")

        if not eligibility_instance.active:
            raise serializers.ValidationError("Saver is currently in-eligible")

        agent = eligibility_instance.agent
        if agent != user:
            raise serializers.ValidationError("Invalid Agent")

        # loan = AjoLoan.objects.filter(eligibility=eligibility_instance)
        ajo_user = eligibility_instance.ajo_user
        active_status = ["OPEN", "IN_PROGRESS", "DEFAULTED", "PROCESSING"]
        loan = AjoLoan.objects.filter(borrower=ajo_user, status__in=active_status)
        # print(loan)
        if loan.exists():
            raise serializers.ValidationError(f"Borrower has an active loan")
        if eligibility_type not in omit_processing_fee:
            # staff_eligibility_percent = const.staff_eligibility_percentage
            # savings_multiplier = const.savings_multiplier
            admin_cap_amount = const.max_loan_amount

            eligible_amount = eligibility_instance.amount

            loan_amount_cur = "NGN{:,}".format(int(loan_amount))
            eligible_amount_cur = "NGN{:,}".format(int(eligible_amount))
            admin_cap_amount_cur = "NGN{:,}".format(int(admin_cap_amount))
            amt_approved_cur = "NGN{:,}".format(
                int(eligibility_instance.amount_approved)
            )

            if loan_amount > eligible_amount:
                raise serializers.ValidationError(
                    f"Loan amount of {loan_amount_cur} exceeds the eligible amount of {eligible_amount_cur}. "
                    "Please request an amount within your eligibility."
                )

            elif (
                admin_cap_amount < eligible_amount <= loan_amount
                and eligibility_instance.approved is False
            ):
                raise serializers.ValidationError(
                    f"loan amount {loan_amount} exceeds max loan amount {admin_cap_amount}.Please contact admin"
                )
            if (
                eligibility_instance.approved is True
                and loan_amount > eligibility_instance.amount_approved
            ):
                raise serializers.ValidationError(
                    f"Loan amount of {loan_amount_cur} exceeds {amt_approved_cur} approved amount. "
                    "Please request an amount within your eligibility."
                )

        if eligibility_instance.verified_saver is False:
            raise serializers.ValidationError("Kindly verify saver's phone number")

        agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()

        if eligibility_instance.loan_type == LoanType.PROSPER_LOAN:
            is_prosper = True
        else:
            is_prosper = False

        interest_helper = AjoLoan.calculate_interest(
            principal_amount=loan_amount,
            is_prosper=is_prosper,
            loan_tenure=loan_tenure,
            savings_id=eligibility_instance.saving.id,
        )

        loan_processing_fee = interest_helper.get("loan_processing_fee", 89999999999999)
        health_insurance_fee = interest_helper.get(
            "health_insurance_fee", 89999999999999
        )
        insurance_duration = interest_helper.get("insurance_duration", 89999999999999)
        total_insurance_fee = health_insurance_fee * insurance_duration

        if const.charge_health_insurance_fee:
            # total_debit_amount = loan_processing_fee + total_insurance_fee
            total_debit_amount = (
                loan_processing_fee + const.health_insurance_activation_fee
            )
        else:
            total_debit_amount = loan_processing_fee

        if eligibility_type not in omit_processing_fee and not check_if_agent_can_pay(
            agent_wallet=agent_wallet, amount=total_debit_amount
        ):
            raise serializers.ValidationError("Insufficient wallet balance ")

        access_token = request.headers.get("Authorization", "").partition(" ")[-1]
        agent_transaction_pin = data.get("transaction_pin")

        verify_agent_pin = verify_transaction_pin(
            transaction_pin=agent_transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )

        if verify_agent_pin is True:
            pass
        else:
            raise serializers.ValidationError("Incorrect transaction pin")

        data["eligibility"] = eligibility_instance
        data["interest_rate"] = interest_helper.get("interest_rate")
        data["agent"] = agent
        data["interest_amount"] = interest_helper.get("interest_amount")
        data["repayment_amount"] = interest_helper.get("repayment_amount")
        data["daily_repayment_amount"] = interest_helper.get("daily_repayment_amount")
        data["tenor_in_days"] = interest_helper.get("tenor_in_days")
        data["loan_processing_fee"] = interest_helper.get("loan_processing_fee")
        return data


class VerifyLoanApplicantSerializer(serializers.Serializer):
    eligibility = serializers.CharField()
    otp = serializers.CharField(max_length=6, min_length=6)

    def validate(self, data):
        eligibility = data.get("eligibility")
        otp = data.get("otp")

        try:

            eligibility_instance = LoanEligibility.objects.get(id=eligibility)
        except LoanEligibility.DoesNotExist:
            raise serializers.ValidationError("Invalid eligibility details")

        ajo_user = eligibility_instance.ajo_user

        try:
            ajo_user.validate_onboarding_stage()
        except NotImplementedError as err:
            raise serializers.ValidationError(str(err))

        # saver_phone_number = eligibility_instance.ajo_user.phone_number
        saver_phone_number = eligibility_instance.ajo_user.phone

        if environment == "development":
            pass

        else:
            ussd_verification = verify_ussd_otp(
                otp=otp, phone_number=saver_phone_number
            )

            if ussd_verification is True:
                pass
            elif verify_sms_voice_otp(otp=otp, phone_number=saver_phone_number) is True:
                pass
            else:
                raise serializers.ValidationError("Invalid OTP or OTP Expired")

        data["eligibility_instance"] = eligibility_instance

        return data


class VerifyGuarantorOtpSerializer(serializers.Serializer):
    loan_id = serializers.CharField()
    otp = serializers.CharField(max_length=6, min_length=6)
    guarantor_phone_no = serializers.CharField(max_length=13, min_length=11)

    def validate(self, data):
        loan_id = data.get("loan_id")
        otp = data.get("otp")
        guarantor_phone_no = data.get("guarantor_phone_no")

        try:
            ajo_loan_instance = AjoLoan.objects.get(id=loan_id)
        except AjoLoan.DoesNotExist:
            raise serializers.ValidationError("Invalid loan details")

        ajo_user = ajo_loan_instance.borrower
        agent = ajo_user.user
        agent_phone = agent.user_phone
        borrower_phone = ajo_user.phone_number

        fmt_guarantor_phone_no = alt_format_phone_no(phone_number=guarantor_phone_no)
        if fmt_guarantor_phone_no == agent_phone:
            raise serializers.ValidationError(
                "Cannot user agent's phone number. Please provide separate phone number to proceed."
            )

        elif fmt_guarantor_phone_no == borrower_phone:
            raise serializers.ValidationError(
                "Cannot use borrower's phone number. Please provide separate phone number to proceed."
            )

        # print(fmt_guarantor_phone_no, agent_phone, borrower_phone)

        if environment == "development":
            pass

        else:
            ussd_verification = verify_ussd_otp(
                otp=otp, phone_number=guarantor_phone_no
            )

            if ussd_verification is True:
                pass
            elif verify_sms_voice_otp(otp=otp, phone_number=guarantor_phone_no) is True:
                pass
            else:
                raise serializers.ValidationError("Invalid OTP or OTP Expired")

        data["ajo_loan_instance"] = ajo_loan_instance
        data["fmt_guarantor_phone_no"] = fmt_guarantor_phone_no
        return data


class PayVerificationFeeSerializer(serializers.Serializer):
    loan_id = serializers.CharField()

    def validate(self, data):
        context = self.context
        extra_data = context.get("extra_data")
        request = extra_data.get("request")
        payment_type = extra_data.get("payment_type")

        agent = request.user
        loan_id = data.get("loan_id")
        loan = AjoLoan.objects.filter(
            agent=agent, id=loan_id, status=LoanStatus.PENDING
        )

        if not loan.exists():
            raise serializers.ValidationError(
                "Sorry, no loan record found for the provided details."
            )

        loan_instance = loan.last()

        if payment_type == "PROCESSING FEE":
            amount_to_charge = loan_instance.processing_fee
        elif payment_type == "LOAN INTEREST":
            amount_to_charge = loan_instance.interest_amount

        # check agent balance
        agent_wallet = AjoAgentSelector(user=agent).get_agent_ajo_wallet()

        if not check_if_agent_can_pay(
            agent_wallet=agent_wallet, amount=amount_to_charge
        ):
            raise serializers.ValidationError("Insufficient wallet balance ")

        data["loan_instance"] = loan_instance
        data["amount_to_charge"] = amount_to_charge
        return data


class LoanDisbursementSerializer(serializers.Serializer):
    loan_id = serializers.IntegerField()
    transaction_pin = serializers.CharField(max_length=4)
    new_savings = serializers.ChoiceField(choices=["yes", "no"])

    def validate(self, data):
        request = self.context.get("request")
        user = request.user
        # user = CustomUser.objects.filter(email="<EMAIL>").last()
        loan_id = data.get("loan_id")

        # agent_can_give_loan = performance_checker(agent=user)
        # if agent_can_give_loan is False:
        #     raise serializers.ValidationError(
        #         "Loan processing cannot be completed due to poor repayment history"
        #     )

        try:
            ajo_loan = AjoLoan.objects.get(id=loan_id)
        except AjoLoan.DoesNotExist:
            raise serializers.ValidationError(
                "Sorry, no loan record found for the provided details."
            )

        loan_status = ajo_loan.status
        disburse_status = ajo_loan.is_disbursed

        if disburse_status == True:
            raise serializers.ValidationError(
                f"Current loan has already been disbursed."
            )

        if loan_status != LoanStatus.APPROVED:
            raise serializers.ValidationError(
                f"Unable to proceed with loan disbursement. "
                f"Current loan status is {loan_status}"
            )

        eligibility = ajo_loan.eligibility
        savings = eligibility.saving
        eligibility_amount_saved = eligibility.amount_saved
        savings_group = savings.group

        if savings_group is not None:
            raise serializers.ValidationError("Unable to Disburse Group Loan.")

        access_token = request.headers.get("Authorization", "").partition(" ")[-1]
        agent_transaction_pin = data.get("transaction_pin")

        verify_agent_pin = verify_transaction_pin(
            transaction_pin=agent_transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )

        if verify_agent_pin is True:
            pass
        else:
            raise serializers.ValidationError("Incorrect transaction pin")

        data["savings"] = savings
        data["eligibility_amount_saved"] = eligibility_amount_saved
        data["loan_instance"] = ajo_loan
        return data


class BorrowerInfoSerializer(serializers.Serializer):
    loan = serializers.CharField()
    daily_income = serializers.FloatField(default=0.0)
    annual_shop_rent = serializers.FloatField(default=0.0)
    weekly_income = serializers.FloatField(default=0.0)
    verification_number = serializers.CharField(max_length=11, min_length=11)
    verification_type = serializers.ChoiceField(choices=VerificationType.choices)

    def validate(self, data):
        context = self.context
        # extra_data = context.get("extra_data")
        verification_number = data.get("verification_number")
        loan_id = data.get("loan")
        verification_type = data.get("verification_type")

        try:
            # ajo_loan = AjoLoan.objects.get(id=loan_id, processing_fee_charged=True)
            ajo_loan = AjoLoan.objects.get(id=loan_id)
        except AjoLoan.DoesNotExist:
            raise serializers.ValidationError("Invalid loan detail")
        borrower = ajo_loan.borrower

        try:
            borrower.validate_onboarding_stage()
        except NotImplementedError as err:
            raise serializers.ValidationError(str(err))

        you_verify = YouVerifyRequest.get_create_identity_result(
            id=verification_number,
            id_type=verification_type,
            ajo_user=borrower,
        )
        bvn_verification = you_verify.response
        status_code = bvn_verification.get("statusCode")
        verification_data = bvn_verification.get("data")
        status = verification_data.get("status")
        base_64_img_string = verification_data.get("image")
        ajo_user_nin = verification_data.get("nin")

        dateOfBirth = verification_data.get("dateOfBirth", None)
        last_name = verification_data.get("lastName", "") or ""
        first_name = verification_data.get("firstName", "") or ""
        middle_name = verification_data.get("middleName", "") or ""

        age = LoanGuarantor.calculate_age(birthdate=dateOfBirth)

        failed_status_code = ["402", "503", "500", "403"]

        if environment == "development":  # test data
            data["ajo_loan"] = ajo_loan
            data["age"] = age
            data["verification_data"] = verification_data
            data["bvn_verification"] = bvn_verification
            data["base_64_img_string"] = base_64_img_string
            data["ajo_user_nin"] = ajo_user_nin
            data["last_name"] = last_name
            data["date_of_birth"] = dateOfBirth
            return data

        if str(status_code) in failed_status_code:
            raise serializers.ValidationError(
                f"{verification_type} provider is currently not available."
            )

        if status_code != 200 and status != "found":
            raise serializers.ValidationError(
                f"Unable to fetch {verification_type} details. Kindly confirm {verification_type}."
            )

        alt_ver_type = "BVN" if verification_type == "BVN" else "NIN"

        if not dateOfBirth:
            raise serializers.ValidationError(
                f"Kindly provide an alternative supporting document to verify age"
            )

        const = ConstantTable.get_constant_table_instance()

        borrower_max_age = const.borrower_max_age
        borrower_min_age = const.borrower_min_age

        if age < borrower_min_age:
            raise serializers.ValidationError(
                f"Borrower must be at least {borrower_min_age} years old."
            )
        elif age > borrower_max_age:
            raise serializers.ValidationError(
                f"Borrower must be at most {borrower_max_age} years old."
            )

        borrower_info_on_guarantor = BorrowerInfo.is_borrower_info_on_loan_guarantor(
            last_name=last_name,
            first_name=first_name,
            middle_name=middle_name,
            date_of_birth=dateOfBirth,
        )
        _to_validate_reference = f"{last_name}{dateOfBirth}"
        reference_exist = BorrowerInfo.verification_reference_exists(
            reference=_to_validate_reference
        )
        # print(borrower_info_on_guarantor, "\n\n")
        if borrower_info_on_guarantor is True or reference_exist[0] is True:
            raise serializers.ValidationError(
                f"Guarantor or Borrower with the provided {verification_type.lower()} details exists."
            )
        borrower_info_instance = BorrowerInfo.objects.filter(
            verification_number=verification_number
        ).last()

        if borrower_info_instance:
            if ajo_loan.borrower != borrower_info_instance.borrower:
                raise serializers.ValidationError(
                    f"{verification_type.lower()}-{verification_number}"
                    f" has already been linked to another user."
                )

        # bvn_verification.get("data").pop("image")
        invalid_image_str = ["data:image/jpg;base64,***", "***"]

        if base_64_img_string in invalid_image_str:
            raise serializers.ValidationError(
                f"Kindly provide an alternative supporting document to verify image on {verification_type}"
            )

        borrower_first_name = (
            "" if not borrower.first_name else borrower.first_name.strip().upper()
        )

        borrower_last_name = (
            "" if not borrower.last_name else borrower.last_name.strip().upper()
        )

        name_list = [borrower_first_name, borrower_last_name]

        # Normalize input names
        normalized_first_name = first_name.strip().upper()
        normalized_last_name = last_name.strip().upper()
        normalized_middle_name = middle_name.strip().upper()

        # Check if names match
        name_match = (normalized_last_name and normalized_first_name in name_list) or (
            normalized_last_name and normalized_middle_name in name_list
        )

        if not name_match:
            borrower_fullname = f"{borrower_last_name} {borrower_first_name}"
            id_fullname = f"{last_name} {first_name}"
            raise serializers.ValidationError(
                f"Borrower's full name '{borrower_fullname}' does not match Verification ID's full name '{id_fullname}'."
            )

        data["ajo_loan"] = ajo_loan
        data["age"] = age
        data["verification_data"] = verification_data
        data["bvn_verification"] = bvn_verification
        data["base_64_img_string"] = base_64_img_string
        data["ajo_user_nin"] = ajo_user_nin
        data["last_name"] = last_name
        data["date_of_birth"] = dateOfBirth
        return data


class RepaymentWalletInfoSerializer(serializers.ModelSerializer):
    phone_number = serializers.CharField()


class GuarantorViewSerializer(serializers.Serializer):
    loan_id = serializers.CharField()
    surname = serializers.CharField()
    last_name = serializers.CharField()
    email = serializers.EmailField(required=False, allow_blank=True, allow_null=True)
    verification_type = serializers.ChoiceField(choices=VerificationType.choices)
    preferred_verification = serializers.ChoiceField(
        choices=PreferredVerification.choices
    )
    guarantor_phone_number = serializers.CharField()
    verification_number = serializers.CharField(max_length=11, min_length=11)

    def validate(self, data):
        # context = self.context
        # extra_data = context.get("extra_data")
        verification_number = data.get("verification_number")
        loan_id = data.get("loan_id")
        verification_type = data.get("verification_type")
        guarantor_phone_number = data.get("guarantor_phone_number")

        try:
            ajo_loan = AjoLoan.objects.get(id=loan_id)
        except AjoLoan.DoesNotExist:
            raise serializers.ValidationError("Invalid loan detail")

        if ajo_loan.face_match is False:
            raise serializers.ValidationError("Borrower is not verified")
        borrower = ajo_loan.borrower

        guarantor_has_active_loan = AjoLoan.loan_guarantor_exist(
            guarantor_phone=guarantor_phone_number,
            guarantor_nin_bvn=verification_number,
            borrower=borrower,
        )

        if guarantor_has_active_loan:
            raise serializers.ValidationError(f"Guarantor has an active loan")

        borrower_info_qs = BorrowerInfo.objects.filter(nin=verification_number)

        if borrower_info_qs.exists():
            # borrower_info_instance = borrower_info_qs.last()
            raise serializers.ValidationError(
                f"Borrower with NIN {verification_number} already exist."
            )
        else:

            try:
                borrower_info_instance = BorrowerInfo.objects.get(loan=ajo_loan)
            except BorrowerInfo.DoesNotExist:
                raise serializers.ValidationError(
                    "Borrower has not being linked to a loan."
                )

        if borrower_info_instance.is_verified is False:
            raise serializers.ValidationError("Borrower is not verified")

        const = ConstantTable.get_constant_table_instance()

        if (
            const.use_guarantor_verification is False
            and ajo_loan.loan_type == LoanType.BNPL
        ):
            data["ajo_loan"] = ajo_loan
            data["borrower_info_instance"] = borrower_info_instance
            # data["verification_data"] = {}
            # data["nin_verification"] = {}
            # data["base_64_img_string"] = ""
            # data["base_64_signature_string"] = ""
            # data["age"] = ""
            # data["dateOfBirth"] = ""
            # data["nin_last_name"] = ""
            # data["verification_id_phone_no"] = ""
            # data["verification_id_name"] = ""
            return data

        you_verify = YouVerifyRequest.get_create_identity_result(
            id=verification_number,
            id_type=verification_type,
            ajo_user=ajo_loan.borrower,
        )

        nin_verification = you_verify.response
        status_code = nin_verification.get("statusCode")
        verification_data = nin_verification.get("data")
        status = verification_data.get("status")
        base_64_img_string = verification_data.get("image")
        base_64_signature_string = verification_data.get("signature")

        failed_status_code = ["402", "503", "500", "403"]

        if str(status_code) in failed_status_code:
            raise serializers.ValidationError(
                f"{verification_type} provider is currently not available."
            )

        if status_code != 200:
            raise serializers.ValidationError(
                f"Unable to fetch {verification_type} details."
                f"Kindly confirm {verification_type}."
            )
        if status != "found":
            raise serializers.ValidationError(
                f"Unable to fetch {verification_type} details."
                f"Kindly confirm {verification_type}."
            )

        dateOfBirth = verification_data.get("dateOfBirth", None)
        verification_id_last_name = verification_data.get("lastName", None)
        verification_id_first_name = verification_data.get("firstName", None)
        middle_name = verification_data.get("middle_name", None)
        verification_id_phone_no = verification_data.get("mobile", None)

        # alt_ver_type = "BVN" if verification_type == "BVN" else "NIN"
        if not dateOfBirth:
            # raise serializers.ValidationError(f"Age not found.")
            raise serializers.ValidationError(
                f"Kindly provide an alternative supporting document to verify age"
            )

        age = LoanGuarantor.calculate_age(birthdate=dateOfBirth)

        const = ConstantTable.get_constant_table_instance()
        guarantor_max_age = const.guarantor_max_age
        guarantor_min_age = const.guarantor_min_age

        guaranto_details_on_borrower_info = (
            LoanGuarantor.is_guarantor_details_on_borrower_info(
                last_name=verification_id_last_name,
                first_name=verification_id_first_name,
                middle_name=middle_name,
                date_of_birth=dateOfBirth,
            )
        )

        _to_validate_reference = f"{verification_id_last_name}{dateOfBirth}"

        reference_exist = LoanGuarantor.verification_reference_exists(
            reference=_to_validate_reference
        )
        # print(guaranto_details_on_borrower_info, "\n\n")
        if guaranto_details_on_borrower_info is True or reference_exist[0] is True:
            raise serializers.ValidationError(
                f"Guarantor or Borrower with the provided {verification_type.lower()} details exists."
            )

        if age < guarantor_min_age:
            raise serializers.ValidationError(
                f"Guarantor must be at least {guarantor_min_age} years old."
            )
        elif age > guarantor_max_age:
            raise serializers.ValidationError(
                f"Guarantor must be at most {guarantor_max_age} years old."
            )

        # nin_verification.get("data").pop("image")

        if base_64_signature_string:
            nin_verification.get("data").pop("signature")

        invalid_image_str = ["data:image/jpg;base64,***", "***"]

        if base_64_img_string in invalid_image_str:
            raise serializers.ValidationError(
                f"Kindly provide an alternative supporting document to verify image on {verification_type}"
            )

        data["ajo_loan"] = ajo_loan
        data["borrower_info_instance"] = borrower_info_instance
        data["verification_data"] = verification_data
        data["nin_verification"] = nin_verification
        data["base_64_img_string"] = base_64_img_string
        data["base_64_signature_string"] = base_64_signature_string
        data["age"] = age
        data["dateOfBirth"] = dateOfBirth
        data["nin_last_name"] = verification_id_last_name
        data["verification_id_phone_no"] = verification_id_phone_no
        data["verification_id_name"] = (
            f"{verification_id_last_name} {verification_id_first_name}"
        )
        return data


class LoanHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = AjoLoan
        fields = "__all__"

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        try:
            borrower_info = BorrowerInfo.objects.get(loan=instance)
        except BorrowerInfo.DoesNotExist:
            borrower_info = None

        borrower_id = None
        credit_bureau_data = {}

        if borrower_info:
            borrower_id = borrower_info.id
            try:
                credit_bureau_data = json.loads(borrower_info.credit_bureau)
            except:
                credit_bureau_data = (
                    eval(borrower_info.credit_bureau)
                    if borrower_info.credit_bureau
                    else None
                )
        repayment_qs = AjoLoanRepayment.objects.filter(ajo_loan=instance)
        total_paid = (
            repayment_qs.aggregate(total_paid=Sum("repayment_amount"))["total_paid"]
            or 0
        )

        representation["user_name"] = instance.user_name
        representation["user_phone_no"] = instance.user_phone_no
        representation["borrower_info"] = borrower_id
        representation["borrower_info_data"] = credit_bureau_data
        representation["repaid"] = total_paid
        representation["missed_repayments_count"] = int(instance.outstanding_days)
        representation["amount_due"] = instance.outstanding_due
        representation["branch"] = ""
        representation["agent_name"] = instance.agent.first_name
        return representation


class GetLoanAmountSummarySerializer(serializers.Serializer):
    loan_amount = serializers.FloatField()
    stock_price = serializers.FloatField(default=0.0, allow_null=True)
    savings_created = serializers.BooleanField(default=True)
    savings_id = serializers.CharField(
        allow_blank=True, allow_null=True, required=False
    )
    loan_tenure = serializers.ChoiceField(
        choices=Tenure.choices, default=Tenure.ONE_MONTH
    )

    def validate(self, attrs):
        loan_request_amount = attrs.get("loan_amount")
        loan_tenure = attrs.get("loan_tenure")
        const = ConstantTable.get_constant_table_instance()
        if loan_request_amount < 10000:
            raise serializers.ValidationError("Minimum loan amount is N10,000.")

        loan_tenure_value = Tenure.match_value_get_days(loan_tenure)
        usable_const = const.staff_loans_config.get(str(loan_tenure_value), False)

        if not usable_const:
            split_tenure = loan_tenure.split("_")
            raise serializers.ValidationError(
                f"Sorry, the {split_tenure[0]}-{split_tenure[1]} loan tenure isn't available at the moment."
            )
        return attrs


class BorrowerInfoStatusSerializer(serializers.Serializer):
    session_id = serializers.CharField(max_length=300)


class AjoLoanCalculatorSerializer(serializers.Serializer):
    loan_request_amount = serializers.IntegerField()

    def validate(self, attrs):
        loan_request_amount = attrs.get("loan_request_amount")

        if loan_request_amount < 5000:
            raise serializers.ValidationError("Minimum loan amount is 5000")
        return attrs


class RepaymentHookSerializer(serializers.Serializer):
    company = serializers.CharField()
    # sub_company_email = serializers.CharField()
    reference = serializers.CharField()
    recipient_account_name = serializers.CharField()
    recipient_account_number = serializers.CharField()
    amount = serializers.FloatField()
    payer_account_name = serializers.CharField(allow_blank=True, allow_null=True)
    payer_account_number = serializers.CharField(allow_blank=True, allow_null=True)
    payer_bank_code = serializers.CharField(allow_blank=True, allow_null=True)
    paid_at = serializers.CharField()
    narration = serializers.CharField()
    transaction_reference = serializers.CharField()
    session_id = serializers.CharField()
    fee = serializers.CharField()
    amount_payable = serializers.CharField()
    settlement_status = serializers.BooleanField()
    currency = serializers.CharField()


class RepaymentWalletDetailsSerializer(serializers.Serializer):
    loan_id = serializers.IntegerField()
    amount = serializers.FloatField()
    transaction_pin = serializers.CharField(max_length=4)

    def validate(self, data):
        request = self.context.get("request")
        loan_id = data.get("loan_id")
        amount = data.get("amount")
        # user = CustomUser.objects.filter(email="<EMAIL>").last()
        user = request.user

        try:
            ajo_loan = AjoLoan.objects.get(id=loan_id)
        except AjoLoan.DoesNotExist:
            raise serializers.ValidationError({"error": "Invalid loan detail"})

        if ajo_loan.status != LoanStatus.OPEN:
            raise serializers.ValidationError(
                {"error": f"Current loan is being {ajo_loan.status}"}
            )

        agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()
        if not check_if_agent_can_pay(agent_wallet=agent_wallet, amount=amount):
            raise serializers.ValidationError(
                {"error": "Insufficient balance, Agent should top up wallet"}
            )

        access_token = request.headers.get("Authorization", "").partition(" ")[-1]
        agent_transaction_pin = data.get("transaction_pin")

        verify_agent_pin = verify_transaction_pin(
            transaction_pin=agent_transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )
        if verify_agent_pin is True:
            pass
        else:
            raise serializers.ValidationError("Incorrect transaction pin")

        data["loan"] = ajo_loan
        data["amount"] = amount
        data["agent_wallet"] = agent_wallet

        return data


class FaceMatchSerializer(serializers.Serializer):
    borrower_info = serializers.CharField()
    image = serializers.ImageField()

    def validate(self, data):
        borrower_info = data.get("borrower_info")
        image = data.get("image")
        try:
            borrower_info_instance = BorrowerInfo.objects.get(id=borrower_info)
        except BorrowerInfo.DoesNotExist:
            raise serializers.ValidationError("Invalid Borrower")

        ajo_user = borrower_info_instance.borrower
        loan_instance = borrower_info_instance.loan
        agent = ajo_user.user
        agent_email = agent.email

        full_name = ajo_user.fullname
        age = borrower_info_instance.age

        ######################################################################################
        if environment == "development":  # development
            borrower_info_instance.face_match = True
            borrower_info_instance.notified = True
            borrower_info_instance.save()
            loan_instance.face_match = True
            loan_instance.save()
            data["borrower_info_instance"] = borrower_info_instance
            return data
        ######################################################################################

        is_collector = BorrowerInfo.is_agent_as_borrower(
            model_instance=borrower_info_instance
        )
        if is_collector:
            raise serializers.ValidationError(
                "We're sorry, but based on our assessment, collectors are not qualified for loans at this time."
            )
        snapped_image = None
        if borrower_info_instance.face_match is False:

            biometrics_meta_result = BorrowerInfo.liveness(
                borrower_info=borrower_info_instance, face=image
            )
            image_liveliness = biometrics_meta_result[0]
            status = image_liveliness.get("status")

            if status == "success":
                data = image_liveliness.get("data").get("data")

                if data.get("result") is False:
                    # notify_admin_borrower_img_mismatch.delay(
                    #     subject="Notice: Borrower Image Liveness Verification Failure",
                    #     agent_email=agent_email,
                    #     full_name=full_name,
                    #     age=age,
                    # )

                    raise serializers.ValidationError(
                        "Please ensure that you capture a live image using your device's camera."
                        " Avoid using pictures or images from cards. If you continue to face issues, "
                        "please contact support for assistance."
                    )
            else:
                raise serializers.ValidationError("Service Temporarily unavailable")

            facial_biometrics_response = BorrowerInfo.facial_biometrics(
                borrower_info=borrower_info_instance,
                face_two_bs64=biometrics_meta_result[1].base64_img_str,
            )
            snapped_image = biometrics_meta_result[1].base64_img_str

            status = facial_biometrics_response.get("status")

            if status == "success":
                data = facial_biometrics_response.get("data").get("data")
                similarity = data.get("similarity", 0)

                if data.get("result") is False or similarity < 0.8:

                    # notify_admin_borrower_img_mismatch.delay(
                    #     subject="Notice: Borrower's Face mismatch",
                    #     agent_email=agent_email,
                    #     full_name=full_name,
                    #     age=age,
                    # )
                    raise serializers.ValidationError(
                        "Face does not match the face on the National Identification Number (NIN)"
                    )
            else:
                raise serializers.ValidationError("Service Temporarily unavailable")

            if not agent_email.startswith("liberty"):

                agent_facial_verification = BorrowerInfo.is_agent(
                    model_instance=borrower_info_instance
                )
                if not borrower_info_instance.confirmed_agent_not_borrower:
                    if not isinstance(agent_facial_verification, dict):
                        raise serializers.ValidationError(
                            "Unable to verify agent image.Please contact admin"
                        )
                    else:
                        status = agent_facial_verification.get("status")
                        if status == "success":
                            data = agent_facial_verification.get("data").get("data")
                            similarity = data.get("similarity", 0)

                            if similarity > 0.8:
                                raise serializers.ValidationError(
                                    "We're sorry, but based on our assessment, agents are not qualified for loans at this time."
                                )
                        else:
                            raise serializers.ValidationError(
                                "Service Temporarily unavailable"
                            )

        borrower_info_instance.face_match = True
        borrower_info_instance.confirmed_agent_not_borrower = True
        borrower_info_instance.notified = True
        borrower_info_instance.snapped_image = snapped_image
        borrower_info_instance.save()
        loan_instance.face_match = True
        loan_instance.save()
        ajo_user.update_user_image_from_base64(base64_img_str=snapped_image)
        data["borrower_info_instance"] = borrower_info_instance
        return data


class CheckSingleOrMultipleEligibility(serializers.Serializer):
    loan_type = serializers.ChoiceField(required=True, choices=LoanType.choices)
    savings = serializers.PrimaryKeyRelatedField(
        required=False, queryset=AjoSaving.objects.all(), many=True
    )


class MiniLoanEligibilitySerializer(serializers.ModelSerializer):
    class Meta:
        model = LoanEligibility
        fields = [
            "id",
            "agent",
            "ajo_user",
            "saving",
            "loan_type",
            "amount",
            "amount_saved",
            "percentage_saved",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        ajo_user: AjoUser = instance.ajo_user
        savings: AjoSaving = instance.saving

        total_eligible_amount = instance.amount
        total_amount_saved = savings.amount_saved

        representation["savings_id"] = instance.saving.id
        representation["has_documentation"] = instance.has_documentation
        representation["total_amount"] = instance.amount
        representation["active_savings"] = total_amount_saved
        representation["phone_number"] = ajo_user.phone_number
        representation["full_name"] = ajo_user.fullname
        representation["agent_name"] = instance.agent.email
        representation["savings_eligibility"] = [
            {
                "id": instance.id,
                "eligible_amount": total_eligible_amount,
                "days_saved": savings.frequency_paid,
                "savings_name": savings.name,
                "amount_saved": total_amount_saved,
            }
        ]

        return representation


class GuarantorFaceMatchSerializer(serializers.Serializer):
    loan_id = serializers.CharField()
    guarantor_id = serializers.CharField()
    image = serializers.ImageField()

    def validate(self, data):
        loan_id = data.get("loan_id")
        guarantor_id = data.get("guarantor_id")
        image = data.get("image")

        try:
            ajo_loan = AjoLoan.objects.get(id=loan_id)
        except AjoLoan.DoesNotExist:
            raise serializers.ValidationError("Invalid loan detail")

        if ajo_loan.face_match is False:
            raise serializers.ValidationError("Borrower is not verified")
        try:
            loan_guarantor = LoanGuarantor.objects.get(id=guarantor_id)
        except LoanGuarantor.DoesNotExist:
            raise serializers.ValidationError("Invalid Guarantor request")

        is_collector = BorrowerInfo.is_agent_as_borrower(model_instance=loan_guarantor)

        if is_collector:
            raise serializers.ValidationError(
                "We're sorry, but based on our assessment, collectors are not qualified to stand as guarantor for a loan."
            )

        if loan_guarantor.is_verified and loan_guarantor.borrower != ajo_loan.borrower:
            raise serializers.ValidationError(
                "This guarantor is already verified and active for another borrower."
            )

        try:
            borrower_info_instance = BorrowerInfo.objects.get(loan=loan_id)
        except BorrowerInfo.DoesNotExist:
            raise serializers.ValidationError("Loan is not matched with a borrower")

        biometrics_meta_result = LoanGuarantor.liveness(
            borrower_info=borrower_info_instance,
            loan_guarantor_instance=loan_guarantor,
            face=image,
        )

        image_liveliness = biometrics_meta_result[0]
        status = image_liveliness.get("status")
        snapped_image = None
        if status == "success":
            data = image_liveliness.get("data").get("data")
            if data.get("result") is False:

                raise serializers.ValidationError(
                    "Please ensure that you capture a live image using your device's camera."
                    "Avoid using pictures or images from cards. If you continue to face issues, "
                    "please contact support for assistance."
                )
        else:
            raise serializers.ValidationError("Service Temporarily unavailable")

        facial_biometrics_response = LoanGuarantor.facial_biometrics(
            loan_guarantor_instance=loan_guarantor,
            borrower_info=borrower_info_instance,
            face_two_bs64=biometrics_meta_result[1].base64_img_str,
        )
        snapped_image = biometrics_meta_result[1].base64_img_str
        status = facial_biometrics_response.get("status")

        if status == "success":
            data = facial_biometrics_response.get("data").get("data")
            similarity = data.get("similarity", 0)
            if data.get("result") is False or similarity < 0.8:

                raise serializers.ValidationError(
                    "Face does not match the face on the National Identification Number (NIN)"
                )
        else:
            raise serializers.ValidationError("Service Temporarily unavailable")

        agent_facial_verification = BorrowerInfo.is_agent(
            model_instance=loan_guarantor, guarantor=True
        )
        if not loan_guarantor.confirmed_agent_not_guarnator:
            if not isinstance(agent_facial_verification, dict):
                raise serializers.ValidationError(
                    "Unable to verify agent image.Please contact admin"
                )
            else:
                status = agent_facial_verification.get("status")
                if status == "success":
                    data = agent_facial_verification.get("data").get("data")
                    similarity = data.get("similarity", 0)
                    # result = data.get("result")
                    if similarity > 0.8:
                        raise serializers.ValidationError(
                            "We're sorry, but based on our assessment, agents cannot stand as guarantors for loans."
                        )
                else:
                    raise serializers.ValidationError("Service Temporarily unavailable")

        loan_guarantor.confirmed_agent_not_guarnator = True
        loan_guarantor.save(update_fields=["confirmed_agent_not_guarnator"])
        data["borrower_info_instance"] = borrower_info_instance
        data["ajo_loan"] = ajo_loan
        data["loan_guarantor"] = loan_guarantor
        data["environment"] = environment
        data["snapped_image"] = snapped_image
        return data


class ProsperAgentSerializer(serializers.ModelSerializer):
    amount_left_to_disburse = serializers.SerializerMethodField()
    monthly_disbursable = serializers.SerializerMethodField()

    class Meta:
        model = ProsperAgent
        exclude = ["unique_reference"]

    def get_monthly_disbursable(self, inst: ProsperAgent):
        return inst.amount_left_to_disburse

    def get_amount_left_to_disburse(self, inst: ProsperAgent):
        return inst.year_disbursable


class CreditBureauCheckSerializer(serializers.Serializer):
    bvn = serializers.CharField()
    phone_number = serializers.CharField()

    def validate(self, data):
        request = self.context.get("request")
        user = request.user
        phone_number = data.get("phone_number")
        agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()

        constant_model_instance = ConstantTable.get_constant_table_instance()
        charge_amount = constant_model_instance.credit_bureau_check_fee

        try:
            ajo_user = AjoUserSelector(
                user=user, phone_number=phone_number
            ).get_ajo_user()
        except ValueError:
            raise serializers.ValidationError("Ajo user was not found")

        if not check_if_agent_can_pay(agent_wallet=agent_wallet, amount=charge_amount):
            raise serializers.ValidationError("Insufficient wallet balance")
        data["ajo_user"] = ajo_user
        data["charge_amount"] = charge_amount
        data["agent_wallet"] = agent_wallet
        return data


class StaffLoanEligibilitySummarySerializer(serializers.Serializer):
    email = serializers.EmailField()
    # use_id = serializers.CharField(required=False)
    savings_id = serializers.CharField(required=True)

    def validate(self, data):
        # email = data.get("email")
        # user_id = data.get("user_id")
        savings_id = data.get("savings_id")
        try:
            saving = AjoSaving.objects.get(id=savings_id)
        except AjoSaving.DoesNotExist:
            raise serializers.ValidationError("Invalid savings id...")
        data["saving"] = saving
        return data


class DueLoanRepaymentSerializer(serializers.Serializer):
    class Meta:
        model = AjoLoan
        # fields = "__all__"

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        borrower = instance.borrower
        # today = timezone.now().date()
        # loan_start_date: datetime = instance.start_date

        # day_difference = (today - loan_start_date).days
        # day_count = day_difference if day_difference > 0 else 0

        # daily_repayment_amount = instance.daily_repayment_amount
        # amount_due = daily_repayment_amount * day_count
        # count_repayment_qs = repayment_qs.count()

        # expected_repayment_amount = instance.repayment_amount

        repayment_qs = AjoLoanRepayment.objects.filter(ajo_loan=instance)
        total_paid = (
            repayment_qs.aggregate(total_paid=Sum("repayment_amount"))["total_paid"]
            or 0
        )
        representation["loan_id"] = instance.id
        representation["loan_amount"] = instance.amount
        representation["borrower_full_name"] = borrower.fullname
        representation["phone_number"] = borrower.phone
        # representation["status"] = ""
        representation["repaid"] = total_paid
        representation["missed_repayments_count"] = int(instance.outstanding_days_today)
        representation["amount_due"] = instance.outstanding_due_today
        return representation


class UpdateBadLoanToGuarantorSerializer(serializers.Serializer):
    loan_id = serializers.CharField()
    proceed = serializers.BooleanField()

    def validate(self, data):
        loan_id = data.get("loan_id")
        try:
            loan_instance = AjoLoan.objects.get(id=loan_id)
        except AjoLoan.DoesNotExist:
            raise serializers.ValidationError("Invalid loan id...")

        loan_verification_stage = loan_instance.verification_stage
        warning_verification_stage = VerificationStage.WARNING
        if not loan_verification_stage == warning_verification_stage:
            raise serializers.ValidationError(
                f"Kindly confirm that loan verification stage is {warning_verification_stage.lower()}."
            )

        data["loan_instance"] = loan_instance
        return data


class SendOtpLoanKYCDocumentationSerializer(serializers.Serializer):
    savings_id = serializers.CharField()

    def validate(self, data):
        request = self.context.get("request")
        agent = request.user
        savings_id = data.get("savings_id")

        try:
            savings_instance = AjoSaving.objects.get(id=savings_id, user=agent)
        except AjoSaving.DoesNotExist:
            raise serializers.ValidationError("Invalid savings request")

        ajo_user = savings_instance.ajo_user

        try:
            ajo_user.validate_onboarding_stage()
        except NotImplementedError as err:
            raise serializers.ValidationError(str(err))

        data["savings_instance"] = savings_instance
        return data


class VerifyLoanKYCDocumentationSerializer(serializers.Serializer):
    savings_id = serializers.CharField()
    otp = serializers.CharField(max_length=6, min_length=6)

    def validate(self, data):
        savings_id = data.get("savings_id")
        otp = data.get("otp")
        try:
            savings_instance = AjoSaving.objects.get(id=savings_id)
        except AjoSaving.DoesNotExist:
            raise serializers.ValidationError("Invalid savings")

        # saver_phone_number = savings_instance.ajo_user.phone_number
        saver_phone_number = savings_instance.ajo_user.phone
        ajo_user = savings_instance.ajo_user

        try:
            ajo_user.validate_onboarding_stage()
        except NotImplementedError as err:
            raise serializers.ValidationError(str(err))

        if environment == "development":
            pass
        else:
            ussd_verification = verify_ussd_otp(
                otp=otp, phone_number=saver_phone_number
            )

            if (
                ussd_verification is True
                or verify_sms_voice_otp(otp=otp, phone_number=saver_phone_number)
                is True
            ):
                pass
            else:
                raise serializers.ValidationError("Invalid OTP or OTP Expired")

        data["savings_instance"] = savings_instance
        return data


class LoanKYCDocumentationSerializer(serializers.Serializer):

    # id = serializers.IntegerField(required=False, allow_null=True)
    savings = serializers.CharField(required=False, allow_null=True)
    # unique_ref = serializers.CharField(required=False, allow_null=True, allow_blank=False)
    borrower_phone_number = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    borrower_annual_shop_rent = serializers.FloatField(required=False, allow_null=True)
    borrower_email = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    borrower_daily_income = serializers.FloatField(required=False, allow_null=True)
    borrower_weekly_income = serializers.FloatField(required=False, allow_null=True)
    borrower_verification_number = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    borrower_verification_type = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    borrower_base_64_capture = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    guarantor_first_name = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    guarantor_last_name = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    guarantor_email = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    guarantor_phone_number = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    guarantor_verification_number = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    guarantor_verification_type = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    guarantor_relationship_to_borrower = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    guarantor_base_64_capture = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )

    def validate(self, data):
        request = self.context.get("request")
        # collector = request.user
        # phone_number = data.get("borrower_phone_number")
        savings = data.get("savings")
        verification_key = f"savings{savings}loanKYC"
        is_verified = cache.get(key=verification_key)

        try:
            savings = AjoSaving.objects.get(id=savings)
        except AjoSaving.DoesNotExist:
            raise serializers.ValidationError(f"Invalid savings")

        ajo_user = savings.ajo_user
        try:
            ajo_user.validate_onboarding_stage()
        except NotImplementedError as err:
            raise serializers.ValidationError(str(err))

        savings_type = savings.savings_type
        boosta_2x_plans = ["BOOSTA_2X", "BOOSTA_2X_MINI"]
        if savings_type in boosta_2x_plans:
            plan_type = savings_type.replace("_", " ")
            raise serializers.ValidationError(
                f"Unable to create documentation for {plan_type}."
            )

        if not is_verified:
            if not LoanKYCDocumentation.objects.filter(savings=savings).exists():
                raise serializers.ValidationError(
                    "Saver not verified for Loan KYC Documentation"
                )

        documentation_status = ["COMPLETED", "INCOMPLETE", "PENDING", "FAILED"]

        other_documentation = LoanKYCDocumentation.objects.filter(
            borrower=ajo_user, documentation_status__in=documentation_status
        ).exclude(savings=savings)

        if other_documentation.exists():
            doc_instance = other_documentation.last()

            if (
                doc_instance.documentation_status == "COMPLETED"
                and doc_instance.has_loan
            ):
                pass
            else:
                raise serializers.ValidationError("Saver has an existing Documentation")

        data["borrower"] = ajo_user
        data["savings"] = savings

        return data


class ProcessLoanKYCDocumentationSerializer(serializers.Serializer):
    unique_ref = serializers.UUIDField()
    loan_amount = serializers.FloatField()
    transaction_pin = serializers.CharField(max_length=4)
    loan_tenure = serializers.ChoiceField(
        choices=Tenure.choices, default=Tenure.ONE_MONTH
    )

    def validate(self, data):
        request = self.context.get("request")
        collector = request.user
        unique_ref = data.get("unique_ref")
        loan_amount = data.get("loan_amount")
        loan_tenure = data.get("loan_tenure")

        try:
            model_instance = LoanKYCDocumentation.objects.get(
                borrower__user=collector, unique_ref=unique_ref
            )
        except LoanKYCDocumentation.DoesNotExist:
            raise serializers.ValidationError("Loan Kyc Documentation Does not exist")
        savings = model_instance.savings
        savings_type = savings.savings_type

        omit_processing_fee = ["BNPL", "BOOSTA_2X"]
        if savings_type not in omit_processing_fee and loan_amount < 10000:
            raise serializers.ValidationError("Minimum loan amount is N10,000")

        if savings_type == SavingsType.BOOSTA:
            loan_eligibility = LoanEligibility.objects.filter(
                saving=savings, active=True
            )
            if not loan_eligibility.exists():
                raise serializers.ValidationError(
                    f"The user is currently ineligible for {savings_type}. Please confirm eligibility before proceeding with the loan application. Note that eligible amount is the final loan amount."
                )

        if model_instance.documentation_status != DocumentationStatus.PENDING:
            raise serializers.ValidationError(
                "Kindly Re-process Dcocumentation or complete documentation stages"
            )

        agent_wallet = AjoAgentSelector(user=collector).get_agent_ajo_wallet()

        interest_helper = AjoLoan.calculate_interest(
            principal_amount=loan_amount,
            loan_tenure=loan_tenure,
            savings_id=model_instance.savings.id,
        )

        loan_processing_fee = interest_helper.get("loan_processing_fee")
        principal_amount = interest_helper.get("principal_amount")

        health_insurance_fee = interest_helper.get(
            "health_insurance_fee", 89999999999999
        )
        insurance_duration = interest_helper.get("insurance_duration", 89999999999999)
        total_insurance_fee = health_insurance_fee * insurance_duration

        const = ConstantTable.get_constant_table_instance()

        if const.charge_health_insurance_fee:
            # total_debit_amount = loan_processing_fee + total_insurance_fee
            total_debit_amount = (
                loan_processing_fee + const.health_insurance_activation_fee
            )
        else:
            total_debit_amount = loan_processing_fee

        if savings_type not in omit_processing_fee and not check_if_agent_can_pay(
            agent_wallet=agent_wallet, amount=total_debit_amount
        ):
            raise serializers.ValidationError("Insufficient wallet balance ")

        if savings_type in omit_processing_fee:
            if not LoanEligibility.objects.filter(saving=savings).exists():
                raise serializers.ValidationError(
                    "Verification can't be completed at this time. Please wait until the user becomes eligible."
                )

        access_token = request.headers.get("Authorization", "").partition(" ")[-1]
        agent_transaction_pin = data.get("transaction_pin")

        verify_agent_pin = verify_transaction_pin(
            transaction_pin=agent_transaction_pin,
            access_token=access_token,
            customer_user_id=collector.customer_user_id,
        )

        if verify_agent_pin is True:
            pass
        else:
            raise serializers.ValidationError("Incorrect transaction pin")

        data["model_instance"] = model_instance
        data["loan_processing_fee"] = loan_processing_fee
        data["principal_amount"] = principal_amount
        return data


class ListLoanKYCDocumentationSerializer(serializers.Serializer):

    def to_representation(self, instance: LoanKYCDocumentation):
        representation = super().to_representation(instance)

        instance.update_status_on_stages_update
        savings_instance = instance.savings
        representation["unique_ref"] = instance.unique_ref
        representation["paid_processing_fee"] = instance.paid_processing_fee

        if not savings_instance:
            savings_tenure = ""
            representation["savings_id"] = None
            representation["savings_type"] = ""
            representation["amount_saved"] = 0.0
        else:
            representation["savings_id"] = savings_instance.id
            representation["savings_type"] = savings_instance.savings_type
            representation["amount_saved"] = savings_instance.amount_saved
            savings_tenure = savings_instance.tenure_str

        eligibility_query = LoanEligibility.objects.filter(
            saving=savings_instance, active=True
        )

        if eligibility_query.exists():
            eligibiity_data = eligibility_query.values("amount", "loan_type").first()
        else:
            eligibiity_data = None

        representation["eligibiity_data"] = eligibiity_data
        representation["savings_tenure"] = savings_tenure
        representation["failed_stages"] = instance.failed_stages
        representation["borrower_full_name"] = instance.borrower.fullname
        representation["documentation_status"] = instance.documentation_status
        representation["borrower_phone_number"] = instance.borrower.phone
        representation["verify_phone_number"] = instance.verify_phone_number

        representation["borrower_information"] = {
            "edit_stage": instance.borrower_edit_stage,
            "crc_info": {
                "crc_eligible": instance.crc_eligible,
                "high_outstanding_debt": instance.high_outstanding_debt,
                "crc_outstanding": instance.crc_outstanding,
                "crc_open_loans_count": instance.crc_open_loans_count,
                "crc_bad_loans_count": instance.crc_bad_loans_count,
                "crc_open_loans": instance.crc_open_loans,
                "crc_bad_loans_institions": instance.crc_bad_loans_institions,
            },
            "info": instance.borrower_information,
        }
        representation["guarantor_details"] = {
            "edit_stage": instance.guarantor_edit_stage,
            "info": instance.guarantor_details,
        }

        return representation


class CreateLoanForExistingKYCDocumentationSerializer(serializers.Serializer):
    eligibility_id = serializers.CharField()
    # saver_phone_number = serializers.CharField()

    def validate(self, data):
        eligibility_id = data.get("eligibility_id")

        try:
            eligibility_instance = LoanEligibility.objects.get(id=eligibility_id)
        except LoanEligibility.DoesNotExist:
            raise serializers.ValidationError("Invalid Loan Eligibility")

        savings_instance = eligibility_instance.saving
        # savings_type = savings_instance.savings_type

        # saver_phone_number = savings_instance.ajo_user.phone_number
        loan_doc_qs = LoanKYCDocumentation.objects.filter(savings=savings_instance)
        if not loan_doc_qs.exists():
            raise serializers.ValidationError("Loan Documentation Does not exist")
        doc_instance = loan_doc_qs.last()

        if doc_instance.documentation_status != DocumentationStatus.COMPLETED:
            raise serializers.ValidationError(
                f"Please confirm that the documentation has been fully completed and that the processing fee has been paid."
            )

        if (
            doc_instance.ajo_user_guarantor_loan_is_active
            or doc_instance.ajo_user_loan_is_active
        ):

            if doc_instance.ajo_user_guarantor_loan_is_active:
                response_msg = "Guarantor on Documentation has an active loan"
            else:
                response_msg = "Borrower on Documentation has an active loan"
            raise serializers.ValidationError(f"{response_msg}")

        if AjoLoan.objects.filter(kyc_doc_ref=doc_instance.unique_ref).exists():
            raise serializers.ValidationError("Documentation has an existing loan")

        approve_eligibility = eligibility_instance.approved
        documentation_amount = doc_instance.loan_amount
        eligible_amount = eligibility_instance.amount
        approved_amount = eligibility_instance.amount_approved
        const = ConstantTable.get_constant_table_instance()
        admin_cap_amount = const.max_loan_amount

        processing_fee = doc_instance.processing_fee
        loan_amount = doc_instance.loan_amount
        tenure = doc_instance.loan_tenure

        if loan_amount <= 0:
            raise serializers.ValidationError(
                f"Invalid Loan amount:{loan_amount} on Loan Kyc."
            )

        if (
            documentation_amount > eligible_amount
        ) and eligibility_instance.loan_type != "BNPL":
            raise serializers.ValidationError(
                f"Documentation amount of {documentation_amount} exceeds the eligible amount of {eligible_amount}.kindly save up"
            )

        elif (
            admin_cap_amount < eligible_amount <= documentation_amount
            and approve_eligibility is False
        ):
            raise serializers.ValidationError(
                f"Documentation amount {documentation_amount} exceeds max loan amount {admin_cap_amount}. Please contact admin"
            )

        amount_to_disburse = documentation_amount
        if approve_eligibility:
            if approved_amount <= 0:
                raise serializers.ValidationError(
                    f"Approved amount should be greater than 0"
                )
            elif approved_amount > documentation_amount:
                raise serializers.ValidationError(
                    f"Approved amount {approved_amount} exceeds provided amount of {documentation_amount} during documentation"
                )
            amount_to_disburse = approved_amount  # use amount approved

        data["amount_to_disburse"] = amount_to_disburse
        data["doc_instance"] = doc_instance
        data["savings_instance"] = savings_instance
        data["eligibility_instance"] = eligibility_instance
        return data


class LoanSendOtpPhoneNumberSerializer(serializers.Serializer):
    mobile_number = serializers.CharField(max_length=13, min_length=11)


class LoanFeedbackSerializer(serializers.Serializer):
    loan_id = serializers.CharField()
    feedback = serializers.IntegerField()
    loan_stage = serializers.ChoiceField(choices=VerificationStage.choices)
    comment = serializers.CharField(allow_null=True, allow_blank=True)

    def validate(self, data):
        loan_id = data.get("loan_id")

        try:
            loan = AjoLoan.objects.get(id=loan_id)
        except AjoLoan.DoesNotExist:
            raise serializers.ValidationError("invalid update request")

        data["loan"] = loan
        return data


class LoanVerifyOtpSerializer(serializers.Serializer):
    otp = serializers.CharField(max_length=6, min_length=6)
    mobile_number = serializers.CharField(max_length=13, min_length=11)

    def validate(self, data):
        otp = data.get("otp")
        mobile_number = data.get("mobile_number")

        if environment == "development":
            pass

        else:
            ussd_verification = verify_ussd_otp(otp=otp, phone_number=mobile_number)

            if ussd_verification is True:
                pass
            elif verify_sms_voice_otp(otp=otp, phone_number=mobile_number) is True:
                pass
            else:
                raise serializers.ValidationError("Invalid OTP or OTP Expired")

        return data


class GetProductAssignmentSerializer(serializers.ModelSerializer):

    class Meta:
        model = ProductAssignment
        fields = "__all__"

    def to_representation(self, instance: ProductAssignment):
        representation = super().to_representation(instance)
        borrower = instance.borrower_loan.borrower
        phone_number = borrower.phone
        asterisk_number = f"{phone_number[:2]}******{phone_number[-3:]}"
        supervisor = instance.supervisor
        if supervisor:
            supervisor_email = supervisor.email
        else:
            supervisor_email = None
        representation["borrower_name"] = borrower.fullname
        representation["supervisor_email"] = supervisor_email
        representation["phone_number"] = asterisk_number
        return representation


class AssignProductSerializer(serializers.Serializer):
    product_id = serializers.CharField()
    otp = serializers.CharField(max_length=6, min_length=6)

    def validate(self, data):
        product_id = data.get("product_id")
        otp = data.get("otp")

        try:
            product_instance = ProductAssignment.objects.get(id=product_id)
        except ProductAssignment.DoesNotExist:
            raise serializers.ValidationError("Invalid Product selection")
        phone_no = product_instance.borrower_loan.borrower.phone

        if not product_instance.imei:
            raise serializers.ValidationError(
                "Product has not been assigned an IMEI. Please contact the admin for assistance."
            )

        _loan = product_instance.borrower_loan
        if _loan.status != "PENDING_PRODUCT_ASSIGNMENT":
            raise serializers.ValidationError(
                "Please confirm that the loan has successfully undergone the supervisor's second level of disbursement."
            )

        if not product_instance.verified_imei:
            raise serializers.ValidationError("Confirm IMEI is verified.")

        if environment == "development":
            pass

        else:
            ussd_verification = verify_ussd_otp(otp=otp, phone_number=phone_no)

            if ussd_verification is True:
                pass
            elif verify_sms_voice_otp(otp=otp, phone_number=phone_no) is True:
                pass
            else:
                raise serializers.ValidationError("Invalid OTP")

        data["product_instance"] = product_instance
        return data


class ConfirmIMEIStatusViewSerializer(serializers.Serializer):
    product_id = serializers.CharField()
    otp = serializers.CharField(max_length=6, min_length=6)

    def validate(self, data):
        product_id = data.get("product_id")
        otp = data.get("otp")

        try:
            product_instance = ProductAssignment.objects.get(id=product_id)
        except ProductAssignment.DoesNotExist:
            raise serializers.ValidationError("Invalid Product selection")

        if not product_instance.imei:
            raise serializers.ValidationError(
                "Product has not been assigned an IMEI. Please contact the admin for assistance."
            )

        _loan = product_instance.borrower_loan
        if _loan.status != "PENDING_PRODUCT_ASSIGNMENT":
            raise serializers.ValidationError(
                "Please confirm that the loan has successfully undergone the supervisor's second level of disbursement."
            )

        if product_instance.device_type == DeviceType.ANDRIOD:
            lookup_response = EasyCtrlMgr.device_lookup(imei=product_instance.imei)

            device_info = lookup_response.get("data", {}).get("records", [])
            EasyControlMetaData.objects.create(
                product_instance=product_instance, raw_data=lookup_response
            )

            if not isinstance(device_info, list) or not device_info:
                raise serializers.ValidationError("Device has not been registered.")

        phone_no = product_instance.borrower_loan.borrower.phone
        if environment == "development":
            pass

        else:
            ussd_verification = verify_ussd_otp(otp=otp, phone_number=phone_no)

            if ussd_verification is True:
                pass
            elif verify_sms_voice_otp(otp=otp, phone_number=phone_no) is True:
                pass
            else:
                raise serializers.ValidationError("Invalid OTP")

        data["device_info"] = device_info
        data["product_instance"] = product_instance
        return data


class AddProductImeiSerializer(serializers.Serializer):
    product_id = serializers.CharField()
    imei = serializers.CharField()
    device_type = serializers.ChoiceField(choices=DeviceType.choices)

    def validate(self, data):
        product_id = data.get("product_id")
        imei = data.get("imei")

        try:
            product_instance = ProductAssignment.objects.get(id=product_id)
        except ProductAssignment.DoesNotExist:
            raise serializers.ValidationError("Invalid Product selection")

        data["product_instance"] = product_instance

        return data


class ProductImeiBulkUpdateSerializer(serializers.Serializer):

    file = serializers.FileField()

    def validate(self, data):
        file = data.get("file")

        df = pd.read_excel(file)
        product_id = list(df["id"])
        loan_id = list(df["borrower_loan"])
        # serial_numbers = list(df["serial_number"])
        imei = list(df["imei"])

        for index, imei_val in enumerate(imei):
            imei_val = str(imei_val)
            invalid_type = ["nan", "none", "null"]
            if not imei_val or imei_val.lower() in invalid_type:
                raise serializers.ValidationError(
                    f"Cannot assign invalid IMEI: {imei_val}. S/N {product_id[index]}"
                )

        for index, id in enumerate(product_id):
            try:
                product_instance = ProductAssignment.objects.get(id=id)
            except ProductAssignment.DoesNotExist:
                return

            file_loan_id = str(loan_id[index])
            product_loan_id = str(product_instance.borrower_loan.id)

            if product_loan_id != file_loan_id:
                raise serializers.ValidationError("Altered loan/product id")

        data["imei"] = imei
        data["product_id"] = product_id
        return data


class PendingGroupInfoSerializer(serializers.Serializer):

    def to_representation(self, instance: AjoSepo):
        representation = super().to_representation(instance)

        savings_queryset = AjoSaving.objects.filter(group=instance)

        representation["group_id"] = instance.group_id
        representation["group_status"] = instance.status
        representation["group_name"] = instance.name
        representation["tenor"] = instance.loan_tenure
        representation["repayment_type"] = instance.repayment_type
        representation["group_name"] = instance.name

        participants_data = []
        readines = []
        for participant_savings in savings_queryset:

            eligibility_instance = LoanEligibility.objects.filter(
                saving=participant_savings
            ).last()

            try:
                doc_instance = LoanKYCDocumentation.objects.get(
                    savings=participant_savings
                )
                documentation_id = doc_instance.id
                documentation_status = doc_instance.documentation_status
            except LoanKYCDocumentation.DoesNotExist:
                documentation_id = None
                documentation_status = DocumentationStatus.INCOMPLETE

            if eligibility_instance:
                eligibility_id = eligibility_instance.id
                is_eligible = eligibility_instance.active
                verified_saver = eligibility_instance.verified_saver
                eligible_amount = eligibility_instance.amount
            else:
                verified_saver = False
                eligibility_id = None
                is_eligible = False
                eligible_amount = 0.0

            if documentation_status == DocumentationStatus.COMPLETED and (
                is_eligible or verified_saver
            ):
                participant_rediness = True
            else:
                participant_rediness = False

            ajo_user = participant_savings.ajo_user

            data = {
                "savings_id": participant_savings.id,
                "full_name": ajo_user.fullname,
                "phone_number": ajo_user.phone_number,
                "leader": instance.leader == ajo_user,
                "amount_saved": participant_savings.amount_saved,
                "eligibility_id": eligibility_id,
                "is_eligible": is_eligible,
                "eligible_amount": eligible_amount,
                "documentation_id": documentation_id,
                "documentation_status": documentation_status,
                "participant_rediness": participant_rediness,
            }
            participants_data.append(data)
            readines.append(participant_rediness)

        count_true = readines.count(True)
        total_savings_count = savings_queryset.count()

        if total_savings_count >= instance.participants:

            try:
                percentage_ready = (count_true / total_savings_count) * 100
            except ZeroDivisionError:
                percentage_ready = 0.0

        else:
            percentage_ready = 0.0

        representation["participants_data"] = participants_data
        representation["percentage_ready"] = percentage_ready
        representation["capacity"] = instance.participants
        representation["count_of_participant"] = savings_queryset.count()

        return representation


class GetRunningGroupLoanSerializer(serializers.Serializer):

    def to_representation(self, instance: AjoSepo):
        representation = super().to_representation(instance)

        savings_queryset = AjoSaving.objects.filter(group=instance)
        savings_ids = savings_queryset.values_list("id", flat=True)

        # get all eligibility associated with the savings
        eligibility_queryset = LoanEligibility.objects.filter(saving_id__in=savings_ids)
        eligibility_queryset_ids = eligibility_queryset.values_list("id", flat=True)

        ajoloan_queryset = AjoLoan.objects.filter(
            eligibility_id__in=eligibility_queryset_ids
        )

        total_expected_repayment = (
            ajoloan_queryset.aggregate(repayment_amount=Sum("repayment_amount"))[
                "repayment_amount"
            ]
            or 0
        )

        repayments_queryset = AjoLoanRepayment.objects.filter(
            ajo_loan_id__in=ajoloan_queryset.values_list("id", flat=True)
        )
        amount_collected = (
            repayments_queryset.aggregate(repayment_amount=Sum("repayment_amount"))[
                "repayment_amount"
            ]
            or 0
        )

        representation["group_id"] = instance.group_id
        representation["group_status"] = instance.status
        representation["group_name"] = instance.name
        representation["total_expected_repayment"] = total_expected_repayment
        representation["amount_collected"] = amount_collected

        participants_data = []
        total_amount_due = 0
        total_missed_repayment = 0

        for loan_instance in ajoloan_queryset:

            missed_repayment = int(loan_instance.outstanding_days)
            amount_due = loan_instance.outstanding_due_today
            total_amount_due += amount_due

            total_missed_repayment += missed_repayment
            ajo_user = loan_instance.borrower
            total_paid = (
                repayments_queryset.filter(ajo_loan=loan_instance).aggregate(
                    repayment_amount=Sum("repayment_amount")
                )["repayment_amount"]
                or 0
            )
            data = {
                "loan_id": loan_instance.id,
                "savings_id": loan_instance.eligibility.saving.id,
                "full_name": ajo_user.fullname,
                "phone_number": ajo_user.phone_number,
                "loan_amount": loan_instance.amount,
                "amount_due": amount_due,
                "repayment_amount": loan_instance.repayment_amount,
                "daily_repayment": loan_instance.daily_repayment_amount,
                "total_paid": total_paid,
                "missed_repayment": missed_repayment,
            }
            participants_data.append(data)

        representation["total_missed_repayment"] = total_missed_repayment
        representation["total_amount_due"] = total_amount_due
        representation["participants_data"] = participants_data
        representation["date_closed"] = instance.date_closed
        return representation


class CreateGroupLoanSerializer(serializers.Serializer):
    documentation_ids = serializers.ListField()

    def validate(self, data):
        doc_ids = data.get("documentation_ids")

        doc_ids_len = len(doc_ids)
        documentation_queryset = LoanKYCDocumentation.objects.filter(id__in=doc_ids)
        avail_doc_count = documentation_queryset.count()

        if doc_ids_len < 6:
            raise serializers.ValidationError("Kindly provide atleast 6 items")

        if doc_ids_len != avail_doc_count:
            raise serializers.ValidationError(
                f"Provided Documentation Length: {doc_ids_len} does not match available Documentation Length: {avail_doc_count}"
            )

        # get savings ids
        savings_ids = documentation_queryset.values_list("savings", flat=True)

        # get eligibility tied to the respective savings
        eligibility_queryset = LoanEligibility.objects.filter(saving_id__in=savings_ids)
        eligibility_qs_len = eligibility_queryset.count()

        # print(savings_ids, "\n\n")
        # print(eligibility_queryset, "\n\n")

        if avail_doc_count != eligibility_qs_len:
            raise serializers.ValidationError(
                f"Provided Documentation Length: {avail_doc_count} does not match available Count of Eligible savings: {eligibility_qs_len}"
            )

        data["documentation_queryset"] = documentation_queryset
        data["eligibility_queryset"] = eligibility_queryset
        return data


class DisburseGroupLoanSerializer(serializers.Serializer):
    loan_ids = serializers.ListField()
    transaction_pin = serializers.CharField(max_length=4)

    def validate(self, data):
        request = self.context.get("request")
        user = request.user
        loan_ids = data.get("loan_ids")
        ajoloan_queryset = AjoLoan.objects.filter(
            id__in=loan_ids,
            is_disbursed=False,
            eligibility__saving__amount_saved__gt=0,
            verification_stage=VerificationStage.DISBURSEMENT,
            status=LoanStatus.APPROVED,
        )
        if not ajoloan_queryset:
            raise serializers.ValidationError(f"No Pending Group Loan Disbursment")

        access_token = request.headers.get("Authorization", "").partition(" ")[-1]
        agent_transaction_pin = data.get("transaction_pin")

        verify_agent_pin = verify_transaction_pin(
            transaction_pin=agent_transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )

        if verify_agent_pin is True:
            pass
        else:
            raise serializers.ValidationError("Incorrect transaction pin")

        data["ajoloan_queryset"] = ajoloan_queryset
        return data


class LoanRepaymentAmountSerializer(serializers.Serializer):
    loan_id = serializers.IntegerField()
    amount = serializers.FloatField()


class GroupLoanRepaymentSerializer(serializers.Serializer):
    loans = LoanRepaymentAmountSerializer(many=True)
    transaction_pin = serializers.CharField()

    def validate(self, attrs):
        loan_objects: List[Dict[str, Any]] = attrs.get("loans")

        total_amount = 0
        for item in loan_objects:
            total_amount += item.get("amount")

        attrs["total_amount"] = total_amount
        return attrs


class VerifyLoanEligibilitySerializer(serializers.Serializer):
    eligibility_id = serializers.CharField()
    duration = serializers.CharField()
    seasonal_fluctuation_description = serializers.CharField()
    other_income_sources_description = serializers.CharField()
    collateral_assets_description = serializers.CharField()
    can_provide_references = serializers.CharField()
    img_string = serializers.CharField()
    items = serializers.ListSerializer(child=serializers.DictField())
    existing_repayment_amount = serializers.FloatField()
    monthly_expenses = serializers.FloatField()

    def validate(self, attrs):
        eligibility_id = attrs.get("eligibility_id")
        duration = attrs.get("duration")
        available_tenure = dict(Tenure.choices)
        tenure_indays = available_tenure.get(duration)
        const = ConstantTable.get_constant_table_instance()
        max_tries = const.boosta_2x_config.get("max_tries", 3)

        if not available_tenure or not tenure_indays:
            raise serializers.ValidationError(f"Invalid Loan Duration{duration}")
        try:
            eligibility_instance = LoanEligibility.objects.get(id=eligibility_id)
        except LoanEligibility.DoesNotExist:
            raise serializers.ValidationError("Invalid Eligibility ID")

        try:

            verfication_instance = LoanEligibilityVerification.objects.get(
                eligibility=eligibility_instance
            )
            if (
                verfication_instance.tries >= max_tries
                and verfication_instance.able_to_repay is False
            ):
                raise serializers.ValidationError(
                    "Request could not be completed. Please contact support."
                )

        except LoanEligibilityVerification.DoesNotExist:
            pass

        attrs["duration"] = tenure_indays
        attrs["ajo_user"] = eligibility_instance.ajo_user
        return attrs


class ManualLoanDiskPostingSerializer(serializers.Serializer):

    loan_id = serializers.CharField(required=True)
    amount = serializers.FloatField(required=False)

    class Meta:
        fields = ["loan_id", "amount"]

    def validate(self, attrs):
        validated_data = super().validate(attrs)
        loan_id = validated_data.get("loan_id")
        try:
            AjoLoan.objects.get(id=loan_id)
        except AjoLoan.DoesNotExist:
            raise serializers.ValidationError("Loan ID not found.")

        return validated_data


class ManualLoanDiskBorrowePostingSerializer(serializers.Serializer):

    ajo_user_id = serializers.CharField(required=True)

    class Meta:
        fields = ["ajo_user_id"]

    def validate(self, attrs):
        validated_data = super().validate(attrs)
        ajo_user_id = validated_data.get("ajo_user_id")
        try:
            AjoUser.objects.get(id=ajo_user_id)
        except AjoUser.DoesNotExist:
            raise serializers.ValidationError("AjoUser not found.")

        return validated_data


class BulkRepaymentSerializer(serializers.Serializer):
    record_summary = serializers.ListSerializer(child=serializers.DictField())
    transaction_pin = serializers.CharField(max_length=4)

    def validate(self, data):
        request = self.context.get("request")
        user = request.user
        record_summary = data.get("record_summary")
        loan_ids = []
        amount = []
        for entry in record_summary:
            loan_ids.append(entry.get("loan_id"))
            amount.append(entry.get("amount"))

        total_amount = sum(tuple(amount))
        print(loan_ids, total_amount)
        loans = AjoLoan.objects.filter(id__in=loan_ids)

        existing_loan_ids = loans.values_list("id", flat=True)
        missing_loan_ids = [
            loan_id for loan_id in loan_ids if loan_id not in existing_loan_ids
        ]
        if missing_loan_ids:
            raise serializers.ValidationError(
                "Some of the requested loan information could not be found. Please verify the details and try again."
            )
        agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()

        if not check_if_agent_can_pay(agent_wallet=agent_wallet, amount=total_amount):
            raise serializers.ValidationError("Insufficient wallet balance.")

        access_token = request.headers.get("Authorization", "").partition(" ")[-1]
        agent_transaction_pin = data.get("transaction_pin")

        verify_agent_pin = verify_transaction_pin(
            transaction_pin=agent_transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )

        if verify_agent_pin is True:
            pass
        else:
            raise serializers.ValidationError("Incorrect transaction pin")

        return data


class BulkRepaymentHistorySerializer(serializers.Serializer):
    batch_id = serializers.CharField()
    total_amount = serializers.FloatField()
    status = serializers.CharField()
    created_at = serializers.DateTimeField()
    record_count = serializers.IntegerField()


class LoanTopUpSerializer(serializers.Serializer):
    eligibility_id = serializers.CharField()
    tenure = serializers.CharField()
    transaction_pin = serializers.CharField()

    def validate(self, attrs):
        context = self.context

        request = context.get("request")
        transaction_pin = attrs.get("transaction_pin")
        eligibility_id = attrs.get("eligibility_id")
        tenure = attrs.get("tenure")
        user = request.user
        const = ConstantTable.get_constant_table_instance()
        topup_config = const.topup_config
        const_processing_fee_percent = topup_config.get("processing_fee_percent", 5)
        processing_fee_percent = const_processing_fee_percent / 100

        try:
            get_tenure_in_days = AjoLoan.tenure_in_day_count(tenure=tenure)
        except AssertionError as err:
            raise serializers.ValidationError(f"{err}")

        try:
            eligibility_instance = LoanEligibility.objects.get(id=eligibility_id)
        except LoanEligibility.DoesNotExist:
            raise serializers.ValidationError("Loan top-up not found.")

        topup_amt = eligibility_instance.amount

        if not eligibility_instance.verified_saver:
            raise serializers.ValidationError(f"saver is currently not verified")

        if (
            eligibility_instance.requires_approval
            and eligibility_instance.approved is False
        ):
            raise serializers.ValidationError(
                f"Loan top-up amount ({eligibility_instance.amount}) requires approval. Please contact support."
            )

        if eligibility_instance.is_collected:
            raise serializers.ValidationError(
                f"Unable to perform the top-up as the loan has already been disbursed."
            )

        if (
            eligibility_instance.topup_decision_status != TopUpDecisionStatus.PENDING
            or eligibility_instance.active is False
        ):
            raise serializers.ValidationError(
                f"Could not perform the top-up because the decision status is {eligibility_instance.topup_decision_status} or in-active."
            )

        topup_summary = LoanEligibility.get_topup_eligibility(
            loan_id=eligibility_instance.loan_topup_id, get_summary=True, tenure=tenure
        )

        if topup_summary.get("is_eligible") is False:
            raise serializers.ValidationError(
                f"Could not perform the top-up because the decision status is {eligibility_instance.topup_decision_status} or in-active."
            )

        # confirm if agent can pay processing fee
        agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()
        # topup_porcessing_fee = topup_amt * processing_fee_percent
        topup_porcessing_fee = topup_summary["loan_processing_fee"]
        if not check_if_agent_can_pay(
            agent_wallet=agent_wallet, amount=topup_porcessing_fee
        ):
            raise serializers.ValidationError(
                f"Insufficient wallet balance. A processing fee of {topup_porcessing_fee:,.2f} "
                f"is required to complete "
                "this top-up loan application. Please fund your wallet and try again."
            )

        access_token = request.headers.get("Authorization", "").partition(" ")[-1]

        verify_agent_pin = verify_transaction_pin(
            transaction_pin=transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )

        if verify_agent_pin is True:
            pass
        else:
            raise serializers.ValidationError("Incorrect transaction pin")

        attrs["topup_summary"] = topup_summary
        attrs["topup_porcessing_fee"] = topup_porcessing_fee
        return attrs


class CreditWorthinessSerializer(serializers.Serializer):

    account_id = serializers.CharField()
    amount = serializers.FloatField()
    tenor = serializers.IntegerField()
    bvn = serializers.CharField(max_length=16)
    tenor = serializers.IntegerField()

    def validate(self, attrs):
        # request = self.context.get("request")
        # user = request.user
        account_id = attrs.get("account_id")
        bvn = attrs.get("bvn")

        bvn_data = YouVerifyRequest.get_create_identity_result(
            id=bvn, id_type="BVN", source=IdentityRequestSource.EXTERNAL
        )

        if not bvn_data or not bvn_data.is_valid:
            raise serializers.ValidationError("Invalid BVN number")

        # ajo_user = AjoUser.objects.filter(user=user).first()
        # most_recent_check = CreditWorthinessData.get_most_recent_for_user(
        #     ajo_user=user, account_id=account_id
        # )
        # use the above when the user signin is ready
        most_recent_check = CreditWorthinessData.get_most_recent_for_user(
            account_id=account_id
        )

        if most_recent_check:
            attempted = most_recent_check.has_attempted_recently()
            if attempted[0] is False:
                raise serializers.ValidationError(
                    f"Last credit check was {attempted[1]} days ago. Can only check 90 days after previous check."
                )

        attrs["bvn"] = bvn_data.verification_id
        return attrs


class CheckerIdVerificationSerializer(serializers.Serializer):
    phone_number = serializers.CharField()
    id_number = serializers.CharField(max_length=11, min_length=11)
    id_type = serializers.ChoiceField(choices=VerificationType.choices)
    loan_type = serializers.ChoiceField(choices=LoanType.choices)
    transaction_pin = serializers.CharField(max_length=4, min_length=4)

    def validate(self, attrs):

        # Fetch constant values from a shared configuration table
        cosnt = ConstantTable.get_constant_table_instance()
        request = self.context.get("request")
        access_token = request.headers.get("Authorization", "").partition(" ")[-1]
        user = request.user

        # Extract fields from the provided data
        phone_number = attrs.get("phone_number")
        id_number = attrs.get("id_number")
        id_type = attrs.get("id_type")
        loan_type = attrs.get("loan_type")
        transaction_pin = attrs.get("transaction_pin")

        # Fetch fees and assessment expiration duration
        checker_fee = float(cosnt.checker_fee)
        loan_assessment_days_to_last = cosnt.loan_assessment_days_to_last

        # Fetch the agent's wallet balance
        agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()

        # Find the associated user by phone number
        ajo_user = AjoUser.objects.filter(
            user=user, phone_number__startswith=phone_number
        ).first()

        if not ajo_user:
            raise serializers.ValidationError(
                f"Saver with phone number {phone_number} is not associated with agent {user.email}"
            )

        # Filter for existing assessments with completed statuses
        processed_stage = ["SUCCESS", "FAILED"]
        existing_processed_assessment = LoanAffordability.objects.filter(
            checker_status__in=processed_stage, id_number=id_number, id_type=id_type
        ).last()

        # Check if there’s a recent assessment within the valid duration
        if (
            existing_processed_assessment is not None
            and existing_processed_assessment.days_since_created
            < loan_assessment_days_to_last
        ):
            amount_approved = existing_processed_assessment.amount_approved
            if existing_processed_assessment.checker_status == "SUCCESS":
                raise serializers.ValidationError(
                    f"Based on the recent assessment with the provided {existing_processed_assessment.id_type}, "
                    f"you have an offer of {amount_approved} available. This offer is valid for the next "
                    f"{loan_assessment_days_to_last - existing_processed_assessment.days_since_created} days, "
                    "during which you can proceed if you'd like to accept it."
                )
            else:
                raise serializers.ValidationError(
                    f"Borrower does not qualify for {existing_processed_assessment.loan_type} loan. "
                    "Kindly try again at a later date."
                )

        # Find an ongoing loan application instance
        loan_application_instance = LoanAffordability.objects.filter(
            ajo_user=ajo_user,
            checker_status=NinBvnRequestStatus.PROCESSING,
            checker_fee_paid=True,
        ).last()

        # Verify the agent's transaction PIN
        verify_agent_pin = verify_transaction_pin(
            transaction_pin=transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )

        if verify_agent_pin is not True:
            raise serializers.ValidationError("Incorrect transaction pin")

        # Handle ongoing loan applications within the valid duration
        if (
            loan_application_instance is not None
            and loan_application_instance.days_since_created
            < loan_assessment_days_to_last
        ):
            if loan_application_instance.stage == CheckerStage.ID_VERIFICATION:
                try:
                    loan_application_instance.id_verification(
                        id_number=id_number,
                        id_type=id_type,
                        ajo_user=ajo_user,
                        loan_type=loan_type,
                    )
                except LookupError as err:
                    raise serializers.ValidationError(str(err))
            else:
                # # Notify the user to proceed to the next stage
                # stage_update = loan_application_instance.stage.replace("_", " ")
                # raise serializers.ValidationError(
                #     f"You have successfully started the process. Please proceed to {stage_update} stage to continue."
                # )
                pass
        else:
            # Create a new loan application instance if none exists or if the existing one is expired
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            unique_id = str(uuid.uuid4()).split("-")[0]
            reference = f"{timestamp}{unique_id}"

            if not loan_application_instance or (
                loan_application_instance
                and loan_application_instance.months_since_created
                >= loan_assessment_days_to_last
            ):
                if not check_if_agent_can_pay(
                    agent_wallet=agent_wallet, amount=checker_fee
                ):
                    raise serializers.ValidationError("Insufficient wallet balance")

                loan_application_instance = LoanAffordability.objects.create(
                    ajo_user=ajo_user,
                    reference=reference,
                    id_type=id_type,
                    stage=CheckerStage.PAYMENT,
                    checker_status=NinBvnRequestStatus.PROCESSING,
                    id_number=id_number,
                    loan_type=loan_type,
                    checker_fee=checker_fee,
                )
                loan_application_instance.create_checker_fee_transaction_record(
                    agent_wallet=agent_wallet,
                    checker_fee=checker_fee,
                    ajo_user=ajo_user,
                )

                loan_application_instance.update_to_id_verification_checker_stage()

        # Attempt ID verification for the loan application
        try:
            loan_application_instance.id_verification(
                id_number=id_number,
                id_type=id_type,
                ajo_user=ajo_user,
                loan_type=loan_type,
            )
        except LookupError as err:
            raise serializers.ValidationError(str(err))

        # Pass the instance back in the validated data
        attrs["checker_instance"] = loan_application_instance

        return attrs


class CheckerImageVerificationSerializer(serializers.Serializer):
    checker_id = serializers.CharField()
    snapped_image = serializers.CharField()

    def validate(self, attrs):
        # request = self.context.get("request")
        # user = request.user
        checker_id = attrs.get("checker_id")
        snapped_image = attrs.get("snapped_image")

        try:
            loan_application_instance = LoanAffordability.objects.get(id=checker_id)
        except LoanAffordability.DoesNotExist:
            raise serializers.ValidationError(f"Invalid verification request")

        if not loan_application_instance.face_match:
            id_image = loan_application_instance.id_image

            biometrics_object = Biometrics()

            try:
                face_one = biometrics_object.convert_b64_2_jpg(snapped_image)
                face_two = biometrics_object.convert_b64_2_jpg(id_image)
            except Exception as err:
                raise serializers.ValidationError(f"Invalid Image format")

            loan_application_instance.snapped_image = snapped_image
            loan_application_instance.save()

            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            unique_id = str(uuid.uuid4()).split("-")[0]
            reference = f"{timestamp}{unique_id}"

            identity_match = biometrics_object.face_Similarity(
                ref_id=reference, face_one=face_one, face_two=face_two
            )

            verification_response = identity_match.get("response")
            if (
                identity_match.get("status") == "success"
                and verification_response.get("status") == "success"
            ):
                data = verification_response.get("data").get("data")
                similarity = data.get("similarity")
                if similarity < 0.8:
                    raise serializers.ValidationError(
                        f"The face provided does not match the one associated with your {loan_application_instance.id_type} record. Please ensure you are using the correct NIN and that the image is clear and well-lit."
                    )

            else:
                raise serializers.ValidationError("Service Temporarily unavailable")

            loan_application_instance.face_match = True
            loan_application_instance.save()

        attrs["checker_instance"] = loan_application_instance
        return attrs


class CheckerIntendedAmountVerificationSerializer(serializers.Serializer):
    checker_id = serializers.CharField()
    intended_amount = serializers.FloatField()
    duration = serializers.CharField()
    seasonal_fluctuation_description = serializers.CharField()
    other_income_sources_description = serializers.CharField()
    collateral_assets_description = serializers.CharField()
    can_provide_references = serializers.CharField()
    img_string = serializers.CharField()
    items = serializers.ListSerializer(child=serializers.DictField())
    existing_loan_repayment = serializers.FloatField()
    monthly_expenses = serializers.FloatField()

    def validate(self, attrs):
        checker_id = attrs.get("checker_id")
        duration = attrs.get("duration")
        intended_amount = attrs.get("intended_amount")
        available_tenure = dict(Tenure.choices)
        tenure_indays = available_tenure.get(duration)
        const = ConstantTable.get_constant_table_instance()

        if not available_tenure or not tenure_indays:
            raise serializers.ValidationError(f"Invalid Loan Duration{duration}")

        if intended_amount < 50000:
            raise serializers.ValidationError(
                f"To proceed, please enter an amount of at least 50,000."
            )

        if not available_tenure or not tenure_indays:
            raise serializers.ValidationError(f"Invalid Loan Duration{duration}")

        try:
            loan_application_instance = LoanAffordability.objects.get(id=checker_id)
        except LoanAffordability.DoesNotExist:
            raise serializers.ValidationError(f"Invalid Eligibility Check request")

        if not loan_application_instance.stage == "LOAN_AMOUNT":
            raise serializers.ValidationError(
                f"You cannot proceed because the current checker stage is {loan_application_instance.stage.replace('_', ' ')}."
            )

        attrs["checker_instance"] = loan_application_instance
        attrs["duration"] = tenure_indays
        attrs["ajo_user"] = loan_application_instance.ajo_user
        return attrs


class CheckerHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = LoanAffordability
        fields = "__all__"


class CreditHealthInquirySerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=13, min_length=11)
    verification_id = serializers.CharField(max_length=11, min_length=11)
    id_type = serializers.ChoiceField(choices=VerificationType.choices)

    # def validate(self, data):
    #     phone_number = data.get("phone_number")
    #     verification_id = data.get("verification_id")
    #     id_type = data.get("id_type")

    #     return data


class CreditHealthAmountInquiryHandlerSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=13, min_length=11)
    intended_amount = serializers.FloatField()
    duration = serializers.CharField()
    loan_type = serializers.CharField()
    seasonal_fluctuation_description = serializers.CharField()
    other_income_sources_description = serializers.CharField()
    collateral_assets_description = serializers.CharField()
    can_provide_references = serializers.CharField()
    img_string = serializers.CharField()
    items = serializers.ListSerializer(child=serializers.DictField())
    existing_loan_repayment = serializers.FloatField()
    monthly_expenses = serializers.FloatField()

    def validate(self, attrs):
        duration = attrs.get("duration")
        intended_amount = attrs.get("intended_amount")
        items = attrs.get("items")
        available_tenure = dict(Tenure.choices)
        tenure_indays = available_tenure.get(duration)
        _constant_table = ConstantTable.get_constant_table_instance()
        boosta_xtype_config = _constant_table.boosta_2x_config
        # const_duration = list(boosta_xtype_config.items())
        const_duration = boosta_xtype_config.get(tenure_indays)

        if not items:
            raise serializers.ValidationError("Please provide at least one item.")

        if not available_tenure or not tenure_indays or const_duration is None:
            raise serializers.ValidationError(f"Invalid Loan Duration {duration}")

        if intended_amount < 50000:
            raise serializers.ValidationError(
                f"To proceed, please enter an amount of at least 50,000."
            )

        if not available_tenure or not tenure_indays:
            raise serializers.ValidationError(f"Invalid Loan Duration{duration}")

        attrs["duration"] = tenure_indays
        return attrs


class VerifyOtpCreateUserAndSavingsUnderAgentSerializer(serializers.Serializer):
    loan_access_code = serializers.CharField()
    otp = serializers.CharField(max_length=6, min_length=6)

    def validate(self, data):
        request = self.context.get("request")
        loan_access_code = data.get("loan_access_code")
        otp = data.get("otp")
        agent = request.user

        agent_otp_request = CreditHealthAgentRequest.objects.filter(
            agent=agent,
            stage=CreditHealthStages.OTP,
            credit_health__loan_access_code=loan_access_code,
        )
        request_instance = None
        if not agent_otp_request.exists():
            raise serializers.ValidationError("Invalid access code.")

        else:
            request_instance = agent_otp_request.last()
            borrower_phone_number = request_instance.credit_health.borrower_phone_number

            _ajo_user = AjoUser.objects.filter(
                phone_number__startswith=borrower_phone_number
            )

            if _ajo_user.exclude(
                onboarding_source=OnboardingSource.CREDIT_HEALTH
            ).exists():
                raise serializers.ValidationError(
                    "This service is only available to Liberty Life users. Please contact support."
                )

            if (
                _ajo_user.filter(onboarding_source=OnboardingSource.CREDIT_HEALTH)
                .exclude(user=agent)
                .exists()
            ):
                raise serializers.ValidationError(
                    "The user is already associated with another agent. For further assistance, please contact support."
                )

            if environment == "development":
                pass

            else:
                ussd_verification = verify_ussd_otp(
                    otp=otp, phone_number=borrower_phone_number
                )
                if ussd_verification is True:
                    pass
                elif (
                    verify_sms_voice_otp(otp=otp, phone_number=borrower_phone_number)
                    is True
                ):
                    pass
                else:
                    raise serializers.ValidationError("Invalid OTP or OTP Expired")

            data["request_instance"] = request_instance
            return data


class ApproveEligibilitySerializer(serializers.Serializer):
    eligibility_id = serializers.CharField()
    email = serializers.CharField()
    password = serializers.CharField()
    approved_amount = serializers.FloatField()
    reason = serializers.CharField()


class GetBureauDataSerializer(serializers.Serializer):
    verification_id = serializers.CharField()


class LoanKYCDocumentationAdminSerializer(serializers.Serializer):
    documentation_id = serializers.CharField()
    new_amount = serializers.CharField()
    # phone_number = serializers.CharField()


class GetEligibleMerchantSerializer(serializers.ModelSerializer):
    class Meta:
        model = MerchantEligibilitySummary
        exclude = ("user_id",)


class ChargeMerchantLoanProccessingFeeSerializer(serializers.Serializer):
    eligibility_id = serializers.CharField()
    transaction_pin = serializers.CharField()

    def validate(self, data):
        eligibility_id = data.get("eligibility_id")
        agent_transaction_pin = data.get("transaction_pin")
        request = self.context.get("request")
        user = request.user

        try:
            eligibility_summary = MerchantEligibilitySummary.objects.get(
                id=eligibility_id, user_id=user.customer_user_id
            )
        except MerchantEligibilitySummary.DoesNotExist:
            raise serializers.ValidationError("Eligibility summary not found")

        const = ConstantTable.get_constant_table_instance()
        merchant_loan_config = const.merchant_loan_config
        merchant_processing_fee = merchant_loan_config.get("loan_processing_fee", 500.0)

        liberty_mgr = LibertyPayMgr(
            config=settings,
        )

        _liberty_pay_balance_request = liberty_mgr.get_agent_balance(
            user_ids=[user.customer_user_id]
        )

        if _liberty_pay_balance_request.get("status") == "success":
            # get spend balance
            balance = liberty_mgr.get_wallet_balance(
                _liberty_pay_balance_request.get("response")
            )

            if isinstance(balance, float):
                if balance < merchant_processing_fee:
                    raise serializers.ValidationError("Insufficient wallet balance")
            else:

                raise serializers.ValidationError("Unable to get agent wallet balance")
        else:

            raise serializers.ValidationError("Unable to get agent wallet balance")

        access_token = request.headers.get("Authorization", "").partition(" ")[-1]

        verify_agent_pin = verify_transaction_pin(
            transaction_pin=agent_transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )

        if verify_agent_pin is True:
            pass
        else:
            raise serializers.ValidationError("Incorrect transaction pin")

        data["liberty_mgr"] = liberty_mgr
        data["loan_processing_fee"] = merchant_processing_fee
        data["eligibility_summary"] = eligibility_summary
        return data


class DisburseMerchantLoanSerializer(serializers.Serializer):
    eligibility_id = serializers.CharField()
    transaction_pin = serializers.CharField()

    def validate(self, data):
        agent_transaction_pin = data.get("transaction_pin")
        request = self.context.get("request")
        user = request.user

        access_token = request.headers.get("Authorization", "").partition(" ")[-1]

        verify_agent_pin = verify_transaction_pin(
            transaction_pin=agent_transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )

        if verify_agent_pin is True:
            pass
        else:
            raise serializers.ValidationError("Incorrect transaction pin")

        # data["liberty_mgr"] = liberty_mgr
        # data["loan_processing_fee"] = merchant_processing_fee
        return data


class GetCreditWorthinessSerializer(serializers.Serializer):
    eligibility_id = serializers.CharField()

    def validate(self, data):
        eligibility_id = data.get("eligibility_id")
        try:
            eligibility_summary = MerchantEligibilitySummary.objects.get(
                id=eligibility_id, application_stage="CREDIT_WORTHINESS_CHECK"
            )
        except MerchantEligibilitySummary.DoesNotExist:
            raise serializers.ValidationError("Eligibility summary not found")
        data["eligibility_summary"] = eligibility_summary
        return data


class MerchantDirectDebitAccountSerializer(serializers.Serializer):
    eligibility_id = serializers.CharField()
    account_list = serializers.ListField(child=serializers.DictField())

    def validate(self, data):
        eligibility_id = data.get("eligibility_id")
        account_list = data.get("account_list")

        try:
            eligibility_summary = MerchantEligibilitySummary.objects.get(
                id=eligibility_id,
            )
        except MerchantEligibilitySummary.DoesNotExist:
            raise serializers.ValidationError(
                f"Eligibility summary not found ({eligibility_id})"
            )

        if len(account_list) < 3:
            raise serializers.ValidationError("At least 3 accounts are required.")

        required_keys = ["account_number", "bank_name", "bank_code", "account_name"]
        for account in account_list:
            if not all(key in account for key in required_keys):
                raise serializers.ValidationError(
                    f"Each account must have {', '.join(required_keys)}."
                )

            # Check if account already exists
            existing_account = MerchantDirectDebitAccount.objects.filter(
                account_number=account["account_number"], bank_code=account["bank_code"]
            ).first()

            if existing_account:
                raise serializers.ValidationError(
                    f"Account {account['account_number']} is already associated with another user."
                )

        data["eligibility_summary"] = eligibility_summary
        return data


class DisburseMerchantLoanSerializer(serializers.Serializer):
    eligibility_id = serializers.CharField()
    repayment_type = serializers.ChoiceField(
        choices=RepaymentFrequency.choices,
    )
    transaction_pin = serializers.CharField()
    loan_amount = serializers.FloatField()

    def validate(self, data):
        eligibility_id = data.get("eligibility_id")
        transaction_pin = data.get("transaction_pin")
        loan_amount = data.get("loan_amount")
        request = self.context.get("request")
        user = request.user

        try:
            eligibility_summary = MerchantEligibilitySummary.objects.get(
                id=eligibility_id,
                is_disbursed=False,
                user_id=user.customer_user_id,
                application_stage="LOAN_AMOUNT_ENTRY",
            )
        except MerchantEligibilitySummary.DoesNotExist:
            raise serializers.ValidationError(
                "Eligibility summary not found or loan already disbursed"
            )

        const = ConstantTable.get_constant_table_instance()
        merchant_loan_config = const.merchant_loan_config
        min_loan_amount = merchant_loan_config.get("min_loan_amount")
        eligible_amount = eligibility_summary.eligible_amount

        if loan_amount > eligible_amount:
            raise serializers.ValidationError(
                f"Loan amount ({loan_amount}) exceeds the eligible amount ({eligible_amount})"
            )

        if min_loan_amount > loan_amount:
            raise serializers.ValidationError(
                f"Loan amount ({loan_amount}) is less than the minimum loan amount ({min_loan_amount})"
            )

        data["eligibility_summary"] = eligibility_summary
        data["min_loan_amount"] = min_loan_amount
        data["eligible_amount"] = eligible_amount

        access_token = request.headers.get("Authorization", "").partition(" ")[-1]

        verify_agent_pin = verify_transaction_pin(
            transaction_pin=transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )

        if verify_agent_pin is True:
            pass
        else:
            raise serializers.ValidationError("Incorrect transaction pin")

        return data


class MerchantLoanRepaymentSerializer(serializers.Serializer):
    loan_id = serializers.IntegerField()
    amount = serializers.FloatField()
    transaction_pin = serializers.CharField(max_length=4)

    def validate(self, data):
        loan_id = data.get("loan_id")
        amount = data.get("amount")
        agent_transaction_pin = data.get("transaction_pin")
        request = self.context.get("request")
        user = request.user

        try:
            ajo_loan = AjoLoan.objects.get(id=loan_id)
        except AjoLoan.DoesNotExist:
            raise serializers.ValidationError("Loan not found")

        liberty_mgr = LibertyPayMgr(
            config=settings,
        )

        _liberty_pay_balance_request = liberty_mgr.get_agent_balance(
            user_ids=[user.customer_user_id]
        )

        if _liberty_pay_balance_request.get("status") == "success":
            # get spend balance
            balance = liberty_mgr.get_wallet_balance(
                _liberty_pay_balance_request.get("response")
            )

            if isinstance(balance, float):
                if balance < amount:
                    raise serializers.ValidationError(
                        f"Insufficient wallet balance ({balance})"
                    )
            else:

                raise serializers.ValidationError("Unable to get agent wallet balance")
        else:

            raise serializers.ValidationError("Unable to get agent wallet balance")

        access_token = request.headers.get("Authorization", "").partition(" ")[-1]

        verify_agent_pin = verify_transaction_pin(
            transaction_pin=agent_transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )

        if verify_agent_pin is True:
            pass
        else:
            raise serializers.ValidationError("Incorrect transaction pin")

        data["liberty_mgr"] = liberty_mgr
        data["ajo_loan"] = ajo_loan
        return data
