from import_export import fields, resources

from loans.models import *


class LoanEligibilityResource(resources.ModelResource):
    class Meta:
        model = LoanEligibility


class AjoLoanResource(resources.ModelResource):

    outstanding_due_today = fields.Field(column_name="Outstanding Due Today")
    outstanding_days_today = fields.Field(column_name="Outstanding Days Today")

    class Meta:
        model = AjoLoan

    def dehydrate_outstanding_due_today(self, obj):
        return obj.outstanding_due_today

    def dehydrate_outstanding_days_today(self, obj):
        return obj.outstanding_days_today


class ProsperAgentResource(resources.ModelResource):
    class Meta:
        model = ProsperAgent


class AccountBalancesResource(resources.ModelResource):
    class Meta:
        model = AccountBalances


class InvestmentCapitalResource(resources.ModelResource):
    class Meta:
        model = InvestmentCapital


class TotalInvestmentCapitalResource(resources.ModelResource):
    class Meta:
        model = TotalInvestmentCapital


class BorrowerInfoResource(resources.ModelResource):
    class Meta:
        model = BorrowerInfo


class LoanGuarantorResource(resources.ModelResource):
    class Meta:
        model = LoanGuarantor


class AjoLoanRepaymentResource(resources.ModelResource):
    class Meta:
        model = AjoLoanRepayment


class BiometricsMetaDataResource(resources.ModelResource):
    class Meta:
        model = BiometricsMetaData


class YouVerifyRequestResource(resources.ModelResource):
    class Meta:
        model = YouVerifyRequest


class CreditBureauMetaDataResource(resources.ModelResource):
    class Meta:
        model = CreditBureauMetaData


class ProsperAgentPaymentRecordResource(resources.ModelResource):
    class Meta:
        model = ProsperAgentPaymentRecord


class LoanDiskMetaDataResource(resources.ModelResource):
    class Meta:
        model = LoanDiskMetaData


class RepaymentCheckerResource(resources.ModelResource):
    class Meta:
        model = RepaymentChecker


class BorrowerCreditBureauWorthinessResource(resources.ModelResource):
    class Meta:
        model = BorrowerCreditBureauWorthiness


class AjoUserAgencyBankingBranchRequestDataDumpResource(resources.ModelResource):
    class Meta:
        model = AjoUserAgencyBankingBranchRequestDataDump


class RepaymentVirtuaWalletCreditRecordResouce(resources.ModelResource):
    class Meta:
        model = RepaymentVirtuaWalletCreditRecord


class HolidayResource(resources.ModelResource):
    class Meta:
        model = Holiday


class LoanKYCDocumentationResource(resources.ModelResource):
    class Meta:
        model = LoanKYCDocumentation


class LoanAnalysisLogResource(resources.ModelResource):
    class Meta:
        model = LoanAnalysisLog


class AgentCommissionPayrollResource(resources.ModelResource):
    class Meta:
        model = AgentCommissionPayroll


class AjoLoanScheduleResource(resources.ModelResource):
    class Meta:
        model = AjoLoanSchedule


class ComplianceChecksLogsResource(resources.ModelResource):
    class Meta:
        model = ComplianceChecksLogs


class GiniMachineLogResource(resources.ModelResource):
    class Meta:
        model = GiniMachineLog


class MissedRepaymentsTableResource(resources.ModelResource):
    class Meta:
        model = MissedRepaymentsTable

    def dehydrate_loan_officer(self, obj):
        return (
            f"{obj.loan_officer.first_name} {obj.loan_officer.last_name}"
            if obj.loan_officer
            else ""
        )

    def dehydrate_loan_supervisor(self, obj):
        return (
            f"{obj.loan_supervisor.first_name} {obj.loan_supervisor.last_name}"
            if obj.loan_supervisor
            else ""
        )

    def dehydrate_borrower(self, obj):
        return f"{obj.borrower.first_name} {obj.borrower.last_name}"


class Missed14DaysResource(resources.ModelResource):
    class Meta:
        model = Missed14Days

    def dehydrate_loan_officer(self, obj):
        return (
            f"{obj.loan_officer.first_name} {obj.loan_officer.last_name}"
            if obj.loan_officer
            else ""
        )

    def dehydrate_loan_supervisor(self, obj):
        return (
            f"{obj.loan_supervisor.first_name} {obj.loan_supervisor.last_name}"
            if obj.loan_supervisor
            else ""
        )

    def dehydrate_borrower(self, obj):
        return f"{obj.borrower.first_name} {obj.borrower.last_name}"


class Missed28DaysResource(resources.ModelResource):
    class Meta:
        model = Missed28Days

    def dehydrate_loan_officer(self, obj):
        return (
            f"{obj.loan_officer.first_name} {obj.loan_officer.last_name}"
            if obj.loan_officer
            else ""
        )

    def dehydrate_loan_supervisor(self, obj):
        return (
            f"{obj.loan_supervisor.first_name} {obj.loan_supervisor.last_name}"
            if obj.loan_supervisor
            else ""
        )

    def dehydrate_borrower(self, obj):
        return f"{obj.borrower.first_name} {obj.borrower.last_name}"


class Missed60DaysResource(resources.ModelResource):
    class Meta:
        model = Missed60Days

    def dehydrate_loan_officer(self, obj):
        return (
            f"{obj.loan_officer.first_name} {obj.loan_officer.last_name}"
            if obj.loan_officer
            else ""
        )

    def dehydrate_loan_supervisor(self, obj):
        return (
            f"{obj.loan_supervisor.first_name} {obj.loan_supervisor.last_name}"
            if obj.loan_supervisor
            else ""
        )

    def dehydrate_borrower(self, obj):
        return f"{obj.borrower.first_name} {obj.borrower.last_name}"


class Missed90DaysResource(resources.ModelResource):
    class Meta:
        model = Missed90Days

    def dehydrate_loan_officer(self, obj):
        return (
            f"{obj.loan_officer.first_name} {obj.loan_officer.last_name}"
            if obj.loan_officer
            else ""
        )

    def dehydrate_loan_supervisor(self, obj):
        return (
            f"{obj.loan_supervisor.first_name} {obj.loan_supervisor.last_name}"
            if obj.loan_supervisor
            else ""
        )

    def dehydrate_borrower(self, obj):
        return f"{obj.borrower.first_name} {obj.borrower.last_name}"


class MissedPastMaturity15DaysResource(resources.ModelResource):
    class Meta:
        model = MissedPastMaturity15Days

    def dehydrate_loan_officer(self, obj):
        return (
            f"{obj.loan_officer.first_name} {obj.loan_officer.last_name}"
            if obj.loan_officer
            else ""
        )

    def dehydrate_loan_supervisor(self, obj):
        return (
            f"{obj.loan_supervisor.first_name} {obj.loan_supervisor.last_name}"
            if obj.loan_supervisor
            else ""
        )

    def dehydrate_borrower(self, obj):
        return f"{obj.borrower.first_name} {obj.borrower.last_name}"


class MissedLostResource(resources.ModelResource):
    class Meta:
        model = MissedLost

    def dehydrate_loan_officer(self, obj):
        return (
            f"{obj.loan_officer.first_name} {obj.loan_officer.last_name}"
            if obj.loan_officer
            else ""
        )

    def dehydrate_loan_supervisor(self, obj):
        return (
            f"{obj.loan_supervisor.first_name} {obj.loan_supervisor.last_name}"
            if obj.loan_supervisor
            else ""
        )

    def dehydrate_borrower(self, obj):
        return f"{obj.borrower.first_name} {obj.borrower.last_name}"


class ProductAssignmentResource(resources.ModelResource):
    class Meta:
        model = ProductAssignment


class EasyControlMetaDataResource(resources.ModelResource):
    class Meta:
        model = EasyControlMetaData


class LoanFeedbackResource(resources.ModelResource):
    class Meta:
        model = LoanFeedback


class EscrowDebitErrorLogResource(resources.ModelResource):
    class Meta:
        model = EscrowDebitErrorLog


class CRCInformationUploadResource(resources.ModelResource):
    class Meta:
        model = CRCInformationUpload


class OffetPastMaturityWithEscrowLogResource(resources.ModelResource):
    class Meta:
        model = OffetPastMaturityWithEscrowLog


class VfdProviderDisbursementLogResource(resources.ModelResource):
    class Meta:
        model = VfdProviderDisbursementLog


class EasyControlDeviceGroupResource(resources.ModelResource):
    class Meta:
        model = EasyControlDeviceGroup


class HealthInsuranceResource(resources.ModelResource):
    class Meta:
        model = HealthInsurance


class VfdRepaymentScheduleResource(resources.ModelResource):
    class Meta:
        model = VfdRepaymentSchedule


class VfdRepaymentResource(resources.ModelResource):
    class Meta:
        model = VfdRepayment


class GroupLoansDisbursementLogResource(resources.ModelResource):
    class Meta:
        model = GroupLoansDisbursementLog


class MonnifyAccountFundingLogResource(resources.ModelResource):
    class Meta:
        model = MonnifyAccountFundingLog


class UserEarningsResource(resources.ModelResource):
    class Meta:
        model = UserEarnings


class LoanEligibilityItemResource(resources.ModelResource):
    class Meta:
        model = LoanEligibilityItem


class LoanEligibilityVerificationResource(resources.ModelResource):
    class Meta:
        model = LoanEligibilityVerification


class InsuranceRenewalLogResource(resources.ModelResource):
    class Meta:
        model = InsuranceRenewalLog


class PastMaturityBufferResource(resources.ModelResource):
    class Meta:
        model = PastMaturityBuffer


class BulkRepaymentRecordResource(resources.ModelResource):
    class Meta:
        model = BulkRepaymentRecord


class HealthInsuranceSummaryResource(resources.ModelResource):
    class Meta:
        model = HealthInsuranceSummary


class HealthPlanRenewalResource(resources.ModelResource):
    class Meta:
        model = HealthPlanRenewal


class RenewalAllocationResource(resources.ModelResource):
    class Meta:
        model = RenewalAllocation


class ExcessRenewalRecordsResource(resources.ModelResource):
    class Meta:
        model = ExcessRenewalRecords


class OpenAiEligbilityReviewResource(resources.ModelResource):
    class Meta:
        model = OpenAiEligbilityReview


class CreditWorthinessDataResource(resources.ModelResource):
    class Meta:
        model = CreditWorthinessData


class LoanAffordabilityResource(resources.ModelResource):
    class Meta:
        model = LoanAffordability


class LoanDiskMirrorResource(resources.ModelResource):
    class Meta:
        model = LoanDiskLoansMirror


class CreditHealthRecordResource(resources.ModelResource):
    class Meta:
        model = CreditHealthRecord


class CreditHealthAgentRequestResource(resources.ModelResource):
    class Meta:
        model = CreditHealthAgentRequest


class SeedsAccountBalanceResource(resources.ModelResource):
    class Meta:
        model = SeedsAccountBalance


class MerchantEligibilitySummaryResource(resources.ModelResource):
    class Meta:
        model = MerchantEligibilitySummary


class SMSLogResource(resources.ModelResource):
    class Meta:
        model = SMSLog


class MerchantDirectDebitAccountResource(resources.ModelResource):
    class Meta:
        model = MerchantDirectDebitAccount


class AgentRepaymentPerformanceResource(resources.ModelResource):
    class Meta:
        model = AgentRepaymentPerformance


class SMSTemplateResource(resources.ModelResource):
    class Meta:
        model = SMSTemplate


class LostLoanResource(resources.ModelResource):
    class Meta:
        model = LostLoan


class OutstandingLoanChargeResource(resources.ModelResource):
    class Meta:
        model = OutstandingLoanCharge


class LoanChargeAttemptResource(resources.ModelResource):
    class Meta:
        model = LoanChargeAttempt
