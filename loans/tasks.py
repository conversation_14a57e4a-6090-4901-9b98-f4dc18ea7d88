import logging
import os
from datetime import datetime, timedelta
from time import sleep
from typing import Any, Dict, List

import pandas as pd
from celery import shared_task
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.exceptions import ObjectDoesNotExist
from django.db import transaction as django_transaction
from django.db.models import Count, ExpressionWrapper, F, IntegerField, Q, Sum
from django.db.models.functions import ExtractDay
from django.utils import timezone
from sentry_sdk import capture_exception
from decouple import config

from accounts.agency_banking import AgencyBankingClass  # get_loan_balance,
from accounts.agency_banking import (
    agent_login,
    check_balances,
    commissions_agent_login,
    escrow_agent_login,
    liberty_life_agent_login,
    loan_agent_login,
    repayment_agent_login,
    paybox_login,
)
from accounts.helper_files.monify import Monnify
from accounts.models import ConstantTable, CustomUser
from accounts.responses import failed_celery_response, success_celery_response
from admin_dashboard.tasks import DateUtility
from ajo.model_choices import AccountFormType, GroupStatus
from ajo.models import AjoSaving, AjoUser, AjoUserWithdrawalAccount, BankAccountDetails
from ajo.payment_actions import (
    check_if_agent_can_pay,
    fund_agent_wallet,
    fund_ajo_savings_position,
    move_savings_funds_to_escrow_for_ajo_user,
)
from ajo.selectors import (
    AjoAgentSelector,
    AjoSavingsSelector,
    AjoUserSelector,
    BankAccountSelector,
)
from ajo.tasks import general_send_email
from ajo.third_party import TextMessages
from loans.helpers.credit_record import CRCRecord
from loans.helpers.loan_kyc_docs import DocumentationApiHandler
from loans.models import VfdRepaymentSchedule
from payment.model_choices import (
    PlanType,
    Status,
    TransactionDestination,
    TransactionFormType,
    TransactionSource,
    TransactionTypeCreditOrDebitChoices,
    WalletTypes,
)
from payment.models import Commission, Transaction, WalletSystem
from payment.payment_actions import pay_withdrawal_and_handle_response
from payment.services import TransactionService

from .enums import (
    DocumentationStatus,
    InsuranceRequestStatus,
    LoanPerformanceStatus,
    LoanStatus,
    RepaymentType,
    Tenure,
    VerificationStage,
)
from .helpers.compliance import ComplianceChecks
from .helpers.core_banking import CoreBankingManager
from .helpers.helpers import (
    SalaryDecisioning,
    get_days_without_holiday,
    get_percentages,
    send_ajo_loan_officers_daily_check_email,
    send_ajo_loan_officers_daily_report_email,
    send_ajo_loan_officers_monthly_check_email,
    send_ajo_loan_officers_weekly_check_email,
)
from .helpers.loandisk_helper import (
    BranchLoanDiskManager,
    LoandiskManager,
    loan_disk_date_format,
)
from .helpers.missed_loans import create_or_update_missed_loan, remove_missed_loan
from .helpers.repayments import (
    process_loan_repayment_from_escrow_wallet,
    process_loan_repayment_from_spend_wallet,
)
from .models import (
    AgentRepaymentPerformance,
    AjoLoan,
    LoanChargeAttempt,
    LoanDiskLoansMirror,
    MerchantEligibilitySummary,
    SMSLog,
)  # InvestmentCapital,
from .models import (
    AccountBalances,
    AjoLoanRepayment,
    AjoLoanSchedule,
    BulkRepaymentRecord,
    CreditBureauMetaData,
    HealthInsurance,
    HealthPlanRenewal,
    Holiday,
    InvestmentCapital,
    LoanAffordability,
    LoanDiskMetaData,
    LoanEligibility,
    LoanEligibilityVerification,
    LoanKYCDocumentation,
    Missed14Days,
    Missed28Days,
    Missed60Days,
    Missed90Days,
    MissedLost,
    MissedPastMaturity15Days,
    MissedRepaymentsTable,
    OffetPastMaturityWithEscrowLog,
    OpenAiEligbilityReview,
    ProductAssignment,
    RepaymentChecker,
    TotalInvestmentCapital,
)
from .services import DiscrepancyFixes
from payment.model_choices import DisbursementProviderType, TransactionFormType

logger = logging.getLogger(__name__)

User = get_user_model()

disbursed_loans_status_list = ["COMPLETED", "OPEN"]
if settings.ENVIRONMENT == "development":
    admin_email_list = ["<EMAIL>"]
else:
    admin_email_list = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        # "<EMAIL>",
        # "<EMAIL>",
        # "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ]

loan_officer_sms_template_id = settings.LOAN_OFFICER_SMS_TEMPLATE_ID
loan_borrower_sms_template_id = settings.LOAN_BORROWER_SMS_TEMPLATE_ID


@shared_task
def run_loan_officers_daily_summary():
    """
    - Checks the loan activities of loan officers from the previous day
    - Provides the summary for the previous day loan activities
    - Generates an excel summary for every officer
    - Share the generated summary with the loan officer via email on a daily basis
    """
    # Define Dates
    previous_day = DateUtility().previous_day
    today = timezone.now()

    # Write queries
    loan_officers_qs = User.objects.filter(user_type="STAFF_AGENT")
    ajo_loans_qs = AjoLoan.objects.filter()
    ajo_loan_repayment_qs = AjoLoanRepayment.objects.all()

    ajo_savings_transactions_qs = Transaction.objects.filter(
        plan_type="AJO",
        wallet_type="AJO_USER",
        status="SUCCESS",
        transaction_form_type="WALLET_DEPOSIT",
    )
    ajo_savings_table_qs = AjoSaving.objects.all()

    # Calculate performance metrics
    for officer in loan_officers_qs:
        loans_disbursed_by_agent_qs = ajo_loans_qs.filter(
            agent=officer, status__in=disbursed_loans_status_list
        )
        loans_disbursed_by_agent_yesterday_qs = loans_disbursed_by_agent_qs.filter(
            created_at__date=previous_day.date()
        )
        agent_loans_repayment_qs = ajo_loan_repayment_qs.filter(agent=officer)
        previous_day_agent_repayment_qs = ajo_loan_repayment_qs.filter(
            agent=officer, paid_date__date=previous_day.date()
        )
        agent_savings_qs = ajo_savings_transactions_qs.filter(user=officer)
        agent_ajo_savings_table_qs = ajo_savings_table_qs.filter(user=officer)
        agent_yesterday_savings_qs = agent_savings_qs.filter(
            date_created__date=previous_day.date()
        )

        # Loans Disbursed
        agent_total_loan_disbursed_amount = list(
            loans_disbursed_by_agent_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_loan_disbursed_count = list(
            loans_disbursed_by_agent_qs.aggregate(Count("amount")).values()
        )[0]
        agent_total_loan_disbursed_amount_yesterday = list(
            loans_disbursed_by_agent_yesterday_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_loan_disbursed_count_yesterday = list(
            loans_disbursed_by_agent_yesterday_qs.aggregate(Count("amount")).values()
        )[0]

        agent_total_loan_disbursed_amount = (
            agent_total_loan_disbursed_amount
            if agent_total_loan_disbursed_amount
            else 0.00
        )
        agent_total_loan_disbursed_count = (
            agent_total_loan_disbursed_count if agent_total_loan_disbursed_count else 0
        )
        agent_total_loan_disbursed_amount_yesterday = (
            agent_total_loan_disbursed_amount_yesterday
            if agent_total_loan_disbursed_amount_yesterday
            else 0.00
        )
        agent_total_loan_disbursed_count_yesterday = (
            agent_total_loan_disbursed_count_yesterday
            if agent_total_loan_disbursed_count_yesterday
            else 0
        )

        # Loan Repayments
        agent_total_loan_repayment_amount = list(
            agent_loans_repayment_qs.aggregate(Sum("repayment_amount")).values()
        )[0]
        agent_total_loan_repayment_count = list(
            agent_loans_repayment_qs.aggregate(Count("repayment_amount")).values()
        )[0]
        agent_total_loan_repayment_amount_yesterday = list(
            previous_day_agent_repayment_qs.aggregate(Sum("repayment_amount")).values()
        )[0]
        agent_total_loan_repayment_count_yesterday = list(
            previous_day_agent_repayment_qs.aggregate(
                Count("repayment_amount")
            ).values()
        )[0]
        expected_repayment_amount_yesterday = list(
            loans_disbursed_by_agent_qs.filter(created_at__lt=today)
            .aggregate(Sum("daily_repayment_amount"))
            .values()
        )[0]
        overall_expected_repayment_amount = list(
            loans_disbursed_by_agent_qs.filter(created_at__lt=today)
            .aggregate(Sum("repayment_amount"))
            .values()
        )[0]

        # Due Repayment Amount Yesterday
        due_loans_qs = ajo_loans_qs.filter(
            agent=officer, start_date__lte=timezone.now().date()
        )

        loans_due_amount_yesterday = 0
        for loan in due_loans_qs:
            days_due = (timezone.now().date() - loan.start_date).days
            amount_due = days_due * loan.daily_repayment_amount
            balance = amount_due - loan.total_paid_amount
            loans_due_amount_yesterday += balance

        agent_total_loan_repayment_amount = (
            agent_total_loan_repayment_amount
            if agent_total_loan_repayment_amount
            else 0.00
        )
        agent_total_loan_repayment_count = (
            agent_total_loan_repayment_count if agent_total_loan_repayment_count else 0
        )
        agent_total_loan_repayment_amount_yesterday = (
            agent_total_loan_repayment_amount_yesterday
            if agent_total_loan_repayment_amount_yesterday
            else 0.00
        )
        agent_total_loan_repayment_count_yesterday = (
            agent_total_loan_repayment_count_yesterday
            if agent_total_loan_repayment_count_yesterday
            else 0
        )
        expected_repayment_amount_yesterday = (
            expected_repayment_amount_yesterday
            if expected_repayment_amount_yesterday
            else 0.00
        )
        overall_expected_repayment_amount = (
            overall_expected_repayment_amount
            if overall_expected_repayment_amount
            else 0.00
        )

        outstanding_repayment_amount_yesterday = (
            expected_repayment_amount_yesterday
            - agent_total_loan_repayment_amount_yesterday
        )
        overall_outstanding_repayment_amount = (
            overall_expected_repayment_amount - agent_total_loan_repayment_amount
        )

        agent_repayment_vs_expected_percentage_yesterday = get_percentages(
            agent_total_loan_repayment_amount_yesterday,
            loans_due_amount_yesterday,
        )
        overall_agent_repayment_vs_expected_percentage = get_percentages(
            agent_total_loan_repayment_amount, overall_expected_repayment_amount
        )

        # Savings
        agent_total_savings_amount = list(
            agent_savings_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_savings_count = list(
            agent_savings_qs.aggregate(Count("amount")).values()
        )[0]
        agent_total_savings_amount_yesterday = list(
            agent_yesterday_savings_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_savings_count_yesterday = list(
            agent_yesterday_savings_qs.aggregate(Count("amount")).values()
        )[0]
        agent_total_active_saving_amount = list(
            agent_ajo_savings_table_qs.filter(is_active=True)
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]

        agent_total_savings_amount = (
            agent_total_savings_amount if agent_total_savings_amount else 0.00
        )
        agent_total_savings_count = (
            agent_total_savings_count if agent_total_savings_count else 0
        )
        agent_total_savings_amount_yesterday = (
            agent_total_savings_amount_yesterday
            if agent_total_savings_amount_yesterday
            else 0.00
        )
        agent_total_savings_count_yesterday = (
            agent_total_savings_count_yesterday
            if agent_total_savings_count_yesterday
            else 0
        )

        # Earnings
        # agent_total_earning = int((agent_total_loan_disbursed_amount if agent_total_loan_disbursed_amount else 0.00) * 0.0425)
        # agent_earning_yesterday = int((agent_total_loan_disbursed_amount_yesterday if agent_total_loan_disbursed_amount_yesterday else 0.00) * 0.0425)
        agent_current_month_earning = ""

        # Send Email to Loan Officer
        send_email = send_ajo_loan_officers_daily_check_email(
            message="This is your loan disbursement performance summary for yesterday.",
            email_subject="Previous Day Performance Summary",
            email=officer.email,
            name=officer.get_username(),
            date=datetime.now(),
            agent_total_loan_disbursed_amount=agent_total_loan_disbursed_amount,
            agent_total_loan_disbursed_count=agent_total_loan_disbursed_count,
            agent_total_loan_disbursed_amount_yesterday=agent_total_loan_disbursed_amount_yesterday,
            agent_total_loan_disbursed_count_yesterday=agent_total_loan_disbursed_count_yesterday,
            agent_total_loan_repayment_amount=agent_total_loan_repayment_amount,
            overall_expected_repayment_amount=overall_expected_repayment_amount,
            agent_total_loan_repayment_count=agent_total_loan_repayment_count,
            agent_total_loan_repayment_amount_yesterday=agent_total_loan_repayment_amount_yesterday,
            agent_total_loan_repayment_count_yesterday=agent_total_loan_repayment_count_yesterday,
            agent_total_savings_amount=agent_total_savings_amount,
            agent_total_active_saving_amount=(
                agent_total_active_saving_amount
                if agent_total_active_saving_amount
                else 0.00
            ),
            agent_total_savings_count=agent_total_savings_count,
            agent_total_savings_amount_yesterday=agent_total_savings_amount_yesterday,
            agent_total_savings_count_yesterday=agent_total_savings_count_yesterday,
            agent_current_month_earning=agent_current_month_earning,
            outstanding_repayment_amount_yesterday=loans_due_amount_yesterday,
            overall_outstanding_repayment_amount=overall_outstanding_repayment_amount,
            agent_repayment_vs_expected_percentage_yesterday=agent_repayment_vs_expected_percentage_yesterday,
            overall_agent_repayment_vs_expected_percentage=overall_agent_repayment_vs_expected_percentage,
        )

    return "DONE!!!!"


@shared_task
def run_loan_officers_weekly_summary():
    """
    - Checks the loan activities of loan officers for the week
    - Provides a summary of the week's loan activities
    - Generates an excel summary for every officer
    - Share the generated summary with the loan officers via email on a weekly basis
    """
    # Define Dates
    week_start = DateUtility().week_start
    previous_day = DateUtility().previous_day
    today = DateUtility().today_date

    # Write queries
    loan_officers_qs = User.objects.filter(user_type="STAFF_AGENT")
    ajo_loans_qs = AjoLoan.objects.filter()
    ajo_loan_repayment_qs = AjoLoanRepayment.objects.all()

    ajo_savings_transactions_qs = Transaction.objects.filter(
        plan_type="AJO",
        wallet_type="AJO_USER",
        status="SUCCESS",
        transaction_form_type="WALLET_DEPOSIT",
    )

    # Calculate performance metrics
    for officer in loan_officers_qs:
        loans_disbursed_by_agent_qs = ajo_loans_qs.filter(
            agent=officer, status__in=disbursed_loans_status_list
        )
        loans_disbursed_by_agent_week_qs = loans_disbursed_by_agent_qs.filter(
            created_at__gte=week_start
        )
        agent_loans_repayment_qs = ajo_loan_repayment_qs.filter(agent=officer)
        previous_day_agent_repayment_qs = ajo_loan_repayment_qs.filter(
            agent=officer, paid_date__gte=week_start
        )
        agent_savings_qs = ajo_savings_transactions_qs.filter(user=officer)
        agent_week_savings_qs = agent_savings_qs.filter(date_created__gte=week_start)

        # Loans Disbursed
        agent_total_loan_disbursed_amount = list(
            loans_disbursed_by_agent_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_loan_disbursed_count = list(
            loans_disbursed_by_agent_qs.aggregate(Count("amount")).values()
        )[0]
        agent_total_loan_disbursed_amount_week = list(
            loans_disbursed_by_agent_week_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_loan_disbursed_count_week = list(
            loans_disbursed_by_agent_week_qs.aggregate(Count("amount")).values()
        )[0]

        agent_total_loan_disbursed_amount = (
            agent_total_loan_disbursed_amount
            if agent_total_loan_disbursed_amount
            else 0.00
        )
        agent_total_loan_disbursed_count = (
            agent_total_loan_disbursed_count if agent_total_loan_disbursed_count else 0
        )
        agent_total_loan_disbursed_amount_week = (
            agent_total_loan_disbursed_amount_week
            if agent_total_loan_disbursed_amount_week
            else 0.00
        )
        agent_total_loan_disbursed_count_week = (
            agent_total_loan_disbursed_count_week
            if agent_total_loan_disbursed_count_week
            else 0
        )

        # Loan Repayments
        agent_total_loan_repayment_amount = list(
            agent_loans_repayment_qs.aggregate(Sum("repayment_amount")).values()
        )[0]
        agent_total_loan_repayment_count = list(
            agent_loans_repayment_qs.aggregate(Count("repayment_amount")).values()
        )[0]
        agent_total_loan_repayment_amount_week = list(
            previous_day_agent_repayment_qs.aggregate(Sum("repayment_amount")).values()
        )[0]
        agent_total_loan_repayment_count_week = list(
            previous_day_agent_repayment_qs.aggregate(
                Count("repayment_amount")
            ).values()
        )[0]
        expected_repayment_amount_week = list(
            loans_disbursed_by_agent_qs.filter(created_at__lt=week_start)
            .aggregate(Sum("daily_repayment_amount"))
            .values()
        )[0]
        overall_expected_repayment_amount = list(
            loans_disbursed_by_agent_qs.filter(created_at__lt=today)
            .aggregate(Sum("repayment_amount"))
            .values()
        )[0]

        # Due Repayment Amount Yesterday
        due_loans_qs = ajo_loans_qs.filter(
            agent=officer, start_date__lte=timezone.now().date()
        )

        loans_due_amount_yesterday = 0
        for loan in due_loans_qs:
            days_due = (timezone.now().date() - loan.start_date).days
            amount_due = days_due * loan.daily_repayment_amount
            balance = amount_due - loan.total_paid_amount
            loans_due_amount_yesterday += balance

        expected_repayment_amount_week = (
            expected_repayment_amount_week if expected_repayment_amount_week else 0.00
        )

        agent_total_loan_repayment_amount = (
            agent_total_loan_repayment_amount
            if agent_total_loan_repayment_amount
            else 0.00
        )
        agent_total_loan_repayment_count = (
            agent_total_loan_repayment_count if agent_total_loan_repayment_count else 0
        )
        agent_total_loan_repayment_amount_week = (
            agent_total_loan_repayment_amount_week
            if agent_total_loan_repayment_amount_week
            else 0.00
        )
        agent_total_loan_repayment_count_week = (
            agent_total_loan_repayment_count_week
            if agent_total_loan_repayment_count_week
            else 0
        )

        # Savings
        agent_total_savings_amount = list(
            agent_savings_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_savings_count = list(
            agent_savings_qs.aggregate(Count("amount")).values()
        )[0]
        agent_total_savings_amount_week = list(
            agent_week_savings_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_savings_count_week = list(
            agent_week_savings_qs.aggregate(Count("amount")).values()
        )[0]

        agent_total_savings_amount = (
            agent_total_savings_amount if agent_total_savings_amount else 0.00
        )
        agent_total_savings_count = (
            agent_total_savings_count if agent_total_savings_count else 0
        )
        agent_total_savings_amount_week = (
            agent_total_savings_amount_week if agent_total_savings_amount_week else 0.00
        )
        agent_total_savings_count_week = (
            agent_total_savings_count_week if agent_total_savings_count_week else 0
        )
        expected_repayment_amount_week = (
            expected_repayment_amount_week if expected_repayment_amount_week else 0.00
        )
        overall_expected_repayment_amount = (
            overall_expected_repayment_amount
            if overall_expected_repayment_amount
            else 0.00
        )

        outstanding_repayment_amount_week = (
            expected_repayment_amount_week - agent_total_loan_repayment_amount_week
        )
        overall_outstanding_repayment_amount = (
            overall_expected_repayment_amount - agent_total_loan_repayment_amount
        )

        agent_repayment_vs_expected_percentage_week = get_percentages(
            agent_total_loan_repayment_amount_week, loans_due_amount_yesterday
        )
        overall_agent_repayment_vs_expected_percentage = get_percentages(
            agent_total_loan_repayment_amount, overall_expected_repayment_amount
        )

        # Earnings
        # agent_total_earning = int((agent_total_loan_disbursed_amount if agent_total_loan_disbursed_amount else 0.00) * 0.0425)
        agent_earning_week = int(
            (
                agent_total_loan_disbursed_amount_week
                if agent_total_loan_disbursed_amount_week
                else 0.00
            )
            * 0.0425
        )

        # Send Email to Loan Officer
        # if settings.ENVIRONMENT == "development":
        send_email = send_ajo_loan_officers_weekly_check_email(
            message="This is your loan disbursement performance summary for the week.",
            # file="",
            # file_name=f"Previous Day Performance Summary_{datetime.now()}.xlsx",
            email_subject="Week Performance Summary",
            email=officer.email,
            name=officer.get_username(),
            date=datetime.now(),
            agent_total_loan_disbursed_amount=agent_total_loan_disbursed_amount,
            agent_total_loan_disbursed_count=agent_total_loan_disbursed_count,
            agent_total_loan_disbursed_amount_week=agent_total_loan_disbursed_amount_week,
            agent_total_loan_disbursed_count_week=agent_total_loan_disbursed_count_week,
            agent_total_loan_repayment_amount=agent_total_loan_repayment_amount,
            overall_expected_repayment_amount=overall_expected_repayment_amount,
            agent_total_loan_repayment_count=agent_total_loan_repayment_count,
            agent_total_loan_repayment_amount_week=agent_total_loan_repayment_amount_week,
            agent_total_loan_repayment_count_week=agent_total_loan_repayment_count_week,
            agent_total_savings_amount=agent_total_savings_amount,
            agent_total_savings_count=agent_total_savings_count,
            agent_total_savings_amount_week=agent_total_savings_amount_week,
            agent_total_savings_count_week=agent_total_savings_count_week,
            agent_earning_week=agent_earning_week,
            outstanding_repayment_amount_week=loans_due_amount_yesterday,
            overall_outstanding_repayment_amount=overall_outstanding_repayment_amount,
            agent_repayment_vs_expected_percentage_week=agent_repayment_vs_expected_percentage_week,
            overall_agent_repayment_vs_expected_percentage=overall_agent_repayment_vs_expected_percentage,
        )

    return "DONE!!!!"


@shared_task
def run_loan_officers_monthly_summary():
    """
    - Checks the loan activities of loan officers for the month
    - Provides a summary of the month's loan activities
    - Generates an excel summary for every officer
    - Share the generated summary with the loan officers via email on a monthly basis
    """
    # Define Dates
    # week_start = DateUtility().week_start
    # previous_day = DateUtility().previous_day
    month_start = DateUtility().month_start
    today = DateUtility().today_date

    # Write queries
    loan_officers_qs = User.objects.filter(user_type="STAFF_AGENT")
    ajo_loans_qs = AjoLoan.objects.filter()
    ajo_loan_repayment_qs = AjoLoanRepayment.objects.all()

    ajo_savings_transactions_qs = Transaction.objects.filter(
        plan_type="AJO",
        wallet_type="AJO_USER",
        status="SUCCESS",
        transaction_form_type="WALLET_DEPOSIT",
    )

    # Calculate performance metrics
    for officer in loan_officers_qs:
        loans_disbursed_by_agent_qs = ajo_loans_qs.filter(
            agent=officer, status__in=disbursed_loans_status_list
        )
        loans_disbursed_by_agent_month_qs = loans_disbursed_by_agent_qs.filter(
            created_at__gte=month_start
        )
        agent_loans_repayment_qs = ajo_loan_repayment_qs.filter(agent=officer)
        previous_day_agent_repayment_qs = ajo_loan_repayment_qs.filter(
            agent=officer, paid_date__gte=month_start
        )
        agent_savings_qs = ajo_savings_transactions_qs.filter(user=officer)
        agent_month_savings_qs = agent_savings_qs.filter(date_created__gte=month_start)

        # Loans Disbursed
        agent_total_loan_disbursed_amount = list(
            loans_disbursed_by_agent_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_loan_disbursed_count = list(
            loans_disbursed_by_agent_qs.aggregate(Count("amount")).values()
        )[0]
        agent_total_loan_disbursed_amount_month = list(
            loans_disbursed_by_agent_month_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_loan_disbursed_count_month = list(
            loans_disbursed_by_agent_month_qs.aggregate(Count("amount")).values()
        )[0]

        agent_total_loan_disbursed_amount = (
            agent_total_loan_disbursed_amount
            if agent_total_loan_disbursed_amount
            else 0.00
        )
        agent_total_loan_disbursed_count = (
            agent_total_loan_disbursed_count if agent_total_loan_disbursed_count else 0
        )
        agent_total_loan_disbursed_amount_month = (
            agent_total_loan_disbursed_amount_month
            if agent_total_loan_disbursed_amount_month
            else 0.00
        )
        agent_total_loan_disbursed_count_month = (
            agent_total_loan_disbursed_count_month
            if agent_total_loan_disbursed_count_month
            else 0
        )

        # Loan Repayments
        agent_total_loan_repayment_amount = list(
            agent_loans_repayment_qs.aggregate(Sum("repayment_amount")).values()
        )[0]
        agent_total_loan_repayment_count = list(
            agent_loans_repayment_qs.aggregate(Count("repayment_amount")).values()
        )[0]
        agent_total_loan_repayment_amount_month = list(
            previous_day_agent_repayment_qs.aggregate(Sum("repayment_amount")).values()
        )[0]
        agent_total_loan_repayment_count_month = list(
            previous_day_agent_repayment_qs.aggregate(
                Count("repayment_amount")
            ).values()
        )[0]
        expected_repayment_amount_month = list(
            loans_disbursed_by_agent_qs.filter(created_at__lt=month_start)
            .aggregate(Sum("repayment_amount"))
            .values()
        )[0]
        overall_expected_repayment_amount = list(
            loans_disbursed_by_agent_qs.filter(created_at__lt=today)
            .aggregate(Sum("repayment_amount"))
            .values()
        )[0]
        expected_repayment_amount_month = (
            expected_repayment_amount_month if expected_repayment_amount_month else 0.00
        )

        agent_total_loan_repayment_amount = (
            agent_total_loan_repayment_amount
            if agent_total_loan_repayment_amount
            else 0.00
        )
        agent_total_loan_repayment_count = (
            agent_total_loan_repayment_count if agent_total_loan_repayment_count else 0
        )
        agent_total_loan_repayment_amount_month = (
            agent_total_loan_repayment_amount_month
            if agent_total_loan_repayment_amount_month
            else 0.00
        )
        agent_total_loan_repayment_count_month = (
            agent_total_loan_repayment_count_month
            if agent_total_loan_repayment_count_month
            else 0
        )
        expected_repayment_amount_month = (
            expected_repayment_amount_month if expected_repayment_amount_month else 0.00
        )
        overall_expected_repayment_amount = (
            overall_expected_repayment_amount
            if overall_expected_repayment_amount
            else 0.00
        )

        outstanding_repayment_amount_month = (
            expected_repayment_amount_month - agent_total_loan_repayment_amount_month
        )
        overall_outstanding_repayment_amount = (
            overall_expected_repayment_amount - agent_total_loan_repayment_amount
        )

        agent_repayment_vs_expected_percentage_month = get_percentages(
            agent_total_loan_repayment_amount_month, expected_repayment_amount_month
        )
        overall_agent_repayment_vs_expected_percentage = get_percentages(
            agent_total_loan_repayment_amount, overall_expected_repayment_amount
        )

        # Due Repayment Amount Yesterday
        due_loans_qs = ajo_loans_qs.filter(
            agent=officer, start_date__lte=timezone.now().date()
        )

        loans_due_amount_yesterday = 0
        for loan in due_loans_qs:
            days_due = (timezone.now().date() - loan.start_date).days
            amount_due = days_due * loan.daily_repayment_amount
            balance = amount_due - loan.total_paid_amount
            loans_due_amount_yesterday += balance

        # Savings
        agent_total_savings_amount = list(
            agent_savings_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_savings_count = list(
            agent_savings_qs.aggregate(Count("amount")).values()
        )[0]
        agent_total_savings_amount_month = list(
            agent_month_savings_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_savings_count_month = list(
            agent_month_savings_qs.aggregate(Count("amount")).values()
        )[0]

        agent_total_savings_amount = (
            agent_total_savings_amount if agent_total_savings_amount else 0.00
        )
        agent_total_savings_count = (
            agent_total_savings_count if agent_total_savings_count else 0
        )
        agent_total_savings_amount_month = (
            agent_total_savings_amount_month
            if agent_total_savings_amount_month
            else 0.00
        )
        agent_total_savings_count_month = (
            agent_total_savings_count_month if agent_total_savings_count_month else 0
        )

        # Earnings
        # agent_total_earning = int((agent_total_loan_disbursed_amount if agent_total_loan_disbursed_amount else 0.00) * 0.0425)
        agent_earning_month = 0.00

        # Send Email to Loan Officer
        # if settings.ENVIRONMENT == "development":
        send_email = send_ajo_loan_officers_monthly_check_email(
            message="This is your loan disbursement performance summary for the month.",
            # file="",
            # file_name=f"Previous Day Performance Summary_{datetime.now()}.xlsx",
            email_subject="Month Performance Summary",
            email=officer.email,
            name=officer.get_username(),
            date=datetime.now(),
            agent_total_loan_disbursed_amount=agent_total_loan_disbursed_amount,
            agent_total_loan_disbursed_count=agent_total_loan_disbursed_count,
            agent_total_loan_disbursed_amount_month=agent_total_loan_disbursed_amount_month,
            agent_total_loan_disbursed_count_month=agent_total_loan_disbursed_count_month,
            agent_total_loan_repayment_amount=agent_total_loan_repayment_amount,
            overall_expected_repayment_amount=overall_expected_repayment_amount,
            agent_total_loan_repayment_count=agent_total_loan_repayment_count,
            agent_total_loan_repayment_amount_month=agent_total_loan_repayment_amount_month,
            agent_total_loan_repayment_count_month=agent_total_loan_repayment_count_month,
            agent_total_savings_amount=agent_total_savings_amount,
            agent_total_savings_count=agent_total_savings_count,
            agent_total_savings_amount_month=agent_total_savings_amount_month,
            agent_total_savings_count_month=agent_total_savings_count_month,
            agent_earning_month=agent_earning_month,
            outstanding_repayment_amount_month=loans_due_amount_yesterday,
            overall_outstanding_repayment_amount=overall_outstanding_repayment_amount,
            agent_repayment_vs_expected_percentage_month=agent_repayment_vs_expected_percentage_month,
            overall_agent_repayment_vs_expected_percentage=overall_agent_repayment_vs_expected_percentage,
        )
    return "DONE!!!!"


@shared_task
def run_loan_officers_daily_report():
    # Define Dates
    previous_day = DateUtility().previous_day
    today = timezone.now()

    # Write queries
    loan_officers_qs = User.objects.filter(user_type="STAFF_AGENT")
    ajo_loans_qs = AjoLoan.objects.filter()
    ajo_loan_repayment_qs = AjoLoanRepayment.objects.all()

    ajo_savings_transactions_qs = Transaction.objects.filter(
        plan_type="AJO",
        wallet_type="AJO_USER",
        status="SUCCESS",
        transaction_form_type="WALLET_DEPOSIT",
    )
    ajo_savings_table_qs = AjoSaving.objects.all()
    loan_officers_report_list = []

    # Calculate performance metrics
    for officer in loan_officers_qs:
        loans_disbursed_by_agent_qs = ajo_loans_qs.filter(
            agent=officer, status__in=disbursed_loans_status_list
        )
        loans_disbursed_by_agent_yesterday_qs = loans_disbursed_by_agent_qs.filter(
            created_at__date=previous_day.date()
        )
        agent_loans_repayment_qs = ajo_loan_repayment_qs.filter(agent=officer)
        previous_day_agent_repayment_qs = ajo_loan_repayment_qs.filter(
            agent=officer, paid_date__date=previous_day.date()
        )
        agent_savings_qs = ajo_savings_transactions_qs.filter(user=officer)
        agent_ajo_savings_table_qs = ajo_savings_table_qs.filter(user=officer)
        agent_yesterday_savings_qs = agent_savings_qs.filter(
            date_created__date=previous_day.date()
        )

        # Loans Disbursed
        agent_total_loan_disbursed_amount = list(
            loans_disbursed_by_agent_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_loan_disbursed_count = list(
            loans_disbursed_by_agent_qs.aggregate(Count("amount")).values()
        )[0]
        agent_total_loan_disbursed_amount_yesterday = list(
            loans_disbursed_by_agent_yesterday_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_loan_disbursed_count_yesterday = list(
            loans_disbursed_by_agent_yesterday_qs.aggregate(Count("amount")).values()
        )[0]

        agent_total_loan_disbursed_amount = (
            agent_total_loan_disbursed_amount
            if agent_total_loan_disbursed_amount
            else 0.00
        )
        agent_total_loan_disbursed_count = (
            agent_total_loan_disbursed_count if agent_total_loan_disbursed_count else 0
        )
        agent_total_loan_disbursed_amount_yesterday = (
            agent_total_loan_disbursed_amount_yesterday
            if agent_total_loan_disbursed_amount_yesterday
            else 0.00
        )
        agent_total_loan_disbursed_count_yesterday = (
            agent_total_loan_disbursed_count_yesterday
            if agent_total_loan_disbursed_count_yesterday
            else 0
        )

        # Loan Repayments
        agent_total_loan_repayment_amount = list(
            agent_loans_repayment_qs.aggregate(Sum("repayment_amount")).values()
        )[0]

        agent_total_loan_repayment_count = list(
            agent_loans_repayment_qs.aggregate(Count("repayment_amount")).values()
        )[0]

        agent_total_loan_repayment_amount_yesterday = list(
            previous_day_agent_repayment_qs.aggregate(Sum("repayment_amount")).values()
        )[0]

        agent_total_loan_repayment_count_yesterday = list(
            previous_day_agent_repayment_qs.aggregate(
                Count("repayment_amount")
            ).values()
        )[0]

        expected_repayment_amount_yesterday = list(
            loans_disbursed_by_agent_qs.filter(created_at__lt=today)
            .aggregate(Sum("daily_repayment_amount"))
            .values()
        )[0]

        # Due Repayment Amount Yesterday
        due_loans_qs = ajo_loans_qs.filter(
            agent=officer, start_date__lte=timezone.now().date()
        )

        loans_due_amount_yesterday = 0
        for loan in due_loans_qs:
            days_due = (timezone.now().date() - loan.start_date).days
            amount_due = days_due * loan.daily_repayment_amount
            balance = amount_due - loan.total_paid_amount
            loans_due_amount_yesterday += balance

        overall_expected_repayment_amount = list(
            loans_disbursed_by_agent_qs.filter(created_at__lt=today)
            .aggregate(Sum("repayment_amount"))
            .values()
        )[0]

        agent_total_loan_repayment_amount = (
            agent_total_loan_repayment_amount
            if agent_total_loan_repayment_amount
            else 0.00
        )

        agent_total_loan_repayment_count = (
            agent_total_loan_repayment_count if agent_total_loan_repayment_count else 0
        )

        agent_total_loan_repayment_amount_yesterday = (
            agent_total_loan_repayment_amount_yesterday
            if agent_total_loan_repayment_amount_yesterday
            else 0.00
        )

        agent_total_loan_repayment_count_yesterday = (
            agent_total_loan_repayment_count_yesterday
            if agent_total_loan_repayment_count_yesterday
            else 0
        )

        expected_repayment_amount_yesterday = (
            expected_repayment_amount_yesterday
            if expected_repayment_amount_yesterday
            else 0.00
        )

        overall_expected_repayment_amount = (
            overall_expected_repayment_amount
            if overall_expected_repayment_amount
            else 0.00
        )

        outstanding_repayment_amount_yesterday = (
            expected_repayment_amount_yesterday
            - agent_total_loan_repayment_amount_yesterday
        )

        overall_outstanding_repayment_amount = (
            overall_expected_repayment_amount - agent_total_loan_repayment_amount
        )

        agent_repayment_vs_expected_percentage_yesterday = get_percentages(
            agent_total_loan_repayment_amount_yesterday,
            loans_due_amount_yesterday,
        )
        overall_agent_repayment_vs_expected_percentage = get_percentages(
            agent_total_loan_repayment_amount, overall_expected_repayment_amount
        )

        # Savings
        agent_total_savings_amount = list(
            agent_savings_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_savings_count = list(
            agent_savings_qs.aggregate(Count("amount")).values()
        )[0]
        agent_total_savings_amount_yesterday = list(
            agent_yesterday_savings_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_savings_count_yesterday = list(
            agent_yesterday_savings_qs.aggregate(Count("amount")).values()
        )[0]
        agent_total_active_saving_amount = list(
            agent_ajo_savings_table_qs.filter(is_active=True)
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]

        agent_total_savings_amount = (
            agent_total_savings_amount if agent_total_savings_amount else 0.00
        )
        agent_total_savings_count = (
            agent_total_savings_count if agent_total_savings_count else 0
        )
        agent_total_savings_amount_yesterday = (
            agent_total_savings_amount_yesterday
            if agent_total_savings_amount_yesterday
            else 0.00
        )
        agent_total_savings_count_yesterday = (
            agent_total_savings_count_yesterday
            if agent_total_savings_count_yesterday
            else 0
        )

        # Earnings
        # agent_total_earning = int((agent_total_loan_disbursed_amount if agent_total_loan_disbursed_amount else 0.00) * 0.0425)
        # agent_earning_yesterday = int((agent_total_loan_disbursed_amount_yesterday if agent_total_loan_disbursed_amount_yesterday else 0.00) * 0.0425)
        agent_current_month_earning = 0

        # Extra data from Vico
        transactions_count = list(
            ajo_savings_transactions_qs.filter(
                user=officer,
                transaction_type="CREDIT",
                # date_created__date=previous_day.date()
            )
            .aggregate(Count("amount"))
            .values()
        )[0]

        login_counts = AgencyBankingClass.get_agency_user_details(
            user_id=officer.customer_user_id, start_date=previous_day.date()
        )

        if "success" in login_counts and login_counts.get("success") == True:
            login_count = login_counts.get("login_count")
            supervisor = login_counts.get("supervisor")
            branch = login_counts.get("branch")
        else:
            login_count = 45
            supervisor = ""
            branch = ""

        avg_daily_savings = 3826
        avg_daily_trnx_count = 5
        avg_daily_loan = 15306

        agent_age_days = (datetime.now().date() - officer.created_at.date()).days
        savings_performance = round(
            (agent_total_savings_amount_yesterday / avg_daily_savings) * 100
        )
        loans_performance = round(
            (agent_total_loan_disbursed_amount_yesterday / avg_daily_loan) * 100
        )
        overall_performance = round((savings_performance + loans_performance) / 2)

        # Set Activity
        if agent_age_days > 30 and login_count > 5:
            activity = "Active Old"
        elif agent_age_days <= 30 and login_count > 5:
            activity = "Active New"
        elif agent_age_days <= 30 and login_count < 5:
            activity = "Inactive New"
        else:
            activity = "Inactive Old"

        # Set Churn
        if (
            activity == "Active Old"
            and agent_total_savings_count_yesterday < 1
            and login_count < 5
        ):
            churn = "Churned"
        else:
            churn = "Not Churned"

        data = {
            "Email": officer.get_username(),
            "Supervisor": supervisor,
            "Branch": branch,
            "Activity": activity,
            "Total Disbursed Amount": agent_total_loan_disbursed_amount,
            "Total Disbursed Count": agent_total_loan_disbursed_count,
            "Total Disbursed Amount Yesterday": agent_total_loan_disbursed_amount_yesterday,
            "Total Disbursed Count Yesterday": agent_total_loan_disbursed_count_yesterday,
            "Overall Expected Repayment Amount": overall_expected_repayment_amount,
            "Total Repayment Amount": agent_total_loan_repayment_amount,
            "Total Repayment Count": agent_total_loan_repayment_count,
            "Total Repayment Amount Yesterday": agent_total_loan_repayment_amount_yesterday,
            "Total Repayment Count Yesterday": agent_total_loan_repayment_count_yesterday,
            "Due Repayment Amount Yesterday": loans_due_amount_yesterday,
            "Loan Balance Amount": overall_outstanding_repayment_amount,
            "Repayment vs Expected Percentage Yesterday": f"{agent_repayment_vs_expected_percentage_yesterday}%",
            "Overall Repayment vs Expected Percentage": f"{overall_agent_repayment_vs_expected_percentage}%",
            "Total Savings Amount": agent_total_savings_amount,
            "Total Savings Count": agent_total_savings_count,
            "Total Active Savings Amount": agent_total_active_saving_amount,
            "Total Savings Amount Yesterday": agent_total_savings_amount_yesterday,
            "Total Savings Count Yesterday": agent_total_savings_count_yesterday,
            # "current_month_earning": agent_current_month_earning,
            # Vico extra
            "Transaction Count": transactions_count if transactions_count else 0,
            "Savings Performance": f"{savings_performance}%",
            "Loans Performance": f"{loans_performance}%",
            "Login Count": login_count,
            "Churned": churn,
            "Activity": activity,
            "Overall Performance": f"{overall_performance}%",
        }
        loan_officers_report_list.append(data)

    df = pd.DataFrame().from_dict(loan_officers_report_list)
    file_path = os.path.join(settings.BASE_DIR, "media/loan_officers")

    try:
        os.mkdir(file_path)
    except:
        pass

    excel_report = df.to_excel(
        f"{file_path}/loan_officers_daily_performance_{datetime.now().date()}.xlsx",
        index=False,
    )

    with open(
        f"{file_path}/loan_officers_daily_performance_{datetime.now().date()}.xlsx",
        "rb",
    ) as read_file:
        excel_file = read_file.read()

    for email in admin_email_list:
        send_email = send_ajo_loan_officers_daily_report_email(
            message="This is the Loan officers performance report for yesterday",
            file=excel_file,
            file_name=f"Loan Officers Daily Report_{datetime.now().date()}.xlsx",
            email_subject="Loan Officers Daily Performance Report",
            email=email,
            name="Team",
            date=datetime.now(),
        )

    return "DONE!!!"


@shared_task
def run_loan_officers_weekly_report():
    # Define Dates
    week_start = DateUtility().week_start
    previous_day = DateUtility().previous_day
    today = DateUtility().today_date

    # Write queries
    loan_officers_qs = User.objects.filter(user_type="STAFF_AGENT")
    ajo_loans_qs = AjoLoan.objects.filter()
    ajo_loan_repayment_qs = AjoLoanRepayment.objects.all()
    ajo_savings_table_qs = AjoSaving.objects.all()

    ajo_savings_transactions_qs = Transaction.objects.filter(
        plan_type="AJO",
        wallet_type="AJO_USER",
        status="SUCCESS",
        transaction_form_type="WALLET_DEPOSIT",
    )
    loan_officers_report_list = []

    # Calculate performance metrics
    for officer in loan_officers_qs:
        loans_disbursed_by_agent_qs = ajo_loans_qs.filter(
            agent=officer, status__in=disbursed_loans_status_list
        )
        loans_disbursed_by_agent_week_qs = loans_disbursed_by_agent_qs.filter(
            created_at__gte=week_start
        )
        agent_loans_repayment_qs = ajo_loan_repayment_qs.filter(agent=officer)
        previous_day_agent_repayment_qs = ajo_loan_repayment_qs.filter(
            agent=officer, paid_date__gte=week_start
        )
        agent_savings_qs = ajo_savings_transactions_qs.filter(user=officer)
        agent_week_savings_qs = agent_savings_qs.filter(date_created__gte=week_start)
        agent_ajo_savings_table_qs = ajo_savings_table_qs.filter(user=officer)

        # Loans Disbursed
        agent_total_loan_disbursed_amount = list(
            loans_disbursed_by_agent_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_loan_disbursed_count = list(
            loans_disbursed_by_agent_qs.aggregate(Count("amount")).values()
        )[0]
        agent_total_loan_disbursed_amount_week = list(
            loans_disbursed_by_agent_week_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_loan_disbursed_count_week = list(
            loans_disbursed_by_agent_week_qs.aggregate(Count("amount")).values()
        )[0]

        agent_total_loan_disbursed_amount = (
            agent_total_loan_disbursed_amount
            if agent_total_loan_disbursed_amount
            else 0.00
        )
        agent_total_loan_disbursed_count = (
            agent_total_loan_disbursed_count if agent_total_loan_disbursed_count else 0
        )

        agent_total_loan_disbursed_amount_week = (
            agent_total_loan_disbursed_amount_week
            if agent_total_loan_disbursed_amount_week
            else 0.00
        )
        agent_total_loan_disbursed_count_week = (
            agent_total_loan_disbursed_count_week
            if agent_total_loan_disbursed_count_week
            else 0
        )

        # Loan Repayments
        agent_total_loan_repayment_amount = list(
            agent_loans_repayment_qs.aggregate(Sum("repayment_amount")).values()
        )[0]
        agent_total_loan_repayment_count = list(
            agent_loans_repayment_qs.aggregate(Count("repayment_amount")).values()
        )[0]
        agent_total_loan_repayment_amount_week = list(
            previous_day_agent_repayment_qs.aggregate(Sum("repayment_amount")).values()
        )[0]
        agent_total_loan_repayment_count_week = list(
            previous_day_agent_repayment_qs.aggregate(
                Count("repayment_amount")
            ).values()
        )[0]
        expected_repayment_amount_week = list(
            loans_disbursed_by_agent_qs.filter(created_at__lt=week_start)
            .aggregate(Count("daily_repayment_amount"))
            .values()
        )[0]
        overall_expected_repayment_amount = list(
            loans_disbursed_by_agent_qs.filter(created_at__lt=today)
            .aggregate(Sum("repayment_amount"))
            .values()
        )[0]
        expected_repayment_amount_week = (
            expected_repayment_amount_week if expected_repayment_amount_week else 0.00
        )

        agent_total_loan_repayment_amount = (
            agent_total_loan_repayment_amount
            if agent_total_loan_repayment_amount
            else 0.00
        )
        agent_total_loan_repayment_count = (
            agent_total_loan_repayment_count if agent_total_loan_repayment_count else 0
        )
        agent_total_loan_repayment_amount_week = (
            agent_total_loan_repayment_amount_week
            if agent_total_loan_repayment_amount_week
            else 0.00
        )
        agent_total_loan_repayment_count_week = (
            agent_total_loan_repayment_count_week
            if agent_total_loan_repayment_count_week
            else 0
        )

        # Due Repayment Amount Yesterday
        due_loans_qs = ajo_loans_qs.filter(
            agent=officer, start_date__lte=timezone.now().date()
        )

        loans_due_amount = 0
        for loan in due_loans_qs:
            days_due = (timezone.now().date() - loan.start_date).days
            amount_due = days_due * loan.daily_repayment_amount
            balance = amount_due - loan.total_paid_amount
            loans_due_amount += balance

        # Savings
        agent_total_savings_amount = list(
            agent_savings_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_active_savings_amount = list(
            agent_ajo_savings_table_qs.filter(is_active=True)
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]
        agent_total_savings_count = list(
            agent_savings_qs.aggregate(Count("amount")).values()
        )[0]
        agent_total_savings_amount_week = list(
            agent_week_savings_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_savings_count_week = list(
            agent_week_savings_qs.aggregate(Count("amount")).values()
        )[0]

        agent_total_savings_amount = (
            agent_total_savings_amount if agent_total_savings_amount else 0.00
        )
        agent_total_active_savings_amount = (
            agent_total_active_savings_amount
            if agent_total_active_savings_amount
            else 0.00
        )
        agent_total_savings_count = (
            agent_total_savings_count if agent_total_savings_count else 0
        )
        agent_total_savings_amount_week = (
            agent_total_savings_amount_week if agent_total_savings_amount_week else 0.00
        )
        agent_total_savings_count_week = (
            agent_total_savings_count_week if agent_total_savings_count_week else 0
        )
        expected_repayment_amount_week = (
            expected_repayment_amount_week if expected_repayment_amount_week else 0.00
        )
        overall_expected_repayment_amount = (
            overall_expected_repayment_amount
            if overall_expected_repayment_amount
            else 0.00
        )

        outstanding_repayment_amount_week = (
            expected_repayment_amount_week - agent_total_loan_repayment_amount_week
        )
        overall_outstanding_repayment_amount = (
            overall_expected_repayment_amount - agent_total_loan_repayment_amount
        )

        agent_repayment_vs_expected_percentage_week = get_percentages(
            agent_total_loan_repayment_amount_week, loans_due_amount
        )
        overall_agent_repayment_vs_expected_percentage = get_percentages(
            agent_total_loan_repayment_amount, overall_expected_repayment_amount
        )

        # Earnings
        # agent_total_earning = int((agent_total_loan_disbursed_amount if agent_total_loan_disbursed_amount else 0.00) * 0.0425)
        agent_earning_week = int(
            (
                agent_total_loan_disbursed_amount_week
                if agent_total_loan_disbursed_amount_week
                else 0.00
            )
            * 0.0425
        )

        # Extra data from Vico
        transactions_count = list(
            ajo_savings_transactions_qs.filter(user=officer, transaction_type="CREDIT")
            .aggregate(Count("amount"))
            .values()
        )[0]

        login_counts = AgencyBankingClass.get_agency_user_details(
            user_id=officer.customer_user_id, start_date=week_start
        )

        if "success" in login_counts and login_counts.get("success") == True:
            login_count = login_counts.get("login_count")
            supervisor = login_counts.get("supervisor")
            branch = login_counts.get("branch")
        else:
            login_count = 45
            supervisor = ""
            branch = ""

        avg_weekly_savings = 187500
        avg_weekly_trnx_count = 38
        avg_Weekly_loan = 750000

        agent_age_days = (datetime.now().date() - officer.created_at.date()).days
        savings_performance = round(
            (agent_total_savings_amount_week / avg_weekly_savings) * 100
        )
        loans_performance = round(
            (agent_total_loan_disbursed_amount_week / avg_Weekly_loan) * 100
        )
        overall_performance = round((savings_performance + loans_performance) / 2)

        # Set Activity
        if agent_age_days > 30 and login_count > 40:
            activity = "Active Old"
        elif agent_age_days <= 30 and login_count > 20:
            activity = "Active New"
        elif agent_age_days <= 30 and login_count < 20:
            activity = "Inactive New"
        else:
            activity = "Inactive Old"

        # Set Churn
        if (
            activity == "Active Old"
            and agent_total_savings_count_week < 1
            and login_count < 10
        ):
            churn = "Churned"
        else:
            churn = "Not Churned"

        data = {
            "Email": officer.get_username(),
            "Name": officer.get_username(),
            "Branch": branch,
            "Supervisor": supervisor,
            "Total Disbursed Amount": agent_total_loan_disbursed_amount,
            "Total Disbursed Count": agent_total_loan_disbursed_count,
            "Total Disbursed Amount Week": agent_total_loan_disbursed_amount_week,
            "Total Disbursed Count Week": agent_total_loan_disbursed_count_week,
            "Overall Expected Repayment Amount": overall_expected_repayment_amount,
            "Total Repayment Amount": agent_total_loan_repayment_amount,
            "Total Repayment Count": agent_total_loan_repayment_count,
            "Total Repayment Amount Week": agent_total_loan_repayment_amount_week,
            "Total Repayment Count Week": agent_total_loan_repayment_count_week,
            "Due Repayment Amount Week": loans_due_amount,
            "Loan Balance Amount": overall_outstanding_repayment_amount,
            "Repayment vs Expected Percentage Week": f"{round(agent_repayment_vs_expected_percentage_week)}%",
            "Overall Repayment vs Expected Percentage": f"{round(overall_agent_repayment_vs_expected_percentage)}%",
            "Total Savings Amount": agent_total_savings_amount,
            "Total Savings Count": agent_total_savings_count,
            "Total Active Savings Amount": agent_total_active_savings_amount,
            "Total Savings Amount Week": agent_total_savings_amount_week,
            "Total Savings Count Week": agent_total_savings_count_week,
            # Vico extra
            "Transaction Count": transactions_count if transactions_count else 0,
            "Savings Performance": f"{savings_performance}%",
            "Loans Performance": f"{loans_performance}%",
            "Login Count": login_count,
            "Churned": churn,
            "Activity": activity,
            "Overall Performance": f"{overall_performance}%",
        }
        loan_officers_report_list.append(data)

    df = pd.DataFrame().from_dict(loan_officers_report_list)
    file_path = os.path.join(settings.BASE_DIR, "media/loan_officers")

    try:
        os.mkdir(file_path)
    except:
        pass

    excel_report = df.to_excel(
        f"{file_path}/loan_officers_weekly_performance_{datetime.now().date()}.xlsx"
    )

    with open(
        f"{file_path}/loan_officers_weekly_performance_{datetime.now().date()}.xlsx",
        "rb",
    ) as read_file:
        excel_file = read_file.read()

    for email in admin_email_list:
        send_email = send_ajo_loan_officers_daily_report_email(
            message="This is the Loan officers performance report for the week",
            file=excel_file,
            file_name=f"Loan Officers Weekly Report_{datetime.now().date()}.xlsx",
            email_subject="Loan Officers Weekly Performance Report",
            email=email,
            name="Team",
            date=datetime.now(),
        )

    return "DONE!!!"


@shared_task
def run_loan_officers_monthly_report():
    # Define Dates
    # week_start = DateUtility().week_start
    # previous_day = DateUtility().previous_day
    month_start = DateUtility().month_start
    today = DateUtility().today_date

    # Write queries
    loan_officers_qs = User.objects.filter(user_type="STAFF_AGENT")
    ajo_loans_qs = AjoLoan.objects.filter()
    ajo_loan_repayment_qs = AjoLoanRepayment.objects.all()
    ajo_savings_table_qs = AjoSaving.objects.all()

    ajo_savings_transactions_qs = Transaction.objects.filter(
        plan_type="AJO",
        wallet_type="AJO_USER",
        status="SUCCESS",
        transaction_form_type="WALLET_DEPOSIT",
    )
    loan_officers_report_list = []

    # Calculate performance metrics
    for officer in loan_officers_qs:
        loans_disbursed_by_agent_qs = ajo_loans_qs.filter(
            agent=officer, status__in=disbursed_loans_status_list
        )
        loans_disbursed_by_agent_month_qs = loans_disbursed_by_agent_qs.filter(
            created_at__gte=month_start
        )
        agent_loans_repayment_qs = ajo_loan_repayment_qs.filter(agent=officer)
        previous_day_agent_repayment_qs = ajo_loan_repayment_qs.filter(
            agent=officer, paid_date__gte=month_start
        )
        agent_savings_qs = ajo_savings_transactions_qs.filter(user=officer)
        agent_month_savings_qs = agent_savings_qs.filter(date_created__gte=month_start)

        # Loans Disbursed
        agent_total_loan_disbursed_amount = list(
            loans_disbursed_by_agent_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_loan_disbursed_count = list(
            loans_disbursed_by_agent_qs.aggregate(Count("amount")).values()
        )[0]
        agent_total_loan_disbursed_amount_month = list(
            loans_disbursed_by_agent_month_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_loan_disbursed_count_month = list(
            loans_disbursed_by_agent_month_qs.aggregate(Count("amount")).values()
        )[0]

        agent_total_loan_disbursed_amount = (
            agent_total_loan_disbursed_amount
            if agent_total_loan_disbursed_amount
            else 0.00
        )
        agent_total_loan_disbursed_count = (
            agent_total_loan_disbursed_count if agent_total_loan_disbursed_count else 0
        )
        agent_total_loan_disbursed_amount_month = (
            agent_total_loan_disbursed_amount_month
            if agent_total_loan_disbursed_amount_month
            else 0.00
        )
        agent_total_loan_disbursed_count_month = (
            agent_total_loan_disbursed_count_month
            if agent_total_loan_disbursed_count_month
            else 0
        )

        # Loan Repayments
        agent_total_loan_repayment_amount = list(
            agent_loans_repayment_qs.aggregate(Sum("repayment_amount")).values()
        )[0]
        agent_total_loan_repayment_count = list(
            agent_loans_repayment_qs.aggregate(Count("repayment_amount")).values()
        )[0]
        agent_total_loan_repayment_amount_month = list(
            previous_day_agent_repayment_qs.aggregate(Sum("repayment_amount")).values()
        )[0]
        agent_total_loan_repayment_count_month = list(
            previous_day_agent_repayment_qs.aggregate(
                Count("repayment_amount")
            ).values()
        )[0]
        expected_repayment_amount_month = list(
            loans_disbursed_by_agent_qs.filter(created_at__lt=month_start)
            .aggregate(Sum("daily_repayment_amount"))
            .values()
        )[0]
        overall_expected_repayment_amount = list(
            loans_disbursed_by_agent_qs.filter(created_at__lt=today)
            .aggregate(Sum("repayment_amount"))
            .values()
        )[0]
        expected_repayment_amount_month = (
            expected_repayment_amount_month if expected_repayment_amount_month else 0.00
        )

        agent_total_loan_repayment_amount = (
            agent_total_loan_repayment_amount
            if agent_total_loan_repayment_amount
            else 0.00
        )
        agent_total_loan_repayment_count = (
            agent_total_loan_repayment_count if agent_total_loan_repayment_count else 0
        )
        agent_total_loan_repayment_amount_month = (
            agent_total_loan_repayment_amount_month
            if agent_total_loan_repayment_amount_month
            else 0.00
        )
        agent_total_loan_repayment_count_month = (
            agent_total_loan_repayment_count_month
            if agent_total_loan_repayment_count_month
            else 0
        )
        expected_repayment_amount_month = (
            expected_repayment_amount_month if expected_repayment_amount_month else 0.00
        )
        overall_expected_repayment_amount = (
            overall_expected_repayment_amount
            if overall_expected_repayment_amount
            else 0.00
        )

        # Due Repayment Amount Yesterday
        due_loans_qs = ajo_loans_qs.filter(
            agent=officer, start_date__lte=timezone.now().date()
        )

        loans_due_amount = 0
        for loan in due_loans_qs:
            days_due = (timezone.now().date() - loan.start_date).days
            amount_due = days_due * loan.daily_repayment_amount
            balance = amount_due - loan.total_paid_amount
            loans_due_amount += balance

        outstanding_repayment_amount_month = (
            expected_repayment_amount_month - agent_total_loan_repayment_amount_month
        )
        overall_outstanding_repayment_amount = (
            overall_expected_repayment_amount - agent_total_loan_repayment_amount
        )

        agent_repayment_vs_expected_percentage_month = get_percentages(
            agent_total_loan_repayment_amount_month, loans_due_amount
        )
        overall_agent_repayment_vs_expected_percentage = get_percentages(
            agent_total_loan_repayment_amount, overall_expected_repayment_amount
        )

        # Savings
        agent_ajo_savings_table_qs = ajo_savings_table_qs.filter(user=officer)
        agent_total_savings_amount = list(
            agent_savings_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_active_savings_amount = list(
            agent_ajo_savings_table_qs.filter(is_active=True)
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]
        agent_total_savings_count = list(
            agent_savings_qs.aggregate(Count("amount")).values()
        )[0]
        agent_total_savings_amount_month = list(
            agent_month_savings_qs.aggregate(Sum("amount")).values()
        )[0]
        agent_total_savings_count_month = list(
            agent_month_savings_qs.aggregate(Count("amount")).values()
        )[0]

        agent_total_savings_amount = (
            agent_total_savings_amount if agent_total_savings_amount else 0.00
        )
        agent_total_savings_count = (
            agent_total_savings_count if agent_total_savings_count else 0
        )
        agent_total_savings_amount_month = (
            agent_total_savings_amount_month
            if agent_total_savings_amount_month
            else 0.00
        )
        agent_total_savings_count_month = (
            agent_total_savings_count_month if agent_total_savings_count_month else 0
        )

        # Earnings
        # agent_total_earning = int((agent_total_loan_disbursed_amount if agent_total_loan_disbursed_amount else 0.00) * 0.0425)
        agent_earning_month = 0.00

        # Extra data from Vico
        transactions_count = list(
            ajo_savings_transactions_qs.filter(user=officer, transaction_type="CREDIT")
            .aggregate(Count("amount"))
            .values()
        )[0]

        login_counts = AgencyBankingClass.get_agency_user_details(
            user_id=officer.customer_user_id, start_date=month_start
        )

        if "success" in login_counts and login_counts.get("success") == True:
            login_count = login_counts.get("login_count")
            supervisor = login_counts.get("supervisor")
            branch = login_counts.get("branch")
        else:
            login_count = 30
            supervisor = ""
            branch = ""

        avg_monthly_savings = 187500
        avg_monthly_trnx_count = 38
        avg_monthly_loan = 750000

        agent_age_days = (datetime.now().date() - officer.created_at.date()).days
        savings_performance = round(
            (agent_total_savings_amount_month / avg_monthly_savings) * 100
        )
        loans_performance = round(
            (agent_total_loan_disbursed_amount_month / avg_monthly_loan) * 100
        )
        overall_performance = round((savings_performance + loans_performance) / 2)

        # Set Activity
        if agent_age_days > 30 and login_count > 160:
            activity = "Active Old"
        elif agent_age_days <= 30 and login_count > 80:
            activity = "Active New"
        elif agent_age_days <= 30 and login_count < 80:
            activity = "Inactive New"
        else:
            activity = "Inactive Old"

        # Set Churn
        if (
            activity == "Active Old"
            and agent_total_savings_count_month < 4
            and login_count < 40
        ):
            churn = "Churned"
        else:
            churn = "Not Churned"

        data = {
            "Email": officer.get_username(),
            "Branch": branch,
            "Supervisor": supervisor,
            "Total Disbursed Amount": agent_total_loan_disbursed_amount,
            "Total Disbursed Count": agent_total_loan_disbursed_count,
            "Total Disbursed Amount Month": agent_total_loan_disbursed_amount_month,
            "Total Disbursed Count Month": agent_total_loan_disbursed_count_month,
            "Overall Expected Repayment Amount": overall_expected_repayment_amount,
            "Total Repayment Amount": agent_total_loan_repayment_amount,
            "Total Repayment Count": agent_total_loan_repayment_count,
            "Total Repayment Amount Month": agent_total_loan_repayment_amount_month,
            "Total Repayment Count Month": agent_total_loan_repayment_count_month,
            "Due Repayment Amount Month": loans_due_amount,
            "Loan Balance Amount": overall_outstanding_repayment_amount,
            "Repayment vs Expected Percentage Month": f"{agent_repayment_vs_expected_percentage_month}%",
            "Overall Repayment vs Expected Percentage": f"{overall_agent_repayment_vs_expected_percentage}%",
            "Total Savings Amount": agent_total_savings_amount,
            "Total Savings Count": agent_total_savings_count,
            "Total Active Savings Amount": agent_total_active_savings_amount,
            "Total Savings Amount Month": agent_total_savings_amount_month,
            "Total Savings Count Month": agent_total_savings_count_month,
            # Vico extra
            "Transaction Count": transactions_count if transactions_count else 0,
            "Savings Performance": f"{savings_performance}%",
            "Loans Performance": f"{loans_performance}%",
            "Login Count": login_count,
            "Churned": churn,
            "Activity": activity,
            "Overall Performance": f"{overall_performance}%",
        }
        loan_officers_report_list.append(data)

    df = pd.DataFrame().from_dict(loan_officers_report_list)
    file_path = os.path.join(settings.BASE_DIR, "media/loan_officers")

    try:
        os.mkdir(file_path)
    except:
        pass

    excel_report = df.to_excel(
        f"{file_path}/loan_officers_monthly_performance_{datetime.now().date()}.xlsx",
        index=False,
    )

    with open(
        f"{file_path}/loan_officers_monthly_performance_{datetime.now().date()}.xlsx",
        "rb",
    ) as read_file:
        excel_file = read_file.read()

    for email in admin_email_list:
        send_email = send_ajo_loan_officers_daily_report_email(
            message="This is the Loan officers performance report for the month",
            file=excel_file,
            file_name=f"Loan Officers Monthly Report_{datetime.now().date()}.xlsx",
            email_subject="Loan Officers Monthly Performance Report",
            email=email,
            name="Team",
            date=datetime.now(),
        )

    return "DONE!!!"


@shared_task
def create_eligibility_for_eligible_save_per_agent():
    # call get eligible savers for staff agent
    # eligibility_created = LoanEligibility.get_eligible_savers_for_staff_agent_v1()
    # for eligibility in LoanEligibility.objects.filter(saving__withdrawn=False):
    for eligibility in LoanEligibility.objects.all():
        eligibility.save()

    # eligibility_created = LoanEligibility.get_eligible_savers_for_staff_agent_v2()

    # PROSPER LOANS
    all_not_staff_agent_savings = LoanEligibility.get_prosper_agent_active_savings()
    get_distinct_agents = all_not_staff_agent_savings.values("user").distinct()
    all_prosper_users = User.objects.filter(
        id__in=[distinct_agent["user"] for distinct_agent in get_distinct_agents]
    )

    for agent in all_prosper_users:
        prosper_eligibility = (
            LoanEligibility.check_create_eligible_users_for_ajo_user_propser_loan(
                agent=agent
            )
        )
    return "Done"


@shared_task
def create_update_staff_agent_loan_eligibility_5x_rule():
    """create or update eligibility instance for
    for ajouser tied to an agent with an active savings"""
    eligibility_result = LoanEligibility.get_staff_agent_loan_eligibility_5x_rule()
    return eligibility_result


@shared_task
def create_bnpl_eligibility(savings_id=None):
    if savings_id is not None:
        elig = LoanEligibility.bnpl_eligibility(savings_id=savings_id)
    else:
        elig = LoanEligibility.bnpl_eligibility()
    return elig


@shared_task
def release_escrow_balance(loan_id):
    from lite.utils import retrieve_wallet

    with django_transaction.atomic():
        try:
            loan = AjoLoan.objects.get(id=loan_id)
        except Exception as err:
            return str(err)

        ajo_user = loan.borrower

        # Fetch User's seeds account
        if ajo_user.lite_user:
            wallet, borrower_wema_account = retrieve_wallet(ajo_user)
        else:
            return "borrower not a lite user."

        # call the selector
        ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
        ajo_user_escrow_wallet = ajo_user_selector.get_any_ajo_user_wallet(
            wallet_type=WalletTypes.AJO_LOAN_ESCROW
        )

        amount = ajo_user_escrow_wallet.available_balance
        user = loan.agent
        wallet_type = ajo_user_escrow_wallet.wallet_type
        transaction_form_type = TransactionFormType.AJO_LOAN_ESCROW_HOLDING_REFUND
        savings = loan.eligibility.saving
        debit_description = f"{amount} has been debit from escrow wallet of your saver: {ajo_user.phone_number}."

        if amount > 0:

            fully_repaid = loan.total_paid_amount >= loan.repayment_amount
            ajo_user_repayment_records_sum = (
                AjoLoanRepayment.objects.filter(ajo_loan=loan).aggregate(
                    total=Sum("repayment_amount")
                )["total"]
                or 0
            )

            if fully_repaid and ajo_user_repayment_records_sum >= loan.repayment_amount:
                # loan_available_balance = check_balances(login_purpose="LOAN")
                loan_available_balance = AjoLoan.check_balance()
                if loan_available_balance >= amount:
                    loan_access_token = loan_agent_login().get("access")

                    # Get Escrow User
                    escrow_user_email = settings.ESCROW_USER_EMAIL
                    escrow_user = User.objects.filter(email=escrow_user_email).last()
                    escrow_phone_number = escrow_user.user_phone

                    escrow_access_token = escrow_agent_login(token_not_valid=True).get(
                        "access"
                    )
                    transaction_pin = settings.ESCROW_TRANSACTION_PIN

                    # send buddy from loans to escrow
                    send_loan_buddy_to_escrow = AgencyBankingClass.send_money_from_loans_to_savings_through_pay_buddy(
                        phone_number=escrow_phone_number,
                        amount=amount,
                        transaction_reference=loan.loan_ref,
                        access_token=loan_access_token,
                    )

                    if (
                        send_loan_buddy_to_escrow.get("data").get("message")
                        == "success"
                    ):
                        # Deduct from escrow wallet
                        trans_obj = TransactionService.dynamic_deduction_from_wallet_transaction(
                            user=user,
                            amount=amount,
                            wallet_type=wallet_type,
                            transaction_form_type=transaction_form_type,
                            ajo_user=ajo_user,
                            description=debit_description,
                        )

                        deduct_ajo_user_escrow_wallet = WalletSystem().deduct_balance(
                            wallet=ajo_user_escrow_wallet,
                            amount=amount,
                            transaction_instance=trans_obj,
                            onboarded_user=ajo_user,
                        )

                        trans_obj.status = Status.SUCCESS
                        trans_obj.transaction_date_completed = timezone.now()
                        trans_obj.wallet_balance_before = deduct_ajo_user_escrow_wallet[
                            "balance_before"
                        ]
                        trans_obj.wallet_balance_after = deduct_ajo_user_escrow_wallet[
                            "balance_after"
                        ]
                        trans_obj.save()

                        # Transfer from escrow to borrower's wema account
                        transfer_transaction: "Transaction" = (
                            TransactionService.create_transfer_to_external_account_transaction(
                                user=escrow_user,
                                amount=amount,
                                description=f"Escrow Transfer to borrower's wema account for: {ajo_user.phone_number}",
                                wallet_type=WalletTypes.AJO_LOAN_ESCROW,
                                # unique_reference=str | None = None,
                                ajo_user=ajo_user,
                                # plan_type=PlanType=PlanType.AJO,
                                transaction_form_type=TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT,
                                quotation_id=loan.loan_ref,
                                transfer_provider=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
                            )
                        )
                        reference = str(transfer_transaction.transaction_id)

                        request_data = {
                            "from_wallet_type": "COLLECTION",
                            "data": [
                                {
                                    "account_number": borrower_wema_account.account_number,
                                    "account_name": borrower_wema_account.account_name,
                                    "bank_code": borrower_wema_account.bank_code,
                                    "bank_name": borrower_wema_account.bank_name,
                                    "amount": amount,
                                    "narration": "Transfer from escrow account to wema",
                                    "is_beneficiary": "False",
                                    "save_beneficiary": "True",
                                    "remove_beneficiary": "False",
                                    "is_recurring": "False",
                                    "ledger_commission": 0.00,
                                    "commission_type": None,
                                }
                            ],
                            "total_amount": 0.00,
                            "total_amount_with_charge": 0.00,
                            "transaction_pin": "",
                            "reference": str(reference),
                            "narration": f"LD-{str(reference)}",
                        }

                    send_money_to_borrower_wema_account = AgencyBankingClass.send_money_to_external_account_through_agency(
                        amount=amount,
                        account_number=borrower_wema_account.account_number,
                        account_name=borrower_wema_account.account_name,
                        bank_code=borrower_wema_account.bank_code,
                        bank_name=borrower_wema_account.bank_name,
                        customer_reference=reference,
                        narration="Transfer from escrow account to wema",
                        access_token=escrow_access_token,
                        transaction_pin=transaction_pin,
                    )

                    response_message = (
                        send_money_to_borrower_wema_account.get("data", {}).get(
                            "message"
                        )
                        == "success"
                    )

                    if response_message == True:
                        transfer_transaction.status = Status.PENDING
                    else:
                        transfer_transaction.status = Status.FAILED

                    transfer_transaction.request_data = request_data
                    transfer_transaction.payload = send_money_to_borrower_wema_account
                    transfer_transaction.unique_reference = reference
                    transfer_transaction.save()

                    AjoLoan.objects.filter(id=loan.id).update(escrow_settled=True)
                    AjoLoan.objects.filter(id=loan.id).update(escrow_liquidated=True)

                    loan.escrow_settled = True
                    loan.escrow_liquidated = True
                    loan.save()
                else:
                    pass

    return "Escrow Refunded"


@shared_task
def create_virtual_repayment_wallet():
    ajo_users = AjoUser.objects.all()
    exceptions = {}
    for ajo_user in ajo_users:
        try:
            if not BankAccountDetails.objects.filter(
                ajo_user=ajo_user, form_type=AccountFormType.LOAN_REPAYMENT
            ).exists():
                if AjoLoan.objects.filter(
                    borrower=ajo_user,
                    status=LoanStatus.OPEN,
                ).exists():
                    create_wallet_acct = CoreBankingManager().create_account(
                        first_name=ajo_user.first_name,
                        last_name=ajo_user.last_name,
                        middle_name=ajo_user.alias,
                        email="",
                        phone=ajo_user.phone_number,
                        bvn=ajo_user.bvn,
                        date_of_birth=str(ajo_user.dob),
                    )
                    status_code = create_wallet_acct.get("status_code")
                    if status_code == 200:
                        data = create_wallet_acct.get("data").get("account_details")
                        account_number = data.get("account_number")
                        acct_name = data.get("first_name") + " " + data.get("last_name")
                        bank_code = data.get("bank_code")
                        bank_name = data.get("bank_name")
                        if BankAccountSelector.check_for_account_number(
                            account_number=account_number
                        ):
                            raise ValueError(
                                f"this account number, {account_number}, is already assigned to a form type on our system"
                            )

                        BankAccountDetails.objects.create(
                            user=ajo_user.user,
                            ajo_user=ajo_user,
                            account_number=account_number,
                            account_name=acct_name,
                            bank_code=bank_code,
                            bank_name=bank_name,
                            account_type="Repayment",
                            consent=True,
                            form_type=AccountFormType.LOAN_REPAYMENT,
                            payload=create_wallet_acct,
                        )
                    else:
                        pass
                else:
                    pass
        except Exception as e:
            exceptions[ajo_user.phone_number] = str(e)
            pass

    return {
        "message": "Repayment account creation process completed",
        "errors": exceptions,
    }


@shared_task
def run_post_loan_to_loan_disk(loan_id, loan_status_id=8, rerun=1, loan_mirror_id=None):
    """
    Posts loan details to the LoanDisk system for a given loan ID.

    Args:
        loan_id (str): The ID of the loan to be posted to the LoanDisk system.

    Returns:
        dict: Response containing details of the loan posting to the LoanDisk system.
              If successful, includes the LoanDisk loan ID.
              If unsuccessful, returns an empty dictionary.
    """

    try:
        # Retrieve the AjoLoan instance corresponding to the provided loan ID
        instance = AjoLoan.objects.get(id=loan_id)
    except AjoLoan.DoesNotExist:
        LoanDiskMetaData.objects.create(
            main_branch_response=f"Invalid Ajo Loan ID Passed. Ajo Loan ID: {loan_id}",
        )
        return f"Invalid Ajo Loan ID Passed. Ajo Loan ID: {loan_id}"

    borrower = instance.borrower
    loan_disk_borrower_id = borrower.loandisk_borrower_id
    if loan_disk_borrower_id == None:
        if rerun == 0:
            LoanDiskMetaData.objects.create(
                main_branch_response=f"Tried to create a borrow for a loan on loan disk but borrower does not still have a loan disk ID. LoanDB ID: {instance.id} AjoUser ID: {instance.borrower.id}",
            )
            return f"Tried to create a borrow for a loan on loan disk but borrower does not still have a loan disk ID. LoanDB ID: {instance.id} AjoUser ID: {instance.borrower.id}"
        else:
            create_borrower_on_loan_disk_branch(ajo_user_id=instance.borrower.id)
            return run_post_loan_to_loan_disk(
                loan_id=loan_id, loan_status_id=loan_status_id, rerun=0
            )

    bank_name = ""
    account_number = ""

    try:
        borrower_bank_details: AjoUserWithdrawalAccount = borrower.withdrawal_account

        bank_name = borrower_bank_details.bank_name
        account_number = borrower_bank_details.account_number
    except ObjectDoesNotExist:
        # return "this borrower has not set an external account"
        pass

    loan_date = loan_disk_date_format(instance.created_at)

    try:
        loan_tenure = Tenure.match_value_get_days(instance.tenor)
    except Exception:
        loan_tenure = 30

    interest_rate = round((instance.interest_rate / loan_tenure), 2)
    loan_created_date = instance.created_at.date() + timedelta(days=4)
    # loan_first_repayment_date = loan_repayment_start_date(instance.created_at, 4)
    first_repayment_date = loan_disk_date_format(loan_created_date)
    # Prepare loan details for posting to LoanDisk
    ajo_loan_details = {
        "loan_id": f"{instance.loan_ref}{instance.id}",
        "amount": instance.amount,
        "interest_rate": interest_rate,
        "loandisk_tenor_days": loan_tenure,
        "loan_date": loan_date,
        "bank_name": bank_name,
        "account_number": account_number,
        "loan_first_repayment_date": first_repayment_date,
    }

    if instance.loandisk_loan_id == None:
        # Post loan details to LoanDisk for the borrower
        post_borrower_loan_disk_response = LoandiskManager().create_loan(
            loan_disk_borrower_id=loan_disk_borrower_id,
            ajo_loan_details=ajo_loan_details,
            loan_status_id=loan_status_id,
            loan_id=loan_id,
            loan_mirror_id=loan_mirror_id,
        )

        metadata, created = LoanDiskMetaData.objects.get_or_create(loan_id=loan_id)
        metadata.main_branch_response = post_borrower_loan_disk_response
        metadata.save()

        loan_disk_loan_id = post_borrower_loan_disk_response.get("response", {}).get(
            "loan_id", None
        )

        if loan_disk_loan_id is not None:
            instance.loandisk_loan_id = loan_disk_loan_id
            instance.save()

        return post_borrower_loan_disk_response

    return "Done creating loan on loan disk."

    # try:
    #     BRANCH_LOCATION_CODES = (
    #         ConstantTable.get_constant_table_instance().branch_location_codes
    #     )

    #     # Retrieve borrower details
    #     borrower = instance.borrower
    #     loan_disk_borrower_id = borrower.loandisk_borrower_id

    #     # check if loandisk borrower ID is available
    #     # if no, create borrower on loandisk

    #     bank_name = ""
    #     account_number = ""

    #     if loan_disk_borrower_id == None:
    #         last_eligibility_instance = LoanEligibility.objects.filter(
    #             ajo_user=borrower
    #         ).last()
    #         if last_eligibility_instance == None:
    #             return "No eligibility instance found when trying to create borrower on LoanDisk"

    #         create_borrower_on_loandisk_response = create_borrower_on_loan_disk_branch(
    #             last_eligibility_instance.id
    #         )

    #         # query ajo load again to make sure the borrower has been created on loandisk
    #         instance = AjoLoan.objects.get(id=loan_id)
    #         borrower = instance.borrower
    #         loan_disk_borrower_id = borrower.loandisk_borrower_id

    #         if loan_disk_borrower_id == None:
    #             return f"""
    #             Unable to create borrower on LoanDisk
    #             \n\n
    #             {create_borrower_on_loandisk_response}
    #             """
    #     try:
    #         borrower_bank_details: AjoUserWithdrawalAccount = (
    #             borrower.withdrawal_account
    #         )

    #         bank_name = borrower_bank_details.bank_name
    #         account_number = borrower_bank_details.account_number
    #     except ObjectDoesNotExist:
    #         # return "this borrower has not set an external account"
    #         pass

    #     # Format loan date for LoanDisk
    #     loan_date = loan_disk_date_format(instance.created_at)

    #     try:
    #         loan_tenure = Tenure.match_value_get_days(instance.tenor)
    #     except Exception:
    #         loan_tenure = 30

    #     interest_rate = round((instance.interest_rate / loan_tenure), 2)
    #     loan_created_date = instance.created_at.date() + timedelta(days=4)
    #     # loan_first_repayment_date = loan_repayment_start_date(instance.created_at, 4)
    #     first_repayment_date = loan_disk_date_format(loan_created_date)
    #     # Prepare loan details for posting to LoanDisk
    #     ajo_loan_details = {
    #         "loan_id": f"{instance.loan_ref}{instance.id}",
    #         "amount": instance.amount,
    #         "interest_rate": interest_rate,
    #         "loandisk_tenor_days": loan_tenure,
    #         "loan_date": loan_date,
    #         "bank_name": bank_name,
    #         "account_number": account_number,
    #         "loan_first_repayment_date": first_repayment_date,
    #     }

    #     post_borrower_loan_disk_response = None

    #     if instance.loandisk_loan_id == None:
    #         # Post loan details to LoanDisk for the borrower
    #         post_borrower_loan_disk_response = LoandiskManager().create_loan(
    #             loan_disk_borrower_id=loan_disk_borrower_id,
    #             ajo_loan_details=ajo_loan_details,
    #             loan_status_id=loan_status_id,
    #         )

    #         print(
    #             "post_borrower_loan_disk_response",
    #             post_borrower_loan_disk_response,
    #             "\n\n\n",
    #         )

    #         metadata, created = LoanDiskMetaData.objects.get_or_create(loan_id=loan_id)
    #         metadata.main_branch_response = post_borrower_loan_disk_response
    #         metadata.save()

    #         loan_disk_loan_id = post_borrower_loan_disk_response.get(
    #             "response", {}
    #         ).get("loan_id", None)

    #         instance.loandisk_loan_id = loan_disk_loan_id
    #         instance.save()

    #     # check if borrower has branch
    #     # if not, get the borrowers branch on agency banking and save it

    #     if instance.borrower.branch_name == None or instance.borrower.branch_name == "":
    #         agency_banking_cls_intance = (
    #             AgencyBankingClass.get_ajo_user_loan_branch_team(
    #                 user_id=instance.borrower.user.customer_user_id
    #             )
    #         )

    #         if isinstance(agency_banking_cls_intance, dict):
    #             branch_name = agency_banking_cls_intance.get("data", {}).get(
    #                 "branch", None
    #             )
    #             if branch_name == None or branch_name == "":
    #                 return {
    #                     "message": "unable to get borrowers branch details on agency banking",
    #                     "err": agency_banking_cls_intance,
    #                 }

    #         else:
    #             return {
    #                 "message": "unable to get borrowers branch details on agency banking",
    #                 "err": agency_banking_cls_intance,
    #             }

    #     branch_loandisk_borrower_id = instance.borrower.branch_loandisk_borrower_id
    #     # Now that we've confirmed that this borrower has a branch, let's confirm if this borrower exist in the loandisk branch dedicated for the branch
    #     if branch_loandisk_borrower_id == None:
    #         last_eligibility_instance = LoanEligibility.objects.filter(
    #             ajo_user=instance.borrower
    #         ).last()
    #         if last_eligibility_instance == None:
    #             return "No eligibility instance found when trying to create borrower on branch LoanDisk"

    #         create_borrower_on_loandisk_response = create_borrower_on_loan_disk_branch(
    #             last_eligibility_instance.id
    #         )
    #         instance.refresh_from_db()
    #         borrower = instance.borrower

    #         branch_loandisk_borrower_id = instance.borrower.branch_loandisk_borrower_id
    #         if branch_loandisk_borrower_id == None:
    #             return f"""
    #             Unable to create borrower on branch LoanDisk
    #             \n\n
    #             {create_borrower_on_loandisk_response}
    #             """

    #     branch_id = BRANCH_LOCATION_CODES.get(
    #         str(instance.borrower.branch_name).upper(), None
    #     )
    #     ajo_loan_details["branch_id"] = branch_id
    #     ajo_loan_details["loan_ref_2"] = f"id_{instance.id}{instance.loan_ref}"

    #     branch_response = BranchLoanDiskManager().create_loan_on_branch(
    #         loan_disk_borrower_id=instance.borrower.branch_loandisk_borrower_id,
    #         ajo_loan_details=ajo_loan_details,
    #         loan_status_id=loan_status_id,
    #     )
    #     metadata = LoanDiskMetaData.objects.filter(loan_id=loan_id).last()
    #     if metadata:
    #         metadata.branch_response = post_borrower_loan_disk_response
    #         metadata.save()

    #     ajo_loandisk_id = branch_response.get("response", {}).get("loan_id")
    #     instance.branch_loandisk_loan_id = ajo_loandisk_id
    #     instance.save()

    #     return {
    #         "ajo_loan_details": ajo_loan_details,
    #         "main_branch_loan_posting": post_borrower_loan_disk_response,
    #         "branch_loan_posting": branch_response,
    #     }

    # except Exception as err:
    #     return f"{err}"


@shared_task
def update_loan_status_on_loan_disk(
    loan_id, loan_status="open", from_signal=False, loan_mirror_id=None
):

    try:
        instance = AjoLoan.objects.get(id=loan_id)
    except AjoLoan.DoesNotExist:
        LoanDiskMetaData.objects.create(
            main_branch_response=f"Unable Update Loan on Loan Disk. Because an invalid LoanID was passed. LoanID: {loan_id}",
        )
        return f"Unable Update Loan on Loan Disk. Because an invalid LoanID was passed. LoanID: {loan_id}"

    if from_signal == True:
        if instance.status == LoanStatus.AWAITING_CRC:
            loandisk_loan_status = 366032
        elif instance.status in [LoanStatus.OPEN, LoanStatus.APPROVED]:
            loandisk_loan_status = 1
        elif instance.status in [LoanStatus.DENIED, LoanStatus.DECLINED_BY_SUPERVISOR]:
            loandisk_loan_status = 17
        else:
            return "Loan status not supported"

    else:
        loandisk_loan_status = 1

        if loan_status == VerificationStage.FACE_MATCH:
            loandisk_loan_status = 366028
        elif loan_status == VerificationStage.CREDIT_BUREAU:
            loandisk_loan_status = 366032
        elif loan_status == VerificationStage.GUARANTOR:
            loandisk_loan_status = 366030
        elif loan_status == VerificationStage.GUARANTOR_CAPTURE:
            loandisk_loan_status = 366031

    try:
        loan_tenure = Tenure.match_value_get_days(instance.tenor)
    except Exception:
        loan_tenure = 30

    loan_created_date = instance.created_at.date() + timedelta(days=4)
    first_repayment_date = loan_disk_date_format(loan_created_date)

    interest_rate = round((instance.interest_rate / loan_tenure), 2)

    borrower = instance.borrower
    loan_disk_borrower_id = borrower.loandisk_borrower_id
    loan_date = loan_disk_date_format(pass_date=instance.date_disbursed)

    borrower_bank_details = None

    try:
        borrower_bank_details: AjoUserWithdrawalAccount = borrower.withdrawal_account
    except ObjectDoesNotExist:
        # return "this borrower has not set an external account"
        pass

    if borrower_bank_details == None:
        account_number = ""
    else:
        account_number = borrower_bank_details.account_number

    # confirm if loandisk_loan_id is not None
    if instance.loandisk_loan_id == None:
        post_loan_response = run_post_loan_to_loan_disk(
            instance.id,
            loan_status_id=loandisk_loan_status,
            loan_mirror_id=loan_mirror_id,
        )
        # Solution is most likely to add the loandisk mirror arg in this functon call.

        instance.refresh_from_db()

        if instance.loandisk_loan_id == None:
            LoanDiskMetaData.objects.create(
                main_branch_response=f"Unable to create loan on LoanDisk when trying to update loan status on loandiskLOAN ID: {loan_id}",
            )
            return f"Unable to create loan on LoanDisk when trying to update loan status on loandiskLOAN ID: {loan_id}"

    ajo_loan_details = {
        "loan_id": instance.loan_ref,
        "loan_principal_amount": instance.amount,
        "loan_date": loan_date,
        "interest_amount": instance.interest_rate,
        "loandisk_loan_status": loandisk_loan_status,
        "loandisk_loan_id": instance.loandisk_loan_id,
        "loan_first_repayment_date": first_repayment_date,
        "loan_status_id": loandisk_loan_status,
        "loandisk_tenor_days": loan_tenure,
        "branch_loandisk_loan_id": instance.branch_loandisk_loan_id,
        "interest_rate": interest_rate,
        "ministry": instance.borrower.trade,
        "borrower_bank_details": account_number,
    }

    post_borrower_loan_disk_response = LoandiskManager().update_loan(
        loan_disk_borrower_id=loan_disk_borrower_id,
        ajo_loan_details=ajo_loan_details,
        loan_mirror_id=loan_mirror_id,
    )

    LoanDiskMetaData.objects.create(
        main_branch_response={
            "LOAN ID": loan_id,
            "loandisk update response": post_borrower_loan_disk_response,
        }
    )

    return "Done Updating Loan on LoanDisk"

    # if instance.branch_loandisk_loan_id == None:
    #     post_loan_response = run_post_loan_to_loan_disk(instance.id)

    #     instance.refresh_from_db()

    #     if instance.branch_loandisk_loan_id == None:
    #         return f"""
    #             Unable to create loan on branch LoanDisk
    #             \n\n
    #             {post_loan_response}
    #         """

    # ajo_loan_details = {
    #     "loan_id": instance.loan_ref,
    #     "loan_principal_amount": instance.amount,
    #     "loan_date": loan_date,
    #     "interest_amount": instance.interest_rate,
    #     "loandisk_loan_status": loandisk_loan_status,
    #     "loandisk_loan_id": instance.loandisk_loan_id,
    #     "loan_first_repayment_date": first_repayment_date,
    #     "loan_status_id": loandisk_loan_status,
    #     "loandisk_tenor_days": loan_tenure,
    #     "branch_loandisk_loan_id": instance.branch_loandisk_loan_id,
    #     "interest_rate": interest_rate,
    #     "ministry": instance.borrower.trade,
    #     "borrower_bank_details": account_number,
    # }

    # post_borrower_loan_disk_response = LoandiskManager().update_loan(
    #     loan_disk_borrower_id=loan_disk_borrower_id, ajo_loan_details=ajo_loan_details
    # )

    # metadata, created = LoanDiskMetaData.objects.get_or_create(loan_id=loan_id)
    # metadata.main_branch_response = post_borrower_loan_disk_response
    # metadata.save()

    # BRANCH_LOCATION_CODES = (
    #     ConstantTable.get_constant_table_instance().branch_location_codes
    # )

    # try:
    #     instance = AjoLoan.objects.get(id=loan_id)
    # except AjoLoan.DoesNotExist:
    #     return {}

    # if from_signal == True:
    #     if instance.status == LoanStatus.AWAITING_CRC:
    #         loandisk_loan_status = 366032
    #     elif instance.status in [LoanStatus.OPEN, LoanStatus.APPROVED]:
    #         loandisk_loan_status = 1
    #     elif instance.status in [LoanStatus.DENIED, LoanStatus.DECLINED_BY_SUPERVISOR]:
    #         loandisk_loan_status = 17
    #     else:
    #         return "Loan status not supported"

    # else:
    #     loandisk_loan_status = 1

    #     if loan_status == VerificationStage.FACE_MATCH:
    #         loandisk_loan_status = 366028
    #     elif loan_status == VerificationStage.CREDIT_BUREAU:
    #         loandisk_loan_status = 366032
    #     elif loan_status == VerificationStage.GUARANTOR:
    #         loandisk_loan_status = 366030
    #     elif loan_status == VerificationStage.GUARANTOR_CAPTURE:
    #         loandisk_loan_status = 366031

    # try:
    #     loan_tenure = Tenure.match_value_get_days(instance.tenor)
    # except Exception:
    #     loan_tenure = 30

    # loan_created_date = instance.created_at.date() + timedelta(days=4)
    # first_repayment_date = loan_disk_date_format(loan_created_date)

    # interest_rate = round((instance.interest_rate / loan_tenure), 2)

    # borrower = instance.borrower
    # loan_disk_borrower_id = borrower.loandisk_borrower_id
    # loan_date = loan_disk_date_format(pass_date=instance.date_disbursed)

    # borrower_bank_details = None

    # try:
    #     borrower_bank_details: AjoUserWithdrawalAccount = borrower.withdrawal_account
    # except ObjectDoesNotExist:
    #     # return "this borrower has not set an external account"
    #     pass

    # if borrower_bank_details == None:
    #     account_number = ""
    # else:
    #     account_number = borrower_bank_details.account_number

    # # confirm if loandisk_loan_id is not None
    # if instance.loandisk_loan_id == None:
    #     post_loan_response = run_post_loan_to_loan_disk(
    #         instance.id, loan_status_id=loandisk_loan_status
    #     )

    #     instance.refresh_from_db()

    #     if instance.loandisk_loan_id == None:
    #         return f"""
    #             Unable to create loan on LoanDisk
    #             \n\n
    #             {post_loan_response}
    #         """

    # if instance.branch_loandisk_loan_id == None:
    #     post_loan_response = run_post_loan_to_loan_disk(instance.id)

    #     instance.refresh_from_db()

    #     if instance.branch_loandisk_loan_id == None:
    #         return f"""
    #             Unable to create loan on branch LoanDisk
    #             \n\n
    #             {post_loan_response}
    #         """

    # ajo_loan_details = {
    #     "loan_id": instance.loan_ref,
    #     "loan_principal_amount": instance.amount,
    #     "loan_date": loan_date,
    #     "interest_amount": instance.interest_rate,
    #     "loandisk_loan_status": loandisk_loan_status,
    #     "loandisk_loan_id": instance.loandisk_loan_id,
    #     "loan_first_repayment_date": first_repayment_date,
    #     "loan_status_id": loandisk_loan_status,
    #     "loandisk_tenor_days": loan_tenure,
    #     "branch_loandisk_loan_id": instance.branch_loandisk_loan_id,
    #     "interest_rate": interest_rate,
    #     "ministry": instance.borrower.trade,
    #     "borrower_bank_details": account_number,
    # }

    # post_borrower_loan_disk_response = LoandiskManager().update_loan(
    #     loan_disk_borrower_id=loan_disk_borrower_id, ajo_loan_details=ajo_loan_details
    # )

    # print(
    #     "update loan post_borrower_loan_disk_response",
    #     post_borrower_loan_disk_response,
    #     "\n\n\n",
    # )

    # metadata, created = LoanDiskMetaData.objects.get_or_create(loan_id=loan_id)
    # metadata.main_branch_response = post_borrower_loan_disk_response
    # metadata.save()

    # # print(post_borrower_loan_disk_response)

    # # post to agent branch
    # # loan_disk_id_query = AjoLoan.get_supervisor_agent_branch_loan_disk_id(loan_instance=instance)

    # branch_id = BRANCH_LOCATION_CODES.get(
    #     str(instance.borrower.branch_name).upper(), None
    # )

    # ajo_loan_details["branch_id"] = branch_id
    # ajo_loan_details["loan_ref_2"] = f"id_{instance.id}{instance.loan_ref}"
    # ajo_loan_details["branch_loandisk_loan_id"] = instance.branch_loandisk_loan_id

    # branch_response = BranchLoanDiskManager().update_loan_on_branch(
    #     loan_disk_borrower_id=loan_disk_borrower_id, ajo_loan_details=ajo_loan_details
    # )

    # print("update loan branch_response", branch_response, "\n\n\n")

    # metadata.branch_response = branch_response
    # metadata.save()

    # metadata, created = LoanDiskMetaData.objects.get_or_create(loan_id=loan_id)
    # metadata.main_branch_response = branch_response
    # metadata.save()

    # return {
    #     "payload": ajo_loan_details,
    #     "main_branch_loan_update_response": post_borrower_loan_disk_response,
    #     "branch_response": branch_response,
    # }


@shared_task
def create_borrower_on_loan_disk_branch(ajo_user_id):
    try:
        ajo_user_instance = AjoUser.objects.get(id=ajo_user_id)
    except AjoUser.DoesNotExist:
        LoanDiskMetaData.objects.create(
            main_branch_response=f"Invalid Ajo User ID Passed. AjoUser ID: {ajo_user_id}",
        )
        return f"Invalid Ajo User ID Passed. AjoUser ID: {ajo_user_id}"

    if not ajo_user_instance.loandisk_borrower_id:
        post_borrower_reponse = LoandiskManager().get_or_create_borrower(
            phone_number=ajo_user_instance.phone_number,
        )
        LoanDiskMetaData.objects.create(
            eligibility_id=ajo_user_instance.phone_number,
            main_branch_response=post_borrower_reponse,
        )
        borrower_id = post_borrower_reponse.get("response", {}).get("borrower_id", None)
        ajo_user_instance.loandisk_borrower_id = borrower_id
        ajo_user_instance.save()

        return post_borrower_reponse

    return "Done Creating borrower."

    # try:
    #     BRANCH_LOCATION_CODES = (
    #         ConstantTable.get_constant_table_instance().branch_location_codes
    #     )

    #     try:
    #         eligibility_instance = LoanEligibility.objects.get(id=eligibility_id)
    #     except LoanEligibility.DoesNotExist:
    #         return {}

    #     # agent = eligibility_instance.agent
    #     ajo_user = eligibility_instance.ajo_user

    #     if not ajo_user.loandisk_borrower_id:
    #         # print("not branch")
    #         post_borrower_reponse = LoandiskManager().get_or_create_borrower(
    #             phone_number=ajo_user.phone_number
    #         )

    #         print(
    #             f"""
    #         post_borrower_reponse: {post_borrower_reponse}
    #         \n\n\n
    #         """
    #         )
    #         LoanDiskMetaData.objects.create(
    #             eligibility_id=eligibility_id,
    #             main_branch_response=post_borrower_reponse,
    #         )
    #         borrower_id = post_borrower_reponse.get("response", {}).get(
    #             "borrower_id", None
    #         )
    #         ajo_user.loandisk_borrower_id = borrower_id
    #         ajo_user.save()

    #     # get ajo user details on agency banking
    #     branch_name = ajo_user.branch_name

    #     if branch_name == None or branch_name == "":
    #         agency_banking_cls_intance = (
    #             AgencyBankingClass.get_ajo_user_loan_branch_team(
    #                 user_id=ajo_user.user.customer_user_id
    #             )
    #         )

    #         if isinstance(agency_banking_cls_intance, dict):
    #             branch_name = agency_banking_cls_intance.get("data", {}).get(
    #                 "branch", None
    #             )
    #             if branch_name:
    #                 ajo_user.branch_name = branch_name
    #                 ajo_user.save()
    #         else:
    #             return {
    #                 "message": "on creating borrower on loandisk, this borrower does not have branch attached to their profile",
    #                 "phone": ajo_user.phone_number,
    #                 "agency_banking_response": agency_banking_cls_intance,
    #             }

    #     if (
    #         ajo_user.branch_loandisk_borrower_id == None
    #         or ajo_user.branch_loandisk_borrower_id == ""
    #     ):

    #         # get borrower branch code
    #         branch_id = BRANCH_LOCATION_CODES.get(str(branch_name).upper(), None)

    #         borrower_details = LoanEligibility.borrower_details(ajo_user=ajo_user)

    #         borrower_details["date_of_birth"] = str(loan_disk_date_format(ajo_user.dob))
    #         borrower_details["address"] = ajo_user.address
    #         borrower_details["age"] = ajo_user.age
    #         borrower_details["alias"] = ajo_user.alias
    #         borrower_details["bvn"] = ajo_user.bvn

    #         response = BranchLoanDiskManager().create_borrower_on_branch(
    #             borrower_details=borrower_details, branch_id=branch_id
    #         )

    #         metadata, created = LoanDiskMetaData.objects.get_or_create(
    #             eligibility_id=eligibility_id
    #         )
    #         metadata.branch_response = response
    #         metadata.save()

    #         borrower_id = response.get("response", {}).get("borrower_id", None)
    #         ajo_user.branch_loandisk_borrower_id = borrower_id
    #         ajo_user.save()

    #         return success_celery_response(
    #             message="ajo user sent to loan disk",
    #             data={"phone_number": ajo_user.phone_number},
    #         )

    # except Exception as err:
    #     return f"{err}"


@shared_task
def create_loan_ids_branches_for_all_eligibility():
    eligibilities_without_loan_disk = LoanEligibility.objects.filter(
        Q(ajo_user__loandisk_borrower_id__isnull=True)
        | Q(ajo_user__branch_name__isnull=True)
        | Q(ajo_user__branch_loandisk_borrower_id__isnull=True)
    )

    for eligibility in eligibilities_without_loan_disk:
        create_borrower_on_loan_disk_branch.delay(ajo_user_id=eligibility.ajo_user.id)

    eligibility_ids = list(eligibilities_without_loan_disk.values_list("id", flat=True))

    return {
        "status": "success",
        "data": f"created loan IDs, branches for users in this eligibility list: {eligibility_ids}",
    }


@shared_task
def update_existing_savers_eligibility():
    start_date = datetime(2024, 2, 15)
    end_date = datetime(2024, 2, 29)
    old_savings = AjoSaving.objects.filter(
        created_at__date__range=[start_date, end_date], user__user_type="STAFF_AGENT"
    )
    old_savers_list = list(old_savings.values_list("ajo_user__id", flat=True))
    get_staff_eligibilities = LoanEligibility.objects.filter(
        ajo_user__in=old_savers_list
    ).filter(verified_saver=False)
    all_updatable_ajo_users = get_staff_eligibilities.values_list("ajo_user", flat=True)
    # # all_updatable_ajo_users = AjoUser.objects.filter(id__in=).values_list("ajo_user", flat=True)

    new_eligibilities = LoanEligibility.objects.filter(
        ajo_user__id__in=all_updatable_ajo_users,
        saving__created_at__date__month=datetime(2024, 3, 1).month,
    )
    print(new_eligibilities)

    for eligibility in new_eligibilities:
        get_old_savings = old_savings.filter(ajo_user=eligibility.ajo_user).first()
        print(get_old_savings)

        commisson_amount = (
            Commission.objects.filter(
                quotation_id=get_old_savings.quotation_id
            ).aggregate(total=Sum("amount"))["total"]
            or 0
        )
        print(commisson_amount)
        new_savings = eligibility.saving
        new_savings.loan_addable_amount = (
            commisson_amount + 100 if commisson_amount > 0 else 0
        )
        new_savings.save()

    return "Updated Successfully"


@shared_task
def one_off_fund_ajo_user_task(debit_txn_id, savings_id):
    transaction_form_type = (
        TransactionFormType.CHARGE_SPEND_ON_CLOSED_SAVINGS_ELIGIBILITY
    )

    print(f"DEBITED TRANSACTION ID ==> {debit_txn_id}")
    print(f"SAVINGS ID ==> {savings_id}")

    try:
        savings = AjoSaving.objects.get(id=savings_id)
        if savings.plan_balance_after <= 0 and savings.is_active is True:
            txn = Transaction.objects.get(
                id=debit_txn_id, transaction_form_type=transaction_form_type
            )
            credit_amount = txn.amount
            if credit_amount > 0:
                fund_ajo_savings_position(
                    user=savings.user,
                    ajo_user=savings.ajo_user,
                    ajo_savings=savings,
                    amount=credit_amount,
                    request_data=None,
                )
                return "Balance updated successfully"
            else:
                return f"Credit amount is {credit_amount}"
        else:
            return "could not update balance"
    except (AjoSaving.DoesNotExist, Transaction.DoesNotExist) as err:
        return str(err)


@shared_task
def repayment_checker():
    statuses = ["DEFAULTED", "OPEN"]
    loan_qs = AjoLoan.objects.filter(status__in=statuses)
    # print(loan_qs.count())
    repayment_update = 0
    for loan_instance in loan_qs:
        ajo_user = loan_instance.borrower
        agent = loan_instance.agent
        repayment_qs = AjoLoanRepayment.objects.filter(ajo_loan=loan_instance)
        total_paid = (
            repayment_qs.aggregate(total_paid=Sum("repayment_amount"))["total_paid"]
            or 0
        )

        expected_repayment = loan_instance.repayment_amount
        out_standing = expected_repayment - total_paid

        ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
        ajo_user_spend_wallet = ajo_user_selector.get_spending_wallet()
        spend_available_balance = ajo_user_spend_wallet.available_balance

        ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
        wallet = ajo_user_selector.get_ajo_user_wallet()
        savings_available_balance = wallet.available_balance

        if spend_available_balance > 0 or savings_available_balance > 0:
            RepaymentChecker.objects.create(
                ajo_loan=loan_instance,
                agent_email=agent.email,
                phone_number=ajo_user.phone_number,
                borrower_full_name=ajo_user.fullname,
                spend_available_balance=spend_available_balance,
                savings_available_balance=savings_available_balance,
                loan_outstanding=out_standing,
                loan_amount_paid=total_paid,
                repayment_count=repayment_qs.count(),
            )
            repayment_update += 1

    return f"Total repayment checker created {repayment_update}"


@shared_task
def notify_admin_borrower_img_mismatch(subject, agent_email, full_name, age):
    emails = [
        "<EMAIL>"
        # "<EMAIL>",
        # # "<EMAIL>",
        # "<EMAIL>",
        # # "<EMAIL>",
        # "<EMAIL>",
        # "<EMAIL>"
    ]

    template_dir = os.path.join("loans", "borrower_mismatch.html")
    for email in emails:
        general_send_email(
            recipient=email,
            subject=subject,
            template_dir=template_dir,
            use_template=True,
            body=None,
            date=str(timezone.now()),
            title=subject,
            agent_email=agent_email,
            full_name=full_name,
            age=age,
        )


@shared_task
def notify_admin_guarantor_img_mismatch(
    subject,
    agent_email,
    full_name,
    age,
    verification_number,
    guarantor_full_name,
    guarantor_age,
):
    emails = [
        # "<EMAIL>"
        # "<EMAIL>",
        # # "<EMAIL>",
        # "<EMAIL>",
        # # "<EMAIL>",
        # "<EMAIL>",
        # "<EMAIL>"
    ]

    template_dir = os.path.join("loans", "guarantor_mismatch.html")
    for email in emails:
        general_send_email(
            recipient=email,
            subject=subject,
            template_dir=template_dir,
            use_template=True,
            body=None,
            date=str(timezone.now()),
            title=subject,
            agent_email=agent_email,
            full_name=full_name,
            age=age,
            guarantor_age=guarantor_age,
            guarantor_full_name=guarantor_full_name,
            verification_num=verification_number,
        )


# @shared_task
# def recheck_unsettled_response(subject, agent_email,
#                                         full_name, age, verification_num,
#                                         guarantor_full_name,
#                                         guarantor_age):
#     emails = [
#         "<EMAIL>"
#         # "<EMAIL>",
#         # # "<EMAIL>",
#         # "<EMAIL>",
#         # # "<EMAIL>",
#         # "<EMAIL>",
#         # "<EMAIL>"
#     ]

#     template_dir = os.path.join("loans", "guarantor_mismatch.html")
#     for email in emails:
#         general_send_email(
#             recipient=email,
#             subject=subject,
#             template_dir=template_dir,
#             use_template=True,
#             body=None,
#             date=str(timezone.now()),
#             title=subject,
#             agent_email=agent_email,
#             full_name=full_name,
#             age=age,
#             guarantor_age=guarantor_age,
#             guarantor_full_name=guarantor_full_name,
#             verification_num=verification_num,
#         )


@shared_task
def run_loan_supervisors_daily_report():
    # Define Dates
    previous_day = DateUtility().previous_day
    today = timezone.now()

    supervisors_list = AgencyBankingClass.get_agency_supervisor_details()

    # Write queries
    loan_officers_qs = User.objects.filter(user_type="STAFF_AGENT")
    ajo_loans_qs = AjoLoan.objects.filter()
    ajo_loan_repayment_qs = AjoLoanRepayment.objects.all()

    ajo_savings_transactions_qs = Transaction.objects.filter(
        plan_type="AJO",
        wallet_type="AJO_USER",
        status="SUCCESS",
        transaction_form_type="WALLET_DEPOSIT",
    )
    ajo_savings_table_qs = AjoSaving.objects.all()

    if "success" in supervisors_list and supervisors_list.get("success") == True:
        teams = supervisors_list.get("supervisors_teams_list")
    else:
        teams = []

    for team in teams:
        supervisor_name = team.get("supervisor")
        supervisor_email = team.get("supervisor_email")
        supervisor_user_id = team.get("supervisor_user_id")
        loan_officers = team.get("users_list")
        branch = team.get("branch")

        loan_officers_report_list = []
        for officer_id in loan_officers:
            officer = loan_officers_qs.filter(customer_user_id=officer_id).last()

            if officer:
                loans_disbursed_by_agent_qs = ajo_loans_qs.filter(
                    agent=officer, status__in=disbursed_loans_status_list
                )
                loans_disbursed_by_agent_yesterday_qs = (
                    loans_disbursed_by_agent_qs.filter(
                        created_at__date=previous_day.date()
                    )
                )
                agent_loans_repayment_qs = ajo_loan_repayment_qs.filter(agent=officer)
                previous_day_agent_repayment_qs = ajo_loan_repayment_qs.filter(
                    agent=officer, paid_date__date=previous_day.date()
                )
                agent_savings_qs = ajo_savings_transactions_qs.filter(user=officer)
                agent_ajo_savings_table_qs = ajo_savings_table_qs.filter(user=officer)
                agent_yesterday_savings_qs = agent_savings_qs.filter(
                    date_created__date=previous_day.date()
                )

                # Loans Disbursed
                agent_total_loan_disbursed_amount = list(
                    loans_disbursed_by_agent_qs.aggregate(Sum("amount")).values()
                )[0]
                agent_total_loan_disbursed_count = list(
                    loans_disbursed_by_agent_qs.aggregate(Count("amount")).values()
                )[0]
                agent_total_loan_disbursed_amount_yesterday = list(
                    loans_disbursed_by_agent_yesterday_qs.aggregate(
                        Sum("amount")
                    ).values()
                )[0]
                agent_total_loan_disbursed_count_yesterday = list(
                    loans_disbursed_by_agent_yesterday_qs.aggregate(
                        Count("amount")
                    ).values()
                )[0]

                agent_total_loan_disbursed_amount = (
                    agent_total_loan_disbursed_amount
                    if agent_total_loan_disbursed_amount
                    else 0.00
                )
                agent_total_loan_disbursed_count = (
                    agent_total_loan_disbursed_count
                    if agent_total_loan_disbursed_count
                    else 0
                )
                agent_total_loan_disbursed_amount_yesterday = (
                    agent_total_loan_disbursed_amount_yesterday
                    if agent_total_loan_disbursed_amount_yesterday
                    else 0.00
                )
                agent_total_loan_disbursed_count_yesterday = (
                    agent_total_loan_disbursed_count_yesterday
                    if agent_total_loan_disbursed_count_yesterday
                    else 0
                )

                # Loan Repayments
                agent_total_loan_repayment_amount = list(
                    agent_loans_repayment_qs.aggregate(Sum("repayment_amount")).values()
                )[0]

                agent_total_loan_repayment_count = list(
                    agent_loans_repayment_qs.aggregate(
                        Count("repayment_amount")
                    ).values()
                )[0]

                agent_total_loan_repayment_amount_yesterday = list(
                    previous_day_agent_repayment_qs.aggregate(
                        Sum("repayment_amount")
                    ).values()
                )[0]

                agent_total_loan_repayment_count_yesterday = list(
                    previous_day_agent_repayment_qs.aggregate(
                        Count("repayment_amount")
                    ).values()
                )[0]

                expected_repayment_amount_yesterday = list(
                    loans_disbursed_by_agent_qs.filter(created_at__lt=today)
                    .aggregate(Sum("daily_repayment_amount"))
                    .values()
                )[0]

                # Due Repayment Amount Yesterday
                due_loans_qs = ajo_loans_qs.filter(
                    agent=officer, start_date__lte=timezone.now().date()
                )

                loans_due_amount_yesterday = 0
                for loan in due_loans_qs:
                    days_due = (timezone.now().date() - loan.start_date).days
                    amount_due = days_due * loan.daily_repayment_amount
                    balance = amount_due - loan.total_paid_amount
                    loans_due_amount_yesterday += balance

                overall_expected_repayment_amount = list(
                    loans_disbursed_by_agent_qs.filter(created_at__lt=today)
                    .aggregate(Sum("repayment_amount"))
                    .values()
                )[0]

                agent_total_loan_repayment_amount = (
                    agent_total_loan_repayment_amount
                    if agent_total_loan_repayment_amount
                    else 0.00
                )

                agent_total_loan_repayment_count = (
                    agent_total_loan_repayment_count
                    if agent_total_loan_repayment_count
                    else 0
                )

                agent_total_loan_repayment_amount_yesterday = (
                    agent_total_loan_repayment_amount_yesterday
                    if agent_total_loan_repayment_amount_yesterday
                    else 0.00
                )

                agent_total_loan_repayment_count_yesterday = (
                    agent_total_loan_repayment_count_yesterday
                    if agent_total_loan_repayment_count_yesterday
                    else 0
                )

                expected_repayment_amount_yesterday = (
                    expected_repayment_amount_yesterday
                    if expected_repayment_amount_yesterday
                    else 0.00
                )

                overall_expected_repayment_amount = (
                    overall_expected_repayment_amount
                    if overall_expected_repayment_amount
                    else 0.00
                )

                outstanding_repayment_amount_yesterday = (
                    expected_repayment_amount_yesterday
                    - agent_total_loan_repayment_amount_yesterday
                )

                overall_outstanding_repayment_amount = (
                    overall_expected_repayment_amount
                    - agent_total_loan_repayment_amount
                )

                agent_repayment_vs_expected_percentage_yesterday = get_percentages(
                    agent_total_loan_repayment_amount_yesterday,
                    loans_due_amount_yesterday,
                )
                overall_agent_repayment_vs_expected_percentage = get_percentages(
                    agent_total_loan_repayment_amount, overall_expected_repayment_amount
                )

                # Savings
                agent_total_savings_amount = list(
                    agent_savings_qs.aggregate(Sum("amount")).values()
                )[0]
                agent_total_savings_count = list(
                    agent_savings_qs.aggregate(Count("amount")).values()
                )[0]
                agent_total_savings_amount_yesterday = list(
                    agent_yesterday_savings_qs.aggregate(Sum("amount")).values()
                )[0]
                agent_total_savings_count_yesterday = list(
                    agent_yesterday_savings_qs.aggregate(Count("amount")).values()
                )[0]
                agent_total_active_saving_amount = list(
                    agent_ajo_savings_table_qs.filter(is_active=True)
                    .aggregate(Sum("amount_saved"))
                    .values()
                )[0]

                agent_total_savings_amount = (
                    agent_total_savings_amount if agent_total_savings_amount else 0.00
                )
                agent_total_savings_count = (
                    agent_total_savings_count if agent_total_savings_count else 0
                )
                agent_total_savings_amount_yesterday = (
                    agent_total_savings_amount_yesterday
                    if agent_total_savings_amount_yesterday
                    else 0.00
                )
                agent_total_savings_count_yesterday = (
                    agent_total_savings_count_yesterday
                    if agent_total_savings_count_yesterday
                    else 0
                )

                # Earnings
                # agent_total_earning = int((agent_total_loan_disbursed_amount if agent_total_loan_disbursed_amount else 0.00) * 0.0425)
                # agent_earning_yesterday = int((agent_total_loan_disbursed_amount_yesterday if agent_total_loan_disbursed_amount_yesterday else 0.00) * 0.0425)
                agent_current_month_earning = 0

                # Extra data from Vico
                transactions_count = list(
                    ajo_savings_transactions_qs.filter(
                        user=officer,
                        transaction_type="CREDIT",
                        # date_created__date=previous_day.date()
                    )
                    .aggregate(Count("amount"))
                    .values()
                )[0]

                login_counts = AgencyBankingClass.get_agency_user_details(
                    user_id=officer.customer_user_id, start_date=previous_day.date()
                )

                if "success" in login_counts and login_counts.get("success") == True:
                    login_count = login_counts.get("login_count")
                    supervisor = login_counts.get("supervisor")
                    branch = login_counts.get("branch")
                else:
                    login_count = 45
                    supervisor = ""
                    branch = ""

                avg_daily_savings = 3826
                avg_daily_trnx_count = 5
                avg_daily_loan = 15306

                agent_age_days = (
                    datetime.now().date() - officer.created_at.date()
                ).days
                savings_performance = round(
                    (agent_total_savings_amount_yesterday / avg_daily_savings) * 100
                )
                loans_performance = round(
                    (agent_total_loan_disbursed_amount_yesterday / avg_daily_loan) * 100
                )
                overall_performance = round(
                    (savings_performance + loans_performance) / 2
                )

                # Set Activity
                if agent_age_days > 30 and login_count > 5:
                    activity = "Active Old"
                elif agent_age_days <= 30 and login_count > 5:
                    activity = "Active New"
                elif agent_age_days <= 30 and login_count < 5:
                    activity = "Inactive New"
                else:
                    activity = "Inactive Old"

                # Set Churn
                if (
                    activity == "Active Old"
                    and agent_total_savings_count_yesterday < 1
                    and login_count < 5
                ):
                    churn = "Churned"
                else:
                    churn = "Not Churned"

                data = {
                    "Email": officer.get_username(),
                    "Supervisor": supervisor,
                    "Branch": branch,
                    "Activity": activity,
                    "Total Disbursed Amount": agent_total_loan_disbursed_amount,
                    "Total Disbursed Count": agent_total_loan_disbursed_count,
                    "Total Disbursed Amount Yesterday": agent_total_loan_disbursed_amount_yesterday,
                    "Total Disbursed Count Yesterday": agent_total_loan_disbursed_count_yesterday,
                    "Overall Expected Repayment Amount": overall_expected_repayment_amount,
                    "Total Repayment Amount": agent_total_loan_repayment_amount,
                    "Total Repayment Count": agent_total_loan_repayment_count,
                    "Total Repayment Amount Yesterday": agent_total_loan_repayment_amount_yesterday,
                    "Total Repayment Count Yesterday": agent_total_loan_repayment_count_yesterday,
                    "Due Repayment Amount Yesterday": loans_due_amount_yesterday,
                    "Loan Balance Amount": overall_outstanding_repayment_amount,
                    "Repayment vs Expected Percentage Yesterday": f"{agent_repayment_vs_expected_percentage_yesterday}%",
                    "Overall Repayment vs Expected Percentage": f"{overall_agent_repayment_vs_expected_percentage}%",
                    "Total Savings Amount": agent_total_savings_amount,
                    "Total Savings Count": agent_total_savings_count,
                    "Total Active Savings Amount": agent_total_active_saving_amount,
                    "Total Savings Amount Yesterday": agent_total_savings_amount_yesterday,
                    "Total Savings Count Yesterday": agent_total_savings_count_yesterday,
                    # "current_month_earning": agent_current_month_earning,
                    # Vico extra
                    "Transaction Count": (
                        transactions_count if transactions_count else 0
                    ),
                    "Savings Performance": f"{savings_performance}%",
                    "Loans Performance": f"{loans_performance}%",
                    "Login Count": login_count,
                    "Churned": churn,
                    "Activity": activity,
                    "Overall Performance": f"{overall_performance}%",
                }
                loan_officers_report_list.append(data)
            else:
                pass

        if loan_officers_report_list:
            # Generate and share report with Supervisor
            df = pd.DataFrame().from_dict(loan_officers_report_list)
            file_path = os.path.join(settings.BASE_DIR, "media/loan_officers")

            try:
                os.mkdir(file_path)
            except:
                pass

            excel_report = df.to_excel(
                f"{file_path}/supervisors_officers_daily_performance_{datetime.now().date()}.xlsx",
                index=False,
            )

            with open(
                f"{file_path}/supervisors_officers_daily_performance_{datetime.now().date()}.xlsx",
                "rb",
            ) as read_file:
                excel_file = read_file.read()

            # for email in admin_email_list:
            send_email = send_ajo_loan_officers_daily_report_email(
                message=f"This is the performance report for your loan officers in {branch} branch for yesterday",
                file=excel_file,
                file_name=f"{supervisor_name} Loan Officers Daily Report_{datetime.now().date()}.xlsx",
                email_subject="Your Loan Officers Daily Performance Report",
                email=supervisor_email,
                name=f"{supervisor_name}",
                date=datetime.now(),
            )
        else:
            pass
    return "DONE!!!"


@shared_task
def run_loan_supervisors_weekly_report():
    # Define Dates
    week_start = DateUtility().week_start
    today = DateUtility().today_date
    # today = timezone.now()

    supervisors_list = AgencyBankingClass.get_agency_supervisor_details()

    # Write queries
    loan_officers_qs = User.objects.filter(user_type="STAFF_AGENT")
    ajo_loans_qs = AjoLoan.objects.filter()
    ajo_loan_repayment_qs = AjoLoanRepayment.objects.all()

    ajo_savings_transactions_qs = Transaction.objects.filter(
        plan_type="AJO",
        wallet_type="AJO_USER",
        status="SUCCESS",
        transaction_form_type="WALLET_DEPOSIT",
    )
    ajo_savings_table_qs = AjoSaving.objects.all()

    if "success" in supervisors_list and supervisors_list.get("success") == True:
        teams = supervisors_list.get("supervisors_teams_list")
    else:
        teams = []

    for team in teams:
        supervisor_name = team.get("supervisor")
        supervisor_email = team.get("supervisor_email")
        supervisor_user_id = team.get("supervisor_user_id")
        loan_officers = team.get("users_list")
        branch = team.get("branch")

        loan_officers_report_list = []
        for officer_id in loan_officers:
            officer = loan_officers_qs.filter(customer_user_id=officer_id).last()

            if officer:
                loans_disbursed_by_agent_qs = ajo_loans_qs.filter(
                    agent=officer, status__in=disbursed_loans_status_list
                )
                loans_disbursed_by_agent_week_qs = loans_disbursed_by_agent_qs.filter(
                    created_at__gte=week_start
                )
                agent_loans_repayment_qs = ajo_loan_repayment_qs.filter(agent=officer)
                previous_day_agent_repayment_qs = ajo_loan_repayment_qs.filter(
                    agent=officer, paid_date__gte=week_start
                )
                agent_savings_qs = ajo_savings_transactions_qs.filter(user=officer)
                agent_week_savings_qs = agent_savings_qs.filter(
                    date_created__gte=week_start
                )
                agent_ajo_savings_table_qs = ajo_savings_table_qs.filter(user=officer)

                # Loans Disbursed
                agent_total_loan_disbursed_amount = list(
                    loans_disbursed_by_agent_qs.aggregate(Sum("amount")).values()
                )[0]
                agent_total_loan_disbursed_count = list(
                    loans_disbursed_by_agent_qs.aggregate(Count("amount")).values()
                )[0]
                agent_total_loan_disbursed_amount_week = list(
                    loans_disbursed_by_agent_week_qs.aggregate(Sum("amount")).values()
                )[0]
                agent_total_loan_disbursed_count_week = list(
                    loans_disbursed_by_agent_week_qs.aggregate(Count("amount")).values()
                )[0]

                agent_total_loan_disbursed_amount = (
                    agent_total_loan_disbursed_amount
                    if agent_total_loan_disbursed_amount
                    else 0.00
                )
                agent_total_loan_disbursed_count = (
                    agent_total_loan_disbursed_count
                    if agent_total_loan_disbursed_count
                    else 0
                )

                agent_total_loan_disbursed_amount_week = (
                    agent_total_loan_disbursed_amount_week
                    if agent_total_loan_disbursed_amount_week
                    else 0.00
                )
                agent_total_loan_disbursed_count_week = (
                    agent_total_loan_disbursed_count_week
                    if agent_total_loan_disbursed_count_week
                    else 0
                )

                # Loan Repayments
                agent_total_loan_repayment_amount = list(
                    agent_loans_repayment_qs.aggregate(Sum("repayment_amount")).values()
                )[0]
                agent_total_loan_repayment_count = list(
                    agent_loans_repayment_qs.aggregate(
                        Count("repayment_amount")
                    ).values()
                )[0]
                agent_total_loan_repayment_amount_week = list(
                    previous_day_agent_repayment_qs.aggregate(
                        Sum("repayment_amount")
                    ).values()
                )[0]
                agent_total_loan_repayment_count_week = list(
                    previous_day_agent_repayment_qs.aggregate(
                        Count("repayment_amount")
                    ).values()
                )[0]
                expected_repayment_amount_week = list(
                    loans_disbursed_by_agent_qs.filter(created_at__lt=week_start)
                    .aggregate(Count("daily_repayment_amount"))
                    .values()
                )[0]
                overall_expected_repayment_amount = list(
                    loans_disbursed_by_agent_qs.filter(created_at__lt=today)
                    .aggregate(Sum("repayment_amount"))
                    .values()
                )[0]
                expected_repayment_amount_week = (
                    expected_repayment_amount_week
                    if expected_repayment_amount_week
                    else 0.00
                )

                agent_total_loan_repayment_amount = (
                    agent_total_loan_repayment_amount
                    if agent_total_loan_repayment_amount
                    else 0.00
                )
                agent_total_loan_repayment_count = (
                    agent_total_loan_repayment_count
                    if agent_total_loan_repayment_count
                    else 0
                )
                agent_total_loan_repayment_amount_week = (
                    agent_total_loan_repayment_amount_week
                    if agent_total_loan_repayment_amount_week
                    else 0.00
                )
                agent_total_loan_repayment_count_week = (
                    agent_total_loan_repayment_count_week
                    if agent_total_loan_repayment_count_week
                    else 0
                )

                # Due Repayment Amount Yesterday
                due_loans_qs = ajo_loans_qs.filter(
                    agent=officer, start_date__lte=timezone.now().date()
                )

                loans_due_amount = 0
                for loan in due_loans_qs:
                    days_due = (timezone.now().date() - loan.start_date).days
                    amount_due = days_due * loan.daily_repayment_amount
                    balance = amount_due - loan.total_paid_amount
                    loans_due_amount += balance

                # Savings
                agent_total_savings_amount = list(
                    agent_savings_qs.aggregate(Sum("amount")).values()
                )[0]
                agent_total_active_savings_amount = list(
                    agent_ajo_savings_table_qs.filter(is_active=True)
                    .aggregate(Sum("amount_saved"))
                    .values()
                )[0]
                agent_total_savings_count = list(
                    agent_savings_qs.aggregate(Count("amount")).values()
                )[0]
                agent_total_savings_amount_week = list(
                    agent_week_savings_qs.aggregate(Sum("amount")).values()
                )[0]
                agent_total_savings_count_week = list(
                    agent_week_savings_qs.aggregate(Count("amount")).values()
                )[0]

                agent_total_savings_amount = (
                    agent_total_savings_amount if agent_total_savings_amount else 0.00
                )
                agent_total_active_savings_amount = (
                    agent_total_active_savings_amount
                    if agent_total_active_savings_amount
                    else 0.00
                )
                agent_total_savings_count = (
                    agent_total_savings_count if agent_total_savings_count else 0
                )
                agent_total_savings_amount_week = (
                    agent_total_savings_amount_week
                    if agent_total_savings_amount_week
                    else 0.00
                )
                agent_total_savings_count_week = (
                    agent_total_savings_count_week
                    if agent_total_savings_count_week
                    else 0
                )
                expected_repayment_amount_week = (
                    expected_repayment_amount_week
                    if expected_repayment_amount_week
                    else 0.00
                )
                overall_expected_repayment_amount = (
                    overall_expected_repayment_amount
                    if overall_expected_repayment_amount
                    else 0.00
                )

                outstanding_repayment_amount_week = (
                    expected_repayment_amount_week
                    - agent_total_loan_repayment_amount_week
                )
                overall_outstanding_repayment_amount = (
                    overall_expected_repayment_amount
                    - agent_total_loan_repayment_amount
                )

                agent_repayment_vs_expected_percentage_week = get_percentages(
                    agent_total_loan_repayment_amount_week, loans_due_amount
                )
                overall_agent_repayment_vs_expected_percentage = get_percentages(
                    agent_total_loan_repayment_amount, overall_expected_repayment_amount
                )

                # Earnings
                # agent_total_earning = int((agent_total_loan_disbursed_amount if agent_total_loan_disbursed_amount else 0.00) * 0.0425)
                agent_earning_week = int(
                    (
                        agent_total_loan_disbursed_amount_week
                        if agent_total_loan_disbursed_amount_week
                        else 0.00
                    )
                    * 0.0425
                )

                # Extra data from Vico
                transactions_count = list(
                    ajo_savings_transactions_qs.filter(
                        user=officer, transaction_type="CREDIT"
                    )
                    .aggregate(Count("amount"))
                    .values()
                )[0]

                login_counts = AgencyBankingClass.get_agency_user_details(
                    user_id=officer.customer_user_id, start_date=week_start
                )

                if "success" in login_counts and login_counts.get("success") == True:
                    login_count = login_counts.get("login_count")
                    supervisor = login_counts.get("supervisor")
                    branch = login_counts.get("branch")
                else:
                    login_count = 45
                    supervisor = ""
                    branch = ""

                avg_weekly_savings = 187500
                avg_weekly_trnx_count = 38
                avg_Weekly_loan = 750000

                agent_age_days = (
                    datetime.now().date() - officer.created_at.date()
                ).days
                savings_performance = round(
                    (agent_total_savings_amount_week / avg_weekly_savings) * 100
                )
                loans_performance = round(
                    (agent_total_loan_disbursed_amount_week / avg_Weekly_loan) * 100
                )
                overall_performance = round(
                    (savings_performance + loans_performance) / 2
                )

                # Set Activity
                if agent_age_days > 30 and login_count > 40:
                    activity = "Active Old"
                elif agent_age_days <= 30 and login_count > 20:
                    activity = "Active New"
                elif agent_age_days <= 30 and login_count < 20:
                    activity = "Inactive New"
                else:
                    activity = "Inactive Old"

                # Set Churn
                if (
                    activity == "Active Old"
                    and agent_total_savings_count_week < 1
                    and login_count < 10
                ):
                    churn = "Churned"
                else:
                    churn = "Not Churned"

                data = {
                    "Email": officer.get_username(),
                    # "Name": officer.get_username(),
                    "Branch": branch,
                    "Supervisor": supervisor,
                    "Total Disbursed Amount": agent_total_loan_disbursed_amount,
                    "Total Disbursed Count": agent_total_loan_disbursed_count,
                    "Total Disbursed Amount Week": agent_total_loan_disbursed_amount_week,
                    "Total Disbursed Count Week": agent_total_loan_disbursed_count_week,
                    "Overall Expected Repayment Amount": overall_expected_repayment_amount,
                    "Total Repayment Amount": agent_total_loan_repayment_amount,
                    "Total Repayment Count": agent_total_loan_repayment_count,
                    "Total Repayment Amount Week": agent_total_loan_repayment_amount_week,
                    "Total Repayment Count Week": agent_total_loan_repayment_count_week,
                    "Due Repayment Amount Week": loans_due_amount,
                    "Loan Balance Amount": overall_outstanding_repayment_amount,
                    "Repayment vs Expected Percentage Week": f"{round(agent_repayment_vs_expected_percentage_week)}%",
                    "Overall Repayment vs Expected Percentage": f"{round(overall_agent_repayment_vs_expected_percentage)}%",
                    "Total Savings Amount": agent_total_savings_amount,
                    "Total Savings Count": agent_total_savings_count,
                    "Total Active Savings Amount": agent_total_active_savings_amount,
                    "Total Savings Amount Week": agent_total_savings_amount_week,
                    "Total Savings Count Week": agent_total_savings_count_week,
                    # Vico extra
                    "Transaction Count": (
                        transactions_count if transactions_count else 0
                    ),
                    "Savings Performance": f"{savings_performance}%",
                    "Loans Performance": f"{loans_performance}%",
                    "Login Count": login_count,
                    "Churned": churn,
                    "Activity": activity,
                    "Overall Performance": f"{overall_performance}%",
                }
                loan_officers_report_list.append(data)
            else:
                pass

        if loan_officers_report_list:
            # Generate and share report
            df = pd.DataFrame().from_dict(loan_officers_report_list)
            file_path = os.path.join(settings.BASE_DIR, "media/loan_officers")

            try:
                os.mkdir(file_path)
            except:
                pass

            excel_report = df.to_excel(
                f"{file_path}/supervisor_officers_weekly_performance_{datetime.now().date()}.xlsx"
            )

            with open(
                f"{file_path}/supervisor_officers_weekly_performance_{datetime.now().date()}.xlsx",
                "rb",
            ) as read_file:
                excel_file = read_file.read()

            send_email = send_ajo_loan_officers_daily_report_email(
                message=f"This is the report for your loan officers in {branch} branch for the week",
                file=excel_file,
                file_name=f"{supervisor_name} Loan Officers Weekly Report_{datetime.now().date()}.xlsx",
                email_subject="Your Loan Officers Weekly Performance Report",
                email=supervisor_email,
                name=f"{supervisor_name}",
                date=datetime.now(),
            )
        else:
            pass

    return "DONE!!!"


@shared_task
def run_loan_supervisors_monthly_report():
    # Define Dates
    month_start = DateUtility().month_start
    today = DateUtility().today_date

    supervisors_list = AgencyBankingClass.get_agency_supervisor_details()

    # Write queries
    loan_officers_qs = User.objects.filter(user_type="STAFF_AGENT")
    ajo_loans_qs = AjoLoan.objects.filter()
    ajo_loan_repayment_qs = AjoLoanRepayment.objects.all()

    ajo_savings_transactions_qs = Transaction.objects.filter(
        plan_type="AJO",
        wallet_type="AJO_USER",
        status="SUCCESS",
        transaction_form_type="WALLET_DEPOSIT",
    )
    ajo_savings_table_qs = AjoSaving.objects.all()

    if "success" in supervisors_list and supervisors_list.get("success") == True:
        teams = supervisors_list.get("supervisors_teams_list")
    else:
        teams = []

    for team in teams:
        supervisor_name = team.get("supervisor")
        supervisor_email = team.get("supervisor_email")
        supervisor_user_id = team.get("supervisor_user_id")
        loan_officers = team.get("users_list")
        branch = team.get("branch")

        loan_officers_report_list = []
        for officer_id in loan_officers:
            officer = loan_officers_qs.filter(customer_user_id=officer_id).last()

            if officer:
                loans_disbursed_by_agent_qs = ajo_loans_qs.filter(
                    agent=officer, status__in=disbursed_loans_status_list
                )
                loans_disbursed_by_agent_month_qs = loans_disbursed_by_agent_qs.filter(
                    created_at__gte=month_start
                )
                agent_loans_repayment_qs = ajo_loan_repayment_qs.filter(agent=officer)
                previous_day_agent_repayment_qs = ajo_loan_repayment_qs.filter(
                    agent=officer, paid_date__gte=month_start
                )
                agent_savings_qs = ajo_savings_transactions_qs.filter(user=officer)
                agent_month_savings_qs = agent_savings_qs.filter(
                    date_created__gte=month_start
                )

                # Loans Disbursed
                agent_total_loan_disbursed_amount = list(
                    loans_disbursed_by_agent_qs.aggregate(Sum("amount")).values()
                )[0]
                agent_total_loan_disbursed_count = list(
                    loans_disbursed_by_agent_qs.aggregate(Count("amount")).values()
                )[0]
                agent_total_loan_disbursed_amount_month = list(
                    loans_disbursed_by_agent_month_qs.aggregate(Sum("amount")).values()
                )[0]
                agent_total_loan_disbursed_count_month = list(
                    loans_disbursed_by_agent_month_qs.aggregate(
                        Count("amount")
                    ).values()
                )[0]

                agent_total_loan_disbursed_amount = (
                    agent_total_loan_disbursed_amount
                    if agent_total_loan_disbursed_amount
                    else 0.00
                )
                agent_total_loan_disbursed_count = (
                    agent_total_loan_disbursed_count
                    if agent_total_loan_disbursed_count
                    else 0
                )
                agent_total_loan_disbursed_amount_month = (
                    agent_total_loan_disbursed_amount_month
                    if agent_total_loan_disbursed_amount_month
                    else 0.00
                )
                agent_total_loan_disbursed_count_month = (
                    agent_total_loan_disbursed_count_month
                    if agent_total_loan_disbursed_count_month
                    else 0
                )

                # Loan Repayments
                agent_total_loan_repayment_amount = list(
                    agent_loans_repayment_qs.aggregate(Sum("repayment_amount")).values()
                )[0]
                agent_total_loan_repayment_count = list(
                    agent_loans_repayment_qs.aggregate(
                        Count("repayment_amount")
                    ).values()
                )[0]
                agent_total_loan_repayment_amount_month = list(
                    previous_day_agent_repayment_qs.aggregate(
                        Sum("repayment_amount")
                    ).values()
                )[0]
                agent_total_loan_repayment_count_month = list(
                    previous_day_agent_repayment_qs.aggregate(
                        Count("repayment_amount")
                    ).values()
                )[0]
                expected_repayment_amount_month = list(
                    loans_disbursed_by_agent_qs.filter(created_at__lt=month_start)
                    .aggregate(Sum("daily_repayment_amount"))
                    .values()
                )[0]
                overall_expected_repayment_amount = list(
                    loans_disbursed_by_agent_qs.filter(created_at__lt=today)
                    .aggregate(Sum("repayment_amount"))
                    .values()
                )[0]
                expected_repayment_amount_month = (
                    expected_repayment_amount_month
                    if expected_repayment_amount_month
                    else 0.00
                )

                agent_total_loan_repayment_amount = (
                    agent_total_loan_repayment_amount
                    if agent_total_loan_repayment_amount
                    else 0.00
                )
                agent_total_loan_repayment_count = (
                    agent_total_loan_repayment_count
                    if agent_total_loan_repayment_count
                    else 0
                )
                agent_total_loan_repayment_amount_month = (
                    agent_total_loan_repayment_amount_month
                    if agent_total_loan_repayment_amount_month
                    else 0.00
                )
                agent_total_loan_repayment_count_month = (
                    agent_total_loan_repayment_count_month
                    if agent_total_loan_repayment_count_month
                    else 0
                )
                expected_repayment_amount_month = (
                    expected_repayment_amount_month
                    if expected_repayment_amount_month
                    else 0.00
                )
                overall_expected_repayment_amount = (
                    overall_expected_repayment_amount
                    if overall_expected_repayment_amount
                    else 0.00
                )

                # Due Repayment Amount Yesterday
                due_loans_qs = ajo_loans_qs.filter(
                    agent=officer, start_date__lte=timezone.now().date()
                )

                loans_due_amount = 0
                for loan in due_loans_qs:
                    days_due = (timezone.now().date() - loan.start_date).days
                    amount_due = days_due * loan.daily_repayment_amount
                    balance = amount_due - loan.total_paid_amount
                    loans_due_amount += balance

                outstanding_repayment_amount_month = (
                    expected_repayment_amount_month
                    - agent_total_loan_repayment_amount_month
                )
                overall_outstanding_repayment_amount = (
                    overall_expected_repayment_amount
                    - agent_total_loan_repayment_amount
                )

                agent_repayment_vs_expected_percentage_month = get_percentages(
                    agent_total_loan_repayment_amount_month, loans_due_amount
                )
                overall_agent_repayment_vs_expected_percentage = get_percentages(
                    agent_total_loan_repayment_amount, overall_expected_repayment_amount
                )

                # Savings
                agent_ajo_savings_table_qs = ajo_savings_table_qs.filter(user=officer)
                agent_total_savings_amount = list(
                    agent_savings_qs.aggregate(Sum("amount")).values()
                )[0]
                agent_total_active_savings_amount = list(
                    agent_ajo_savings_table_qs.filter(is_active=True)
                    .aggregate(Sum("amount_saved"))
                    .values()
                )[0]
                agent_total_savings_count = list(
                    agent_savings_qs.aggregate(Count("amount")).values()
                )[0]
                agent_total_savings_amount_month = list(
                    agent_month_savings_qs.aggregate(Sum("amount")).values()
                )[0]
                agent_total_savings_count_month = list(
                    agent_month_savings_qs.aggregate(Count("amount")).values()
                )[0]

                agent_total_savings_amount = (
                    agent_total_savings_amount if agent_total_savings_amount else 0.00
                )
                agent_total_savings_count = (
                    agent_total_savings_count if agent_total_savings_count else 0
                )
                agent_total_savings_amount_month = (
                    agent_total_savings_amount_month
                    if agent_total_savings_amount_month
                    else 0.00
                )
                agent_total_savings_count_month = (
                    agent_total_savings_count_month
                    if agent_total_savings_count_month
                    else 0
                )

                # Earnings
                # agent_total_earning = int((agent_total_loan_disbursed_amount if agent_total_loan_disbursed_amount else 0.00) * 0.0425)
                agent_earning_month = 0.00

                # Extra data from Vico
                transactions_count = list(
                    ajo_savings_transactions_qs.filter(
                        user=officer, transaction_type="CREDIT"
                    )
                    .aggregate(Count("amount"))
                    .values()
                )[0]

                login_counts = AgencyBankingClass.get_agency_user_details(
                    user_id=officer.customer_user_id, start_date=month_start
                )

                if "success" in login_counts and login_counts.get("success") == True:
                    login_count = login_counts.get("login_count")
                    supervisor = login_counts.get("supervisor")
                    branch = login_counts.get("branch")
                else:
                    login_count = 30
                    supervisor = ""
                    branch = ""

                avg_monthly_savings = 187500
                avg_monthly_trnx_count = 38
                avg_monthly_loan = 750000

                agent_age_days = (
                    datetime.now().date() - officer.created_at.date()
                ).days
                savings_performance = round(
                    (agent_total_savings_amount_month / avg_monthly_savings) * 100
                )
                loans_performance = round(
                    (agent_total_loan_disbursed_amount_month / avg_monthly_loan) * 100
                )
                overall_performance = round(
                    (savings_performance + loans_performance) / 2
                )

                # Set Activity
                if agent_age_days > 30 and login_count > 160:
                    activity = "Active Old"
                elif agent_age_days <= 30 and login_count > 80:
                    activity = "Active New"
                elif agent_age_days <= 30 and login_count < 80:
                    activity = "Inactive New"
                else:
                    activity = "Inactive Old"

                # Set Churn
                if (
                    activity == "Active Old"
                    and agent_total_savings_count_month < 4
                    and login_count < 40
                ):
                    churn = "Churned"
                else:
                    churn = "Not Churned"

                data = {
                    "Email": officer.get_username(),
                    "Branch": branch,
                    "Supervisor": supervisor,
                    "Total Disbursed Amount": agent_total_loan_disbursed_amount,
                    "Total Disbursed Count": agent_total_loan_disbursed_count,
                    "Total Disbursed Amount Month": agent_total_loan_disbursed_amount_month,
                    "Total Disbursed Count Month": agent_total_loan_disbursed_count_month,
                    "Overall Expected Repayment Amount": overall_expected_repayment_amount,
                    "Total Repayment Amount": agent_total_loan_repayment_amount,
                    "Total Repayment Count": agent_total_loan_repayment_count,
                    "Total Repayment Amount Month": agent_total_loan_repayment_amount_month,
                    "Total Repayment Count Month": agent_total_loan_repayment_count_month,
                    "Due Repayment Amount Month": loans_due_amount,
                    "Loan Balance Amount": overall_outstanding_repayment_amount,
                    "Repayment vs Expected Percentage Month": f"{agent_repayment_vs_expected_percentage_month}%",
                    "Overall Repayment vs Expected Percentage": f"{overall_agent_repayment_vs_expected_percentage}%",
                    "Total Savings Amount": agent_total_savings_amount,
                    "Total Savings Count": agent_total_savings_count,
                    "Total Active Savings Amount": agent_total_active_savings_amount,
                    "Total Savings Amount Month": agent_total_savings_amount_month,
                    "Total Savings Count Month": agent_total_savings_count_month,
                    # Vico extra
                    "Transaction Count": (
                        transactions_count if transactions_count else 0
                    ),
                    "Savings Performance": f"{savings_performance}%",
                    "Loans Performance": f"{loans_performance}%",
                    "Login Count": login_count,
                    "Churned": churn,
                    "Activity": activity,
                    "Overall Performance": f"{overall_performance}%",
                }
                loan_officers_report_list.append(data)
            else:
                pass

        if loan_officers_report_list:
            # Generate and share report
            df = pd.DataFrame().from_dict(loan_officers_report_list)
            file_path = os.path.join(settings.BASE_DIR, "media/loan_officers")

            try:
                os.mkdir(file_path)
            except:
                pass

            excel_report = df.to_excel(
                f"{file_path}/supervisor_officers_monthly_performance_{datetime.now().date()}.xlsx"
            )

            with open(
                f"{file_path}/supervisor_officers_monthly_performance_{datetime.now().date()}.xlsx",
                "rb",
            ) as read_file:
                excel_file = read_file.read()

            send_email = send_ajo_loan_officers_daily_report_email(
                message=f"This is the report for your loan officers in {branch} branch for the month",
                file=excel_file,
                file_name=f"{supervisor_name} Loan Officers monthly Report_{datetime.now().date()}.xlsx",
                email_subject="Your Loan Officers monthly Performance Report",
                email=supervisor_email,
                name=f"{supervisor_name}",
                date=datetime.now(),
            )
        else:
            pass

    return "DONE!!!"


# SMS TEMPLATES TO BORROWER AND AGENT


def custom_difference_between_days(start_date: str, end_date: str):
    from datetime import datetime

    start = datetime.strptime(f"{start_date}", "%Y-%m-%d")
    end = datetime.strptime(f"{end_date}", "%Y-%m-%d")
    delta = end - start
    return delta.days


# @shared_task
# def send_sms_to_ajo_loan_officer_loan_summary(loan_officer):
#     agent = CustomUser.objects.filter(id=loan_officer).last()

#     user_name = agent.username

#     agent_status = agent.performance_status

#     from loans.models import AjoLoan

#     outstanding_loans = AjoLoan.objects.filter(agent=agent).count()

#     customer_count = outstanding_loans

#     open_ajo_loan_under_agent = AjoLoan.objects.filter(agent=agent).filter(status="OPEN")

#     defaulted_customer_count = open_ajo_loan_under_agent.count()

#     phone_number = agent.user_phone

#     defaulted_amount = 0

#     for object in open_ajo_loan_under_agent:
#         total_paid_amount = object.total_paid_amount

#         loan_repayment_start_date = object.start_date

#         date = str(datetime.now().date())

#         number_of_days_so_far = custom_difference_between_days(loan_repayment_start_date, date)

#         actual_amount_to_be_paid_till_date = number_of_days_so_far * object.daily_repayment_amount

#         default_amount = round((actual_amount_to_be_paid_till_date - total_paid_amount), 2)

#         if (
#             (total_paid_amount < actual_amount_to_be_paid_till_date)
#             and (agent_status == "ACTIVE" or agent_status == "INACTIVE")
#             and (open_ajo_loan_under_agent)
#         ):
#             defaulted_amount += default_amount

#     total_loan_repayment = 0

#     for amount in open_ajo_loan_under_agent:
#         repayment_amount = amount.daily_repayment_amount * 7

#         total_loan_repayment += repayment_amount

#     # message = (
#     # f"Dear {user_name}, \nYour status is {agent_status} \nLoan repayment for this week is a total of {total_loan_repayment} for {customer_count} customer(s) \nMissed: {defaulted_amount} for {defaulted_customer_count} customer(s) \nPast Maturity: \nOutstanding loans: {outstanding_loans} "
#     # )
#     # print(f"Message ----> \n\n====================================\n\n{message}\n\n====================================")

#     place_holders = {
#         "user_name": user_name,
#         "agent_status": agent_status,
#         "total_loan_repayment": total_loan_repayment,
#         "customer_count": customer_count,
#         "defaulted_amount": defaulted_amount,
#         "defaulted_customer_count": defaulted_customer_count,
#         "outstanding_loans": outstanding_loans,
#     }

#     TextMessages.dynamic_send_sms(
#         user=agent,
#         phone_number=phone_number,
#         template_id=loan_officer_sms_template_id,
#         placeholders=place_holders,
#     )


@shared_task
def send_sms_to_ajo_loan_borrower_loan_summary():
    # Get all Loans the are either OPEN, PAST MATURITY OR LOST
    open_loans = AjoLoan.objects.filter(status=LoanStatus.OPEN)

    info = {}

    # Go through all Loans
    for ajo_loan in open_loans:
        user_name = ajo_loan.borrower.fullname
        ajo_user = ajo_loan.borrower
        phone_number = ajo_loan.borrower.phone

        try:
            borrower_bank_details = (
                BankAccountSelector(
                    ajo_user=ajo_user,
                )
                .get_ajo_user_account_details(form_type=AccountFormType.LOAN_REPAYMENT)
                .account_number
            )
        except ValueError:
            borrower_bank_details = None

        expected_repayment_amount = ajo_loan.repayment_amount
        balance = expected_repayment_amount - ajo_loan.total_paid_amount
        defaulted_amount = ajo_loan.outstanding_due

        if defaulted_amount > balance:
            defaulted_amount = balance
        date = str(datetime.now().date())

        place_holders = {
            "user_name": user_name,
            "defaulted_amount": defaulted_amount,
            "borrower_bank_details": borrower_bank_details,
            "amount": ajo_loan.repayment_amount,
            "loan_balance": balance,
            "date": date,
        }

        if defaulted_amount > 0:

            sms_response = TextMessages.dynamic_send_sms(
                ajo_user=ajo_user,
                phone_number=phone_number,
                template_id=loan_borrower_sms_template_id,
                placeholders=place_holders,
            )
            # print(sms_response)
            info[f"{phone_number} - {user_name}"] = sms_response

    return info


# @shared_task
# def send_sms_to_ajo_loan_borrower_loan_summary(ajo_user):
#     ajo_user = AjoUser.objects.get(id=ajo_user)
#     # ajo_user = AjoUser.objects.filter(id=ajo_user).last()

#     from ajo.models import BankAccountDetails

#     borrower_bank_details = (
#         BankAccountSelector(ajo_user=ajo_user)
#         .get_ajo_user_account_details(form_type=AccountFormType.LOAN_REPAYMENT)
#         .account_number
#     )

#     from loans.models import AjoLoan

#     ajo_loan = AjoLoan.objects.filter(borrower=ajo_user).last()

#     user_name = ajo_loan.borrower.first_name

#     amount = ajo_loan.amount

#     total_paid_amount = ajo_loan.total_paid_amount

#     loan_repayment_start_date = ajo_loan.start_date

#     date = str(datetime.now().date())

#     number_of_days_so_far = custom_difference_between_days(loan_repayment_start_date, date)

#     actual_amount_to_be_paid_till_date = number_of_days_so_far * ajo_loan.daily_repayment_amount

#     defaulted_amount = round((actual_amount_to_be_paid_till_date - total_paid_amount), 2)

#     loan_balance = amount - total_paid_amount

#     phone_number = ajo_loan.borrower.phone

#     # message = f"Dear {user_name}, \nYour loan repayment of {defaulted_amount} till date is due. \nMake payment via the details provided; \n{borrower_bank_details} or contact your agent. \n\nPayment Received:\nAmount: {amount} \nBalance: {loan_balance} \nPhone Number: +************* \nDate Sent: {date}"
#     # print(f"Message ----> \n\n====================================\n\n{message}\n\n====================================")

#     place_holders = {
#         "user_name": user_name,
#         "defaulted_amount": defaulted_amount,
#         "borrower_bank_details": borrower_bank_details,
#         "amount": amount,
#         "loan_balance": loan_balance,
#         "date": date,
#     }

#     if total_paid_amount <= actual_amount_to_be_paid_till_date:
#         TextMessages.dynamic_send_sms(
#             ajo_user=ajo_user,
#             phone_number=phone_number,
#             template_id=loan_borrower_sms_template_id,
#             placeholders=place_holders,
#         )


@shared_task
def send_sms_to_ajo_loan_borrower_loan_summary():
    # Get all Loans the are either OPEN, PAST MATURITY OR LOST
    open_loans = AjoLoan.objects.filter(status=LoanStatus.OPEN)

    info = {}

    # Go through all Loans
    for ajo_loan in open_loans:
        # Get Borrowers Name
        user_name = ajo_loan.borrower.fullname

        # Get Borrower details
        ajo_user = ajo_loan.borrower

        phone_number = ajo_loan.borrower.phone

        try:
            borrower_bank_details = (
                BankAccountSelector(
                    ajo_user=ajo_user,
                )
                .get_ajo_user_account_details(form_type=AccountFormType.LOAN_REPAYMENT)
                .account_number
            )
        except ValueError:
            borrower_bank_details = None

        # Get Amount received by borrower
        amount = ajo_loan.amount

        # Get Borrower loan balance
        loan_balance = amount - ajo_loan.total_repayment

        # Get Borrower defaulted amount as of yesterday
        defaulted_amount = ajo_loan.outstanding_due

        if defaulted_amount > loan_balance:
            defaulted_amount = loan_balance

        # Get Current Date
        date = str(datetime.now().date())

        place_holders = {
            "user_name": user_name,
            "defaulted_amount": defaulted_amount,
            "borrower_bank_details": borrower_bank_details,
            "amount": amount,
            "loan_balance": loan_balance,
            "date": date,
        }

        if defaulted_amount > 0:
            sms_response = TextMessages.dynamic_send_sms(
                ajo_user=ajo_user,
                phone_number=phone_number,
                template_id=loan_borrower_sms_template_id,
                placeholders=place_holders,
            )

            info[f"{phone_number} - {user_name}"] = sms_response

    return info


# POST SAVINGS TO LOANDISK
@shared_task
def run_post_savings_to_loan_disk(ajo_savings_id):
    """
    Posts Ajo Saving details to the LoanDisk system for a given Ajo Saving ID.
    """
    try:
        BRANCH_LOCATION_CODES = (
            ConstantTable.get_constant_table_instance().branch_location_codes
        )
        try:
            instance = AjoSaving.objects.get(id=ajo_savings_id)

        except AjoSaving.DoesNotExists:
            return {}

        user = instance.user

        ajo_user = instance.ajo_user

        if ajo_user.loandisk_borrower_id == "" or ajo_user.loandisk_borrower_id is None:
            create_borrower_on_loandisk = LoandiskManager().get_or_create_borrower(
                phone_number=ajo_user.phone_number
            )
            ajo_user.loandisk_borrower_id = create_borrower_on_loandisk
            ajo_user.save()
            ajo_user_loandisk_borrower_id = ajo_user.loandisk_borrower_id

        else:
            ajo_user_loandisk_borrower_id = ajo_user.loandisk_borrower_id

        ajo_savings = AjoSavingsSelector(
            id=ajo_savings_id,
            user=user,
        ).get_ajo_saving_plan()

        # ajo_user_wallet = AjoUserSelector(ajo_user=ajo_user).get_ajo_user_wallet()

        ajo_user_account_number = (
            AjoUserSelector(ajo_user=ajo_user).get_ajo_user_wallet().wallet_number
        )

        ajo_user_balance = AjoUserSelector(
            ajo_user=ajo_user
        ).get_amount_saved_in_active_savings()

        savings_status = 0
        # if datetime.now().date == instance.maturity_date:
        #     return savings_status == 3
        # elif datetime.now().date < instance.maturity_date:
        #     return savings_status == 1

        saving_details = {
            "savings_id": ajo_savings_id,
            "borrower_id": ajo_user_loandisk_borrower_id,
            # "savings_account_number": "***********",
            "savings_account_number": ajo_user_account_number,
            "savings_fees": "125",
            # "savings_fees": instance.commission_amount,
            # "savings_description": "",
            "savings_balance": ajo_user_balance,
            "savings_status": savings_status,
        }

        print("------------------------------------------------------------")
        print("POSTING TO GENERAL BRANCH")
        print("------------------------------------------------------------\n")

        response = LoandiskManager().post_savings(
            savings_id=ajo_savings_id, saving_details=saving_details
        )

        print("------------------------------------------------------------")
        print("POSTED TO GENERAL BRANCH")
        print("------------------------------------------------------------\n")

        metadata, created = LoanDiskMetaData.objects.get_or_create(
            savings_id=ajo_savings_id
        )
        metadata.main_branch_response = response
        metadata.save()

        savings_id = response.get("response", {}).get("savings_id", None)
        instance.savings_id_on_loan_disk = savings_id
        instance.save()  # get ajo user details on agency banking

        branch_name = ajo_user.branch_name

        # get borrower branch code
        branch_id = BRANCH_LOCATION_CODES.get(str(branch_name).upper(), None)

        print("------------------------------------------------------------")
        print("POSTING TO PERSONAL BRANCH")
        print("------------------------------------------------------------\n")
        branch_response = BranchLoanDiskManager().post_savings_to_branch(
            saving_details=saving_details, branch_id=branch_id
        )
        print("------------------------------------------------------------")
        print("POSTED TO PERSONAL BRANCH")
        print("------------------------------------------------------------\n")

        metadata, created = LoanDiskMetaData.objects.get_or_create(
            savings_id=ajo_savings_id
        )
        metadata.branch_response = branch_response
        metadata.save()

        branch_savings_id = response.get("response", {}).get("borrower_id", None)
        instance.savings_id_on_loan_disk_branch = branch_savings_id
        instance.save()

    except Exception as err:
        return f"{err}"


@shared_task
def celery_handle_loandisk_loan_repayment(
    ajo_loan_id: int, amount: float, repayment_type: str, retry=0, loan_mirror_id=None
):

    try:
        ajo_loan_instance = AjoLoan.objects.get(id=ajo_loan_id)
    except AjoLoan.DoesNotExist:
        return "Loan does not exist"

    loandisk_repayment_method = "33630"
    if RepaymentType.TRANSFER == repayment_type:
        loandisk_repayment_method = "50573"

    borrower_instance = ajo_loan_instance.borrower

    loandisk_loan_id = ajo_loan_instance.loandisk_loan_id

    loandisk_main_branch_repayment_response = None

    if loandisk_loan_id:

        loandisk_main_branch_repayment_response = LoandiskManager().post_repayment(
            loandisk_loan_id=loandisk_loan_id,
            amount=amount,
            loan_repayment_method_id=loandisk_repayment_method,
            loan_mirror_id=loan_mirror_id,
        )
    else:
        loandisk_main_branch_repayment_response = (
            f"this loan {ajo_loan_instance} does not have a loandisk loan id"
        )

    # POSTING TO BRANCH LOANDISK
    branch_loandisk_loan_id = ajo_loan_instance.branch_loandisk_loan_id
    loandisk_branch_repayment_response = None

    BRANCH_LOCATION_CODES = (
        ConstantTable.get_constant_table_instance().branch_location_codes
    )

    branch_id = BRANCH_LOCATION_CODES.get(
        str(borrower_instance.branch_name).upper(), None
    )

    if branch_loandisk_loan_id and branch_id:
        loandisk_branch_repayment_response = BranchLoanDiskManager().post_repayment(
            loandisk_loan_id=branch_loandisk_loan_id,
            amount=amount,
            branch_id=branch_id,
            loan_repayment_method_id=loandisk_repayment_method,
        )

    else:
        loandisk_branch_repayment_response = f"this loan {ajo_loan_instance} does not have a branch loandisk loan id or borrower id"

    response = {
        "message": f"{loandisk_main_branch_repayment_response}:{loandisk_branch_repayment_response}"
    }

    if "This loan has not been released to the borrower" in response.get("message"):
        if retry >= 2:
            return response

        update_loan_status_on_loan_disk(
            loan_id=ajo_loan_id, loan_mirror_id=loan_mirror_id
        )
        celery_handle_loandisk_loan_repayment.delay(
            ajo_loan_id=ajo_loan_id,
            amount=amount,
            repayment_type=repayment_type,
            retry=retry + 1,
        )

    else:
        return response


@shared_task
def repay_loan_from_savings() -> None:
    """Task to check if a loan as at yesterday, is overdue by 5 days or more, then it calls the repayment function which repays the amounut due from the user's savings"""

    laon_status = [LoanStatus.OPEN, LoanStatus.DEFAULTED]
    loans = AjoLoan.objects.filter(status__in=laon_status)
    loans_due_past_15_day = [loan for loan in loans if loan.outstanding_days >= 15]

    for loan in loans_due_past_15_day:
        process_loan_repayment_from_spend_wallet(loan)


@shared_task
def disbursement_notification(ajo_user_id):
    notification = AjoLoan.send_sms_to_ajo_loan_borrower_on_open_loan(
        ajo_user_id=ajo_user_id
    )
    return notification


@shared_task
def convert_ajo_savings_to_loan_repayment(plans_info: List[Dict[str, Any]]):
    """
    Converts a List of plan info to loan repayments

    Args:
        plans_info (List[Dict[str, Any]]): e.g. [{"plan_id": 80, "loan_id": 88}]
    """
    plans_worked_on = {}
    if not plans_info:
        {"message": "no plans to work on"}

    for count, plan_info in enumerate(plans_info):
        plan_id = plan_info.get("plan_id", None)
        loan_id = plan_info.get("loan_id", None)

        if not plan_id or not loan_id:
            plans_worked_on[count] = (
                f"for item {count+1} on the list, no plan id or loan id"
            )
            continue

        try:
            ajo_plan = AjoSaving.objects.get(id=plan_id)
        except AjoSaving.DoesNotExist as err:
            plans_worked_on[plan_id] = "ajo plan does not exist"
            continue

        try:
            DiscrepancyFixes.convert_ajo_savings_to_repayment(
                ajo_plan=ajo_plan,
                loan_id=loan_id,
            )

            plans_worked_on[plan_id] = "plan successfully converted to repayment"

        except Exception as err:
            plans_worked_on[plan_id] = f"encountered error with plan: {err}"

    return plans_worked_on


@shared_task
def create_loan_repayment_on_failed_instance_record(transaction_id, loan_id):
    recorded_txn_instance = Transaction.objects.filter(
        transaction_id=transaction_id
    ).last()
    if not recorded_txn_instance:
        response = "txn does not exist"
    else:
        if (
            recorded_txn_instance.transaction_form_type
            != TransactionFormType.LOAN_REPAYMENT
        ):
            response = f"txn ref {transaction_id} is not a loan repayment type"

        else:
            repayment_qs = AjoLoanRepayment.objects.filter(repayment_ref=transaction_id)
            if repayment_qs.exists():
                response = f"txn ref {transaction_id} has an existing loan repayment"
            else:

                try:
                    loan_instance = AjoLoan.objects.get(id=loan_id)
                    amount = recorded_txn_instance.amount
                    repayment_record = AjoLoanRepayment.objects.create(
                        agent=loan_instance.agent,
                        ajo_loan=loan_instance,
                        borrower=loan_instance.borrower,
                        loan_amount=loan_instance.amount,
                        repayment_amount=amount,
                        repayment_ref=transaction_id,
                        repayment_type=RepaymentType.AGENT_DEBIT,
                    )
                    repayment_record.handle_repayment_settlement()
                    response = f"Successfully resolved"
                except AjoLoan.DoesNotExist:
                    response = f"loan id {loan_id} DoesNotExist"
    return response


@shared_task
def process_loan_documentation(kyc_doc_id):
    print("PROCESSING LOAN KYC DOCUMENTATION")
    documentation_instance = LoanKYCDocumentation.objects.get(id=kyc_doc_id)
    print("GOT DOCUMENTATION INSTANCE")

    try:
        documentation_instance.process_borrower_data
    except Exception as e:
        print(
            f"An error occurred while requesting borrower NIN/BVN verification and borrower face match: {e}"
        )

    for _ in range(10):
        sleep(1)

    try:
        documentation_instance.process_guarantor_data
    except Exception as e:
        print(
            f"An error occurred while requesting guarantor NIN/BVN verification and guarantor face match: {e}"
        )

    print("PROCESSED LOAN KYC DOCUMENTATION")


@shared_task
def update_and_settle_unsettled_repayment_position_with_false_response():
    error = {"status": False, "message": "it didn't go through, try again"}

    unsettled_repayment = AjoLoanRepayment.objects.filter(
        repayment_type=RepaymentType.AGENT_DEBIT,
        settlement_status=False,
        repayment_meta_response__isnull=False,
    )
    for repayment in unsettled_repayment:
        if repayment.repayment_meta_response == str(error):
            try:
                AjoLoanRepayment.settle_repayment_status(repayment=repayment)
            except Exception as err:
                return f"encountered error: {err}"

        else:
            pass

    return "Done"


@shared_task
def update_and_settle_unsettled_None_repayment_position_with_None_response():

    unsettled_repayment = AjoLoanRepayment.objects.filter(
        repayment_type=RepaymentType.AGENT_DEBIT, settlement_status=False
    )
    for repayment in unsettled_repayment:
        if repayment.repayment_meta_response is None:
            try:
                AjoLoanRepayment.settle_repayment_status(repayment=repayment)
            except Exception as err:
                return f"encountered error: {err}"

    return "Done"


@shared_task
def send_mail_to_loan_officer_supervisor_to_approve_loan(loan_instance):
    notification = (
        AjoLoan.email_notification_for_loan_officer_supervisor_to_approve_borrower_loan(
            loan_instance=loan_instance
        )
    )
    return notification


@shared_task
def temp_task_to_update_loan_status_on_loandisk(loan_id):
    """
    THIS IS A TEMPORARY TASK TO UPDATE LOAN STATUS ON LOANDISK
    IT IS TO BE USED
     ONCE
    """
    BRANCH_LOCATION_CODES = (
        ConstantTable.get_constant_table_instance().branch_location_codes
    )

    loan = AjoLoan.objects.get(id=loan_id)

    list_of_processing_loan_status = [
        "PENDING",
        "IN_PROGRESS",
        "PROCESSING",
        "OPEN_TO_SUPERVISOR",
        "DECLINED_BY_SUPERVISOR",
        "APPROVED_BY_SUPERVISOR",
    ]
    list_of_credit_bureau_loan_status = ["AWAITING_CRC_VERIFICATION"]
    list_of_open_loans = ["OPEN", "COMPLETED", "DEFAULTED", "APPROVED"]
    list_of_failed_loans = ["REJECTED", "DENIED"]

    if loan.status in list_of_open_loans:
        loandisk_loan_status = 1
    else:
        loandisk_loan_status = 8
        if loan.status in list_of_credit_bureau_loan_status:
            loandisk_loan_status = 366032
        elif loan.status in list_of_failed_loans:
            loandisk_loan_status = 17

    if loan.loandisk_loan_id == None:
        return run_post_loan_to_loan_disk(loan.id, loan_status_id=loandisk_loan_status)
        # instance.refresh_from_db()

    try:
        loan_tenure = Tenure.match_value_get_days(loan.tenor)
    except Exception:
        loan_tenure = 30

    loan_created_date = loan.created_at.date() + timedelta(days=4)
    first_repayment_date = loan_disk_date_format(loan_created_date)

    interest_rate = round((loan.interest_rate / loan_tenure), 2)

    borrower = loan.borrower
    loan_disk_borrower_id = borrower.loandisk_borrower_id
    loan_date = loan_disk_date_format(pass_date=loan.date_disbursed)

    borrower_bank_details = None

    try:
        borrower_bank_details: AjoUserWithdrawalAccount = borrower.withdrawal_account
    except ObjectDoesNotExist:
        pass

    if borrower_bank_details == None:
        account_number = ""
    else:
        account_number = borrower_bank_details.account_number

    ajo_loan_details = {
        "loan_id": loan.loan_ref,
        "loan_principal_amount": loan.amount,
        "loan_date": loan_date,
        "interest_amount": loan.interest_rate,
        "loandisk_loan_status": loandisk_loan_status,
        "loandisk_loan_id": loan.loandisk_loan_id,
        "loan_first_repayment_date": first_repayment_date,
        "loan_status_id": loandisk_loan_status,
        "loandisk_tenor_days": loan_tenure,
        "branch_loandisk_loan_id": loan.branch_loandisk_loan_id,
        "interest_rate": interest_rate,
        "ministry": loan.borrower.trade,
        "borrower_bank_details": account_number,
    }

    post_borrower_loan_disk_response = LoandiskManager().update_loan(
        loan_disk_borrower_id=loan_disk_borrower_id, ajo_loan_details=ajo_loan_details
    )
    metadata, created = LoanDiskMetaData.objects.get_or_create(loan_id=loan.id)
    metadata.main_branch_response = post_borrower_loan_disk_response
    metadata.save()

    branch_id = BRANCH_LOCATION_CODES.get(str(loan.borrower.branch_name).upper(), None)

    ajo_loan_details["branch_id"] = branch_id
    ajo_loan_details["loan_ref_2"] = f"id_{loan.id}{loan.loan_ref}"
    ajo_loan_details["branch_loandisk_loan_id"] = loan.branch_loandisk_loan_id

    branch_response = BranchLoanDiskManager().update_loan_on_branch(
        loan_disk_borrower_id=loan_disk_borrower_id, ajo_loan_details=ajo_loan_details
    )

    metadata.branch_response = branch_response
    metadata.save()

    metadata, created = LoanDiskMetaData.objects.get_or_create(loan_id=loan.id)
    metadata.main_branch_response = branch_response
    metadata.save()

    return {
        "main_branch_response": post_borrower_loan_disk_response,
        "branch_response": branch_response,
        "ajo_loan_details": ajo_loan_details,
    }


def fetch_all_loans_on_loan_disk():
    import requests

    base_url = "https://api-main.loandisk.com"

    all_loans = f"{base_url}/5797/56713/loan/from/1/count/4000"

    headers = {
        "Authorization": f"Basic NZaVE3RXR8WM7dgX9HKC664TKabRhp6pxDEdG867",
        "Content-Type": "application/json",
    }

    response = requests.get(all_loans, headers=headers)

    try:
        response = response.json()
    except:
        response = response.text

    return response


def temp_delete_loans_on_loan_disk():

    import requests

    loandisk_loans = fetch_all_loans_on_loan_disk()

    if isinstance(loandisk_loans, dict):
        loans = loandisk_loans.get("response", {}).get("Results", {})[0]

        for loan in loans:
            loan_id = loan.get("loan_id")

            base_url = f"https://api-main.loandisk.com/5797/56713/loan/{loan_id}"

            headers = {
                "Authorization": f"Basic NZaVE3RXR8WM7dgX9HKC664TKabRhp6pxDEdG867",
                "Content-Type": "application/json",
            }

            try:
                requests.delete(base_url, headers=headers)
            except Exception as e:
                print("Error: ", e)


@shared_task
def main_temp_task_to_update_loan_status_on_loandisk():

    temp_delete_loans_on_loan_disk()

    AjoLoan.objects.all().update(branch_loandisk_loan_id=None, loandisk_loan_id=None)

    loans = AjoLoan.objects.filter(
        branch_loandisk_loan_id__isnull=True, loandisk_loan_id__isnull=True
    ).order_by("-created_at")
    for loan in loans:
        temp_task_to_update_loan_status_on_loandisk.delay(loan.id)


@shared_task
def update_agent_image_on_loan_kyc_doc(kyc_id):
    # To update agent image against comparison on documentation verification
    documentation_instance = LoanKYCDocumentation.objects.get(id=kyc_id)
    return documentation_instance.update_agent_image()


@shared_task
def correct_duplicated_account():
    acct_no = "**********"
    duplicate_account = BankAccountDetails.objects.filter(
        account_number=acct_no, form_type=AccountFormType.LOAN_REPAYMENT
    )
    for account in duplicate_account:
        ajo_user = account.ajo_user
        if ajo_user.phone_number == "***********":
            pass
        else:
            create_wallet_acct = CoreBankingManager().create_account(
                first_name=ajo_user.first_name,
                last_name=ajo_user.last_name,
                middle_name=ajo_user.alias,
                email="",
                phone=ajo_user.phone_number,
                bvn=ajo_user.bvn,
                date_of_birth=str(ajo_user.dob),
            )
            status_code = create_wallet_acct.get("status_code")
            users = []
            if status_code == 200:
                data = create_wallet_acct.get("data").get("account_details")
                account_number = data.get("account_number")
                acct_name = data.get("first_name") + " " + data.get("last_name")
                bank_code = data.get("bank_code")
                bank_name = data.get("bank_name")
                bvn = data.get("bvn")

                account.account_number = account_number
                account.account_name = acct_name
                account.payload = create_wallet_acct

                account.bank_code = bank_code
                account.bank_name = bank_name
                account.bvn = bvn
                account.save()
                users.append(ajo_user.phone_number)

        return users


@shared_task
def update_ajoloan_performance_status():
    # update all past dues date loans
    today = timezone.now()
    today_date = today.date()

    loan_qs = AjoLoan.objects.filter(status="OPEN")
    for loan_instance in loan_qs:
        loan_instance.update_status
    return f"{loan_qs.count()} Loan instances updated."


# 1. Use loans to get last documentation
# 2. Check borrowers repayment pattern
# 3. If last documentation is less than 60days
# 4. Create the docs excluding guarantor
# 5. At the stage of verifying the user otp we create documentation
@shared_task
def offset_past_maturity_with_escrow_balance():
    from accounts.models import ConstantTable

    performance_statuses = [
        LoanPerformanceStatus.DEFAULTED,
        LoanPerformanceStatus.LOST,
        LoanPerformanceStatus.PAST_MATURITY,
    ]
    defaulted_loans = AjoLoan.objects.filter(
        status=LoanStatus.OPEN,
        escrow_offset=False,
    )

    for loan in defaulted_loans:
        today = datetime.now().date()

        if loan.end_date:
            pass
        else:
            continue

        days_to_end = (loan.end_date - today).days
        missed_days = loan.outstanding_days
        loan_balance = loan.repayment_amount - loan.total_repayment
        ajo_escrow_wallet_balance = AjoUserSelector(
            ajo_user=loan.borrower
        ).get_loan_escrow_wallet_balance()
        escrow_to_bal_target = (
            ConstantTable.get_constant_table_instance().escrow_to_bal_target
        )

        bal_to_repayment_perc = (loan_balance / loan.repayment_amount) * 100
        bal_to_repayment_perc_bool = bal_to_repayment_perc <= escrow_to_bal_target

        if (
            (days_to_end < 15 and missed_days > 10)
            or loan.performance_status in performance_statuses
            or bal_to_repayment_perc_bool
            or days_to_end <= 0
        ):
            OffetPastMaturityWithEscrowLog.create_record(
                data={
                    "loan": loan,
                    "bal_to_repayment_perc_bool": bal_to_repayment_perc_bool,
                    "bal_to_repayment_perc": bal_to_repayment_perc,
                    "escrow_balance": ajo_escrow_wallet_balance,
                    "total_paid": loan.total_repayment,
                    "repayment_amount": loan.repayment_amount,
                    "loan_balance": loan_balance,
                    "missed_days": missed_days,
                    "days_to_end": days_to_end,
                }
            )
            process_loan_repayment_from_escrow_wallet(loan=loan)

    return "Completed"


@shared_task
def run_daily_aggregate_sweep_to_loans():

    try:
        today = timezone.localdate()

        transaction = Transaction.objects.filter(
            transaction_form_type=TransactionFormType.AJO_LOAN_DISBURSEMENT,
            wallet_type=WalletTypes.LOAN_DISBURSEMENT,
            date_created__date=today,
        )

        print("Number of transactions for today:", transaction.count())  # Debugging

        aggregated_amount = transaction.aggregate(total_amount=Sum("amount"))
        print("Aggregated amount:", aggregated_amount)  # Debugging

        total_amount = aggregated_amount["total_amount"]
        print("Total amount:", total_amount)  # Debugging

        if total_amount is None:
            raise ValueError("No transactions found for today")

        loan_access_token = loan_agent_login().get("access")
        savings_acct_user = (
            get_user_model()
            .objects.filter(email=settings.AGENCY_BANKING_USEREMAIL)
            .last()
        )

        debit_transaction = (
            TransactionService.dynamic_deduction_from_wallet_transaction(
                user=savings_acct_user,
                amount=total_amount,
                wallet_type=None,
                description="Savings to loans sweep",
                transaction_form_type=TransactionFormType.SAVINGS_TO_LOANS_SWEEP,
            )
        )

        loans_acct_user = (
            get_user_model()
            .objects.filter(email=f"{settings.LOAN_AGENCY_BANKING_USEREMAIL}")
            .last()
        )
        loans_phone_no = AgencyBankingClass.get_user_info(
            access_token=loan_access_token,
            user_id=loans_acct_user.customer_user_id,
        )
        phone_number = loans_phone_no.get("data").get("phone_number")
        daily_sweep_transfer = pay_withdrawal_and_handle_response(
            transaction_id=debit_transaction.id,
            amount=total_amount,
            phone_number=phone_number,
        )

        if daily_sweep_transfer.get("status") == True:
            return f"A sum of {total_amount} was sent to loans through daily sweep"
        else:
            raise ValueError(f"{daily_sweep_transfer} erroor")

    except Exception as err:
        return f"Encountered an error: {err}"


@shared_task
def create_loan_repayment_schedule(loan_id):
    loan = AjoLoan.objects.get(id=loan_id)
    loan.create_schedule()


@shared_task
def recreate_bulk_schedules(loan_ids, is_delete=False):
    for loan_id in loan_ids:
        try:
            loan = AjoLoan.objects.get(id=loan_id)
            # Extend the end date of the loan by 1 but let the new end date not be a holiday or a weekend
            if is_delete:
                new_end_date = loan.end_date - timedelta(days=1)
            else:
                new_end_date = loan.end_date + timedelta(days=1)
            while (
                new_end_date.weekday() in [5, 6]
                or Holiday.objects.filter(date=new_end_date, type="company").exists()
                or Holiday.objects.filter(
                    date=new_end_date, type="agent", agent=loan.agent
                ).exists()
                or Holiday.objects.filter(
                    date=new_end_date,
                    type="branch",
                    branch__name=loan.agent.user_branch,
                ).exists()
            ):
                if is_delete:
                    new_end_date -= timedelta(days=1)
                else:
                    new_end_date += timedelta(days=1)

            loan.end_date = new_end_date
            loan.save()
            loan.delete_schedules()
            loan.create_schedule()
            repayments = AjoLoanRepayment.objects.filter(ajo_loan=loan)

            for repayment in repayments:
                update_loan_repayment_schedules(loan_id, repayment.id)

            health_data = loan.check_health()
            loan.repayment_health_score = health_data.get("repayment_health_score")
            loan.timeliness_score = health_data.get("timeliness_score")
            loan.repayment_percent = health_data.get("repayment_percent")
            loan.save()

        except AjoLoan.DoesNotExist:
            # Log the error or handle it appropriately
            print(f"Loan with ID {loan_id} does not exist.")
        except Exception as e:
            # Handle other possible exceptions
            print(f"An error occurred while processing loan with ID {loan_id}: {e}")


@shared_task
def update_repayment_health():
    loans = AjoLoan.objects.all()
    for loan in loans:
        health_data = loan.check_health()
        loan.repayment_health_score = health_data.get("repayment_health_score")
        loan.timeliness_score = health_data.get("timeliness_score")
        loan.repayment_percent = health_data.get("repayment_percent")
        loan.save()


@shared_task
def update_loan_repayment_schedules(loan_id, repayment_id):
    loan = AjoLoan.objects.get(id=loan_id)
    repayment = AjoLoanRepayment.objects.get(id=repayment_id)
    schedules = loan.ajoloanschedule_set.exclude(fully_paid=True).order_by("due_date")
    last_schedule = schedules.last()

    amount = round(repayment.repayment_amount, 2)
    schedule_index = 0

    while amount > 0 and schedule_index < len(schedules):
        schedule = schedules[schedule_index]

        if amount >= schedule.due_amount:
            remaining_due = round(schedule.due_amount - schedule.paid_amount, 2)
            schedule.paid_amount = round(schedule.paid_amount + remaining_due, 2)
            amount = round(amount - remaining_due, 2)
            schedule.paid_date = repayment.paid_date
            if schedule.paid_amount >= schedule.due_amount:
                schedule.fully_paid = True
                schedule.fully_paid_date = repayment.paid_date
            schedule.late_days = (repayment.paid_date.date() - schedule.due_date).days
            schedule.is_late = schedule.late_days > 0
            schedule.save()
        else:
            remaining_due = round(schedule.due_amount - schedule.paid_amount, 2)
            if amount >= remaining_due:
                schedule.paid_amount = round(schedule.paid_amount + remaining_due, 2)
                amount = round(amount - remaining_due, 2)
            else:
                schedule.paid_amount += amount
                amount = 0
            schedule.paid_date = repayment.paid_date
            schedule.late_days = (repayment.paid_date.date() - schedule.due_date).days
            schedule.is_late = schedule.late_days > 0
            if schedule.paid_amount >= schedule.due_amount:
                schedule.fully_paid = True
                schedule.fully_paid_date = repayment.paid_date
            schedule.save()

        if schedule.id == last_schedule.id and amount > 0:
            last_schedule.refresh_from_db()
            last_schedule.paid_amount = round(last_schedule.paid_amount + amount, 2)
            if last_schedule.paid_amount >= last_schedule.due_amount:
                last_schedule.fully_paid = True
                last_schedule.fully_paid_date = repayment.paid_date
            last_schedule.save()

        schedule_index += 1

    today = timezone.now().date()
    remaining_schedules = loan.ajoloanschedule_set.filter(
        fully_paid_date__isnull=True, due_date__lte=today
    )
    remaining_schedules.update(
        late_days=ExpressionWrapper(
            ExtractDay(today - F("due_date")), output_field=IntegerField()
        )
    )


@shared_task
def account_balances_update():
    today = timezone.localdate()
    today_transaction = Transaction.objects.filter(
        transaction_form_type=TransactionFormType.DISBURSE_TO_EXTERNAL_ACCOUNT,
        wallet_type=WalletTypes.LOAN_DISBURSEMENT,
        status=Status.SUCCESS,
        date_created__date=today,
    )
    # TOTAL
    total_disbursement_today = today_transaction.aggregate(total_amount=Sum("amount"))
    total_disbursement_today = total_disbursement_today["total_amount"]
    if total_disbursement_today is None:
        total_disbursement_today = 0
    # print(total_disbursement_today)

    # all_time_disbursements
    alltime__disbursement_transaction = Transaction.objects.filter(
        transaction_form_type=TransactionFormType.AJO_LOAN_DISBURSEMENT,
        status=Status.SUCCESS,
    )
    alltime_total_disbursement = alltime__disbursement_transaction.aggregate(
        total_amount=Sum("amount")
    )
    alltime__disbursement_amount = alltime_total_disbursement["total_amount"]
    # print(alltime__disbursement_amount)

    repayments = AjoLoanRepayment.objects.all()
    alltime_total_repayment = repayments.aggregate(total_amount=Sum("repayment_amount"))
    alltime_repayment = alltime_total_repayment["total_amount"]
    # print(alltime_repayment,)

    todays_repayments = AjoLoanRepayment.objects.filter(created_at__date=today)
    today_total_repayment = todays_repayments.aggregate(
        total_amount=Sum("repayment_amount")
    )
    todays_repayment = today_total_repayment["total_amount"] or 0

    commissions_transaction = Transaction.objects.filter(
        transaction_form_type=TransactionFormType.AJO_LOAN_CHARGE_FEE,
        wallet_type=WalletTypes.AJO_AGENT,
        status=Status.SUCCESS,
    )
    commissions_transaction = commissions_transaction.aggregate(
        total_amount=Sum("amount")
    )
    all_time_commissions = commissions_transaction["total_amount"]
    commissions_balance = check_balances(login_purpose="COMMISSION")
    escrow_balance = check_balances(login_purpose="ESCROW")
    repayment_balance = check_balances(login_purpose="REPAYMENT")
    savings_balance = check_balances(login_purpose="SAVINGS")
    loans_balance = check_balances(login_purpose="LOAN")
    # loans_balance = get_loan_balance()

    # # total_savings_without_Loans
    total_savings_without_Loans = WalletSystem.objects.exclude(
        wallet_type__in=[
            "AJO_LOAN",
            "PROSPER_LOAN_WALLET",
            "AJO_LOAN_ESCROW",
            "LOAN_DISBURSEMENT",
            "AJO_LOAN_REPAYMENT",
        ]
    ).aggregate(total=Sum("available_balance"))["total"]
    difference = savings_balance - total_savings_without_Loans

    # investment = InvestmentCapital.objects.all()
    # total_investment = investment.aggregate(total_amount=Sum("investment_amount"))
    # total_investment = total_investment["total_amount"]

    today_escrow_transaction = Transaction.objects.filter(
        transaction_form_type=TransactionFormType.AJO_LOAN_ESCROW_HOLDING,
        wallet_type=WalletTypes.AJO_LOAN_ESCROW,
        status=Status.SUCCESS,
    )
    total_escrow = today_escrow_transaction.aggregate(total_amount=Sum("amount"))
    all_time_escrow_balance = total_escrow["total_amount"]

    monnify_account_balance = Monnify().check_monnify_balance()
    monnify_account_balance = monnify_account_balance.get("responseBody").get(
        "availableBalance"
    )

    available_float = sum(
        [
            monnify_account_balance,
            loans_balance,
            commissions_balance,
            escrow_balance,
            repayment_balance,
        ]
    )
    # print(available_float)
    performance_statuses = [
        LoanPerformanceStatus.PERFORMING,
        LoanPerformanceStatus.DEFAULTED,
        LoanPerformanceStatus.LOST,
        LoanPerformanceStatus.PAST_MATURITY,
        LoanPerformanceStatus.OWED_BALANCE,
    ]
    all_loans = AjoLoan.objects.filter(
        status=LoanStatus.OPEN, performance_status__in=performance_statuses
    )
    total_loan_repayment = all_loans.aggregate(total_amount=Sum("repayment_amount"))
    total_loan_repayment = total_loan_repayment["total_amount"]
    total_paid_repayment = all_loans.aggregate(total_amount=Sum("total_paid_amount"))
    total_paid_repayment = total_paid_repayment["total_amount"]

    spend_wallet_balance = (
        WalletSystem.objects.filter(wallet_type=WalletTypes.AJO_SPENDING).aggregate(
            total_amount=Sum("available_balance")
        )["total_amount"]
        or 0.00
    )

    corebanking_repayment_amount = (
        todays_repayments.filter(
            repayment_type=RepaymentType.TRANSFER,
        ).aggregate(
            total_amount=Sum("repayment_amount")
        )["total_amount"]
        or 0.00
    )

    vfd_repayment_amount = (
        todays_repayments.filter(
            repayment_type=RepaymentType.AGENT_DEBIT,
        ).aggregate(
            total_amount=Sum("repayment_amount")
        )["total_amount"]
        or 0.00
    )

    # total_outstanding_repayment = total_loan_repayment - total_paid_repayment

    AccountBalances.objects.create(
        savings_account_balance=round(savings_balance, 2),
        total_savings_without_Loans=round(total_savings_without_Loans, 2),
        difference=round(difference, 2),
        total_disbursement_today=round(total_disbursement_today, 2),
        total_repayments_today=round(todays_repayment, 2),
        loans_account_balance=round(loans_balance, 2),
        commissions_account_balance=round(commissions_balance, 2),
        escrow_account_balance=round(escrow_balance, 2),
        repayment_account_balance=round(repayment_balance, 2),
        available_float=round(available_float, 2),
        monnify_account_balance=monnify_account_balance,
        spend_wallet_balance=round(spend_wallet_balance, 2),
        corebanking_repayment_amount=round(corebanking_repayment_amount, 2),
        vfd_repayment_amount=round(vfd_repayment_amount, 2),
    )

    # InvestmentCapital.objects.create(
    #     all_time_total_disbursements=alltime__disbursement_amount,
    #     all_time_total_repayments=alltime_repayment,
    #     all_time_escrow_balance=all_time_escrow_balance,
    #     all_time_commissions=all_time_commissions,
    #     total_outstanding_repayment=total_outstanding_repayment,
    # )
    # TotalInvestmentCapital.objects.create(total_investment=total_investment)

    return "Balance created"


@shared_task
def get_and_update_kyc_verification_status():
    kyc_doc_qs = LoanKYCDocumentation.objects.filter(
        documentation_status=DocumentationStatus.PROCESSING
    )
    update_count = 0
    for kyc_instance in kyc_doc_qs:
        if kyc_instance.minutes_since_processing_started > 5:
            # request documentation update
            docapi_obj = DocumentationApiHandler()
            request_response = docapi_obj.get_verification_status(
                verification_id=kyc_instance.unique_ref
            )
            # print(request_response.status_code)
            if request_response.status_code == 201:
                json_res = request_response.json()

                LoanKYCDocumentation.update_verification_status(
                    documentation_instance=kyc_instance, verification_result=json_res
                )
                update_count += 1

    return f"UPDATED {update_count} kyc instances.."


@shared_task
def get_and_update_failed_kyc_documentation_status():
    kyc_doc_qs = LoanKYCDocumentation.objects.filter(
        documentation_status=DocumentationStatus.FAILED
    )
    update_count = 0
    for kyc_instance in kyc_doc_qs:
        # request documentation update
        docapi_obj = DocumentationApiHandler()
        request_response = docapi_obj.get_verification_status(
            verification_id=kyc_instance.unique_ref
        )

        if request_response.status_code == 201:
            json_res = request_response.json()

            LoanKYCDocumentation.update_verification_status(
                documentation_instance=kyc_instance, verification_result=json_res
            )
            update_count += 1

    return f"UPDATED {update_count} kyc instances.."


@shared_task
def update_existing_bureau_status():
    crd_qs = CreditBureauMetaData.objects.filter(is_active=True).distinct(
        "verification_id"
    )

    for q in crd_qs:

        one_month_ago = timezone.now() - timedelta(days=30)
        date_object = one_month_ago.date()
        verification_id = q.verification_id
        # print(verification_id)
        bureau_qs = CreditBureauMetaData.objects.filter(
            verification_id=verification_id,
            status="SUCCESS",
            created_at__date__gte=date_object,
        )
        if bureau_qs.exists():
            bureau_qs.update(is_active=False)
            bureau_instance = bureau_qs.last()
            bureau_instance.is_active = True
            bureau_instance.save()
        else:
            bureau_qs = CreditBureauMetaData.objects.filter(
                verification_id=verification_id
            ).update(is_active=False)

    return f"updated {crd_qs.count()}"


@shared_task
def localhost_documentation_verification(kyc_doc_id):
    kyc_instance = LoanKYCDocumentation.objects.get(id=kyc_doc_id)
    kyc_instance.run_localhost_verifications()
    return f"Successfully processed dcocumentation"


@shared_task
def retry_documentation_verification():
    """
    A Celery task that retries the verification process for all `LoanKYCDocumentation` instances
    with a status of "PROCESSING".

    This task:
    1. Filters the `LoanKYCDocumentation` queryset to retrieve all records with the
       `documentation_status` set to `DocumentationStatus.PROCESSING`.
    2. For each KYC instance in the filtered queryset, it calls the `run_localhost_verifications()`
       method to re-attempt the verification process.
    3. After processing all relevant instances, it returns a success message indicating
       that the documentation was successfully processed.

    Returns:
        str: A success message indicating that the documentation processing was completed.
    """

    # Fetch all LoanKYCDocumentation instances where the status is 'PROCESSING'
    query_set = LoanKYCDocumentation.objects.filter(
        documentation_status=DocumentationStatus.PROCESSING
    )

    # Loop through each KYC instance and run the verification process
    update_count = 0
    for kyc_instance in query_set:
        kyc_minutes_since_last_update = kyc_instance.minutes_since_last_update()

        if kyc_minutes_since_last_update >= 5:
            kyc_instance.run_localhost_verifications()
            update_count += 1

    return f"Total update count: {update_count}"


@shared_task
def settle_repayment_for_invalid_savings_token():
    specific_datetime = datetime(2024, 5, 13, 16, 27)

    error = {
        "status": False,
        "data": "max recursion depth reached. token is invalid",
        "response": {
            "detail": "Given token not valid for any token type",
            "code": "token_not_valid",
            "messages": [
                {
                    "token_class": "AccessToken",
                    "token_type": "access",
                    "message": "Token is invalid or expired",
                }
            ],
        },
    }

    unsettled_repayment = AjoLoanRepayment.objects.filter(
        repayment_type=RepaymentType.AGENT_DEBIT,
        settlement_status=False,
        repayment_meta_response__isnull=False,
        created_at__gte=specific_datetime,
    )
    # print(unsettled_repayment, "ttttttttttttt")
    for repayment in unsettled_repayment:
        if repayment.repayment_meta_response == str(error):
            try:
                AjoLoanRepayment.settle_repayment_status(repayment=repayment)
            except Exception as err:
                return f"encountered error: {err}"

    return "Repayment Settled"


@shared_task
def settled_repayments_for_failed_duplicate():
    specific_datetime = datetime(2024, 5, 13, 16, 27)
    error = {
        "data": {
            "error": "143",
            "message": "Duplicate Transaction. Please try again after 5 minutes!",
        },
        "status_code": 403,
        "status": False,
    }
    unsettled_repayment = AjoLoanRepayment.objects.filter(
        repayment_type=RepaymentType.AGENT_DEBIT,
        settlement_status=False,
        repayment_meta_response__isnull=False,
        created_at__gte=specific_datetime,
    )
    for repayment in unsettled_repayment:
        if repayment.repayment_meta_response == str(error):
            try:
                AjoLoanRepayment.settle_repayment_status(repayment=repayment)
            except Exception as err:
                return f"encountered error: {err}"

    return "Repayment Settled"


@shared_task
def run_all_loan_supervisors_weekly_report():
    # Define Dates
    week_start = DateUtility().week_start
    today = DateUtility().today_date
    # today = timezone.now()

    supervisors_list = AgencyBankingClass.get_agency_supervisor_details()

    # Write queries
    # loan_officers_qs = User.objects.filter(user_type="STAFF_AGENT")
    ajo_loans_qs = AjoLoan.objects.filter()
    ajo_loan_repayment_qs = AjoLoanRepayment.objects.all()

    ajo_savings_transactions_qs = Transaction.objects.filter(
        plan_type="AJO",
        wallet_type="AJO_USER",
        status="SUCCESS",
        transaction_form_type="WALLET_DEPOSIT",
    )
    ajo_savings_table_qs = AjoSaving.objects.all()

    if "success" in supervisors_list and supervisors_list.get("success") == True:
        teams = supervisors_list.get("supervisors_teams_list")
    else:
        teams = []

    supervisors_report_list = []
    for team in teams:
        supervisor_name = team.get("supervisor")
        supervisor_email = team.get("supervisor_email")
        supervisor_user_id = int(team.get("supervisor_user_id"))
        loan_officers = team.get("users_list")
        branch = team.get("branch")
        supervisor = User.objects.filter(customer_user_id=supervisor_user_id).last()
        number_of_loan_officers = len(loan_officers)

        loans_disbursed_by_supervisor_qs = ajo_loans_qs.filter(
            agent__customer_user_id__in=loan_officers,
            status__in=disbursed_loans_status_list,
        )
        loans_disbursed_by_supervisor_week_qs = loans_disbursed_by_supervisor_qs.filter(
            created_at__gte=week_start
        )
        supervisor_loans_repayment_qs = ajo_loan_repayment_qs.filter(
            agent__customer_user_id__in=loan_officers
        )
        previous_day_supervisor_repayment_qs = ajo_loan_repayment_qs.filter(
            agent__customer_user_id__in=loan_officers, paid_date__gte=week_start
        )
        supervisor_savings_qs = ajo_savings_transactions_qs.filter(
            user__customer_user_id__in=loan_officers
        )
        supervisor_week_savings_qs = supervisor_savings_qs.filter(
            date_created__gte=week_start
        )
        supervisor_ajo_savings_table_qs = ajo_savings_table_qs.filter(
            user__customer_user_id__in=loan_officers
        )

        # Loanssupervisorursed
        supervisor_total_loan_disbursed_amount = list(
            loans_disbursed_by_supervisor_qs.aggregate(Sum("amount")).values()
        )[0]

        supervisor_total_loan_disbursed_count = list(
            loans_disbursed_by_supervisor_qs.aggregate(Count("amount")).values()
        )[0]

        supervisor_total_loan_disbursed_amount_week = list(
            loans_disbursed_by_supervisor_week_qs.aggregate(Sum("amount")).values()
        )[0]

        supervisor_total_loan_disbursed_count_week = list(
            loans_disbursed_by_supervisor_week_qs.aggregate(Count("amount")).values()
        )[0]

        supervisor_total_loan_disbursed_amount = (
            supervisor_total_loan_disbursed_amount
            if supervisor_total_loan_disbursed_amount
            else 0.00
        )

        supervisor_total_loan_disbursed_count = (
            supervisor_total_loan_disbursed_count
            if supervisor_total_loan_disbursed_count
            else 0
        )

        supervisor_total_loan_disbursed_amount_week = (
            supervisor_total_loan_disbursed_amount_week
            if supervisor_total_loan_disbursed_amount_week
            else 0.00
        )

        supervisor_total_loan_disbursed_count_week = (
            supervisor_total_loan_disbursed_count_week
            if supervisor_total_loan_disbursed_count_week
            else 0
        )

        # Loan Repayments
        supervisor_total_loan_repayment_amount = list(
            supervisor_loans_repayment_qs.aggregate(Sum("repayment_amount")).values()
        )[0]

        supervisor_total_loan_repayment_count = list(
            supervisor_loans_repayment_qs.aggregate(Count("repayment_amount")).values()
        )[0]

        supervisor_total_loan_repayment_amount_week = list(
            previous_day_supervisor_repayment_qs.aggregate(
                Sum("repayment_amount")
            ).values()
        )[0]

        supervisor_total_loan_repayment_count_week = list(
            previous_day_supervisor_repayment_qs.aggregate(
                Count("repayment_amount")
            ).values()
        )[0]

        expected_repayment_amount_week = list(
            loans_disbursed_by_supervisor_qs.filter(created_at__lt=week_start)
            .aggregate(Count("daily_repayment_amount"))
            .values()
        )[0]

        overall_expected_repayment_amount = list(
            loans_disbursed_by_supervisor_qs.filter(created_at__lt=today)
            .aggregate(Sum("repayment_amount"))
            .values()
        )[0]

        expected_repayment_amount_week = (
            expected_repayment_amount_week if expected_repayment_amount_week else 0.00
        )

        supervisor_total_loan_repayment_amount = (
            supervisor_total_loan_repayment_amount
            if supervisor_total_loan_repayment_amount
            else 0.00
        )
        supervisor_total_loan_repayment_count = (
            supervisor_total_loan_repayment_count
            if supervisor_total_loan_repayment_count
            else 0
        )
        supervisor_total_loan_repayment_amount_week = (
            supervisor_total_loan_repayment_amount_week
            if supervisor_total_loan_repayment_amount_week
            else 0.00
        )
        supervisor_total_loan_repayment_count_week = (
            supervisor_total_loan_repayment_count_week
            if supervisor_total_loan_repayment_count_week
            else 0
        )

        # Due Repayment Amount Yesterday
        due_loans_qs = ajo_loans_qs.filter(
            agent__customer_user_id__in=loan_officers,
            start_date__lte=timezone.now().date(),
        )

        loans_due_amount = 0
        for loan in due_loans_qs:
            days_due = (timezone.now().date() - loan.start_date).days
            amount_due = days_due * loan.daily_repayment_amount
            balance = amount_due - loan.total_paid_amount
            loans_due_amount += balance

        # Savings
        supervisor_total_savings_amount = list(
            supervisor_savings_qs.aggregate(Sum("amount")).values()
        )[0]
        supervisor_total_active_savings_amount = list(
            supervisor_ajo_savings_table_qs.filter(is_active=True)
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]
        supervisor_total_savings_count = list(
            supervisor_savings_qs.aggregate(Count("amount")).values()
        )[0]
        supervisor_total_savings_amount_week = list(
            supervisor_week_savings_qs.aggregate(Sum("amount")).values()
        )[0]
        supervisor_total_savings_count_week = list(
            supervisor_week_savings_qs.aggregate(Count("amount")).values()
        )[0]

        supervisor_total_savings_amount = (
            supervisor_total_savings_amount if supervisor_total_savings_amount else 0.00
        )
        supervisor_total_active_savings_amount = (
            supervisor_total_active_savings_amount
            if supervisor_total_active_savings_amount
            else 0.00
        )
        supervisor_total_savings_count = (
            supervisor_total_savings_count if supervisor_total_savings_count else 0
        )
        supervisor_total_savings_amount_week = (
            supervisor_total_savings_amount_week
            if supervisor_total_savings_amount_week
            else 0.00
        )
        supervisor_total_savings_count_week = (
            supervisor_total_savings_count_week
            if supervisor_total_savings_count_week
            else 0
        )
        expected_repayment_amount_week = (
            expected_repayment_amount_week if expected_repayment_amount_week else 0.00
        )
        overall_expected_repayment_amount = (
            overall_expected_repayment_amount
            if overall_expected_repayment_amount
            else 0.00
        )

        outstanding_repayment_amount_week = (
            expected_repayment_amount_week - supervisor_total_loan_repayment_amount_week
        )
        overall_outstanding_repayment_amount = (
            overall_expected_repayment_amount - supervisor_total_loan_repayment_amount
        )

        supervisor_repayment_vs_expected_percentage_week = get_percentages(
            supervisor_total_loan_repayment_amount_week, loans_due_amount
        )
        overall_supervisor_repayment_vs_expected_percentage = get_percentages(
            supervisor_total_loan_repayment_amount, overall_expected_repayment_amount
        )

        # Extra data from Vico
        transactions_count = list(
            ajo_savings_transactions_qs.filter(
                user__customer_user_id__in=loan_officers, transaction_type="CREDIT"
            )
            .aggregate(Count("amount"))
            .values()
        )[0]

        total_login_count = 0
        for officer in loan_officers:
            login_counts = AgencyBankingClass.get_agency_user_details(
                user_id=officer, start_date=week_start
            )

            if "success" in login_counts and login_counts.get("success") == True:
                login_count = login_counts.get("login_count")
            else:
                login_count = 45
            total_login_count += login_count

        avg_weekly_savings = 187500 * number_of_loan_officers
        avg_weekly_trnx_count = 38 * number_of_loan_officers
        avg_Weekly_loan = 750000 * number_of_loan_officers

        if supervisor:
            supervisor_age_days = (
                datetime.now().date() - supervisor.created_at.date()
            ).days
        else:
            supervisor_age_days = 0
        savings_performance = round(
            (supervisor_total_savings_amount_week / avg_weekly_savings) * 100
        )
        loans_performance = round(
            (supervisor_total_loan_disbursed_amount_week / avg_Weekly_loan) * 100
        )
        overall_performance = round((savings_performance + loans_performance) / 2)

        # Set Activity
        if supervisor_age_days > 30 and total_login_count > 40:
            activity = "Active Old"
        elif supervisor_age_days <= 30 and total_login_count > 20:
            activity = "Active New"
        elif supervisor_age_days <= 30 and total_login_count < 20:
            activity = "Inactive New"
        else:
            activity = "Inactive Old"

        # Set Churn
        if (
            activity == "Active Old"
            and supervisor_total_savings_count_week < (number_of_loan_officers * 1)
            and total_login_count < (10 * number_of_loan_officers)
        ):
            churn = "Churned"
        else:
            churn = "Not Churned"

        data = {
            "Supervisor Email": supervisor_email,
            "Supervisor Name": supervisor_name,
            "Branch": branch,
            "Number of Loan Officers": number_of_loan_officers,
            "Total Disbursed Amount": supervisor_total_loan_disbursed_amount,
            "Total Disbursed Count": supervisor_total_loan_disbursed_count,
            "Total Disbursed Amount Week": supervisor_total_loan_disbursed_amount_week,
            "Total Disbursed Count Week": supervisor_total_loan_disbursed_count_week,
            "Overall Expected Repayment Amount": overall_expected_repayment_amount,
            "Total Repayment Amount": supervisor_total_loan_repayment_amount,
            "Total Repayment Count": supervisor_total_loan_repayment_count,
            "Total Repayment Amount Week": supervisor_total_loan_repayment_amount_week,
            "Total Repayment Count Week": supervisor_total_loan_repayment_count_week,
            "Due Repayment Amount Week": loans_due_amount,
            "Loan Balance Amount": overall_outstanding_repayment_amount,
            "Repayment vs Expected Percentage Week": f"{round(supervisor_repayment_vs_expected_percentage_week)}%",
            "Overall Repayment vs Expected Percentage": f"{round(overall_supervisor_repayment_vs_expected_percentage)}%",
            "Total Savings Amount": supervisor_total_savings_amount,
            "Total Savings Count": supervisor_total_savings_count,
            "Total Active Savings Amount": supervisor_total_active_savings_amount,
            "Total Savings Amount Week": supervisor_total_savings_amount_week,
            "Total Savings Count Week": supervisor_total_savings_count_week,
            # Vico extra
            "Transaction Count": (transactions_count if transactions_count else 0),
            "Savings Performance": f"{savings_performance}%",
            "Loans Performance": f"{loans_performance}%",
            "Total Login Counts": total_login_count,
            "Churned": churn,
            "Activity": activity,
            "Overall Performance": f"{overall_performance}%",
        }
        supervisors_report_list.append(data)
    else:
        pass

    if supervisors_report_list:
        # Generate and share report
        df = pd.DataFrame().from_dict(supervisors_report_list)
        file_path = os.path.join(settings.BASE_DIR, "media/loan_officers")

        try:
            os.mkdir(file_path)
        except:
            pass

        excel_report = df.to_excel(
            f"{file_path}/loans_supervisors_weekly_performance_{datetime.now().date()}.xlsx"
        )

        with open(
            f"{file_path}/loans_supervisors_weekly_performance_{datetime.now().date()}.xlsx",
            "rb",
        ) as read_file:
            excel_file = read_file.read()

        for email in admin_email_list:
            send_email = send_ajo_loan_officers_daily_report_email(
                message=f"This is the report for loans supervisors in the past week",
                file=excel_file,
                file_name=f"Loans Supervisors Weekly Report_{datetime.now().date()}.xlsx",
                email_subject="Loans Supervisors Weekly Performance Report",
                email=email,
                name="Team",
                date=datetime.now(),
            )
    else:
        pass

    return "DONE!!!"


@shared_task
def run_all_loan_branches_weekly_report():
    # Define Dates
    week_start = DateUtility().week_start
    today = DateUtility().today_date

    supervisors_list = AgencyBankingClass.get_agency_supervisor_details()

    # Write queries
    # loan_officers_qs = User.objects.filter(user_type="STAFF_AGENT")
    ajo_loans_qs = AjoLoan.objects.filter()
    ajo_loan_repayment_qs = AjoLoanRepayment.objects.all()

    ajo_savings_transactions_qs = Transaction.objects.filter(
        plan_type="AJO",
        wallet_type="AJO_USER",
        status="SUCCESS",
        transaction_form_type="WALLET_DEPOSIT",
    )
    ajo_savings_table_qs = AjoSaving.objects.all()

    if "success" in supervisors_list and supervisors_list.get("success") == True:
        teams = supervisors_list.get("supervisors_teams_list")
    else:
        teams = []

    branch_dict = {}
    for team in teams:
        supervisor_user_id = int(team.get("supervisor_user_id"))
        loan_officers = team.get("users_list")
        branch = team.get("branch")

        if branch in branch_dict:
            existing_loan_officers = branch_dict[f"{branch}"].get("loan_officers")
            updated_loan_officers_list = existing_loan_officers + loan_officers
            supervisors_count = branch_dict[f"{branch}"].get("supervisors_count")
            supervisors_count += 1

            branch_dict[f"{branch}"]["loan_officers"] = updated_loan_officers_list
            branch_dict[f"{branch}"]["supervisors_count"] = supervisors_count
        else:
            branch_dict[f"{branch}"] = {}
            branch_dict[f"{branch}"] = {}
            branch_dict[f"{branch}"]["loan_officers"] = loan_officers
            branch_dict[f"{branch}"]["supervisors_count"] = 1

    branch_report_list = []
    for branch in branch_dict:
        loan_officers = branch_dict.get(branch).get("loan_officers")
        number_of_loan_supervisors = branch_dict.get(branch).get("supervisors_count")
        number_of_loan_officers = len(loan_officers)

        loans_disbursed_by_branch_qs = ajo_loans_qs.filter(
            agent__customer_user_id__in=loan_officers,
            status__in=disbursed_loans_status_list,
        )
        loans_disbursed_by_branch_week_qs = loans_disbursed_by_branch_qs.filter(
            created_at__gte=week_start
        )
        branch_loans_repayment_qs = ajo_loan_repayment_qs.filter(
            agent__customer_user_id__in=loan_officers
        )
        previous_day_branch_repayment_qs = ajo_loan_repayment_qs.filter(
            agent__customer_user_id__in=loan_officers, paid_date__gte=week_start
        )
        branch_savings_qs = ajo_savings_transactions_qs.filter(
            user__customer_user_id__in=loan_officers
        )
        branch_week_savings_qs = branch_savings_qs.filter(date_created__gte=week_start)
        branch_ajo_savings_table_qs = ajo_savings_table_qs.filter(
            user__customer_user_id__in=loan_officers
        )

        # Loansbranchursed
        branch_total_loan_disbursed_amount = list(
            loans_disbursed_by_branch_qs.aggregate(Sum("amount")).values()
        )[0]

        branch_total_loan_disbursed_count = list(
            loans_disbursed_by_branch_qs.aggregate(Count("amount")).values()
        )[0]

        branch_total_loan_disbursed_amount_week = list(
            loans_disbursed_by_branch_week_qs.aggregate(Sum("amount")).values()
        )[0]

        branch_total_loan_disbursed_count_week = list(
            loans_disbursed_by_branch_week_qs.aggregate(Count("amount")).values()
        )[0]

        branch_total_loan_disbursed_amount = (
            branch_total_loan_disbursed_amount
            if branch_total_loan_disbursed_amount
            else 0.00
        )

        branch_total_loan_disbursed_count = (
            branch_total_loan_disbursed_count
            if branch_total_loan_disbursed_count
            else 0
        )

        branch_total_loan_disbursed_amount_week = (
            branch_total_loan_disbursed_amount_week
            if branch_total_loan_disbursed_amount_week
            else 0.00
        )

        branch_total_loan_disbursed_count_week = (
            branch_total_loan_disbursed_count_week
            if branch_total_loan_disbursed_count_week
            else 0
        )

        # Loan Repayments
        branch_total_loan_repayment_amount = list(
            branch_loans_repayment_qs.aggregate(Sum("repayment_amount")).values()
        )[0]

        branch_total_loan_repayment_count = list(
            branch_loans_repayment_qs.aggregate(Count("repayment_amount")).values()
        )[0]

        branch_total_loan_repayment_amount_week = list(
            previous_day_branch_repayment_qs.aggregate(Sum("repayment_amount")).values()
        )[0]

        branch_total_loan_repayment_count_week = list(
            previous_day_branch_repayment_qs.aggregate(
                Count("repayment_amount")
            ).values()
        )[0]

        expected_repayment_amount_week = list(
            loans_disbursed_by_branch_qs.filter(created_at__lt=week_start)
            .aggregate(Count("daily_repayment_amount"))
            .values()
        )[0]

        overall_expected_repayment_amount = list(
            loans_disbursed_by_branch_qs.filter(created_at__lt=today)
            .aggregate(Sum("repayment_amount"))
            .values()
        )[0]

        expected_repayment_amount_week = (
            expected_repayment_amount_week if expected_repayment_amount_week else 0.00
        )

        branch_total_loan_repayment_amount = (
            branch_total_loan_repayment_amount
            if branch_total_loan_repayment_amount
            else 0.00
        )
        branch_total_loan_repayment_count = (
            branch_total_loan_repayment_count
            if branch_total_loan_repayment_count
            else 0
        )
        branch_total_loan_repayment_amount_week = (
            branch_total_loan_repayment_amount_week
            if branch_total_loan_repayment_amount_week
            else 0.00
        )
        branch_total_loan_repayment_count_week = (
            branch_total_loan_repayment_count_week
            if branch_total_loan_repayment_count_week
            else 0
        )

        # Due Repayment Amount Yesterday
        due_loans_qs = ajo_loans_qs.filter(
            agent__customer_user_id__in=loan_officers,
            start_date__lte=timezone.now().date(),
        )

        loans_due_amount = 0
        for loan in due_loans_qs:
            days_due = (timezone.now().date() - loan.start_date).days
            amount_due = days_due * loan.daily_repayment_amount
            balance = amount_due - loan.total_paid_amount
            loans_due_amount += balance

        # Savings
        branch_total_savings_amount = list(
            branch_savings_qs.aggregate(Sum("amount")).values()
        )[0]
        branch_total_active_savings_amount = list(
            branch_ajo_savings_table_qs.filter(is_active=True)
            .aggregate(Sum("amount_saved"))
            .values()
        )[0]
        branch_total_savings_count = list(
            branch_savings_qs.aggregate(Count("amount")).values()
        )[0]
        branch_total_savings_amount_week = list(
            branch_week_savings_qs.aggregate(Sum("amount")).values()
        )[0]
        branch_total_savings_count_week = list(
            branch_week_savings_qs.aggregate(Count("amount")).values()
        )[0]

        branch_total_savings_amount = (
            branch_total_savings_amount if branch_total_savings_amount else 0.00
        )
        branch_total_active_savings_amount = (
            branch_total_active_savings_amount
            if branch_total_active_savings_amount
            else 0.00
        )
        branch_total_savings_count = (
            branch_total_savings_count if branch_total_savings_count else 0
        )
        branch_total_savings_amount_week = (
            branch_total_savings_amount_week
            if branch_total_savings_amount_week
            else 0.00
        )
        branch_total_savings_count_week = (
            branch_total_savings_count_week if branch_total_savings_count_week else 0
        )
        expected_repayment_amount_week = (
            expected_repayment_amount_week if expected_repayment_amount_week else 0.00
        )
        overall_expected_repayment_amount = (
            overall_expected_repayment_amount
            if overall_expected_repayment_amount
            else 0.00
        )

        outstanding_repayment_amount_week = (
            expected_repayment_amount_week - branch_total_loan_repayment_amount_week
        )
        overall_outstanding_repayment_amount = (
            overall_expected_repayment_amount - branch_total_loan_repayment_amount
        )

        branch_repayment_vs_expected_percentage_week = get_percentages(
            branch_total_loan_repayment_amount_week, loans_due_amount
        )
        overall_branch_repayment_vs_expected_percentage = get_percentages(
            branch_total_loan_repayment_amount, overall_expected_repayment_amount
        )

        # Extra data from Vico
        transactions_count = list(
            ajo_savings_transactions_qs.filter(
                user__customer_user_id__in=loan_officers, transaction_type="CREDIT"
            )
            .aggregate(Count("amount"))
            .values()
        )[0]

        total_login_count = 0
        for officer in loan_officers:
            login_counts = AgencyBankingClass.get_agency_user_details(
                user_id=officer, start_date=week_start
            )

            if "success" in login_counts and login_counts.get("success") == True:
                login_count = login_counts.get("login_count")
            else:
                login_count = 45
            total_login_count += login_count

        avg_weekly_savings = 187500 * number_of_loan_officers
        avg_weekly_trnx_count = 38 * number_of_loan_officers
        avg_Weekly_loan = 750000 * number_of_loan_officers

        # if branch:
        #     branch_age_days = (datetime.now().date() - branch.created_at.date()).days
        # else:
        #     branch_age_days = 0
        savings_performance = round(
            (branch_total_savings_amount_week / avg_weekly_savings) * 100
        )
        loans_performance = round(
            (branch_total_loan_disbursed_amount_week / avg_Weekly_loan) * 100
        )
        overall_performance = round((savings_performance + loans_performance) / 2)

        # Set Activity
        # if total_login_count > (40 * number_of_loan_officers):
        #     activity = "Active Old"
        # elif total_login_count > (20 * number_of_loan_officers):
        #     activity = "Active New"
        # elif total_login_count < (20 * number_of_loan_officers):
        #     activity = "Inactive New"
        # else:
        #     activity = "Inactive Old"

        # Set Churn
        if branch_total_savings_count_week < (
            number_of_loan_officers * 1
        ) and total_login_count < (10 * number_of_loan_officers):
            churn = "Churned"
        else:
            churn = "Not Churned"

        data = {
            "Branch": branch,
            "Number of Loan Officers": number_of_loan_officers,
            "Number of Loan Supervisors": number_of_loan_supervisors,
            "Total Disbursed Amount": branch_total_loan_disbursed_amount,
            "Total Disbursed Count": branch_total_loan_disbursed_count,
            "Total Disbursed Amount Week": branch_total_loan_disbursed_amount_week,
            "Total Disbursed Count Week": branch_total_loan_disbursed_count_week,
            "Overall Expected Repayment Amount": overall_expected_repayment_amount,
            "Total Repayment Amount": branch_total_loan_repayment_amount,
            "Total Repayment Count": branch_total_loan_repayment_count,
            "Total Repayment Amount Week": branch_total_loan_repayment_amount_week,
            "Total Repayment Count Week": branch_total_loan_repayment_count_week,
            "Due Repayment Amount Week": loans_due_amount,
            "Loan Balance Amount": overall_outstanding_repayment_amount,
            "Repayment vs Expected Percentage Week": f"{round(branch_repayment_vs_expected_percentage_week)}%",
            "Overall Repayment vs Expected Percentage": f"{round(overall_branch_repayment_vs_expected_percentage)}%",
            "Total Savings Amount": branch_total_savings_amount,
            "Total Savings Count": branch_total_savings_count,
            "Total Active Savings Amount": branch_total_active_savings_amount,
            "Total Savings Amount Week": branch_total_savings_amount_week,
            "Total Savings Count Week": branch_total_savings_count_week,
            # Vico extra
            "Transaction Count": (transactions_count if transactions_count else 0),
            "Savings Performance": f"{savings_performance}%",
            "Loans Performance": f"{loans_performance}%",
            "Total Login Counts": total_login_count,
            "Churned": churn,
            # "Activity": activity,
            "Overall Performance": f"{overall_performance}%",
        }
        branch_report_list.append(data)
    else:
        pass

    if branch_report_list:
        # Generate and share report
        df = pd.DataFrame().from_dict(branch_report_list)
        file_path = os.path.join(settings.BASE_DIR, "media/loan_officers")

        try:
            os.mkdir(file_path)
        except:
            pass

        excel_report = df.to_excel(
            f"{file_path}/loans_branches_weekly_performance_{datetime.now().date()}.xlsx"
        )

        with open(
            f"{file_path}/loans_branches_weekly_performance_{datetime.now().date()}.xlsx",
            "rb",
        ) as read_file:
            excel_file = read_file.read()

        for email in admin_email_list:
            send_email = send_ajo_loan_officers_daily_report_email(
                message=f"This is the report for loans branches in the past week",
                file=excel_file,
                file_name=f"Loans Branches Weekly Report_{datetime.now().date()}.xlsx",
                email_subject="Loans Branches Weekly Performance Report",
                email=email,
                name="Team",
                date=datetime.now(),
            )
    else:
        pass

    return "DONE!!!"


@shared_task
def run_send_branch_users_to_paybox():
    import json

    import requests

    from accounts.models import CustomUser
    from collections_app.helpers.helpers import (
        get_agent_supervisor_details,
        get_branch_loan_officers,
        get_loan_branches_list,
    )

    base_url = settings.PAYBOX_URL
    url = f"{base_url}/clock-app/add_employee_to_branch/?company_uuid=f92f464b-b10a-46b9-bfd2-74b6956e85e5"
    authorization = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {settings.PAYBOX_TOKEN}",
    }

    branches = get_loan_branches_list()

    branch_ids = {
        "festac": "c18f777e-20fe-4c19-ab65-523e9ee25b59",
        "alaba": "714ca5f1-8eb4-4e09-9b10-1e3760cacb7e",
        "ikeja": "0ecb3161-d36e-42d7-9e50-aaf9124b4dcd",
        "sango": "5eafb36d-ec8d-43b1-b901-f4e838be1c98",
        "ajah": "ee6511b8-a770-4f48-a208-9dde7f034beb",
        "mile 12": "7d856583-3dd3-4217-ab1b-e4eebc8794f7",
    }

    responses_list = []

    for branch in set(branches):
        branch_agents_emails_list = []
        branch_lower = branch.lower()
        try:
            branch_id = branch_ids[f"{branch_lower}"]
        except:
            continue
        agents_list = get_branch_loan_officers(query_branch=branch).get(
            "loan_officers", []
        )
        for agent in agents_list:
            agent_email_check = get_agent_supervisor_details(customer_user_id=agent)
            agent_email = agent_email_check.get("user_email", "")
            # print(agent_email_check)
            branch_agents_emails_list.append(agent_email)
        # print("::::::::::::::AGENTS LIST::::::::::::::")
        # print(branch_agents_emails_list)
        # print("::::::::::::::AGENTS LIST::::::::::::::")

        data = {"employees": branch_agents_emails_list, "branch_id": branch_id}

        resp = requests.request(
            "POST", headers=authorization, url=url, data=json.dumps(data)
        )
        responses_list.append(resp.text)
    return responses_list


def update_create_investment_record():

    today = timezone.localdate()
    alltime__disbursement_transaction = Transaction.objects.filter(
        transaction_form_type=TransactionFormType.AJO_LOAN_DISBURSEMENT,
        status=Status.SUCCESS,
    )
    alltime_total_disbursement = alltime__disbursement_transaction.aggregate(
        total_amount=Sum("amount")
    )
    alltime__disbursement_amount = alltime_total_disbursement["total_amount"]

    repayments = AjoLoanRepayment.objects.all()
    alltime_total_repayment = repayments.aggregate(total_amount=Sum("repayment_amount"))
    alltime_repayment = alltime_total_repayment["total_amount"]

    today_escrow_transaction = Transaction.objects.filter(
        transaction_form_type=TransactionFormType.AJO_LOAN_ESCROW_HOLDING,
        wallet_type=WalletTypes.AJO_LOAN_ESCROW,
        status=Status.SUCCESS,
    )
    total_escrow = today_escrow_transaction.aggregate(total_amount=Sum("amount"))
    all_time_escrow_balance = total_escrow["total_amount"]

    performance_statuses = [
        LoanPerformanceStatus.PERFORMING,
        LoanPerformanceStatus.DEFAULTED,
        LoanPerformanceStatus.LOST,
        LoanPerformanceStatus.PAST_MATURITY,
        LoanPerformanceStatus.OWED_BALANCE,
    ]

    all_loans = AjoLoan.objects.filter(
        status=LoanStatus.OPEN, performance_status__in=performance_statuses
    )
    total_loan_repayment = all_loans.aggregate(total_amount=Sum("repayment_amount"))
    total_loan_repayment = total_loan_repayment["total_amount"]
    total_paid_repayment = all_loans.aggregate(total_amount=Sum("total_paid_amount"))
    total_paid_repayment = total_paid_repayment["total_amount"]

    if total_loan_repayment is None:
        total_loan_repayment = 0
    if total_paid_repayment is None:
        total_paid_repayment = 0

    total_outstanding_repayment = total_loan_repayment - total_paid_repayment

    loans_balance = check_balances(login_purpose="LOAN")
    commissions_balance = check_balances(login_purpose="COMMISSION")
    escrow_balance = check_balances(login_purpose="ESCROW")
    repayment_balance = check_balances(login_purpose="REPAYMENT")
    savings_balance = check_balances(login_purpose="SAVINGS")

    today_transaction = Transaction.objects.filter(
        transaction_form_type=TransactionFormType.DISBURSE_TO_EXTERNAL_ACCOUNT,
        wallet_type=WalletTypes.LOAN_DISBURSEMENT,
        status=Status.SUCCESS,
        date_created__date=today,
    )
    total_disbursement_today = today_transaction.aggregate(total_amount=Sum("amount"))
    total_disbursement_today = total_disbursement_today["total_amount"]
    if total_disbursement_today is None:
        total_disbursement_today = 0

    total_savings_without_Loans = WalletSystem.objects.exclude(
        wallet_type__in=[
            "AJO_LOAN",
            "PROSPER_LOAN_WALLET",
            "AJO_LOAN_ESCROW",
            "LOAN_DISBURSEMENT",
            "AJO_LOAN_REPAYMENT",
        ]
    ).aggregate(total=Sum("available_balance"))["total"]

    todays_repayments = AjoLoanRepayment.objects.filter(created_at__date=today)
    today_total_repayment = todays_repayments.aggregate(
        total_amount=Sum("repayment_amount")
    )
    todays_repayment = today_total_repayment["total_amount"]
    if todays_repayment is None:
        todays_repayment = 0

    difference = savings_balance - total_savings_without_Loans
    available_float = sum(
        [loans_balance, commissions_balance, escrow_balance, repayment_balance]
    )
    commissions_transaction = Transaction.objects.filter(
        transaction_form_type=TransactionFormType.AJO_LOAN_CHARGE_FEE,
        wallet_type=WalletTypes.AJO_AGENT,
        status=Status.SUCCESS,
    )
    commissions_transaction = commissions_transaction.aggregate(
        total_amount=Sum("amount")
    )
    all_time_commissions = commissions_transaction["total_amount"]

    AccountBalances.objects.create(
        savings_account_balance=round(savings_balance, 2),
        total_savings_without_Loans=round(total_savings_without_Loans, 2),
        difference=round(difference, 2),
        total_disbursement_today=round(total_disbursement_today, 2),
        total_repayments_today=round(todays_repayment, 2),
        loans_account_balance=round(loans_balance),
        commissions_account_balance=round(commissions_balance, 2),
        escrow_account_balance=round(escrow_balance, 2),
        repayment_account_balance=round(repayment_balance, 2),
        available_float=round(available_float, 2),
        # monnify_account_balance=monnify_account_balance,
    )
    response = {
        "alltime__disbursement_amount": alltime__disbursement_amount,
        "alltime_repayment": alltime_repayment,
        "all_time_escrow_balance": all_time_escrow_balance,
        "total_outstanding_repayment": total_outstanding_repayment,
        "all_time_commissions": all_time_commissions,
    }
    # print(response)

    return response


@shared_task
def documentation_compliance_checks(loan_id: int):
    """
    perform documentation checks through a loan
    """
    try:
        loan = AjoLoan.objects.get(id=loan_id)
    except AjoLoan.DoesNotExist:
        return {
            "status": "failed",
            "message": f"loan object with ID, {loan_id}, does not exist",
        }

    ComplianceChecks(loan=loan).documentation_checks()

    return {
        "status": "success",
        "message": f"loan documentation checks done on loan with ID, {loan_id}",
    }


@shared_task
def disbursement_compliance_checks(
    loan_id: int | None = None,
    agent_id: int | None = None,
):
    """
    perform disbursement checks through a loan
    """
    if loan_id:
        try:
            loan = AjoLoan.objects.get(id=loan_id)
        except AjoLoan.DoesNotExist:
            return {
                "status": "failed",
                "message": f"loan object with ID, {loan_id}, does not exist",
            }

        ComplianceChecks(loan=loan).disbursement_checks()

    elif agent_id:
        try:
            agent = CustomUser.objects.get(id=agent_id)
        except CustomUser.DoesNotExist:
            return {
                "status": "failed",
                "message": f"agent object with ID, {agent_id}, does not exist",
            }

        ComplianceChecks(agent=agent).disbursement_checks()

    else:
        return {
            "status": "failed",
            "message": "please pass either agent_id or loan_id to this function",
        }

    return {
        "status": "success",
        "message": f"loan disbursement checks done on loan with ID, {loan_id}",
    }


@shared_task
def repayment_compliance_checks(loan_id: int):
    """
    perform repayment checks through a loan
    """
    try:
        loan = AjoLoan.objects.get(id=loan_id)
    except AjoLoan.DoesNotExist:
        return {
            "status": "failed",
            "message": f"loan object with ID, {loan_id}, does not exist",
        }

    ComplianceChecks(loan=loan).repayment_checks()

    return {
        "status": "success",
        "message": f"loan repayment checks done on loan with ID, {loan_id}",
    }


@shared_task
def categorize_loans(loan_id=None):
    open_loans = AjoLoan.objects.filter(status="OPEN")

    if loan_id:
        open_loans = open_loans.filter(id=loan_id)

    for loan in open_loans:
        outstanding_days = loan.outstanding_days_today

        # Add or update the loan in MissedRepaymentsTable if outstanding_days > 1
        if outstanding_days >= 1:
            create_or_update_missed_loan(loan, MissedRepaymentsTable)

        if outstanding_days >= 61:
            target_model = Missed90Days
            other_models = [Missed14Days, Missed28Days, Missed60Days]
        elif outstanding_days >= 29:
            target_model = Missed60Days
            other_models = [Missed14Days, Missed28Days, Missed90Days]
        elif outstanding_days >= 15:
            target_model = Missed28Days
            other_models = [Missed14Days, Missed60Days, Missed90Days]
        elif outstanding_days >= 1:
            target_model = Missed14Days
            other_models = [Missed28Days, Missed60Days, Missed90Days]
        else:
            # Loan with outstanding_days <= 0 should not be in any of the specific missed days tables
            target_model = None
            other_models = [
                Missed14Days,
                Missed28Days,
                Missed60Days,
                Missed90Days,
                MissedRepaymentsTable,
            ]

        # Remove the loan from other missed days tables
        for model in other_models:
            remove_missed_loan(loan, model)

        # Add or update the loan in the correct missed days table
        if target_model:
            create_or_update_missed_loan(loan, target_model)

    defaulting_loans = AjoLoan.objects.filter(
        performance_status__in=[
            LoanPerformanceStatus.PAST_MATURITY,
            LoanPerformanceStatus.LOST,
            LoanPerformanceStatus.DEFAULTED,
        ]
    ).exclude(status=LoanStatus.COMPLETED)

    for loan in defaulting_loans:

        defaulting_days = loan.days_after_end

        if defaulting_days >= 1:
            create_or_update_missed_loan(loan, MissedRepaymentsTable)

        if defaulting_days > 15:
            create_or_update_missed_loan(loan, MissedLost)
            remove_missed_loan(loan, MissedPastMaturity15Days)
        else:
            create_or_update_missed_loan(loan, MissedPastMaturity15Days)
            remove_missed_loan(loan, MissedLost)


@shared_task
def categorize_missed_loan(loan_id):

    try:
        loan = AjoLoan.objects.get(id=loan_id)
        loan.refresh_from_db()

        if loan.status == LoanStatus.OPEN:

            outstanding_days = loan.outstanding_days_today

            # Add or update the loan in MissedRepaymentsTable if outstanding_days > 1
            if outstanding_days > 1:
                create_or_update_missed_loan(loan, MissedRepaymentsTable)

            if outstanding_days >= 61:
                target_model = Missed90Days
                other_models = [Missed14Days, Missed28Days, Missed60Days]
            elif outstanding_days >= 29:
                target_model = Missed60Days
                other_models = [Missed14Days, Missed28Days, Missed90Days]
            elif outstanding_days >= 15:
                target_model = Missed28Days
                other_models = [Missed14Days, Missed60Days, Missed90Days]
            elif outstanding_days >= 1:
                target_model = Missed14Days
                other_models = [Missed28Days, Missed60Days, Missed90Days]
            else:
                # Loan with outstanding_days <= 0 should not be in any of the specific missed days tables
                target_model = None
                other_models = [
                    Missed14Days,
                    Missed28Days,
                    Missed60Days,
                    Missed90Days,
                    MissedRepaymentsTable,
                ]

            # Remove the loan from other missed days tables
            for model in other_models:
                remove_missed_loan(loan, model)

            # Add or update the loan in the correct missed days table
            if target_model:
                create_or_update_missed_loan(loan, target_model)

        elif (
            loan.performance_status
            in [LoanPerformanceStatus.PAST_MATURITY, LoanPerformanceStatus.LOST]
            and loan.status != LoanStatus.COMPLETED
        ):
            defaulting_days = loan.days_after_end

            if defaulting_days >= 1:
                create_or_update_missed_loan(loan, MissedRepaymentsTable)

            if defaulting_days > 15:
                create_or_update_missed_loan(loan, MissedLost)
                remove_missed_loan(loan, MissedPastMaturity15Days)
            else:
                create_or_update_missed_loan(loan, MissedPastMaturity15Days)
                remove_missed_loan(loan, MissedLost)

    except AjoLoan.DoesNotExist:
        pass


@shared_task
def update_open_loan_escrow_amount():
    loans = AjoLoan.objects.all()
    for loan in loans:
        if (
            loan.status == LoanStatus.OPEN
            or loan.status == LoanStatus.OPEN_TO_SUPERVISOR
        ):
            ajo_user_selector = AjoUserSelector(ajo_user=loan.borrower)
            escrow_balance = ajo_user_selector.get_loan_escrow_wallet_balance()
            loan.escrow_amount = escrow_balance
            loan.save()

    return "Done"


@shared_task
def update_supervisor_device_mgmt(device_mgmt_id):
    """
    This task updates the supervisor and branch information for a given ProductAssignment
    based on the current borrower's loan details.
    Args:
        device_mgmt_id (int): The ID of the ProductAssignment to update.
    Returns:
        str or dict: A message indicating the result of the operation, or the user details if successful.
    """
    try:
        # Fetch the ProductAssignment instance by ID
        dvc_mgmt = ProductAssignment.objects.get(id=device_mgmt_id)
    except ProductAssignment.DoesNotExist:
        # Return a message if the ProductAssignment does not exist
        return "Product assignment does not exist"

    # Get the current supervisor of the ProductAssignment
    supervisor = dvc_mgmt.supervisor

    if supervisor is None:
        # Get the user from the borrower's loan
        user = dvc_mgmt.borrower_loan.borrower.user
        user_id = user.customer_user_id
        # print(user_id)
        # print(user_id, "\n\n")
        # Get today's date as a date object
        today = datetime.today().date()

        # Fetch agency user details using the AgencyBankingClass
        user_details = AgencyBankingClass.get_agency_user_details(
            user_id=user_id, start_date=today
        )
        # print(user_details, "\n\n")
        if user_details.get("success"):
            # Extract supervisor email and branch from user details
            supervisor_email = user_details.get("supervisor_email")
            branch = user_details.get("branch")

            # Find the supervisor instance by email
            supervisor_instance = CustomUser.objects.filter(
                email=supervisor_email
            ).last()
            if supervisor_instance is not None:
                # Update the ProductAssignment with the new supervisor and branch
                dvc_mgmt.supervisor = supervisor_instance
                dvc_mgmt.branch = branch
                dvc_mgmt.save()
                return user_details
            else:
                # Return a message if the supervisor does not exist in the system
                return f"Supervisor-{supervisor_email} does not exist on savings back"
        else:
            # Return the user details if the operation was not successful
            return user_details
    else:
        # Return a message if the ProductAssignment already has a supervisor
        return "Supervisor is not None"


@shared_task
def fund_loan_account_from_escrow_via_buddy():
    """
    Transfer from escrow to loans
    """
    escrow_access_token = escrow_agent_login(token_not_valid=True).get("access")
    transaction_pin = settings.ESCROW_TRANSACTION_PIN
    transfer_limit = ConstantTable.get_constant_table_instance().internal_transfer_limit

    # Check Escrow account balance
    available_balance = check_balances(
        login_purpose="ESCROW", access_token=escrow_access_token
    )
    # available_balance = 5000
    if available_balance > transfer_limit:
        available_balance = transfer_limit

    # Get Escrow User
    escrow_user_email = settings.ESCROW_USER_EMAIL
    escrow_user = User.objects.filter(email=escrow_user_email).last()

    # Get Loans account Phone number
    loans_user_email = settings.LOAN_AGENCY_BANKING_USEREMAIL
    loans_user = User.objects.filter(email=loans_user_email).last()
    loans_phone_number = loans_user.user_phone

    transfer_transaction = TransactionService.create_internal_transfer_between_accounts(
        user=escrow_user,
        amount=available_balance,
        transaction_description="Transfer from escrow account to loans account",
        transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
        transaction_form_type=TransactionFormType.INTERNAL_TRANSFER_TO_LOANS,
        status=Status.PENDING,
    )
    reference = str(transfer_transaction.transaction_id)
    narration = "Transfer from escrow account to loans account"

    request_data = {
        "from_wallet_type": "COLLECTION",
        "to_wallet_type": "COLLECTION",
        "data": [
            {
                "buddy_phone_number": loans_phone_number,
                "amount": available_balance,
                "narration": narration,
                "is_beneficiary": "False",
                "save_beneficiary": "False",
                "remove_beneficiary": "False",
                "is_recurring": "False",
                "custom_reference": reference,
            }
        ],
        "transaction_pin": "",
    }

    send_money_to_loans = AgencyBankingClass.send_money_from_an_account_in_liberty_to_user_through_pay_buddy(
        transaction_pin=transaction_pin,
        phone_number=loans_phone_number,
        amount=available_balance,
        transaction_reference=reference,
        access_token=escrow_access_token,
        narration=narration,
    )

    if send_money_to_loans.get("data").get("message") == "success":
        transfer_transaction.status = Status.SUCCESS
    else:
        transfer_transaction.status = Status.FAILED

    transfer_transaction.request_data = request_data
    transfer_transaction.payload = send_money_to_loans
    transfer_transaction.save()

    return send_money_to_loans


@shared_task
def fund_loan_account_from_repayment_via_buddy():
    """
    Transfer from repayment to loans
    """
    repayment_access_token = repayment_agent_login(token_not_valid=True).get("access")
    transaction_pin = settings.REPAYMENT_TRANSACTION_PIN
    transfer_limit = (
        ConstantTable.get_constant_table_instance().upper_internal_transfer_limit
    )

    # Check repayment account balance
    available_balance = check_balances(
        login_purpose="REPAYMENT", access_token=repayment_access_token
    )

    if available_balance > transfer_limit:
        available_balance = transfer_limit
    # available_balance = 5000

    # Get repayment User
    repayment_user_email = settings.LOAN_REPAYMENT_USER_EMAIL
    repayment_user = User.objects.filter(email=repayment_user_email).last()

    # Get Loans account Phone number
    loans_user_email = settings.LOAN_AGENCY_BANKING_USEREMAIL
    loans_user = User.objects.filter(email=loans_user_email).last()
    loans_phone_number = loans_user.user_phone

    transfer_transaction = TransactionService.create_internal_transfer_between_accounts(
        user=repayment_user,
        amount=available_balance,
        transaction_description="Transfer from repayment account to loans account",
        transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
        transaction_form_type=TransactionFormType.INTERNAL_TRANSFER_TO_LOANS,
        status=Status.PENDING,
    )
    reference = str(transfer_transaction.transaction_id)
    narration = "Transfer from escrow account to loans account"

    request_data = {
        "from_wallet_type": "COLLECTION",
        "to_wallet_type": "COLLECTION",
        "data": [
            {
                "buddy_phone_number": loans_phone_number,
                "amount": available_balance,
                "narration": narration,
                "is_beneficiary": "False",
                "save_beneficiary": "False",
                "remove_beneficiary": "False",
                "is_recurring": "False",
                "custom_reference": reference,
            }
        ],
        "transaction_pin": "",
    }

    send_money_to_loans = AgencyBankingClass.send_money_from_an_account_in_liberty_to_user_through_pay_buddy(
        transaction_pin=transaction_pin,
        phone_number=loans_phone_number,
        amount=available_balance,
        transaction_reference=reference,
        access_token=repayment_access_token,
        narration=narration,
    )

    if send_money_to_loans.get("data").get("message") == "success":
        transfer_transaction.status = Status.SUCCESS
    else:
        transfer_transaction.status = Status.FAILED

    transfer_transaction.request_data = request_data
    transfer_transaction.payload = send_money_to_loans
    transfer_transaction.save()

    return send_money_to_loans


@shared_task
def fund_loan_account_from_commission_via_buddy():
    """
    Transfer from commission to loans
    """
    commissions_access_token = commissions_agent_login(token_not_valid=True).get(
        "access"
    )
    transaction_pin = settings.COMMISSIONS_TRANSACTION_PIN
    transfer_limit = ConstantTable.get_constant_table_instance().internal_transfer_limit

    # Check Commission account balance
    available_balance = check_balances(
        login_purpose="COMMISSION", access_token=commissions_access_token
    )

    if available_balance > transfer_limit:
        available_balance = transfer_limit
    # available_balance = 5000

    # Get Commission User
    commission_user_email = settings.LOAN_COMMISSIONS_USER_EMAIL
    commission_user = User.objects.filter(email=commission_user_email).last()

    # Get Loans account Phone number
    loans_user_email = settings.LOAN_AGENCY_BANKING_USEREMAIL
    loans_user = User.objects.filter(email=loans_user_email).last()
    loans_phone_number = loans_user.user_phone

    transfer_transaction = TransactionService.create_internal_transfer_between_accounts(
        user=commission_user,
        amount=available_balance,
        transaction_description="Transfer from commission account to loans account",
        transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
        transaction_form_type=TransactionFormType.INTERNAL_TRANSFER_TO_LOANS,
        status=Status.PENDING,
    )
    reference = str(transfer_transaction.transaction_id)
    narration = "Transfer from escrow account to loans account"

    request_data = {
        "from_wallet_type": "COLLECTION",
        "to_wallet_type": "COLLECTION",
        "data": [
            {
                "buddy_phone_number": loans_phone_number,
                "amount": available_balance,
                "narration": narration,
                "is_beneficiary": "False",
                "save_beneficiary": "False",
                "remove_beneficiary": "False",
                "is_recurring": "False",
                "custom_reference": reference,
            }
        ],
        "transaction_pin": "",
    }

    send_money_to_loans = AgencyBankingClass.send_money_from_an_account_in_liberty_to_user_through_pay_buddy(
        transaction_pin=transaction_pin,
        phone_number=loans_phone_number,
        amount=available_balance,
        transaction_reference=reference,
        access_token=commissions_access_token,
        narration=narration,
    )

    if send_money_to_loans.get("data").get("message") == "success":
        transfer_transaction.status = Status.SUCCESS
    else:
        transfer_transaction.status = Status.FAILED

    transfer_transaction.request_data = request_data
    transfer_transaction.payload = send_money_to_loans
    transfer_transaction.save()

    return send_money_to_loans


@shared_task
def fund_monnify_account_from_loans_via_agency_transfer():
    """
    Transfer from savings to loans
    """
    from loans.models import MonnifyAccountFundingLog

    # Monnify Account Details
    account_number = settings.MONNIFY_COLLECTIONS_ACCOUNT_NUMBER
    account_name = settings.MONNIFY_ACCOUNT_NAME
    bank_code = settings.MONNIFY_BANK_CODE
    bank_name = settings.MONNIFY_BANK_NAME
    const = ConstantTable.get_constant_table_instance()
    transfer_limit = const.providus_transfer_amount

    loans_access_token = loan_agent_login(token_not_valid=True).get("access")

    # Check Savings account balance
    available_balance = check_balances(
        login_purpose="LOAN", access_token=loans_access_token
    )

    monnify_account_balance = Monnify().check_monnify_balance()
    monnify_account_balance = monnify_account_balance.get("responseBody").get(
        "availableBalance"
    )

    if monnify_account_balance > 40_000_000:
        return "Cap reached"

    if available_balance > transfer_limit:
        available_balance = transfer_limit

    # Get Savings User
    # savings_user_email = settings.LOAN_AGENCY_BANKING_USEREMAIL
    # savings_user = User.objects.filter(email=savings_user_email).last()

    # Get Loans account Phone number
    loans_user_email = settings.LOAN_AGENCY_BANKING_USEREMAIL
    loans_user = User.objects.filter(email=loans_user_email).last()
    loans_phone_number = loans_user.user_phone
    transaction_pin = settings.LOAN_AGENCY_BANKING_TRANSACTION_PIN

    transfer_transaction: "Transaction" = (
        TransactionService.create_internal_transfer_between_accounts(
            user=loans_user,
            amount=available_balance,
            transaction_description="Transfer from loans account to monnify account",
            transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
            transaction_form_type=TransactionFormType.FUND_MONNIFY_ACCOUNT,
            status=Status.PENDING,
        )
    )
    reference = str(transfer_transaction.transaction_id)

    request_data = {
        "from_wallet_type": "COLLECTION",
        "data": [
            {
                "account_number": account_number,
                "account_name": account_name,
                "bank_code": bank_code,
                "bank_name": bank_name,
                "amount": available_balance,
                "narration": "Transfer from loans account to monnify account",
                "is_beneficiary": "False",
                "save_beneficiary": "True",
                "remove_beneficiary": "False",
                "is_recurring": "False",
                "ledger_commission": 0.00,
                "commission_type": None,
            }
        ],
        "total_amount": 0.00,
        "total_amount_with_charge": 0.00,
        "transaction_pin": "",
        "reference": str(reference),
        "narration": f"LD-{str(reference)}",
    }

    send_money_to_monnify_account = (
        AgencyBankingClass.send_money_to_external_account_through_agency(
            amount=available_balance,
            account_number=account_number,
            account_name=account_name,
            bank_code=bank_code,
            bank_name=bank_name,
            customer_reference=reference,
            narration="Transfer from loans account to monnify account",
            access_token=loans_access_token,
            transaction_pin=transaction_pin,
        )
    )
    response_message = (
        send_money_to_monnify_account.get("data", {}).get("message") == "success"
    )

    if response_message == True:
        transfer_transaction.status = Status.PENDING
    else:
        transfer_transaction.status = Status.FAILED

    MonnifyAccountFundingLog.objects.create(
        transaction=transfer_transaction,
        transaction_reference=reference,
        amount=transfer_transaction.amount,
    )

    transfer_transaction.request_data = request_data
    transfer_transaction.payload = send_money_to_monnify_account
    transfer_transaction.unique_reference = reference
    transfer_transaction.save()

    return send_money_to_monnify_account, transfer_transaction.id


@shared_task
def disburse_group_loan_helper(loan_ids: list):
    return  #

    for loan_id in loan_ids:
        try:

            loan_instance = AjoLoan.objects.get(
                id=loan_id,
                verification_stage=VerificationStage.DISBURSEMENT,
                status=LoanStatus.APPROVED,
            )

        except AjoLoan.DoesNotExist:
            loan_instance = None

        if loan_instance is not None:
            savings = loan_instance.eligibility.saving
            moved_funds_to_escrow = move_savings_funds_to_escrow_for_ajo_user(
                loan=loan_instance
            )

            if not moved_funds_to_escrow:
                continue
            credit_ajo_user = AjoLoan.disburse_ajo_loan(
                loan=loan_instance, new_savings=None
            )

            if credit_ajo_user.get("status") is True:
                group = savings.group
                group.status = GroupStatus.RUNNING
                group.save()
                # response = {"status": "Success", "message": "Loan successfully disbursed"}
                try:
                    schedule = AjoLoanSchedule.objects.filter(loan=loan_instance)
                    if schedule.exists():
                        schedule.delete()
                    create_loan_repayment_schedule(loan_instance.id)
                except Exception as e:
                    capture_exception(e)
                try:
                    send_mail_to_loan_officer_supervisor_to_approve_loan(
                        loan_instance=loan_instance.id
                    )
                except Exception as e:
                    pass

                try:
                    disbursement_compliance_checks.delay(loan_id=loan_instance.id)
                except Exception:
                    pass

    return loan_ids


@shared_task
def disburse_group_loan_handler(loan_ids: list):

    for loan_id in loan_ids:
        try:

            loan_instance = AjoLoan.objects.get(
                id=loan_id,
                verification_stage=VerificationStage.DISBURSEMENT,
                status=LoanStatus.APPROVED,
            )

        except AjoLoan.DoesNotExist:
            loan_instance = None

        if loan_instance is not None:
            savings = loan_instance.eligibility.saving
            moved_funds_to_escrow = move_savings_funds_to_escrow_for_ajo_user(
                loan=loan_instance
            )

            if not moved_funds_to_escrow:
                continue
            credit_ajo_user = AjoLoan.disburse_ajo_loan(
                loan=loan_instance, new_savings=None
            )

            if credit_ajo_user.get("status") is True:
                group = savings.group
                group.status = GroupStatus.RUNNING
                group.save()
                # response = {"status": "Success", "message": "Loan successfully disbursed"}
                try:
                    schedule = AjoLoanSchedule.objects.filter(loan=loan_instance)
                    if schedule.exists():
                        schedule.delete()
                    create_loan_repayment_schedule(loan_instance.id)
                except Exception as e:
                    capture_exception(e)
                try:
                    send_mail_to_loan_officer_supervisor_to_approve_loan(
                        loan_instance=loan_instance.id
                    )
                except Exception as e:
                    pass

                try:
                    disbursement_compliance_checks.delay(loan_id=loan_instance.id)
                except Exception:
                    pass

    return loan_ids


@shared_task
def group_loan_repayment(loan_data: List[Dict[str, Any]]) -> List:
    data = {}

    for loan_repayment in loan_data:
        loan_id = loan_repayment.get("loan_id")
        amount = loan_repayment.get("amount")

        try:
            loan = AjoLoan.objects.get(id=loan_id)
        except AjoLoan.DoesNotExist:
            data["loan_id"] = "a loan with this ID does not exist"
            continue

        if not loan.status != LoanStatus.OPEN:
            data["loan_id"] = f"loan's status is {loan.status}"
            continue

        agent_wallet = AjoAgentSelector(user=loan.user).get_agent_ajo_wallet()
        if not check_if_agent_can_pay(agent_wallet=agent_wallet):
            data["loan_id"] = "insufficient funds in agent's wallet"

        repayment_status, repayment = AjoLoanRepayment.loan_repayment(
            loan=loan,
            amount=amount,
            from_wallet=agent_wallet,
            repayment_type=RepaymentType.AGENT_DEBIT,
        )

        if repayment_status == True:
            """Post to loan disk when ready"""

            # POST REPAYMENT RECORD TO LOANDISK
            celery_handle_loandisk_loan_repayment.delay(
                ajo_loan_id=loan.id,
                amount=amount,
                repayment_type=RepaymentType.AGENT_DEBIT,
            )

        # release_escrow_balance(phone_number=loan.borrower.phone, amount=amount, agent=loan.agent)

        update_loan_repayment_schedules.delay(loan.id, repayment.id)
        categorize_missed_loan.delay(loan.id)

        data["loan_id"] = "repayment made successfully"

    return success_celery_response(message="task completed succesfully", data=data)


@shared_task
def borrower_record_upload():
    return CRCRecord.borrower_information()


@shared_task
def credit_record_upload():
    return CRCRecord.credit_information()


@shared_task
def crc_upload():
    CRCRecord.credit_information()
    CRCRecord.borrower_information()


@shared_task
def create_health_plan_handler(
    loan_id,
    kyc_instance_id,
):
    """
    Celery task to handle the creation of a health insurance plan.

    This task is responsible for initiating the process of creating a health
    insurance plan for a given loan ID. It communicates with the HealthInsurance
    service to request the creation of the plan.

    Args:
        loan_id (int): The unique identifier for the loan.

    Returns:
        Response: The response from the HealthInsurance service indicating the
                  status of the plan creation request.

    Example:
        create_health_plan_handler.delay(loan_id=123)
    """

    result = HealthInsurance.request_create_paln(
        loan_id=loan_id,
        kyc_instance_id=kyc_instance_id,
    )
    return result


# @shared_task
# def settle_health_plan_payments():
#     # Fetch all successful health insurances with pending fund transfer status
#     # Do buddy transfer from savings account to libertylife account
#     # Do external transfer from libertylife account to VFD generated account for user

#     successful_pending_transfer_plans = HealthInsurance.objects.filter(
#         request_status=InsuranceRequestStatus.SUCCESS,
#         fund_transfer_status=InsuranceRequestStatus.PENDING,
#         is_processing=False,
#     )
#     # print("::::::::::::::::::::")
#     # print(successful_pending_transfer_plans)
#     # print("::::::::::::::::::::")

#     successful_pending_transfer_plans.update(is_processing=True)

#     if successful_pending_transfer_plans is None:
#         return "No pending plan found"

#     # Savings Account Login
#     savings_access_token = agent_login(token_not_valid=True).get("access")
#     transaction_pin = settings.AGENCY_BANKING_TRANSACTION_PIN
#     savings_user_email = settings.AGENCY_BANKING_USEREMAIL
#     savings_user = User.objects.filter(email=savings_user_email).last()
#     # savings_phone_number = savings_user.user_phone

#     # Check Savings account balance
#     # available_balance = check_balances(login_purpose="SAVINGS", access_token=savings_access_token)

#     # Get LibertyLife User
#     liberty_life_user_email = settings.LIBERTY_LIFE_AGENCY_USER_EMAIL
#     liberty_life_user = User.objects.filter(email=liberty_life_user_email).last()
#     # liberty_life_phone_number = liberty_life_user.user_phone
#     response_data = {}

#     for plan in successful_pending_transfer_plans:
#         send = HealthInsurance.send_healthplan_payment_to_liberty_life_account(
#             plan=plan, access_token=savings_access_token
#         )
#         response_data[f"{plan.ajo_user.phone_number}"] = send
#     successful_pending_transfer_plans.update(is_processing=False)
#     return response_data


# @shared_task
# def send_health_plans_payments_to_vfd():
#     # Do external transfer from libertylife account to VFD generated account for user

#     pending_vfd_transfer_plans = HealthInsurance.objects.filter(
#         request_status=InsuranceRequestStatus.SUCCESS,
#         fund_transfer_status=InsuranceRequestStatus.SUCCESS,
#         vfd_transfer_status=InsuranceRequestStatus.PENDING,
#         is_processing=False,
#     )
#     pending_vfd_transfer_plans.update(is_processing=True)
#     response_data = {}
#     liberty_life_access_token = liberty_life_agent_login(token_not_valid=True).get(
#         "access"
#     )

#     for plan in pending_vfd_transfer_plans:
#         send = HealthInsurance.send_health_plan_payment_to_borrower_assigned_account(
#             plan=plan, access_token=liberty_life_access_token
#         )
#         response_data[f"{plan.ajo_user.phone_number}"] = send
#     pending_vfd_transfer_plans.update(is_processing=False)
#     return response_data


@shared_task
def create_vfd_repayment_schedule():
    schedule = VfdRepaymentSchedule.create_vfd_repayment_schedule_task()


@shared_task
def update_loan_total_paid_amount(loan_instance: AjoLoan = None):
    if (
        loan_instance
        and loan_instance.total_paid_amount < loan_instance.total_repayment
    ):
        loan_instance.total_paid_amount = loan_instance.total_repayment
        repayment_qs = AjoLoanRepayment.objects.filter(ajo_loan=loan_instance)

        if loan_instance.total_paid_amount >= loan_instance.repayment_amount:
            loan_instance.status = LoanStatus.COMPLETED

            try:
                loan_instance.date_completed = repayment_qs.latest(
                    "created_at"
                ).paid_date
            except Exception:
                pass

        loan_instance.synced_with_repayments = True
        loan_instance.save()
    else:
        open_loans_qs = AjoLoan.objects.filter(status=LoanStatus.OPEN)
        for loan in open_loans_qs:
            repayment_qs = AjoLoanRepayment.objects.filter(ajo_loan=loan)

            if loan.total_paid_amount < loan.total_repayment:
                loan.total_paid_amount = loan.total_repayment
                if loan.total_paid_amount >= loan.repayment_amount:
                    loan.status = LoanStatus.COMPLETED

                    try:
                        loan.date_completed = repayment_qs.latest(
                            "created_at"
                        ).paid_date
                    except Exception:
                        pass

                loan.synced_with_repayments = True
                loan.save()


@shared_task
def send_agent_commission_to_paybox():
    import json

    import requests

    month_start = DateUtility().month_start

    loan_ofiicers_qs = get_user_model().objects.filter(user_type="STAFF_AGENT")

    commission_list = []

    for loan_officer in loan_ofiicers_qs:
        # calculate loan_officer earnings
        disbursed_loan_qs = AjoLoan.objects.filter(
            agent=loan_officer, is_disbursed=True
        )
        disbursed_loan_amount_by_month = disbursed_loan_qs.filter(
            created_at__gte=month_start
        )
        this_month_disbursed_amount = (
            disbursed_loan_amount_by_month.aggregate(amount=Sum("amount"))["amount"]
            or 0
        )
        this_month_total_interest_amount = (
            disbursed_loan_amount_by_month.aggregate(amount=Sum("interest_amount"))[
                "amount"
            ]
            or 0
        )

        commission_earned = SalaryDecisioning().get_commission(
            disbursement_amount_month=this_month_disbursed_amount,
            total_interest=this_month_total_interest_amount,
        )

        commission_data = {
            "email": loan_officer.email,
            "commission_amount": commission_earned,
        }

        commission_list.append(commission_data)

    # Send Data to Paybox
    base_url = settings.PAYBOX_URL
    url = f"{base_url}/payroll/add_bonus_payroll/?company_uuid={'company_id'}"
    authorization = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {settings.PAYBOX_TOKEN}",
    }

    data = {"commissions": commission_list}
    resp = requests.request(
        "POST", headers=authorization, url=url, data=json.dumps(data)
    )

    return resp.json()


@shared_task
def retry_failed_monnify_transactions():
    from loans.models import MonnifyAccountFundingLog
    from payment.checks import VerifyTransactionsOnAgency

    """
    Task to retry failed Monnify transactions that have not been retried yet.
    """
    pending_funding_transactions = MonnifyAccountFundingLog.objects.filter(
        status=Status.PENDING, retried=False
    )

    for transaction in pending_funding_transactions:
        check_transaction_status = VerifyTransactionsOnAgency.verify_bank_transfers_v2(
            reference=transaction.transaction_reference,
        )
        try:
            if check_transaction_status.get("status"):
                if check_transaction_status.get("transferred"):
                    transaction.status = Status.SUCCESS
                    transaction.retried = False
                    transaction.verification_data = check_transaction_status
                    transaction.save()
                elif check_transaction_status.get("reversed"):
                    transaction.retry_date = timezone.now()
                    transaction.status = Status.FAILED
                    # retry transaction
                    fund_monnify_account_from_loans_via_agency_transfer()
                    transaction.retried = True
                    transaction.verification_data = check_transaction_status
                    transaction.save()
                else:
                    pass
        except Exception as e:
            # Log the exception
            logger.error(
                f"Failed to retry transaction {transaction.transaction_reference}: {e}"
            )
    return {
        "status": "completed",
        "retried_transactions checked": pending_funding_transactions.count(),
    }


@shared_task
def send_salaries_to_paybox():
    import json

    import requests

    from accounts.models import ConstantTable
    from collections_app.enums import UserTypes
    from collections_app.helpers.helpers import SupervisorClass
    from collections_app.models import PayrollLog

    constants_inst = ConstantTable.get_constant_table_instance()

    loan_oficers_list = list(
        get_user_model()
        .objects.filter(user_type=UserTypes.STAFF_AGENT)
        .values_list("customer_user_id", flat=True)
    )
    all_supervisors = SupervisorClass().get_all_supervisors() or []
    all_users = set(loan_oficers_list + all_supervisors)

    paybox_data_batch_list = []
    agent_details_list = []

    for user in all_users:
        if user in all_supervisors:
            user_type = UserTypes.SUPERVISOR
        else:
            user_type = UserTypes.STAFF_AGENT
        try:
            user: CustomUser = CustomUser.objects.get(customer_user_id=user)
        except CustomUser.DoesNotExist:
            continue

        salary_info = SalaryDecisioning().get_prorated_salary(
            user=user,
            user_type=user_type,
            is_payroll=True,
        )

        data = {
            "email": user.email,
            "net_amount": salary_info.get("full_salary", 0.00),
            "payable_amount": salary_info.get("full_salary", 0.00),
            "bonus": salary_info.get("commission_earned", 0.00),
        }
        paybox_data_batch_list.append(data)

        agent_data = data.update(
            {
                "name": user.first_name + " " + user.last_name,
                "branch": user.user_branch if user.user_branch else "",
            }
        )
        agent_details_list.append(agent_data)
    # print(paybox_data_batch_list)

    # print("res::::::::::::::::::::::::::")
    # print(res)
    # print("paybox user:::::::::::::::")
    # print(paybox_user)
    # print("res::::::::::::::::::::::::::")

    constants_inst.last_payroll_date = timezone.now()
    constants_inst.save()

    data = {"employees": paybox_data_batch_list}

    PayrollLog.objects.create(
        request_data=data,
        response_data={},
        last_payroll_date=salary_info.get("last_payroll_date"),
        agent_details_data=agent_details_list,
    )
    return "Run successfully"


def failed_disbursements_supervisor_notification(
    email, borrower_phone_number, amount, full_name,
    title, subject
):
    template_dir = os.path.join("loans", "failed_disbursement.html")
    general_send_email(
        recipient=email,
        subject=subject,
        template_dir=template_dir,
        use_template=True,
        body=None,
        date=str(timezone.now()),
        title=title,
        borrower_phone_number=borrower_phone_number,
        amount=amount,
        full_name=full_name,
    )


@shared_task
def renew_health_insurance_plan():
    """
    Celery task to handle the renewal of Insurance plans.
    """
    const = ConstantTable.get_constant_table_instance()
    monthly_fee = const.health_insurance_fee

    insurances = HealthInsurance.objects.filter(
        daily_allocation__gte=monthly_fee, request_status="SUCCESS"
    )
    for insurance in insurances:
        insurance.renew_plan()

    return insurances.count()


@shared_task
def post_loans_to_loandisk_from_mirror_db():
    print(f"=======RUNNING POSTING LOANS FROM MIRROR TABLE TASK==========")
    loandisk_mirror = LoanDiskLoansMirror.objects.filter(
        request_type="LOAN_ENTRY", resolved=False
    )

    for loan_mirror in loandisk_mirror:
        print(f"POST LOANS RUNNING FOR ==={loan_mirror.loan_id} {loan_mirror.id}====")
        loan_mirror.status = "PENDING"
        loan_mirror.save()
        run_post_loan_to_loan_disk(
            loan_id=loan_mirror.loan_id, loan_mirror_id=loan_mirror.id
        )


@shared_task
def update_loans_to_loandisk_from_mirror_db():
    print(f"=======RUNNING UPDATING TASK==========")
    loandisk_mirror = LoanDiskLoansMirror.objects.filter(
        request_type="UPDATE_LOAN_ENTRY", resolved=False
    )

    for loan_mirror in loandisk_mirror:
        print(
            f"UPDATING LOANS RUNNING FOR ==={loan_mirror.loan_id} {loan_mirror.id}===="
        )
        loan_mirror.status = "PENDING"
        loan_mirror.save()
        update_loan_status_on_loan_disk(
            loan_id=loan_mirror.loan_id, from_signal=True, loan_mirror_id=loan_mirror.id
        )


@shared_task
def run_repayment_posting_to_loan_disk():
    print(f"=======RUNNING REPAYMENT TASK==========")
    loandisk_mirror = LoanDiskLoansMirror.objects.filter(
        request_type="REPAYMENT_ENTRY", resolved=False
    )
    print

    for loan_mirror in loandisk_mirror:
        loan_mirror.status = "PENDING"
        loan_mirror.save()
        celery_handle_loandisk_loan_repayment(
            ajo_loan_id=loan_mirror.loan_id,
            amount=loan_mirror.repayment_amount,
            repayment_type=loan_mirror.repayment_amount,
            loan_mirror_id=loan_mirror.id,
        )


@shared_task
def notify_liberty_life_upon_funds_transfer():
    """
    Celery task to notify Liberty Life upon funds sharing.
    """
    import base64
    import json

    import requests

    username = settings.LIBERTY_LIFE_USERNAME
    password = settings.LIBERTY_LIFE_PASSWORD
    string_value = f"{username}:{password}"
    auth_token = base64.b64encode(string_value.encode("ascii")).decode("utf-8")

    url = f"{settings.LIBERTY_LIFE_BASE_URL}/log_health_payments/"

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Basic {auth_token}",
    }

    completed_health_insurance_transfers = HealthInsurance.objects.filter(
        request_status=InsuranceRequestStatus.SUCCESS,
        fund_transfer_status=InsuranceRequestStatus.SUCCESS,
        vfd_transfer_status=InsuranceRequestStatus.SUCCESS,
        liberty_life_notified=False,
    )

    for insurance in completed_health_insurance_transfers:
        data = {
            "phone_number": insurance.ajo_user.phone_number,
            "user_name": f"{insurance.ajo_user.first_name} {insurance.ajo_user.last_name}",
            "account_number": insurance.account_number,
            "reference": str(
                insurance.vfd_transfer and insurance.vfd_transfer.transaction_id
            ),
            "amount": insurance.amount,
        }

        try:
            response = requests.post(url, headers=headers, data=json.dumps(data))

            insurance.liberty_life_notification_payload = response.text
            if response.status_code == 200:
                insurance.liberty_life_notified = True
                insurance.liberty_life_notified_count += 1
        except Exception as e:
            insurance.liberty_life_notification_payload = str(e)
        insurance.save()


@shared_task
def update_escrow_offset_value():
    ajo_loans = AjoLoan.objects.filter(escrow_offset=False)

    for loan in ajo_loans:
        escrow_debit_trans = AjoLoanRepayment.objects.filter(
            ajo_loan=loan,
            repayment_type=RepaymentType.ESCROW_DEBIT,
            repayment_amount__gt=0,
        )

        if escrow_debit_trans:
            loan.escrow_offset = True
            loan.save()


@shared_task
def bulk_repayment_handler(batch_id=None):
    """
    Handles bulk loan repayment processing as an asynchronous task.

    Parameters:
    -----------
    batch_id : str, optional
        A unique identifier for the batch of repayments to process. Defaults to None.
        When provided, this ID is used to retrieve and process a specific batch
        of loan repayments.

    Process:
    --------
    1. Invokes the `make_payment` method on the `BulkRepaymentRecord` model, passing
       in the `batch_id` to initiate repayment processing for the given batch.
    2. Executes asynchronously as a Celery task, allowing large or time-intensive
       repayment batches to be processed in the background without blocking the main
       application flow.

    Returns:
    --------
    Any
        The return value from the `BulkRepaymentRecord.make_payment` method, which
        may include a status or confirmation of successful processing.

    Example:
    --------
    # Trigger bulk repayment handling asynchronously for a specific batch
    bulk_repayment_handler.delay(batch_id="12345ABC")

    # This runs as a background task, processing all repayments in the specified batch.
    """
    return BulkRepaymentRecord.make_payment(batch_id=batch_id)


@shared_task
def create_update_renewal_summary():
    """create/update summary"""
    insurance_queryset = HealthInsurance.objects.filter(loan__isnull=False)
    for insurance_instance in insurance_queryset:
        insurance_instance.create_renewal()
    return f"Total count: {insurance_queryset.count()}"


@shared_task
def request_plan_renewal():
    """request plan renewal"""
    count = HealthPlanRenewal.notify_liberty_life_plan_renewal()

    return f"Total count: {count}"


@shared_task
def openai_loan_review(
    eligibility_id, eligible_amount, phone_number, verification_result, checker_id=None
):
    review_result = OpenAiEligbilityReview.get_eligibility_review(
        eligibility_id=eligibility_id,
        eligible_amount=eligible_amount,
        phone_number=phone_number,
        verification_result=verification_result,
        checker_id=checker_id,
    )
    return review_result


@shared_task
def notify_liberty_life_upon_renewals_funds_transfer():
    """
    Celery task to notify Liberty Life upon funds remittance
    for health plan renewals.
    """
    import base64
    import json

    import requests

    username = settings.LIBERTY_LIFE_USERNAME
    password = settings.LIBERTY_LIFE_PASSWORD
    string_value = f"{username}:{password}"
    auth_token = base64.b64encode(string_value.encode("ascii")).decode("utf-8")

    url = f"{settings.LIBERTY_LIFE_BASE_URL}/log_health_payments/"

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Basic {auth_token}",
    }

    completed_health_insurance_transfers = HealthPlanRenewal.objects.filter(
        status=InsuranceRequestStatus.SUCCESS,
        transfer_status=InsuranceRequestStatus.SUCCESS,
        borrower_account_transfer_status=InsuranceRequestStatus.SUCCESS,
        liberty_life_notified=False,
    )

    for insurance in completed_health_insurance_transfers:
        data = {
            "phone_number": insurance.phone_number,
            "user_name": f"{insurance.first_name} {insurance.last_name}",
            "account_number": insurance.account_number,
            "reference": str(
                insurance.borrower_account_transfer
                and insurance.borrower_account_transfer.transaction_id
            ),
            "amount": insurance.amount,
        }

        try:
            response = requests.post(url, headers=headers, data=json.dumps(data))

            insurance.liberty_life_notification_payload = response.text
            if response.status_code == 200:
                insurance.liberty_life_notified = True
        except Exception as e:
            insurance.liberty_life_notification_payload = str(e)
        insurance.save()


@shared_task
def update_completed_loans_missed_repayment():
    """
    Update completed loans with missed repayments.
    """
    completed_loans = AjoLoan.objects.filter(
        status=LoanStatus.COMPLETED,
        missed_repayment_tables_resolved=False,
    )

    for loan in completed_loans:
        categorize_loans(loan_id=loan.id)
        loan.missed_repayment_tables_resolved = True
        loan.save()


@shared_task
def handle_image_upload_result_update_on_loan_eligiblity_verification(
    verification_instance_id, result, img_string
):
    try:
        verification_instance = LoanEligibilityVerification.objects.get(
            id=verification_instance_id
        )
    except LoanEligibilityVerification.DoesNotExist:
        return
    actual_data = result.get("actual_data")
    verification_instance.proposed_loan_amount = actual_data.get("proposed_loan_amount")
    verification_instance.percentage_difference = actual_data.get(
        "percentage_difference"
    )
    verification_instance.earnings_per_unit_percentage = actual_data.get(
        "earnings_per_unit_percentage"
    )
    verification_instance.alt_proposed_loan_amount = actual_data.get(
        "alt_proposed_loan_amount"
    )
    verification_instance.total_repayment_amount = actual_data.get(
        "total_repayment_amount"
    )
    verification_instance.alt_total_repayment_amount = actual_data.get(
        "alt_total_repayment_amount"
    )
    verification_instance.weekly_revenue = result.get("weekly_revenue")
    verification_instance.weekly_profit = result.get("weekly_profit")
    verification_instance.comfortable_weekly_repayment = result.get(
        "comfortable_weekly_repayment"
    )
    verification_instance.daily_revenue = result.get("daily_revenue")
    verification_instance.daily_profit = result.get("daily_profit")
    verification_instance.img_string = img_string

    verification_instance.save()

    return result


@shared_task
def update_credit_health_request_status_stages(loan_access_code, stage, request_status):
    from loans.helpers.libertylife import LibertyLifeMgr

    liberty_lifemgr = LibertyLifeMgr()

    result = liberty_lifemgr.update_credit_health_request_status_stages(
        loan_access_code=loan_access_code, request_status=request_status, stage=stage
    )
    return str(loan_access_code)


@shared_task
def send_healthplan_funds_libertylife_account():
    """
    - This is the first stage in the remittance of the initial amount
    for health plan policy subscriptions.
    The funds are transferred to an agency banking account.
    """
    queryset = HealthInsurance.objects.filter(
        request_status=InsuranceRequestStatus.SUCCESS,
        fund_transfer_status=InsuranceRequestStatus.PENDING,
    )
    response_message = {}
    savings_access_token = agent_login(token_not_valid=True).get("access")
    for query in queryset:
        query: HealthInsurance
        send = HealthInsurance.send_healthplan_payment_to_liberty_life_account(
            plan=query, access_token=savings_access_token
        )
        response_message[f"{query.ajo_user.phone_number}"] = send
    return response_message


@shared_task
def send_healthplan_funds_borrower_assigned_account():
    """ "
    This is the second leg in the remittance of initial funds for borrower's health plan
    policy. At this point the amount is transferred to a virtual account created for
    the borrower by LibertyLife business.
    The funds go out from the Liberty Life Agency banking acount.
    """
    response_message = {}
    liberty_life_access_token = liberty_life_agent_login(token_not_valid=True).get(
        "access"
    )

    queryset = HealthInsurance.objects.filter(
        request_status=InsuranceRequestStatus.SUCCESS,
        fund_transfer_status=InsuranceRequestStatus.SUCCESS,
        vfd_transfer_status__in=[
            InsuranceRequestStatus.PENDING,
            InsuranceRequestStatus.FAILED,
        ],
    )

    for query in queryset:
        query: HealthInsurance
        send = HealthInsurance.send_health_plan_payment_to_borrower_assigned_account(
            plan=query, access_token=liberty_life_access_token
        )
        response_message[f"{query.ajo_user.phone_number}"] = send
    return response_message


@shared_task
def send_healthplan_renewal_funds_libertylife_account():
    """
    - This is the first stage in the remittance of the renewal amount
    for health plan policy subscriptions.
    The funds are transferred to an agency banking account.
    """
    response_message = {}

    queryset = HealthPlanRenewal.objects.filter(
        status=InsuranceRequestStatus.SUCCESS,
        transfer_status=InsuranceRequestStatus.PENDING,
    )

    savings_access_token = agent_login(token_not_valid=True).get("access")
    for query in queryset:
        query: HealthPlanRenewal
        send = (
            HealthPlanRenewal.send_healthplan_renewal_payment_to_liberty_life_account(
                plan=query, access_token=savings_access_token
            )
        )
        response_message[f"{query.phone_number}"] = send
    response_message


@shared_task
def send_healthplan_renewal_funds_borrower_assigned_account():
    """
    This is the second leg in the remittance of RENEWAL funds for borrower's health plan
    policy. At this point the amount is transferred to a virtual account created for
    the borrower by LibertyLife business.
    The funds go out from the Liberty Life Agency banking acount.
    """
    response_message = {}

    queryset = HealthPlanRenewal.objects.filter(
        status=InsuranceRequestStatus.SUCCESS,
        transfer_status=InsuranceRequestStatus.SUCCESS,
        borrower_account_transfer_status__in=[
            InsuranceRequestStatus.PENDING,
            InsuranceRequestStatus.FAILED,
        ],
    )
    liberty_life_access_token = liberty_life_agent_login(token_not_valid=True).get(
        "access"
    )
    for query in queryset:
        query: HealthPlanRenewal
        send = HealthPlanRenewal.send_health_plan_renewal_payment_to_borrower_assigned_account(
            plan=query, access_token=liberty_life_access_token
        )
        response_message[f"{query.phone_number}"] = send
    return response_message


@shared_task
def send_text_messages(
    phone_number: str,
    message: str,
    loan_repayment=False,
    repayment_id=None,
    sms_log_instance_id=None,
) -> str:

    result = TextMessages.send_sms(user_phone=phone_number, message=message)

    status = result.get("response", {}).get("status", False)
    if loan_repayment and repayment_id is not None:
        if isinstance(status, bool):

            AjoLoanRepayment.objects.filter(id=repayment_id).update(
                borrower_notified=status, sms_result=result
            )
        else:
            AjoLoanRepayment.objects.filter(id=repayment_id).update(sms_result=result)
    if sms_log_instance_id is not None:
        if isinstance(status, bool):

            SMSLog.objects.filter(id=sms_log_instance_id).update(
                response=result, sent=status
            )
        else:
            SMSLog.objects.filter(id=sms_log_instance_id).update(
                response=result,
            )
    return str(result)


@shared_task
def send_supplementary_salaries_to_paybox():
    import pandas as pd
    import json

    from collections_app.models import PayrollLog

    last_payroll_date = PayrollLog.objects.latest("created_at").last_payroll_date

    paybox_data_batch_list = []
    agent_details_list = []

    temp_metrics_file = pd.read_excel("SEEDS SUPPLEMENTARY.xlsx")
    temp_metrics_json = temp_metrics_file.to_json(orient="records")
    temp_metrics_list = json.loads(temp_metrics_json)

    for record in temp_metrics_list:
        net_amount = record.get("net_amount", 0.00)
        # print("ORIGINAL PAYABLE AMOUNT:::::::::::::::::;")
        # print(record.get("Payable Amount"))
        # print(type(record.get("Payable Amount")))
        payable_amount = (
            float(record.get("Payable Amount", 0.00))
            if isinstance(record.get("Payable Amount", 0.00), int)
            or isinstance(record.get("Payable Amount", 0.00), float)
            else 0.00
        )
        bonus = record.get("bonus", 0.00)
        # print(":::::::::::::::::::FINAL PAYABLE AMOUNT COMING IN:::::::::::::")
        # print(":::::::::::::::::::FINAL PAYABLE AMOUNT COMING IN:::::::::::::")
        # print(payable_amount)
        # print("::::::::::::::MORE CHECKS::::::::::::::::::::::")
        # print("::::::::::::::MORE CHECKS::::::::::::::::::::::")
        # print(type(payable_amount))

        # balance = net_amount - payable_amount

        data = {
            "email": record.get("Agent Email"),
            "net_amount": net_amount,
            "payable_amount": payable_amount,
            "bonus": bonus,
        }
        paybox_data_batch_list.append(data)

    data = {"employees": paybox_data_batch_list}

    PayrollLog.objects.create(
        request_data=data,
        response_data={},
        last_payroll_date=last_payroll_date,
        agent_details_data={"data": agent_details_list},
    )
    return "Run successfully"


@shared_task
def update_seeds_account_balances():
    from loans.models import SeedsAccountBalance

    spend_wallet_balance = (
        WalletSystem.objects.filter(wallet_type=WalletTypes.AJO_SPENDING).aggregate(
            total_amount=Sum("available_balance")
        )["total_amount"]
        or 0.00
    )

    response = CoreBankingManager.check_account_details()
    wema_ajo_loans_balance = (
        response.get("data", {}).get("acount_details").get("cash_balance", 0.00)
    )
    final_wema_ajo_loans_balance = (
        float(wema_ajo_loans_balance.replace("NGN", "").replace(",", ""))
        if isinstance(wema_ajo_loans_balance, str)
        else wema_ajo_loans_balance
    )
    SeedsAccountBalance.objects.create(
        spend_wallet_balance=spend_wallet_balance,
        wema_ajo_loans_balance=final_wema_ajo_loans_balance,
        wema_response=response,
        raw_wema_balance=wema_ajo_loans_balance,
    )

    return "Run successfully!!!"


@shared_task
def handle_get_transaction_from_liberty_pay_create_eligibility_summary():
    ### handles create merchant eligiblity summary
    return (
        MerchantEligibilitySummary.get_transaction_from_liberty_pay_create_eligibility_summary()
    )


@shared_task
def handle_loan_statement_notification():
    ### notify borrower monthly on loan statement
    return SMSLog.loan_statement_notification()


@shared_task
def handle_weekly_notification():
    ### notify borrower weekly on loan summary
    return SMSLog.weekly_notification()


@shared_task
def handle_past_maturity_loan_notification():
    ### notify past maturity
    return SMSLog.past_maturity_loan_notification()


@shared_task
def handle_bulk_create_or_update_agent_performance():
    ### agent repayment
    return AgentRepaymentPerformance.bulk_create_or_update_agent_performance()


@shared_task
def fund_providus_account_from_loans_via_agency_transfer():
    """
    Transfer from loans to Providus account
    """
    from loans.models import MonnifyAccountFundingLog
    from decouple import config

    cont_instance = ConstantTable.objects.last()

    # Monnify Account Details
    account_number = config("PROVIDUS_ACCOUNT_NUMBER")
    account_name = config("PROVIDUS_ACCOUNT_NAME")
    bank_code = config("PROVIDUS_BANK_CODE")
    bank_name = config("PROVIDUS_BANK_NAME")
    transfer_limit = ConstantTable.get_constant_table_instance().internal_transfer_limit
    transfer_amount = cont_instance.providus_transfer_amount

    loans_access_token = loan_agent_login(token_not_valid=True).get("access")

    # Check Savings account balance
    available_balance = check_balances(
        login_purpose="LOAN", access_token=loans_access_token
    )

    if available_balance < transfer_amount:
        return "Insufficient funds"

    # Get Loans account Phone number
    loans_user_email = settings.LOAN_AGENCY_BANKING_USEREMAIL
    loans_user = User.objects.filter(email=loans_user_email).last()
    loans_phone_number = loans_user.user_phone
    transaction_pin = settings.LOAN_AGENCY_BANKING_TRANSACTION_PIN

    transfer_transaction: "Transaction" = (
        TransactionService.create_internal_transfer_between_accounts(
            user=loans_user,
            amount=transfer_amount,
            transaction_description="Daily funding of providus account",
            transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
            transaction_form_type=TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT,
            status=Status.PENDING,
        )
    )
    reference = str(transfer_transaction.transaction_id)

    request_data = {
        "from_wallet_type": "COLLECTION",
        "data": [
            {
                "account_number": account_number,
                "account_name": account_name,
                "bank_code": bank_code,
                "bank_name": bank_name,
                "amount": transfer_amount,
                "narration": "Daily Funding of Providus account",
                "is_beneficiary": "False",
                "save_beneficiary": "True",
                "remove_beneficiary": "False",
                "is_recurring": "False",
                "ledger_commission": 0.00,
                "commission_type": None,
            }
        ],
        "total_amount": 0.00,
        "total_amount_with_charge": 0.00,
        "transaction_pin": "",
        "reference": str(reference),
        "narration": f"LD-{str(reference)}",
    }

    send_money_to_providus_account = (
        AgencyBankingClass.send_money_to_external_account_through_agency(
            amount=transfer_amount,
            account_number=account_number,
            account_name=account_name,
            bank_code=bank_code,
            bank_name=bank_name,
            customer_reference=reference,
            narration="Daily Funding of Providus account",
            access_token=loans_access_token,
            transaction_pin=transaction_pin,
        )
    )
    response_message = (
        send_money_to_providus_account.get("data", {}).get("message") == "success"
    )

    if response_message == True:
        transfer_transaction.status = Status.PENDING
    else:
        transfer_transaction.status = Status.FAILED

    # MonnifyAccountFundingLog.objects.create(
    #     transaction=transfer_transaction,
    #     transaction_reference=reference,
    #     amount=transfer_transaction.amount,
    # )

    transfer_transaction.request_data = request_data
    transfer_transaction.payload = send_money_to_providus_account
    transfer_transaction.unique_reference = reference
    transfer_transaction.save()

    return send_money_to_providus_account, transfer_transaction.id


@shared_task
def auto_charge_merchant_outstanding_loan():
    """
    Automatically charge merchants for outstanding loans.

    This task performs the following steps:
    1. Retrieves all open merchant overdraft loans.
    2. For each loan, checks the agent's balance.
    3. If there's an outstanding due and sufficient balance, it attempts to charge the agent's wallet.
    4. Creates a repayment record for successful charges.
    5. Keeps track of successful charges, failed charges, and total amount charged.

    Returns:
        dict: A summary of the auto-charge process, including:
            - Number of loans processed
            - Number of successful charges
            - Number of failed charges
            - Total amount charged
            - List of failed charge messages
    """
    from loans.models import AjoLoan, AjoLoanRepayment
    from payment.services import TransactionService
    from payment.model_choices import (
        TransactionTypeCreditOrDebitChoices,
        TransactionFormType,
        Status,
    )
    from loans.helpers.apis.agency_banking import LibertyPayMgr
    from django.conf import settings
    from loans.enums import RepaymentType

    liberty_mgr = LibertyPayMgr(config=settings)

    # Get all open merchant overdraft loans
    due_loans = AjoLoan.objects.filter(status="OPEN", loan_type="MERCHANT_OVERDRAFT")

    successful_charges = 0
    failed_charges = 0
    total_charged = 0
    failed_charge_messages = []

    for loan in due_loans:
        user = loan.agent
        # print(loan.outstanding_due)
        if loan.outstanding_due <= 0:
            continue

        # Get agent balance
        balance_request = liberty_mgr.get_agent_balance(
            user_ids=[user.customer_user_id]
        )

        if balance_request.get("status") == "success":
            balance = liberty_mgr.get_wallet_balance(balance_request.get("response"))

            # Determine amount to charge
            charge_amount = min(loan.outstanding_due, balance)

            if charge_amount > 0:
                description = f"Automatic loan repayment for outstanding due"

                # Create transfer transaction
                transfer_transaction = TransactionService.create_internal_transfer_between_accounts(
                    user=user,
                    amount=charge_amount,
                    transaction_description=description,
                    transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
                    transaction_form_type=TransactionFormType.LOAN_REPAYMENT,
                    status=Status.PENDING,
                    transfer_provider=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
                )

                # Charge user's wallet
                reference = str(transfer_transaction.transaction_id)
                charge_result = liberty_mgr.charge_user_wallet(
                    user_id=user.customer_user_id,
                    unique_reference=reference,
                    narration=description,
                    amount=charge_amount,
                )

                # Update transaction status
                if (
                    charge_result.get("status") == "success"
                    and charge_result.get("response", {}).get("message") == "success"
                ):
                    transfer_transaction.status = Status.SUCCESS

                    # Create repayment record
                    success, repayment_record = AjoLoanRepayment.loan_repayment(
                        loan=loan,
                        amount=charge_amount,
                        from_wallet=None,
                        repayment_type=RepaymentType.LIBERTYPAY_DIRECTDEBIT,
                        transaction_source_trx=transfer_transaction,
                    )

                    successful_charges += 1
                    total_charged += charge_amount
                else:
                    transfer_transaction.status = Status.FAILED
                    failed_charges += 1
                    failed_charge_messages.append(
                        f"Failed to charge loan {loan.id} for user {user.id}: Insufficient funds or charge request error"
                    )

                transfer_transaction.payload = charge_result
                transfer_transaction.save(update_fields=["payload", "status"])

    result = {
        "loans_processed": len(due_loans),
        "successful_charges": successful_charges,
        "failed_charges": failed_charges,
        "total_charged": total_charged,
        "failed_charge_messages": failed_charge_messages,
    }

    return result


@shared_task
def update_date_completed_for_completed_loans():
    completed_loans = AjoLoan.objects.filter(
        status=LoanStatus.COMPLETED, date_completed__isnull=True
    )

    for loan_instance in completed_loans:
        if loan_instance.total_paid_amount >= loan_instance.repayment_amount:
            repayment_qs = AjoLoanRepayment.objects.filter(ajo_loan=loan_instance)

            loan_instance.status = LoanStatus.COMPLETED

            try:
                loan_instance.date_completed = repayment_qs.latest(
                    "created_at"
                ).paid_date
            except AjoLoanRepayment.DoesNotExist:
                pass

            loan_instance.save()


@shared_task
def generate_daily_disbursements_details_report():
    today = datetime.now().date()

    if today.weekday() == 0:
        start_date = today - timedelta(days=3)
    else:
        start_date = today - timedelta(days=1)

    disbursed_loans_qs = AjoLoan.objects.filter(
        status=LoanStatus.OPEN,
        date_disbursed__date__gte=start_date,
        date_disbursed__date__lt=today,
    )

    if not disbursed_loans_qs.exists():
        return "No disbursed loans for the previous day(s)"

    report_data_list = []

    for loan in disbursed_loans_qs:
        account_details = AjoUserWithdrawalAccount.objects.filter(
            user=loan.agent, ajo_user=loan.borrower
        ).first()

        data = {
            "Loan Amount": loan.amount,
            "Date Disbursed": loan.date_disbursed.strftime("%Y-%m-%d") if loan.date_disbursed else "",
            "Loan Term": loan.tenor,
            "Date Applied": loan.created_at.strftime("%Y-%m-%d") if loan.created_at else "",
            "Loan Status": loan.status,
            "Borrower Phone Number": (
                loan.borrower.phone_number
                if loan.borrower and loan.borrower.phone_number
                else ""
            ),
            "Borrower Name": (
                loan.borrower_full_name if loan.borrower_full_name else ""
            ),
            "Account Number": account_details.account_number if account_details else "",
            "Account Name": account_details.account_name if account_details else "",
            "Supervisor Disbursement Status": loan.supervisor_disbursement_status,
        }

        report_data_list.append(data)

    df = pd.DataFrame().from_dict(report_data_list)
    file_path = os.path.join(settings.BASE_DIR, "media/loan_officers")

    try:
        os.mkdir(file_path)
    except:
        pass

    excel_report = df.to_excel(
        f"{file_path}/disbursed_loans_account_{start_date}.xlsx",
        index=False,
    )

    with open(
        f"{file_path}/disbursed_loans_account_{start_date}.xlsx",
        "rb",
    ) as read_file:
        excel_file = read_file.read()

    for email in config("DISBURSEMENT_COMPLIANCE_EMAILS").split(","):
        send_email = send_ajo_loan_officers_daily_report_email(
            message="These are the account details for the loans disbursed in the previous day(s)",
            file=excel_file,
            file_name=f"Dibursed Loans Accounts Daily Report_{start_date}.xlsx",
            email_subject="Dibursed Loans Accounts Daily Report",
            email=email,
            name="Team",
            date=datetime.now(),
        )

    return "DONE!!!"

@shared_task
def merchant_due_loan_auto_charge():
    count_charged = LoanChargeAttempt.attempt_charge()

    return str(count_charged)