import json
from typing import Dict

from adminsortable2.admin import SortableAdminMixin
from django import forms
from django.conf import settings
from django.contrib import admin, messages
from django.contrib.auth import get_user_model
from django.db.models import Avg
from django.http import HttpResponseRedirect
from django.urls import path
from django.db.models import Q
from import_export.admin import ImportExportModelAdmin

from accounts.models import (
    ActionPermission,
    ManualAdjustment,
    StaffCommissionAllocation,
)
from ajo.payment_actions import transfer_insurance_to_escrow, update_transaction_status
from ajo.selectors import AjoUserSelector
from ajo.services import BankAccountService
from loans.resources import *
from loans.tasks import (
    create_health_plan_handler,
    recreate_bulk_schedules,
    release_escrow_balance,
    update_loan_status_on_loan_disk,
    update_supervisor_device_mgmt,
)
from loans.helpers.core_banking import CoreBankingManager
from .models import Assignment, GeneralComments, FraudMarkers

admin.site.register(Assignment)
admin.site.register(GeneralComments)
admin.site.register(FraudMarkers)


User = get_user_model()


class LoanEligibilityResourceAdmin(ImportExportModelAdmin):
    resource_class = LoanEligibilityResource
    search_fields = ["ajo_user__phone_number", "agent__email"]
    list_filter = [
        "active",
        "eligibility_type",
        "requires_approval",
        "loan_type",
        "is_gliding",
        "approved",
        "verified_saver",
        "is_collected",
        "active_savings",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def toggle_savings_on_kyc(self, request, queryset: QuerySet[LoanEligibility]):
        message = "Invalid action"
        for query_instance in queryset:
            if not query_instance.active:
                message = "Currently not eligible"
                break

            ajo_user = query_instance.ajo_user
            kyc_instance = LoanKYCDocumentation.objects.filter(
                has_loan=False,
                borrower=ajo_user,
                documentation_status=DocumentationStatus.COMPLETED,
            ).last()
            if not kyc_instance:
                message = "Invalid request"
                break
            eligibility_savings_instance = query_instance.saving
            LoanKYCDocumentation.objects.filter(
                savings=eligibility_savings_instance
            ).update(savings=None)
            kyc_instance.savings = eligibility_savings_instance
            kyc_instance.save()
            query_instance.has_documentation = True
            query_instance.save()
            message = f"Updated kyc: {kyc_instance.id} - savings ID: {eligibility_savings_instance.id}"

        self.message_user(request, str(message))

    def update_top_up(self, request, queryset: QuerySet[LoanEligibility]):
        message = "Invalid action"
        for instance in queryset:
            loan_id = instance.loan_topup_id
            message = instance.get_topup_eligibility(loan_id=loan_id)

        self.message_user(request, str(message))

    toggle_savings_on_kyc.short_description = "Toggle savings instance on KYC-DOC"
    toggle_savings_on_kyc.allow_tags = True

    update_top_up.short_description = "update top-up"
    update_top_up.allow_tags = True

    actions = [
        toggle_savings_on_kyc,
        update_top_up,
    ]


class AjoLoanScheduleInline(admin.TabularInline):
    model = AjoLoanSchedule


class AjoLoanResourceResourceAdmin(ImportExportModelAdmin):
    resource_class = AjoLoanResource
    search_fields = (
        "agent__email",
        "borrower__phone_number",
        "borrower__first_name",
        "borrower__last_name",
        "escrow_liquidated",
    )
    list_filter = (
        "status",
        "performance_status",
        "loan_type",
        "pre_topup",
        "early_repayment",
        "is_disbursed",
        "created_at",
        "end_date_issue",
    )
    inlines = [AjoLoanScheduleInline]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields] + [
            "outstanding_due_today",
            "outstanding_days_today",
            "days_after_end",
        ]

    def update_with_repayment_object(self, request, queryset: QuerySet[AjoLoan]):
        """
        Updates AjoLoan instances with repayment information, including total paid amount and count of recorded repayments.
        If the total paid amount equals or exceeds the repayment amount, the loan status is updated to 'COMPLETED', and an
        asynchronous task is triggered to release escrow balance.

        Args:
            request (HttpRequest): The request object.
            queryset (QuerySet[AjoLoan]): A queryset of AjoLoan objects representing loans to be updated.

        Returns:
            None

        Side Effects:
            Updates AjoLoan instances with repayment information and may trigger an asynchronous task to release escrow balance.
        """
        response = "Invalid selection."

        for ajo_loan_instance in queryset:
            response = ajo_loan_instance.update_repayment_fields()
            # if ajo_loan_instance.status != LoanStatus.OPEN:
            #     response = f"loan id {ajo_loan_instance.id} is currently not OPEN"
            #     continue

            # # Fetching repayment records for the loan
            # repayment_qs = AjoLoanRepayment.objects.filter(
            #     ajo_loan=ajo_loan_instance, repayment_amount__gt=0
            # )

            # if not repayment_qs.exists():
            #     response = f"No repayment record for loan {ajo_loan_instance.id}"
            #     continue

            # # Calculating total paid amount and count of recorded repayments
            # total_paid_amount = (
            #     repayment_qs.aggregate(total_paid=Sum("repayment_amount"))["total_paid"]
            #     or 0
            # )
            # ajo_loan_instance.total_paid_amount = total_paid_amount
            # ajo_loan_instance.count_of_recorded_repayments = repayment_qs.count()

            # # If total paid amount exceeds or equals the repayment amount, mark the loan as COMPLETED
            # if total_paid_amount >= ajo_loan_instance.repayment_amount:
            #     ajo_loan_instance.status = LoanStatus.COMPLETED
            #     # Triggering asynchronous task to release escrow balance
            #     release_escrow_balance.delay(loan_id=ajo_loan_instance.id)
            # else:
            #     ajo_loan_instance.status = LoanStatus.OPEN
            # # Saving the updated loan instance
            # ajo_loan_instance.save()
            # response = f"sucessfully updated loan{ajo_loan_instance.id} instance"

        # Sending response message to user
        self.message_user(request, str(response))

    def refund_over_paid_loan(self, request, queryset: QuerySet[AjoLoan]):

        response = "Invalid selection"
        for ajo_loan_instance in queryset:
            if ajo_loan_instance.status != LoanStatus.COMPLETED:
                response = f"loan id {ajo_loan_instance.id} is currently not {LoanStatus.COMPLETED}"
                continue

            amount_to_refund = (
                ajo_loan_instance.total_paid_amount - ajo_loan_instance.repayment_amount
            )

            if amount_to_refund > 0:
                ajo_user = ajo_loan_instance.borrower
                # call the selector
                ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
                ajo_user_spend_wallet = ajo_user_selector.get_spending_wallet()
                quotation_id = ajo_loan_instance.unique_loan_ref
                unique_reference = f"{quotation_id}_overpaid_loan_refund"
                credit_transaction = TransactionService.create_deposit_by_wallet_transaction(
                    user=ajo_user.user,
                    amount=amount_to_refund,
                    wallet_type=ajo_user_spend_wallet.wallet_type,
                    transaction_form_type=TransactionFormType.REFUND_REPAYMENT,
                    unique_reference=unique_reference,
                    description=f"Your spend wallet has been credited with an amount of {amount_to_refund} corresponding to the overpayment on your loan..",
                    status=Status.PENDING,
                    quotation_id=quotation_id,
                    ajo_user=ajo_user,
                    plan_type=PlanType.AJO,
                )

                fund_agent_wallet(
                    transaction=credit_transaction,
                    wallet=ajo_user_spend_wallet,
                    amount=amount_to_refund,
                    unique_reference=unique_reference,
                )

                ajo_loan_instance.total_paid_amount = ajo_loan_instance.repayment_amount
                ajo_loan_instance.save()

                response = f"sucessfully updated loan{ajo_loan_instance.id} instance"
            else:
                response = (
                    f"can't refund amount less than 10: amount {amount_to_refund}"
                )

        # Sending response message to user
        self.message_user(request, str(response))

    def settle_ajoloan_escrow(self, request, queryset: QuerySet[AjoLoan]):

        for ajo_loan in queryset:
            if (
                ajo_loan.status == LoanStatus.COMPLETED
                and ajo_loan.escrow_settled == False
            ):
                ajo_user = ajo_loan.borrower
                ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
                ajo_user_escrow_wallet = ajo_user_selector.get_any_ajo_user_wallet(
                    wallet_type=WalletTypes.AJO_LOAN_ESCROW
                )
                ajo_user_spend_wallet = ajo_user_selector.get_spending_wallet()

                amount = ajo_loan.amount / 5
                user = ajo_loan.agent
                wallet_type = ajo_user_escrow_wallet.wallet_type
                transaction_form_type = (
                    TransactionFormType.AJO_LOAN_ESCROW_HOLDING_REFUND
                )
                savings = ajo_loan.eligibility.saving
                quotation_id = savings.quotation_id
                # credit_transaction_unique_reference = f"{quotation_id}_escrow_refund"
                debit_description = f"{amount} has been debit from escrow wallet of your saver: {ajo_user.phone_number}."
                credit_description = f"{amount} has been credited into the spend wallet of your saver: {ajo_user.phone_number}, as refund from escrow wallet."

                if amount > 0:
                    fully_repaid = (
                        ajo_loan.total_paid_amount >= ajo_loan.repayment_amount
                    )
                    ajo_user_repayment_records_sum = (
                        AjoLoanRepayment.objects.filter(ajo_loan=ajo_loan).aggregate(
                            total=Sum("repayment_amount")
                        )["total"]
                        or 0
                    )
                    if fully_repaid:
                        loan_available_balance = AjoLoan.check_balance()
                        if loan_available_balance >= amount:
                            loan_access_token = loan_agent_login().get("access")
                            # savings_access_token = agent_login().get("access")
                            # savings_acct_user = (
                            #     get_user_model().objects.filter(email=f"{settings.AGENCY_BANKING_USEREMAIL}").last()
                            # )
                            # savings_phone_no = AgencyBankingClass().get_user_info(
                            #     access_token=savings_access_token,
                            #     user_id=savings_acct_user.customer_user_id,
                            # )
                            # phone_number = savings_phone_no.get("data").get("phone_number")

                            # temporary fix
                            savings_user = User.objects.get(
                                email=settings.AGENCY_BANKING_USEREMAIL
                            )
                            savings_phone_number = savings_user.user_phone
                            # send buddy to savings
                            send_loan_buddy_to_savings = AgencyBankingClass.send_money_from_loans_to_savings_through_pay_buddy(
                                phone_number=savings_phone_number,
                                amount=amount,
                                transaction_reference=ajo_loan.loan_ref,
                                access_token=loan_access_token,
                            )
                            if (
                                send_loan_buddy_to_savings.get("data").get("message")
                                == "success"
                            ):
                                # debit escrow wallet
                                # debit_agent wallet
                                trans_obj = TransactionService.dynamic_deduction_from_wallet_transaction(
                                    user=user,
                                    amount=amount,
                                    wallet_type=wallet_type,
                                    transaction_form_type=transaction_form_type,
                                    quotation_id=quotation_id,
                                    ajo_user=ajo_user,
                                    description=debit_description,
                                )

                                deduct_ajo_user_escrow_wallet = (
                                    WalletSystem().deduct_balance(
                                        wallet=ajo_user_escrow_wallet,
                                        amount=amount,
                                        transaction_instance=trans_obj,
                                        onboarded_user=ajo_user,
                                    )
                                )
                                trans_obj.status = Status.SUCCESS
                                trans_obj.transaction_date_completed = timezone.now()
                                trans_obj.wallet_balance_before = (
                                    deduct_ajo_user_escrow_wallet["balance_before"]
                                )
                                trans_obj.wallet_balance_after = (
                                    deduct_ajo_user_escrow_wallet["balance_after"]
                                )
                                trans_obj.save()

                                credit_transaction = TransactionService.create_deposit_by_wallet_transaction(
                                    user=user,
                                    amount=amount,
                                    wallet_type=ajo_user_spend_wallet.wallet_type,
                                    transaction_form_type=transaction_form_type,
                                    # unique_reference=credit_transaction_unique_reference,
                                    description=credit_description,
                                    status=Status.PENDING,
                                    quotation_id=quotation_id,
                                    ajo_user=ajo_user,
                                    plan_type=PlanType.AJO,
                                )
                                fund_agent_wallet(
                                    transaction=credit_transaction,
                                    wallet=ajo_user_spend_wallet,
                                    amount=amount,
                                    # unique_reference=credit_transaction_unique_reference,
                                )
                                AjoLoan.objects.filter(id=ajo_loan.id).update(
                                    escrow_settled=True
                                )

                                self.message_user(
                                    request,
                                    f"Escrow settled uccessfully, repay_total = {ajo_user_repayment_records_sum}",
                                )
                            else:
                                self.message_user(
                                    request,
                                    f"Send to savins buddy failed try again later [response = {send_loan_buddy_to_savings}]",
                                )
                        else:
                            self.message_user(
                                request, f"Insuficient balance in loans wallet"
                            )
                    else:
                        self.message_user(
                            request,
                            f"Loan not fully paid [amount paid = {ajo_user_repayment_records_sum}]",
                        )
                else:
                    self.message_user(
                        request,
                        f"Borrower's Escrow balance is empty  [balance {ajo_user_escrow_wallet}]",
                    )
            else:
                self.message_user(
                    request, f"Loan is either not completed or escrow is settled"
                )

    def update_to_two_month_tenure(self, request, queryset: QuerySet[AjoLoan]):

        message = "Invalid action"
        for loan_instance in queryset:

            loan_calc = AjoLoan.compute_interest_with_duration(
                principal=loan_instance.amount,
                duration=60,
                loan_type=loan_instance.loan_type,
                ajouser=loan_instance.borrower,
                start_date=loan_instance.start_date,
            )

            loan_instance.tenor = Tenure.TWO_MONTH
            loan_instance.interest_amount = loan_calc["interest_amount"]
            loan_instance.interest_rate = loan_calc["interest_rate"]
            loan_instance.end_date = loan_calc["end_date"]
            loan_instance.start_date = loan_calc["start_date"]
            loan_instance.expected_repayment_count = 60
            loan_instance.repayment_amount = loan_calc["repayment_amount"]
            loan_instance.tenor_in_days = loan_calc["tenor_in_days"]
            loan_instance.daily_repayment_amount = loan_calc["daily_repayment_amount"]

            loan_instance.save(
                update_fields=[
                    "tenor",
                    "interest_amount",
                    "interest_rate",
                    "end_date",
                    "start_date",
                    "expected_repayment_count",
                    "repayment_amount",
                    "tenor_in_days",
                    "daily_repayment_amount",
                ]
            )
            message = "Success"

        self.message_user(request, message)

    def update_to_three_month_tenure(self, request, queryset: QuerySet[AjoLoan]):

        message = "Invalid action"
        for loan_instance in queryset:
            loan_calc = AjoLoan.compute_interest_with_duration(
                principal=loan_instance.amount,
                duration=90,
                loan_type=loan_instance.loan_type,
                ajouser=loan_instance.borrower,
                start_date=loan_instance.start_date,
            )
            loan_instance.tenor = Tenure.THREE_MONTH
            loan_instance.interest_amount = loan_calc["interest_amount"]
            loan_instance.interest_rate = loan_calc["interest_rate"]
            loan_instance.end_date = loan_calc["end_date"]
            loan_instance.start_date = loan_calc["start_date"]
            loan_instance.expected_repayment_count = 90
            loan_instance.repayment_amount = loan_calc["repayment_amount"]
            loan_instance.tenor_in_days = loan_calc["tenor_in_days"]
            loan_instance.daily_repayment_amount = loan_calc["daily_repayment_amount"]
            loan_instance.save(
                update_fields=[
                    "tenor",
                    "interest_amount",
                    "interest_rate",
                    "end_date",
                    "start_date",
                    "expected_repayment_count",
                    "repayment_amount",
                    "tenor_in_days",
                    "daily_repayment_amount",
                ]
            )
            message = "Success"

        self.message_user(request, message)

    def update_to_four_month_tenure(self, request, queryset: QuerySet[AjoLoan]):

        message = "Invalid action"
        for loan_instance in queryset:
            loan_calc = AjoLoan.compute_interest_with_duration(
                principal=loan_instance.amount,
                duration=120,
                loan_type=loan_instance.loan_type,
                ajouser=loan_instance.borrower,
                start_date=loan_instance.start_date,
            )
            loan_instance.tenor = Tenure.FOUR_MONTH
            loan_instance.interest_amount = loan_calc["interest_amount"]
            loan_instance.interest_rate = loan_calc["interest_rate"]
            loan_instance.end_date = loan_calc["end_date"]
            loan_instance.start_date = loan_calc["start_date"]
            loan_instance.expected_repayment_count = 120
            loan_instance.repayment_amount = loan_calc["repayment_amount"]
            loan_instance.tenor_in_days = loan_calc["tenor_in_days"]
            loan_instance.daily_repayment_amount = loan_calc["daily_repayment_amount"]

            loan_instance.save(
                update_fields=[
                    "tenor",
                    "interest_amount",
                    "interest_rate",
                    "end_date",
                    "start_date",
                    "expected_repayment_count",
                    "repayment_amount",
                    "tenor_in_days",
                    "daily_repayment_amount",
                ]
            )
            message = "Success"

        self.message_user(request, message)

    def create_repayment_from_ajo_user_spend(
        self, request, queryset: QuerySet[AjoLoan]
    ):
        from .helpers.repayments import process_loan_repayment_from_spend_wallet

        response: Dict[str, str] = {}

        for query_instance in queryset:
            ajo_user = query_instance.borrower
            response_key = ajo_user.phone_number
            spend_wallet_balance = AjoUserSelector(
                ajo_user=ajo_user
            ).get_spending_wallet_balance()
            if spend_wallet_balance <= 0:
                response[response_key] = f"there is no money to perform repayment with."
                continue

            try:
                processing = process_loan_repayment_from_spend_wallet(
                    loan=query_instance,
                    amount=spend_wallet_balance,
                )
                response[response_key] = processing
                continue

            except Exception as err:
                response[response_key] = f"error encountered: {err}"
                continue

        self.message_user(request, str(response))

    def return_overage(self, request, queryset: QuerySet[AjoLoan]):
        message = "Invalid selection"
        for query_instance in queryset:

            eligibility_instance = query_instance.eligibility
            amount_applied = query_instance.amount
            eligibility_multiplier = eligibility_instance.multiplier
            actual_escrow_value = amount_applied / eligibility_multiplier
            ajo_user = query_instance.borrower
            user = ajo_user.user
            ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
            ajo_user_escrow_wallet = ajo_user_selector.get_any_ajo_user_wallet(
                wallet_type=WalletTypes.AJO_LOAN_ESCROW
            )
            available_balance = ajo_user_escrow_wallet.available_balance

            if available_balance > actual_escrow_value:
                amount = available_balance - actual_escrow_value
                loan_access_token = loan_agent_login().get("access")
                savings_user = User.objects.get(email=settings.AGENCY_BANKING_USEREMAIL)
                savings_phone_number = savings_user.user_phone
                # send buddy to savings
                send_loan_buddy_to_savings = AgencyBankingClass.send_money_from_loans_to_savings_through_pay_buddy(
                    phone_number=savings_phone_number,
                    amount=amount,
                    transaction_reference=query_instance.loan_ref,
                    access_token=loan_access_token,
                )
                transfer_response = send_loan_buddy_to_savings.get("data").get(
                    "message"
                )
                if transfer_response == "success":

                    ajo_user_spend_wallet = ajo_user_selector.get_spending_wallet()

                    wallet_type = ajo_user_escrow_wallet.wallet_type
                    transaction_form_type = TransactionFormType.RETURN_ESCROW_OVERAGE
                    savings = query_instance.eligibility.saving
                    quotation_id = savings.quotation_id
                    credit_transaction_unique_reference = (
                        f"{quotation_id}_escrow_overage"
                    )
                    debit_description = f"{actual_escrow_value} has been debit from escrow wallet {ajo_user.phone_number}."
                    credit_description = f"An amount of {amount} has been transferred from your wallet to correct excess deposit on escrow."

                    wallet_type = ajo_user_escrow_wallet.wallet_type

                    trans_obj = (
                        TransactionService.dynamic_deduction_from_wallet_transaction(
                            user=user,
                            amount=amount,
                            wallet_type=wallet_type,
                            transaction_form_type=transaction_form_type,
                            quotation_id=quotation_id,
                            ajo_user=ajo_user,
                            description=debit_description,
                        )
                    )

                    deduct_ajo_user_escrow_wallet = WalletSystem().deduct_balance(
                        wallet=ajo_user_escrow_wallet,
                        amount=amount,
                        transaction_instance=trans_obj,
                        onboarded_user=ajo_user,
                    )

                    trans_obj.status = Status.SUCCESS
                    trans_obj.transaction_date_completed = timezone.now()
                    trans_obj.wallet_balance_before = deduct_ajo_user_escrow_wallet[
                        "balance_before"
                    ]
                    trans_obj.wallet_balance_after = deduct_ajo_user_escrow_wallet[
                        "balance_after"
                    ]
                    trans_obj.save()

                    credit_transaction = (
                        TransactionService.create_deposit_by_wallet_transaction(
                            user=user,
                            amount=amount,
                            wallet_type=ajo_user_spend_wallet.wallet_type,
                            transaction_form_type=transaction_form_type,
                            unique_reference=credit_transaction_unique_reference,
                            description=credit_description,
                            status=Status.PENDING,
                            quotation_id=quotation_id,
                            ajo_user=ajo_user,
                            plan_type=PlanType.AJO,
                        )
                    )

                    fund_agent_wallet(
                        transaction=credit_transaction,
                        wallet=ajo_user_spend_wallet,
                        amount=amount,
                        unique_reference=credit_transaction_unique_reference,
                    )
                    message = transfer_response

                else:
                    message = f"{send_loan_buddy_to_savings}"
            else:
                message = f"AVAIL-BAL: {available_balance} - ESCROW ACTUAL VAL: {actual_escrow_value}"
        self.message_user(request, message)

    def update_loan_schedules(self, request, queryset: QuerySet[AjoLoan]):

        ids = [loan.id for loan in queryset if loan.status == "OPEN"]
        recreate_bulk_schedules.delay(ids)

    def trigger_repayment_from_escrow(self, request, queryset: QuerySet[AjoLoan]):
        """
        Trigger repayment from the escrow wallet of the ajo user
        """
        from .helpers.repayments import process_loan_repayment_from_escrow_wallet

        response: Dict[str, str] = {}

        for query_instance in queryset:
            response_key = query_instance.borrower.phone

            if query_instance.status in [LoanStatus.COMPLETED, LoanStatus.CLOSED]:
                response[response_key] = "Loan is either closed or completed"
                continue

            try:
                processing = process_loan_repayment_from_escrow_wallet(
                    loan=query_instance,
                )
                response[response_key] = processing
                continue

            except Exception as err:
                response[response_key] = f"error encountered: {err}"
                continue

        self.message_user(request, str(response))

    def update_bnpl_loan_amount_fields(self, request, queryset: QuerySet[AjoLoan]):
        message = ""

        for query_instance in queryset:
            savings_instance = query_instance.eligibility.saving
            print(savings_instance.savings_type, "\n\n")
            if savings_instance.savings_type == "BNPL":
                savings_id = savings_instance.id
                amount_summary = AjoLoan.calculate_interest(
                    principal_amount=10000,  # default amount not always referenced
                    savings_id=savings_id,
                )
                AjoLoan.objects.filter(id=query_instance.id).update(
                    interest_rate=amount_summary.get("interest_rate"),
                    interest_amount=amount_summary.get("interest_amount"),
                    processing_fee=amount_summary.get("loan_processing_fee"),
                    repayment_amount=amount_summary.get("repayment_amount"),
                    bnpl_amount=amount_summary.get("bnpl_amount", 0.0),
                    amount=amount_summary.get("principal_amount"),
                    agent_disbursement_amount=amount_summary.get(
                        "agent_disbursement_amount", 0.0
                    ),
                    tenor=Tenure.THREE_MONTH,
                    loan_type=savings_instance.savings_type,
                    daily_repayment_amount=amount_summary.get("daily_repayment_amount"),
                    tenor_in_days=amount_summary.get("tenor_in_days"),
                )

                message = amount_summary
            else:
                message = "Not a Valid BNPL Savings Type"

        self.message_user(request, str(message))

    def allow_edit_borrower_info(self, request, queryset: QuerySet[AjoLoan]):
        message = "Invalid Action"

        for instance in queryset:
            if instance.verification_stage == VerificationStage.FACE_MATCH:
                instance.verification_stage = VerificationStage.BORROWER_INFO
                instance.save()
                message = "success"
            else:
                message = f"unable to change status from {instance.verification_stage} => {VerificationStage.BORROWER_INFO}"

        self.message_user(request, str(message))

    def allow_edit_guarantor(self, request, queryset: QuerySet[AjoLoan]):
        message = "Invalid Action"

        for instance in queryset:
            if instance.verification_stage == VerificationStage.GUARANTOR_CAPTURE:
                instance.verification_stage = VerificationStage.GUARANTOR
                instance.save()
                message = "success"
            else:
                message = f"unable to change status from {instance.verification_stage} => {VerificationStage.GUARANTOR}"

        self.message_user(request, str(message))

    def update_to_supervisor(self, request, queryset: QuerySet[AjoLoan]):
        message = "Invalid Action"

        for instance in queryset:
            ajo_user_selector = AjoUserSelector(ajo_user=instance.borrower)
            ajo_user_disbursment_wallet = (
                ajo_user_selector.get_loan_disbursement_wallet()
            )

            loan_type = instance.loan_type
            boosta_xtype = ["BOOSTA_2X", "BOOSTA_2X_MINI", "CREDIT_HEALTH"]

            if loan_type in boosta_xtype:
                loan_calc = AjoLoan.compute_interest_with_duration(
                    principal=instance.amount,
                    duration=120,  # ignore duration this method helps just re-calculate the expected disbursement amount
                    loan_type=instance.loan_type,
                    ajouser=instance.borrower,
                    start_date=instance.start_date,
                )
                _amount = loan_calc.get("disbursement_amount")
            else:
                _amount = instance.amount

            _debug_info = {
                "verification_stage": instance.verification_stage,
                "status": instance.status,
                "available_balance": ajo_user_disbursment_wallet.available_balance,
                "disbursement_amount": _amount,
            }

            if (
                instance.verification_stage == VerificationStage.DISBURSEMENT
                and instance.status == LoanStatus.APPROVED
                and ajo_user_disbursment_wallet.available_balance >= _amount
            ):

                instance.verification_stage = VerificationStage.SUPERVISOR_DISBURSEMENT
                instance.status = LoanStatus.OPEN_TO_SUPERVISOR
                instance.is_disbursed = True
                instance.save()
                message = "success"
            else:
                message = f"MSG: Error --- {_debug_info}"

        self.message_user(request, str(message))

    def update_daily_allocation(self, request, queryset: QuerySet[AjoLoan]):
        message = "Invalid Action"

        for instance in queryset:
            health_insurance = HealthInsurance.objects.filter(loan=instance)
            if health_insurance.exists():  # update insurance

                try:
                    daily_repayment_count = (
                        instance.total_paid_amount / instance.daily_repayment_amount
                    )
                    actual_count = math.floor(daily_repayment_count)
                except ZeroDivisionError:
                    actual_count = 0
                const = ConstantTable.get_constant_table_instance()
                if actual_count > 0:
                    total_allocation = const.health_insurance_daily_fee * actual_count
                    instance.daily_health_allocation = total_allocation
                    insurance_instance = health_insurance.last()
                    insurance_instance.daily_allocation = total_allocation
                    insurance_instance.save()
                    instance.save()
                    message = f"success. amount: {total_allocation}"
                else:
                    message = "ZeroDivisionError"
            else:
                message = "Insurance instance DoesNotExist"

        self.message_user(request, str(message))

    def update_related_boosta_loan(self, request, queryset: QuerySet[AjoLoan]):
        message = "Invalid Action"

        for instance in queryset:
            # health_insurance = HealthInsurance.objects.filter(loan=instance)
            eligibility = instance.eligibility
            savings = eligibility.saving

            if savings.savings_type == SavingsType.BOOSTA:
                quotation_id = savings.quotation_id

                get_plan_transactions = Transaction.objects.filter(
                    quotation_id=quotation_id,
                    status=Status.SUCCESS,
                    wallet_type=WalletTypes.AJO_USER,
                    transaction_form_type=TransactionFormType.WALLET_DEPOSIT,
                )
                # get total boosta savings
                sum_of_amount_saved = (
                    get_plan_transactions.aggregate(sum_amount=Sum("amount"))[
                        "sum_amount"
                    ]
                    or 0
                )

                constant = ConstantTable.get_constant_table_instance()
                # admin_cap_amount = constant.max_loan_amount
                const_value = constant.boosta_loan_config

                # get expected loan amount
                loan_tenure = savings.boosta_tenure
                usable_const = const_value.get(str(loan_tenure))
                interest_percent = float(usable_const["spreadable_interest"] / 100)

                # amount_saved = savings.amount_saved
                # savings_expected_amount = savings.expected_amount
                eligible_amount_from_amount_saved = (
                    sum_of_amount_saved / interest_percent
                )

                elibible_amount = (
                    math.floor(eligible_amount_from_amount_saved / 1000) * 1000
                )
                # confirm if expected amount == amount disbursed
                # print(instance.loan_ref, "\n\n")
                amount_disbursed = (
                    Transaction.objects.filter(
                        quotation_id=instance.loan_ref,
                        transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
                        status=Status.SUCCESS,
                        wallet_type=WalletTypes.LOAN_DISBURSEMENT,
                        transaction_form_type=TransactionFormType.AJO_LOAN_DISBURSEMENT,
                    ).aggregate(sum_amount=Sum("amount"))["sum_amount"]
                    or 0
                )

                if elibible_amount == amount_disbursed:
                    ########################################################################
                    ########################################################################
                    ########################################################################

                    ajo_user_selector = AjoUserSelector(ajo_user=savings.ajo_user)
                    ajo_user = savings.ajo_user
                    agent_user = ajo_user.user

                    # Obtain the Ajo user's wallet and balance
                    ajo_user_wallet = ajo_user_selector.get_ajo_user_wallet()
                    # get savings available balance
                    ajouser_available_balance = ajo_user_wallet.available_balance

                    # get Escrow holding balance

                    ajo_user_escrow_wallet = ajo_user_selector.get_any_ajo_user_wallet(
                        wallet_type=WalletTypes.AJO_LOAN_ESCROW
                    )

                    # ajo_user_spend_wallet = ajo_user_selector.get_spending_wallet()
                    # escrow balance
                    escrow_available_balance = ajo_user_escrow_wallet.available_balance

                    # get balance to confirm actual value that made the user eligible
                    actual_balance = sum_of_amount_saved - escrow_available_balance

                    boosta_actual_value = sum_of_amount_saved
                    savings_debit_description = ""
                    expected_amount_to_charge_savings_wallet = (
                        sum_of_amount_saved - escrow_available_balance
                    )
                    if (
                        ajouser_available_balance > 0
                        and ajouser_available_balance
                        >= expected_amount_to_charge_savings_wallet
                    ):

                        # # If savings wallet balance is insufficient, attempt to debit the spending wallet
                        # ajo_user_spend_wallet = ajo_user_selector.get_spending_wallet()

                        # move funds from escrow to Boosta comission(validate amount in escrow == amount expected to fund boosta commission)

                        ########################################################################
                        ########################################################################
                        # ----------------------------CHARGE SAVINGS---------------------------#
                        ########################################################################
                        ########################################################################

                        transaction_form_type = (
                            TransactionFormType.AJO_LOAN_ESCROW_HOLDING
                        )

                        savings_debit_description = f"This transaction debits {expected_amount_to_charge_savings_wallet} to complete a previous debit where the total amount was not fully deducted."

                        debit_transaction = (
                            TransactionService.create_ajo_commissions_transaction(
                                user=savings.user,
                                amount=expected_amount_to_charge_savings_wallet,
                                quotation_id=savings.quotation_id,
                                ajo_user=savings.ajo_user,
                                transaction_description=savings_debit_description,
                                transaction_form_type=transaction_form_type,
                                wallet_type=ajo_user_wallet.wallet_type,
                            )
                        )

                        # Debit the savings wallet
                        debit_ajo_plan(
                            ajo_savings=savings,
                            amount=expected_amount_to_charge_savings_wallet,
                            wallet=ajo_user_wallet,
                            transaction_instance=debit_transaction,
                        )
                    else:
                        savings_debit_description = "No savings balance"
                    if escrow_available_balance > 0:

                        ########################################################################
                        ########################################################################
                        # ----------------------------CHARGE ESCROW-----------------------------#
                        ########################################################################
                        ########################################################################

                        escrow_debit_description = f"Debit Escrow to correct the error in the transfer, which was sent to Escrow instead of Boosta Commission."
                        escrow_debit_transaction_instance = TransactionService.dynamic_deduction_from_wallet_transaction(
                            user=agent_user,
                            amount=escrow_available_balance,
                            wallet_type=ajo_user_escrow_wallet.wallet_type,
                            transaction_form_type=transaction_form_type,
                            quotation_id=quotation_id,
                            ajo_user=ajo_user,
                            description=escrow_debit_description,
                        )

                        deduct_ajo_user_escrow_wallet = WalletSystem().deduct_balance(
                            wallet=ajo_user_escrow_wallet,
                            amount=escrow_available_balance,
                            transaction_instance=escrow_debit_transaction_instance,
                            onboarded_user=ajo_user,
                        )

                        escrow_debit_transaction_instance.status = Status.SUCCESS
                        escrow_debit_transaction_instance.transaction_date_completed = (
                            timezone.now()
                        )
                        escrow_debit_transaction_instance.wallet_balance_before = (
                            deduct_ajo_user_escrow_wallet["balance_before"]
                        )
                        escrow_debit_transaction_instance.wallet_balance_after = (
                            deduct_ajo_user_escrow_wallet["balance_after"]
                        )
                        escrow_debit_transaction_instance.save()

                        ########################################################################
                        ########################################################################
                        # -------------------ATTEMPT CREDIT BOOSTA COMMISSION-------------------#
                        ########################################################################
                        ########################################################################

                        credit_description = f"{boosta_actual_value} commission, from BOOSTA loans {ajo_user.phone_number}"
                        transaction_form_type = TransactionFormType.COMMISSION
                        quotation_id = str(instance.unique_loan_ref)

                        commission_transaction_exist = Transaction.objects.filter(
                            wallet_type=WalletTypes.BOOSTA_COMMISSION,
                            transaction_form_type=transaction_form_type,
                            quotation_id=quotation_id,
                        ).exists()
                        if not commission_transaction_exist:

                            boosta_commission_wallet = AjoAgentSelector(
                                user=agent_user
                            ).get_agent_ajo_wallet(
                                wallet_type=WalletTypes.BOOSTA_COMMISSION
                            )

                            credit_transaction = (
                                TransactionService.create_deposit_by_wallet_transaction(
                                    user=agent_user,
                                    amount=boosta_actual_value,
                                    wallet_type=WalletTypes.BOOSTA_COMMISSION,
                                    transaction_form_type=transaction_form_type,
                                    unique_reference=quotation_id,
                                    description=credit_description,
                                    status=Status.PENDING,
                                    quotation_id=quotation_id,
                                    ajo_user=ajo_user,
                                    plan_type=PlanType.AJO,
                                )
                            )

                            fund_agent_wallet(
                                transaction=credit_transaction,
                                wallet=boosta_commission_wallet,
                                amount=boosta_actual_value,
                                unique_reference=quotation_id,
                            )

                        # update eligibility and loan type
                        # ELIGIBILITY
                        eligibility.amount = elibible_amount
                        eligibility.loan_type = SavingsType.BOOSTA
                        eligibility.save()

                        # LOAN

                        interest_helper = AjoLoan.compute_interest_with_duration(
                            principal=elibible_amount,
                            duration=90,
                            loan_type="BOOSTA",
                            ajouser=ajo_user,
                            start_date=instance.start_date,
                        )

                        instance.interest_rate = interest_helper.get("interest_rate")
                        instance.start_date = interest_helper.get("start_date")
                        instance.end_date = interest_helper.get("weekly_repayment")
                        instance.weekly_repayment = interest_helper.get(
                            "weekly_repayment"
                        )
                        instance.principal_daily_repayment = interest_helper.get(
                            "principal_daily_repayment"
                        )
                        instance.principal_repayment = interest_helper.get(
                            "principal_repayment"
                        )
                        instance.interest_amount = interest_helper.get(
                            "interest_amount"
                        )
                        instance.processing_fee = interest_helper.get(
                            "loan_processing_fee"
                        )
                        instance.repayment_amount = interest_helper.get(
                            "repayment_amount"
                        )
                        # instance.total_outstanding_balance = interest_helper.get(
                        #     "repayment_amount"
                        # )
                        instance.bnpl_amount = interest_helper.get("bnpl_amount", 0.0)
                        instance.amount = interest_helper.get("principal_amount")
                        instance.agent_disbursement_amount = interest_helper.get(
                            "agent_disbursement_amount", 0.0
                        )
                        instance.tenor = Tenure.THREE_MONTH
                        instance.loan_type = LoanType.BOOSTA
                        instance.daily_repayment_amount = interest_helper.get(
                            "daily_repayment_amount"
                        )
                        instance.tenor_in_days = interest_helper.get("tenor_in_days")
                        instance.save()

                        message = f"success:\n{savings_debit_description} ::::: {escrow_debit_transaction_instance} ::::: {credit_description}"
                    else:
                        message = f"Escrow balance: {escrow_available_balance}"
                else:
                    message = f"Eligible amount: {elibible_amount} is not equal to amount disbursed: {amount_disbursed}"

            else:
                message = f"Savings type {savings.savings_type}"

        self.message_user(request, str(message))

    def update_crc_status(self, request, queryset: QuerySet[AjoLoan]):
        message = "Invalid Action"

        for instance in queryset:
            if instance.verification_stage != VerificationStage.FACE_MATCH:
                message = f"unable to verify crc status on {instance.verification_stage} verification Stage"
                continue
            try:
                borrower_info_instance = BorrowerInfo.objects.get(loan=instance)
            except BorrowerInfo.DoesNotExist:
                message = "Borrower information has not being created for selected loan instance"
                continue

            if not borrower_info_instance.base_64_img_string:
                message = "Borrower information (NIN/BVN) has not being verified"
                continue

            biometrics_log = BiometricsMetaData.objects.filter(
                borrower_info=borrower_info_instance,
                user_type="BORROWER",
                base64_img_str__isnull=False,
            ).last()
            if not biometrics_log:
                message = "Borrower has no captured image"
                continue

            borrower_info_instance.face_match = True
            borrower_info_instance.notified = True
            borrower_info_instance.snapped_image = biometrics_log.base64_img_str
            borrower_info_instance.save()
            instance.face_match = True
            instance.save()

            verification_number = borrower_info_instance.verification_number
            borrower = instance.borrower

            constant = ConstantTable.get_constant_table_instance()
            debt_threshold = constant.debt_threshold
            debt_institution_count = constant.debt_institution_count

            if borrower_info_instance.verification_type == "NIN":
                verification_number = borrower.phone

            credit_bureau = FirstcentralCreditCheck(
                bvn=verification_number,
                debt_threshold=debt_threshold,
                ajo_user=borrower,
                max_debt_institution_count=debt_institution_count,
            ).get_credit_status()

            borrower_info_instance.credit_bureau = json.dumps(credit_bureau)
            borrower_info_instance.save()

            credit_bureau_status = credit_bureau.get("status")
            message = credit_bureau.get("reason")

            ManualAdjustment.create_adjusment_record(
                model_instance=instance,
                email=request.user.email,
                phone_number=instance.borrower.phone,
                correction_type="crc_manual_update",
                reason="Action Triggered by admin",
                notes=message,
                status="SUCCESS" if credit_bureau_status else "FAILED",
            )
            if isinstance(credit_bureau_status, bool):

                bad_loans_institions_count = credit_bureau.get(
                    "bad_loans_institions_count"
                )
                high_outstanding_debt = credit_bureau.get("high_outstanding_debt")
                if credit_bureau_status:

                    instance.status = LoanStatus.PROCESSING
                    instance.verification_stage = VerificationStage.GUARANTOR
                    instance.save()
                    borrower_info_instance.is_verified = True
                    borrower_info_instance.save()
                    update_loan_status_on_loan_disk.delay(
                        loan_id=instance.id, loan_status=VerificationStage.GUARANTOR
                    )
                else:
                    instance.status = LoanStatus.AWAITING_CRC
                    instance.verification_stage = VerificationStage.CREDIT_BUREAU
                    instance.save()

                    update_loan_status_on_loan_disk.delay(
                        loan_id=instance.id,
                        loan_status=VerificationStage.CREDIT_BUREAU,
                    )
                    # update borrower info with first central response
                borrower_info_instance.open_loan_count = credit_bureau.get(
                    "count_of_open_loans"
                )
                borrower_info_instance.bad_loan_count = credit_bureau.get(
                    "bad_loans_institions_count"
                )
                borrower_info_instance.high_outstanding_debt = credit_bureau.get(
                    "high_outstanding_debt"
                )
                borrower_info_instance.save()

            else:
                message = "Unable to verify credit worthiness"

        self.message_user(request, str(message))

    def approve_loan(self, request, queryset: QuerySet[AjoLoan]):
        message = "Invalid Action"

        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:

            for instance in queryset:
                if instance.verification_stage != VerificationStage.GUARANTOR_CAPTURE:
                    message = f"unable to Approve loan on {instance.verification_stage} verification Stage"
                    continue
                try:
                    borrower_info_instance = BorrowerInfo.objects.get(loan=instance)
                except BorrowerInfo.DoesNotExist:
                    message = "Borrower information has not being created for selected loan instance"
                    continue

                guarantor_instance = instance.guarantor

                if not guarantor_instance:
                    message = "No guarantor associated with loan"
                    continue

                if not guarantor_instance.base_64_img_string:
                    message = "Guarantor information (NIN/BVN) has not being verified"
                    continue

                biometrics_log = BiometricsMetaData.objects.filter(
                    borrower_info=borrower_info_instance,
                    user_type="GUARANTOR",
                    base64_img_str__isnull=False,
                ).last()

                if not biometrics_log:
                    message = "Guarantor has no captured image"
                    continue

                guarantor_instance.is_verified = True
                guarantor_instance.notified = True
                guarantor_instance.snapped_image = biometrics_log.base64_img_str
                guarantor_instance.save()
                instance.verification_stage = VerificationStage.DISBURSEMENT
                instance.status = LoanStatus.APPROVED
                instance.save()
                message = "success"
                ManualAdjustment.create_adjusment_record(
                    model_instance=instance,
                    email=request.user.email,
                    phone_number=instance.borrower.phone,
                    correction_type="loan",
                    reason="Action Triggered by admin",
                    notes=message,
                    status="SUCCESS",
                )

        self.message_user(request, str(message))

    def update_loan(self, request, queryset: QuerySet[AjoLoan]):
        message = "Invalid Action"

        for instance in queryset:
            savings = instance.eligibility.saving
            boosta_savings = ["BOOSTA", "BOOSTA_2X"]
            savings_type = savings.savings_type

            if savings_type in boosta_savings:
                duration = savings.boosta_tenure
            else:
                loan_tenure = dict(Tenure.choices)
                tenure_value = loan_tenure.get(str(instance.tenor))
                duration = int(tenure_value)

            result = AjoLoan.compute_interest_with_duration(
                start_date=instance.start_date,
                duration=duration,
                loan_type=savings.savings_type,
                ajouser=instance.borrower,
                principal=instance.amount,
            )

            # instance.start_date = result.get("start_date")
            # instance.end_date = result.get("end_date")
            instance.tenor = result.get("duration_label")
            instance.insurance_fee_on_repayment = result.get(
                "insurance_fee_on_repayment"
            )
            instance.const_insurance_value = result.get("insurance_renewal_fee")
            instance.tenor_in_days = result.get("tenor_in_days")
            instance.daily_repayment_amount = result.get("daily_repayment_amount")
            instance.total_outstanding_balance = result.get("repayment_amount")
            instance.principal_daily_repayment = result.get("principal_daily_repayment")
            instance.principal_repayment = result.get("principal_repayment")
            instance.save()
            message = "succes"

        self.message_user(request, str(message))

    def manual_escrow_update(self, request, queryset: QuerySet[AjoLoan]):
        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:
            for instance in queryset:
                if instance.loan_type not in [
                    "BOOSTA_2X",
                    "BOOSTA_2X_MINI",
                    "CREDIT_HEALTH",
                ]:
                    message = "invalid boosta loan type."
                    continue
                if not instance.is_disbursed:
                    message = "loan instance is yet to be disbursed."
                    continue

                message = transfer_insurance_to_escrow(
                    loan=instance, savings=instance.eligibility.saving
                )

        self.message_user(request, str(message))

    def update_with_savings_tenure(self, request, queryset: QuerySet[AjoLoan]):
        message = "Invalid Action"

        for instance in queryset:
            instance.update_loan_summary()
            message = "success"

        self.message_user(request, str(message))

    def get_topup_summary(self, request, queryset: QuerySet[AjoLoan]):
        message = "Invalid Action"

        for instance in queryset:
            message = LoanEligibility.get_topup_eligibility(
                loan_id=instance.id, get_summary=True
            )

        self.message_user(request, str(message))

    def manual_comm_transfer(self, request, queryset: QuerySet[AjoLoan]):
        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:

            for instance in queryset:
                if instance.loan_type not in [
                    "BOOSTA_2X",
                    "BOOSTA_2X_MINI",
                    "CREDIT_HEALTH",
                    "AJO",
                ]:
                    message = f"invalid loan type {instance.loan_type}."
                    continue
                if instance.status != LoanStatus.OPEN:
                    message = f"invalid loan status {instance.status}."
                    continue

                message = StaffCommissionAllocation.create_commission_distribution(
                    loan_id=instance.id
                )  # commission allocation
        self.message_user(request, str(message))

    def detect_dibursed_but_open_to_supervisor_loans(
        self, request, queryset: QuerySet[AjoLoan]
    ):
        for loan in queryset:
            # if loan.status == LoanStatus.OPEN:
            #     continue

            response_message = {}
            loan: AjoLoan
            onboarded_user = loan.borrower

            disbursement_transactions = Transaction.objects.filter(
                onboarded_user=onboarded_user,
                status=Status.SUCCESS,
                transaction_form_type=TransactionFormType.DISBURSEMENT_TO_SPEND,
            )

            # data = AjoLoan.calculate_interest(
            #     principal_amount=loan.amount,
            #     loan_tenure=loan.tenor,
            #     savings_id=loan.eligibility.saving.id,
            # )

            if disbursement_transactions:
                loan.status = LoanStatus.OPEN
                loan.is_disbursed = True
                loan.date_disbursed = disbursement_transactions.first().date_created
                # loan.start_date = data.get("start_date")
                # loan.end_date = data.get("end_date")
                # loan.tenor_in_days = data.get("tenor_in_days")
                # loan.daily_repayment_amount = data.get("daily_repayment_amount")
                loan.supervisor_disbursement_status = DisbursementStatus.SUCCESSFUL
                loan.number_of_spend_disbursements = disbursement_transactions.count()
                loan.save()

                response_message[f"{loan.id}"] = "updated successfully"

        self.message_user(request, response_message)

    def update_status_to_guarantor_capture(self, request, queryset: QuerySet[AjoLoan]):
        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:
            for instance in queryset:
                if instance.status not in [
                    LoanStatus.PROCESSING,
                    LoanStatus.APPROVED,
                    LoanStatus.PENDING,
                ]:
                    message = f"Invalid loan status: {instance.status}"
                    continue

                try:
                    guarantor = LoanGuarantor.objects.get(borrower_info__loan=instance)
                except LoanGuarantor.DoesNotExist:
                    message = "No guarantor found for this loan"
                    continue
                except LoanGuarantor.MultipleObjectsReturned:
                    message = "Multiple guarantors found for this loan"
                    continue

                if not guarantor.is_verified:
                    message = "Guarantor is not verified"
                    continue

                if not guarantor.borrower_info.is_verified:
                    message = "Borrower info is not verified"
                    continue

                # Update the records
                guarantor.is_verified = False
                guarantor.save()

                instance.status = LoanStatus.PROCESSING
                instance.verification_stage = VerificationStage.GUARANTOR_CAPTURE
                instance.save()

                message = "success"

        self.message_user(request, str(message))

    def reject_ajoloan(self, request, queryset: QuerySet[AjoLoan]):
        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:
            for instance in queryset:
                instance.status = LoanStatus.REJECTED
                instance.save()
                message = "success"

        self.message_user(request, str(message))

    def toggle_open(self, request, queryset: QuerySet[AjoLoan]):
        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:
            for instance in queryset:
                instance.status = LoanStatus.OPEN
                instance.save()
                message = "success"

        self.message_user(request, str(message))

    def reject_ajoloan_refund_full_deposit(self, request, queryset: QuerySet[AjoLoan]):
        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:
            for loan_instance in queryset:
                if (
                    loan_instance.status != LoanStatus.OPEN_TO_SUPERVISOR
                    or loan_instance.verification_stage
                    != VerificationStage.SUPERVISOR_DISBURSEMENT
                ):
                    message = f"Loan cannot be refunded as it is not in the correct status ({loan_instance.status}) or verification stage ({loan_instance.verification_stage})"
                    continue
                ajo_user = loan_instance.borrower
                agent = ajo_user.user

                loan_type = loan_instance.loan_type

                xtype_boosta = ["BOOSTA_2X", "BOOSTA_2X_MINI", "CREDIT_HEALTH"]

                savings_loan_types = [
                    "BOOSTA",
                    "BOOSTA_2X",
                    "BOOSTA_2X_MINI",
                    "CREDIT_HEALTH",
                ]
                eligibility_instance = loan_instance.eligibility
                savings = eligibility_instance.saving
                amount_applied = loan_instance.amount
                eligibility_multiplier = eligibility_instance.multiplier

                ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)

                if savings.savings_type in savings_loan_types:
                    actual_escrow_value = eligibility_instance.amount_saved
                else:
                    actual_escrow_value = amount_applied / eligibility_multiplier

                wallet_type = (
                    WalletTypes.BOOSTA_2x_COMMISSION
                    if loan_type in xtype_boosta
                    else WalletTypes.BOOSTA_COMMISSION
                )

                # BOOSTA COMMISION WALLET
                boosta_commission_wallet = AjoAgentSelector(
                    user=loan_instance.agent
                ).get_agent_ajo_wallet(wallet_type=wallet_type)

                # ESCROW WALLET
                ajo_user_escrow_wallet = ajo_user_selector.get_any_ajo_user_wallet(
                    wallet_type=WalletTypes.AJO_LOAN_ESCROW
                )
                # DISBURSEMENT WALLET
                ajo_user_disbursment_wallet = (
                    ajo_user_selector.get_loan_disbursement_wallet()
                )

                loan_calc = AjoLoan.compute_interest_with_duration(
                    principal=loan_instance.amount,
                    duration=loan_instance.actual_duaration,
                    loan_type=loan_instance.loan_type,
                    ajouser=loan_instance.borrower,
                    start_date=loan_instance.start_date,
                )

                boosta_insurance = loan_calc.get("boosta_insurance", 0)
                disburse_amount = loan_calc.get("disbursement_amount")

                quotation_id = savings.quotation_id

                debit_uniqe_ref = f"{quotation_id}_commission_debit_for_refund"

                # Check if debit transaction already exists
                if Transaction.objects.filter(
                    unique_reference=debit_uniqe_ref
                ).exists():
                    message = f"Debit transaction with reference {debit_uniqe_ref} already exists. Skipping."
                    continue

                # Perform all balance checks
                if boosta_commission_wallet.available_balance < actual_escrow_value:
                    message = f"Insufficient balance in Boosta commission wallet. Available: {boosta_commission_wallet.available_balance}, Required: {actual_escrow_value}."
                    continue
                if ajo_user_escrow_wallet.available_balance < boosta_insurance:
                    message = f"Insufficient balance in Ajo user escrow wallet. Available: {ajo_user_escrow_wallet.available_balance}, Required: {boosta_insurance}."
                    continue
                if ajo_user_disbursment_wallet.available_balance < disburse_amount:
                    message = f"Insufficient balance in Ajo user disbursement wallet. Available: {ajo_user_disbursment_wallet.available_balance}, Required: {disburse_amount}."
                    continue

                # Proceed with deductions
                transaction_form_type = TransactionFormType.COMMISSION

                # Deduct from Boosta commission wallet
                debit_transaction = (
                    TransactionService.dynamic_deduction_from_wallet_transaction(
                        user=agent,
                        amount=actual_escrow_value,
                        wallet_type=boosta_commission_wallet.wallet_type,
                        request_data=None,
                        unique_reference=debit_uniqe_ref,
                        quotation_id=quotation_id,
                        description="Debit to refund full amount to escrow",
                        ajo_user=ajo_user,
                        transaction_form_type=transaction_form_type,
                    )
                )

                deduct_agent_wallet = WalletSystem.deduct_balance(
                    wallet=boosta_commission_wallet,
                    amount=actual_escrow_value,
                    transaction_instance=debit_transaction,
                    onboarded_user=ajo_user,
                )

                # Update the transaction status
                debit_transaction.status = Status.SUCCESS
                debit_transaction.transaction_date_completed = timezone.now()
                debit_transaction.wallet_balance_before = deduct_agent_wallet[
                    "balance_before"
                ]
                debit_transaction.wallet_balance_after = deduct_agent_wallet[
                    "balance_after"
                ]
                debit_transaction.save()

                # Deduct from Ajo user escrow wallet
                debit_transaction = TransactionService.dynamic_deduction_from_wallet_transaction(
                    user=agent,
                    amount=boosta_insurance,
                    wallet_type=ajo_user_escrow_wallet.wallet_type,
                    request_data=None,
                    quotation_id=savings.quotation_id,
                    description="Debit initial escrow to refund full amount which was initially saved",
                    ajo_user=ajo_user,
                    transaction_form_type=transaction_form_type,
                )

                deduct_agent_wallet = WalletSystem.deduct_balance(
                    wallet=ajo_user_escrow_wallet,
                    amount=boosta_insurance,
                    transaction_instance=debit_transaction,
                    onboarded_user=ajo_user,
                )

                # Update the transaction status
                debit_transaction.status = Status.SUCCESS
                debit_transaction.transaction_date_completed = timezone.now()
                debit_transaction.wallet_balance_before = deduct_agent_wallet[
                    "balance_before"
                ]
                debit_transaction.wallet_balance_after = deduct_agent_wallet[
                    "balance_after"
                ]
                debit_transaction.save()

                # Deduct from Ajo user disbursement wallet
                debit_transaction = TransactionService.dynamic_deduction_from_wallet_transaction(
                    user=agent,
                    amount=disburse_amount,
                    wallet_type=ajo_user_disbursment_wallet.wallet_type,
                    request_data=None,
                    quotation_id=savings.quotation_id,
                    description="charge disbursement wallet against intial amount disbursed, to refund full amount which was initially saved{actual_escrow_value}",
                    ajo_user=ajo_user,
                    transaction_form_type=transaction_form_type,
                )

                deduct_agent_wallet = WalletSystem.deduct_balance(
                    wallet=ajo_user_disbursment_wallet,
                    amount=disburse_amount,
                    transaction_instance=debit_transaction,
                    onboarded_user=ajo_user,
                )

                # Update the transaction status
                debit_transaction.status = Status.SUCCESS
                debit_transaction.transaction_date_completed = timezone.now()
                debit_transaction.wallet_balance_before = deduct_agent_wallet[
                    "balance_before"
                ]
                debit_transaction.wallet_balance_after = deduct_agent_wallet[
                    "balance_after"
                ]
                debit_transaction.save()

                # Fund the escrow wallet
                loan_instance.fund_escrow_wallet(
                    borrower=loan_instance.borrower,
                    wallet=ajo_user_escrow_wallet,
                    amount=actual_escrow_value,
                    transaction_description=f"{actual_escrow_value} was deposited to escrow wallet from initial loan which was rejected.",
                    transaction_form_type=TransactionFormType.AJO_LOAN_ESCROW_HOLDING,
                    quotation_id=str(loan_instance.unique_loan_ref),
                )
                loan_instance.status = LoanStatus.REJECTED
                loan_instance.save()
                message = "success"

        self.message_user(request, str(message))

    def refund_non_refundable_balance(self, request, queryset: QuerySet[AjoLoan]):
        """
        This method processes refunds for non-refundable balances of declined loans.

        It performs the following steps:
        1. Checks if the user has permission to perform this action.
        2. Iterates through the queryset of AjoLoan instances.
        3. Verifies if the loan status is DECLINED_BY_SUPERVISOR.
        4. Calculates the actual escrow value based on the loan type.
        5. Retrieves the appropriate commission wallet (BOOSTA or BOOSTA_2x).
        6. Computes the non-refundable amount.
        7. Checks for existing transactions to avoid duplicates.
        8. Verifies sufficient balance in the commission wallet.
        9. Creates a debit transaction from the commission wallet.
        10. Creates a credit transaction to refund the non-refundable amount to the user's spend wallet.
        11. Updates transaction statuses and balances.

        The method handles various edge cases and provides appropriate error messages.
        It ensures that only eligible loans are processed and that all necessary checks are performed
        before executing the refund."""

        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:
            for loan_instance in queryset:

                if loan_instance.status != LoanStatus.DECLINED_BY_SUPERVISOR:
                    message = f"Loan cannot be refunded as it is not in the correct status ({loan_instance.status}) or verification stage ({loan_instance.verification_stage})"
                    continue
                ajo_user = loan_instance.borrower
                agent = ajo_user.user

                loan_type = loan_instance.loan_type

                xtype_boosta = ["BOOSTA_2X", "BOOSTA_2X_MINI", "CREDIT_HEALTH"]

                savings_loan_types = [
                    "BOOSTA",
                    "BOOSTA_2X",
                    "BOOSTA_2X_MINI",
                    "CREDIT_HEALTH",
                ]
                eligibility_instance = loan_instance.eligibility
                savings = eligibility_instance.saving
                amount_applied = loan_instance.amount
                eligibility_multiplier = eligibility_instance.multiplier

                ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)

                if savings.savings_type in savings_loan_types:
                    actual_escrow_value = eligibility_instance.amount_saved - 1500
                else:
                    actual_escrow_value = amount_applied / eligibility_multiplier

                wallet_type = (
                    WalletTypes.BOOSTA_2x_COMMISSION
                    if loan_type in xtype_boosta
                    else WalletTypes.BOOSTA_COMMISSION
                )

                # BOOSTA COMMISION WALLET
                boosta_commission_wallet = AjoAgentSelector(
                    user=loan_instance.agent
                ).get_agent_ajo_wallet(wallet_type=wallet_type)

                ajo_user_spend_wallet = ajo_user_selector.get_spending_wallet()

                loan_calc = AjoLoan.compute_interest_with_duration(
                    principal=loan_instance.amount,
                    duration=loan_instance.actual_duaration,
                    loan_type=loan_instance.loan_type,
                    ajouser=loan_instance.borrower,
                    start_date=loan_instance.start_date,
                )
                # pprint(loan_calc)
                actual_deposit = loan_calc.get("actual_deposit")
                boosta_insurance = loan_calc.get("boosta_insurance")
                non_refundable_amount = actual_deposit - boosta_insurance
                quotation_id = loan_instance.eligibility.saving.quotation_id
                debit_uniqe_ref = f"{quotation_id}_commission_debit_for_refund"

                # Check if debit transaction already exists
                if Transaction.objects.filter(
                    unique_reference=debit_uniqe_ref
                ).exists():
                    message = f"Debit transaction with reference {debit_uniqe_ref} already exists. Skipping."
                    continue

                # Perform all balance checks
                if boosta_commission_wallet.available_balance < actual_escrow_value:
                    message = f"Insufficient balance in Boosta commission wallet. Available: {boosta_commission_wallet.available_balance}, Required: {actual_escrow_value}."
                    continue

                # Proceed with deductions
                transaction_form_type = TransactionFormType.COMMISSION
                # Deduct from Boosta commission wallet
                debit_transaction = (
                    TransactionService.dynamic_deduction_from_wallet_transaction(
                        user=agent,
                        amount=actual_escrow_value,
                        wallet_type=boosta_commission_wallet.wallet_type,
                        request_data=None,
                        unique_reference=debit_uniqe_ref,
                        quotation_id=savings.quotation_id,
                        description="Debit commission wallet for declined loan refund",
                        ajo_user=ajo_user,
                        transaction_form_type=transaction_form_type,
                    )
                )

                deduct_agent_wallet = WalletSystem.deduct_balance(
                    wallet=boosta_commission_wallet,
                    amount=actual_escrow_value,
                    transaction_instance=debit_transaction,
                    onboarded_user=ajo_user,
                )

                # Update the transaction status
                debit_transaction.status = Status.SUCCESS
                debit_transaction.transaction_date_completed = timezone.now()
                debit_transaction.wallet_balance_before = deduct_agent_wallet[
                    "balance_before"
                ]
                debit_transaction.wallet_balance_after = deduct_agent_wallet[
                    "balance_after"
                ]
                debit_transaction.save()

                ###############################################################

                transaction_form_type = (
                    TransactionFormType.AJO_LOAN_ESCROW_HOLDING_REFUND
                )

                credit_transaction_unique_reference = (
                    f"{quotation_id}_non_refundable_amount_refund"
                )

                # Check if credit transaction already exists
                if Transaction.objects.filter(
                    unique_reference=credit_transaction_unique_reference
                ).exists():
                    message = f"Credit transaction with reference {credit_transaction_unique_reference} already exists. Skipping."
                    continue

                credit_description = (
                    f"Refund of {non_refundable_amount} after loan decline. "
                    f"This amount covers the non-refundable portion of the initial deposit."
                )
                credit_transaction = (
                    TransactionService.create_deposit_by_wallet_transaction(
                        user=agent,
                        amount=non_refundable_amount,
                        wallet_type=ajo_user_spend_wallet.wallet_type,
                        transaction_form_type=transaction_form_type,
                        unique_reference=credit_transaction_unique_reference,
                        description=credit_description,
                        status=Status.PENDING,
                        quotation_id=quotation_id,
                        ajo_user=ajo_user,
                        plan_type=PlanType.AJO,
                    )
                )

                fund_agent_wallet(
                    transaction=credit_transaction,
                    wallet=ajo_user_spend_wallet,
                    amount=non_refundable_amount,
                    unique_reference=credit_transaction_unique_reference,
                )

                message = "success"

        self.message_user(request, str(message))

    def update_date_disbursed_to_today(self, request, queryset: QuerySet[AjoLoan]):

        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:
            for loan_instance in queryset:
                loan_calc = AjoLoan.compute_interest_with_duration(
                    principal=loan_instance.amount,
                    duration=loan_instance.expected_repayment_count,
                    loan_type=loan_instance.loan_type,
                    ajouser=loan_instance.borrower,
                )
                start_date = loan_calc["start_date"]
                end_date = loan_calc["end_date"]
                loan_instance.interest_amount = loan_calc["interest_amount"]
                loan_instance.interest_rate = loan_calc["interest_rate"]
                loan_instance.end_date = end_date
                loan_instance.start_date = start_date
                loan_instance.repayment_amount = loan_calc["repayment_amount"]
                loan_instance.tenor_in_days = loan_calc["tenor_in_days"]
                loan_instance.daily_repayment_amount = loan_calc[
                    "daily_repayment_amount"
                ]
                loan_instance.date_disbursed = timezone.now()

                loan_instance.save(
                    update_fields=[
                        "interest_amount",
                        "interest_rate",
                        "end_date",
                        "start_date",
                        "repayment_amount",
                        "tenor_in_days",
                        "daily_repayment_amount",
                        "date_disbursed",
                    ]
                )
                message = f"Success ---- StartDate: {start_date} EndDate: {end_date} DateDisbursed: {timezone.now()} "

        self.message_user(request, message)

    def update_amount_saved_amount_disbursed(
        self, request, queryset: QuerySet[AjoLoan]
    ):

        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:
            for loan_instance in queryset:
                boosta_loans = ["BOOSTA_2X", "BOOSTA_2X_MINI", "CREDIT_HEALTH"]
                if loan_instance.loan_type in boosta_loans:
                    loan_calc = AjoLoan.compute_interest_with_duration(
                        principal=loan_instance.amount,
                        duration=loan_instance.expected_repayment_count,
                        loan_type=loan_instance.loan_type,
                        ajouser=loan_instance.borrower,
                    )
                    disburse_amount = loan_calc.get("disbursement_amount")
                else:
                    disburse_amount = loan_instance.amount

                loan_instance.amount_disbursed = disburse_amount
                loan_instance.amount_saved = loan_instance.eligibility.amount_saved

                loan_instance.save(update_fields=["amount_disbursed", "amount_saved"])
                message = "success"
        self.message_user(request, message)

    def get_weekly_sms_summary(self, request, queryset: QuerySet[AjoLoan]):

        message = "Invalid Action"

        for instance in queryset:
            message = SMSLog.weekly_notification(loan_id=instance.id)
            # print(message)

        self.message_user(request, message)

    def get_loan_statement_sms_summary(self, request, queryset: QuerySet[AjoLoan]):

        message = "Invalid Action"

        for instance in queryset:
            message = SMSLog.loan_statement_notification(loan_id=instance.id)

        self.message_user(request, message)
    
    def pre_topup_sms_notification(self, request, queryset: QuerySet[AjoLoan]):

        message = "Invalid Action"

        for instance in queryset:
            message = SMSLog.pre_topup_notification(loan_id=instance.id)

        self.message_user(request, message)

    def notify_borrower_and_guarantor_on_past_maturity(
        self, request, queryset: QuerySet[AjoLoan]
    ):

        message = "Invalid Action"

        for instance in queryset:
            message = SMSLog.past_maturity_loan_notification(loan_id=instance.id)

        self.message_user(request, message)

    def update_loan_performance_to_lost(self, request, queryset: QuerySet[AjoLoan]):
        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:

            for instance in queryset:
                days_after_end = instance.days_after_end
                const = ConstantTable.get_constant_table_instance()
                loan_performance_config = const.loan_performance_config
                use_config = loan_performance_config.get("use_config", False)
                loan_overdue_days = loan_performance_config.get(
                    "loan_overdue_days", 180
                )
                lost_loan_data = {
                    "days_after_end": days_after_end,
                    "added_by": request.user,
                    "admin_loan_overdue_days": loan_overdue_days,
                    "reason": "",
                }
                if not use_config:
                    instance.status = LoanStatus.PAST_MATURITY
                    instance.performance_status = LoanPerformanceStatus.LOST
                    instance.loan_behavior = LoanBehavior.BAD
                    instance.save(
                        update_fields=["performance_status", "loan_behavior", "status"]
                    )
                    lost_loan, created = LostLoan.objects.update_or_create(
                        loan=instance, defaults=lost_loan_data
                    )
                    message = "success"
                else:
                    if days_after_end >= loan_overdue_days:
                        instance.status = LoanStatus.PAST_MATURITY
                        instance.performance_status = LoanPerformanceStatus.LOST
                        instance.loan_behavior = LoanBehavior.BAD
                        instance.save(
                            update_fields=[
                                "performance_status",
                                "loan_behavior",
                                "status",
                            ]
                        )
                        lost_loan, created = LostLoan.objects.update_or_create(
                            loan=instance, defaults=lost_loan_data
                        )
                        message = "success"
                    else:
                        message = f"Days After End: {days_after_end} >= Admin OverDue Days: {loan_overdue_days}"

        self.message_user(request, message)

    def charge_close_loan_due_to_sys_glitch(self, request, queryset: QuerySet[AjoLoan]):
        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:

            for instance in queryset:
                borrower = instance.borrower
                instance.status = LoanStatus.CLOSED
                instance.save(update_fields=["status"])
                ajo_user_selector = AjoUserSelector(ajo_user=borrower)
                disbursement_wallet = ajo_user_selector.get_loan_disbursement_wallet()
                txn_qs = Transaction.objects.filter(
                    quotation_id=instance.unique_loan_ref,
                    wallet_type=disbursement_wallet.wallet_type,
                ).last()

                if txn_qs:
                    amount = txn_qs.amount
                    err_amt = amount * 2
                    if disbursement_wallet.available_balance >= err_amt:
                        description = f"Charge the disbursement wallet due to a double disbursement caused by a system glitch."
                        txn_instance = TransactionService.dynamic_deduction_from_wallet_transaction(
                            user=instance.agent,
                            amount=amount,
                            wallet_type=disbursement_wallet.wallet_type,
                            transaction_form_type=TransactionFormType.AJO_LOAN_DISBURSEMENT,
                            ajo_user=borrower,
                            description=description,
                        )
                        wallet_result = WalletSystem().deduct_balance(
                            wallet=disbursement_wallet,
                            amount=amount,
                            transaction_instance=txn_instance,
                            onboarded_user=borrower,
                        )
                        txn_instance.status = Status.SUCCESS
                        txn_instance.transaction_date_completed = timezone.now()
                        txn_instance.wallet_balance_before = wallet_result[
                            "balance_before"
                        ]
                        txn_instance.wallet_balance_after = wallet_result[
                            "balance_after"
                        ]
                        txn_instance.save()

                        ###############################
                        escrow_wallet = ajo_user_selector.get_any_ajo_user_wallet(
                            wallet_type=WalletTypes.AJO_LOAN_ESCROW
                        )
                        escrow_txn_qs = Transaction.objects.filter(
                            quotation_id=instance.unique_loan_ref,
                            wallet_type=escrow_wallet.wallet_type,
                        ).last()
                        
                        if escrow_txn_qs:

                            escrow_tf_amount = escrow_txn_qs.amount
                            escrow_err_amt = escrow_tf_amount * 2
                           
                            description = f"Charge the disbursement wallet due to a double disbursement caused by a system glitch."
                            
                            escrow_debit_txn_instance = TransactionService.dynamic_deduction_from_wallet_transaction(
                                user=instance.agent,
                                amount=escrow_tf_amount,
                                wallet_type=escrow_wallet.wallet_type,
                                transaction_form_type=TransactionFormType.ESCROW_HOLDING_OFFSET_LOAN_REPAYMENT,
                                ajo_user=borrower,
                                description=description,
                            )
                            wallet_result = WalletSystem().deduct_balance(
                                wallet=escrow_wallet,
                                amount=escrow_tf_amount,
                                transaction_instance=escrow_debit_txn_instance,
                                onboarded_user=borrower,
                            )
                            escrow_debit_txn_instance.status = Status.SUCCESS
                            escrow_debit_txn_instance.transaction_date_completed = (
                                timezone.now()
                            )
                            escrow_debit_txn_instance.wallet_balance_before = (
                                wallet_result["balance_before"]
                            )
                            escrow_debit_txn_instance.wallet_balance_after = (
                                wallet_result["balance_after"]
                            )
                            escrow_debit_txn_instance.save()

                            message = "success"
                            instance.status = LoanStatus.CLOSED
                            instance.save(update_fields=["status"])

                        instance.status = LoanStatus.CLOSED
                        instance.save(update_fields=["status"])

                        message = "success"

                    else:
                        message = f"Insufficient Funds available bal: {disbursement_wallet.available_balance} error amt: {err_amt}"
                else:
                    message = (
                        f"Invalid transaction/quotation id {instance.unique_loan_ref}"
                    )

        self.message_user(request, message)

    allow_edit_borrower_info.short_description = "enable borrower edit screen"
    allow_edit_borrower_info.allow_tags = True

    allow_edit_guarantor.short_description = "enable guarantor edit screen"
    allow_edit_guarantor.allow_tags = True

    approve_loan.short_description = "approve loan"
    approve_loan.allow_tags = True

    create_repayment_from_ajo_user_spend.short_description = (
        "REPAYMENT: Repay Loan from Ajo User Spend Wallet"
    )
    create_repayment_from_ajo_user_spend.allow_tags = True

    detect_dibursed_but_open_to_supervisor_loans.short_description = (
        "LOANS: Correct Disbursed Loans Status"
    )
    detect_dibursed_but_open_to_supervisor_loans.allow_tags = True

    get_loan_statement_sms_summary.short_description = "loan statement summary"
    get_loan_statement_sms_summary.allow_tags = True

    get_topup_summary.short_description = "Get top-up summary"
    get_topup_summary.allow_tags = True

    get_weekly_sms_summary.short_description = "weekly sms summary"
    get_weekly_sms_summary.allow_tags = True

    manual_comm_transfer.short_description = "Manual comm TF"
    manual_comm_transfer.allow_tags = True

    manual_escrow_update.short_description = "manual escrow update (Boosta 2x, 2x mini)"
    manual_escrow_update.allow_tags = True

    notify_borrower_and_guarantor_on_past_maturity.short_description = (
        "Past Maturity loan notification"
    )
    notify_borrower_and_guarantor_on_past_maturity.allow_tags = True

    refund_non_refundable_balance.short_description = "Refund non-refundable balance"
    refund_non_refundable_balance.allow_tags = True

    refund_over_paid_loan.short_description = "refund over paid loan"
    refund_over_paid_loan.allow_tags = True

    reject_ajoloan.short_description = "Reject Loan"
    reject_ajoloan.allow_tags = True

    reject_ajoloan_refund_full_deposit.short_description = (
        "Reject ajo loan/refund full deposit"
    )
    reject_ajoloan_refund_full_deposit.allow_tags = True

    return_overage.short_description = "REFUND: Return Escrow Overage To Spend Wallet"
    return_overage.allow_tags = True

    settle_ajoloan_escrow.short_description = (
        "Settle unrealesed escrow balance after completed repayment"
    )
    settle_ajoloan_escrow.allow_tags = True

    toggle_open.short_description = "Update status to OPEN"
    toggle_open.allow_tags = True

    trigger_repayment_from_escrow.short_description = (
        "REPAYMENT: Trigger repayment from Escrow"
    )
    trigger_repayment_from_escrow.allow_tags = True

    update_amount_saved_amount_disbursed.short_description = (
        "update amount saved and amount disbursed field"
    )
    update_amount_saved_amount_disbursed.allow_tags = True

    update_bnpl_loan_amount_fields.short_description = "BNPL: Update amounts"
    update_bnpl_loan_amount_fields.allow_tags = True

    update_crc_status.short_description = "update crc status"
    update_crc_status.allow_tags = True

    update_date_disbursed_to_today.short_description = "Update Date disburse to today"
    update_date_disbursed_to_today.allow_tags = True

    update_loan.short_description = "update loan (repayment amount, insurance fee, etc excluding start and end date)"
    update_loan.allow_tags = True

    update_loan_schedules.short_description = "Update Loan Schedules"
    update_loan_schedules.allow_tags = True

    update_related_boosta_loan.short_description = "update related boosta loan"
    update_related_boosta_loan.allow_tags = True

    update_status_to_guarantor_capture.short_description = "update to gruantor capture"
    update_status_to_guarantor_capture.allow_tags = True

    update_to_four_month_tenure.short_description = "Update to 120 days"
    update_to_four_month_tenure.allow_tags = True

    update_to_supervisor.short_description = "update status to supervisor.."
    update_to_supervisor.allow_tags = True

    update_to_three_month_tenure.short_description = "Update to 90 days"
    update_to_three_month_tenure.allow_tags = True

    update_to_two_month_tenure.short_description = "Update to 60 days"
    update_to_two_month_tenure.allow_tags = True

    update_with_repayment_object.short_description = (
        "update loan with corresponding repayment"
    )
    update_with_repayment_object.allow_tags = True

    update_with_savings_tenure.short_description = (
        "update with corresponding savings tenure (Boosta 2x, 2x mini)"
    )
    update_with_savings_tenure.allow_tags = True

    update_loan_performance_to_lost.short_description = (
        "Update Performance Status to Lost"
    )
    update_loan_performance_to_lost.allow_tags = True

    charge_close_loan_due_to_sys_glitch.short_description = (
        "Charge/Close loan due to double disbursement"
    )
    charge_close_loan_due_to_sys_glitch.allow_tags = True

    pre_topup_sms_notification.short_description = (
        "Pre-topup SMS Notification"
    )
    pre_topup_sms_notification.allow_tags = True

    actions = [
        update_loan_performance_to_lost,
        allow_edit_borrower_info,
        allow_edit_guarantor,
        approve_loan,
        create_repayment_from_ajo_user_spend,
        detect_dibursed_but_open_to_supervisor_loans,
        get_loan_statement_sms_summary,
        get_topup_summary,
        get_weekly_sms_summary,
        manual_comm_transfer,
        manual_escrow_update,
        notify_borrower_and_guarantor_on_past_maturity,
        refund_non_refundable_balance,
        refund_over_paid_loan,
        reject_ajoloan,
        reject_ajoloan_refund_full_deposit,
        return_overage,
        settle_ajoloan_escrow,
        toggle_open,
        trigger_repayment_from_escrow,
        update_amount_saved_amount_disbursed,
        update_bnpl_loan_amount_fields,
        update_crc_status,
        update_date_disbursed_to_today,
        update_loan,
        update_loan_schedules,
        update_related_boosta_loan,
        update_status_to_guarantor_capture,
        update_to_four_month_tenure,
        update_to_supervisor,
        update_to_three_month_tenure,
        update_to_two_month_tenure,
        update_with_repayment_object,
        update_with_savings_tenure,
        charge_close_loan_due_to_sys_glitch,
        pre_topup_sms_notification
    ]


class ProsperAgentResourceResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["ajo_agent"]
    resource_class = ProsperAgentResource
    search_fields = ("ajo_agent__email",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def recalculate_data(self, request, queryset):
        for instance in queryset:
            instance: ProsperAgent

            instance.handle_calculated_data()

        self.message_user(request, "All Data Recalculated Successfully")

    def resent_application_email(self, request, queryset):
        for instance in queryset:
            instance: ProsperAgent

            ProsperAgent.create_instance(
                ajo_agent=instance.ajo_agent,
                amount=instance.agent_deposit,
                unique_reference=instance.unique_reference,
                task=False,
            )

        self.message_user(request, "Application Email Resent")

    recalculate_data.add_description = "Recalculate Agent Data"
    resent_application_email.add_description = "Resent Application Email"

    actions = [recalculate_data, resent_application_email]


class BorrowerInfoResourceAdmin(ImportExportModelAdmin):
    resource_class = BorrowerInfoResource
    search_fields = (
        "borrower__phone_number",
        "borrower__user__email",
        "verification_number",
    )

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        data.remove("base_64_img_string")
        data.remove("base_64_signature_string")
        data.remove("you_verify_metadata")
        data.remove("credit_bureau")
        data.remove("snapped_image")
        return data

    def updated_info(self, request, queryset: QuerySet[BorrowerInfo]):
        message = "Invalid action"

        for query_instance in queryset:
            biometrics_meta_data = BiometricsMetaData.objects.filter(
                borrower_info=query_instance,
                request_type=BiometricsRequestType.FACE_MATCH,
                user_type=UserVerificationType.BORROWER,
            ).last()

            borrower_info_verification_data = YouVerifyRequest.objects.filter(
                verification_id=query_instance.verification_number,
                request_type=query_instance.verification_type,
                is_valid=True,
            ).last()

            if biometrics_meta_data and query_instance.snapped_image is None:
                query_instance.snapped_image = biometrics_meta_data.base64_img_str
                query_instance.save()
                # message = "success"

            if borrower_info_verification_data:
                borrower_id_verification = borrower_info_verification_data.response
                query_instance.you_verify_metadata = borrower_id_verification
                query_instance.save()
                message = "success"
            else:
                message = "YouVerify response is null"
                continue

        self.message_user(request, str(message))

    def update_dob(self, request, queryset: QuerySet[BorrowerInfo]):
        message = "Invalid action"

        for instance in queryset:
            instance.update_dob_with_verification_response()
            message = "success"

        self.message_user(request, str(message))

    def update_all_dob(self, request, queryset: QuerySet[BorrowerInfo]):
        message = "Invalid action"

        for instance in queryset:
            info_qs = BorrowerInfo.objects.filter(
                Q(date_of_birth__isnull=True)
                | Q(date_of_birth=""),  # OR condition for date_of_birth
                is_verified=True,  # Separate filter condition
            )
            count = 0
            for info in info_qs:
                info.update_dob_with_verification_response()
                count += 1
            message = f"success {count}"

        self.message_user(request, str(message))

    def create_cash_connect_account(self, request, queryset: QuerySet[BorrowerInfo]):
        message = "Invalid action"

        for instance in queryset:
            if not instance.is_verified:
                message = "Borrower is not verified"
                continue
            message = BankAccountService.create_cash_connect_account_details(
                borrower_info=instance
            )

        self.message_user(request, str(message))

    def verify_agent(self, request, queryset: QuerySet[BorrowerInfo]):
        message = "Invalid action"

        for instance in queryset:
            instance.confirmed_agent_not_borrower = True
            instance.confirmed_agent_not_borrower_by = request.user
            instance.save()
            message = "Successfully updated agent not borrower confirmation"

        self.message_user(request, str(message))

    updated_info.short_description = "update verification data"
    updated_info.allow_tags = True

    update_dob.short_description = "update dob"
    update_dob.allow_tags = True

    update_all_dob.short_description = "update all dob"
    update_all_dob.allow_tags = True

    verify_agent.short_description = "verify agent"
    verify_agent.allow_tags = True

    create_cash_connect_account.short_description = (
        "Cash Connect: Create Collection Account"
    )
    create_cash_connect_account.allow_tags = True

    actions = [
        updated_info,
        create_cash_connect_account,
        update_dob,
        update_all_dob,
        verify_agent,
    ]


class LoanGuarantorResourceAdmin(ImportExportModelAdmin):
    resource_class = LoanGuarantorResource
    search_fields = (
        "verification_number",
        "borrower__phone_number",
        "borrower__user__email",
        "email",
        "surname",
        "last_name",
        "phone_number",
    )
    list_filter = (
        "verification_type",
        "preferred_verification",
        "is_verified",
        "notified",
    )

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        data.remove("base_64_img_string")
        data.remove("base_64_signature_string")
        data.remove("you_verify_metadata")
        data.remove("snapped_image")
        return data

    def updated_info(self, request, queryset: QuerySet[LoanGuarantor]):
        message = "Invalid action"

        for query_instance in queryset:

            guarantor_info_verification_data = YouVerifyRequest.objects.filter(
                verification_id=query_instance.verification_number,
                request_type=query_instance.verification_type,
                is_valid=True,
            ).last()
            if not guarantor_info_verification_data:
                message = "YouVerify response is null"
                continue
            else:
                guarantor_id_verification = guarantor_info_verification_data.response
                query_instance.you_verify_metadata = guarantor_id_verification
                query_instance.save()
                message = "success"

        self.message_user(request, str(message))

    def verify_agent(self, request, queryset: QuerySet[LoanGuarantor]):
        message = "Invalid action"

        for instance in queryset:
            instance.confirmed_agent_not_guarnator = True
            instance.confirmed_agent_not_guarnator_by = request.user
            instance.save()
            message = "Successfully updated agent not borrower confirmation"

        self.message_user(request, str(message))

    updated_info.short_description = "update verification data"
    updated_info.allow_tags = True
    verify_agent.short_description = "verify agent"
    verify_agent.allow_tags = True

    actions = [updated_info, verify_agent]


class AjoLoanRepaymentResourceAdmin(ImportExportModelAdmin):
    resource_class = AjoLoanRepaymentResource
    search_fields = (
        "agent__email",
        "borrower__phone_number",
        "borrower__user__email",
        "borrower__first_name",
        "borrower__last_name",
        "repayment_ref",
        "ajo_loan__eligibility__saving__quotation_id",
    )

    list_filter = ("paid_date", "repayment_type", "settlement_status")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def settle_unsettled_repayment_position_with_false_response_action(
        self, request, queryset: QuerySet[AjoLoanRepayment]
    ):
        error = {"status": False, "message": "it didn't go through, try again"}

        for repayment in queryset:
            if (
                repayment.repayment_meta_response == str(error)
                and repayment.repayment_type == RepaymentType.AGENT_DEBIT
                and repayment.settlement_status == False
            ):
                try:
                    AjoLoanRepayment.settle_repayment_status(repayment=repayment)

                    self.message_user(request, str("Repayment Settled Successfully"))

                except Exception as err:

                    self.message_user(request, str("Repayment Settlement Failed"))

                    return f"encountered error: {err}"
            else:
                self.message_user(
                    request, str("Repayment Does not meet required criteria")
                )

    def settle_unsettled_None_repayment_position_with_None_response_action(
        self, request, queryset: QuerySet[AjoLoanRepayment]
    ):

        for repayment in queryset:
            if (
                repayment.repayment_meta_response is None
                and repayment.repayment_type == RepaymentType.AGENT_DEBIT
                and repayment.settlement_status == False
            ):
                try:
                    AjoLoanRepayment.settle_repayment_status(repayment=repayment)

                    self.message_user(request, str("Repayment Settled Successfully"))

                except Exception as err:

                    self.message_user(request, str("Repayment Settlement Failed"))

                    return f"encountered error: {err}"

            else:
                self.message_user(
                    request, str("Repayment Does not meet required criteria")
                )

    def settle_unsettled_repayment_invalid_token(
        self, request, queryset: QuerySet[AjoLoanRepayment]
    ):
        error = {
            "status": False,
            "data": "max recursion depth reached. token is invalid",
            "response": {
                "detail": "Given token not valid for any token type",
                "code": "token_not_valid",
                "messages": [
                    {
                        "token_class": "AccessToken",
                        "token_type": "access",
                        "message": "Token is invalid or expired",
                    }
                ],
            },
        }
        for repayment in queryset:
            if (
                repayment.repayment_meta_response == str(error)
                and repayment.repayment_type == RepaymentType.AGENT_DEBIT
                and repayment.settlement_status == False
            ):

                try:
                    AjoLoanRepayment.settle_repayment_status(repayment=repayment)

                    self.message_user(request, str("Repayment Settled Successfully"))

                except Exception as err:

                    self.message_user(request, str("Repayment Settlement Failed"))

                    return f"encountered error: {err}"

            else:
                self.message_user(
                    request, str("Repayment Does not meet required criteria")
                )

    def settle_unsettled_repayment_duplicate(
        self, request, queryset: QuerySet[AjoLoanRepayment]
    ):
        error = {
            "data": {
                "error": "143",
                "message": "Duplicate Transaction. Please try again after 5 minutes!",
            },
            "status_code": 403,
            "status": False,
        }
        for repayment in queryset:
            if (
                repayment.repayment_meta_response == str(error)
                and repayment.repayment_type == RepaymentType.AGENT_DEBIT
                and repayment.settlement_status == False
            ):

                try:
                    AjoLoanRepayment.settle_repayment_status(repayment=repayment)

                    self.message_user(request, str("Repayment Settled Successfully"))

                except Exception as err:

                    self.message_user(request, str("Repayment Settlement Failed"))

                    return f"encountered error: {err}"

            else:
                self.message_user(
                    request, str("Repayment Does not meet required criteria")
                )

    def settle_unsettled_no_provided_token(
        self, request, queryset: QuerySet[AjoLoanRepayment]
    ):
        error = {
            "status": False,
            "message": "no provider token to continue with this transaction",
        }
        for repayment in queryset:
            if (
                repayment.repayment_meta_response == str(error)
                and repayment.repayment_type == RepaymentType.AGENT_DEBIT
                and repayment.settlement_status == False
            ):

                try:
                    AjoLoanRepayment.settle_repayment_status(repayment=repayment)

                    self.message_user(request, str("Repayment Settled Successfully"))

                except Exception as err:

                    self.message_user(request, str("Repayment Settlement Failed"))

                    return f"encountered error: {err}"

            else:
                self.message_user(
                    request, str("Repayment Does not meet required criteria")
                )

    def settle_bad_escrow_offset_repayments(
        self, request, queryset: QuerySet[AjoLoanRepayment]
    ):
        """
        1 - Verify that there exists a bad escrow offset: Look for all escrow based repayments that are pre-mature.
        2 - Move escrow debit amount to ledger amount
        3 - Set repayment_amount to zero

        "NOTE:" In the future the ledger balance would be used to settle repayment
        So how do I know when a ledger balance has already been used to settle repayment:
        The ledger balance becomes zero at this point.
        """

        if request.user.email not in [
            "<EMAIL>",
            "<EMAIL>",
        ]:
            self.message_user(request, "You are not authorized to perform this action")
            return

        response = {}
        for query in queryset:
            # check if repayment is escrow offset and pre-mature
            loan = query.ajo_loan
            today = datetime.now().date()
            days_to_end = (loan.end_date - today).days
            if query.repayment_type != "ESCROW_DEBIT":
                response[f"{query.id}"] = "not an escrow debit"
            elif days_to_end < 15:
                response[f"{query.id}"] = "already good escrow debit"
                continue
            elif query.repayment_amount == 0.00:
                response[f"{query.id}"] = "already resolved"
                continue
            else:
                repayment_amount = query.repayment_amount
                query.ledger_amount = repayment_amount
                query.repayment_amount = 0.00
                loan.total_paid_amount -= repayment_amount
                loan.escrow_offset = False
                loan.save()
                query.save()

                response[f"{query.id}"] = "resolved successfully"

        return self.message_user(request, response)

    def update_bad_repayment_ledger_balance(
        self, request, queryset: QuerySet[AjoLoanRepayment]
    ):
        if request.user.email not in [
            "<EMAIL>",
            "<EMAIL>",
        ]:
            self.message_user(request, "You are not authorized to perform this action")
            return

        response = {}
        for query in queryset:
            # check if repayment is escrow offset and pre-mature
            if query.repayment_type != "ESCROW_DEBIT":
                response[f"{query.id}"] = "not an escrow debit"
            else:
                bad_escrow = Transaction.objects.filter(
                    wallet_type=WalletTypes.AJO_LOAN_ESCROW,
                    transaction_form_type=TransactionFormType.LOAN_REPAYMENT,
                    status=Status.SUCCESS,
                    onboarded_user=query.ajo_loan.borrower,
                ).latest("date_created")

                if bad_escrow:
                    amount = bad_escrow.amount
                    query.ledger_amount = amount
                    query.save()

                response[f"{query.id}"] = "resolved successfully"

        return self.message_user(request, response)

    def refund_ledger_to_spend(self, request, queryset: QuerySet[AjoLoanRepayment]):
        message = "Invalid Action"
        for instance in queryset:
            ledger_amount = instance.ledger_amount

            if ledger_amount > 0:

                loan = instance.ajo_loan
                ajo_user = loan.borrower
                user = ajo_user.user

                loan_access_token = loan_agent_login().get("access")

                savings_acct_user = (
                    get_user_model()
                    .objects.filter(email=f"{settings.AGENCY_BANKING_USEREMAIL}")
                    .last()
                )

                phone_number = savings_acct_user.user_phone
                # send buddy to savings
                send_loan_buddy_to_savings = AgencyBankingClass.send_money_from_loans_to_savings_through_pay_buddy(
                    phone_number=phone_number,
                    amount=ledger_amount,
                    transaction_reference=loan.loan_ref,
                    access_token=loan_access_token,
                )

                if send_loan_buddy_to_savings.get("data").get("message") == "success":
                    ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
                    ajo_user_spend_wallet = ajo_user_selector.get_spending_wallet()
                    transaction_form_type = (
                        TransactionFormType.AJO_LOAN_ESCROW_HOLDING_REFUND
                    )

                    quotation_id = loan.eligibility.saving.quotation_id

                    credit_transaction_unique_reference = (
                        f"ledger{instance.id}-{quotation_id}_escrow_refund"
                    )

                    credit_description = (
                        f"Escrow from repayment legder amount RepaymentID:{instance.id}"
                    )
                    credit_transaction = (
                        TransactionService.create_deposit_by_wallet_transaction(
                            user=user,
                            amount=ledger_amount,
                            wallet_type=ajo_user_spend_wallet.wallet_type,
                            transaction_form_type=transaction_form_type,
                            unique_reference=credit_transaction_unique_reference,
                            description=credit_description,
                            status=Status.PENDING,
                            quotation_id=quotation_id,
                            ajo_user=ajo_user,
                            plan_type=PlanType.AJO,
                        )
                    )

                    fund_agent_wallet(
                        transaction=credit_transaction,
                        wallet=ajo_user_spend_wallet,
                        amount=ledger_amount,
                        unique_reference=credit_transaction_unique_reference,
                    )

                    instance.comment = f"{ledger_amount} Refunded to spend and debited from loans account"
                    instance.settlement_status = True
                    instance.ledger_amount = 0
                    instance.save()

                    message = "success"
                else:
                    message = str(send_loan_buddy_to_savings)
            else:
                message = f"settlement status: {instance.settlement_status} ::::::: ledger amount: {ledger_amount}"

        return self.message_user(request, message)

    settle_unsettled_repayment_position_with_false_response_action.short_description = (
        "Settle unsettled repayment with false response"
    )
    settle_unsettled_repayment_position_with_false_response_action.allow_tags = True

    settle_unsettled_None_repayment_position_with_None_response_action.short_description = (
        "Settle unsettled repayment with None response"
    )
    settle_unsettled_None_repayment_position_with_None_response_action.allow_tags = True

    settle_unsettled_repayment_invalid_token.short_description = (
        "Settle unsettled repayment with invalid token"
    )
    settle_unsettled_repayment_invalid_token.allow_tags = True

    settle_unsettled_repayment_duplicate.short_description = (
        "settle unsettled repayment duplicate"
    )
    settle_unsettled_repayment_duplicate.allow_tags = True

    settle_unsettled_no_provided_token.short_description = "settle no provided token"
    settle_unsettled_no_provided_token.allow_tags = True

    settle_bad_escrow_offset_repayments.short_description = "Resolve bad escrow debits"
    settle_bad_escrow_offset_repayments.allow_tags = True

    update_bad_repayment_ledger_balance.short_description = "Update bad ledger balance"
    update_bad_repayment_ledger_balance.allow_tags = True

    refund_ledger_to_spend.short_description = "Escrow refund from ledger"
    refund_ledger_to_spend.allow_tags = True

    actions = [
        settle_unsettled_repayment_position_with_false_response_action,
        settle_unsettled_None_repayment_position_with_None_response_action,
        settle_unsettled_repayment_invalid_token,
        settle_unsettled_repayment_duplicate,
        settle_unsettled_no_provided_token,
        refund_ledger_to_spend,
        settle_bad_escrow_offset_repayments,
        update_bad_repayment_ledger_balance,
    ]


class BiometricsMetaDataResourceAdmin(ImportExportModelAdmin):
    resource_class = BiometricsMetaDataResource
    search_fields = (
        "borrower_info__borrower__phone_number",
        "borrower_info__borrower__user__email",
    )
    list_filter = (
        "user_type",
        "request_type",
        "status",
    )

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        data.remove("base64_img_str")
        data.remove("img_str_bvn_or_nin")
        # data.remove("response_data")
        data.remove("agency_info_response")
        return data


class YouVerifyRequestResourceAdmin(ImportExportModelAdmin):
    resource_class = YouVerifyRequestResource
    search_fields = (
        "verification_id",
        "ajo_user__phone_number",
        "ajo_user__user__email",
    )
    list_filter = ("verification_provider", "request_type", "source", "is_valid")
    list_display = (
        "id",
        "verification_id",
        "status_code",
        "source",
        "is_valid",
        "request_type",
        "updated_at",
        "verification_provider",
        "ajo_user",
        "created_at",
    )


class CreditBureauMetaDataResourceAdmin(ImportExportModelAdmin, SortableAdminMixin):
    resource_class = CreditBureauMetaDataResource
    search_fields = (
        "verification_id",
        "ajo_user__phone_number",
        "ajo_user__user__email",
    )
    list_filter = ("status", "updated_at", "created_at", "no_of_defaulted_loans")
    list_display = (
        "id",
        "verification_id",
        "ajo_user",
        "no_of_defaulted_loans",
        "status",
        "is_active",
        "updated_at",
        "created_at",
    )
    # ordering = ["-updated_at", "-created_at", "-no_of_defaulted_loans"]
    # sortable_by = ["-updated_at", "-created_at", "-no_of_defaulted_loans"]


class ProsperAgentPaymentRecordResourceAdmin(ImportExportModelAdmin):
    resource_class = ProsperAgentPaymentRecordResource
    search_fields = ["prosper_agent__email"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LoanDiskMetaDataResourceAdmin(ImportExportModelAdmin):
    resource_class = LoanDiskMetaDataResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RepaymentCheckerResourceAdmin(ImportExportModelAdmin):
    resource_class = RepaymentCheckerResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class BorrowerCreditBureauWorthinessResourceAdmin(ImportExportModelAdmin):
    resource_class = BorrowerCreditBureauWorthiness

    search_fields = (
        "borrower__user__email",
        "borrower__phone_number",
        "borrower__first_name",
        "borrower__last_name",
    )

    list_filter = (
        "credit_worthy",
        "created_at",
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AjoUserAgencyBankingBranchRequestDataDumpResourceAdmin(ImportExportModelAdmin):
    resource_class = AjoUserAgencyBankingBranchRequestDataDumpResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RepaymentVirtuaWalletCreditRecordAdmin(ImportExportModelAdmin):

    resource_class = RepaymentVirtuaWalletCreditRecordResouce
    search_fields = [
        "agent__email",
        "ajo_user__phone_number",
        "ajo_user__first_name",
        "ajo_user__last_name",
    ]
    list_filter = [
        "status",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    # def create_failed_repayment_object_from_transfer(self, request, queryset: QuerySet[RepaymentVirtuaWalletCreditRecord]):

    #     for repay_obj in queryset:
    #         if repay_obj.has_repayment == False:
    #             transaction = Transaction.objects.filter(unique_reference=repay_obj.transaction_reference).last()
    #             if transaction.status == Status.SUCCESS:
    #                 ajo_user=repay_obj.ajo_user
    #                 ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
    #                 wallet_type = transaction.wallet_type
    #                 ajo_user_repayment_wallet = ajo_user_selector.get_any_ajo_user_wallet(wallet_type=wallet_type)

    #                 AjoLoanRepayment.objects.create(
    #                 agent=transaction.user,
    #                 borrower=ajo_user,
    #                 ajo_loan=loan,
    #                 amount=repay_obj.amount,
    #                 repayment_type=RepaymentType.TRANSFER,
    #                 transaction_source_trx=transaction,
    #             )

    # create_failed_repayment_object_from_transfer.short_description = "create failed repayment object from transfer"
    # create_failed_repayment_object_from_transfer.allow_tags = True

    # actions = [
    #     create_failed_repayment_object_from_transfer
    # ]


class HolidayForm(forms.ModelForm):
    start_date = forms.DateField(
        required=False, widget=forms.TextInput(attrs={"type": "date"})
    )
    end_date = forms.DateField(
        required=False, widget=forms.TextInput(attrs={"type": "date"})
    )

    class Meta:
        model = Holiday
        fields = [
            "name",
            "date",
            "type",
            "agent",
            "branch",
            "salary_waver",
            "created_by",
            "start_date",
            "end_date",
        ]


class HolidayResourceAdmin(admin.ModelAdmin):
    form = HolidayForm
    search_fields = ["name", "created_by__email"]
    list_filter = ["date", "type"]
    readonly_fields = ("created_by",)

    def is_weekend(self, date):
        return date.weekday() >= 5

    def holiday_exists(self, date, type, agent=None, branch=None):
        if type == "company":
            return Holiday.objects.filter(date=date, type=type).exists()
        elif type == "agent":
            return Holiday.objects.filter(date=date, type=type, agent=agent).exists()
        elif type == "branch":
            return Holiday.objects.filter(date=date, type=type, branch=branch).exists()
        return False

    @transaction.atomic
    def save_model(self, request, obj, form, change):
        # Retrieve form fields for holiday dates
        start_date = form.cleaned_data.get("start_date")
        end_date = form.cleaned_data.get("end_date")
        single_date = form.cleaned_data.get("date")

        # Case 1: Date Range - start_date and end_date are provided
        if start_date and end_date:
            current_date = start_date
            while current_date <= end_date:
                if self.is_weekend(current_date):
                    pass
                elif self.holiday_exists(current_date, obj.type, obj.agent, obj.branch):
                    pass
                else:
                    Holiday.objects.create(
                        name=obj.name,
                        date=current_date,
                        type=obj.type,
                        agent=obj.agent,
                        branch=obj.branch,
                        salary_waver=obj.salary_waver,
                        created_by=request.user,
                    )
                current_date += timedelta(days=1)

            # Notify the user about the bulk creation success
            self.message_user(
                request, "Holidays added successfully for the specified range."
            )

        # Case 2: Single Date - only `date` is provided
        elif single_date:
            obj.created_by = request.user
            obj.save()
            self.message_user(request, "Holiday added successfully.")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AccountBalancesResourceAdmin(ImportExportModelAdmin):

    resource_class = AccountBalancesResource

    list_filter = ["created_at"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def update_spend_wallet_balance(self, request, queryset):
        for query in queryset:
            spend_wallet_balance = (
                WalletSystem.objects.filter(
                    wallet_type=WalletTypes.AJO_SPENDING
                ).aggregate(total_amount=Sum("available_balance"))["total_amount"]
                or 0.00
            )
            query.spend_wallet_balance = spend_wallet_balance
            query.save()
        self.message_user(request, "Updated successfully")

    update_spend_wallet_balance.add_tags = True
    update_spend_wallet_balance.short_description = (
        "Wallet Balance: Update SPEND WALLET BALANCE"
    )

    actions = [
        update_spend_wallet_balance,
    ]


class InvestmentCapitalResourceAdmin(ImportExportModelAdmin):

    resource_class = InvestmentCapitalResource

    list_filter = ["created_at"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TotalInvestmentCapitalResourceAdmin(ImportExportModelAdmin):

    resource_class = TotalInvestmentCapitalResource

    list_filter = ["created_at"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LoanKYCDocumentationResourceAdmin(ImportExportModelAdmin):

    resource_class = LoanKYCDocumentationResource
    search_fields = (
        "unique_ref",
        "borrower_phone_number",
        "borrower__phone_number",
        "borrower__user__email",
    )

    list_filter = (
        "documentation_status",
        "paid_processing_fee",
        "borrower_verification_type",
        "borrower_above_min_age_limit",
        "borrower_below_max_age_limit",
        "borrower_face_match",
        "borrower_capture_is_live",
        "guarantor_verification_type",
        "guarantor_face_match",
        "guarantor_capture_is_live",
        "guarantor_above_min_age_limit",
        "guarantor_below_max_age_limit",
    )

    # readonly_fields = ()
    raw_id_fields = (
        "borrower",
        "savings",
    )

    def get_list_display(self, request):
        data = [
            "savings_type",
            "borrower_has_active_loan",
            "guarantor_has_active_loan",
        ] + [field.name for field in self.model._meta.concrete_fields]
        data.remove("borrower_base_64_capture")
        data.remove("borrower_base_64_nin_bvn_img")
        data.remove("guarantor_base_64_nin_bvn_img")
        data.remove("guarantor_base_64_capture")
        data.remove("agent_image")
        return data

    def repost_guarantor(self, request, queryset: QuerySet[LoanKYCDocumentation]):
        message = "Invalid action"

        for kyc_instance in queryset:
            kyc_instance.process_guarantor_data
            message = "success"

        self.message_user(request, message=message)

    def repost_borrower(self, request, queryset: QuerySet[LoanKYCDocumentation]):
        message = "Invalid action"

        for kyc_instance in queryset:
            kyc_instance.process_borrower_data
            message = "success"

        self.message_user(request, message=message)

    def update_status(self, request, queryset: QuerySet[LoanKYCDocumentation]):
        message = "Invalid action"
        for kyc_instance in queryset:
            other_status = ["PENDING", "INCOMPLETE"]
            if kyc_instance.documentation_status in other_status:
                message = f"could not proceed with update as status is {kyc_instance.documentation_status}"
                continue
            docapi_obj = DocumentationApiHandler()
            request_response = docapi_obj.get_verification_status(
                verification_id=kyc_instance.unique_ref
            )
            request_status_code = request_response.status_code
            if request_status_code == 201:
                json_res = request_response.json()
                LoanKYCDocumentation.update_verification_status(
                    documentation_instance=kyc_instance, verification_result=json_res
                )
                message = f"success {request_status_code}"
            else:
                message = f"error {request_status_code}"

        self.message_user(request, message=message)

    def localhost_verification(self, request, queryset: QuerySet[LoanKYCDocumentation]):
        message = "Invalid action"
        for kyc_instance in queryset:
            other_status = ["INCOMPLETE", "COMPLETED"]
            if kyc_instance.documentation_status in other_status:
                message = f"could not proceed with update as status is {kyc_instance.documentation_status}"
                continue
            if kyc_instance.paid_processing_fee is False:
                message = f"processing fee hasn't being paid for"
                continue
            result = kyc_instance.run_localhost_verifications()
            message = "success"

        self.message_user(request, message=message)

    def save_guarantor_not_borrower_to_completed(
        self, request, queryset: QuerySet[LoanKYCDocumentation]
    ):
        message = "Invalid action"
        for kyc_instance in queryset:
            if (
                kyc_instance.documentation_status == "FAILED"
                and not kyc_instance.guarantor_not_borrower
            ):
                kyc_instance.guarantor_not_borrower = True

                if (
                    VerificationStage.GUARANTOR in kyc_instance.failed_stages
                    and len(kyc_instance.failed_stages) == 1
                ):
                    kyc_instance.documentation_status = "COMPLETED"

                kyc_instance.save()
                kyc_instance.append_guarantor_to_failed_stage
                message = (
                    f"Success; Updated status: {kyc_instance.documentation_status}"
                )
            else:
                message = f"Status: {kyc_instance.documentation_status}, Guarantor not borrower: {kyc_instance.guarantor_not_borrower}"

        self.message_user(request, message=message)

    def save_guarantor_not_agent_to_completed(
        self, request, queryset: QuerySet[LoanKYCDocumentation]
    ):
        message = "Invalid action"
        for kyc_instance in queryset:
            if (
                kyc_instance.documentation_status == "FAILED"
                and not kyc_instance.agent_is_not_guarantor
            ):
                kyc_instance.agent_is_not_guarantor = True
                kyc_instance.agent_is_not_guarantor_state = "SUCCESS"

                if (
                    VerificationStage.GUARANTOR in kyc_instance.failed_stages
                    and len(kyc_instance.failed_stages) == 1
                ):
                    kyc_instance.documentation_status = "COMPLETED"

                kyc_instance.save()
                kyc_instance.append_guarantor_to_failed_stage
                message = (
                    f"Success; Updated status: {kyc_instance.documentation_status}"
                )
            else:
                message = f"Status: {kyc_instance.documentation_status}, Guarantor not borrower: {kyc_instance.guarantor_not_borrower}"

        self.message_user(request, message=message)

    def update_agent_not_borrower(
        self, request, queryset: QuerySet[LoanKYCDocumentation]
    ):
        message = "Invalid action"
        for kyc_instance in queryset:
            if (
                kyc_instance.documentation_status == "FAILED"
                and not kyc_instance.agent_is_not_borrower
            ):
                kyc_instance.agent_is_not_borrower = True
                kyc_instance.agent_is_not_borrower_state = "SUCCESS"

                if (
                    VerificationStage.BORROWER_INFO in kyc_instance.failed_stages
                    and len(kyc_instance.failed_stages) == 1
                ):
                    kyc_instance.documentation_status = "COMPLETED"

                kyc_instance.save()
                kyc_instance.append_borrower_to_failed_stage
                message = (
                    f"Success; Updated status: {kyc_instance.documentation_status}"
                )
            else:
                message = f"Status: {kyc_instance.documentation_status}, Borrower not Agent: {kyc_instance.agent_is_not_borrower}"

        self.message_user(request, message=message)

    def enable_guarantor_edit_screen(
        self, request, queryset: QuerySet[LoanKYCDocumentation]
    ):
        message = "Invalid action"
        for kyc_instance in queryset:
            if VerificationStage.GUARANTOR_CAPTURE in kyc_instance.failed_stages:
                kyc_instance.failed_stages.remove(VerificationStage.GUARANTOR_CAPTURE)

            if VerificationStage.GUARANTOR not in kyc_instance.failed_stages:
                kyc_instance.failed_stages.append(VerificationStage.GUARANTOR)
            kyc_instance.documentation_status = "FAILED"
            kyc_instance.save()
            message = "success"
        self.message_user(request, message=message)

    def enable_borrower_info_edit_screen(
        self, request, queryset: QuerySet[LoanKYCDocumentation]
    ):
        message = "Invalid action"
        for kyc_instance in queryset:
            if VerificationStage.FACE_MATCH in kyc_instance.failed_stages:
                kyc_instance.failed_stages.remove(VerificationStage.FACE_MATCH)

            if VerificationStage.BORROWER_INFO not in kyc_instance.failed_stages:
                kyc_instance.failed_stages.append(VerificationStage.BORROWER_INFO)

            kyc_instance.documentation_status = "FAILED"
            kyc_instance.save()
            message = "success"
        self.message_user(request, message=message)

    def update_status_to_not_used(
        self, request, queryset: QuerySet[LoanKYCDocumentation]
    ):
        message = "Invalid action"
        for kyc_instance in queryset:
            eligibility_instance = LoanEligibility.objects.filter(
                saving_id=kyc_instance.savings.id
            ).last()
            if eligibility_instance:
                eligibility_instance.has_documentation = False
                eligibility_instance.save()
            kyc_instance.documentation_status = DocumentationStatus.NOT_USED
            kyc_instance.save()
            message = "success"
        self.message_user(request, message=message)

    def create_health_plan(self, request, queryset: QuerySet[LoanKYCDocumentation]):
        message = "Invalid action"
        for kyc_instance in queryset:
            savings = kyc_instance.savings
            quotation_id = str(kyc_instance.unique_ref)
            insurance_transaction = Transaction.objects.filter(
                quotation_id=quotation_id,
                transaction_form_type=TransactionFormType.HEALTH_INSURANCE,
            )

            if insurance_transaction.exists():
                transaction_instance = insurance_transaction.last()
                result = HealthInsurance.create_health_plan(
                    loan=None,
                    amount=transaction_instance.amount,
                    duration=1,
                    kyc_instance=kyc_instance,
                )
                message = result

            else:
                message = "Health Insurance Transaction DoesNotExist"
        self.message_user(request, message=str(message))

    def update_to_paid(self, request, queryset: QuerySet[LoanKYCDocumentation]):
        message = "Invalid action"
        for kyc_instance in queryset:
            savings = kyc_instance.savings
            quotation_id = str(kyc_instance.unique_ref)
            insurance_transaction = Transaction.objects.filter(
                quotation_id=quotation_id,
                transaction_form_type=TransactionFormType.AJO_LOAN_CHARGE_FEE,
            )

            if insurance_transaction.exists():
                kyc_instance.paid_processing_fee = True
                kyc_instance.save()
                message = "Payment Status Updated"

            else:
                message = "Transaction DoesNotExist"

        self.message_user(request, message=str(message))

    def run_borrower_info_checks(
        self, request, queryset: QuerySet[LoanKYCDocumentation]
    ):
        message = "Invalid action"
        for kyc_instance in queryset:
            # savings = kyc_instance.savings
            kyc_instance.verify_borrower_information()
            message = kyc_instance.documentation_status
        self.message_user(request, message=str(message))

    def run_guarantor_checks(self, request, queryset: QuerySet[LoanKYCDocumentation]):
        message = "Invalid action"
        for kyc_instance in queryset:
            # savings = kyc_instance.savings
            kyc_instance.verify_guarantor_information()
            message = kyc_instance.documentation_status
        self.message_user(request, message=str(message))

    def update_agent_image(self, request, queryset: QuerySet[LoanKYCDocumentation]):
        message = "Invalid action"
        for kyc_instance in queryset:
            savings = kyc_instance.savings
            ajouser = savings.ajo_user
            user = ajouser.user
            start_date = timezone.now().date()

            get_agent_image = AgencyBankingClass().get_agency_user_details(
                user_id=user.customer_user_id, start_date=start_date
            )

            agent_image = get_agent_image.get("user_bvn_image_base64_string", None)
            if agent_image:
                kyc_instance.agent_image = agent_image
                kyc_instance.save()
                message = "success"
            else:
                message = get_agent_image
        self.message_user(request, message=str(message))

    def push_to_completed(self, request, queryset: QuerySet[LoanKYCDocumentation]):
        message = "Invalid action"
        for kyc_instance in queryset:
            has_adjustment_privileges = settings.CAN_MODIFY_RECORDS
            user = request.user
            user_email = user.email

            if user_email not in has_adjustment_privileges:
                message = (
                    "you do not have the necessary permission to perform this action"
                )
                continue

            if not kyc_instance.crc_eligible:
                message = f"CRC status is {kyc_instance.crc_state}"
                continue
            elif not kyc_instance.paid_processing_fee:
                message = f"user has not paid processing fee"
                continue

            kyc_instance.documentation_status = DocumentationStatus.COMPLETED
            kyc_instance.agent_is_not_borrower = True
            kyc_instance.agent_is_not_borrower_state = DocumentationStatus.SUCCESS
            kyc_instance.agent_is_not_borrower_similarity = 0.5
            kyc_instance.borrower_face_match = True
            kyc_instance.borrower_livelines_state = DocumentationStatus.SUCCESS

            kyc_instance.borrower_capture_is_live = True
            kyc_instance.borrower_face_similarity = 0.9
            kyc_instance.borrower_live_similarity = 0.9

            kyc_instance.borrower_facematch_state = DocumentationStatus.SUCCESS

            kyc_instance.guarantor_face_match = True
            kyc_instance.borrower_live_similarity = 0.9

            kyc_instance.guarantor_facematch_state = DocumentationStatus.SUCCESS
            kyc_instance.guarantor_capture_is_live = True
            kyc_instance.guarantor_face_similarity = 0.9
            kyc_instance.guarantor_live_similarity = 0.9
            kyc_instance.guarantor_livelines_state = DocumentationStatus.SUCCESS
            kyc_instance.agent_is_not_guarantor = True
            kyc_instance.agent_is_not_guarantor_state = DocumentationStatus.SUCCESS
            kyc_instance.guarantor_not_borrower = True
            kyc_instance.failed_stages.clear()
            kyc_instance.save()
            ManualAdjustment.create_adjusment_record(
                model_instance=kyc_instance,
                email=user_email,
                phone_number=kyc_instance.borrower.phone,
                correction_type="manual_update",
                reason="This adjustment was made due to a downtime currently being experienced by the biometrics provider.",
                status="SUCCESS",
            )
            message = "success"
        self.message_user(request, message=str(message))

    def refund_loan_processing_fee(
        self, request, queryset: QuerySet[LoanKYCDocumentation]
    ):
        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:
            for instance in queryset:
                if instance.has_loan:
                    message = (
                        "This KYC documentation is already associated with a loan."
                    )
                    continue

                if not instance.paid_processing_fee:
                    message = "Loan processing fee has not been paid for this loan."
                    continue

                txn_qs = Transaction.objects.filter(
                    quotation_id=instance.unique_ref,
                    transaction_form_type=TransactionFormType.AJO_LOAN_CHARGE_FEE,
                )

                if not txn_qs.exists():
                    message = "No processing fee record found for this loan."
                    continue

                txn_instance = txn_qs.first()

                refund_transaction_queryset = Transaction.objects.filter(
                    transaction_form_type=TransactionFormType.REFUND_LOAN_FEE,
                    quotation_id=txn_instance.quotation_id,
                    onboarded_user=txn_instance.onboarded_user,
                )

                if refund_transaction_queryset.exists():
                    message = "A refund has already been processed for this loan fee."
                    continue

                user = txn_instance.user
                amount = txn_instance.amount
                agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()

                credit_transaction = (
                    TransactionService.create_deposit_by_wallet_transaction(
                        user=user,
                        amount=amount,
                        wallet_type=agent_wallet.wallet_type,
                        transaction_form_type=TransactionFormType.REFUND_LOAN_FEE,
                        description="Refund for loan processing fee",
                        status=Status.PENDING,
                        quotation_id=txn_instance.quotation_id,
                        ajo_user=txn_instance.onboarded_user,
                        plan_type=PlanType.AJO,
                    )
                )

                fund_agent_wallet(
                    transaction=credit_transaction,
                    wallet=agent_wallet,
                    amount=amount,
                )

                message = f"Successfully refunded loan processing fee of {amount} for loan {instance.unique_ref}"

        self.message_user(request, message=str(message))

    localhost_verification.short_description = "Re-try verification"
    localhost_verification.allow_tags = True

    save_guarantor_not_borrower_to_completed.short_description = (
        "Update borrower not guarantor"
    )
    save_guarantor_not_borrower_to_completed.allow_tags = True

    save_guarantor_not_agent_to_completed.short_description = (
        "Update agent not guarantor"
    )
    save_guarantor_not_agent_to_completed.allow_tags = True

    enable_guarantor_edit_screen.short_description = "Enable guarantor edit screen"
    enable_guarantor_edit_screen.allow_tags = True

    enable_borrower_info_edit_screen.short_description = "Enable borrower edit screen"
    enable_borrower_info_edit_screen.allow_tags = True

    update_agent_not_borrower.short_description = "Update Agent is not Borower"
    update_agent_not_borrower.allow_tags = True

    update_status_to_not_used.short_description = "Update Status to not used"
    update_status_to_not_used.allow_tags = True

    create_health_plan.short_description = "Create Health Plan"
    create_health_plan.allow_tags = True

    update_to_paid.short_description = "Update to Paid"
    update_to_paid.allow_tags = True

    run_borrower_info_checks.short_description = "verify borrower information"
    run_borrower_info_checks.allow_tags = True

    run_guarantor_checks.short_description = "verify guarantor information"
    run_guarantor_checks.allow_tags = True

    update_agent_image.short_description = "update agent image"
    update_agent_image.allow_tags = True

    push_to_completed.short_description = "push to completed"
    push_to_completed.allow_tags = True

    refund_loan_processing_fee.short_description = "Refund loan processing fee"
    refund_loan_processing_fee.allow_tags = True

    actions = [
        create_health_plan,
        enable_borrower_info_edit_screen,
        enable_guarantor_edit_screen,
        localhost_verification,
        push_to_completed,
        run_borrower_info_checks,
        run_guarantor_checks,
        save_guarantor_not_agent_to_completed,
        save_guarantor_not_borrower_to_completed,
        update_agent_image,
        update_agent_not_borrower,
        update_status_to_not_used,
        update_to_paid,
        refund_loan_processing_fee,
    ]


class LoanAnalysisLogResourceAdmin(ImportExportModelAdmin):
    resource_class = LoanAnalysisLogResource
    list_per_page = 100
    list_display = (
        "message",
        "percent",
        "prev_percent",
        "due_to_repayment_ratio",
        "prev_due_to_repayment_ratio",
        "amount_due",
        "total_paid",
        "can_give_loan",
        "agent",
        "missed_amount",
        "positive_count",
        "negative_count",
        "block_reason",
        "average_past_maturity_days",
        "total_past_maturity_amount",
        "created_at",
        "updated_at",
        "average_outstanding_days",
        "over_due_display",
    )
    search_fields = ("agent__email", "agent__user_phone")
    list_filter = ("can_give_loan", "created_at")
    date_hierarchy = "created_at"

    def defaulting_loans_display(self, obj):
        return ", ".join([str(loan) for loan in obj.defaulting_loans.all()])

    defaulting_loans_display.short_description = "Defaulting Loans"

    def over_due_display(self, obj):
        overdue_data = obj.over_due
        return json.dumps(overdue_data)

    over_due_display.short_description = "Overdue Loans (JSON)"

    def check_percent_and_average(self, request, queryset: QuerySet[LoanAnalysisLog]):
        queryset = LoanAnalysisLog.objects.filter(
            **{
                k: v == "1" if k in ["can_give_loan"] else v
                for k, v in request.GET.items()
            }
        )

        data = [
            overdue_value for obj in queryset for overdue_value in obj.over_due.values()
        ]

        overall_average_days = round(sum(data) / len(data), 2)

        percentages = [obj.percent for obj in queryset]

        average_percent = round(sum(percentages) / len(percentages), 2)

        self.message_user(
            request,
            f"The average percentage is {average_percent} and the average outstanding days is {overall_average_days}",
        )

    check_percent_and_average.short_description = (
        "Check average percentage and average outstanding days for selected logs"
    )

    def calculate_commission(self, request, queryset):

        # Get logs for the current month
        current_month = timezone.now().month
        logs = LoanAnalysisLog.objects.filter(created_at__month=current_month)

        # Get unique agents with logs this month
        agents = logs.values_list("agent", flat=True).distinct()

        for agent_id in agents:
            # Calculate average percent for the agent's logs this month
            average_percent = (
                logs.filter(agent=agent_id).aggregate(Avg("percent"))["percent__avg"]
                or 0.0
            )
            agent = logs.filter(agent=agent_id).first().agent

            # Determine commission percent based on average_percent
            if 80 <= average_percent <= 85:
                commission_percent = 0.5
            elif average_percent > 85:
                commission_percent = 1.0
            else:
                commission_percent = 0.0  # Default if not within specified ranges

            if commission_percent > 0:
                last_month = current_month - 1
                disbursed_amount = (
                    AjoLoan.objects.filter(
                        is_disbursed=True, date_disbursed__date__month=last_month
                    ).aggregate(models.Sum("amount"))["amount__sum"]
                    or 0.0
                )

                if disbursed_amount <= 0:
                    continue

                # Calculate commission amount
                commission_amount = disbursed_amount * (commission_percent / 100)

                # Check if a commission record already exists for this agent in the current month
                commission_record, created = (
                    AgentCommissionPayroll.objects.get_or_create(
                        agent=agent,
                        created_at__date__month=current_month,
                        defaults={
                            "average_percent": average_percent,
                            "amount": disbursed_amount,
                            "commission_percent": commission_percent,
                            "commission_amount": commission_amount,
                        },
                    )
                )

                # If commission record already exists, update its values
                if not created:
                    commission_record.average_percent = average_percent
                    commission_record.amount = disbursed_amount
                    commission_record.commission_percent = commission_percent
                    commission_record.commission_amount = commission_amount
                    commission_record.save()

        self.message_user(
            request, "Commission calculation and records update complete."
        )

    calculate_commission.short_description = (
        "Calculate Commission for current month logs"
    )

    actions = [check_percent_and_average, calculate_commission]


class AgentCommissionPayrollResourceAdmin(ImportExportModelAdmin):
    resource_classes = [AgentCommissionPayrollResource]
    search_fields = ("agent__email",)
    list_filter = [
        ("created_at", admin.DateFieldListFilter),
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AjoLoanScheduleResourceAdmin(ImportExportModelAdmin):
    resource_classes = [AjoLoanScheduleResource]
    search_fields = ("loan__borrower__phone_number", "loan__loan_ref")
    list_filter = ("created_at", "due_date", "is_late", "fully_paid")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ComplianceChecksLogsResourceAdmin(ImportExportModelAdmin):
    resource_class = ComplianceChecksLogsResource
    search_fields = [
        "borrower__phone_number",
        "agent__email",
        "guarantor__email",
        "guarantor__phone_number",
        "check",
    ]
    list_filter = [
        "branch",
        "compliance_type",
        ("created_at", admin.DateFieldListFilter),
        "treated",
    ]

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class GiniMachineLogResourceAdmin(ImportExportModelAdmin):
    resource_class = GiniMachineLogResource
    search_fields = [
        "ajo_user__phone_number",
    ]
    list_filter = []

    date_hierarchy = "created_at"

    def get_list_display(self, request):

        display_list = [field.name for field in self.model._meta.concrete_fields]
        display_list.remove("data")
        return display_list


class MissedRepaymentsTableResourceAdmin(ImportExportModelAdmin):
    resource_class = MissedRepaymentsTableResource
    search_fields = [
        "loan__borrower__phone_number",
        "loan__loan_ref",
        "loan_supervisor__email",
        "loan_officer__email",
        "borrower__phone",
        "guarantor__phone",
    ]
    list_filter = [
        ("created_at", admin.DateFieldListFilter),
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class Missed14DaysResourceAdmin(ImportExportModelAdmin):
    resource_class = Missed14DaysResource
    search_fields = [
        "loan__borrower__phone_number",
        "loan__loan_ref",
        "loan_supervisor__email",
        "loan_officer__email",
        "borrower__phone",
        "guarantor__phone",
    ]
    list_filter = [
        ("created_at", admin.DateFieldListFilter),
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class Missed28DaysResourceAdmin(ImportExportModelAdmin):
    resource_class = Missed28DaysResource
    search_fields = [
        "loan__borrower__phone_number",
        "loan__loan_ref",
        "loan_supervisor__email",
        "loan_officer__email",
        "borrower__phone",
        "guarantor__phone",
    ]
    list_filter = [
        ("created_at", admin.DateFieldListFilter),
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class Missed60DaysResourceAdmin(ImportExportModelAdmin):
    resource_class = Missed60DaysResource
    search_fields = [
        "loan__borrower__phone_number",
        "loan__loan_ref",
        "loan_supervisor__email",
        "loan_officer__email",
        "borrower__phone",
        "guarantor__phone",
    ]
    list_filter = [
        ("created_at", admin.DateFieldListFilter),
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class Missed90DaysResourceAdmin(ImportExportModelAdmin):
    resource_class = Missed90DaysResource
    search_fields = [
        "loan__borrower__phone_number",
        "loan__loan_ref",
        "loan_supervisor__email",
        "loan_officer__email",
        "borrower__phone",
        "guarantor__phone",
    ]
    list_filter = [
        ("created_at", admin.DateFieldListFilter),
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class MissedPastMaturity15DaysResourceAdmin(ImportExportModelAdmin):
    resource_class = MissedPastMaturity15DaysResource
    search_fields = [
        "loan__borrower__phone_number",
        "loan__loan_ref",
        "loan_supervisor__email",
        "loan_officer__email",
        "borrower__phone",
        "guarantor__phone",
    ]
    list_filter = [
        ("created_at", admin.DateFieldListFilter),
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class MissedLostResourceAdmin(ImportExportModelAdmin):
    resource_class = MissedLostResource
    search_fields = [
        "loan__borrower__phone_number",
        "loan__loan_ref",
        "loan_supervisor__email",
        "loan_officer__email",
        "borrower__phone",
        "guarantor__phone",
    ]
    list_filter = [
        ("created_at", admin.DateFieldListFilter),
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class ProductAssignmentResourceAdmin(ImportExportModelAdmin):
    resource_class = ProductAssignmentResource
    search_fields = [
        "borrower_loan__borrower__phone_number",
        "borrower_loan__borrower__user__email",
        "supervisor__email",
        "imei_added_by__email",
        "device_group_id",
        "imei",
        "imei2",
    ]
    list_filter = ["product_status", "assigned", "assigned", "condition"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]

    def update_supervisor(self, request, queryset: QuerySet[ProductAssignment]):
        message = "Invalid Action"
        for query_instance in queryset:
            message = update_supervisor_device_mgmt(device_mgmt_id=query_instance.id)

        self.message_user(request, str(message))

    def add_to_performing_group(self, request, queryset: QuerySet[ProductAssignment]):
        message = "Invalid Action"
        for query_instance in queryset:
            loan_performance = "PERFORMING"
            move_to_group = EasyControlDeviceGroup.add_device(
                loan_performance=loan_performance,
                product_instance=query_instance,
                device_id=query_instance.device_id,
            )
            message = move_to_group
        self.message_user(request, str(message))

    def add_to_owed_balance_group(self, request, queryset: QuerySet[ProductAssignment]):
        message = "Invalid Action"
        for query_instance in queryset:
            loan_performance = "OWED_BALANCE"
            move_to_group = EasyControlDeviceGroup.add_device(
                loan_performance=loan_performance,
                product_instance=query_instance,
                device_id=query_instance.device_id,
            )
            message = move_to_group

        self.message_user(request, str(message))

    update_supervisor.short_description = "Update Product Supervisor"
    update_supervisor.allow_tags = True

    add_to_performing_group.short_description = "Move Device to Open Group"
    add_to_performing_group.allow_tags = True

    add_to_owed_balance_group.short_description = "Move Device to Owed Balance Group"
    add_to_owed_balance_group.allow_tags = True

    actions = [
        update_supervisor,
        add_to_performing_group,
        add_to_owed_balance_group,
    ]


class EasyControlMetaDataResourceAdmin(ImportExportModelAdmin):
    resource_class = EasyControlMetaDataResource
    # search_fields = []

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]

    def create_update_easycontrol_device_group(
        self, request, queryset: QuerySet[ProductAssignment]
    ):
        message = "Invalid Action"
        for query_instance in queryset:
            message = EasyControlDeviceGroup.create_device_group()

        self.message_user(request, str(message))

    create_update_easycontrol_device_group.short_description = (
        "Create Update EasyControl Device Group"
    )
    create_update_easycontrol_device_group.allow_tags = True

    actions = [
        create_update_easycontrol_device_group,
    ]


class LoanFeedbackResourceAdmin(ImportExportModelAdmin):
    resource_class = LoanFeedbackResource
    search_fields = [
        "loan__borrower__phone_number",
    ]
    list_filter = (
        "feedback",
        "loan_stage",
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class EscrowDebitErrorLogResourceAdmin(ImportExportModelAdmin):
    resource_class = EscrowDebitErrorLogResource
    # search_fields = []

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class LoanFeedbackResourceAdmin(ImportExportModelAdmin):
    resource_class = LoanFeedbackResource
    search_fields = [
        "loan__borrower__phone_number",
    ]
    list_filter = (
        "feedback",
        "loan_stage",
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class EscrowDebitErrorLogResourceAdmin(ImportExportModelAdmin):
    resource_class = EscrowDebitErrorLogResource
    # search_fields = []

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class OffetPastMaturityWithEscrowLogResourceAdmin(ImportExportModelAdmin):
    resource_class = OffetPastMaturityWithEscrowLogResource
    # search_fields = []

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class VfdProviderDisbursementLogResourceAdmin(ImportExportModelAdmin):
    resource_class = VfdProviderDisbursementLogResource
    search_fields = ["transaction__onboarded_user__phone_number"]
    list_filter = ["created_at", "updated_at", "transaction__transaction_form_type"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]

    def resolve_status(self, request, queryset: VfdProviderDisbursementLog):
        vfd_interest_rate_base = (
            ConstantTable.get_constant_table_instance().vfd_interest_rate
        )
        vfd_transfer_charge = (
            ConstantTable.get_constant_table_instance().vfd_transfer_charge
        )

        for query in queryset:
            vfd_interest_rate = vfd_interest_rate_base * query.tenor
            amount = query.transaction.amount - vfd_transfer_charge
            query.status = query.transaction.status
            query.amount = amount
            query.interest = vfd_interest_rate * amount
            query.borrower = query.transaction.onboarded_user
            query.disbursement_date = query.transaction.date_created
            query.vfd_interest_rate = vfd_interest_rate
            # query.tenor = 4 if query.tenor == 1 else query.tenor
            query.save()

        return self.message_user(request, "status resolved")

    resolve_status.short_description = "Update Status for Vfd Disbursement Logs"

    actions = [resolve_status]


class EasyControlDeviceGroupResourceAdmin(ImportExportModelAdmin):
    resource_class = EasyControlDeviceGroup
    search_fields = ["group_id"]
    # list_filter = [""]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class CRCInformationUploadResourceAdmin(ImportExportModelAdmin):
    resource_class = CRCInformationUploadResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class HealthInsuranceResourceAdmin(ImportExportModelAdmin):
    resource_class = [HealthInsuranceResource]
    search_fields = ["ajo_user__phone_number", "ajo_user__user__email"]
    list_filter = [
        "request_status",
        "fund_transfer_status",
        "provider",
        "vfd_transfer_status",
    ]

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.fields] + [
            "liberty_life_payload",
            "bank_transfer_payload",
        ]
        data.remove("transfer_payload")
        data.remove("payload")
        return data

    def request_create_health_plan(self, request, queryset: QuerySet[HealthInsurance]):

        message = "Invalid Action"
        for insurance_instance in queryset:

            if insurance_instance.request_status != InsuranceRequestStatus.SUCCESS:

                result = HealthInsurance.request_create_paln(
                    loan_id=(
                        insurance_instance.loan.id if insurance_instance.loan else None
                    ),
                    kyc_instance_id=(
                        insurance_instance.kyc_instance
                        if insurance_instance.kyc_instance
                        else None
                    ),
                )
                message = result
            else:
                message = insurance_instance.request_status

        self.message_user(request, str(message))

    def reset_is_processing_for_ops(self, request, queryset: HealthInsurance):
        liberty_life_user_email = settings.LIBERTY_LIFE_AGENCY_USER_EMAIL
        liberty_life_user = User.objects.filter(email=liberty_life_user_email).last()

        queryset.update(is_processing=False)
        for query in queryset:
            query: HealthInsurance
            if query.vfd_transfer and query.vfd_transfer.status == Status.SUCCESS:
                transfer_trans = Transaction.objects.filter(
                    status=Status.SUCCESS,
                    onboarded_user=query.ajo_user,
                    user=liberty_life_user,
                )
                query.vfd_transfer_count = transfer_trans.count()
            elif query.vfd_transfer and query.vfd_transfer.status == Status.FAILED:
                query.vfd_transfer_status = InsuranceRequestStatus.PENDING
            query.save()

    def send_healthplan_funds_libertylife_account(
        self, request, queryset: HealthInsurance
    ):
        if request.user.email not in [
            "<EMAIL>",
            "<EMAIL>",
        ]:
            self.message_user(request, "You are not authorized to perform this action")
            return

        response_message = {}
        savings_access_token = agent_login(token_not_valid=True).get("access")
        for query in queryset:
            query: HealthInsurance
            send = HealthInsurance.send_healthplan_payment_to_liberty_life_account(
                plan=query, access_token=savings_access_token
            )
            response_message[f"{query.ajo_user.phone_number}"] = send
        self.message_user(request, response_message)

    def send_healthplan_funds_borrower_assigned_account(
        self, request, queryset: HealthInsurance
    ):
        response_message = {}
        liberty_life_access_token = liberty_life_agent_login(token_not_valid=True).get(
            "access"
        )
        for query in queryset:
            query: HealthInsurance
            send = (
                HealthInsurance.send_health_plan_payment_to_borrower_assigned_account(
                    plan=query, access_token=liberty_life_access_token
                )
            )
            response_message[f"{query.ajo_user.phone_number}"] = send
        self.message_user(request, response_message)

    request_create_health_plan.short_description = "Create/update health plan"
    request_create_health_plan.allow_tags = True
    reset_is_processing_for_ops.short_description = (
        "Reset IS_PROCESSING from health insurance"
    )
    reset_is_processing_for_ops.allow_tags = True
    send_healthplan_funds_libertylife_account.short_description = (
        "Send health plan funds to Libertylife Account"
    )
    send_healthplan_funds_libertylife_account.allow_tags = True
    send_healthplan_funds_borrower_assigned_account.short_description = (
        "Send health plan funds to borrower Assigned Account"
    )
    send_healthplan_funds_borrower_assigned_account.allow_tags = True

    actions = [
        request_create_health_plan,
        reset_is_processing_for_ops,
        send_healthplan_funds_libertylife_account,
        send_healthplan_funds_borrower_assigned_account,
    ]


class InsuranceRenewalLogResourceAdmin(ImportExportModelAdmin):
    resource_class = [InsuranceRenewalLogResource]
    search_fields = [
        "Insurance__ajo_user__user__email",
        "Insurance__ajo_user__phone_number",
    ]
    list_filter = ["request_type", "created_at", "updated_at"]

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.fields] + ["request"]
        data.remove("request_result")
        return data


class VfdRepaymentScheduleResourceAdmin(ImportExportModelAdmin):
    resource_class = VfdRepaymentScheduleResource
    search_fields = ["borrower_phone_number"]
    list_filter = ["created_at", "updated_at"]
    date_hierarchy = "repayment_date"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]

    def update_repayment_schedule(self, request, queryset):
        for query in queryset:
            query: VfdRepaymentSchedule
            disbursement_log = query.disbursement_log
            tenor = query.tenor
            monthly_amount = (
                disbursement_log.amount + disbursement_log.interest
            ) / tenor
            query.amount_disbursed = disbursement_log.amount
            query.borrower_phone_number = (
                disbursement_log
                and disbursement_log.borrower
                and disbursement_log.borrower.phone
                or None
            )
            query.borrower_first_name = (
                disbursement_log
                and disbursement_log.borrower
                and disbursement_log.borrower.first_name
                or None
            )
            query.borrower_last_name = (
                disbursement_log
                and disbursement_log.borrower
                and disbursement_log.borrower.last_name
                or None
            )
            disbursement_request_data = ast.literal_eval(disbursement_log.request_data)
            trans_ref = disbursement_request_data.get("trans_ref")
            unique_loan_id = disbursement_request_data.get("unique_loan_id")
            query.tenor = disbursement_log.tenor
            query.repayment_amount = monthly_amount
            query.trans_reference = trans_ref
            query.unique_loan_id = unique_loan_id
            query.save()

        return self.message_user(request, "status resolved")

    def complete_repayment_schedules(self, request, queryset):
        for query in queryset:
            query: VfdRepaymentSchedule
            tenor = query.tenor
            existing_schedules = VfdRepaymentSchedule.objects.filter(
                disbursement_log=query.disbursement_log
            ).order_by("repayment_position")

            if existing_schedules.count() < tenor:
                last_position = existing_schedules.last().repayment_position
                disburse_instance = existing_schedules.last().disbursement_log

                current_date = datetime.now().date()

                for position in range(last_position + 1, tenor + 1):
                    if (current_date.month + position) > 12:
                        actual_month = (current_date.month + position) - 12
                        repayment_date = current_date.replace(
                            year=current_date.year + 1, month=actual_month, day=5
                        )
                    else:
                        repayment_date = current_date.replace(
                            month=current_date.month + position, day=5
                        )

                    VfdRepaymentSchedule.objects.create(
                        disbursement_log=disburse_instance or None,
                        amount_disbursed=disburse_instance.amount,
                        borrower_phone_number=disburse_instance
                        and disburse_instance.borrower
                        and disburse_instance.borrower.phone
                        or None,
                        borrower_first_name=disburse_instance
                        and disburse_instance.borrower
                        and disburse_instance.borrower.first_name
                        or None,
                        borrower_last_name=disburse_instance
                        and disburse_instance.borrower
                        and disburse_instance.borrower.last_name
                        or None,
                        repayment_date=repayment_date,
                        tenor=disburse_instance.tenor,
                        repayment_position=position,
                        repayment_amount=existing_schedules.last().repayment_amount,
                    )

    update_repayment_schedule.short_description = "Update Vfd Repayment Schedule"
    complete_repayment_schedules.short_description = "Complete Repayment Schedules"
    actions = [update_repayment_schedule, complete_repayment_schedules]


class VfdRepaymentResourceAdmin(ImportExportModelAdmin):
    resource_class = VfdRepaymentResource
    search_fields = ["borrower_phone_number"]
    list_filter = ["created_at", "updated_at"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]

    def update_existing_vfd_repayments(self, request, queryset: VfdRepayment):
        for query in queryset:
            target_month_end = query.target_month.replace(day=5)

            if query.target_month.month == 1:
                period_start = query.target_month.replace(
                    year=query.target_month.year - 1, day=5, month=12
                )
            else:
                period_start = query.target_month.replace(
                    day=5, month=query.target_month.month - 1
                )

            query_filter = Q(
                created_at__gte=period_start, created_at__lte=target_month_end
            )

            query: VfdRepayment
            query.total_disbursed_target_month = (
                VfdProviderDisbursementLog.objects.filter(
                    query_filter, status=Status.SUCCESS
                ).aggregate(total_amount=Sum("amount"))["total_amount"]
                or 0.00
            )
            query.save()

    update_existing_vfd_repayments.short_description = "Update Existing Repayments"
    update_existing_vfd_repayments.allow_tags = True

    actions = [update_existing_vfd_repayments]


class GroupLoanDisbursementLogResourceAdmin(ImportExportModelAdmin):
    resource_class = GroupLoansDisbursementLogResource
    search_fields = ["borrower_phone_number"]
    list_filter = ["created_at", "updated_at"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class MonnifyAccountFundingLogResourceAdmin(ImportExportModelAdmin):
    resource_class = MonnifyAccountFundingLogResource
    search_fields = ["transaction_reference"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class UserEarningsResourceAdmin(ImportExportModelAdmin):
    resource_class = UserEarningsResource
    search_fields = ["user__email"]
    date_hierarchy = "payroll_date"
    list_filter = ["user_type"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class LoanEligibilityItemResourceAdmin(ImportExportModelAdmin):
    resource_class = LoanEligibilityItemResource
    search_fields = ["ajo_user__phone_number", "phone_number", "ajo_user__user__email"]
    list_filter = ["request_source"]

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.fields]

        return data


class LoanEligibilityVerificationResourceAdmin(ImportExportModelAdmin):
    resource_class = LoanEligibilityVerificationResource
    search_fields = ["ajo_user__phone_number", "phone_number", "ajo_user__user__email"]
    list_filter = ["request_source"]

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.fields]
        data.remove("img_string")
        data.remove("verification_result")
        data.remove("active_result")
        return data

    def get_openai_review(self, request, queryset: LoanEligibilityVerification):
        message = "Invalid Action"

        for instance in queryset:
            verification_object = LoanEligibilityVerification()
            calc = verification_object._perform_loan_calculation(
                instance=instance, ajo_user=instance.ajo_user
            )
            # print(calc)
            message = "success"
        self.message_user(request, message)

    get_openai_review.short_description = "get OpenAI Review"
    get_openai_review.allow_tags = True

    actions = [
        get_openai_review,
    ]


class PastMaturityBufferResourceAdmin(ImportExportModelAdmin):
    resource_classes = [PastMaturityBufferResource]
    search_fields = ["agent__email"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class BulkRepaymentRecordResourceAdmin(ImportExportModelAdmin):
    resource_classes = [BulkRepaymentRecordResource]
    search_fields = ["loan__agent__email", "loan__borrower__phone_number"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class HealthInsuranceSummaryResourceAdmin(ImportExportModelAdmin):
    resource_classes = [HealthInsuranceSummaryResource]
    search_fields = [
        "health_insurance__loan__agent__email",
        "health_insurance__loan__borrower__phone_number",
    ]
    list_filter = ["created_at", "updated_at"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class HealthPlanRenewalResourceAdmin(ImportExportModelAdmin):
    resource_classes = [HealthPlanRenewalResource]
    search_fields = [
        "summary__health_insurance__loan__agent__email",
        "summary__health_insurance__loan__borrower__phone_number",
    ]
    list_filter = [
        "status",
        "was_renewed",
        "transfer_status",
        "created_at",
        "updated_at",
        "borrower_account_transfer_status",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]

    def renewal_notification(self, request, queryset: QuerySet[HealthPlanRenewal]):
        message = "Invalid Action"
        for instance in queryset:
            instance.notify_liberty_life_plan_renewal(renewal_id=instance.id)
            message = "success"
        self.message_user(request, str(message))

    def libertylife_update_user_account_number(
        self, request, queryset: QuerySet[HealthPlanRenewal]
    ):
        message = "Invalid Action"
        for instance in queryset:
            _summary = instance.summary
            health_insurance_instance = _summary.health_insurance

            log_instance = InsuranceRenewalLog.objects.filter(
                Insurance=health_insurance_instance, status__in=[200, 201, "200", "201"]
            ).last()

            if log_instance:
                result = log_instance.request_result
                request_result = parse_to_dict(result)
                account_number = (
                    request_result.get("response", {})
                    .get("enrolee", {})
                    .get("account_number")
                )
                if account_number:
                    plans = HealthPlanRenewal.objects.filter(summary_id=_summary.id)
                    plans.update(account_number=account_number)
                    message = f"Successfully updated account number to {account_number}"
                else:
                    message = "Account number not found in response"
            else:
                message = "Renewal log not found"

        self.message_user(request, str(message))

    def send_healthplan_renewal_funds_libertylife_account(
        self, request, queryset: QuerySet[HealthPlanRenewal]
    ):
        if request.user.email not in [
            "<EMAIL>",
            "<EMAIL>",
        ]:
            self.message_user(request, "You are not authorized to perform this action")
            return

        response_message = {}
        savings_access_token = agent_login(token_not_valid=True).get("access")
        for query in queryset:
            query: HealthPlanRenewal
            send = HealthPlanRenewal.send_healthplan_renewal_payment_to_liberty_life_account(
                plan=query, access_token=savings_access_token
            )
            response_message[f"{query.phone_number}"] = send
        self.message_user(request, response_message)

    def send_healthplan_renewal_funds_borrower_assigned_account(
        self, request, queryset: HealthInsurance
    ):
        if request.user.email not in [
            "<EMAIL>",
            "<EMAIL>",
        ]:
            self.message_user(request, "You are not authorized to perform this action")
            return

        response_message = {}
        liberty_life_access_token = liberty_life_agent_login(token_not_valid=True).get(
            "access"
        )
        for query in queryset:
            query: HealthPlanRenewal
            send = HealthPlanRenewal.send_health_plan_renewal_payment_to_borrower_assigned_account(
                plan=query, access_token=liberty_life_access_token
            )
            response_message[f"{query.phone_number}"] = send
        self.message_user(request, response_message)

    def update_health_plan_remittance(
        self, request, queryset: QuerySet[HealthPlanRenewal]
    ):
        if request.user.email not in [
            "<EMAIL>",
            "<EMAIL>",
        ]:
            self.message_user(request, "You are not authorized to perform this action")
            return

        response_message = {}

        for query in queryset:
            query.borrower_account_transfer_status = (
                query.borrower_account_transfer.status
                if query.borrower_account_transfer
                else query.borrower_account_transfer_status
            )
            query.transfer_status = (
                query.liberty_life_transfer.status
                if query.liberty_life_transfer
                else query.transfer_status
            )
            query.save()

            response_message[f"{query.phone_number}"] = "resolved"

        return self.message_user(request, response_message)

    renewal_notification.short_description = "notify libertylife"
    renewal_notification.allow_tags = True

    send_healthplan_renewal_funds_libertylife_account.short_description = (
        "Send health plan RENEWAL funds to Libertylife Account"
    )
    send_healthplan_renewal_funds_libertylife_account.allow_tags = True

    send_healthplan_renewal_funds_borrower_assigned_account.short_description = (
        "Send health plan RENEWAL funds to borrower assigned Account"
    )
    send_healthplan_renewal_funds_borrower_assigned_account.allow_tags = True

    update_health_plan_remittance.short_description = "Update Health Plan Remittance"
    update_health_plan_remittance.allow_tags = True

    libertylife_update_user_account_number.short_description = (
        "Libertylife update account number"
    )
    libertylife_update_user_account_number.allow_tags = True

    actions = [
        renewal_notification,
        send_healthplan_renewal_funds_libertylife_account,
        send_healthplan_renewal_funds_borrower_assigned_account,
        update_health_plan_remittance,
        libertylife_update_user_account_number,
    ]


class RenewalAllocationResourceAdmin(ImportExportModelAdmin):
    resource_classes = [RenewalAllocationResource]
    search_fields = [
        "repayment_instance__ajo_loan__agent__email",
        "repayment_instance__ajo_loan__borrower__phone_number",
    ]
    list_filter = ["created_at", "updated_at"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class ExcessRenewalRecordsResourceAdmin(ImportExportModelAdmin):
    resource_classes = [ExcessRenewalRecordsResource]
    search_fields = ("phone_number",)
    list_filter = ("renewal_due", "created_at", "updated_at")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


class OpenAiEligbilityReviewResourceAdmin(ImportExportModelAdmin):
    resource_classes = [OpenAiEligbilityReviewResource]
    search_fields = ["phone_number"]
    list_filter = ["request_source", "created_at", "updated_at"]

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.fields] + [
            "request_body",
            "result",
        ]
        data.remove("payload")
        data.remove("review_result")
        return data


class CreditWorthinessDataAdmin(ImportExportModelAdmin):
    resource_class = CreditWorthinessDataResource
    search_fields = (
        "ajo_user__phone_number",
        "ajo_user__user__email",
        "mono_account_id",
        "reference",
    )
    list_filter = (
        "completed_check",
        "is_mono_valid",
        "date_created",
    )
    list_display = (
        "id",
        "ajo_user",
        "reference",
        "mono_account_id",
        "completed_check",
        "is_mono_valid",
        "date_created",
        "last_updated",
    )
    ordering = ("-date_created",)


class LoanAffordabilityResourceAdmin(ImportExportModelAdmin):
    resource_classes = LoanAffordabilityResource
    # search_fields = ["phone_number"]
    # list_filter = ["created_at", "updated_at"]

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.fields] + [
            "snapped_image_str",
            "id_image_str",
        ]
        data.remove("snapped_image")
        data.remove("id_image")
        return data


class LoanDiskLoansMirrorAdmin(ImportExportModelAdmin):
    search_fields = (
        "loan_id",
        "borrower",
        "borrower_phone",
    )

    list_filter = (
        "resolved",
        "request_type",
        "status",
        "loan_status",
        "loan_verification_stage",
        "date_created",
        "date_disbursed",
    )

    list_display = (
        "loan_id",
        "amount",
        "repayment_type",
        "resolved",
        "request_type",
        "status",
        "loan_status",
        "loan_verification_stage",
        "borrower",
        "borrower_phone",
        "date_created",
        "last_updated",
        "date_disbursed",
        "date_completed",
    )

    ordering = ("-date_created",)


class CreditHealthRecordResourceAdmin(ImportExportModelAdmin):
    resource_classes = CreditHealthRecordResource
    search_fields = (
        "loan_access_code",
        "borrower_phone_number",
    )

    # list_filter = ("",)

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.fields]
        return data


class CreditHealthAgentRequestResourceAdmin(ImportExportModelAdmin):
    resource_classes = CreditHealthAgentRequestResource
    search_fields = ("loan_access_code",)

    # list_filter = ("",)

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.fields]
        return data


class SeedsAccountBalanceResourceAdmin(ImportExportModelAdmin):
    resource_classes = SeedsAccountBalanceResource
    search_fields = ("loan_access_code",)

    # list_filter = ("",)

    def get_list_display(self, request):
        data = [
            "created_at",
            "spend_wallet_balance",
            "wema_ajo_loans_balance",
            "raw_wema_balance",
            "wema_response",
            "updated_at",
        ]
        return data

    def update_wallet_balance(self, request, queryset):
        for query in queryset:
            spend_wallet_balance = (
                WalletSystem.objects.filter(
                    wallet_type=WalletTypes.AJO_SPENDING
                ).aggregate(total_amount=Sum("available_balance"))["total_amount"]
                or 0.00
            )

            response = CoreBankingManager.check_account_details()
            wema_ajo_loans_balance = (
                response.get("data", {}).get("acount_details").get("cash_balance", 0.00)
            )

            final_wema_ajo_loans_balance = (
                float(wema_ajo_loans_balance.replace("NGN", "").replace(",", ""))
                if isinstance(wema_ajo_loans_balance, str)
                else wema_ajo_loans_balance
            )

            query.spend_wallet_balance = spend_wallet_balance
            query.wema_ajo_loans_balance = final_wema_ajo_loans_balance
            query.wema_response = response
            query.raw_wema_balance = wema_ajo_loans_balance
            query.save()
        self.message_user(request, "Updated successfully")

    update_wallet_balance.add_tags = True
    update_wallet_balance.short_description = (
        "Wallet Balance: Update SPEND WALLET BALANCE"
    )

    actions = [
        update_wallet_balance,
    ]


class MerchantEligibilitySummaryAdmin(ImportExportModelAdmin):
    resource_classes = [MerchantEligibilitySummaryResource]
    search_fields = ("phone_number", "email")
    list_filter = (
        "application_stage",
        "type_of_user",
        "has_terminal",
        "eligible",
        "source",
        "created_at",
        "updated_at",
    )

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.fields]
        return data

    def fetch_single_eligibility(
        self, request, queryset: QuerySet[MerchantEligibilitySummary]
    ):
        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:

            for instance in queryset:
                message = MerchantEligibilitySummary.get_transaction_from_liberty_pay_create_eligibility_summary(
                    merchant_id=instance.user_id
                )

        self.message_user(request, str(message))

    def fetch_all_eligibility(
        self, request, queryset: QuerySet[MerchantEligibilitySummary]
    ):
        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:

            for instance in queryset:
                message = (
                    MerchantEligibilitySummary.get_transaction_from_liberty_pay_create_eligibility_summary()
                )

        self.message_user(request, str(message))

    def update_eligible_to_not_eligible(
        self, request, queryset: QuerySet[MerchantEligibilitySummary]
    ):
        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:

            for instance in queryset:
                message = MerchantEligibilitySummary.objects.filter(
                    eligible=True, application_stage=None
                ).update(eligible=False)

        self.message_user(request, str(message))

    def update_specific_eligible_to_not_eligible(
        self, request, queryset: QuerySet[MerchantEligibilitySummary]
    ):
        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:

            for instance in queryset:
                instance.eligible = False
                instance.application_stage = None
                instance.save(update_fields=["eligible", "application_stage"])
                message = "success"

        self.message_user(request, str(message))

    def update_info_from_agency_banking(
        self, request, queryset: QuerySet[MerchantEligibilitySummary]
    ):
        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:

            for instance in queryset:
                agency_banking_handler = LibertyPayMgr(config=settings)
                get_user_info = agency_banking_handler.get_agency_banking_users(
                    id=instance.user_id
                )
                user_info_response = get_user_info.get("response", [{}])[0]
                date_joined = user_info_response.get("date_joined")
                officer_info = user_info_response.get("acquisition_officer", {})
                accof_first_name = officer_info.get("first_name")
                accof_last_name = officer_info.get("last_name")
                accof_phone = normalize_ngn_phone(
                    officer_info.get("phone_number", "**********")
                )

                instance.acquisition_officer_full_name = (
                    f"{accof_first_name} {accof_last_name}"
                )
                instance.acquisition_officer_phone_number = accof_phone
                instance.date_joined = date_joined
                instance.save(
                    update_fields=[
                        "acquisition_officer_full_name",
                        "acquisition_officer_phone_number",
                        "date_joined",
                    ]
                )
                message = "success"

        self.message_user(request, str(message))

    fetch_single_eligibility.short_description = "User ID: Get summary"
    fetch_single_eligibility.allow_tags = True

    fetch_all_eligibility.short_description = "All: Get summary"
    fetch_all_eligibility.allow_tags = True

    update_eligible_to_not_eligible.short_description = "Update all to not eligible"
    update_eligible_to_not_eligible.allow_tags = True

    update_specific_eligible_to_not_eligible.short_description = (
        "Update instance to not eligible"
    )
    update_specific_eligible_to_not_eligible.allow_tags = True

    update_info_from_agency_banking.short_description = (
        "Update Acquisition officer Fields"
    )
    update_info_from_agency_banking.allow_tags = True

    actions = [
        fetch_single_eligibility,
        fetch_all_eligibility,
        update_eligible_to_not_eligible,
        update_specific_eligible_to_not_eligible,
        update_info_from_agency_banking,
    ]


class SMSLogAdmin(ImportExportModelAdmin):
    resource_classes = SMSLogResource
    search_fields = ("ajo_user__phone_number", "ajo_user__user__email")
    list_filter = ("sent", "msg_type", "user_type")

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.fields]
        return data


class MerchantDirectDebitAccountAdmin(ImportExportModelAdmin):
    resource_classes = MerchantDirectDebitAccountResource
    # search_fields = ("phone_number", "email")
    # list_filter = ("type_of_user",)

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.fields]
        return data


class AgentRepaymentPerformanceAdmin(ImportExportModelAdmin):
    resource_classes = AgentRepaymentPerformanceResource
    search_fields = ("agent__email",)
    # list_filter = ("type_of_user",)

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.fields]
        return data


@admin.register(SMSTemplate)
class SMSTemplateAdmin(ImportExportModelAdmin):
    resource_class = SMSTemplateResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


@admin.register(LostLoan)
class LostLoanAdmin(ImportExportModelAdmin):
    resource_class = LostLoanResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


@admin.register(OutstandingLoanCharge)
class OutstandingLoanChargeAdmin(ImportExportModelAdmin):
    resource_class = OutstandingLoanChargeResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


@admin.register(LoanChargeAttempt)
class LoanChargeAttemptAdmin(ImportExportModelAdmin):
    resource_class = LoanChargeAttemptResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(LoanDiskLoansMirror, LoanDiskLoansMirrorAdmin)
admin.site.register(CreditWorthinessData, CreditWorthinessDataAdmin)
admin.site.register(AgentCommissionPayroll, AgentCommissionPayrollResourceAdmin)
admin.site.register(MissedRepaymentsTable, MissedRepaymentsTableResourceAdmin)
admin.site.register(Missed14Days, Missed14DaysResourceAdmin)
admin.site.register(Missed28Days, Missed28DaysResourceAdmin)
admin.site.register(Missed60Days, Missed60DaysResourceAdmin)
admin.site.register(Missed90Days, Missed90DaysResourceAdmin)
admin.site.register(MissedPastMaturity15Days, MissedPastMaturity15DaysResourceAdmin)
admin.site.register(MissedLost, MissedLostResourceAdmin)
admin.site.register(LoanAnalysisLog, LoanAnalysisLogResourceAdmin)
admin.site.register(LoanEligibility, LoanEligibilityResourceAdmin)
admin.site.register(AjoLoan, AjoLoanResourceResourceAdmin)
admin.site.register(ProsperAgent, ProsperAgentResourceResourceAdmin)
admin.site.register(BorrowerInfo, BorrowerInfoResourceAdmin)
admin.site.register(LoanGuarantor, LoanGuarantorResourceAdmin)
admin.site.register(AjoLoanRepayment, AjoLoanRepaymentResourceAdmin)
admin.site.register(BiometricsMetaData, BiometricsMetaDataResourceAdmin)
admin.site.register(YouVerifyRequest, YouVerifyRequestResourceAdmin)
admin.site.register(CreditBureauMetaData, CreditBureauMetaDataResourceAdmin)
admin.site.register(ProsperAgentPaymentRecord, ProsperAgentPaymentRecordResourceAdmin)
admin.site.register(LoanDiskMetaData, LoanDiskMetaDataResourceAdmin)
admin.site.register(RepaymentChecker, RepaymentCheckerResourceAdmin)
admin.site.register(
    BorrowerCreditBureauWorthiness, BorrowerCreditBureauWorthinessResourceAdmin
)
admin.site.register(
    AjoUserAgencyBankingBranchRequestDataDump,
    AjoUserAgencyBankingBranchRequestDataDumpResourceAdmin,
)

admin.site.register(
    RepaymentVirtuaWalletCreditRecord, RepaymentVirtuaWalletCreditRecordAdmin
)
admin.site.register(Holiday, HolidayResourceAdmin)
admin.site.register(LoanKYCDocumentation, LoanKYCDocumentationResourceAdmin)
admin.site.register(AccountBalances, AccountBalancesResourceAdmin)
admin.site.register(InvestmentCapital, InvestmentCapitalResourceAdmin)
admin.site.register(TotalInvestmentCapital, TotalInvestmentCapitalResourceAdmin)
admin.site.register(AjoLoanSchedule, AjoLoanScheduleResourceAdmin)
admin.site.register(ComplianceChecksLogs, ComplianceChecksLogsResourceAdmin)
admin.site.register(GiniMachineLog, GiniMachineLogResourceAdmin)
admin.site.register(ProductAssignment, ProductAssignmentResourceAdmin)
admin.site.register(EasyControlMetaData, EasyControlMetaDataResourceAdmin)
admin.site.register(LoanFeedback, LoanFeedbackResourceAdmin)
admin.site.register(EscrowDebitErrorLog, EscrowDebitErrorLogResourceAdmin)
admin.site.register(
    OffetPastMaturityWithEscrowLog, OffetPastMaturityWithEscrowLogResourceAdmin
)
admin.site.register(VfdProviderDisbursementLog, VfdProviderDisbursementLogResourceAdmin)
admin.site.register(EasyControlDeviceGroup, EasyControlDeviceGroupResourceAdmin)
admin.site.register(CRCInformationUpload, CRCInformationUploadResourceAdmin)
admin.site.register(HealthInsurance, HealthInsuranceResourceAdmin)
admin.site.register(VfdRepaymentSchedule, VfdRepaymentScheduleResourceAdmin)
admin.site.register(VfdRepayment, VfdRepaymentResourceAdmin)
admin.site.register(GroupLoansDisbursementLog, GroupLoanDisbursementLogResourceAdmin)
admin.site.register(MonnifyAccountFundingLog, MonnifyAccountFundingLogResourceAdmin)
admin.site.register(UserEarnings, UserEarningsResourceAdmin)
admin.site.register(LoanEligibilityItem, LoanEligibilityItemResourceAdmin)
admin.site.register(
    LoanEligibilityVerification, LoanEligibilityVerificationResourceAdmin
)
admin.site.register(InsuranceRenewalLog, InsuranceRenewalLogResourceAdmin)
admin.site.register(PastMaturityBuffer, PastMaturityBufferResourceAdmin)
admin.site.register(BulkRepaymentRecord, BulkRepaymentRecordResourceAdmin)
admin.site.register(HealthInsuranceSummary, HealthInsuranceSummaryResourceAdmin)
admin.site.register(HealthPlanRenewal, HealthPlanRenewalResourceAdmin)
admin.site.register(RenewalAllocation, RenewalAllocationResourceAdmin)
admin.site.register(ExcessRenewalRecords, ExcessRenewalRecordsResourceAdmin)
admin.site.register(OpenAiEligbilityReview, OpenAiEligbilityReviewResourceAdmin)
admin.site.register(LoanAffordability, LoanAffordabilityResourceAdmin)
admin.site.register(CreditHealthRecord, CreditHealthRecordResourceAdmin)
admin.site.register(CreditHealthAgentRequest, CreditHealthAgentRequestResourceAdmin)
admin.site.register(SeedsAccountBalance, SeedsAccountBalanceResourceAdmin)
admin.site.register(MerchantEligibilitySummary, MerchantEligibilitySummaryAdmin)
admin.site.register(SMSLog, SMSLogAdmin)
admin.site.register(MerchantDirectDebitAccount, MerchantDirectDebitAccountAdmin)
admin.site.register(AgentRepaymentPerformance, AgentRepaymentPerformanceAdmin)
