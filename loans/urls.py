from django.urls import path

from loans.views import *

eligibility = [
    path("eligible_savers/", GetEligibleSaver.as_view(), name="eligible_savers"),
    path("send_otp/", SendVerificationOtpView.as_view()),
    path("verification/", VerifyLoanApplicant.as_view()),
    path("summary/", GetLoanAmountSummaryView.as_view()),
    path("savings-eligible/", GetSavingsEligibility.as_view()),
    path("info_status/", BorrowerInfoStatus.as_view()),
    path("get-single-multiple-eligibiity/", GetSingleMultipleEligibility.as_view()),
    path("get-agent-eligibility-summary/", StaffLoanEligibilitySummary.as_view()),
    path(
        "credict_bureau_check_status/",
        BorrowerCreditBureauWorthinessCheckApiView.as_view(),
    ),
]

borrower = [
    path("borrower_info/", BorrowerInfoView.as_view()),
    path("guarantor/", GuarantorView.as_view()),
    path("face_match/", FaceMatch.as_view()),
    path("validate-bad-loan/", UpdateBadLoanToGuarantor.as_view()),
    path("guarantor/face-match/", GuarantorFaceMatch.as_view()),
]

call_back = [
    path("dojah/verify/", GuarantorDjoahCallBack.as_view()),
]

loan = [
    path("get_loan_tiers/", GetLoanTIers.as_view()),
    path("loan_amount/", LoanAmountApplication.as_view()),
    path("disburse_loan/", DisburseAjoLoan.as_view()),
    path("loan_history/", LoanHistoryAPIView.as_view()),
    path("ajo-loan-calculator/", AjoLoanCalculatorView.as_view()),
    path("prosper-agent-balances/", GetProsperBalancesAPIView.as_view()),
    path("loan-percent-checker/", DueLoanCheckerView.as_view()),
    path("get-loan-settings/", GetLoanLoanSettingsAPIView.as_view()),
    path("post_loan_to_loandisk/", PostLoanToLoanDiskManually.as_view()),
    path("update_loan_on_loandisk/", UpdateLoanToLoanDiskManually.as_view()),
    path("get_loans_without_loandisk_id", GetLoansNotOnLoanDisk.as_view()),
    path("post_borrower_to_loandisk/", PostBorrowerToLoanDiskManually.as_view()),
    path(
        "bad_loandisk_borrower_id",
        LoanDiskBorrowersWithBadID.as_view(),
        name="bad_loandisk_borrower_id",
    ),
    path(
        "loandisk_borrower/<str:ajo_user_id>",
        LoanDiskBorrower.as_view(),
        name="get_loandisk_borrowers",
    ),
    path(
        "fix_borrower_id/<str:ajo_user_id>",
        FixLoanDiskBorrowerID.as_view(),
        name="fix_borrower_id",
    ),
    path(
        "post_repayment_to_loandisk/",
        PostRepaymentsManuallyToLoanDisk.as_view(),
        name="post_repayments_to_loandisk",
    ),
    path(
        "borrowers_not_on_loandisk",
        BorrowersNotOnLoanDisk.as_view(),
        name="borrowers_not_on_loandisk",
    ),
    path(
        "retrieve_borrower_from_loandisk/<int:ajo_user_id>",
        RetrieveBorrowerFromLoanDisk.as_view(),
        name="retrieve_borrower_from_loandisk",
    ),
    path(
        "send_seeds_payroll_to_paybox/",
        SendSeedsPayrollToPayboxView.as_view(),
        name="send_seeds_payroll_to_paybox",
    ),
]

repayment = [
    path("wallet_details/", RepaymentWalletDetails.as_view()),
    path("core_repayment/", RepaymentCallBack.as_view()),
    path("repay_loan/", AjoLoanRepaymentAPIView.as_view()),
    path("repayment_history/", LoanRepaymentHistory.as_view()),
    path("due_loans/", DueLoanRepayment.as_view()),
    path("bulk_repayment/", BulkRepaymentSerializerView.as_view()),
    path("bulk_repayment_history/", BulkRepaymentHistory.as_view()),
]

kyc_docs = [
    path("send-otp-kyc-doc/", SendOtpLoanKYCDocumentation.as_view()),
    path("kyc-doc-verify-otp/", VerifyLoanKYCOTPView.as_view()),
    path("process-kyc-doc/", ProcessLoanKYCDocumentation.as_view()),
    path("create-kyc-doc/", CreateLoanKYCDocumentation.as_view()),
    path("get-kyc-docs/", GetLoanKYCDocumentation.as_view(), name="get_documentations"),
    path("create-loan-kyc-docs/", CreateLoanForExistingKYCDocumentation.as_view()),
    path("loan-kyc-update/", UpdateDocumentationkycWebHook.as_view()),
    path("re-process-doc/<str:stage>/", ReProcessLoanKYCDocumentation.as_view()),
    path("loan_status/", GetLoanStatus.as_view()),
]

leader_board = [
    path("leader-board/", GetAgentLeaderBoard.as_view()),
    path("leader-board-agent/", GetStaffAgentLeaderBoard.as_view()),
]

bnpl = [
    path("bnpl/list-assign-product/", GetProductAssignment.as_view()),
    path("bnpl/assign-product/", AssignProductView.as_view()),
    path("bnpl/add-imei/", AddProductImeiView.as_view()),
    path("bnpl/confirm-imei-status/", ConfirmIMEIStatusView.as_view()),
    path("bnpl/bulk_update/", ProductImeiBulkUpdate.as_view()),
    path("bnpl/get-loan-status/", GetLoanStatusWithImei.as_view()),
]

direct_debit = [
    path("internal/credit_worthiness", CreditWorthiness.as_view()),
    path("mono/credit_webhook", MonoCreditWorthinessWebhook.as_view()),
]

others = [
    path("send-otp-mobile-number/", LoanSendOtpPhoneNumberView.as_view()),
    path("verify-otp-mobile-number/", VerifyGuarantorPhoneOtpView.as_view()),
    path("check-id/", ReusableIdCheckView.as_view()),  # reusable nin/bvn services
    path("check-id/main/", IdCheckView.as_view()),
    path("first-central-check/", RawBureauCheck.as_view()),
    path("const/", GetLoanConstant.as_view()),
    path("feedback/", LoanFeedbackApi.as_view()),
    path("agency-user-details/", GetAgencyBankingAgent.as_view()),
    path("borrower_images/", GetLoanBorrowerImages.as_view()),
    path("guarantor_images/", GetLoanGuarantorImages.as_view()),
    path("update-savers-fullname/", UpdateSaversName.as_view()),
    path("update-account-balances/", UpdateAccountBalancesAPIView.as_view()),
]

boosta = [
    path("boosta-summary/", GetBoostaLoanSummary.as_view()),
]


admin_calls = [
    path("borrower_images/", GetLoanBorrowerImages.as_view()),
    path("update-savers-fullname/", UpdateSaversName.as_view()),
    path("guarantor_images/", GetLoanGuarantorImages.as_view()),
    path("admin-evaluate-topup/", AdminDevGetTopUpEvaluation.as_view()),
    path(
        "api/guarantor-borrower-info-details/",
        GetGuarantorDetailsByVerificationID.as_view(),
    ),
    path("api/healthplan-info-details/", GetHealthPlanDetailsByIDorPhone.as_view()),
    path("api/approve-eligbility/", ApproveEligiblityInstance.as_view()),
    path("api/bureaResult/", GetBureauDataView.as_view()),
    path("api/admin-kyc-doc-update/", AdminLoanKYCDocumentationView.as_view()),
    path(
        "api/admin-update_date_disbursed/", AdminUpdateDisbursementDateAPIView.as_view()
    ),
    path("api/v1/admin-get-sms-summary/", GetSmsSummaryAPIView.as_view()),
]

helpers = [
    path("insurance-summary/", HealthInsuranceSummary.as_view()),
    path("preview-loan-recalculation/", PreviewLoanUpdateRecalculationView.as_view()),
    path("update-loan-actual-end-date/", UpdateLoanActualEnddate.as_view()),
]

top_up = [
    path("top-up/", LoanTopUpApiView.as_view(), name="loan_topup"),
]

eligibility_checker = [
    path("request_amount_checker/", VerifyLoanEligibilityView.as_view()),
    path("checker/history/", CheckerHistory.as_view()),
    path("checker/id-verification/", CheckerIdVerification.as_view()),
    path("checker/face-similarity/", CheckerImageVerification.as_view()),
    path("checker/intended-amount/", CheckerIntendedAmountVerification.as_view()),
]

credit_health = [
    path(
        "apiv1/credit-health/inquire-summary/",
        CreditHealthInquiryHandler.as_view(),
        name="credit_health_inquiry",
    ),
    path(
        "apiv1/credit-health/amount-verification/",
        CreditHealthAmountInquiryHandler.as_view(),
        name="credit_health_amount_inquiry",
    ),
    path(
        "get-credit-health/",
        GetPendingCreditHealthFromLibertyLife.as_view(),
        name="get_credit_health",
    ),
    path(
        "ceardit-health/verify-otp-create-user/",
        VerifyOtpCreateUserAndSavingsUnderAgent.as_view(),
        name="verify_otp_create_user",
    ),
]

merchant_loan = [
    path(
        "merchant/get-eligibility-summary/",
        GetEligibleMerchant.as_view(),
        name="get_merchant_summary",
    ),
    path(
        "merchant/charge-processing-fee/",
        ChargeMerchantLoanProccessingFee.as_view(),
        name="merchant_charge_processing_fee",
    ),
    path(
        "merchant/eligibility-status/",
        GetMerchantEligibilitySummary.as_view(),
        name="eligibility_status",
    ),
    path(
        "merchant/credit-worthiness/",
        GetCreditWorthinessApiView.as_view(),
        name="merchant_credit_worthiness",
    ),
    path(
        "merchant/create-directdebit-account/",
        MerchantDirectDebitAccountCreateView.as_view(),
        name="merchant_direct_debit_account",
    ),
    path(
        "merchant/disbursement/",
        DisburseMerchantLoan.as_view(),
        name="merchant_loan_disbursement",
    ),
    path(
        "merchant/repayment/",
        MerchantLoanRepayment.as_view(),
        name="merchant_loan_repayment",
    ),
]

urlpatterns = [
    *eligibility,
    *loan,
    *borrower,
    *call_back,
    *repayment,
    *kyc_docs,
    *leader_board,
    *others,
    *bnpl,
    *helpers,
    *top_up,
    *admin_calls,
    *direct_debit,
    *eligibility_checker,
    *credit_health,
    *boosta,
    *merchant_loan,
]
