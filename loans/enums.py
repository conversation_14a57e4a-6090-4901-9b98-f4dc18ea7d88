from django.db.models import TextChoices, IntegerChoices
from django.utils.translation import gettext_lazy as _


class LoanStatus(TextChoices):
    PENDING = "PENDING", "PENDING"
    APPROVED = "APPROVED", "APPROVED"
    REJECTED = "REJECTED", "REJECTED"
    IN_PROGRESS = "IN_PROGRESS", "IN_PROGRESS"
    COMPLETED = "COMPLETED", "COMPLETED"
    DEFAULTED = "DEFAULTED", "DEFAULTED"
    OPEN = "OPEN", "OPEN"
    AWAITING_CRC = "AWAITING_CRC_VERIFICATION", "AWAITING_CRC_VERIFICATION"
    PROCESSING = "PROCESSING", "PROCESSING"
    CLOSED = "CLOSED", "CLOSED"
    DENIED = "DENIED", "DENIED"
    NOT_TAKEN = "NOT_TAKEN", "NOT TAKEN"
    OPEN_TO_SUPERVISOR = "OPEN_TO_SUPERVISOR", "OPEN_TO_SUPERVISOR"
    DECLINED_BY_SUPERVISOR = "DECLINED_BY_SUPERVISOR", "DECLINED_BY_SUPERVISOR"
    APPROVED_BY_SUPERVISOR = "APPROVED_BY_SUPERVISOR", "APPROVED_BY_SUPERVISOR"
    PENDING_PRODUCT_ASSIGNMENT = (
        "PENDING_PRODUCT_ASSIGNMENT",
        "PENDING PRODUCT ASSIGNMENT",
    )
    PAST_MATURITY = "PAST_MATURITY", "PAST_MATURITY"


class LoanPerformanceStatus(TextChoices):
    PERFORMING = "PERFORMING", "PERFORMING"
    DEFAULTED = "DEFAULTED", "DEFAULTED"
    LOST = "LOST", "LOST"
    PAST_MATURITY = "PAST_MATURITY", "PAST_MATURITY"
    OWED_BALANCE = "OWED_BALANCE", "OWED_BALANCE"


class LoanBehavior(TextChoices):
    ACTIVE = "ACTIVE", "ACTIVE"
    ONTIME = "ONTIME", "ONTIME"
    LATE = "LATE", "LATE"
    PARTIALPAYMENT = "PARTIALPAYMENT", "PARTIAL PAYMENT"
    PAST_MATURITY = "PAST_MATURITY", "PAST MATURITY"
    DEFAULTED = "DEFAULTED", "DEFAULTED"
    BAD = "BAD", "BAD"


class Feedback(TextChoices):
    EXCELLENT = "EXCELLENT", "EXCELLENT"
    GOOD = "GOOD", "GOOD"
    VERY_BAD = "VERY_BAD", "VERY BAD"
    FAIR = "FAIR", "FAIR"
    BAD = "BAD", "BAD"


class LoanType(TextChoices):
    AJO = "AJO", "AJO"
    BNPL = "BNPL", "BNPL"
    BOOSTA = "BOOSTA", "BOOSTA"
    BOOSTA_2X = "BOOSTA_2X", "BOOSTA_2X"
    AJOSEPO = "AJOSEPO", "AJOSEPO"
    PROSPER_LOAN = "PROSPER_LOAN", "PROSPER_LOAN"
    OTHER = "OTHER", "OTHER"
    BOOSTA_2X_MINI = "BOOSTA_2X_MINI", "BOOSTA_2X_MINI"
    CREDIT_HEALTH = "CREDIT_HEALTH", "CREDIT_HEALTH"
    MERCHANT = "MERCHANT", "MERCHANT"
    MERCHANT_OVERDRAFT = "MERCHANT_OVERDRAFT", "MERCHANT_OVERDRAFT"


class EligibilityType(TextChoices):
    PRIMARY = "PRIMARY", "PRIMARY"
    TOP_UP = "TOP_UP", "TOP UP"


class TopUpDecisionStatus(TextChoices):
    TAKEN = "TAKEN", "TAKEN"
    DECLINED = "DECLINED", "DECLINED"
    PENDING = "PENDING", "PENDING"


class VerificationType(TextChoices):
    NIN = "NIN", "NIN"
    BVN = "BVN", "BVN"


class IdentityRequestSource(TextChoices):
    IN_APP = "IN_APP", "IN APP"
    EXTERNAL = "EXTERNAL", "EXTERNAL"


class VerificationProvider(TextChoices):
    YOUVERIFY = "YOUVERIFY", "YOUVERIFY"
    DOJAH = "DOJAH", "DOJAH"
    ONBOARDING_DATA = "ONBOARDING_DATA", "ONBOARDING DATA"
    YOUVERIFY_DOJAH = "YOUVERIFY_DOJAH", "YOUVERIFY/DOJAH"


class RepaymentType(TextChoices):
    TRANSFER = "TRANSFER", "TRANSFER"
    LOAN_RECOVERY = "LOAN_RECOVERY", "LOAN_RECOVERY"
    AGENT_DEBIT = "AGENT_DEBIT", "AGENT_DEBIT"
    SAVINGS_DEBIT = "SAVINGS_DEBIT", "SAVINGS_DEBIT"
    SPEND_DEBIT = "SPEND_DEBIT", "SPEND_DEBIT"
    ESCROW_DEBIT = "ESCROW_DEBIT", "ESCROW_DEBIT"
    FIRST_WEEK = "FIRST_WEEK ", "FIRST_WEEK"
    LITE_DEBIT = "LITE_DEBIT ", "LITE_DEBIT"
    ESCROW_REPAY = "ESCROW_REPAY ", "ESCROW_REPAY"
    DEDUCTABLE_REPAY = "DEDUCTABLE_REPAY ", "DEDUCTABLE_REPAY"
    LIBERTYPAY_DIRECTDEBIT = "LIBERTYPAY_DIRECTDEBIT ", "LIBERTYPAY_DIRECTDEBIT"
    MERCHANT_DEBIT = "MERCHANT_DEBIT ", "MERCHANT_DEBIT"


class VerificationStage(TextChoices):
    FACE_MATCH = "FACE_MATCH", "FACE MATCH"
    OTP = "OTP", "OTP"
    ADDRESS_VERIFICATION = "ADDRESS_VERIFICATION", "ADDRESS VERIFICATION"
    ADDRESS_VALIDATION = "ADDRESS_VALIDATION", "ADDRESS VALIDATION"
    NEW_ADDRESS_VERIFICATION = "NEW_ADDRESS_VERIFICATION", "NEW ADDRESS VERIFICATION"
    BORROWER_INFO = "BORROWER_INFO", "BORROWER INFO"
    GUARANTOR = "GUARANTOR", "GUARANTOR"
    GUARANTOR_CAPTURE = "GUARANTOR_CAPTURE", "GUARANTOR_CAPTURE"
    CREDIT_BUREAU = "CREDIT_BUREAU", "CREDIT BUREAU"
    APPROVAL = "APPROVAL", "APPROVAL"
    DISBURSEMENT = "DISBURSEMENT", "DISBURSEMENT"
    WARNING = "BAD_LOAN_WARNING", "BAD LOAN WARNING"
    SUPERVISOR_DISBURSEMENT = "SUPERVISOR_DISBURSEMENT", "SUPERVISOR_DISBURSEMENT"
    PRODUCT_ASSIGNMENT = "PRODUCT_ASSIGNMENT", "PRODUCT ASSIGNMENT"
    OTHERS = "OTHERS", "OTHERS"
    PROCESSING_FEE = "PROCESSING_FEE", "PROCESSING FEE"
    DOCUMENTATION = "DOCUMENTATION", "DOCUMENTATION"


class PreferredVerification(TextChoices):
    CAPTURE = "CAPTURE", "CAPTURE"
    LINK = "LINK", "LINK"


class Tenure(TextChoices):
    ONE_WEEK = "1_WEEK", "7"
    TWO_WEEKS = "2_WEEK", "14"
    ONE_MONTH = "1_MONTH", "30"
    SIX_WEEKS = "6_WEEKS", "45"
    TWO_MONTH = "2_MONTH", "60"
    THREE_MONTH = "3_MONTH", "90"
    FOUR_MONTH = "4_MONTH", "120"
    FIVE_MONTH = "5_MONTH", "150"
    SIX_MONTH = "6_MONTH", "180"

    @classmethod
    def match_value_get_days(cls, value: "Tenure") -> int:
        tenor_labels = {label: int(tenor) for label, tenor in cls.choices}
        matched_tenure = tenor_labels.get(value, False)
        assert matched_tenure, Exception("Invalid Loan Tenure")
        return matched_tenure

    @classmethod
    def match_value(cls, value: int) -> str:
        tenor_labels = {int(tenor): label for label, tenor in cls.choices}
        matched_tenure = tenor_labels.get(value, False)
        assert matched_tenure, Exception("Invalid Loan Tenure")
        return matched_tenure


class RepaymentFrequency(TextChoices):
    DAILY = "DAILY", _("DAILY")
    WEEKLY = "WEEKLY", _("WEEKLY")


class LoanChargeNarration(TextChoices):
    LOAN_REPAYMENT = "LOAN_REPAYMENT", "LOAN_REPAYMENT"
    LOAN_FEE = "LOAN_FEE", "LOAN_FEE"
    LOAN_DISBURSEMENT = "LOAN_DISBURSEMENT", "LOAN_DISBURSEMENT"


class BiometricsRequestType(TextChoices):
    LIVELINESS = "LIVELINESS", "LIVELINESS"
    FACE_MATCH = "FACE_MATCH", "FACE MATCH"


class UserVerificationType(TextChoices):
    BORROWER = "BORROWER", "BORROWER"
    GUARANTOR = "GUARANTOR", "GUARANTOR"
    AGENT = "AGENT", "AGENT"


class DisbursementStatus(TextChoices):
    PENDING = "PENDING", "PENDING"
    SUCCESSFUL = "SUCCESSFUL", "SUCCESSFUL"
    FAILED = "FAILED", "FAILED"
    IN_PROGRESS = "IN_PROGRESS", "IN_PROGRESS"


class NinBvnRequestStatus(TextChoices):
    SUCCESS = "SUCCESS", "SUCCESS"
    FAILED = "FAILED", "FAILED"
    PENDING = "PENDING", "PENDING"
    PROCESSING = "PROCESSING", "PROCESSING"


class DocumentationStatus(TextChoices):
    PROCESSING = "PROCESSING", "PROCESSING"
    PENDING = "PENDING", "PENDING"
    COMPLETED = "COMPLETED", "COMPLETED"
    INCOMPLETE = "INCOMPLETE", "INCOMPLETE"
    FAILED = "FAILED", "FAILED"
    SUCCESS = "SUCCESS", "SUCCESS"
    NOT_USED = "NOT_USED", "NOT USED"


class ComplianceTypes(TextChoices):
    DOCUMENTATION = "DOCUMENTATION", _("DOCUMENTATION")
    DISBURSEMENT = "DISBURSEMENT", _("DISBURSEMENT")
    REPAYMENT = "REPAYMENT", _("REPAYMENT")


class ComplianceChecksTypes(TextChoices):
    BORROWER_GUARANTOR_RELATIONSHIP = "BORROWER_GUARANTOR_RELATIONSHIP", _(
        "Borrower Guarantor Relationship Check"
    )
    GUARANTOR_BORROWER_RELATIONSHIP = "GUARANTOR_BORROWER_RELATIONSHIP", _(
        "Guarantor Borrower Relationship Check"
    )
    AGENT_AS_GUARANTOR = "AGENT_AS_GUARANTOR", _("Agent As Guarantor Check")
    AGENT_AS_BORROWER = "AGENT_AS_BORROWER", _("Agent As Borrrower Check")
    FAMILY_RELATIONSHIP = "FAMILY_RELATIONSHIP", _("Family Relationship Check")
    REPEATED_SURNAMES = "REPEATED_SURNAMES", _("Repeated Surnames Check")
    SUPERVISOR_BORROWER_RELATIONSHIP = "SUPERVISOR_BORROWER_RELATIONSHIP", _(
        "Supervisor Borrower Relationship Check"
    )
    BORROWER_GUARANTOR_IN_SAME_BRANCH = "BORROWER_GUARANTOR_IN_SAME_BRANCH", _(
        "Borrower Guarantor In The Same Branch Surname Check"
    )
    GUARANTOR_SURNAME_MATCH = "GUARANTOR_SURNAME_MATCH", _(
        "Guarantor Surname Match Check"
    )
    VERIFY_LIVELINESS = "VERIFY_LIVELINESS", _("Verify Liveliness Check")
    INDIVIDUAL_AGENT_PORTFOLIO_HEALTH = "INDIVIDUAL_AGENT_PORTFOLIO_HEALTH", _(
        "Individual Agent Portfolio Health Check"
    )
    BRANCH_AGENT_PORTFOLIO_HEALTH = "BRANCH_AGENT_PORTFOLIO_HEALTH", _(
        "Branch Agent Portfolio Health Check"
    )
    BRANCH_PORTFOLIO_OVERALL_HEALTH = "BRANCH_PORTFOLIO_OVERALL_HEALTH", _(
        "Branch Portfolio Overall Health Check"
    )
    MONITOR_REPAYMENT_PATTERNS = "MONITOR_REPAYMENT_PATTERNS", _(
        "Monitor Repayment Patterns Check"
    )
    MISSED_PARTIAL_REPAYMENTS = "MISSED_PARTIAL_REPAYMENTS", _(
        "Missed Partial Repayments Check"
    )
    AGENTS_NOT_FOLLOWING_UP_DELINQUENTS = "AGENTS_NOT_FOLLOWING_UP_DELINQUENTS", _(
        "Agents Not Following Up Delinquents Accounts Check"
    )


class ProductCondition(TextChoices):
    GOOD = "GOOD", "GOOD"
    BAD = "BAD", "BAD"
    UNKNOWN = "UNKNOWN", "UNKNOWN"


class ProductAssignmentStatus(TextChoices):
    PROCESSING = "PROCESSING", "PROCESSING"
    PENDING = "PENDING", "PENDING"
    AVAIL = "AVAILABLE", "AVAILABLE"
    UN_AVAIL = "UNAVAILABLE", "UNAVAILABLE"
    LOANED = "LOANED", "LOANED"
    ASSIGNED = "ASSIGNED", "ASSIGNED"
    FAILED = "FAILED", "FAILED"


class CRCUploadType(TextChoices):
    CREDIT = "CREDIT", "CREDIT"
    BORROWER = "BORROWER", "BORROWER"


class InsuranceProvider(TextChoices):
    LIBERTY_LIFE = "LIBERTY_LIFE", "LIBERTY LIFE"


class InsuranceRequestStatus(TextChoices):
    SUCCESS = "SUCCESS", "SUCCESS"
    PENDING = "PENDING", "PENDING"
    FAILED = "FAILED", "FAILED"
    IN_PROGRESS = "IN_PROGRESS", "IN_PROGRESS"


class DeviceType(TextChoices):
    ANDRIOD = "ANDRIOD", "ANDRIOD"
    OTHERS = "OTHERS", "OTHERS"


class RenewalPlanStatus(TextChoices):
    FAILED = "FAILED", "Failed"
    PENDING_ALLOCATION = "PENDING_ALLOCATION", "Pending Allocation"
    ALLOCATION_COMPLETED = "ALLOCATION_COMPLETED", "Allocation Completed"
    PLAN_RENEWED = "PLAN_RENEWED", "Plan Renewed"
    # READY_FOR_TRANSFER = "READY_FOR_TRANSFER", "Ready for Transfer"
    RESET_FOR_NEXT_ALLOCATION = "RESET_FOR_NEXT_ALLOCATION", "Reset for Next Allocation"


class AddressVerificationStage(TextChoices):
    ADDRESS_VERIFICATION = "ADDRESS_VERIFICATION", "ADDRESS VERIFICATION"
    ADDRESS_VERIFIED = "ADDRESS_VERIFIED", "ADDRESS VERIFIED"


class LoanCategory(TextChoices):
    GROUP = "GROUP", "GROUP"
    INDIVIDUAL = "INDIVIDUAL", "INDIVIDUAL"


class InsuranceRequestType(TextChoices):
    ACTIVATION = "ACTIVATION", "ACTIVATION"
    RENEWAL = "RENEWAL", "RENEWAL"


class RepaymentDistributionType(TextChoices):
    HEALTH_INSURANCE = "HEALTH_INSURANCE", "HEALTH_INSURANCE"
    OTHERS = "OTHERS", "OTHERS"


class CheckerStage(TextChoices):
    PAYMENT = "PAYMENT", "PAYMENT"
    ID_VERIFICATION = "ID_VERIFICATION", "ID_VERIFICATION"
    CRC_CHECK = "CRC_CHECK", "CRC_CHECK"
    IMAGE_VERIFICATION = "IMAGE_VERIFICATION", "IMAGE_VERIFICATION"
    LOAN_AMOUNT = "LOAN_AMOUNT", "LOAN_AMOUNT"


class LoanDiskMirrorRequestTypes(TextChoices):
    LOAN_ENTRY = "LOAN_ENTRY", "LOAN ENTRY"
    BORROWER_ENTRY = "BORROWER_ENTRY", "BORROWER ENTRY"
    REPAYMENT_ENTRY = "REPAYMENT_ENTRY", "REPAYMENT ENTRY"
    UPDATE_LOAN_ENTRY = "UPDATE_LOAN_ENTRY", "UPDATE LOAN ENTRY"


class LoanDiskMirrorStages(TextChoices):
    INITIATED = "INITIATED", "INITIATED"
    PENDING = "PENDING", "PENDING"
    COMPLETED = "COMPLETED", "COMPLETED"


class RequestSource(TextChoices):
    WEB = "WEB", "WEB"
    LIBERTY_LIFE = "LIBERTY_LIFE", _("LIBERTY_LIFE")


class CreditHealthStages(TextChoices):
    OTP = "OTP", _("OTP")
    CREATE_SAVER = "CREATE_SAVER", _("CREATE_SAVER")
    OTP_VERIFICATION = "OTP_VERIFICATION", _("OTP_VERIFICATION")
    SAVINGS = "SAVINGS", _("SAVINGS")
    LOANS = "LOANS", _("LOANS")


class ApprovalSource(TextChoices):
    WEB = "WEB", "WEB"
    DJANGO_ADMIN = "DJANGO_ADMIN", "DJANGO ADMIN"


class LoanRecoveryReason(TextChoices):
    Prolonged_Non_Payment = "Prolonged_Non_Payment", "Prolonged Non Payment"
    Refusal_to_Pay = "Refusal_to_Pay", "Refusal to Pay"
    Broken_Payment_Promises = "Broken_Payment_Promises", "Broken Payment Promises"
    Fraudulent_Intent = "Fraudulent_Intent", "Fraudulent Intent"
    Unreachable_Borrower = "Unreachable_Borrower", "Unreachable Borrower"
    Change_of_Contact_Information = (
        "Change_of_Contact_Information",
        "Change of Contact Information",
    )
    Ghosting_or_Disappearance = "Ghosting_or_Disappearance", "Ghosting or Disappearance"
    Bankruptcy_or_Insolvency = "Bankruptcy_or_Insolvency", "Bankruptcy or Insolvency"
    Legal_Action_Initiated = "Legal_Action_Initiated", "Legal_Action Initiated"


class SMSTypes(TextChoices):
    WEEKLY_SUMMARY = "WEEKLY_SUMMARY", _("WEEKLY_SUMMARY")
    PAST_MATURITY = "PAST_MATURITY", _("PAST_MATURITY")
    LOAN_STATEMENT = "LOAN_STATEMENT", _("LOAN_STATEMENT")
    GENERAL = "GENERAL", _("GENERAL")
    DAILY_REPAYMENT = "DAILY_REPAYMENT", _("DAILY_REPAYMENT")
    PRE_TOPUP = "PRE_TOPUP", _("PRE_TOPUP")
    TOPUP = "TOPUP", _("TOPUP")


class UserTypes(TextChoices):
    ADMIN = "ADMIN", _("ADMIN")
    BORROWER = "BORROWER", _("BORROWER")
    STAFF = "STAFF", _("STAFF")
    AGENT = "AGENT", _("AGENT")


class EligibilitySource(TextChoices):
    LIBERTYPAY_TXN_RECORD = "LIBERTYPAY_TXN_RECORD", _("LIBERTYPAY_TXN_RECORD")
    BANK_STATEMENT = "BANK_STATEMENT", _("BANK_STATEMENT")


class MerchantEligibilityApplicationStages(TextChoices):
    OPEN_FOR_PROCESSING = "OPEN_FOR_PROCESSING", _("OPEN FOR PROCESSING")
    AWAITING_PROCESSING_FEE = "AWAITING_PROCESSING_FEE", _("AWAITING PROCESSING FEE")
    CREDIT_WORTHINESS_CHECK = "CREDIT_WORTHINESS_CHECK", _("CREDIT WORTHINESS CHECK")
    LOAN_AMOUNT_ENTRY = "LOAN_AMOUNT_ENTRY", _("LOAN AMOUNT ENTRY")
    ACCOUNT_NUMBER_ENTRY = "ACCOUNT_NUMBER_ENTRY", _("ACCOUNT NUMBER ENTRY")
    DISBURSEMENT = "DISBURSEMENT", _("DISBURSEMENT")
    PROCESSING_COMPLETE = "PROCESSING_COMPLETE", _("PROCESSING COMPLETE")


class TemplateType(TextChoices):
    PAST_MATURITY = "PAST_MATURITY", "Past Maturity"
    DUE_REMINDER = "DUE_REMINDER", "Due Reminder"
    REPAYMENT_CONFIRMATION = "REPAYMENT_CONFIRMATION", "Repayment Confirmation"
    CUSTOM = "CUSTOM", "Custom"
    PRE_TOPUP = "PRE_TOPUP", "Pre-Topup"
    TOPUP = "TOPUP", "Topup"


class MessageTone(IntegerChoices):
    FRIENDLY = 1, "Friendly"  # Light, conversational
    WARM = 2, "Warm"  # Slightly more intentional
    CAUTIONARY = 3, "Cautionary"  # Starts to alert the user
    NEUTRAL = 4, "Neutral"  # Balanced tone
    CONCERNED = 5, "Concerned"  # Shows worry or importance
    FIRM = 6, "Firm"  # Clearly requesting action
    URGENT = 7, "Urgent"  # Requires immediate attention
    STRICT = 8, "Strict"  # Final tone, potential consequences
