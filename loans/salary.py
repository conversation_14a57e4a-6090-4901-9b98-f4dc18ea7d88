import json
import os
from dataclasses import dataclass


@dataclass
class Performance:
    """
        Class to handle loan performance data and provide salary summaries.
   """

    json_dir = os.path.dirname(os.path.abspath(__file__))

    # Relative path to performance.json
    relative_path = "performance.json"

    # Construct the absolute path
    absolute_path = os.path.join(json_dir, relative_path)

    # Open the file
    with open(absolute_path) as performance_json:
        contents = performance_json.read()
    loan_performance_data = json.loads(contents)

    def get_salary_summary(self, amount_disbursed):
        salary_data = None
        for count, performance_object in enumerate(self.loan_performance_data):

            loan_disbursement_amount = performance_object["Loan Disbursements"]

            if amount_disbursed < loan_disbursement_amount:
                salary_data = {'Loan Disbursements': loan_disbursement_amount,
                               'Salary': 0, 'Bonus': 0,
                               'Cumulative Take Home': 0}
                break

            try:
                immediate_next = self.loan_performance_data[count + 1]
                immediate_loan_disbursement_amount = immediate_next["Loan Disbursements"]

                if amount_disbursed > immediate_loan_disbursement_amount:
                    continue

                # If the target amount matches the immediate next
                # loan disbursement amount, set salary_data and break
                if amount_disbursed == immediate_loan_disbursement_amount:
                    salary_data = immediate_next
                    break

                # If the target amount is less than the immediate next
                # loan disbursement amount, set salary_data and break
                if amount_disbursed < immediate_loan_disbursement_amount:
                    salary_data = performance_object
                    break

            except IndexError:
                # If IndexError occurs, set salary_data to the current performance_object and break
                salary_data = performance_object
                break

        return salary_data


if __name__ == "__main__":
    loan_summary = Performance().get_salary_summary(amount_disbursed=6100000)
    print(loan_summary)
