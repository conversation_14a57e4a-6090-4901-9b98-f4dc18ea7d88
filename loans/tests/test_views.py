from pprint import pprint
from django.test import TestCase
from django.urls import reverse, resolve
from accounts.models import BlackListed
from loans.views import *
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model


class ViewsTestCase(TestCase):
    def setUp(self):

        self.user = get_user_model().objects.create(email="admin@mail", password="pasword")
        self.client = APIClient()
        self.kyc_doc = reverse("get_documentations")

        self.ajo_user = AjoUser.objects.create(
            user=self.user, phone_number="*********", first_name="<PERSON>", last_name="cole"
        )

        ConstantTable.objects.create(
            target_based_plans={"BUD": {"lower_target": 60000, "upper_target": 119999}},
            other_loans_config={
                "repayment_threshold_percentage": 90,
            },
            staff_loans_config={
                "days_until_end": 7,
            },
            branch_location_codes={"HQ": "56922"},
        )
        BlackListed.objects.create(
            loan_disbursement={
                "emails": ["<EMAIL>", "<EMAIL>"],
                "phones": ["************", "************"],
            }
        )
        self.client.force_authenticate(user=self.user)

    def test_get_eligible_savers(self):
        url = reverse("eligible_savers")
        savings = AjoSaving.objects.create(
            user=self.user,
            ajo_user=self.ajo_user,
            frequency=SavingsFrequency.DAILY,
            duration=30,
            periodic_amount=1000,
            expected_amount=500000,
        )
        LoanEligibility.objects.create(
            agent=self.user,
            ajo_user=savings.ajo_user,
            saving=savings,
            amount=500000,
            amount_saved=50000,
            multiplier=5,
            admin_cap_amount=1000000,
            percentage_saved=70,
            savings_expected_amount=500000,
        )
        resolved_view = resolve(url).func.__class__

        expected_view = GetEligibleSaver.as_view()
        request_eligibility = self.client.get(url)
        # request eligible savers
        response_data = request_eligibility.json()
        # pprint(response_data)
        expected_data = {
            "count": 1,
            "loan_metrics": {
                "due_collection": 0,
                "due_collection_this_month": 0,
                "due_collection_today": 0,
                "loan_disbursed": 0,
                "loan_disbursed_this_month": 0,
                "loan_disbursed_today": 0,
                "loans_paid": 0,
                "loans_paid_this_month": 0,
                "loans_paid_today": 0,
                "missed_payment": 0,
                "performance": 0,
                "salary_earned": 0,
                "total_savings": 0,
                "total_savings_this_month": 0,
                "total_savings_today": 0,
            },
            "next": None,
            "previous": None,
            "results": [
                {
                    "active_savings": 0.0,
                    "full_name": "Joe cole",
                    "phone_number": "*********",
                    "savings_eligibility": [
                        {
                            "amount_saved": 0.0,
                            "days_saved": 0,
                            "eligible_amount": 500000.0,
                            "id": 1,
                            "savings_name": None,
                        }
                    ],
                    "total_amount": 500000.0,
                }
            ],
            "status": True,
        }

        self.assertEquals(resolved_view, expected_view.__class__)
        self.assertEqual(response_data, expected_data)

    def test_get_documentation_endpoint(self):
        kyc_documnetation_data = {
            "borrower_phone_number": "************",
            "borrower_annual_shop_rent": "10000",
            "borrower_email": "<EMAIL>",
            "borrower_daily_income": "5000.00",
            "borrower_weekly_income": "1500.00",
            "borrower_verification_number": "ABC123",
            "borrower_verification_type": "ID",
            "borrower_base_64_capture": "base64encodedstring",
            "guarantor_first_name": "Smith",
            "guarantor_last_name": "Johnson",
            "guarantor_email": "<EMAIL>",
            "guarantor_phone_number": "************",
            "guarantor_verification_number": "XYZ789",
            "guarantor_verification_type": "Passport",
            "guarantor_relationship_to_borrower": "Friend",
            "guarantor_base_64_capture": "base64encodedstring",
        }
        LoanKYCDocumentation.objects.create(borrower=self.ajo_user, **kyc_documnetation_data)

        request_kyc_docs = self.client.get(self.kyc_doc)
        # request eligible savers
        response_data = request_kyc_docs.json()
        expected_response = {
            "count": 1,
            "next": None,
            "previous": None,
            "results": [
                {
                    "borrower_full_name": "Joe cole",
                    "borrower_information": [
                        {"status": True, "title": "Daily, Weekly income and " "annual shop rent"},
                        {"status": False, "title": "Address Verification"},
                        {"status": False, "title": "Captured live image"},
                        {"status": False, "title": "Borrower face match"},
                        {"status": False, "title": "Borrower is above min age " "23"},
                        {"status": False, "title": "Borrower is below max age " "55"},
                    ],
                    "borrower_phone_number": "*********",
                    "documentation_status": None,
                    "guarantor_details": [
                        {"status": False, "title": "Captured live image"},
                        {"status": False, "title": "Gurantor face match"},
                        {"status": False, "title": "Guarantor is above min age 24"},
                        {"status": False, "title": "Guarantor is below max age 60"},
                    ],
                    "id": 1,
                    "verify_phone_number": False,
                }
            ],
            "status": True,
        }
        self.assertEqual(response_data, expected_response)
