from django.db import models
from django.db.models import (
    Avg,
    <PERSON>,
    Count,
    DurationField,
    ExpressionWrapper,
    F,
    Float<PERSON>ield,
    Max,
    Min,
    Q,
    Sum,
    Value,
    When,
)
from django.db.models.functions import Coalesce, ExtractDay
from django.utils import timezone

from loans.giniengine.api import GiniMachineRequest


class GiniMachineLog(models.Model):
    ajo_user = models.ForeignKey(to="ajo.AjoUser", on_delete=models.SET_NULL, null=True, blank=True)
    score = models.FloatField(default=0.0, null=True, blank=True, help_text="Gini Score")
    resolution = models.CharField(max_length=100, null=True, blank=True, help_text="Resolution")
    AvgLoan = models.FloatField(default=0.0, null=True, blank=True, help_text="Average loan amount")
    AvgLoanDuration = models.FloatField(default=0.0, null=True, blank=True, help_text="Average loan duration in days")
    AvgTimeToPayOffLoan = models.FloatField(
        default=0.0, null=True, blank=True, help_text="Average time to pay off loan in days"
    )
    LargestLoan = models.FloatField(default=0.0, null=True, blank=True, help_text="Largest loan amount")
    LoanRenewalRate = models.CharField(max_length=100, null=True, blank=True, help_text="Loan renewal rate")
    LongestLoanDuration = models.FloatField(
        default=0.0, null=True, blank=True, help_text="Longest loan duration in days"
    )
    ShortestLoanDuration = models.FloatField(
        default=0.0, null=True, blank=True, help_text="Shortest loan duration in days"
    )
    TimeSinceFirstLoan = models.FloatField(
        default=0.0, null=True, blank=True, help_text="Time since first loan in days"
    )
    TimeSinceLastLoan = models.FloatField(default=0.0, null=True, blank=True, help_text="Time since last loan in days")
    TotaloutstandingDebt = models.FloatField(default=0.0, null=True, blank=True, help_text="Total outstanding debt")
    annual_shop_rent = models.FloatField(default=0.0, null=True, blank=True, help_text="Annual shop rent")
    avgDurationOfOpenLoans = models.FloatField(
        default=0.0, null=True, blank=True, help_text="Average duration of open loans"
    )
    avg_monthlysavings = models.FloatField(default=0.0, null=True, blank=True, help_text="Average monthly savings")
    avgsavingsduration = models.FloatField(default=0.0, null=True, blank=True, help_text="Average savings duration")
    avgtimeTakenToPayOff = models.FloatField(
        default=0.0, null=True, blank=True, help_text="Average time taken to pay off"
    )
    avgwithrawalamount = models.FloatField(default=0.0, null=True, blank=True, help_text="Average withdrawal amount")
    bad_loans = models.FloatField(default=0.0, null=True, blank=True, help_text="Number of bad loans")
    borrower_age = models.CharField(max_length=300, null=True, blank=True, help_text="Borrower age category")
    borrower_id = models.CharField(max_length=300, null=True, blank=True, help_text="Borrower ID")
    completionrate = models.FloatField(default=0.0, null=True, blank=True, help_text="Completion rate")
    daily_income = models.FloatField(default=0.0, null=True, blank=True, help_text="Daily income")
    firstTimeLenderStatus = models.CharField(
        max_length=300, null=True, blank=True, help_text="First time lender status"
    )
    fully_paid = models.FloatField(default=0.0, null=True, blank=True, help_text="Number of fully paid loans")
    gender = models.CharField(max_length=10, null=True, blank=True, help_text="Gender")
    guarantor_age = models.CharField(max_length=100, null=True, blank=True, help_text="Guarantor age category")
    highest_savings_amount = models.FloatField(default=0.0, null=True, blank=True, help_text="Highest savings amount")
    libertyloanstaken = models.FloatField(
        default=0.0, null=True, blank=True, help_text="Number of loans taken from Liberty"
    )
    loansavingsamt = models.FloatField(default=0.0, null=True, blank=True, help_text="Loan savings amount")
    loansavingscount = models.FloatField(default=0.0, null=True, blank=True, help_text="Loan savings count")
    lowest_savings_amount = models.FloatField(default=0.0, null=True, blank=True, help_text="Lowest savings amount")
    marital_status = models.CharField(max_length=100, null=True, blank=True, help_text="Marital status")
    numofrepeatloanamt = models.FloatField(
        default=0.0, null=True, blank=True, help_text="Number of repeat loan amounts"
    )
    ok = models.CharField(max_length=300, null=True, blank=True, help_text="OK status")
    open_loans = models.FloatField(default=0.0, null=True, blank=True, help_text="Number of open loans")
    performance_status = models.CharField(max_length=300, null=True, blank=True, help_text="Performance status")
    repaymentstoliberty = models.FloatField(default=0.0, null=True, blank=True, help_text="Repayments to Liberty")
    repeatLenderStatus = models.CharField(max_length=300, null=True, blank=True, help_text="Repeat lender status")
    savingswithdrawalcount = models.FloatField(
        default=0.0, null=True, blank=True, help_text="Savings withdrawal count"
    )
    smallestLoan = models.FloatField(default=0.0, null=True, blank=True, help_text="Smallest loan amount")
    timeSinceCategorizedAsLost = models.FloatField(
        default=0.0, null=True, blank=True, help_text="Time since categorized as lost"
    )
    totalLoans = models.FloatField(default=0.0, null=True, blank=True, help_text="Total number of loans")
    total_savings = models.FloatField(default=0.0, null=True, blank=True, help_text="Total savings")
    trade = models.CharField(max_length=100, null=True, blank=True, help_text="Trade or occupation")
    weekly_income = models.FloatField(default=0.0, null=True, blank=True, help_text="Weekly income")
    data = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now, help_text="Record creation date and time")
    updated_at = models.DateTimeField(auto_now=True, help_text="Record update date and time")

    class Meta:
        verbose_name = "Gini Machine Log"
        verbose_name_plural = "Gini Machine Logs"
        ordering = ["-created_at"]

    def __str__(self):
        return f"Gini Machine Log for Borrower ID {self.borrower_id}"

    def get_credit_bureau_loan_report(self, ajouser, liberty_loan_count) -> dict:
        from loans.models import CreditBureauMetaData

        # get bureau loan report
        crc_loan_report = CreditBureauMetaData.get_bureau_loan_summary(
            liberty_loan_count=liberty_loan_count, ajouser=ajouser
        )
        return crc_loan_report

    def get_user_info(self, ajouser):
        data = {"trade": ajouser.trade, "gender": ajouser.gender, "marital_status": ajouser.marital_status}
        return data

    def borrower_loan_summary(self, ajouser) -> dict:
        from loans.enums import LoanStatus
        from loans.models import AjoLoan

        # Filter loans by borrower and status
        ajo_user_loanqs = AjoLoan.objects.filter(borrower=ajouser, status__in=["OPEN", "COMPLETED"])

        # Get renewal rate
        renewal_rate = AjoLoan.get_loan_renewal_rating(borrower=ajouser)

        # Annotate loans with the count of open and completed loans
        ajo_user_loanqs = ajo_user_loanqs.annotate(
            open_loan_count=Count("id", filter=Q(status=LoanStatus.OPEN)),
            completed_loan_count=Count("id", filter=Q(status=LoanStatus.COMPLETED)),
        )

        # Aggregate total loans, and counts of open and completed loans
        loan_aggregated_data = ajo_user_loanqs.aggregate(
            libertyloanstaken=Count("id"),
            count_of_open_loan=Sum("open_loan_count"),
            numofrepeatloanamt=Count("amount"),
            count_of_completed_loan=Sum("completed_loan_count"),
        )

        # Prefetch related objects to avoid N+1 queries and annotate required fields
        completed_loanqs = (
            ajo_user_loanqs.filter(status=LoanStatus.COMPLETED)
            .select_related("guarantor", "guarantor__borrower_info")
            .annotate(
                guarantor_age=Case(
                    When(guarantor__age__exact="", then=Value("Invalid Age")),
                    When(guarantor__age__lte=35, then=Value("Young Adult")),
                    When(guarantor__age__lte=65, then=Value("Middle-aged")),
                    default=Value("Senior"),
                    output_field=models.CharField(),
                ),
                borrower_age=Case(
                    When(guarantor__borrower_info__age__exact="", then=Value("Invalid Age")),
                    When(guarantor__borrower_info__age__lte=35, then=Value("Young Adult")),
                    When(guarantor__borrower_info__age__lte=65, then=Value("Middle-aged")),
                    default=Value("Senior"),
                    output_field=models.CharField(),
                ),
                loan_id=F("id"),
                borrower_info_id=F("guarantor__borrower_info_id"),
                daily_income=F("guarantor__borrower_info__daily_income"),
                annual_shop_rent=F("guarantor__borrower_info__annual_shop_rent"),
                weekly_income=F("guarantor__borrower_info__weekly_income"),
            )
        )

        # Extract the completed loans with the necessary fields
        loan_summary = (
            completed_loanqs.order_by("-id")
            .values(
                "borrower_id",
                "performance_status",
                "guarantor_age",
                "daily_income",
                "annual_shop_rent",
                "borrower_age",
                "weekly_income",
            )
            .first()
        )
        # print(loan_summary, "\n\n")
        # Handle case when no completed loans are found
        if not loan_summary:
            loan_summary = {
                "borrower_id": ajouser.id,
                "performance_status": None,
                "guarantor_age": None,
                "daily_income": None,
                "annual_shop_rent": None,
                "borrower_age": None,
                "weekly_income": None,
            }

        # Update loan_summary with aggregated data
        liberty_loan_count = loan_aggregated_data.get("libertyloanstaken", 0)
        # merge bureau report
        bureau_report = self.get_credit_bureau_loan_report(ajouser=ajouser, liberty_loan_count=liberty_loan_count)
        loan_summary.update(
            {
                "ok": None,
                "libertyloanstaken": liberty_loan_count,
                "numofrepeatloanamt": loan_aggregated_data.get("numofrepeatloanamt", 0),
                "LoanRenewalRate": renewal_rate,
                **bureau_report,  # Merging bureau_report
                **self.get_user_info(ajouser=ajouser),
            }
        )

        return loan_summary

    def load_successful_transactions(self, ajouser):
        from payment.model_choices import Status
        from payment.models import Transaction

        # query saver's transaction
        ajouser_txnqs = Transaction.objects.filter(
            onboarded_user=ajouser,
            status=Status.SUCCESS,
            amount__gt=0,
        )
        return ajouser_txnqs

    def extract_wallet_metrics(self, ajouser):

        from payment.model_choices import PlanType, TransactionFormType

        wallet_types = ["AJO_USER", "AJO_AGENT", "AJO_PERSONAL", "ROSCA_PERSONAL"]

        transaction_qs = self.load_successful_transactions(ajouser=ajouser)

        repayment_to_liberty = (
            transaction_qs.filter(transaction_form_type="LOAN_REPAYMENT").aggregate(repaymentstoliberty=Sum("amount"))[
                "repaymentstoliberty"
            ]
            or 0
        )

        withdrawal_savings = transaction_qs.filter(
            transaction_form_type=TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT,
            plan_type=PlanType.AJO,
            wallet_type__in=wallet_types,
        )
        savings_withdrawal_metrics = withdrawal_savings.aggregate(
            avgwithrawalamount=Coalesce(Avg("amount"), Value(0.0)),
            savingswithdrawalcount=Coalesce(Count("id"), 0),
        )
        savings_withdrawal_metrics["repaymentstoliberty"] = repayment_to_liberty

        return savings_withdrawal_metrics

    def savings_insights(self, ajouser):
        from ajo.models import AjoSaving
        from payment.model_choices import WalletTypes

        transaction_qs = self.load_successful_transactions(ajouser=ajouser)

        savingqs = AjoSaving.objects.filter(ajo_user=ajouser, maturity_date__isnull=False, is_activated=True)

        # get loan repayment ids
        loan_repayment_ids = savingqs.filter(is_loan_repayment=True).values_list("quotation_id", flat=True)

        credit_savingstxn_qs = transaction_qs.filter(
            transaction_type="CREDIT",
            wallet_type=WalletTypes.AJO_USER,
            transaction_form_type__in=["WALLET_DEPOSIT", "WALLET_AUTO_CHARGE_DEPOSIT", "BANK_DEPOSIT"],
        ).exclude(quotation_id__in=loan_repayment_ids)

        not_repayment_savings = savingqs.filter(is_loan_repayment=False)
        loan_savingsqs = not_repayment_savings.filter(loan=True)
        loan_savings_quoId = loan_savingsqs.values_list("quotation_id", flat=True)

        # Aggregate to get avgsavingsduration
        savings_dt_summary = not_repayment_savings.aggregate(
            avgsavingsduration=Coalesce(
                Avg(
                    ExpressionWrapper(
                        ExtractDay(
                            ExpressionWrapper(F("maturity_date") - F("created_at"), output_field=DurationField())
                        ),
                        output_field=FloatField(),
                    )
                ),
                Value(0.0),
            ),
        )

        loan_savingstxn = credit_savingstxn_qs.filter(quotation_id__in=loan_savings_quoId)
        # sum of expected savings amount
        total_expected_savings_amt = (
            not_repayment_savings.aggregate(sum_expected_amt=Sum("expected_amount"))["sum_expected_amt"] or 0
        )
        # over all savings result
        savings_aggregation = credit_savingstxn_qs.aggregate(
            total_savings=Coalesce(Sum("amount"), Value(0.0)),
            lowest_savings_amount=Coalesce(Min("amount"), Value(0.0)),
            highest_savings_amount=Coalesce(Max("amount"), Value(0.0)),
            avg_monthlysavings=Coalesce(Avg("amount"), Value(0.0)),
        )

        try:
            rate = savings_aggregation["total_savings"] / total_expected_savings_amt
            completionrate = round(rate, 2)
        except ZeroDivisionError:
            completionrate = 0

        loan_savings_agg = loan_savingstxn.aggregate(
            loansavingsamt=Coalesce(Sum("amount"), Value(0.0)),
            loansavingscount=Coalesce(Count("id"), 0),
        )
        savings_aggregation.update(
            {
                "completionrate": completionrate,
                "avgsavingsduration": savings_dt_summary["avgsavingsduration"],
                **loan_savings_agg,
            }
        )
        return savings_aggregation

    def compile_metrics(self, ajouser):
        loan_summary = self.borrower_loan_summary(ajouser=ajouser)
        wallet_metrics = self.extract_wallet_metrics(ajouser=ajouser)
        savings_insight = self.savings_insights(ajouser=ajouser)
        compiled_data = {
            **loan_summary,
            **wallet_metrics,
            **savings_insight,
        }
        return compiled_data

    @classmethod
    def get_loan_and_savings_metrics(cls, ajouser):
        return cls().compile_metrics(ajouser=ajouser)

    @classmethod
    def get_score(cls, ajouser):
        request_data = cls().compile_metrics(ajouser=ajouser)
        giniobj = GiniMachineRequest()
        prediction_result = giniobj.get_prediction_result(data=request_data)
        # score
        # resolution
        predictions = prediction_result.get("predictions", [])
        if predictions:
            data = predictions[0]
            score = data.get("score")
            resolution = data.get("resolution")

        # create objects
        result = {"payload": request_data, "response": prediction_result}
        cls.objects.create(ajo_user=ajouser, score=score, resolution=resolution, data=result, **request_data)

        return result

    @classmethod
    def score_all_loan_users(cls):
        pass
