from rest_framework import permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView

from accounts.permissions import IsBlackListed
from ajo.models import AjoUser
from loans.giniengine.models import GiniMachineLog


class GetGiniRequestData(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed)

    def get(self, request):
        agent_email = request.GET.get("agent_email")
        saver_phone_no = request.GET.get("saver_phone_no")
        saver_id = request.GET.get("saver_id")
        # print(agent_email, saver_phone_no, type(saver_phone_no), "\n")
        ajouser = AjoUser.objects.filter(user__email=agent_email, phone_number__startswith=f"{saver_phone_no}").last()
        if not ajouser:
            response_body = {
                "status": False,
                "message": "Saver does not exist",
                "data": None,
            }

            return Response(data=response_body, status=status.HTTP_400_BAD_REQUEST)
        else:
            data = GiniMachineLog.get_loan_and_savings_metrics(ajouser=ajouser)

            res = {
                "status": True,
                "message": "success",
                "data": data,
            }

            return Response(data=res, status=status.HTTP_200_OK)


class GetGiniPrediction(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed)

    def get(self, request):
        agent_email = request.GET.get("agent_email")
        saver_phone_no = request.GET.get("saver_phone_no")
        saver_id = request.GET.get("saver_id")
        ajouser = AjoUser.objects.filter(user__email=agent_email, phone_number__startswith=f"{saver_phone_no}").last()
        if not ajouser:
            response_body = {
                "status": False,
                "message": "Saver does not exist",
                "data": None,
            }

            return Response(data=response_body, status=status.HTTP_400_BAD_REQUEST)
        else:
            data = GiniMachineLog.get_score(ajouser=ajouser)

            res = {
                "status": True,
                "message": "success",
                "data": data,
            }

            return Response(data=res, status=status.HTTP_200_OK)
