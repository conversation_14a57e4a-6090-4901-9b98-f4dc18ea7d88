import json

import requests
from django.conf import settings


class GiniMachineRequest:

    url = "https://gcredit.ginimachine.com/api/v1/scoring/predict.json?modelId=1917"
    api_key = settings.GINI_MACHINE_APIKEY
    environ = settings.ENVIRONMENT

    def get_prediction_result(self, data):
        payload = json.dumps([data])
        headers = {
            "Authorization": f"X-API-KEY {self.api_key}",
            "Content-Type": "application/json",
        }
        if self.environ != "development":
            response = requests.request("POST", self.url, headers=headers, data=payload)
            res = response.json()
        else:
            res = {
                "operationId": 3467,
                "predictions": [{"id": "2", "score": "0.25640", "resolution": "Decline"}],
            }
        return res

    def get_list_prediction_result(self, data: list):
        payload = json.dumps(data)
        headers = {
            "Authorization": f"X-API-KEY {self.api_key}",
            "Content-Type": "application/json",
        }
        response = requests.request("POST", self.url, headers=headers, data=payload)
        # print(response.text)
        return response.json()
