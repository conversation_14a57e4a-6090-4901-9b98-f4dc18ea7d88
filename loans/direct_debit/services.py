import json
import requests

from django.conf import settings


# class MonoAPI:
#     MONO_API_BASE_URL = "https://api.withmono.com"
#     headers = {
#         "mono-sec-key": settings.MONO_SECRET_KEY
#     }

#     @staticmethod
#     def _handle_response(response):
#         """
#         Helper method to handle API responses
#         """
#         if response.status_code == 200:
#             return response.json()
#         else:
#             return response
#             # IMPROVE THIS..........

#     @staticmethod
#     def get_account_statement(account_id):
#         """
#         Calls the Mono API, requesting for account statement with ID
#         """
#         url = f"{MonoAPI.MONO_API_BASE_URL}/v2/accounts/{account_id}/statement?period=last6months"
       
#         response = requests.get(url, headers=MonoAPI.headers)
#         return MonoAPI._handle_response(response)
    
#     @staticmethod
#     def request_credit_worthiness(account_id, payload):
#         """
#         Calls Mono API to request for the credit worthiness for the customer account linked.
#         """
#         payload = json.dumps(payload)
#         url = f"{MonoAPI.MONO_API_BASE_URL}/v2/accounts/{account_id}/creditworthiness"

#         response = requests.post(url, data=payload, headers=MonoAPI.headers)
#         return MonoAPI._handle_response(response)



class MonoAPI:
    MONO_API_BASE_URL = "https://api.withmono.com"
    headers = {
        "mono-sec-key": settings.MONO_SECRET_KEY,
        'Content-Type': 'application/json'
    }

    @staticmethod
    def _handle_request_response(
        method: str,
        url: str,
        payload: dict = {}
    ):
        """
        Helper method to handle API requests and responses
        """
        try:
            payload = json.dumps(payload)
            response = requests.request(method=method, url=url, data=payload, headers=MonoAPI.headers)

            data = {
                "response_body": response.json(),
                "status_code": response.status_code,
                "status": "success"
            }
            return data

        except (
            requests.exceptions.RequestException,
            Exception,
            json.decoder.JSONDecodeError,
        ) as err:
            data = {
                "response_body": response.text,
                "status_code": response.status_code,
                "status": "failed",
                "error": str(err)
            }
            return data
     
    @staticmethod
    def get_account_statement(account_id):
        """
        Calls the Mono API, requesting for account statement with ID
        """
        url = f"{MonoAPI.MONO_API_BASE_URL}/v2/accounts/{account_id}/statement?period=last6months"
        response = MonoAPI._handle_request_response("GET", url)
        status_code = response.get("status_code")
        status = response.get("status")

        if status_code == 200 and status == "success":
            data = response.get("response_body", {}).get("data", {})
            return {
                "status": True,
                "message": "success",
                "data": data
            }
        elif status == "success":
            message = response.get("response_body", {}).get("message", {})
            return {
                "status": False,
                "message": message,
            }
        else:
            error = response.get("error", {})
            message = response.get("response_body", {})
            return {
                "status": False,
                "message": message,
                "error": error
            }

    @staticmethod
    def request_credit_worthiness(account_id, payload):
        """
        Calls Mono API to request for the credit worthiness for the customer account linked.
        """
        url = f"{MonoAPI.MONO_API_BASE_URL}/v2/accounts/{account_id}/creditworthiness"
        response = MonoAPI._handle_request_response("POST", url, payload)
        status_code = response.get("status_code")
        status = response.get("status")

        if status_code == 200 and status == "success":
            return {
                "status": True,
                "message": "success",
            }
        elif status == "success":
            message = response.get("response_body", {}).get("message", {})
            return {
                "status": False,
                "message": message,
            }
        else:
            error = response.get("error", {})
            message = response.get("response_body", {})
            return {
                "status": False,
                "message": message,
                "error": error
            }

    
    
    

