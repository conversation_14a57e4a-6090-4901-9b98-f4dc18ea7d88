import json
import calendar
from datetime import datetime, timedelta
from collections import defaultdict


class CreditWorthinessCalculator:
    def __init__(self, statement, months: int = None):
        self.statement = statement
        self.months = months

    @staticmethod
    def _reconstruct_amount(amount):
        actual_amount = amount / 100
        return actual_amount
        

    def calculate_total_inflow(self, statement):
        """
        Calculates the total inflow (credit transactions) from the statement.

        Returns:
            float: Total inflow amount.
        """
        total_inflow = sum(
            transaction["amount"] for transaction in statement if transaction["type"] == "credit"
        )
        return CreditWorthinessCalculator._reconstruct_amount(total_inflow)

    def calculate_total_outflow(self, statement):
        """
        Calculates the total outflow (debit transactions) from the statement.

        Returns:
            float: Total outflow amount.
        """
        total_outlow = sum(
            transaction["amount"] for transaction in statement if transaction["type"] == "debit"
        )
        return CreditWorthinessCalculator._reconstruct_amount(total_outlow)

    def get_last_n_months_data(self):

        """
        Filters transactions from the last 'n' months from a given statement.

        Returns:
            list: A sorted list of transactions from the last 'n' months.
        """
        sorted_statement = sorted(
            self.statement, 
            key=lambda transaction: datetime.strptime(transaction["date"], "%Y-%m-%dT%H:%M:%S.%fZ")
        )

        if self.months is None:
            return sorted_statement

        most_recent_date = max(
            datetime.strptime(transaction["date"], "%Y-%m-%dT%H:%M:%S.%fZ")
            for transaction in sorted_statement
        )

        cutoff_date = most_recent_date - timedelta(days=self.months * 30)
        last_n_months_data = [
            transaction for transaction in sorted_statement
            if datetime.strptime(transaction["date"], "%Y-%m-%dT%H:%M:%S.%fZ") >= cutoff_date
        ]

        return last_n_months_data
    

    def get_total_monthly_inflow(self, statement):
        """
        Calculates the total inflow (credit) for each month over the specified number of months.

        Args:
            months (int): Number of months to calculate inflow for, starting from the most recent transaction.

        Returns:
            list: A list of dictionaries containing month, year, and total inflow for each month.
        """
      
        monthly_inflow = defaultdict(float)
        for transaction in statement:
            if transaction["type"] == "credit":
                date_obj = datetime.fromisoformat(transaction["date"].replace("Z", ""))
                month_year_key = (date_obj.year, date_obj.month)
                monthly_inflow[month_year_key] += transaction["amount"]
            
        result = [
            {
                "month": calendar.month_name[month],
                "year": year,
                "total_inflow": CreditWorthinessCalculator._reconstruct_amount(total)
            }
            for (year, month), total in sorted(monthly_inflow.items())
        ]
        return result
    
    
    def check_dormancy(self, statement):
        most_recent_date = max(datetime.fromisoformat(tx["date"].replace("Z", "")) for tx in statement)
        
        def len_of_months():
            earliest_date = min(datetime.fromisoformat(tx["date"].replace("Z", "")) for tx in statement)
            total_months = (most_recent_date.year - earliest_date.year) * 12 + most_recent_date.month - earliest_date.month + 1
            return total_months
        
        if self.months is None:
            total_months = len_of_months()
        else:
            total_months = self.months
        
        dormant_months = defaultdict(str)
        for i in range(total_months):
            check_month = most_recent_date.replace(day=1) - timedelta(days=30 * i)
            start_of_month = check_month.replace(day=1)
            end_of_month = (start_of_month.replace(day=28) + timedelta(days=4)).replace(day=1) - timedelta(days=1)
            
            has_transactions = any(
                start_of_month <= datetime.fromisoformat(tx["date"].replace("Z", "")) <= end_of_month
                for tx in statement
            )
            
            if has_transactions:
                dormant_months[start_of_month.strftime("%Y-%m")] = "active"
            else:
                dormant_months[start_of_month.strftime("%Y-%m")] = "dormant"
        
        # Calculate dormancy percentage
        total_dormant_months = sum(1 for status in dormant_months.values() if status == "dormant")
        dormancy_percentage = (total_dormant_months / total_months) * 100
        account_status = "dormant" if dormancy_percentage >= 50 else "active"
        
        return dormant_months, account_status


    def is_not_negative_balances(self, statement, initial_balance=0):
        """
        Checks if the account balance has ever gone negative by dynamically calculating the balance.

        Args:
            statement (list): List of transactions with keys 'date', 'amount', and 'type'.
            initial_balance (float): Starting balance for calculations.

        Returns:
            tuple: (bool, list). 
                - bool: True if no negative balance exists at any point; False otherwise.
                - list: Transactions that caused a negative balance.
        """
        sorted_statement = sorted(
            statement, key=lambda tx: datetime.fromisoformat(tx["date"].replace("Z", ""))
        )

        running_balance = initial_balance
        negative_balance_transactions = []

        for tx in sorted_statement:
            # Process transactions
            if tx["type"] == "credit":
                running_balance += tx["amount"]
            elif tx["type"] == "debit":
                running_balance -= tx["amount"]

            # Check if the balance goes negative
            if running_balance < 0:
                negative_balance_transactions.append({
                    "date": tx["date"],
                    "balance": running_balance,
                    "amount": tx["amount"],
                    "type": tx["type"],
                    "narration": tx.get("narration", "No narration")
                })

        return len(negative_balance_transactions) == 0, negative_balance_transactions


    def is_sweeper_account(self, statement, low_balance_threshold=2000, low_balance_percentage=50, initial_balance=0):
        """
        Determine if an account is a sweeper account by checking if the dynamically calculated balance
        frequently falls below a threshold after debits.

        Args:
            statement (list): List of transactions with 'amount', 'type', and 'date'.
            low_balance_threshold (float): Threshold for low balances.
            low_balance_percentage (float): Percentage to classify as a sweeper account.
            initial_balance (float): Starting balance for calculations.

        Returns:
            dict: Contains the result and breakdown of statistics.
        """
        sorted_statement = sorted(
            statement, key=lambda tx: datetime.fromisoformat(tx["date"].replace("Z", ""))
        )
        
        running_balance = initial_balance
        low_balance_count = 0
        total_balance_checks = 0

        for tx in sorted_statement:
            # Process transactions
            if tx["type"] == "credit":
                running_balance += tx["amount"]
            elif tx["type"] == "debit":
                running_balance -= tx["amount"]

            total_balance_checks += 1
            
            if running_balance < low_balance_threshold:
                low_balance_count += 1

        if total_balance_checks == 0:
            return {
                "is_sweeper": False,
                "low_balance_count": 0,
                "total_balance_checks": 0,
                "low_balance_percentage": None,
            }

        # Calculate the percentage of low balances
        low_balance_percentage_calculated = (low_balance_count / total_balance_checks) * 100
        is_sweeper = low_balance_percentage_calculated >= low_balance_percentage

        return {
            "is_sweeper": is_sweeper,
            "low_balance_count": low_balance_count,
            "total_balance_checks": total_balance_checks,
            "low_balance_percentage": low_balance_percentage_calculated,
        }

    
    def evaluate_decline_metrics(self, statement, threshold=20):
        """
        Evaluate decline metrics based on the inflow trends in a statement.

        Args:
            statement (list): List of transaction records with keys 'date' and 'amount'.
            threshold (float): Percentage threshold to determine significant decline.

        Returns:
            dict: A dictionary containing detailed evaluations and summary statistics.
        """
        
        monthly_inflows = {}
        for tx in statement:
            tx_date = datetime.fromisoformat(tx["date"].replace("Z", ""))
            month_key = tx_date.strftime("%Y-%m")
            if month_key not in monthly_inflows:
                monthly_inflows[month_key] = 0
            if tx["type"] == "credit":
                monthly_inflows[month_key] += tx["amount"]

        sorted_months = sorted(monthly_inflows.keys())
        inflow_data = [{"month": month, "inflow": monthly_inflows[month]} for month in sorted_months]

        results = []

        for i, inflow in enumerate(inflow_data):
            current_inflow = inflow["inflow"]
            result = {
                "month": inflow["month"], 
                "inflow":  CreditWorthinessCalculator._reconstruct_amount(current_inflow)
            }

            if i > 0:  # Ensure there is a previous month
                last_month_inflow = inflow_data[i - 1]["inflow"]
                change = ((current_inflow - last_month_inflow) / last_month_inflow) * 100
                result["percentage_change"] = round(change, 2)
                result["significant_decline"] = "Yes" if abs(change) <= -threshold else "No"
            else:
                result["percentage_change"] = 0
                result["significant_decline"] = "No"

            if i > 0:
                result["last_month_>_current"] = "Yes" if last_month_inflow > current_inflow else "No"
            else:
                result["last_month_>_current"] = "No"

            # % Change to Month 6
            if i + 5 < 7 and i + 5 > 5:  # Ensure the 6th month exists
                month_6_inflow = inflow_data[i + 5]["inflow"]
                change_to_6 = ((month_6_inflow - current_inflow) / current_inflow) * 100
                result["percentage_change_to_6_months"] = round(change_to_6, 2)
                result["significant_decline_to_6_months"] = (
                   "Yes" if change_to_6 <= -threshold else "No"
                )
            else:
                result["percentage_change_to_6_months"] = None
                result["significant_decline_to_6_months"] = "No"

            results.append(result)

        total_significant_declines = sum(1 for r in results if r["significant_decline"] == "Yes")
        total_significant_declines_to_6_months = sum(1 for r in results if r["significant_decline_to_6_months"] == "Yes")
    
        return {
            "evaluation": results,
            "summary": {
                "Total Significant Declines": total_significant_declines,
                "Total Significant Declines to 6 Months": total_significant_declines_to_6_months,
            }
        }

    
    def compute_worthiness(self):
        
        statement_data = self.get_last_n_months_data()
        
        total_inflow = self.calculate_total_inflow(statement_data)
        total_outflow = self.calculate_total_outflow(statement_data)

        monthly_inflows = self.get_total_monthly_inflow(statement_data)
        
        dormancy_status =self.check_dormancy(statement_data)
        
        sweeper_account = self.is_sweeper_account(statement_data)
        
        negative_balances = self.is_not_negative_balances(statement_data)

        # decline_metrics = self.evaluate_decline_metrics(statement_data)
       
        
        unique_months = {
            datetime.strptime(tx["date"], "%Y-%m-%dT%H:%M:%S.%fZ").strftime("%Y-%m")
            for tx in statement_data
        }
        
        months = sorted(unique_months)
        
        data = {
            "statement": {
                "duration": f"{len(months)} months",
                "months": months,
                "number_of_transactions": len(statement_data),
                # "tx": statement_data
            }, 
            "credit_debit": {
                "monthly_inflows": monthly_inflows,
                "total_inflow": total_inflow,
                "total_outflow": total_outflow
            },
            "dormancy": {
                "dormancy_status": dormancy_status[1],
                "dormancy_breakdown": dormancy_status[0]
            },
            # "sweeper_account": sweeper_account,
            # "negative_balances": negative_balances
            # "decline_metrics": decline_metrics
            
        }
        
        return data

