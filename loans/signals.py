from django.db import transaction
import uuid
from django.db.models import Q

from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver

from loans.enums import VerificationStage
from loans.models import (
    AjoLoan,
    AjoLoanRepayment,
    BorrowerInfo,
    Holiday,
    InvestmentCapital,
    LoanDiskLoansMirror,
    LoanEligibility,
    LoanGuarantor,
    ProductAssignment,
    TotalInvestmentCapital,
    VfdProviderDisbursementLog,
    VfdRepaymentSchedule,
)
from loans.tasks import (
    create_borrower_on_loan_disk_branch,
    recreate_bulk_schedules,
    run_post_loan_to_loan_disk,
    update_create_investment_record,
    update_loan_status_on_loan_disk,
    update_supervisor_device_mgmt,
)


def generate_session_id(model_instance):
    # Combine a unique identifier with the object's class name and ID
    unique_id = str(uuid.uuid4())
    session_id = f"{model_instance.__class__.__name__}_{model_instance.pk}_{unique_id}"
    return session_id


@receiver(post_save, sender=LoanGuarantor)
def listen_to_create_loan_guarantor(sender, instance, created, **kwargs):
    if created:
        # id = instance.id
        instance.session_id = generate_session_id(model_instance=instance)
        instance.save()


@receiver(post_save, sender=BorrowerInfo)
def listen_to_create_borrower_info(sender, instance, created, **kwargs):
    if created:
        # id = instance.id
        instance.session_id = generate_session_id(model_instance=instance)
        instance.save()


@receiver(post_save, sender=LoanEligibility)
def post_borrower_to_loan_disk(sender, instance, created, **kwargs):
    if created:
        create_borrower_on_loan_disk_branch.delay(ajo_user_id=instance.ajo_user.id)
        # create_borrower_on_loan_disk_branch(eligibility_id=instance.id)

        # Joe, please try creating new instances and see the errors that pop up


# @receiver(post_save, sender=AjoLoan)
# def post_loan_to_loan_disk(sender, instance, created, **kwargs):
#     if created:
#         # post loan to loan disk on create
#         run_post_loan_to_loan_disk.delay(loan_id=instance.id)
#         # run_post_loan_to_loan_disk(loan_id=instance.id)

#         # Joe, please try creating new instances and see the errors that pop up\
#     else:
#         update_loan_status_on_loan_disk.delay(loan_id=instance.id, from_signal=True)


# @receiver(post_save, sender=BorrowerInfo)
# def borrower_info_signal(sender, instance, created, **kwargs):
#     if created:
#         update_loan_status_on_loan_disk.delay(
#             loan_id=instance.loan.id, loan_status=VerificationStage.FACE_MATCH
#         )

@receiver(post_save, sender=AjoLoan)
def initiate_posting_loan_to_loan_disk(sender, instance, created, **kwargs):
    if created:
        # create the mirror instance and pass it as pending or initiated and pass the request type too
        LoanDiskLoansMirror.create_loan_entry_instance(instance)
    else:
        LoanDiskLoansMirror.update_loan_entry_instance(instance)

@receiver(post_save, sender=BorrowerInfo)
def borrower_info_signal(sender, instance, created, **kwargs):
    if created:
        LoanDiskLoansMirror.update_loan_entry_instance(instance.loan, loan_verification_stage=VerificationStage.FACE_MATCH)


@receiver(post_save, sender=AjoLoanRepayment)
def loan_disk_repayment_signal(sender, instance, created, **kwargs):
    if created:
       LoanDiskLoansMirror.repayment_entry_instance(instance)
       print(f"REPAYMENT SIGNAL TRIGGERED ====> ")


@receiver(post_save, sender=InvestmentCapital)
def investment_signal(sender, instance, created, **kwargs):
    if created:
        with transaction.atomic():
            try:
                last_investment_capital = InvestmentCapital.objects.filter(
                    id__lt=instance.id
                ).latest("id")
            except InvestmentCapital.DoesNotExist:
                last_investment_capital = None

            try:
                total_investment_capital = TotalInvestmentCapital.objects.latest("id")
            except TotalInvestmentCapital.DoesNotExist:
                total_investment_capital = None

            if last_investment_capital:
                instance.total_investment_amount = (
                    instance.investment_amount
                    + last_investment_capital.total_investment_amount
                )
            else:
                instance.total_investment_amount = instance.investment_amount

            instance.save()

            if total_investment_capital:
                total_investment = (
                    total_investment_capital.total_investment
                    + instance.investment_amount
                )
                TotalInvestmentCapital.objects.create(total_investment=total_investment)
            else:
                TotalInvestmentCapital.objects.create(
                    total_investment=instance.investment_amount
                )

            record = update_create_investment_record()
            instance.all_time_total_repayments = record.get("alltime_repayment")
            instance.all_time_total_disbursements = record.get(
                "alltime__disbursement_amount"
            )
            instance.all_time_escrow_balance = record.get("all_time_escrow_balance")
            instance.total_outstanding_repayment = record.get(
                "total_outstanding_repayment"
            )
            instance.all_time_commissions = record.get("all_time_commissions")
            instance.save()


@receiver(post_save, sender=ProductAssignment)
def product_assignment_signals(sender, instance, created, **kwargs):
    if created:
        update_supervisor_device_mgmt.delay(device_mgmt_id=instance.id)


@receiver(post_save, sender=Holiday)
def update_loan_schedules(sender, instance, created, **kwargs):
    if created:
        if instance.type == "agent":
            # loans = AjoLoan.objects.filter(agent=instance.agent, status="OPEN")
            loans = AjoLoan.objects.filter(
                Q(agent=instance.agent)
                & (Q(status="OPEN") | Q(performance_status="PAST_MATURITY"))
            )
            ids = [loan.id for loan in loans]
            recreate_bulk_schedules.delay(ids)
        elif instance.type == "branch":
            # loans = AjoLoan.objects.filter(
            #     borrower__branch_name=instance.branch.name,
            #     borrower__branch__isnull=False,
            #     status="OPEN",
            # )
            loans = AjoLoan.objects.filter(
                Q(borrower__branch_name=instance.branch.name)
                & Q(borrower__branch__isnull=False)
                & (Q(status="OPEN") | Q(performance_status="PAST_MATURITY"))
            )
            ids = [loan.id for loan in loans]
            recreate_bulk_schedules.delay(ids)
        else:
            loans = AjoLoan.objects.filter(
                Q(status="OPEN") | Q(performance_status="PAST_MATURITY")
            )
            ids = [loan.id for loan in loans]
            recreate_bulk_schedules.delay(ids)


@receiver(post_save, sender=VfdProviderDisbursementLog)
def create_vfd_repayment_schedule(sender, instance, created, **kwargs):
    pass
    # if instance.status == "SUCCESS":
    #     if not VfdRepaymentSchedule.objects.filter(disbursement_log=instance):
    #         VfdRepaymentSchedule.create_repayment_schedule(disburse_instance=instance)
    #     else:
    #         pass

@receiver(post_delete, sender=Holiday)
def update_loan_schedule_on_delete(sender, instance, **kwargs):
    """
    This signal updates a loan schedule when an holiday is deleted.
    """
    if instance.type == "agent":
        # loans = AjoLoan.objects.filter(agent=instance.agent, status="OPEN")
        loans = AjoLoan.objects.filter(
            Q(agent=instance.agent)
            & (Q(status="OPEN") | Q(performance_status="PAST_MATURITY"))
        )
        ids = [loan.id for loan in loans]
        recreate_bulk_schedules.delay(ids, is_delete=True)
    elif instance.type == "branch":
        # loans = AjoLoan.objects.filter(
        #     borrower__branch_name=instance.branch.name,
        #     borrower__branch__isnull=False,
        #     status="OPEN",
        # )
        loans = AjoLoan.objects.filter(
            Q(borrower__branch_name=instance.branch.name)
            & Q(borrower__branch__isnull=False)
            & (Q(status="OPEN") | Q(performance_status="PAST_MATURITY"))
        )
        ids = [loan.id for loan in loans]
        recreate_bulk_schedules.delay(ids, is_delete=True)
    else:
        loans = AjoLoan.objects.filter(
            Q(status="OPEN") | Q(performance_status="PAST_MATURITY")
        )
        ids = [loan.id for loan in loans]
        recreate_bulk_schedules.delay(ids, is_delete=True)

