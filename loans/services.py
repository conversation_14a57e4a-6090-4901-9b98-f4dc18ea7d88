from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.db import transaction as django_transaction

from accounts.model_choices import UserType
from ajo import payment_actions as APA
from ajo.models import AjoSaving
from ajo.selectors import AjoCommissionsSelector, AjoUserSelector
from payment.model_choices import Status, TransactionSource
from payment.models import WalletSystem
from payment.services import (
    Transaction,
    TransactionFormType,
    TransactionService,
    TransactionTypeCreditOrDebitChoices,
    WalletTypes,
)


class DiscrepancyFixes:
    COMPANY_EMAIL = "<EMAIL>"

    @classmethod
    @django_transaction.atomic
    def debit_company_commissions(
        cls,
        commission_amount: float,
        description: str,
    ) -> None:
        """
        Debit the company commissions wallet and refund to an ajo user in any way

        Args:
            commission_amount (float): the amount to be deducted
            description (str): the description for why the deduction occurred

        Raises:
            ValueError: insufficient amount to debit
        """
        amount = commission_amount
        user = get_user_model().objects.get(email=cls.COMPANY_EMAIL)
        commission_wallet = AjoCommissionsSelector(user=user).get_commissions_wallet()

        if commission_wallet.available_balance < amount:
            raise ValueError("insufficient amount to debit")

        debit_transaction = TransactionService.create_debit_commission_balance_from_agent_wallet_transaction(
            user=user,
            amount=amount,
            transaction_description=description,
        )

        APA.debit_agent_commission_wallet_balance(
            transaction=debit_transaction,
            wallet=commission_wallet,
            amount=amount,
        )

    @classmethod
    @django_transaction.atomic
    def debit_agent_commissions(
        cls,
        agent: AbstractUser,
        commission_amount: float,
        description: str,
    ) -> None:
        """
        Debit the agent commissions wallet and refund to an ajo user in any way

        Args:
            agent (AbstractUser): the agent
            commission_amount (float): the amount to be deducted
            description (str): the description for why the deduction occurred

        Raises:
            ValueError: insufficient amount to debit
        """
        amount = commission_amount
        commission_wallet = AjoCommissionsSelector(user=agent).get_commissions_wallet()

        if commission_wallet.available_balance < amount:
            raise ValueError("insufficient amount to debit from agent's wallet")

        debit_transaction = TransactionService.create_debit_commission_balance_from_agent_wallet_transaction(
            user=agent,
            amount=amount,
            transaction_description=description,
        )

        APA.debit_agent_commission_wallet_balance(
            transaction=debit_transaction,
            wallet=commission_wallet,
            amount=amount,
        )

    @classmethod
    @django_transaction.atomic
    def debit_company_commissions_fund_ajo_plan(
        cls,
        ajo_plan: AjoSaving,
        unique_reference: str,
    ):
        """
        Debit the company commissions wallet and refund into an active ajo plan

        Args:
            ajo_plan (AjoSaving): the ajo plan to be refunded
        """
        amount = ajo_plan.commission_amount

        cls.debit_company_commissions(
            commission_amount=amount,
            description=f"{amount} was debited to refund ajo plan {ajo_plan.name}, {ajo_plan.ajo_user.phone_number}",
            unique_reference=unique_reference,
        )

        deposit_transaction = APA.fund_ajo_savings_position(
            user=ajo_plan.user,
            ajo_user=ajo_plan.ajo_user,
            ajo_savings=ajo_plan,
            amount=amount,
        )

        deposit_transaction.description = f"{amount} was paid as a commission refund"
        deposit_transaction.save()

    @classmethod
    @django_transaction.atomic
    def debit_company_commissions_fund_ajo_user_wallet(
        cls,
        commissions_amount: float,
        wallet: WalletSystem,
        plan_name: str,
        unique_reference: str,
        quotation_id: str,
    ):
        ajo_user = wallet.onboarded_user
        number = ajo_user.phone_number

        cls.debit_company_commissions(
            commission_amount=commissions_amount,
            description=f"{commissions_amount} was deducted to repay {number} commissions for {plan_name}",
        )

        # create a transaction instance
        fund_transaction = TransactionService.create_deposit_by_virtual_account_in_ajo_transaction(
            user=ajo_user.user,
            ajo_user=ajo_user,
            unique_reference=unique_reference,
            description=f"{commissions_amount} was just deposited into your wallet as commissions refund",
            amount=commissions_amount,
            wallet_type=wallet.wallet_type,
            status=Status.PENDING,
            transaction_source=TransactionSource.WALLET,
            quotation_id=quotation_id,
        )

        # increment the wallet and update the transaction fields
        APA.fund_wallet_and_update_transaction(
            wallet=wallet,
            amount=commissions_amount,
            transaction_instance=fund_transaction,
            ajo_user=ajo_user,
        )

    @classmethod
    @django_transaction.atomic
    def debit_agent_and_company_commissions_and_fund_ajo_user(
        cls,
        commissions_amount: float,
        wallet: WalletSystem,
        plan_name: str,
        quotation_id: str,
        unique_reference: str,
    ):
        ajo_user = wallet.onboarded_user
        agent = ajo_user.user
        number = ajo_user.phone_number

        ### CHECKS

        # get all the commission transactions for that plan
        commission_transactions = Transaction.objects.filter(
            transaction_form_type__in=[TransactionFormType.COMMISSION, TransactionFormType.SERVICE_CHARGE],
            quotation_id=quotation_id,
        )

        try:
            agent_commission_transaction = commission_transactions.get(
                user=agent,
                wallet_type=WalletTypes.AJO_COMMISSION,
                transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
            )
            agent_commission_amount = agent_commission_transaction.amount
        except Transaction.DoesNotExist:
            raise ValueError("could not obtain the agent commission transaction")

        try:
            company_user = get_user_model().objects.get(email=cls.COMPANY_EMAIL)
            company_commission_transaction = commission_transactions.get(
                user=company_user,
                wallet_type=WalletTypes.AJO_COMMISSION,
                transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
            )
            company_commission_amount = company_commission_transaction.amount
        except Transaction.DoesNotExist:
            raise ValueError("could not obtain the company commission transaction")

        sum_of_commissions = agent_commission_amount + company_commission_amount

        if not sum_of_commissions + 100 == commissions_amount:
            raise ValueError("sum of divided commissions not matching")

        cls.debit_company_commissions(
            commission_amount=company_commission_amount,
            description=f"{company_commission_amount} was deducted to repay {number} commissions for {plan_name}",
        )

        cls.debit_agent_commissions(
            agent=agent,
            commission_amount=agent_commission_amount,
            description=f"{agent_commission_amount} was deducted to repay {number} commissions for {plan_name}",
        )

        # create a transaction instance
        fund_transaction = TransactionService.create_deposit_by_virtual_account_in_ajo_transaction(
            user=ajo_user.user,
            ajo_user=ajo_user,
            unique_reference=unique_reference,
            description=f"{commissions_amount} was just deposited into your wallet as commissions refund",
            amount=commissions_amount,
            wallet_type=wallet.wallet_type,
            status=Status.PENDING,
            transaction_source=TransactionSource.WALLET,
            quotation_id=quotation_id,
        )

        # increment the wallet and update the transaction fields
        APA.fund_wallet_and_update_transaction(
            wallet=wallet,
            amount=commissions_amount,
            transaction_instance=fund_transaction,
            ajo_user=ajo_user,
        )

    @classmethod
    @django_transaction.atomic
    def convert_ajo_savings_to_repayment(cls, ajo_plan: AjoSaving, loan_id: int):
        user = ajo_plan.user
        quotation_id = ajo_plan.quotation_id

        if not ajo_plan.is_active:
            raise ValueError("this plan is inactive")

        if ajo_plan.is_loan_repayment or ajo_plan.loan_id:
            raise ValueError("this plan is already a loan repayment")

        commission_transaction = Transaction.objects.filter(
            user=user,
            transaction_form_type=TransactionFormType.COMMISSION,
            transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
            quotation_id=quotation_id,
            wallet_type=WalletTypes.AJO_USER,
        )

        if not user.user_type == UserType.STAFF_AGENT:
            raise ValueError("this plan's commissions did not solely go to the company")

        if commission_transaction.exists():
            # check if the user is a staff agent

            unique_reference = f"{commission_transaction.last().transaction_id}_reversal"

            if Transaction.objects.filter(unique_reference=unique_reference).exists():
                raise ValueError("this commission has been paid back")

            # refund the plan from the company's commission wallet
            cls.debit_company_commissions_fund_ajo_plan(
                ajo_plan=ajo_plan,
                unique_reference=unique_reference,
            )

        ajo_plan.loan_id = loan_id
        ajo_plan.is_loan_repayment = True
        ajo_plan.loan = True
        ajo_plan.save()

    @classmethod
    @django_transaction.atomic
    def refund_commissions_taken_from_plan_into_spend(
        cls,
        plan: AjoSaving,
    ) -> None:
        user = plan.user
        ajo_user = plan.ajo_user
        quotation_id = plan.quotation_id

        commission_transaction = Transaction.objects.filter(
            transaction_form_type=TransactionFormType.COMMISSION,
            transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
            quotation_id=quotation_id,
            wallet_type=WalletTypes.AJO_USER,
            amount=plan.commission_amount,
        )

        if not commission_transaction.exists():
            raise ValueError("no commission was not deducted for this plan")

        unique_reference = f"{commission_transaction.last().transaction_id}_reversal"

        if Transaction.objects.filter(unique_reference=unique_reference).exists():
            raise ValueError("this commission has been paid back")

        spend_wallet = AjoUserSelector(ajo_user=ajo_user).get_spending_wallet()

        if user.user_type != UserType.STAFF_AGENT:
            # raise ValueError("this plan's commissions did not solely go to the company")
            cls.debit_agent_and_company_commissions_and_fund_ajo_user(
                commissions_amount=plan.commission_amount,
                wallet=spend_wallet,
                plan_name=plan.name,
                quotation_id=plan.quotation_id,
                unique_reference=unique_reference,
            )

        else:
            cls.debit_company_commissions_fund_ajo_user_wallet(
                commissions_amount=plan.commission_amount,
                wallet=spend_wallet,
                plan_name=plan.name,
                unique_reference=unique_reference,
                quotation_id=quotation_id,
            )

    @classmethod
    @django_transaction.atomic
    def liquidate_ajo_savings(cls, plan: AjoSaving) -> str:
        """
        This plan is intended to refund commissions and liquidate an ajo
        savings plan, because it was mistakenly created

        Args:
            plan (AjoSaving): the ajo savings plan to liquidate

        Raises:
            ValueError: various ValueError exceptions may be raised

        Returns:
            str: success message
        """
        if plan.loan or plan.is_loan_repayment:
            raise ValueError("can not liquidate loan savings plan")

        if not plan.is_active or not plan.is_activated:
            raise ValueError("this plan is not active or was never activated")

        # attempt to refund commissions and if it complains that commissions
        # was never taken or has been taken, it will continue the flow
        # if not, it will raise the caught exception
        try:
            cls.refund_commissions_taken_from_plan_into_spend(plan=plan)
            follow_up = "commission was refunded successfully"
        except Exception as err:
            if "commission" in str(err):
                follow_up = f"{err}"
            else:
                raise err

        # check if the amount_saved is 0
        if plan.amount_saved <= 0:
            raise ValueError(f"{follow_up}; no amount in plan to liquidate")

        ajo_user_selector = AjoUserSelector(ajo_user=plan.ajo_user)
        if ajo_user_selector.get_ajo_user_wallet_balance() < plan.amount_saved:
            raise ValueError(f"{follow_up}; insufficient amount in savings wallet to liquidate")

        transactions = APA.move_mature_funds_to_spending_for_ajo_user(ajo_plan=plan)

        debit_transaction: Transaction = transactions.get("debit_transaction")
        debit_transaction.description = (
            f"{plan.name} was liquidated, {debit_transaction.amount} was debited from your savings wallet."
        )
        debit_transaction.save()

        return f"{follow_up}; plan liquidated into spend wallet successfully"
