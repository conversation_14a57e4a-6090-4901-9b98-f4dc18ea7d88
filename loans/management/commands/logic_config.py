import os
from datetime import date, datetime

from django.core.management import BaseCommand

from loans.models import *

from pprint import pprint

logger = settings.LOGGER


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **options):
        agency_banking_handler = LibertyPayMgr(config=settings)
        get_user_info = agency_banking_handler.get_agency_banking_users(id="")
        pprint(get_user_info)
