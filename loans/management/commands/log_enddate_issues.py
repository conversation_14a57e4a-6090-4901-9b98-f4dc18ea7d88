from django.core.management.base import BaseCommand
from loans.models import AjoLoan
from loans.enums import LoanStatus


class Command(BaseCommand):
    help = "Categorize loans with end date issues"

    def handle(self, *args, **kwargs):

        open_loans = AjoLoan.objects.filter(
            status=LoanStatus.OPEN,
            # end_date_issue=False,
        )

        for loan_instance in open_loans:
            self.stdout.write(
                self.style.SUCCESS(f"LOAN ID: {loan_instance.id} RUNNING")
            )

            loan_calc = AjoLoan.compute_interest_with_duration(
                principal=loan_instance.amount,
                duration=loan_instance.actual_duaration,
                loan_type=loan_instance.loan_type,
                ajouser=loan_instance.borrower,
                start_date=loan_instance.start_date,
            )

            if loan_calc is None:
                self.stdout.write(
                    self.style.ERROR(f"{loan_instance.id} FAILED - COULD NOT LOAN CALC RETURNED NONE")
                )
                continue

            expected_end_date = loan_calc.get("end_date")
            loan_instance.expected_end_date = expected_end_date
            loan_instance.end_date_issue = loan_instance.end_date != expected_end_date
            loan_instance.save()

            self.stdout.write(
                self.style.SUCCESS(f"LOAN ID: {loan_instance.id} COMPLETED")
            )

        self.stdout.write(
            self.style.SUCCESS(
                "Open loans end dates resolved successfully",
            )
        )
