from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db.models import Sum
from loans.models import AjoLoan, AjoLoanRepayment, PastMaturityBuffer
from loans.serializers import LoanHistorySerializer
from loans.enums import <PERSON>an<PERSON>tat<PERSON>, LoanBehavior
from accounts.models import CustomUser, ConstantTable
import pprint


class Command(BaseCommand):
    help = "Check the percentage of loan repayments for a given agent"

    def add_arguments(self, parser):
        parser.add_argument("agent", type=str, help="The email of the agent to check")

    def handle(self, *args, **kwargs):
        agent_email = kwargs["agent"]
        user = CustomUser.objects.filter(email=agent_email).first()
        if not user:
            print("No user with this email")
            return
        const = ConstantTable.get_constant_table_instance()
        today = timezone.now().date()

        # Get the total amount paid for all open loans
        loans = user.ajoloans_set.filter(status=LoanStatus.OPEN)
        loans = loans.select_related(
            "agent",
            "borrower",
            "eligibility",
            "guarantor",
            "repayment_savings",
            "checked_by",
        )

        agent_buffer_instance = PastMaturityBuffer.objects.filter(agent=user).last()
        agent_buffer_amount = (
            agent_buffer_instance and agent_buffer_instance.agent_buffer_amount or 0.0
        )

        repayments = AjoLoanRepayment.objects.filter(
            ajo_loan__status="OPEN", agent=user
        )
        total_paid = (
            repayments.aggregate(total_paid=Sum("repayment_amount"))["total_paid"] or 0
        ) + agent_buffer_amount

        # Get the total due amount till yesterday for all open loans
        total_due_yesterday = sum(
            loan.due_yesterday for loan in loans if loan.due_yesterday > 0
        )
        owing_loans = []
        total_past_maturity_amount = None
        average_past_maturity_days = None
        reason = None
        balances = []
        repayments = []
        dues = []
        loans_past_maturity = []

        for loan in loans:
            repayment_total = (
                AjoLoanRepayment.objects.filter(ajo_loan=loan).aggregate(
                    total_repayments=Sum("repayment_amount")
                )["total_repayments"]
                or 0
            )
            if loan.due_yesterday > repayment_total:
                balance = loan.due_yesterday - repayment_total
                
                if loan.performance_status == LoanBehavior.PAST_MATURITY:
                    
                    if agent_buffer_amount >= balance:
                        agent_buffer_amount -= balance
                        continue
                    else:
                        loans_past_maturity.append(loan)
                        owing_loans.append(loan)
                        balances.append(balance)
                        repayments.append(repayment_total)
                        dues.append(loan.due_yesterday)
                else:
                    owing_loans.append(loan)
                    balances.append(balance)
                    repayments.append(repayment_total)
                    dues.append(loan.due_yesterday)
                    
        if total_due_yesterday == 0:
            percentage = 100
        elif total_paid == 0:
            percentage = 0
        else:
            percentage = abs((total_paid / total_due_yesterday) * 100)

        if sum(dues) == 0:
            repay_percent = 100
        else:
            repay_percent = sum(repayments) / sum(dues) * 100

        if percentage < const.get_agent_teir_percent(len(loans)):
            can_give_loan = False
            reason = "Failed overall collection percentage check"
        else:
            if repay_percent < const.percent_checker:
                can_give_loan = False
                reason = "Failed individual loan repayment percentage check"
            else:

                if len(loans_past_maturity) > 0:
                    # Calculate the average amount of these loans
                    total_past_maturity_amount = sum(
                        loan.due_yesterday for loan in loans_past_maturity
                    )

                    # Calculate the average outstanding days of these loans
                    average_past_maturity_days = sum(
                        loan.outstanding_days for loan in loans_past_maturity
                    ) / len(loans_past_maturity)

                    if (
                        total_past_maturity_amount
                        > const.past_maturity_amount_threshold
                        or average_past_maturity_days
                        > const.past_maturity_days_threshold
                    ):
                        can_give_loan = False
                        reason = "Failed past maturity loan check"
                    else:
                        can_give_loan = True
                else:
                    can_give_loan = True

        # Combine sets to ensure all entries are unique
        sorted_combined_loans = sorted(
            owing_loans, key=lambda loan: loan.created_at, reverse=True
        )
        disbursed_amount = (
            AjoLoan.objects.filter(
                is_disbursed=True,
                agent=user,
                date_disbursed__date__month=(timezone.now().month - 1),
            ).aggregate(Sum("amount"))["amount__sum"]
            or 0.0
        )
        commission_amount = disbursed_amount * (0.5 / 100)

        data = {
            "message": "success",
            "percent": percentage,
            "amount_due": total_due_yesterday,
            "total_paid": total_paid,
            "can_give_loan": can_give_loan,
            "show_warning": True if percentage < const.preliminary_checker else False,
            "loans": LoanHistorySerializer(sorted_combined_loans, many=True).data,
            "possible_winning": commission_amount,
            "missing_count": len(sorted_combined_loans),
            "total_missing_loans": sum(balances),
            "total_past_maturity_amount": total_past_maturity_amount,
            "average_past_maturity_days": average_past_maturity_days,
            "block_reason": reason,
            "due_to_repayment_ratio": repay_percent,
            "sum_individual_due": sum(dues),
            "sum_individual_repayment": sum(repayments),
            "total_past_maturity_amount": total_past_maturity_amount,
        }

        pprint.pprint(data)
