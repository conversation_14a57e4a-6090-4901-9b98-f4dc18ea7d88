from datetime import date, datetime
import requests
from django.core.management import BaseCommand
import pandas as pd

import os

from loans.models import *

from web_scrap import LLMEnhancedScraper

logger = settings.LOGGER

from loans.tasks import *


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **options):
       
        # liberty_lifemgr = LibertyLifeMgr()

        # i = liberty_lifemgr.update_credit_health_request_status_stages(
        #     loan_access_code="ZB50G9M8D8TBTHQ", stage="", request_status="PENDING"
        # )

        # i = liberty_lifemgr.get_credit_health_details(
        #     loan_access_code="ZB50G9M8D8TBTHQ"
        # )
        # print(i, "\n\n")

        # scraper = LLMEnhancedScraper(
        #     "", "***************************************************", ""
        # )
        # # jumia_urls = "https://www.jumia.com.ng/catalog/?q=phones"
        # jumia_urls = "https://www.jumia.com.ng/health-beauty"

        # scrapped_file_jumia = scraper.scrape_product(jumia_urls)

        # # url = "https://savingsdev.libertypayng.com/api/v1/bnpl/wbhook/update-product/"
        # url = "https://dragon-humble-blatantly.ngrok-free.app/api/v1/bnpl/wbhook/update-product/"

        # # payload = normalize_data(scrapped_file_jumia)
        # payload = json.dumps(scrapped_file_jumia)
        # headers = {}

        # response = requests.request("POST", url, headers=headers, json=payload)

        # i = PayboxMgr().get_category_and_subcategory_details(
        #     category_name="Electronics",
        #     subcategory_name="Phones",
        #     company_id="166f5d8e-0628-49bc-9f13-9589ae196acb",
        # )
        # i = PayboxMgr.create_product()
        # print(i)
        # DATE_OLD = timezone.now() - timezone.timedelta(days=90)
        # print(DATE_OLD.date())
        # liberty = LibertyPayMgr()
        # # # response = liberty.get_agency_banking_users(type_of_user="AGENT")
        # # # response = liberty.get_agency_banking_users(id=246)
        # response = liberty.get_agency_banking_transactions(timestamp_gte=DATE_OLD.date())
        # pprint(response)
        # const = get_constants()

        # i = MerchantEligibilitySummary.process_merchant_transaction()

        # i = MerchantEligibilitySummary.get_transaction_from_liberty_pay_create_eligibility_summary()
        # print(i)
        # to_exclude_phones = []

        # verified_borrowers = BorrowerInfo.objects.filter(
        #     is_verified=True, verification_type="BVN"
        # ).exclude(borrower__phone_number__in=to_exclude_phones)[:80]

        # borrower_data = []
        # for verified_borrower in verified_borrowers:
        #     user = verified_borrower.borrower
        #     verification_number = verified_borrower.verification_number
        #     borrower_data.append(
        #         {
        #             # "full_name": f"{user.first_name} {user.last_name}",
        #             "phone_number": user.phone_number,
        #             "first_name": user.first_name,
        #             "last_name": user.last_name,
        #             "home_address": user.address,
        #             "bvn": (
        #                 verification_number
        #                 if verified_borrower.verification_type == "BVN"
        #                 else ""
        #             ),
        #             "nin": (
        #                 verification_number
        #                 if verified_borrower.verification_type == "NIN"
        #                 else ""
        #             ),
        #             "date_of_birth": verified_borrower.date_of_birth,
        #             "age": verified_borrower.age,
        #         }
        #     )
        # # Create a pandas DataFrame
        # df = pd.DataFrame(borrower_data)

        # # Save to static/generated/
        # generated_folder = os.path.join(settings.BASE_DIR, "static", "generated")
        # os.makedirs(generated_folder, exist_ok=True)

        # file_name = "borrower_info.xlsx"
        # file_path = os.path.join(generated_folder, file_name)
        # df.to_excel(file_path, index=False)

        # # Construct relative URL manually since staticfiles_storage won't know dynamic files
        # file_url = f"{settings.STATIC_URL}generated/{file_name}"

        # print(f"Saved Excel with {len(borrower_data)} entries to: {file_path}")
        # print(f"Access file at: {file_url}")

        agency_banking_handler = LibertyPayMgr(config=settings)
        get_user_info = agency_banking_handler.get_agency_banking_users(
            id="246"
        )
        pprint(get_user_info)
        pass
# last week