from django.core.management.base import BaseCommand

from loans.tasks import create_eligibility_for_eligible_save_per_agent

import argparse


# from loans.models import LoanEligibility

# Define the loan types as an enumeration
class LoanType:
    PROSPER = 'prosper'
    STAFF = 'staff'
    NILL = 'nill'

parser = argparse.ArgumentParser()

class Command(BaseCommand):
    help = "CREATE ELIGIBILITY FOR ELIGIBLE SAVERS"

    def add_arguments(self, parser):
        parser.add_argument('--ltype', dest='ltype', type=str, choices=[LoanType.PROSPER, LoanType.STAFF], default=LoanType.NILL, help="This determines the loan type to checked - It is optional.")


    def handle(self, *args, **options):
        loan_type = options.get('ltype')

        eligibility = create_eligibility_for_eligible_save_per_agent()

        # # eligible_ins = LoanEligibility.objects.get(id=27)
        # # eligibility = LoanEligibility.create_borrower_on_loan_disk_branch(eligibility_instance=eligible_ins)
        # print(eligibility)
