from django.core.management.base import BaseCommand

from loans.tasks import one_off_fund_ajo_user_task


class Command(BaseCommand):
    help = 'Fund ajo wallet that was debited with no fund transaction trace'

    def add_arguments(self, parser):
        parser.add_argument('debit_txn_id', type=int, help='debited transaction id')
        parser.add_argument('savings_id', type=int, help='savings id')

    def handle(self, *args, **options):
        debit_txn_id = options['debit_txn_id']
        savings_id = options['savings_id']
        one_off_task = one_off_fund_ajo_user_task(debit_txn_id=debit_txn_id, savings_id=savings_id)
        self.stdout.write(self.style.SUCCESS(one_off_task))
