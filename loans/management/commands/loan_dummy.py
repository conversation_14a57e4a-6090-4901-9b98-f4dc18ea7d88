import random
from pprint import pprint

from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand

from ajo.model_choices import Gender
from ajo.utils.ussd_utils import generate_ussd_otp
from loans.helpers.youverify import YouVerifyAPI
from loans.models import LoanEligibility

User = get_user_model()

first_names = ['<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>']
last_names = ['<PERSON>la<PERSON>i', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']

random_first_name = random.choice(first_names)
random_last_name = random.choice(last_names)

phone_number_digits = ''.join(random.choices('0123456789', k=10))
random_gender = random.choice([Gender.MALE, Gender.FEMALE, Gender.OTHER])


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        # user = User.objects.get(id=2)
        #
        # values = {
        #     "phone_number": f"234{phone_number_digits}",
        #     "first_name": random_first_name,
        #     "last_name": random_last_name,
        #     "gender": random_gender,
        #     "marital_status": "SINGLE",
        #     "state": "LAGOS",
        #     "lga": "LGA",
        #     "address": "27 ALARA STREET SABO YABA",
        #     "trade_location": "27 ALARA STREET SABO YABA",
        #     "bvn": ''.join(random.choices('0123456789', k=10)),
        #     "nin": ''.join(random.choices('0123456789', k=10)),
        # }
        #
        # for i in range(3):
        #     AjoUser.objects.create(user=user, **values)
        #     print("Ajousers created successfully")

        
        # you_v = YouVerifyAPI()
        # pvc = "00A0A0A000000000000"
        # response = you_v.pvc_verification(pvc=pvc, request_id="1209348756")
        response = generate_ussd_otp(phone_number="07054383514")
        pprint(response)
