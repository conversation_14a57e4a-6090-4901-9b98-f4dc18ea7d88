from django.core.management.base import BaseCommand
from datetime import datetime
from django.db import transaction


class Command(BaseCommand):
    help = 'Update loans based on holiday and weekend calculations'

    def add_arguments(self, parser):
        parser.add_argument('start_date', type=str, help='Start date for loan processing (DD-MM-YYYY)')
        parser.add_argument('end_date', type=str, help='End date for loan processing (DD-MM-YYYY)')

    def handle(self, *args, **kwargs):
        from loans.models import AjoLoan, AjoLoanRepayment
        from loans.tasks import update_loan_repayment_schedules
        from loans.helpers.holiday import HolidayHelper
        
        holiday_helper = HolidayHelper()

        # Convert input strings to date objects
        start_date = datetime.strptime(kwargs['start_date'], '%d-%m-%Y').date()
        end_date = datetime.strptime(kwargs['end_date'], '%d-%m-%Y').date()

        # Fetch loans for the specified agent within the date range
        loans = AjoLoan.objects.filter(created_at__date__range=(start_date, end_date))

        with transaction.atomic():
            for loan in loans:
                print(f'Processing loan {loan.id}...')
                print("start Date", loan.start_date)
                print("end Date", loan.end_date)
                print()
                if loan.end_date:
                    new_end_date = holiday_helper.extend_end_date(loan.start_date, loan.end_date, agent=loan.agent)
                    loan.end_date = new_end_date
                    loan.save()
                    loan.delete_schedules()
                    loan.create_schedule()
                    repayments = AjoLoanRepayment.objects.filter(ajo_loan=loan)
                    for repayment in repayments:
                        update_loan_repayment_schedules(loan.id, repayment.id)

                    print(f'Updated loan {loan.id}: new end date is {new_end_date}')
