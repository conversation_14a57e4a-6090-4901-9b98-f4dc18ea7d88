from django.core.management.base import BaseCommand
from datetime import datetime
from django.db import transaction


class Command(BaseCommand):
    help = "Update loans based on holiday and weekend calculations --- NEW METRICS"

    def add_arguments(self, parser):
        parser.add_argument(
            "start_date", type=str, help="Start date for loan processing (DD-MM-YYYY)"
        )
        parser.add_argument(
            "end_date", type=str, help="End date for loan processing (DD-MM-YYYY)"
        )

    def handle(self, *args, **kwargs):
        from loans.models import AjoLoan, AjoLoanRepayment
        from loans.tasks import update_loan_repayment_schedules
        from loans.helpers.holiday import HolidayHelper

        holiday_helper = HolidayHelper()

        # Convert input strings to date objects
        start_date = datetime.strptime(kwargs["start_date"], "%d-%m-%Y").date()
        end_date = datetime.strptime(kwargs["end_date"], "%d-%m-%Y").date()

        # Fetch loans for the specified agent within the date range
        loans = AjoLoan.objects.filter(
            created_at__date__range=(start_date, end_date),
            end_date_issue=True,
        )

        with transaction.atomic():
            for loan in loans:
                print(f"Processing loan {loan.id}...")
                print("start Date", loan.start_date)
                print("end Date", loan.end_date)
                print()
                if loan.end_date:
                    loan_calc = AjoLoan.compute_interest_with_duration(
                        principal=loan.amount,
                        duration=loan.actual_duaration,
                        loan_type=loan.loan_type,
                        ajouser=loan.borrower,
                        start_date=loan.start_date,
                    )
                    new_end_date = loan_calc.get("end_date")
                    tenor_in_days = loan_calc.get("tenor_in_days")
                    daily_repayment_amount = loan_calc.get("daily_repayment_amount")
                    repayment_amount = loan_calc.get("repayment_amount")
                    interest_amount = loan_calc.get("interest_amount")
                    interest_rate = loan_calc.get("interest_rate")

                    loan.end_date = new_end_date
                    loan.interest_amount = interest_amount
                    loan.interest_rate = interest_rate
                    loan.repayment_amount = repayment_amount
                    loan.tenor_in_days = tenor_in_days
                    loan.daily_repayment_amount = daily_repayment_amount
                    loan.save()

                    loan.delete_schedules()
                    loan.create_schedule()

                    repayments = AjoLoanRepayment.objects.filter(ajo_loan=loan)
                    for repayment in repayments:
                        update_loan_repayment_schedules(loan.id, repayment.id)

                    print(f"Updated loan {loan.id}: new end date is {new_end_date}")
