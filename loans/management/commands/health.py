from django.core.management.base import BaseCommand
from loans.models import AjoLoan, AjoLoanRepayment, AjoLoanSchedule
from loans.tasks import update_loan_repayment_schedules


class Command(BaseCommand):
    help = ""

    def add_arguments(self, parser):
        parser.add_argument('--loan_id', type=int, help='ID of the loan to process', required=False)

    def handle(self, *args, **options):
        loan_id = options.get('loan_id')
        if loan_id:
            loans = AjoLoan.objects.filter(id=loan_id, is_disbursed=True)
        else:
            loans = AjoLoan.objects.filter(is_disbursed=True)

        for loan in loans:
            print("Loan ID:", loan.id)
            if not AjoLoanSchedule.objects.filter(loan=loan).exists():
                loan.create_schedule()

            repayments = AjoLoanRepayment.objects.filter(ajo_loan=loan)

            for repayment in repayments:
                update_loan_repayment_schedules(loan.id, repayment.id)

            health_data = loan.check_health()
            loan.repayment_health_score = health_data.get("repayment_health_score")
            loan.timeliness_score = health_data.get("timeliness_score")
            loan.repayment_percent = health_data.get("repayment_percent")
            loan.save()