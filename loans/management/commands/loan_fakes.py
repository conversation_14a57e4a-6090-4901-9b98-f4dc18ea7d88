from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.management import BaseCommand

from ajo.models import AjoSaving, AjoSavingsPlanType, AjoUser
from loans.models import AjoLoan, LoanEligibility
from loans.tasks import update_loan_status_on_loan_disk

User = get_user_model()


def Fake_Users():

    if settings.ENVIRONMENT == "development":
        user = User.objects.last()
        if user == None:
            user = User.objects.create(
                email="<EMAIL>",
                username="anything",
                user_password="xA=w][51Jd)O1IfT?65\8XQ&aQEF.Y%##gZsyf",
            )

        ajo_user = AjoUser.objects.last()
        if ajo_user == None:
            ajo_user = AjoUser.objects.create(
                user=user,
                phone_number="08012345678",
                first_name="Ajo",
                last_name="User",
                alias="AjoUser",
                gender="M",
                marital_status="Married",
                dob="1990-01-01",
                state="Lagos",
                lga="Ikeja",
                address="No 1, Ajo Street, Lagos",
                trade="Trader",
                trade_location="Eti-osa",
                bvn="12345678901",
                nin="12345678901",
                onboarding_verified=True,
                branch_name="ALABA",
            )

        return ajo_user, user


def fake_ajo_savings(ajo_user_instance):
    if settings.ENVIRONMENT == "development":

        savings_plan = AjoSavingsPlanType.objects.last()
        if savings_plan == None:
            savings_plan = AjoSavingsPlanType.objects.create(
                name="Daily",
                frequency="Daily savings plan",
                month_count=1,
                lower_limit=1.0,
                upper_limit=1000,
            )

        AjoSaving.objects.filter(ajo_user=ajo_user_instance).delete()

        savings = AjoSaving.objects.create(
            user=ajo_user_instance.user,
            ajo_user=ajo_user_instance,
            plan_type=savings_plan,
            name="Ajo Savings",
            frequency="MONTHLY",
            periodic_amount=100,
            duration=30,
            amount_saved=100,
        )

        return savings


def fake_eligibility(ajo_user_instance: AjoUser):
    if settings.ENVIRONMENT == "development":

        savings = fake_ajo_savings(ajo_user_instance=ajo_user_instance)
        eligibility = LoanEligibility.objects.last()
        if eligibility == None:
            eligibility = LoanEligibility.objects.create(
                agent=ajo_user_instance.user,
                approved_by=ajo_user_instance.user,
                ajo_user=ajo_user_instance,
                saving=savings,
                loan_type="AUTO_LOAN",
                amount=1000,
                amount_approved=1000,
                amount_collected=1000,
                amount_saved=1000,
                savings_expected_amount=1000,
                admin_cap_amount=1000,
                multiplier=1,
                percentage_saved=100,
                active=True,
                verified_saver=True,
                is_gliding=True,
                approved=True,
                requires_approval=False,
            )

        return eligibility


def fake_loans(ajo_user_instance: AjoUser):

    AjoLoan.objects.create(
        agent=ajo_user_instance.user,
        borrower=ajo_user_instance,
        borrower_full_name=ajo_user_instance.first_name
        + " "
        + ajo_user_instance.last_name,
        borrower_phone_number=ajo_user_instance.phone_number,
        eligibility=fake_eligibility(ajo_user_instance=ajo_user_instance),
        amount=6000,
        loan_type="PENDING",
        interest_rate=15,
    )


def fake_update_loan_record_on_loandisk(loan_status):

    loans = AjoLoan.objects.last()

    update_loan_status_on_loan_disk(loan_id=loans.id, loan_status=loan_status)


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **options):
        if settings.ENVIRONMENT == "development":
            # ajo_user_instance, user_instance = Fake_Users()

            # fake_loans(ajo_user_instance)

            fake_update_loan_record_on_loandisk(loan_status="credit_bureau_check")
