from django.core.management import BaseCommand
from django.utils import timezone

from ajo.models import *
from loans.models import *
from payment.models import *
import pandas as pd


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **options):

        for data in BorrowerInfo.objects.all():
            print(data.id)
            if data.credit_bureau and data.credit_bureau != "":
                credit_bureau_data_raw_data = data.credit_bureau
                
                # for key, value in data.items():
                #     if value == "true":
                #         data[key] = True

                try:
                    credit_bureau_data = eval(credit_bureau_data_raw_data)
                except NameError:
                    credit_bureau_data = json.loads(credit_bureau_data_raw_data)

                    # Iterate over the dictionary and replace "true" with True
                    for key, value in credit_bureau_data.items():
                        if isinstance(value, str) and value.lower() == "true":
                            credit_bureau_data[key] = True
                    
                subscriber_value = credit_bureau_data.get("subscriber")
                if isinstance(subscriber_value, str):
                    subscriber_list = [subscriber.strip() for subscriber in subscriber_value.split(',')]
                elif isinstance(subscriber_value, list):
                    subscriber_list = [subscriber.strip() for subscriber in subscriber_value]
                else:
                    subscriber_list = []

                credit_bureau_data['subscriber_list'] = subscriber_list
                data.credit_bureau = credit_bureau_data
                data.save()
     




                # if 'subscriber_list' not in credit_bureau_data:
                #     subscriber_string = credit_bureau_data['subscriber']
                #     subscriber_list = [subscriber.strip() for subscriber in subscriber_string.split(',')]
                #     credit_bureau_data['subscriber_list'] = subscriber_list

                #     data.credit_bureau = json.dumps(credit_bureau_data)
                #     data.save()





        # for savings in AjoSaving.objects.filter(loan_id__isnull=False):
        #     get_loan = AjoLoan.objects.filter(id=savings.loan_id).first()
        #     if get_loan:
        #         get_loan.repayment_savings = savings
        #         get_loan.save()


        # table_needed = []
        # count = 1

        # all_repayment_trans = Transaction.objects.filter(transaction_form_type=TransactionFormType.LOAN_REPAYMENT)
        # for repayments in all_repayment_trans.filter(quotation_id__isnull=False):
        #     try:
        #         onboarded_user = AjoLoanRepayment.objects.get(repayment_ref=repayments.unique_reference).borrower
        #         repayments.onboarded_user = onboarded_user
        #         repayments.save()
        #     except:
        #         pass

        # get_distinct_trans = all_repayment_trans.distinct("user")

        # print(get_distinct_trans)

        # all_trans_count = get_distinct_trans.count()

        # for trans in get_distinct_trans:
        #     print(count, "remaining", all_trans_count - count)

        #     user = trans.user
        #     user_trans = all_repayment_trans.filter(user=user)
        #     user_trans_sum = user_trans.aggregate(total=Sum("amount"))["total"] or 0

        #     bank_reyments_sum = RepaymentVirtuaWalletCreditRecord.objects.filter(agent=user).aggregate(total=Sum("amount"))["total"] or 0

        #     user_repayment_records = AjoLoanRepayment.objects.filter(agent=user)
        #     user_repayment_records_sum = user_repayment_records.aggregate(total=Sum("repayment_amount"))["total"] or 0

            
        #     data = {
        #         "user_id": user.id,
        #         "user_email": user.email,
        #         "trans_sum": user_trans_sum,
        #         "corebanking_trans": bank_reyments_sum,
        #         "repaymnet_sum": user_repayment_records_sum,
        #         "diff": (user_trans_sum + bank_reyments_sum) - user_repayment_records_sum,
        #     }
                
        #     table_needed.append(data)


        #     count += 1


        # df = pd.DataFrame(table_needed)
        # df.to_csv("repayment_reconcile.csv")


            



            




