from django.core.management import BaseCommand

from loans.models import AjoLoan


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **options):

        ajo_loans = AjoLoan.objects.filter(
            amount_disbursed=0,
            amount_saved=0,
            status__in=["COMPLETED"],
            date_disbursed__isnull=False,
        )

        count = 0
        for instance in ajo_loans:
            boosta_loans = ["BOOSTA_2X", "BOOSTA_2X_MINI", "CREDIT_HEALTH"]
            if instance.loan_type in boosta_loans:
                loan_calc = AjoLoan.compute_interest_with_duration(
                    principal=instance.amount,
                    duration=instance.expected_repayment_count,
                    loan_type=instance.loan_type,
                    ajouser=instance.borrower,
                )
                disburse_amount = loan_calc.get("disbursement_amount")
            else:
                disburse_amount = instance.amount

            instance.amount_disbursed = disburse_amount
            instance.amount_saved = instance.eligibility.amount_saved

            instance.save(update_fields=["amount_disbursed", "amount_saved"])

            count += 1
        result = f"count ({count})"
        print(result)
