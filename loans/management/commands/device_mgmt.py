from django.core.management import BaseCommand

from accounts.agency_banking import AgencyBankingClass
from loans.tasks import update_supervisor_device_mgmt


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **options):
        # agency_banking_supervisors = AgencyBankingClass.get_agency_supervisor_details()
        o = update_supervisor_device_mgmt(device_mgmt_id=1)
        print(o)
