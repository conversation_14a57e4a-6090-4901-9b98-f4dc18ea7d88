from django.core.management import BaseCommand

from accounts.agency_banking import AgencyBankingClass
from accounts.models import ConstantTable
from ajo.models import AjoUser
from loans.helpers.loandisk_helper import BranchLoanDiskManager, loan_disk_date_format
from loans.models import LoanDiskMetaData, LoanEligibility


class Command(BaseCommand):
    help = "THE AIM OF THIS COMMAND IS TO CREATE ALL BORROWERS TO THEIR INDIVIDUAL LOAN DISK BRANCH IF THEY'VE NOT BEEN CREATED YET."

    def handle(self, *args, **options):

        BRANCH_LOCATION_CODES = (
            ConstantTable.get_constant_table_instance().branch_location_codes
        )

        queryset = AjoUser.objects.filter(
            loandisk_borrower_id__isnull=False, branch_loandisk_borrower_id__isnull=True
        )

        if queryset.exists():
            for ajo_user in queryset:

                # check if this ajo user has eligibility
                eligibility_qs = LoanEligibility.objects.filter(ajo_user=ajo_user)

                if len(eligibility_qs) < 1:
                    continue

                eligibility_instance = eligibility_qs.last()

                if ajo_user.branch_name == None:

                    agency_banking_cls_intance = (
                        AgencyBankingClass.get_ajo_user_loan_branch_team(
                            user_id=ajo_user.user.customer_user_id
                        )
                    )

                    if isinstance(agency_banking_cls_intance, dict):
                        branch_name = agency_banking_cls_intance.get("data", {}).get(
                            "branch", None
                        )
                        if branch_name:
                            ajo_user.branch_name = branch_name
                            ajo_user.save()

                if ajo_user.branch_name:
                    branch_id = BRANCH_LOCATION_CODES.get(
                        str(branch_name).upper(), None
                    )

                    borrower_details = LoanEligibility.borrower_details(
                        ajo_user=ajo_user
                    )

                    borrower_details["date_of_birth"] = str(
                        loan_disk_date_format(ajo_user.dob)
                    )
                    borrower_details["address"] = ajo_user.address
                    borrower_details["age"] = ajo_user.age
                    borrower_details["alias"] = ajo_user.alias
                    borrower_details["bvn"] = ajo_user.bvn

                    response = BranchLoanDiskManager().create_borrower_on_branch(
                        borrower_details=borrower_details, branch_id=branch_id
                    )

                    metadata, created = LoanDiskMetaData.objects.get_or_create(
                        eligibility_id=eligibility_instance.id
                    )
                    metadata.branch_response = response
                    metadata.save()

                    borrower_id = response.get("response", {}).get("borrower_id", None)
                    ajo_user.branch_loandisk_borrower_id = borrower_id
                    ajo_user.save()
