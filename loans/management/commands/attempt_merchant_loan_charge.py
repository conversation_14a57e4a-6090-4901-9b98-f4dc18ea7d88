from django.core.management.base import BaseCommand
from django.utils import timezone

from loans.models import LoanChargeAttempt


class Command(BaseCommand):
    help = "Attempts to charge outstanding loans for the day"

    def handle(self, *args, **kwargs):

        count_charged = LoanChargeAttempt.attempt_charge()

        self.stdout.write(
            self.style.SUCCESS(
                f"Loan charge attempts completed. Count: {count_charged}"
            )
        )
