import datetime
import statistics
from pprint import pprint

import numpy as np
import pandas as pd
from django.core.management.base import BaseCommand
from django.db.models import Avg, Case, Count, Duration<PERSON>ield, F, <PERSON>loat<PERSON>ield, <PERSON>, <PERSON>, Sum, Value, When
from django.db.models.functions import Coalesce

from loans.models import *


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **options):
        pass


### liberty loan
# get total loans


# Filter loans by borrower phone number
ajo_user_loanqs = AjoLoan.objects.filter(borrower_phone_number="", status__in=["OPEN", "COMPLETED"])
# Get ajo user
ajouser = ajo_user_loanqs.last().borrower
# ajouser = AjoUser.objects.get(id=5483)
renewal_rate = AjoLoan.get_loan_renewal_rating(borrower=ajouser)
ajo_user_info = AjoUser.objects.filter(id=ajouser.id).values("trade", "gender", "marital_status")[0]
# Annotate loans with the count of open and completed loans
ajo_user_loanqs = ajo_user_loanqs.annotate(
    open_loan_count=Count("id", filter=Q(status=LoanStatus.OPEN)),
    completed_loan_count=Count("id", filter=Q(status=LoanStatus.COMPLETED)),
)
# Aggregate total loans, and counts of open and completed loans
loan_aggregated_data = ajo_user_loanqs.aggregate(
    libertyloanstaken=Count("id"),
    count_of_open_loan=Sum("open_loan_count"),
    numofrepeatloanamt=Count("amount"),
    count_of_completed_loan=Sum("completed_loan_count"),
)
# print(loan_aggregated_data, "\n\n")
# Prefetch related objects to avoid N+1 queries
completed_loanqs = ajo_user_loanqs.filter(status=LoanStatus.COMPLETED).select_related(
    "guarantor", "guarantor__borrower_info"
)

completed_loanqs = completed_loanqs.annotate(
    # ajouser details
    ##
    # guarantor_age=F("guarantor__age"),
    guarantor_age=Case(
        When(guarantor__age__exact="", then=Value("Invalid Age")),
        When(guarantor__age__lte=35, then=Value("Young Adult")),
        When(guarantor__age__lte=65, then=Value("Middle-aged")),
        default=Value("Senior"),
        output_field=models.CharField(),  # Assuming borrower_age_category is CharField
    ),
    # borrower_age=F("guarantor__borrower_info__age"),
    borrower_age=Case(
        When(guarantor__borrower_info__age__exact="", then=Value("Invalid Age")),
        When(guarantor__borrower_info__age__lte=35, then=Value("Young Adult")),
        When(guarantor__borrower_info__age__lte=65, then=Value("Middle-aged")),
        default=Value("Senior"),
        output_field=models.CharField(),  # Assuming borrower_age_category is CharField
    ),
    loan_id=F("id"),
    borrower_info_id=F("guarantor__borrower_info_id"),
    daily_income=F("guarantor__borrower_info__daily_income"),
    annual_shop_rent=F("guarantor__borrower_info__annual_shop_rent"),
    weekly_income=F("guarantor__borrower_info__weekly_income"),
)


# Extract the completed loans with the necessary fields, including the alias
completed_loans_value = completed_loanqs.order_by("-id").values(
    # "loan_id",
    "borrower_id",
    # "eligibility_id",
    # "date_disbursed",
    # "repayment_health_score",
    "performance_status",
    # "borrower_phone_number",
    # "guarantor_id",
    "guarantor_age",
    # "borrower_info_id",
    "daily_income",
    "annual_shop_rent",
    "borrower_age",
    "weekly_income",
)[0]
completed_loans_value["libertyloanstaken"] = loan_aggregated_data["libertyloanstaken"]
completed_loans_value["numofrepeatloanamt"] = loan_aggregated_data["numofrepeatloanamt"]
completed_loans_value["LoanRenewalRate"] = renewal_rate
completed_loans_value["ok"] = None
# print(completed_loans_value[0])
## savings transaction ---------------------------------

wallet_types = ["AJO_USER", "AJO_AGENT", "AJO_PERSONAL", "ROSCA_PERSONAL"]

ajouser_txnqs = Transaction.objects.filter(
    onboarded_user=ajouser,
    status=Status.SUCCESS,
    amount__gt=0,
)
## loan repayment transactions
# Borrower Repayments On Liberty

repayment_to_liberty = (
    ajouser_txnqs.filter(transaction_form_type="LOAN_REPAYMENT").aggregate(repaymentstoliberty=Sum("amount"))[
        "repaymentstoliberty"
    ]
    or 0
)
completed_loans_value["repaymentstoliberty"] = repayment_to_liberty


withdrawal_savings = ajouser_txnqs.filter(
    transaction_form_type=TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT,
    plan_type=PlanType.AJO,
    wallet_type__in=wallet_types,
)
savings_withdrawal_result = withdrawal_savings.aggregate(
    avgwithrawalamount=Coalesce(Avg("amount"), Value(0.0)),
    savingswithdrawalcount=Coalesce(Count("id"), 0),
)

# main savings query
savingqs = AjoSaving.objects.filter(ajo_user=ajouser, maturity_date__isnull=False, is_activated=True)

# get loan repayment ids
loan_repayment_ids = savingqs.filter(is_loan_repayment=True).values_list("quotation_id", flat=True)

credit_savingstxn_qs = ajouser_txnqs.filter(
    transaction_type="CREDIT",
    wallet_type=WalletTypes.AJO_USER,
    transaction_form_type__in=["WALLET_DEPOSIT", "WALLET_AUTO_CHARGE_DEPOSIT", "BANK_DEPOSIT"],
).exclude(quotation_id__in=loan_repayment_ids)


not_repayment_savings = savingqs.filter(is_loan_repayment=False)
loan_savingsqs = not_repayment_savings.filter(loan=True)
loan_savings_quoId = loan_savingsqs.values_list("quotation_id", flat=True)

# print(not_repayment_savings.values("maturity_date", "created_at"))
# Aggregate to get avgsavingsduration
savings_dt_summary = not_repayment_savings.aggregate(
    avgsavingsduration=Coalesce(
        Avg(
            ExpressionWrapper(
                ExtractDay(ExpressionWrapper(F("maturity_date") - F("created_at"), output_field=DurationField())),
                output_field=FloatField(),
            )
        ),
        Value(0.0),
    ),
)


loan_savingstxn = credit_savingstxn_qs.filter(quotation_id__in=loan_savings_quoId)
# sum of expected savings amount
total_expected_savings_amt = (
    not_repayment_savings.aggregate(sum_expected_amt=Sum("expected_amount"))["sum_expected_amt"] or 0
)
# over all savings result
overall_savings_result = credit_savingstxn_qs.aggregate(
    total_savings=Coalesce(Sum("amount"), Value(0.0)),
    lowest_savings_amount=Coalesce(Min("amount"), Value(0.0)),
    highest_savings_amount=Coalesce(Max("amount"), Value(0.0)),
    avg_monthlysavings=Coalesce(Avg("amount"), Value(0.0)),
)

try:
    rate = overall_savings_result["total_savings"] / total_expected_savings_amt
    completionrate = round(rate, 2)
except ZeroDivisionError:
    completionrate = 0

overall_savings_result["completionrate"] = completionrate
overall_savings_result["avgsavingsduration"] = savings_dt_summary["avgsavingsduration"]
loan_savings_result = loan_savingstxn.aggregate(
    loansavingsamt=Coalesce(Sum("amount"), Value(0.0)),
    loansavingscount=Coalesce(Count("id"), 0),
)
# merge results
savings_final_result = {
    **overall_savings_result,
    **loan_savings_result,
    **savings_withdrawal_result,
    **savings_dt_summary,
}
#### Credit Bureau checks
crc_qs = CreditBureauMetaData.objects.filter(ajo_user=ajouser, status="SUCCESS").last()
liberty_loan_count = loan_aggregated_data["libertyloanstaken"]
crc_loan_report = crc_qs.retrieve_loan_history(liberty_loan_count)


data = {
    **ajo_user_info,
    **completed_loans_value,
    **savings_final_result,
    **crc_loan_report,
}

pprint(data)
