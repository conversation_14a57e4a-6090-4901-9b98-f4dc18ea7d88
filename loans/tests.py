# # from django.test import TestCase
# from dateutil.relativedelta import relativedelta
# from dateutil.rrule import DAILY, rrule
# # Create your tests here.
# from datetime import datetime


# def due_today():
#     """Returns the due amount as at today"""

#     start_date = datetime(2024, 3, 29).date()

#     today = datetime.now().date() + relativedelta(days=3)
#     print(today)
#     age_of_loan = (today - start_date).days + 1

#     print("Days: ", age_of_loan)
#     if start_date > today:
#         age_of_loan = 0
#     else:

#         holidays = 2

#         weekends = sum(
#             1
#             for dt in rrule(DAILY, dtstart=start_date, until=today)
#             if dt.weekday() in [5, 6]
#         )
#         print("Num of weekends: ", weekends)
#         age_of_loan -= holidays + weekends

#     print("Age of Loan: ", age_of_loan)

#     print("Amount due: ", age_of_loan * 150)


# due_today()

from datetime import datetime, timedelta
from pprint import pprint


class Schedule:
    def __init__(self):
        self.id = 0
        self.due_amount = 0
        self.amount_left = 0
        self.paid_amount = 0
        self.due_date = None
        self.paid_date = None
        self.fully_paid = False
        self.fully_paid_date = None
        self.is_late = False
        self.late_days = 0

    def save(self):
        pprint(self.__dict__)
        print("\n")

    @classmethod
    def create_schedules(cls):
        schedules = []
        for i in range(4):
            schedule = cls()
            schedule.id = i
            schedule.due_date = datetime.now() + timedelta(days=i)
            schedule.due_amount = 1000
            schedules.append(schedule)

        return schedules


schedules = Schedule.create_schedules()


def update_loan_repayment_schedules(amount):
    
    for schedule in schedules:
        if schedule.fully_paid == True:
            continue
        print("\n", amount, "start")
        if amount > schedule.due_amount:

            if schedule.paid_amount == 0:  # nothing has been paid
                schedule.paid_amount = schedule.due_amount
                schedule.paid_date = datetime.now()
                schedule.fully_paid_date = datetime.now()
                schedule.fully_paid = True
                schedule.late_days = (
                    datetime.now().date() - schedule.due_date.date()
                ).days
                schedule.is_late = True if schedule.late_days > 0 else False
                schedule.save()
                amount -= schedule.due_amount

            else:  # part payment
                diff = schedule.due_amount - schedule.paid_amount
                schedule.paid_amount += diff
                schedule.fully_paid_date = datetime.now()
                schedule.fully_paid = True
                schedule.late_days = (
                    datetime.now().date() - schedule.due_date.date()
                ).days
                schedule.is_late = True if schedule.late_days > 0 else False
                schedule.save()
                amount -= diff

        elif amount == schedule.due_amount:
            if schedule.paid_amount == 0:  # nothing has been paid
                schedule.paid_amount = schedule.due_amount
                schedule.paid_date = datetime.now()
                schedule.fully_paid_date = datetime.now()
                schedule.fully_paid = True
                schedule.late_days = (
                    datetime.now().date() - schedule.due_date.date()
                ).days
                schedule.is_late = True if schedule.late_days > 0 else False
                schedule.save()
                amount = 0
            else:  # part payment
                diff = schedule.due_amount - schedule.paid_amount
                schedule.paid_amount += diff
                schedule.fully_paid_date = datetime.now()
                schedule.fully_paid = True
                schedule.late_days = (
                    datetime.now().date() - schedule.due_date.date()
                ).days
                schedule.is_late = True if schedule.late_days > 0 else False
                schedule.save()
                amount -= diff

        else:

            schedule.paid_amount += amount
            schedule.paid_date = datetime.now()
            schedule.save()
            amount = 0

        if schedule.id == schedules[-1].id:
            schedule.paid_amount += amount
            schedule.save()
            amount = 0
        print("end", amount, "\n")
        if amount == 0:
            break


# update_loan_repayment_schedules(1500)
# update_loan_repayment_schedules(1500)
# update_loan_repayment_schedules(500)
update_loan_repayment_schedules(1000)

