import json

from django.core.cache import cache
from django.db.models import F, Sum
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import filters, generics, permissions, status, authentication
from rest_framework.exceptions import NotFound
from rest_framework.generics import GenericAPIView
from rest_framework.response import Response
from rest_framework.views import APIView
from sentry_sdk import capture_exception
from django.utils.timezone import make_aware
from datetime import datetime, timedelta

from accounts.models import ActionPermission, ConstantTable
from accounts.permissions import (
    IpWhiteListPermission,
    IsBlackListed,
    IsLoanAvailable,
    IsStaff,
    IsWhitelistedIP,
)
from accounts.responses import (
    error_response,
    pagination_page_not_found_response,
    serializer_validation_error_response,
    success_response,
    value_error_response,
)
from ajo.model_choices import AccountFormType, AccountProvider, OTPType
from ajo.models import BankAccountDetails
from ajo.payment_actions import (
    move_savings_funds_to_disbursement,
    move_savings_funds_to_escrow_for_ajo_user,
    transfer_savings_to_boosta_commission,
)
from ajo.permissions import (
    CoreRepaymentWebhook,
    CustomAdminPermission,
    HookAuthorizationPermission,
)
from ajo.services import AjoUserWithdrawalAccountService
from ajo.tasks import create_cash_connect_account_handler, create_wema_account_handler
from ajo.third_party import OTP
from cards.utils import get_serializer_key_error
from helper_methods import is_valid_digit_number, normalize_ngn_phone
from loans.direct_debit.direct_debit import CreditWorthinessCalculator
from loans.direct_debit.services import MonoAPI
from loans.enums import LoanStatus
from loans.helpers.agent_leaderboard import (
    get_agent_leaderboard,
    get_staff_agent_leaderboard,
)
from loans.helpers.calculator import DictToObject, parse_to_dict
from loans.helpers.core_banking import CoreBankingManager
from loans.helpers.first_central import RawFirstcentralCreditCheck
from loans.helpers.loan_helpers import FirstcentralCreditCheck
from loans.helpers.loandisk_helper import LoandiskManager
from loans.serializers import *
from loans.tasks import (
    bulk_repayment_handler,
    categorize_missed_loan,
    celery_handle_loandisk_loan_repayment,
    create_borrower_on_loan_disk_branch,
    create_loan_repayment_schedule,
    disburse_group_loan_handler,
    disburse_group_loan_helper,
    disbursement_compliance_checks,
    documentation_compliance_checks,
    group_loan_repayment,
    handle_image_upload_result_update_on_loan_eligiblity_verification,
    localhost_documentation_verification,
    process_loan_documentation,
    release_escrow_balance,
    run_post_loan_to_loan_disk,
    send_mail_to_loan_officer_supervisor_to_approve_loan,
    update_agent_image_on_loan_kyc_doc,
    update_loan_repayment_schedules,
    update_loan_status_on_loan_disk,
)
from payment.model_choices import TransactionSource
from payment.models import Transaction
from savings.pagination import CustomPagination
from savings.settings import REPAYMENT_QUALIFICATION


class GetLoanTIers(APIView):
    permission_classes = (HookAuthorizationPermission,)

    def get(self, request):
        loans_config = ConstantTable.get_constant_table_instance().other_loans_config
        if loans_config.get("investment_tiers"):
            tier_data = loans_config["investment_tiers"]
            for key, value in tier_data.items():
                value["limit"] *= 12
        else:
            tier_data = {}

        response = {"status": "success", "data": tier_data}
        return Response(response, status=status.HTTP_200_OK)


class GetLoanLoanSettingsAPIView(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):
        from cluster.enums import AvailableBankType

        const = ConstantTable.get_constant_table_instance()
        loans_config = const.staff_loans_config
        boosta_2x_config = const.boosta_2x_config
        business_suite_amount = boosta_2x_config.get("business_suite", 150000)
        response = {
            "status": "success",
            "data": {
                "business_suite": float(business_suite_amount),
                "loan_tenures": [
                    {"slug": display, "display": key}
                    for key, display in Tenure.choices
                    if loans_config.get(display)
                ],
                "cluster_banks": [
                    {"slug": key, "display": display}
                    for key, display in AvailableBankType.choices
                ],
            },
        }
        return Response(response, status=status.HTTP_200_OK)


class GetEligibleSaver(generics.ListAPIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    pagination_class = CustomPagination
    serializer_class = GetEligibleUserSerializer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "active",
        "is_collected",
    )
    search_fields = ("ajo_user__phone_number",)

    def get_queryset(self):
        request_user = self.request.user
        eligibility = LoanEligibility.objects.filter(
            agent=request_user, active=True
        ).select_related(
            "ajo_user",
            "saving",
        )
        return eligibility

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        agent = self.request.user
        loan_query_set = AjoLoan.objects.filter(agent=agent)
        loan_metric = AjoLoan.loan_metrics(loan_query_set=loan_query_set, agent=agent)
        const = ConstantTable.get_constant_table_instance()

        response_data = {
            "status": True,
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "loan_metrics": loan_metric,
            "use_guarantor_verification": const.use_guarantor_verification,
            "results": serializer.data,
        }

        return Response(response_data, status=status.HTTP_200_OK)


class SendVerificationOtpView(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)

    def post(self, request):
        serializer = SendVerificationOtpSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        loan_eligibility = validated_data.get("loan_eligibility")
        eligibility = validated_data.get("eligibility")
        ajo_user = loan_eligibility.ajo_user
        # saver_phone_number = ajo_user.phone_number
        saver_phone_number = ajo_user.phone
        create_borrower = create_borrower_on_loan_disk_branch.delay(
            ajo_user_id=ajo_user.id
        )

        if environment != "development":
            sms = OTP.sms_voice_otp(
                otp_type=OTPType.SMS, phone_number=saver_phone_number
            )

        response_data = {
            "status": True,
            "message": "Kindly proceed to number verification.",
            "eligibility": loan_eligibility.id,
            "ussd_code": loan_eligibility.ussd_code,
        }
        return Response(data=response_data, status=status.HTTP_200_OK)


class LoanAmountApplication(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)

    def post(self, request):
        serializer = LoanApplicationSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        loan_amount = validated_data.get("loan_amount")
        eligibility = validated_data.get("eligibility")
        loan_tenure = validated_data.get("loan_tenure")
        repayment_type = validated_data.get("repayment_type")

        const = ConstantTable.get_constant_table_instance()
        use_address_screen = const.use_address_screen

        loan_with_prev_loan_kyc_ref_created = (
            AjoLoan.create_loan_instance_for_repeating_kyc(
                eligibility_instance=eligibility,
                loan_tenure=loan_tenure,
                loan_amount=loan_amount,
                use_address_screen=use_address_screen,
            )
        )
        if isinstance(loan_with_prev_loan_kyc_ref_created, dict):

            ajo_loan = loan_with_prev_loan_kyc_ref_created.get("loan_instance")
            crc_eligible = loan_with_prev_loan_kyc_ref_created.get("crc_eligible")
            reason = loan_with_prev_loan_kyc_ref_created.get("reason")
            if crc_eligible:
                status_code = status.HTTP_202_ACCEPTED
            else:
                data = {"error": reason}
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        else:
            ajo_loan = AjoLoan.create_loan(
                eligibility_instance=eligibility,
                loan_amount=loan_amount,
                loan_tenure=loan_tenure,
                use_address_screen=use_address_screen,
                repayment_type=repayment_type,
            )
            status_code = status.HTTP_201_CREATED

        request_response = AjoLoan.charge_agent(
            loan=ajo_loan, request_data=serializer.data
        )

        default_data = {
            "loan_id": str(ajo_loan.id),
            "saver_phone_number": ajo_loan.saver_phone_number,
            "loan_amount": ajo_loan.amount,
            "processing_fee": ajo_loan.processing_fee,
            "interest": ajo_loan.interest_amount,
            "loan_status": ajo_loan.status,
            "verification_stage": ajo_loan.verification_stage,
            "ussd_code": ajo_loan.ussd_code,
            "loan_tenure": ajo_loan.tenor,
            "verification_stage": ajo_loan.verification_stage,
        }

        if request_response.get("status"):
            response_data = {
                "status": True,
                "message": "Your loan application has been successfully submitted.",
            }
            response_data.update(default_data)
            return Response(data=response_data, status=status_code)
        else:
            response_data = {
                "status": False,
                "message": "Your wallet could not be charged for your loan application.",
            }
            response_data.update(default_data)
            return Response(data=response_data, status=status.HTTP_400_BAD_REQUEST)


class VerifyLoanApplicant(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)

    def post(self, request):
        serializer = VerifyLoanApplicantSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        eligibility_instance = validated_data.get("eligibility_instance")

        eligibility_instance.verified_saver = True
        eligibility_instance.save()

        response_data = {
            "status": True,
            "message": "Phone number verification was successful",
            "eligibility": eligibility_instance.id,
        }
        return Response(data=response_data, status=status.HTTP_202_ACCEPTED)


class BorrowerInfoView(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)

    def post(self, request):
        serializer = BorrowerInfoSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        ajo_loan = validated_data.get("ajo_loan")
        daily_income = validated_data.get("daily_income")
        annual_shop_rent = validated_data.get("annual_shop_rent")
        weekly_income = validated_data.get("weekly_income")
        verification_number = validated_data.get("verification_number")
        verification_type = validated_data.get("verification_type")
        # print(verification_number, "\n\n")
        base_64_img_string = validated_data.get("base_64_img_string")
        bvn_verification = validated_data.get("bvn_verification")
        ajo_user_nin = validated_data.get("ajo_user_nin")
        age = validated_data.get("age")
        last_name = validated_data.get("last_name")
        date_of_birth = validated_data.get("date_of_birth")

        borrower_info_queryset = BorrowerInfo.objects.filter(
            loan=ajo_loan, borrower=ajo_loan.borrower
        )
        # borrower_info_instance = borrower_info_queryset.last()

        if borrower_info_queryset:

            borrower_info_queryset.update(
                loan=ajo_loan,
                daily_income=daily_income,
                annual_shop_rent=annual_shop_rent,
                age=age,
                weekly_income=weekly_income,
                verification_number=verification_number,
                verification_type=verification_type,
                base_64_img_string=base_64_img_string,
                nin=ajo_user_nin,
                you_verify_metadata=bvn_verification,
                verification_ref=f"{last_name}{date_of_birth}",
                date_of_birth=date_of_birth,
            )
            result = borrower_info_queryset.values(
                "id",
                "borrower_id",
                "loan_id",
                "daily_income",
                "annual_shop_rent",
                "weekly_income",
                "session_id",
                "verification_number",
                "nin",
                "age",
                "verification_type",
                "is_verified",
            )[0]

        else:
            borrower_info = BorrowerInfo.objects.create(
                loan=ajo_loan,
                borrower=ajo_loan.borrower,
                daily_income=daily_income,
                annual_shop_rent=annual_shop_rent,
                age=age,
                weekly_income=weekly_income,
                verification_number=verification_number,
                verification_type=verification_type,
                base_64_img_string=base_64_img_string,
                nin=ajo_user_nin,
                you_verify_metadata=bvn_verification,
                verification_ref=f"{last_name}{date_of_birth}",
                date_of_birth=date_of_birth,
            )
            ajo_loan.verification_stage = VerificationStage.FACE_MATCH
            ajo_loan.save()

            result = {
                "id": borrower_info.id,
                "borrower_id": borrower_info.borrower_id,
                "loan_id": borrower_info.loan_id,
                "daily_income": borrower_info.daily_income,
                "annual_shop_rent": borrower_info.annual_shop_rent,
                "weekly_income": borrower_info.weekly_income,
                "session_id": borrower_info.session_id,
                "verification_number": borrower_info.verification_number,
                "nin": borrower_info.nin,
                "verification_type": borrower_info.verification_type,
                "is_verified": borrower_info.is_verified,
            }

        response = {
            "status": True,
            "message": "Kindly proceed to face match",
            "result": result,
        }

        return Response(data=response, status=status.HTTP_200_OK)


class DisburseAjoLoan(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)

    @transaction.atomic
    def post(self, request):
        serializer = LoanDisbursementSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        loan_instance = validated_data.get("loan_instance")
        new_savings = validated_data.get("new_savings")
        savings = validated_data.get("savings")

        if loan_instance.loan_type == LoanType.BNPL:
            credit_ajo_user = move_savings_funds_to_disbursement(
                savings=savings, loan=loan_instance
            )

        else:
            boosta_loans = ["BOOSTA", "BOOSTA_2X", "BOOSTA_2X_MINI", "CREDIT_HEALTH"]
            if loan_instance.loan_type in boosta_loans:
                move_funds = transfer_savings_to_boosta_commission(
                    loan=loan_instance, loan_type=loan_instance.loan_type
                )
            else:
                move_funds = move_savings_funds_to_escrow_for_ajo_user(
                    loan=loan_instance
                )

            if not move_funds:
                response = {
                    "error": "Savings balance not available",
                }
                return Response(data=response, status=status.HTTP_400_BAD_REQUEST)

            credit_ajo_user = AjoLoan.disburse_ajo_loan(
                loan=loan_instance, new_savings=new_savings
            )

        if credit_ajo_user.get("status") is True:
            CreditHealthAgentRequest.update_status_to_loans(
                loan_instance=loan_instance, request_status="ACTIVE"
            )

            response = {
                "status": "Success",
                "message": "Loan successfully disbursed",
                "loan_id": loan_instance.id,
                "amount_disbursed": loan_instance.amount,
                "user_full_name": loan_instance.borrower.fullname,
            }

            try:
                schedule = AjoLoanSchedule.objects.filter(loan=loan_instance)
                if schedule.exists():
                    schedule.delete()
                create_loan_repayment_schedule.delay(loan_instance.id)
            except Exception as e:
                capture_exception(e)
            try:
                send_mail_to_loan_officer_supervisor_to_approve_loan.delay(
                    loan_instance=loan_instance.id
                )
            except Exception as e:
                pass

            try:
                disbursement_compliance_checks.delay(loan_id=loan_instance.id)
            except Exception:
                pass

            return Response(data=response, status=status.HTTP_200_OK)
        else:
            response = {
                "status": "Failed",
                "message": "Please try again after 5 minutes!",
            }
            return Response(data=response, status=status.HTTP_200_OK)


class GuarantorView(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request):
        serializer = GuarantorViewSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        ajo_loan = validated_data.get("ajo_loan")
        surname = validated_data.get("surname")
        last_name = validated_data.get("last_name")
        email = validated_data.get("email")
        preferred_verification = validated_data.get("preferred_verification")
        guarantor_phone_number = validated_data.get("guarantor_phone_number")
        verification_number = validated_data.get("verification_number")
        verification_type = validated_data.get("verification_type")

        base_64_img_string = validated_data.get("base_64_img_string")
        base_64_signature_string = validated_data.get("base_64_signature_string")
        nin_verification = validated_data.get("nin_verification")
        borrower_info_instance = validated_data.get("borrower_info_instance")
        age = validated_data.get("age")
        date_of_birth = validated_data.get("dateOfBirth")
        nin_last_name = validated_data.get("nin_last_name")
        verification_id_name = validated_data.get("verification_id_name")
        guarantor_bvn_nin_phone = validated_data.get("guarantor_bvn_nin_phone")

        loan_guarantor_qs = LoanGuarantor.objects.filter(
            borrower_info=borrower_info_instance
        )

        const = ConstantTable.get_constant_table_instance()

        if (
            const.use_guarantor_verification is False
            and ajo_loan.loan_type == LoanType.BNPL
        ):
            verification_stage = VerificationStage.DISBURSEMENT
            ajo_loan_status = LoanStatus.APPROVED
        else:
            verification_stage = VerificationStage.GUARANTOR_CAPTURE
            ajo_loan_status = LoanStatus.PROCESSING

        if loan_guarantor_qs.exists():
            loan_guarantor_instance = loan_guarantor_qs.last()

            loan_guarantor_instance.surname = surname
            loan_guarantor_instance.last_name = last_name
            loan_guarantor_instance.email = email
            loan_guarantor_instance.phone_number = guarantor_phone_number
            loan_guarantor_instance.verification_number = verification_number
            loan_guarantor_instance.verification_type = verification_type
            loan_guarantor_instance.preferred_verification = preferred_verification
            loan_guarantor_instance.base_64_img_string = base_64_img_string
            loan_guarantor_instance.base_64_signature_string = base_64_signature_string
            loan_guarantor_instance.you_verify_metadata = nin_verification
            loan_guarantor_instance.age = age
            loan_guarantor_instance.verification_ref = f"{nin_last_name}{date_of_birth}"
            loan_guarantor_instance.date_of_birth = date_of_birth
            loan_guarantor_instance.loan_type = ajo_loan.loan_type

            loan_guarantor_instance.save()

            result = loan_guarantor_qs.values(
                "id",
                "borrower",
                "surname",
                "last_name",
                "email",
                "verification_type",
                "age",
                "preferred_verification",
                "phone_number",
                "verification_number",
            )[0]
            result["loan_id"] = ajo_loan.id

        else:
            loan_guarantor_instance = LoanGuarantor.objects.create(
                borrower=ajo_loan.borrower,
                borrower_info=borrower_info_instance,
                surname=surname,
                loan_type=ajo_loan.loan_type,
                last_name=last_name,
                email=email,
                phone_number=guarantor_phone_number,
                verification_number=verification_number,
                verification_type=verification_type,
                preferred_verification=preferred_verification,
                base_64_img_string=base_64_img_string,
                base_64_signature_string=base_64_signature_string,
                you_verify_metadata=nin_verification,
                age=age,
                verification_ref=f"{nin_last_name}{date_of_birth}",
                date_of_birth=date_of_birth,
            )

            result = {
                "loan_id": ajo_loan.id,
                "id": loan_guarantor_instance.id,
                "borrower": loan_guarantor_instance.borrower.id,
                "surname": loan_guarantor_instance.surname,
                "last_name": loan_guarantor_instance.last_name,
                "email": loan_guarantor_instance.email,
                "verification_type": loan_guarantor_instance.verification_type,
                "preferred_verification": loan_guarantor_instance.preferred_verification,
                "guarantor_phone_number": loan_guarantor_instance.phone_number,
                "verification_number": loan_guarantor_instance.verification_number,
            }

        ajo_loan.verification_stage = verification_stage
        ajo_loan.guarantor = loan_guarantor_instance
        ajo_loan.status = ajo_loan_status
        ajo_loan.guarantor_full_name = f"{surname} {last_name}"
        ajo_loan.guarantor_bvn_nin_name = verification_id_name
        ajo_loan.guarantor_bvn_nin_phone = guarantor_bvn_nin_phone
        ajo_loan.guarantor_phone_number = guarantor_phone_number

        ajo_loan.save(
            update_fields=[
                "status",
                "verification_stage",
                "guarantor",
                "guarantor_full_name",
                "guarantor_bvn_nin_name",
                "guarantor_bvn_nin_phone",
                "guarantor_phone_number",
            ]
        )
        if (
            verification_stage == VerificationStage.DISBURSEMENT
            and environment != "development"
        ):
            loan_guarantor_instance.send_verification_update_sms()

        response = {
            "status": True,
            "verification_stage": verification_stage,
            "message": "Kindly proceed to face match",
            "result": result,
        }

        return Response(data=response, status=status.HTTP_200_OK)


class GuarantorFaceMatch(APIView):
    permission_classes = (permissions.IsAuthenticated, IsLoanAvailable)

    def post(self, request):
        serializer = GuarantorFaceMatchSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        loan_guarantor = validated_data.get("loan_guarantor")
        ajo_loan = validated_data.get("ajo_loan")
        snapped_image = validated_data.get("snapped_image")
        ajo_loan.verification_stage = VerificationStage.DISBURSEMENT
        ajo_loan.status = LoanStatus.APPROVED
        ajo_loan.save()
        ajo_loan.update_loan_summary()

        loan_guarantor.is_verified = True
        loan_guarantor.notified = True
        loan_guarantor.snapped_image = snapped_image
        loan_guarantor.save()

        data = {"status": True, "message": "Completed", "loan": ajo_loan.id}

        update_loan_status_on_loan_disk.delay(
            loan_id=ajo_loan.id, loan_status=VerificationStage.GUARANTOR_CAPTURE
        )
        try:
            documentation_compliance_checks.delay(loan_id=ajo_loan.id)
        except Exception:
            pass

        try:
            documentation_compliance_checks.delay(loan_id=ajo_loan.id)
        except Exception:
            pass

        return Response(data=data, status=status.HTTP_200_OK)


class GuarantorDjoahCallBack(APIView):

    def post(self, request, *args, **kwargs):
        data = request.data
        # pprint(data)
        if not request.headers.get("X-Dojah-Signature"):
            return Response(
                {"message": "Callback Failed"}, status=status.HTTP_403_FORBIDDEN
            )

        metadata = data.get("metadata")
        session_id = metadata.get("session_id", None)
        guarantor_phone_number = metadata.get("guarantor_phone_number", None)
        verification_number = metadata.get("verification_number", None)
        reference_id = data.get("referenceId", None)
        verification_status = data.get("verificationStatus")

        verification_result = LoanGuarantor.verify_guarantor_with_dojah_call_back(
            guarantor_phone_number=guarantor_phone_number,
            session_id=session_id,
            verification_number=verification_number,
            verification_status=verification_status,
            reference_id=reference_id,
        )
        # print(verification_result, "\n\n")
        if verification_result is None:
            # print("Borrower info")
            BorrowerInfo.verify_borrower_with_dojah_call_back(
                session_id=session_id,
                verification_number=verification_number,
                verification_status=verification_status,
                reference_id=reference_id,
            )

        return Response({"message": "Callback Successful"}, status=status.HTTP_200_OK)


class LoanHistoryAPIView(generics.ListAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    pagination_class = CustomPagination
    pagination_class.page_size = 50
    serializer_class = LoanHistorySerializer

    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
    )

    filterset_fields = (
        "id",
        "is_disbursed",
        "date_disbursed",
        "borrower__phone_number",
        "status",
        "verification_stage",
        "loan_type",
    )
    search_fields = (
        "borrower__phone_number",
        "verification_stage",
        "loan_type",
        "status",
    )

    def get_queryset(self):
        user = self.request.user
        # user = CustomUser.objects.filter(email="<EMAIL>").last()
        loans = AjoLoan.objects.filter(agent=user)
        return loans


class GetLoanAmountSummaryView(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request):
        serializer = GetLoanAmountSummarySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        loan_amount = serializer.validated_data.get("loan_amount")
        loan_tenure = serializer.validated_data.get("loan_tenure")
        stock_price = serializer.validated_data.get("stock_price")
        savings_created = serializer.validated_data.get("savings_created")
        savings_id = serializer.validated_data.get("savings_id")
        summary = AjoLoan.calculate_interest(
            principal_amount=loan_amount,
            loan_tenure=loan_tenure,
            savings_id=savings_id,
            stock_price=stock_price,
            savings_created=savings_created,
        )
        # print(summary, "\n")
        return Response(data=summary, status=status.HTTP_200_OK)


class BorrowerInfoStatus(generics.ListAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    pagination_class = CustomPagination
    serializer_class = BorrowerInfoSerializer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = ("session_id",)

    # search_fields = ("ajo_user__phone_number",)

    def get_queryset(self):
        request_user = self.request.user
        eligibility = BorrowerInfo.objects.filter(borrower__user=request_user)
        return eligibility


class RepaymentCallBack(APIView):
    permission_classes = (CoreRepaymentWebhook,)

    def post(self, request):
        serializer = RepaymentHookSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        company = validated_data.get("company")
        recipient_account_name = validated_data.get("recipient_account_name")
        recipient_account_number = validated_data.get("recipient_account_number")
        amount = validated_data.get("amount")
        payer_account_name = validated_data.get("payer_account_name")
        payer_account_number = validated_data.get("payer_account_number")
        payer_bank_code = validated_data.get("payer_bank_code")
        paid_at = validated_data.get("paid_at")
        narration = validated_data.get("narration")
        transaction_reference = validated_data.get("reference")
        other_transaction_reference = validated_data.get("transaction_reference")
        session_id = validated_data.get("session_id")
        fee = validated_data.get("fee")
        amount_payable = validated_data.get("amount_payable")
        settlement_status = validated_data.get("settlement_status")
        currency = validated_data.get("currency")

        try:
            get_account = BankAccountDetails.objects.get(
                account_number=recipient_account_number
            )
        except Exception as err:
            return Response("Invalid account", status=status.HTTP_400_BAD_REQUEST)

        ajo_user = get_account.ajo_user

        res = CoreBankingManager.verify_transaction_reference(
            reference=transaction_reference
        )

        if (
            res.get("status") == "success"
            and res.get("data", {}).get("recipient_account_number")
            == recipient_account_number
            and res.get("data", {}).get("amount") == amount
            and res.get("data", {}).get("reference") == transaction_reference
        ):

            # Validate Reference is not repeated in Ajo

            transaction_instance = Transaction.objects.filter(
                unique_reference=transaction_reference
            ).first()

            user = get_account.user
            wallet_type = WalletTypes.AJO_LOAN_REPAYMENT
            transaction_form_type = TransactionFormType.AJO_LOAN_REPAYMENT_TOPUP_DEPOSIT
            transaction_source = TransactionSource.COREBANKING
            description = f"{amount} was received as a deposit from corebanking for ajo loan repayment"

            repayment_record, created = (
                RepaymentVirtuaWalletCreditRecord.objects.get_or_create(
                    agent=user,
                    session_id=session_id,
                    transaction_reference=transaction_reference,
                )
            )

            repayment_record.agent = user
            repayment_record.company = company
            repayment_record.ajo_user = ajo_user
            repayment_record.amount = amount
            repayment_record.account_name = get_account.account_name
            repayment_record.account_number = get_account.account_number
            repayment_record.paid_at = paid_at
            repayment_record.save()

            if transaction_instance is None:
                transaction_instance = TransactionService.create_deposit_transaction(
                    user=user,
                    amount=amount,
                    unique_reference=transaction_reference,
                    wallet_type=wallet_type,
                    transaction_form_type=transaction_form_type,
                    transaction_source=transaction_source,
                    description=description,
                    ajo_user=ajo_user,
                )

            if transaction_instance.status != Status.SUCCESS:
                try:
                    bank_transafer_repayment_response = AjoLoanRepayment.resolve_pending_funding_repaymen_wallet_from_corebanking(
                        transaction_instance=transaction_instance
                    )

                    # POST REPAYMENT RECORD TO LOANDISK
                    loan_id = bank_transafer_repayment_response.get("loan_id")
                    if loan_id != None:
                        celery_handle_loandisk_loan_repayment.delay(
                            ajo_loan_id=loan_id,
                            amount=amount,
                            repayment_type=RepaymentType.TRANSFER,
                        )

                except ValueError as err:
                    return Response(
                        {
                            "status": "failed",
                            "error": "715",
                            "message": str(err),
                        }
                    )

            response = {"status": "success", "message": "Confirmed"}

            return Response(
                data=response,
                status=status.HTTP_200_OK,
            )

        else:
            response = {
                "message": "funding verification data is bad",
                "status": "failed",
            }
            return Response(data=response, status=status.HTTP_400_BAD_REQUEST)


class AjoLoanCalculatorView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = AjoLoanCalculatorSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid(raise_exception=True):
            loan_request_amount = serializer.validated_data.get("loan_request_amount")
            loan_requirements = AjoLoan.calculate_interest(
                principal_amount=loan_request_amount
            )

            constant_table = ConstantTable.get_constant_table_instance()
            ajo_loan_savings_percentage = (
                constant_table.staff_eligibility_percentage / 100
            )

            required_savings_amount = loan_request_amount * ajo_loan_savings_percentage
            processing_fee = loan_requirements.get("loan_processing_fee")

            all_data = dict(
                interest=loan_requirements.get("interest_amount"),
                loan_request_amount=loan_requirements.get("principal_amount"),
                required_savings_amount=required_savings_amount,
                processing_fee=processing_fee,
                initial_deposit=required_savings_amount + processing_fee,
                total_repayment_amount=loan_requirements.get("repayment_amount"),
                daily_repayment_amount=loan_requirements.get("daily_repayment_amount"),
            )

            data = {"status": "Success", "data": all_data}
            return Response(data, status=status.HTTP_200_OK)


class RepaymentWalletDetails(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):
        phone_number = request.query_params.get("phone_number")
        request_user = self.request.user
        ajo_user = AjoUser.objects.filter(phone_number__startswith=phone_number).last()
        const = ConstantTable.get_constant_table_instance()

        bank_info = BankAccountDetails.objects.filter(
            user=request_user,
            ajo_user=ajo_user,
            account_provider=const.loan_collection_channel,  # CASH_CONNECT, WEMA
            form_type=AccountFormType.LOAN_REPAYMENT,
        ).last()

        if not bank_info:
            bank_info = BankAccountDetails.objects.filter(
                user=request_user,
                ajo_user=ajo_user,
                form_type=AccountFormType.LOAN_REPAYMENT,
            ).last()

        if bank_info:
            response = {
                "Account_number": bank_info.account_number,
                "Bank_name": bank_info.bank_name,
                "Account_name": bank_info.account_name,
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"Account_number": None, "Bank_name": None, "Account_name": None}
            return Response(response, status=status.HTTP_200_OK)


class AjoLoanRepaymentAPIView(APIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = RepaymentWalletDetailsSerializer

    def post(self, request):
        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        loan: AjoLoan = validated_data.get("loan")
        amount = validated_data.get("amount")
        agent_wallet = validated_data.get("agent_wallet")

        repayment_status, repayment = AjoLoanRepayment.loan_repayment(
            loan=loan,
            amount=amount,
            from_wallet=agent_wallet,
            repayment_type=RepaymentType.AGENT_DEBIT,
        )

        if repayment_status == True:
            """Post to loan disk when ready"""

            # POST REPAYMENT RECORD TO LOANDISK
            celery_handle_loandisk_loan_repayment.delay(
                ajo_loan_id=loan.id,
                amount=amount,
                repayment_type=RepaymentType.AGENT_DEBIT,
            )

        # release_escrow_balance(phone_number=loan.borrower.phone, amount=amount, agent=loan.agent)

        update_loan_repayment_schedules.delay(loan.id, repayment.id)
        categorize_missed_loan.delay(loan.id)

        response = {"status": "success", "message": "payment successful"}

        return Response(response, status=status.HTTP_200_OK)


class FaceMatch(APIView):
    permission_classes = (permissions.IsAuthenticated, CustomAdminPermission)

    def post(self, request):
        # print(self.request.user.id)
        serializer = FaceMatchSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        borrower_info_instance: BorrowerInfo = validated_data.get(
            "borrower_info_instance"
        )

        loan_instance: AjoLoan = borrower_info_instance.loan
        verification_number = borrower_info_instance.verification_number
        borrower = loan_instance.borrower

        constant = ConstantTable.get_constant_table_instance()
        debt_threshold = constant.debt_threshold
        debt_institution_count = constant.debt_institution_count
        use_warning_screen = constant.use_warning_screen
        # print(use_warning_screen, "\n\n")
        # if user does not have a bvn
        # user ajo user phone number as an alternative
        if borrower_info_instance.verification_type == "NIN":
            verification_number = borrower.phone

        credit_bureau = FirstcentralCreditCheck(
            bvn=verification_number,
            debt_threshold=debt_threshold,
            ajo_user=borrower,
            max_debt_institution_count=debt_institution_count,
        ).get_credit_status()

        borrower_info_instance.credit_bureau = json.dumps(credit_bureau)
        borrower_info_instance.save()

        credit_bureau_status = credit_bureau.get("status")

        borrower_info_id = borrower_info_instance.id
        ajo_user_id = borrower_info_instance.borrower.id

        ## create all virtual wallet
        create_cash_connect_account_handler.apply_async(
            queue="communicationman",
            kwargs={
                "borrower_info_id": borrower_info_id,
            },
        )

        create_wema_account_handler.apply_async(
            queue="communicationman",
            kwargs={
                "ajo_user_id": ajo_user_id,
            },
        )

        if isinstance(credit_bureau_status, bool):
            reason = credit_bureau.get("reason")
            bad_loans_institions_count = credit_bureau.get("bad_loans_institions_count")
            high_outstanding_debt = credit_bureau.get("high_outstanding_debt")
            if credit_bureau_status:
                data = {
                    "status": True,
                    "message": "Completed",
                    "loan": loan_instance.id,
                }

                loan_instance.status = LoanStatus.PROCESSING
                loan_instance.verification_stage = VerificationStage.GUARANTOR
                loan_instance.save()
                borrower_info_instance.is_verified = True
                borrower_info_instance.save()
                update_loan_status_on_loan_disk.delay(
                    loan_id=loan_instance.id, loan_status=VerificationStage.GUARANTOR
                )
            else:
                data = {
                    "status": False,
                    "message": reason,
                    "loan": loan_instance.id,
                }

                if bad_loans_institions_count == 2 and high_outstanding_debt is False:
                    loan_instance.status = LoanStatus.PROCESSING
                    loan_instance.verification_stage = (
                        VerificationStage.CREDIT_BUREAU
                        if use_warning_screen is False
                        else VerificationStage.WARNING
                    )
                    loan_instance.save()
                else:
                    loan_instance.status = LoanStatus.AWAITING_CRC
                    loan_instance.verification_stage = VerificationStage.CREDIT_BUREAU
                    loan_instance.save()

                update_loan_status_on_loan_disk.delay(
                    loan_id=loan_instance.id,
                    loan_status=VerificationStage.CREDIT_BUREAU,
                )
            # update borrower info with first central response
            borrower_info_instance.open_loan_count = credit_bureau.get(
                "count_of_open_loans"
            )
            borrower_info_instance.bad_loan_count = credit_bureau.get(
                "bad_loans_institions_count"
            )
            borrower_info_instance.high_outstanding_debt = credit_bureau.get(
                "high_outstanding_debt"
            )
            borrower_info_instance.save()

            data["data"] = credit_bureau
            return Response(data=data, status=status.HTTP_200_OK)

        else:
            data = {
                "error": "Unable to verify credit worthiness. Please contact support"
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)


class GetSingleMultipleEligibility(APIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = CheckSingleOrMultipleEligibility

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        user: AbstractUser = request.user
        loan_type = serializer.validated_data["loan_type"]
        all_passed_savings = serializer.validated_data.get("savings")

        if loan_type == LoanType.PROSPER_LOAN:
            try:
                get_prosper_agent = ProsperAgent.objects.get(ajo_agent=user)
            except ProsperAgent.DoesNotExist:
                response = {
                    "status": "error",
                    "message": f"Must be {loan_type} agent",
                    "data": None,
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
        else:
            response = {
                "status": "error",
                "message": f"{loan_type} not available to check",
                "data": None,
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        all_eligibility = []

        get_eligibilty = (
            LoanEligibility.check_create_eligible_users_for_ajo_user_propser_loan(
                agent=user
            )
        )

        if len(get_eligibilty) == 1 and get_eligibilty[0].get("savings_id") == None:
            response = {
                "status": True,
                "message": "Eligibility Information",
                "eligibility": None,
                "failure_reason": get_eligibilty[0].get("failure_reason"),
            }
        else:

            for eligibility in get_eligibilty:
                if eligibility["eligibility_data"]:
                    eligibility["eligibility_data"] = MiniLoanEligibilitySerializer(
                        eligibility["eligibility_data"]
                    ).data

                all_eligibility.append(eligibility)

            response = {
                "status": True,
                "message": "Eligibility Information",
                "eligibility": all_eligibility,
            }

        return Response(data=response, status=status.HTTP_200_OK)


class StaffLoanEligibilitySummary(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request):
        serializer = StaffLoanEligibilitySummarySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        email = validated_data.get("email")
        user_id = validated_data.get("user_id")
        saving = validated_data.get("saving")

        data = LoanEligibility.get_eligibility_summary_manually(ajo_savings=saving)
        return Response(data=data, status=status.HTTP_200_OK)


class LoanRepaymentHistory(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):
        loan_id = request.query_params.get("loan_id")
        try:
            ajo_loan = AjoLoan.objects.get(id=loan_id)
        except AjoLoan.DoesNotExist:
            raise serializers.ValidationError(
                "Sorry, no loan record found for the provided details."
            )

        user = request.user
        # user = CustomUser.objects.filter(email="<EMAIL>").last()
        repayments = AjoLoanRepayment.objects.filter(
            agent=user, ajo_loan=ajo_loan, borrower=ajo_loan.borrower
        )

        repayment_list = []
        for repayment in repayments:
            repayment_dict = {
                "amount": repayment.repayment_amount,
                "paid_date": repayment.paid_date,
            }
            repayment_list.append(repayment_dict)

        response_data = {
            "loan_id": loan_id,
            "loan_amount": ajo_loan.amount,
            "total_paid_amount": ajo_loan.total_repayment,
            "expected_repayment_amount": ajo_loan.repayment_amount,
            "frequent_repayment_amount": ajo_loan.daily_repayment_amount,
            "total_repayment_amount": ajo_loan.total_repayment,
            "loan_amount": ajo_loan.amount,
            "loan_tenure": ajo_loan.tenor,
            "maturity_date": (
                ajo_loan.end_date.strftime("%Y-%m-%d") if ajo_loan.end_date else ""
            ),
            "date_disbursed": (
                ajo_loan.date_disbursed.strftime("%Y-%m-%d %I:%M %p")
                if ajo_loan.date_disbursed
                else ""
            ),
            "repayment_count": ajo_loan.tenor_in_days,
            # subject to change when 45days and 60days is active
            "history": repayment_list,
        }

        return Response(data=response_data, status=status.HTTP_200_OK)


class DueLoanRepayment(generics.ListAPIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    pagination_class = CustomPagination
    serializer_class = DueLoanRepaymentSerializer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "loan_type",
        "status",
    )
    search_fields = ("borrower__phone_number",)

    def get_queryset(self):
        agent = self.request.user
        open_loans = AjoLoan.objects.filter(
            agent=agent,
            status=LoanStatus.OPEN,
        ).select_related(
            "borrower",
            "agent",
            "eligibility",
        )
        # eligibility = LoanEligibility.objects.filter(agent=request_user, active=True)
        return open_loans


class GetProsperBalancesAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = ProsperAgentSerializer

    @swagger_auto_schema(
        responses={
            status.HTTP_200_OK: openapi.Schema(type=openapi.TYPE_OBJECT),
            status.HTTP_400_BAD_REQUEST: openapi.Schema(type=openapi.TYPE_OBJECT),
            status.HTTP_401_UNAUTHORIZED: openapi.Schema(type=openapi.TYPE_OBJECT),
        }
    )
    def get(self, request):
        """
        Parameters:
        - request: The HTTP request.
        Returns:
        - Response: HTTP response with details of the prosper agent if any.
        """

        user = self.request.user

        response = {
            "status": "success",
            "message": "request successful",
        }
        try:
            prosper_agent = ProsperAgent.objects.get(ajo_agent=user, active=True)
            prosper_agent.handle_calculated_data()

            serializer = self.serializer_class(prosper_agent)
            response["prosper_agent"] = serializer.data

        except ProsperAgent.DoesNotExist:
            response["prosper_agent"] = {}

        return Response(response, status=status.HTTP_200_OK)


class BorrowerCreditBureauWorthinessCheckApiView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = CreditBureauCheckSerializer

    @swagger_auto_schema(
        responses={
            status.HTTP_200_OK: openapi.Schema(type=openapi.TYPE_OBJECT),
            status.HTTP_400_BAD_REQUEST: openapi.Schema(type=openapi.TYPE_OBJECT),
            status.HTTP_401_UNAUTHORIZED: openapi.Schema(type=openapi.TYPE_OBJECT),
        }
    )
    def post(self, request):
        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        bvn = validated_data.get("bvn")
        # phone_number = validated_data.get("phone_number")
        ajo_user = validated_data.get("ajo_user")
        charge_amount = validated_data.get("charge_amount")
        agent_wallet = validated_data.get("agent_wallet")

        result = (
            BorrowerCreditBureauWorthiness.borrower_credit_bureau_worthiness_request(
                bvn=bvn,
                ajo_user=ajo_user,
                charge_amount=charge_amount,
                agent_wallet=agent_wallet,
            )
        )

        result_status = result.get("status")
        if isinstance(result_status, bool):
            return Response(data=result, status=status.HTTP_200_OK)
        else:
            data = {
                "error": "Unable to verify credit worthiness. Please contact support"
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)


class UpdateBadLoanToGuarantor(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request):
        serializer = UpdateBadLoanToGuarantorSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        loan_instance: AjoLoan = validated_data.get("loan_instance")
        proceed = validated_data.get("proceed")
        loan_id = validated_data.get("loan_id")
        if proceed:
            loan_instance.verification_stage = VerificationStage.GUARANTOR
            loan_instance.save()
            _update_status = True
            message = "You can now proceed to guarantor verification stage"
        else:
            loan_instance.status = LoanStatus.DENIED
            loan_instance.save()
            message = "Loan Denied."
            _update_status = False
        response_data = {
            "status": _update_status,
            "message": message,
            "loan_id": loan_id,
        }
        return Response(data=response_data, status=status.HTTP_200_OK)


class GetSavingsEligibility(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):

        saving_id = request.query_params.get("saving_id")
        start_date = request.query_params.get("start_date", None)
        end_date = request.query_params.get("end_date", None)
        boosta_savings = ["BOOSTA", "BOOSTA_2X", "BOOSTA_2X_MINI"]
        try:
            saving = AjoSaving.objects.get(id=saving_id)
        except AjoSaving.DoesNotExist:
            result = {"status": False, "message": f"invalid savings id {saving_id}"}
            return Response(data=result, status=status.HTTP_400_BAD_REQUEST)

        if saving.savings_type == SavingsType.BNPL:
            data = LoanEligibility.bnpl_eligibility(savings_id=saving.id)

        elif saving.savings_type in boosta_savings:
            data = LoanEligibility.boosta_eligibility(savings_id=saving.id)
        else:
            data = LoanEligibility.get_staff_agent_loan_eligibility_5x_rule(
                savings=saving, start_date=start_date, end_date=end_date
            )
        response_data = {
            "status": True,
            "data": data,
        }

        return Response(data=response_data, status=status.HTTP_200_OK)


class DueLoanCheckerView(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request):

        user = request.user
        const = ConstantTable.get_constant_table_instance()
        today = timezone.now().date()

        # Get the total amount paid for all open loans
        loans: QuerySet[AjoLoan] = user.ajoloans_set.filter(status=LoanStatus.OPEN)
        loans = loans.select_related(
            "agent",
            "borrower",
            "eligibility",
            "guarantor",
            "repayment_savings",
            "checked_by",
        )

        agent_buffer_instance = PastMaturityBuffer.objects.filter(agent=user).last()
        agent_buffer_amount = (
            agent_buffer_instance and agent_buffer_instance.agent_buffer_amount or 0.0
        )

        repayments = AjoLoanRepayment.objects.filter(
            ajo_loan__status="OPEN", agent=user
        )
        total_paid = (
            repayments.aggregate(total_paid=Sum("repayment_amount"))["total_paid"] or 0
        ) + agent_buffer_amount

        # Get the total due amount till yesterday for all open loans
        total_due_yesterday = sum(
            loan.due_yesterday for loan in loans if loan.due_yesterday > 0
        )
        owing_loans = []
        average_days = None
        total_past_maturity_amount = None
        average_past_maturity_days = None
        reason = None
        balances = []
        repayments = []
        dues = []
        loans_past_maturity = []
        exclusion_balance = []

        for loan in loans:
            repayment_total = (
                AjoLoanRepayment.objects.filter(ajo_loan=loan).aggregate(
                    total_repayments=Sum("repayment_amount")
                )["total_repayments"]
                or 0
            )
            if loan.due_yesterday > repayment_total:
                balance = loan.due_yesterday - repayment_total

                if const.use_exclusion:
                    paid_plus_escrow = repayment_total + loan.escrow_amount
                    payment_ratio = (paid_plus_escrow / loan.repayment_amount) * 100
                    if payment_ratio >= const.exclusion_percent:
                        exclusion_balance.append(balance)

                if loan.performance_status == LoanBehavior.PAST_MATURITY:

                    if agent_buffer_amount >= balance:
                        agent_buffer_amount -= balance
                    else:
                        loans_past_maturity.append(loan)
                        owing_loans.append(loan)
                        balances.append(balance)
                        repayments.append(repayment_total)
                        dues.append(loan.due_yesterday)
                else:
                    owing_loans.append(loan)
                    balances.append(balance)
                    repayments.append(repayment_total)
                    dues.append(loan.due_yesterday)

        if total_due_yesterday == 0:
            assisted_percentage = 100
            normal_percentage = 100
            percentage = 100
        elif total_paid == 0:
            assisted_percentage = 0
            normal_percentage = 0
            percentage = 0
        else:
            due_yesterday_excluded = total_due_yesterday - sum(exclusion_balance)
            normal_percentage = abs((total_paid / due_yesterday_excluded) * 100)
            assisted_percentage = abs(
                ((total_paid + sum(exclusion_balance)) / due_yesterday_excluded) * 100
            )
        percentage = (
            assisted_percentage if const.use_exclusion_assistance else normal_percentage
        )

        if sum(dues) == 0:
            normal_repay_percent = 100
            assisted_repay_percent = 100
            repay_percent = 100
        else:
            excluded_result = sum(dues) - sum(exclusion_balance)
            normal_repay_percent = (sum(repayments) / excluded_result) * 100
            assisted_repay_percent = (
                (sum(repayments) + sum(exclusion_balance)) / sum(dues) * 100
            )
        repay_percent = (
            assisted_repay_percent
            if const.use_exclusion_assistance
            else normal_repay_percent
        )

        if percentage < const.get_agent_teir_percent(len(loans)):
            can_give_loan = False
            reason = "Failed overall collection percentage check"
        else:
            if repay_percent < const.percent_checker:
                can_give_loan = False
                reason = "Failed individual loan repayment percentage check"
            else:

                if len(loans_past_maturity) > 0:
                    # Calculate the average amount of these loans
                    total_past_maturity_amount = sum(
                        loan.due_yesterday for loan in loans_past_maturity
                    )

                    # Calculate the average outstanding days of these loans
                    average_past_maturity_days = sum(
                        loan.outstanding_days for loan in loans_past_maturity
                    ) / len(loans_past_maturity)

                    if (
                        total_past_maturity_amount
                        > const.past_maturity_amount_threshold
                        or average_past_maturity_days
                        > const.past_maturity_days_threshold
                    ):
                        can_give_loan = False
                        reason = "Failed past maturity loan check"
                    else:
                        can_give_loan = True
                else:
                    can_give_loan = True

        # Combine sets to ensure all entries are unique
        sorted_combined_loans = sorted(
            owing_loans, key=lambda loan: loan.created_at, reverse=True
        )
        disbursed_amount = (
            AjoLoan.objects.filter(
                is_disbursed=True,
                agent=user,
                date_disbursed__date__month=(timezone.now().month - 1),
            ).aggregate(Sum("amount"))["amount__sum"]
            or 0.0
        )
        commission_amount = disbursed_amount * (0.5 / 100)

        data = {
            "message": "success",
            "percent": percentage,
            "amount_due": total_due_yesterday,
            "total_paid": total_paid,
            "can_give_loan": can_give_loan,
            "show_warning": True if percentage < const.preliminary_checker else False,
            "loans": LoanHistorySerializer(sorted_combined_loans, many=True).data,
            "possible_winning": commission_amount,
            "missing_count": len(sorted_combined_loans),
            "total_missing_loans": sum(balances),
            "total_past_maturity_amount": total_past_maturity_amount,
            "average_past_maturity_days": average_past_maturity_days,
            "block_reason": reason,
            "due_to_repayment_ratio": repay_percent,
            "sum_individual_due": sum(dues),
            "sum_individual_repayment": sum(repayments),
            "total_past_maturity_amount": total_past_maturity_amount,
        }

        log_entry = LoanAnalysisLog.objects.filter(
            agent=user, created_at__date=today
        ).first()

        if data["amount_due"] == 0 and data["total_paid"] == 0:
            pass
        else:
            if log_entry:
                log_entry.message = data["message"]
                log_entry.prev_percent = log_entry.percent
                log_entry.percent = normal_percentage
                log_entry.overall_assist = assisted_percentage
                log_entry.amount_due = data["amount_due"]
                log_entry.total_paid = data["total_paid"]
                log_entry.missed_amount = sum(balances)
                log_entry.can_give_loan = data["can_give_loan"]
                log_entry.average_outstanding_days = average_days
                log_entry.total_past_maturity_amount = total_past_maturity_amount
                log_entry.average_past_maturity_days = average_past_maturity_days
                log_entry.defaulting_loans.clear()
                log_entry.defaulting_loans.add(*sorted_combined_loans)
                log_entry.block_reason = reason
                log_entry.prev_due_to_repayment_ratio = log_entry.due_to_repayment_ratio
                log_entry.due_to_repayment_ratio = normal_repay_percent
                log_entry.individual_assist = assisted_repay_percent
                if can_give_loan:
                    log_entry.positive_count = F("positive_count") + 1
                else:
                    log_entry.negative_count = F("negative_count") + 1
                log_entry.save()
            else:
                # Create a new log entry
                log_entry = LoanAnalysisLog.objects.create(
                    message=data["message"],
                    percent=normal_percentage,
                    overall_assist=assisted_percentage,
                    amount_due=data["amount_due"],
                    total_paid=data["total_paid"],
                    missed_amount=sum(balances),
                    can_give_loan=data["can_give_loan"],
                    agent=request.user,
                    average_outstanding_days=average_days,
                    block_reason=reason,
                    due_to_repayment_ratio=normal_repay_percent,
                    individual_assist=assisted_repay_percent,
                )
                log_entry.defaulting_loans.add(*sorted_combined_loans)
                if can_give_loan:
                    log_entry.positive_count = 1
                else:
                    log_entry.negative_count = 1
                log_entry.save()

        try:
            disbursement_compliance_checks.apply_async(
                queue="compliance",
                kwargs={
                    "agent_id": user.id,
                },
            )
        except Exception:
            pass
        return Response(data=data, status=status.HTTP_200_OK)


# class DueLoanCheckerView(APIView):
#     permission_classes = (permissions.IsAuthenticated,)

#     def post(self, request):

#         user = request.user
#         const = ConstantTable.get_constant_table_instance()
#         today = timezone.now().date()

#         # Get the total amount paid for all open loans
#         loans: QuerySet[AjoLoan] = user.ajoloans_set.filter(status=LoanStatus.OPEN)
#         loans = loans.select_related(
#             "agent",
#             "borrower",
#             "eligibility",
#             "guarantor",
#             "repayment_savings",
#             "checked_by",
#         )

#         repayments = AjoLoanRepayment.objects.filter(
#             ajo_loan__status="OPEN", agent=user
#         )
#         total_paid = (
#             repayments.aggregate(total_paid=Sum("repayment_amount"))["total_paid"] or 0
#         )

#         # Get the total due amount till yesterday for all open loans
#         total_due_yesterday = sum(
#             loan.due_yesterday for loan in loans if loan.due_yesterday > 0
#         )
#         sorted_combined_loans = set([loan for loan in loans if loan.due_yesterday > 0])
#         high_outstanding_days_loan = set()
#         average_days = None
#         total_past_maturity_amount = None
#         average_past_maturity_days = None
#         loans_past_maturity=[]
#         if total_due_yesterday == 0:
#             percentage = 100
#         elif total_paid == 0:
#             percentage = 0
#         else:
#             percentage = abs((total_paid / total_due_yesterday) * 100)

#         if percentage < const.get_agent_teir_percent(len(loans)):
#             can_give_loan = False
#         else:
#             # get loans with outstanding days > 3
#             loans_with_days = [loan for loan in loans if loan.outstanding_days > 3]

#             # Calculate the average of these outstanding days
#             if len(loans_with_days) > 0:
#                 average_days = sum(
#                     [loan.outstanding_days for loan in loans_with_days]
#                 ) / len(loans_with_days)
#                 # Check if the average is exactly 5
#                 if average_days >= const.outstanding_days_threshold:
#                     # Further filter loans with outstanding days > 5
#                     high_outstanding_days_loan = set(
#                         [
#                             loan
#                             for loan in loans_with_days
#                             if loan.outstanding_days >= const.outstanding_days_threshold
#                         ]
#                     )
#                     can_give_loan = False
#                 else:
#                     if const.use_threshold_constant_for_loan_checker:
#                         if any(
#                             loan.outstanding_days >= const.loan_with_days_threshold
#                             for loan in loans
#                         ):
#                             high_outstanding_days_loan = set(
#                                 [
#                                     loan
#                                     for loan in loans_with_days
#                                     if loan.outstanding_days
#                                     >= const.loan_with_days_threshold
#                                 ]
#                             )
#                             can_give_loan = False
#                         else:

#                             # Check if the person has loans past maturity
#                             loans_past_maturity = [
#                                 loan
#                                 for loan in loans
#                                 if loan.end_date < datetime.now().date()
#                             ]

#                             if loans_past_maturity:
#                                 # Calculate the average amount of these loans
#                                 total_past_maturity_amount = sum(
#                                     loan.outstanding_due for loan in loans_past_maturity
#                                 )

#                                 # Calculate the average outstanding days of these loans
#                                 average_past_maturity_days = sum(
#                                     loan.outstanding_days
#                                     for loan in loans_past_maturity
#                                 ) / len(loans_past_maturity)

#                                 # Determine if the person can give a loan based on the averages
#                                 if (
#                                     total_past_maturity_amount
#                                     > const.past_maturity_amount_threshold
#                                     or average_past_maturity_days
#                                     > const.past_maturity_days_threshold
#                                 ):
#                                     can_give_loan = False
#                                 else:
#                                     can_give_loan = True
#                             else:
#                                 can_give_loan = True
#                     else:
#                         # Check if the person has loans past maturity
#                         loans_past_maturity = [
#                             loan
#                             for loan in loans
#                             if loan.performance_status == LoanBehavior.PAST_MATURITY
#                         ]
#                         if loans_past_maturity:
#                             # Calculate the average amount of these loans
#                             total_past_maturity_amount = sum(
#                                 loan.outstanding_due for loan in loans_past_maturity
#                             )

#                             # Calculate the average outstanding days of these loans
#                             average_past_maturity_days = sum(
#                                 loan.outstanding_days for loan in loans_past_maturity
#                             ) / len(loans_past_maturity)

#                             # Determine if the person can give a loan based on the averages
#                             if (
#                                 total_past_maturity_amount
#                                 > const.past_maturity_amount_threshold
#                                 or average_past_maturity_days
#                                 > const.past_maturity_days_threshold
#                             ):
#                                 can_give_loan = False
#                             else:
#                                 can_give_loan = True
#                         else:
#                             can_give_loan = True
#             else:
#                 can_give_loan = True

#         # Combine sets to ensure all entries are unique
#         combined_loans = list(owing_loans.union(high_outstanding_days_loan))
#         combined_loans.extend(loans_past_maturity)
#         sorted_combined_loans = sorted(
#             combined_loans, key=lambda loan: loan.created_at, reverse=True
#         )
#         disbursed_amount = (
#             AjoLoan.objects.filter(
#                 is_disbursed=True,
#                 agent=user,
#                 date_disbursed__date__month=(timezone.now().month - 1),
#             ).aggregate(models.Sum("amount"))["amount__sum"]
#             or 0.0
#         )
#         commission_amount = disbursed_amount * (0.5 / 100)

#         data = {
#             "message": "success",
#             "percent": percentage,
#             "amount_due": total_due_yesterday,
#             "total_paid": total_paid,
#             "can_give_loan": can_give_loan,
#             "show_warning": True if percentage < const.preliminary_checker else False,
#             "loans": LoanHistorySerializer(sorted_combined_loans, many=True).data,
#             "possible_winning": commission_amount,
#             "missing_count": len(combined_loans),
#             "total_missing_loans": total_due_yesterday - total_paid,
#             "total_past_maturity_amount": total_past_maturity_amount,
#             "average_past_maturity_days": average_past_maturity_days
#         }

#         log_entry = LoanAnalysisLog.objects.filter(
#             agent=user, created_at__date=today
#         ).first()

#         if data["amount_due"] == 0 and data["total_paid"] == 0:
#             pass
#         else:
#             if log_entry:
#                 log_entry.message = data["message"]
#                 log_entry.percent = data["percent"]
#                 log_entry.amount_due = data["amount_due"]
#                 log_entry.total_paid = data["total_paid"]
#                 log_entry.missed_amount = data["amount_due"] - data["total_paid"]
#                 log_entry.can_give_loan = data["can_give_loan"]
#                 log_entry.average_outstanding_days = average_days
#                 log_entry.total_past_maturity_amount = total_past_maturity_amount
#                 log_entry.average_past_maturity_days = average_past_maturity_days
#                 log_entry.defaulting_loans.clear()
#                 log_entry.defaulting_loans.add(*sorted_combined_loans)
#                 if can_give_loan:
#                     log_entry.positive_count = F("positive_count") + 1
#                 else:
#                     log_entry.negative_count = F("negative_count") + 1
#                 log_entry.save()
#             else:
#                 # Create a new log entry
#                 log_entry = LoanAnalysisLog.objects.create(
#                     message=data["message"],
#                     percent=data["percent"],
#                     amount_due=data["amount_due"],
#                     total_paid=data["total_paid"],
#                     missed_amount=data["amount_due"] - data["total_paid"],
#                     can_give_loan=data["can_give_loan"],
#                     agent=request.user,
#                     average_outstanding_days=average_days,
#                 )
#                 log_entry.defaulting_loans.add(*sorted_combined_loans)
#                 if can_give_loan:
#                     log_entry.positive_count = 1
#                 else:
#                     log_entry.negative_count = 1
#                 log_entry.save()

#         try:
#             disbursement_compliance_checks.delay(agent_id=user.id)
#         except Exception:
#             pass

#         return Response(data=data, status=status.HTTP_200_OK)


class SendOtpLoanKYCDocumentation(APIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = SendOtpLoanKYCDocumentationSerializer

    def post(self, request):
        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        savings_instance = validated_data.get("savings_instance")
        ajo_user = savings_instance.ajo_user
        # saver_phone_number = ajo_user.phone_number
        saver_phone_number = ajo_user.phone

        if environment != "development":
            sms = OTP.sms_voice_otp(
                otp_type=OTPType.SMS, phone_number=saver_phone_number
            )

        response_data = {
            "status": True,
            "message": "Sent",
            "otp_request": "*347*180*22#",
        }
        return Response(data=response_data, status=status.HTTP_200_OK)


class VerifyLoanKYCOTPView(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request):
        serializer = VerifyLoanKYCDocumentationSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        savings_id = validated_data.get("savings_id")
        # savings_instance = validated_data.get("savings_instance")
        cache.set(key=f"savings{savings_id}loanKYC", value=True, timeout=60 * 30)
        response_data = {
            "status": True,
            "verified": True,
            "message": "Loan Kyc verification was successful",
        }
        return Response(data=response_data, status=status.HTTP_202_ACCEPTED)


class CreateLoanKYCDocumentation(APIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = LoanKYCDocumentationSerializer

    def post(self, request):
        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        borrower = validated_data.get("borrower")
        savings = validated_data.get("savings")

        # Fetch the latest KYC documentation for the borrower
        prev_kyc = LoanKYCDocumentation.objects.filter(borrower=borrower).last()

        # Check if a previous KYC exists
        if prev_kyc:
            # Define the criteria for creating repeat documentation
            criteria = (
                prev_kyc.has_loan,
                not prev_kyc.repeat,
                prev_kyc.documentation_status == DocumentationStatus.COMPLETED,
                prev_kyc.days_since_created <= 50,
            )
            # If all criteria are met, create repeat documentation

            if all(criteria):
                kyc_instance = LoanKYCDocumentation.create_repeat_documentation(
                    savings=savings, doc_instance=prev_kyc
                )
                data = {"savings": savings.id, "unique_ref": kyc_instance.unique_ref}
                response = {
                    "status": True,
                    "message": "Saver has valid Documentation. Kindly proceed to add a new guarantor.",
                    "data": data,
                }
                return Response(data=response, status=status.HTTP_202_ACCEPTED)

        override_validated_data = {
            key: value for key, value in validated_data.items() if value
        }

        instance, created = LoanKYCDocumentation.objects.update_or_create(
            savings=savings, defaults=override_validated_data
        )
        if created:
            # update agent image
            update_agent_image_on_loan_kyc_doc.apply_async(
                queue="documentation",
                args=[instance.id],
            )
            instance.verify_phone_number = True
            instance.save()

        data = {"savings": savings.id, "unique_ref": instance.unique_ref}

        request_reponse = {"status": True, "data": data}
        return Response(data=request_reponse, status=status.HTTP_200_OK)


class ProcessLoanKYCDocumentation(APIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = ProcessLoanKYCDocumentationSerializer

    def post(self, request):
        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        model_instance: LoanKYCDocumentation = validated_data.get("model_instance")
        loan_amount = validated_data.get("principal_amount")
        loan_tenure = validated_data.get("loan_tenure")
        loan_processing_fee = validated_data.get("loan_processing_fee")
        data = serializer.data

        del data["transaction_pin"]

        # proceed to documentation
        charge_loan_fee = LoanKYCDocumentation.charge_processing_fee(
            model_instance=model_instance, processing_fee=loan_processing_fee
        )
        if charge_loan_fee:
            model_instance.loan_amount = loan_amount
            model_instance.loan_tenure = loan_tenure
            model_instance.paid_processing_fee = True
            model_instance.processing_fee = loan_processing_fee
            model_instance.save()
            # update state to processing
            model_instance.update_all_state_to_processing

            localhost_documentation_verification.apply_async(
                queue="documentation",
                args=[model_instance.id],
            )

        request_reponse = {"status": True, "data": data}

        return Response(data=request_reponse, status=status.HTTP_200_OK)


class ReProcessLoanKYCDocumentation(APIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = LoanKYCDocumentationSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        possible_loan_stages = [
            VerificationStage.BORROWER_INFO,
            VerificationStage.FACE_MATCH,
            VerificationStage.GUARANTOR,
            VerificationStage.GUARANTOR_CAPTURE,
        ]
        stage: VerificationStage = kwargs.get("stage")
        if stage not in possible_loan_stages:
            return Response(
                data={"error": f"in valid stage: {stage}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        borrower = validated_data.get("borrower")
        savings = validated_data.get("savings")

        override_validated_data = {
            key: value for key, value in validated_data.items() if value
        }

        instance, created = LoanKYCDocumentation.objects.update_or_create(
            savings=savings, defaults=override_validated_data
        )
        LoanKYCDocumentation.process_by_stages(instance=instance, stage=stage)
        result = {"status": True, "data": {"doc_unique_ref": instance.unique_ref}}

        return Response(data=result, status=status.HTTP_200_OK)


class GetLoanKYCDocumentation(generics.ListAPIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    pagination_class = CustomPagination
    serializer_class = ListLoanKYCDocumentationSerializer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = ("documentation_status", "savings_id")
    search_fields = ("borrower__phone_number", "savings_id")

    def get_queryset(self):
        collector = self.request.user
        eligibility = LoanKYCDocumentation.objects.filter(
            borrower__user=collector, savings__isnull=False
        ).order_by("-updated_at")
        return eligibility


class CreateLoanForExistingKYCDocumentation(APIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = CreateLoanForExistingKYCDocumentationSerializer

    def post(self, request):
        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        documentation_instance: LoanKYCDocumentation = validated_data.get(
            "doc_instance"
        )
        eligibility_instance = validated_data.get("eligibility_instance")
        amount_to_disburse = validated_data.get("amount_to_disburse")

        data = serializer.data
        # proceed to documentation
        loan_instance = LoanKYCDocumentation.create_loan_instance(
            kyc_instance=documentation_instance,
            eligibility_instance=eligibility_instance,
            amount_to_disburse=amount_to_disburse,
        )

        data["verification_stage"] = loan_instance.verification_stage
        request_reponse = {"status": True, "data": data}
        return Response(data=request_reponse, status=status.HTTP_200_OK)


class UpdateDocumentationkycWebHook(APIView):
    # permission_classes = (permissions.IsAuthenticated,)
    # serializer_class =

    def post(self, request):

        data = request.data
        request_ref = data.get("request_ref")
        # print(request_ref, "\n\n")
        try:
            documentation_instance = LoanKYCDocumentation.objects.get(
                unique_ref=request_ref
            )
        except LoanKYCDocumentation.DoesNotExist as err:
            response_data = {"status": False, "message": str(err)}
            return Response(data=response_data, status=status.HTTP_406_NOT_ACCEPTABLE)
        # LoanKYCDocumentation.update_verification_status(
        #     documentation_instance=documentation_instance, verification_result=data
        # )
        data = {"status": True, "message": "kyc documentation updated successfully"}
        return Response(data=data, status=status.HTTP_200_OK)


class GetLoanStatus(APIView):
    # permission_classes = (permissions.IsAuthenticated,)
    # serializer_class =

    def get(self, request):

        verification_id = request.query_params.get("verification_id")

        if not verification_id:
            result = {"error": "verification_id is required"}
            return Response(data=result, status=status.HTTP_400_BAD_REQUEST)

        guarantor = LoanGuarantor.objects.filter(
            verification_number=verification_id
        ).last()

        active_loan_status = [
            "OPEN",
            "IN_PROGRESS",
            "DEFAULTED",
            "PROCESSING",
            "APPROVED",
        ]

        guarantor_to_loan = AjoLoan.objects.filter(
            status__in=active_loan_status,
            guarantor=guarantor,
        )

        borrower_to_loan = BorrowerInfo.objects.filter(
            verification_number=verification_id,
            loan__status__in=active_loan_status,
        )

        guarantor_exist = guarantor_to_loan.exists()
        borrower_exist = borrower_to_loan.exists()

        if guarantor_exist or borrower_exist:
            loan_status = (
                guarantor_to_loan.last().status
                if guarantor_exist
                else borrower_to_loan.last().loan.status
            )
            active_loan = True
        else:
            active_loan = False
            loan_status = None

        data = {
            "status": True,
            "loan_status": loan_status,
            "active_loan": active_loan,
        }

        return Response(data=data, status=status.HTTP_200_OK)


class GetAgentLeaderBoard(GenericAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    pagination_class = CustomPagination

    @method_decorator(cache_page(60))
    def get(self, request, *args, **kwargs):
        date_filter: str | None = request.query_params.get("date_filter")
        branch_filter: str | None = request.query_params.get("branch_filter")
        get_leaderboard = get_agent_leaderboard(
            date_type=date_filter, branch_filter=branch_filter
        )
        try:
            paginated_leaderboard = self.paginate_queryset(get_leaderboard)
        except NotFound:
            return pagination_page_not_found_response()

        return self.get_paginated_response(paginated_leaderboard)


class GetStaffAgentLeaderBoard(GenericAPIView):
    permission_classes = (permissions.IsAuthenticated,)

    @method_decorator(cache_page(60))
    def get(self, request, *args, **kwargs):
        date_filter: str | None = request.query_params.get("date_filter")
        branch_filter: str | None = request.query_params.get("branch_filter")
        get_leaderboard = get_staff_agent_leaderboard(
            date_type=date_filter, branch_filter=branch_filter, email=request.user.email
        )
        return Response(data=get_leaderboard, status=status.HTTP_200_OK)


class LoanSendOtpPhoneNumberView(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed)

    def post(self, request):
        serializer = LoanSendOtpPhoneNumberSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        mobile_number = validated_data.get("mobile_number")

        if environment != "development":
            sms = OTP.sms_voice_otp(otp_type=OTPType.SMS, phone_number=mobile_number)
            # print(sms, "\n\n")

        response_data = {
            "status": True,
            "message": "Sent",
            "mobile_number": mobile_number,
            "otp_request": "*347*180*22#",
        }
        return Response(data=response_data, status=status.HTTP_200_OK)


class LoanFeedbackApi(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed)

    def post(self, request):
        serializer = LoanFeedbackSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        loan = validated_data.get("loan")
        feedback = validated_data.get("feedback")
        loan_stage = validated_data.get("loan_stage")
        comment = validated_data.get("comment")

        feedback_ins = LoanFeedback.rate_loan_process(
            loan=loan, loan_stage=loan_stage, comment=comment, feedback=feedback
        )

        response_data = {
            "status": True,
            "message": "success",
            "id": feedback_ins.id,
            "feedback": feedback,
        }
        return Response(data=response_data, status=status.HTTP_200_OK)


class VerifyGuarantorPhoneOtpView(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed)

    def post(self, request):
        serializer = LoanVerifyOtpSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        mobile_number = validated_data.get("mobile_number")

        response_data = {
            "status": True,
            "message": "Success",
            "mobile_number": mobile_number,
        }
        return Response(data=response_data, status=status.HTTP_202_ACCEPTED)


class IdCheckView(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed)

    def get(self, request):
        id = request.GET.get("id")
        idType = request.GET.get("idType")
        id_types = ["NIN", "BVN"]
        valid_digit = is_valid_digit_number(input_string=id, num_digits=11)
        response_body = {
            "status": False,
        }
        if idType not in id_types or not valid_digit:
            response_body = {
                "status": False,
                "message": f"Invalid request. IDType: {idType}, ID: {id}",
                "data": None,
            }

            return Response(data=response_body, status=status.HTTP_400_BAD_REQUEST)
        else:
            id_instance = YouVerifyRequest.get_create_identity_result(
                id=id, id_type=idType, source=IdentityRequestSource.EXTERNAL
            )
            data = YouVerifyRequest.objects.filter(id=id_instance.id).values()[0]
            response_body = {
                "status": True,
                "message": "success",
                "data": data,
            }

            return Response(data=response_body, status=status.HTTP_200_OK)


class ReusableIdCheckView(APIView):
    # permission_classes = [IpWhiteListPermission]

    def get(self, request):
        id = request.GET.get("id")
        idType = request.GET.get("idType")
        id_types = ["NIN", "BVN"]
        valid_digit = is_valid_digit_number(input_string=id, num_digits=11)
        response_body = {
            "status": False,
        }
        if idType not in id_types or not valid_digit:
            response_body = {
                "status": False,
                "message": f"Invalid request. IDType: {idType}, ID: {id}",
                "data": None,
            }

            return Response(data=response_body, status=status.HTTP_400_BAD_REQUEST)
        else:
            id_instance = YouVerifyRequest.get_create_identity_result(
                id=id, id_type=idType, source=IdentityRequestSource.EXTERNAL
            )
            data = YouVerifyRequest.objects.filter(id=id_instance.id).values()[0]
            response_body = {
                "status": True,
                "message": "success",
                "data": data,
            }

            return Response(data=response_body, status=status.HTTP_200_OK)


class RawBureauCheck(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed)

    def get(self, request):
        Identification = request.GET.get("Identification")

        if not Identification:
            response_body = {
                "status": False,
                "message": "Invalid id",
                "data": None,
            }

            return Response(data=response_body, status=status.HTTP_400_BAD_REQUEST)

        constant = ConstantTable.get_constant_table_instance()
        debt_threshold = constant.debt_threshold
        debt_institution_count = constant.debt_institution_count

        credit_bureau = RawFirstcentralCreditCheck(
            Identification=Identification,
            debt_threshold=debt_threshold,
            max_debt_institution_count=debt_institution_count,
        ).get_credit_status()
        return Response(data=credit_bureau, status=status.HTTP_200_OK)


class GetLoanConstant(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):

        constant = ConstantTable.get_constant_table_instance()
        staff_loan_config = constant.staff_loans_config
        repayment_types = RepaymentFrequency.values

        staff_loan_config.update(
            {
                "use_guarantor_verification": constant.use_guarantor_verification,
                "repayment_type": repayment_types,
                "boosta_loan_config": constant.boosta_loan_config,
            }
        )

        return Response(data=staff_loan_config, status=status.HTTP_200_OK)


class GetAgencyBankingAgent(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):
        user_id = request.GET.get("user_id")
        # Get today's date as a date object
        today = datetime.today().date()

        # Fetch agency user details using the AgencyBankingClass
        user_details = AgencyBankingClass.get_agency_user_details(
            user_id=user_id, start_date=today
        )
        return Response(data=user_details, status=status.HTTP_200_OK)


class GetProductAssignment(generics.ListAPIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    pagination_class = CustomPagination
    serializer_class = GetProductAssignmentSerializer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "assigned",
        "product_status",
    )
    search_fields = ("borrower_loan__borrower__phone_number",)

    def get_queryset(self):
        user = self.request.user
        if user.is_staff:
            prod_assign_qs = ProductAssignment.objects.all()
        else:
            prod_assign_qs = ProductAssignment.objects.filter(
                supervisor=user
            ).select_related(
                "borrower_loan",
            )
        return prod_assign_qs

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        unassigned_count = queryset.filter(assigned=False).count()
        assigned_count = queryset.filter(assigned=True).count()

        response_data = {
            "status": True,
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "is_staff": request.user.is_staff,
            "ussd": "347*180*22#",
            "unassigned_count": unassigned_count,
            "assigned_count": assigned_count,
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class AssignProductView(APIView):
    permission_classes = (permissions.IsAuthenticated, IsLoanAvailable)

    def post(self, request):
        serializer = AssignProductSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        product_instance = validated_data.get("product_instance")

        ProductAssignment.assign_product(product_instance=product_instance)

        response_data = {
            "status": True,
            "message": "Verified",
            "product_id": product_instance.id,
        }
        return Response(data=response_data, status=status.HTTP_202_ACCEPTED)


class ConfirmIMEIStatusView(APIView):
    permission_classes = (permissions.IsAuthenticated, IsLoanAvailable)

    def post(self, request):
        serializer = ConfirmIMEIStatusViewSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        product_instance = validated_data.get("product_instance")
        device_info = validated_data.get("device_info")

        ProductAssignment.update_device_details_after_query(
            product_instance=product_instance, device_info=device_info
        )

        response_data = {
            "status": True,
            "message": "Verified",
            "product_id": product_instance.id,
        }
        return Response(data=response_data, status=status.HTTP_202_ACCEPTED)


class AddProductImeiView(APIView):
    permission_classes = (permissions.IsAuthenticated, IsLoanAvailable, IsStaff)

    def post(self, request):
        serializer = AddProductImeiSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        product_instance = validated_data.get("product_instance")
        imei = validated_data.get("imei")
        device_type = validated_data.get("device_type")

        product_instance.update_imei(
            imei=imei, device_type=device_type, imei_added_by=request.user
        )

        response_data = {
            "status": True,
            "message": "success",
            "imei": imei,
        }
        return Response(data=response_data, status=status.HTTP_202_ACCEPTED)


class ProductImeiBulkUpdate(APIView):
    permission_classes = (permissions.IsAuthenticated, IsLoanAvailable, IsStaff)

    def post(self, request):
        serializer = ProductImeiBulkUpdateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        imei = validated_data.get("imei")
        product_id = validated_data.get("product_id")

        admin_update = ProductAssignment.admin_update_product_imei(
            product_ids=product_id, device_imei=imei, imei_added_by=request.user
        )

        response_data = {
            "status": True,
            "message": "success",
            "total_update": admin_update,
        }
        return Response(data=response_data, status=status.HTTP_202_ACCEPTED)


class GetLoanStatusWithImei(APIView):

    def get(self, request):
        device_imei = request.GET.get("device_imei")

        if not device_imei:
            error_res = {
                "status": False,
                "message": f"Expected `device_imei` not {device_imei}",
                "data": None,
                "account_detail": None,
            }

            return Response(data=error_res, status=status.HTTP_400_BAD_REQUEST)

        else:
            response_data = ProductAssignment.get_repayment_details_and_history(
                imei=device_imei
            )
            ok_res = {
                "status": True,
                "message": "success",
            }

            ok_res.update(response_data)

            return Response(data=ok_res, status=status.HTTP_200_OK)


class PendingGroupInfo(generics.ListAPIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    pagination_class = CustomPagination
    serializer_class = PendingGroupInfoSerializer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "is_active",
        "status",
    )
    # search_fields = ("",)

    def get_queryset(self):
        request_user = self.request.user
        group_savings_query_set = AjoSepo.objects.filter(user=request_user)
        return group_savings_query_set

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)

        response_data = {
            "status": True,
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class GetRunningGroupLoan(generics.ListAPIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    pagination_class = CustomPagination
    serializer_class = GetRunningGroupLoanSerializer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "is_active",
        "status",
    )
    # search_fields = ("",)

    def get_queryset(self):
        request_user = self.request.user
        # group_savings_query_set = AjoSepo.objects.filter(user=request_user, status="RUNNING")
        group_savings_query_set = AjoSepo.objects.filter(user=request_user)
        return group_savings_query_set

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)

        response_data = {
            "status": True,
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class CreateGroupLoan(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)

    def post(self, request):
        serializer = CreateGroupLoanSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        documentation_queryset = validated_data.get("documentation_queryset")
        eligibility_queryset = validated_data.get("eligibility_queryset")

        group_loan_data = LoanKYCDocumentation.create_group_loan(
            documentation_queryset=documentation_queryset,
            eligibility_queryset=eligibility_queryset,
        )

        result = {"status": True, "data": group_loan_data}

        return Response(data=result, status=status.HTTP_200_OK)


class DisburseGroupLoan(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)

    def post(self, request):
        serializer = DisburseGroupLoanSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        ajoloan_queryset = validated_data.get("ajoloan_queryset")
        loan_ids = ajoloan_queryset.values_list("id", flat=True)
        verified_ids = list(loan_ids)

        disburse_group_loan_handler.apply_async(
            queue="communicationman",
            args=[verified_ids],
        )
        result = {"status": True, "message": "success"}

        return Response(data=result, status=status.HTTP_200_OK)


class GroupLoanRepaymentAPIView(GenericAPIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    serializer_class = GroupLoanRepaymentSerializer

    def post(self, request):
        """
        Trigger Repayment Asynchronously
        """
        serializer = self.serializer_class(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)
        validated_data = serializer.validated_data

        loan_objects = validated_data.get("loans")
        transaction_pin = validated_data.get("transaction_pin")
        total_amount = validated_data.get("total_amount")
        user = request.user

        agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()
        if not check_if_agent_can_pay(agent_wallet=agent_wallet, amount=total_amount):
            return error_response(
                message="insufficient funds in wallet for repayment",
                error="891",
            )

        access_token = request.headers.get("Authorization", "").partition(" ")[-1]
        verify_agent_pin = verify_transaction_pin(
            transaction_pin=transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )

        if isinstance(verify_agent_pin, Response):
            return verify_agent_pin

        group_loan_repayment.delay(loan_data=loan_objects)

        return success_response(message="repayment triggered successfully")


class GetBoostaLoanSummary(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):
        loan_amount = request.GET.get("loan_amount")
        loan_tenure = request.GET.get("loan_tenure")
        boosta_xtype = request.GET.get("boosta_xtype")
        business_suite = request.GET.get("business_suite", False)

        if not loan_amount:
            error_res = {
                "status": False,
                "message": f"Invalid loan amount.",
                "data": None,
            }

            return Response(data=error_res, status=status.HTTP_400_BAD_REQUEST)

        if not loan_tenure:
            error_res = {
                "status": False,
                "message": f"Loan tenure is required.",
                "data": None,
            }

            return Response(data=error_res, status=status.HTTP_400_BAD_REQUEST)

        const = ConstantTable.get_constant_table_instance()
        admin_min_loan_amount = const.min_loan_amount

        if admin_min_loan_amount > float(loan_amount):
            error_res = {
                "status": False,
                "message": f"The minimum loan amount is ₦{admin_min_loan_amount}.",
                "data": None,
            }
            return Response(data=error_res, status=status.HTTP_400_BAD_REQUEST)

        try:

            data = AjoLoan.get_boosta_loan_summary(
                loan_amount=loan_amount,
                loan_tenure=loan_tenure,
                boosta_xtype=boosta_xtype,
                business_suite=business_suite,
            )
        except ValueError as err:
            error_res = {
                "status": False,
                "message": str(err),
                "data": None,
            }

            return Response(data=error_res, status=status.HTTP_400_BAD_REQUEST)

        if isinstance(data, dict):
            result = {
                "status": True,
                "message": "success",
                "data": data,
            }
            return Response(data=result, status=status.HTTP_200_OK)
        else:
            error_res = {
                "status": False,
                "message": f"The selected loan tenure: {loan_tenure} is not available. Please choose a different option.",
                "data": None,
            }

            return Response(data=error_res, status=status.HTTP_400_BAD_REQUEST)


class LoanEligibilityAmountCheckerView(APIView):
    # permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    # serializer_class = GroupLoanRepaymentSerializer

    def post(self, request):

        serializer = self.serializer_class(data=request.data)
        pass


class VerifyLoanEligibilityView(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    serializer_class = VerifyLoanEligibilitySerializer

    def post(self, request):

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        result = LoanEligibilityVerification.check_repayment_ability(**validated_data)
        actual_data = result.get("actual_data")

        able_to_repay = actual_data.get("able_to_repay")
        if isinstance(actual_data, dict) and able_to_repay is True:
            data = {
                "status": able_to_repay,
                "message": "success",
                "data": result,
            }
            return Response(data=data, status=status.HTTP_200_OK)
        else:
            data = {
                "status": False,
                "message": "After reviewing your details, we regret to inform you that the loan request cannot be approved. Please contact support.",
                "data": result,
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)


class PostLoanToLoanDiskManually(APIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = ManualLoanDiskPostingSerializer

    def post(self, request):

        serializer = ManualLoanDiskPostingSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        loan_id = validated_data.get("loan_id")
        run_post_loan_response = run_post_loan_to_loan_disk(loan_id=loan_id)

        data = {
            "status": True,
            "message": "success",
            "data": run_post_loan_response,
        }
        return Response(data=data, status=status.HTTP_200_OK)


class UpdateLoanToLoanDiskManually(APIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = ManualLoanDiskPostingSerializer

    def post(self, request):

        serializer = ManualLoanDiskPostingSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        loan_id = validated_data.get("loan_id")
        run_update_loan_response = update_loan_status_on_loan_disk(
            loan_id=loan_id, from_signal=True
        )

        data = {
            "status": True,
            "message": "success",
            "data": run_update_loan_response,
        }
        return Response(data=data, status=status.HTTP_200_OK)


class PostRepaymentsManuallyToLoanDisk(APIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = ManualLoanDiskPostingSerializer

    def post(self, request):

        serializer = ManualLoanDiskPostingSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        loan_id = validated_data.get("loan_id")
        amount = validated_data.get("amount")
        run_post_repayment = celery_handle_loandisk_loan_repayment(
            ajo_loan_id=loan_id,
            amount=amount,
            repayment_type=RepaymentType.LITE_DEBIT,
        )

        data = {
            "status": True,
            "message": "success",
            "data": run_post_repayment,
        }
        return Response(data=data, status=status.HTTP_200_OK)


class GetLoansNotOnLoanDisk(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):
        loans_without_loandisk_id = []
        ajo_loans = AjoLoan.objects.filter(loandisk_loan_id=None)
        for ajo_loan in ajo_loans:
            loan_details = {
                "loan_id": ajo_loan.id,
                "loandisk_branch_id": ajo_loan.branch_loandisk_loan_id,
                "loan_borrower_full_name": ajo_loan.borrower_full_name,
                "amount": ajo_loan.amount,
            }
            loans_without_loandisk_id.append(loan_details)

        data = {
            "length": len(loans_without_loandisk_id),
            "data": loans_without_loandisk_id,
        }

        return Response(data=data, status=status.HTTP_200_OK)

        # return Response(data=loans_without_loandisk_id, status=status.HTTP_200_OK)


class PostBorrowerToLoanDiskManually(APIView):
    # permission_classes = (permissions.IsAuthenticated,)
    serializer_class = ManualLoanDiskBorrowePostingSerializer

    def post(self, request):
        """
        Because borrowers are posted just before loans are posted to lon disk, we expect the borrowers to already exist.
        If that isnt the case, we first check loan disk to ensure they exist, if not, then we post them to loan disk.
        """

        serializer = ManualLoanDiskBorrowePostingSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        ajo_user_id = validated_data.get("ajo_user_id")

        try:
            ajo_user_instance = AjoUser.objects.get(id=ajo_user_id)
        except AjoUser.DoesNotExist:
            data = {"status": False, "message": "Ajo User not found"}
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        retrieved_borrower = LoandiskManager().get_boorower_details(
            ajo_user_instance.phone_number
        )
        if retrieved_borrower.get("status") is False:
            run_post_borrower_response = create_borrower_on_loan_disk_branch(
                ajo_user_id=ajo_user_id
            )
            data = {
                "status": True,
                "message": "success",
                "data": f"Ran Creating Borrower: {run_post_borrower_response}",
            }
            return Response(data=data, status=status.HTTP_200_OK)
        else:
            data = {
                "status": True,
                "message": "success",
                "data": {
                    "message": "Returned a borrower from Loan Disk",
                    "response": retrieved_borrower.get("response"),
                },
            }
            return Response(data=data, status=status.HTTP_200_OK)


class BorrowersNotOnLoanDisk(APIView):

    # permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):
        """Will fetch all borrowers on our backend without a borrower ID"""

        borrowers_without_loan_disk_borrower_id = []
        borrowers = AjoUser.objects.filter(loandisk_borrower_id=None)
        for borrower in borrowers:
            borrower_details = {
                "ajo_user_id": borrower.id,
                "borrower_full_name": borrower.fullname,
                "borrower_phone": borrower.phone_number,
                "bad_borrower_id": borrower.loandisk_borrower_id,
            }
            borrowers_without_loan_disk_borrower_id.append(borrower_details)

        data = {
            "length": len(borrowers_without_loan_disk_borrower_id),
            "data": borrowers_without_loan_disk_borrower_id,
        }

        return Response(data=data, status=status.HTTP_200_OK)


class RetrieveBorrowerFromLoanDisk(APIView):

    # permission_classes = (permissions.IsAuthenticated,)

    def get(self, request, ajo_user_id):

        try:
            ajo_user_instance = AjoUser.objects.get(id=ajo_user_id)
        except AjoUser.DoesNotExist:
            data = {"status": False, "message": "Ajo User not found"}
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        retrieved_borrower = LoandiskManager().get_boorower_details(
            ajo_user_instance.phone_number
        )
        if retrieved_borrower.get("status") is True:
            data = {
                "status": True,
                "message": "SUCCESSFULLY RETIREVED BORROWER FROM LOAN DISK",
                "data": retrieved_borrower.get("response"),
            }
            return Response(data=data, status=status.HTTP_200_OK)

        elif (
            retrieved_borrower.get("status") is False
            and retrieved_borrower.get("message") == "Borrower not on LoanDisk"
        ):
            data = {
                "status": False,
                "message": "BORROWER NOT ON LOAN DISK",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)
        else:
            data = {
                "status": False,
                "message": "UNABLE TO RETRIEVE BORROWER FROM LOAN DISK",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)


class LoanDiskBorrowersWithBadID(APIView):

    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):

        retrieved_borrower_ids = []
        bad_id_borrowers = AjoUser.objects.filter(
            Q(loandisk_borrower_id__contains="response")
            | Q(loandisk_borrower_id__contains="http")
        )

        for bad_id_borrower in bad_id_borrowers:
            borrower_details = {
                "borrower_ajo_user_id": bad_id_borrower.id,
                "borrower_phone": bad_id_borrower.phone_number,
                "borrower_name": f"{bad_id_borrower.first_name} {bad_id_borrower.last_name}",
                "bad_borrower_id": bad_id_borrower.loandisk_borrower_id,
            }
            retrieved_borrower_ids.append(borrower_details)

        data = {"length": len(retrieved_borrower_ids), "data": retrieved_borrower_ids}

        return Response(data=data, status=status.HTTP_200_OK)


class FixLoanDiskBorrowerID(APIView):

    permission_classes = (permissions.IsAuthenticated,)

    def patch(self, request, ajo_user_id):

        try:
            ajo_user = AjoUser.objects.get(id=ajo_user_id)
        except AjoUser.DoesNotExist:
            raise AjoUser.DoesNotExist("AjoUser not found.")

        phone_number = ajo_user.phone_number
        borrower_details = LoandiskManager().get_boorower_details(
            phone_number=phone_number
        )

        if borrower_details.get("status") is True:
            borrower_loandisk_id = borrower_details.get("response")["borrower_id"]
            initial_id = ajo_user.loandisk_borrower_id
            ajo_user.loandisk_borrower_id = borrower_loandisk_id
            ajo_user.save()

            ajo_user.refresh_from_db()

            data = {
                "status": True,
                "response": {
                    "initialId": initial_id,
                    "newId": ajo_user.loandisk_borrower_id,
                },
            }

            return Response(data, status.HTTP_200_OK)
        else:
            data = {"status": False, "message": borrower_details.get("message")}

            return Response(data, status.HTTP_400_BAD_REQUEST)


class LoanDiskBorrower(APIView):

    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request, ajo_user_id):

        try:
            ajo_user = AjoUser.objects.get(id=ajo_user_id)
        except AjoUser.DoesNotExist:
            raise AjoUser.DoesNotExist("AjoUser not found.")

        phone_number = ajo_user.phone_number
        borrower_details = LoandiskManager().get_boorower_details(
            phone_number=phone_number
        )

        return Response(data=borrower_details, status=status.HTTP_400_BAD_REQUEST)


class GetLoanBorrowerImages(APIView):
    # permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):
        loan_id = request.GET.get("loan_id")
        agent_email = request.GET.get("agent_email")
        try:
            loan_instance = AjoLoan.objects.get(id=loan_id, agent__email=agent_email)
        except AjoLoan.DoesNotExist:
            data = {"status": False, "message": "Invalid Loan ID"}
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)
        try:
            borrower_info_instance = BorrowerInfo.objects.get(loan=loan_instance)
        except BorrowerInfo.DoesNotExist:
            data = {
                "status": False,
                "message": "Borrower info has not being created for Loan Instance",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        biometrics_log = BiometricsMetaData.objects.filter(
            borrower_info=borrower_info_instance,
            user_type="BORROWER",
            base64_img_str__isnull=False,
        ).last()
        ajouser = loan_instance.borrower
        user = ajouser.user
        start_date = timezone.now().date()
        try:
            get_agent_image = AgencyBankingClass().get_agency_user_details(
                user_id=user.customer_user_id, start_date=start_date
            )
            agent_image = get_agent_image.get("user_bvn_image_base64_string", None)
        except Exception:
            agent_image = None

        data = {
            "status": True,
            "message": "success",
            "data": {
                "borrowerCapture": (
                    biometrics_log.base64_img_str if biometrics_log else None
                ),
                "borrowerVerificatioinIdImage": (
                    borrower_info_instance.base_64_img_string
                ),
                "agent_image": agent_image,
            },
        }
        return Response(data=data, status=status.HTTP_200_OK)


class GetLoanGuarantorImages(APIView):
    # permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):
        loan_id = request.GET.get("loan_id")
        agent_email = request.GET.get("agent_email")
        try:
            loan_instance = AjoLoan.objects.get(id=loan_id, agent__email=agent_email)
        except AjoLoan.DoesNotExist:
            data = {"status": False, "message": "Invalid Loan ID"}
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        guarantor_instance = loan_instance.guarantor

        try:
            borrower_info_instance = BorrowerInfo.objects.get(loan=loan_instance)
        except BorrowerInfo.DoesNotExist:
            data = {
                "status": False,
                "message": "Borrower info has not being created for Loan Instance",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        biometrics_log = BiometricsMetaData.objects.filter(
            borrower_info=borrower_info_instance,
            user_type="GUARANTOR",
            base64_img_str__isnull=False,
        ).last()

        data = {
            "status": True,
            "message": "success",
            "data": {
                "guarantorCapture": (
                    biometrics_log.base64_img_str if biometrics_log else None
                ),
                "guarantorVerificatioinIdImage": (
                    guarantor_instance.base_64_img_string
                    if guarantor_instance
                    else None
                ),
            },
        }
        return Response(data=data, status=status.HTTP_200_OK)


class BulkRepaymentSerializerView(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    serializer_class = BulkRepaymentSerializer

    def get(self, request):
        batch_id = request.GET.get("batch_id")
        request_user = self.request.user
        repayment_record = BulkRepaymentRecord.objects.filter(
            loan__agent=request_user, batch_id=batch_id
        )
        details = repayment_record.values("batch_id").annotate(
            total_amount=Sum("amount"),
            created_at=Max("created_at"),
            record_count=Count("id"),
        )

        results = []
        for record in repayment_record:
            loan_instance = record.loan
            ajo_user = loan_instance.borrower
            data = {
                "full_name": ajo_user.fullname,
                "phone_number": ajo_user.phone_number,
                "amount_paid": record.amount,
                "status": record.status,
            }
            results.append(data)

        data = {
            "status": True,
            "message": "success",
            "results": results,
        }

        try:
            data.update(details[0])
        except IndexError:
            pass

        return Response(data=data, status=status.HTTP_200_OK)

    def post(self, request):

        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        del validated_data["transaction_pin"]
        batch_id = BulkRepaymentRecord.create_record(
            record_summary=validated_data["record_summary"]
        )
        bulk_repayment_handler.apply_async(
            queue="accountman",
            args=[batch_id],
        )
        # bulk_repayment_handler(batch_id)
        data = {
            "status": True,
            "batch_id": batch_id,
            "message": "Your repayment submission is currently being processed",
            "data": validated_data,
        }
        return Response(data=data, status=status.HTTP_200_OK)


class HealthInsuranceSummary(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):
        insurance_id = request.GET.get("insurance_id")

        try:
            insurance = HealthInsurance.objects.get(id=insurance_id)
        except HealthInsurance.DoesNotExist as err:
            error_res = {
                "status": False,
                "message": str(err),
                "data": None,
            }

            return Response(data=error_res, status=status.HTTP_400_BAD_REQUEST)

        result = {
            "status": True,
            "message": "success",
            "data": insurance.get_summary(),
        }
        return Response(data=result, status=status.HTTP_200_OK)


class BulkRepaymentHistory(generics.ListAPIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    pagination_class = CustomPagination
    serializer_class = BulkRepaymentHistorySerializer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = ("status",)
    # search_fields = ("loan__borrower__phone_number",)

    def get_queryset(self):
        request_user = self.request.user
        return (
            BulkRepaymentRecord.objects.filter(loan__agent=request_user)
            .values("batch_id")
            .annotate(
                total_amount=Sum("amount"),
                status=Max("status"),
                created_at=Max("created_at"),
                record_count=Count("id"),
            )
            .order_by("batch_id")
        )


class UpdateSaversName(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):
        email = request.GET.get("email")
        # request_user_email = request.GET.get("request_user_email")
        phone_number = request.GET.get("phone_number")
        first_name = request.GET.get("first_name")
        last_name = request.GET.get("last_name")
        verified_location_address = request.GET.get("verified_location_address")
        state = request.GET.get("state")
        address = request.GET.get("address")

        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            return Response(
                data={
                    "status": False,
                    "message": "You do not have permission to perform this action.",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            if not email or not phone_number:
                return Response(
                    data={
                        "status": False,
                        "message": "Confirm email and phone number",
                    },
                    status=status.HTTP_200_OK,
                )

            ajo_user = AjoUser.objects.filter(
                user__email=email, phone_number__startswith=phone_number
            ).last()
            if not ajo_user:

                return Response(
                    data={
                        "status": False,
                        "message": "Invalid email or phone number match",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            else:
                if not first_name or not last_name:
                    return Response(
                        data={
                            "status": False,
                            "message": "Confirm first name and last name ",
                        },
                        status=status.HTTP_200_OK,
                    )

                else:

                    ajo_user.first_name = first_name
                    ajo_user.last_name = last_name

                    if verified_location_address and state and address:
                        ajo_user.verified_location_address = verified_location_address
                        ajo_user.state = state
                        ajo_user.address = address

                    ajo_user.save()

                    return Response(
                        data={"status": True, "message": "success"},
                        status=status.HTTP_200_OK,
                    )


class LoanTopUpApiView(GenericAPIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    serializer_class = LoanTopUpSerializer

    def get(self, request):
        eligibility_id = request.GET.get("eligibility_id")
        tenure = request.GET.get("tenure")
        try:
            eligibility_instance = LoanEligibility.objects.get(
                id=eligibility_id, eligibility_type=EligibilityType.TOP_UP
            )
        except LoanEligibility.DoesNotExist:
            return Response(
                data={
                    "status": False,
                    "message": "Loan top-up not found.",
                    "data": None,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        ajo_loan = AjoLoan.objects.get(id=eligibility_instance.loan_topup_id)

        eligibility_instance.get_topup_eligibility(
            loan_id=eligibility_instance.loan_topup_id
        )
        topup_summary = ajo_loan.evaluate_topup_eligibility(
            get_summary=True, tenure=tenure
        )

        return Response(
            data={"status": True, "message": "success", "data": topup_summary},
            status=status.HTTP_200_OK,
        )

    def post(self, request):
        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        topup_summary = validated_data["topup_summary"]
        topup_porcessing_fee = validated_data["topup_porcessing_fee"]
        topup_summary.update({"eligibility_id": validated_data["eligibility_id"]})
        AjoLoan.topup_disbursement(
            topup_summary=topup_summary, topup_porcessing_fee=topup_porcessing_fee
        )

        return Response(
            data={
                "status": True,
                "message": "successfully disbursed",
                "data": topup_summary,
            },
            status=status.HTTP_200_OK,
        )

    def patch(self, request):
        # Assuming you want to partially update the loan top-up details
        eligibility_id = request.GET.get("eligibility_id")

        try:
            eligibility_instance = LoanEligibility.objects.get(
                id=eligibility_id, eligibility_type=EligibilityType.TOP_UP
            )
            eligibility_instance.active = False
            eligibility_instance.topup_decision_status = TopUpDecisionStatus.DECLINED
            eligibility_instance.save()

            return Response(
                data={
                    "status": True,
                    "message": "Loan top-up successfully Declined.",
                    "topup_decision_status": TopUpDecisionStatus.DECLINED,
                },
                status=status.HTTP_200_OK,
            )
        except LoanEligibility.DoesNotExist:
            return Response(
                data={
                    "status": False,
                    "message": "Loan top-up not found.",
                    "data": None,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )


class AdminDevGetTopUpEvaluation(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):
        loan_id = request.GET.get("loan_id", None)
        request_type = request.GET.get("request_type", "ONE_OFF")
        tenure = request.GET.get("tenure")

        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            return Response(
                data={
                    "status": False,
                    "message": "You do not have permission to perform this action.",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not loan_id and request_type == "ONE_OFF":
            return Response(
                data={
                    "status": False,
                    "message": "invalid loan id type",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        else:
            if request_type == "GET_ALL":
                considerable_performance_status = [
                    LoanPerformanceStatus.PERFORMING,
                    LoanPerformanceStatus.OWED_BALANCE,
                    LoanPerformanceStatus.PAST_MATURITY,
                ]
                loan_qs = AjoLoan.objects.filter(
                    status=LoanStatus.OPEN,
                    performance_status__in=considerable_performance_status,
                )
                eligible = 0
                not_eligible = 0
                for ajo_loan in loan_qs:
                    result = LoanEligibility.get_topup_eligibility(loan_id=ajo_loan.id)
                    if result["is_eligible"]:
                        eligible += 1
                    else:
                        not_eligible += 1

                return Response(
                    data={
                        "status": True,
                        "message": "success",
                        "data": {"eligible": eligible, "not_eligible": not_eligible},
                    },
                    status=status.HTTP_200_OK,
                )

            try:
                ajolon = AjoLoan.objects.get(id=loan_id)
            except AjoLoan.DoesNotExist as err:
                return Response(
                    data={
                        "status": False,
                        "message": str(err),
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            return Response(
                data={
                    "status": True,
                    "message": "success",
                    "data": ajolon.evaluate_topup_eligibility(
                        get_summary=True, tenure=tenure
                    ),
                },
                status=status.HTTP_200_OK,
            )


class CreditWorthiness(APIView):

    # permission_classes = []
    serializer_class = CreditWorthinessSerializer

    def post(self, request):

        user = request.user

        try:
            serializer = CreditWorthinessSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError:
            data = {
                "status": False,
                "message": get_serializer_key_error(serializer.errors),
                "response": None,
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        validated_data = serializer.validated_data
        account_id = validated_data.get("account_id")
        amount = validated_data.get("amount")
        tenor = validated_data.get("tenor")
        # ajo_user = validated_data.get("ajo_user")
        bvn = validated_data.get("bvn")

        mono_statement = MonoAPI.get_account_statement(account_id)
        if mono_statement.get("status"):
            statement = mono_statement.get("data")
            worthiness = CreditWorthinessCalculator(statement)
            internal_worthiness = worthiness.compute_worthiness()
            credit_worthiness = CreditWorthinessData.create_credit_worthiness(
                # user=user,
                mono_account_id=account_id,
                internal_response=internal_worthiness,
            )

            const = ConstantTable.get_constant_table_instance()
            payload = {
                "bvn": bvn,
                "principal": amount,
                "interest_rate": const.ajo_loan_interest_rate,
                "term": tenor,
                "run_credit_check": True,
            }

            mono_credit_worthiness = MonoAPI.request_credit_worthiness(
                account_id, payload
            )
            if mono_credit_worthiness.get("status"):
                data = {
                    "status": True,
                    "message": "Successfully ran credit worthiness.",
                    "response": {"reference": credit_worthiness.reference},
                }
                return Response(data, status=status.HTTP_200_OK)

            # Error around mono credit worthiness request.
            data = {
                "status": False,
                "message": "Error requesting for credit worthiness.",
                "response": mono_statement.get("message", None),
                "error_message": mono_statement.get("error", None),
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        # Errors around getting statements
        else:
            data = {
                "status": False,
                "message": "Error getting statement.",
                "response": mono_statement.get("message", None),
                "error_message": mono_statement.get("error", None),
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class MonoCreditWorthinessWebhook(APIView):

    def post(self, request):
        payload = request.data

        account_id = payload.get("data", {}).get("account", None)
        credit_worthiness = (
            CreditWorthinessData.objects.filter(mono_account_id=account_id)
            .order_by("-last_updated")
            .first()
        )
        credit_worthiness.mono_response = payload
        credit_worthiness.completed_check = True
        credit_worthiness.save()

        data = {"status": "success", "response": "Worhtiness Received Successfully"}
        return Response(data, status=status.HTTP_200_OK)


class CheckerIdVerification(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    serializer_class = CheckerIdVerificationSerializer

    def post(self, request):

        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        checker_instance = validated_data["checker_instance"]
        checker_id = checker_instance.id
        stage = checker_instance.stage
        phone_number = validated_data["phone_number"]
        id_number = validated_data["id_number"]
        loan_type = validated_data["loan_type"]

        result = {
            "status": True,
            "stage": stage,
            "data": {
                "checker_id": checker_id,
                "phone_number": phone_number,
                "id_number": id_number,
                "loan_type": loan_type,
            },
        }

        return Response(data=result, status=status.HTTP_200_OK)


class CheckerImageVerification(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    serializer_class = CheckerImageVerificationSerializer

    def post(self, request):

        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        checker_instance = validated_data["checker_instance"]
        checker_id = checker_instance.id
        ajo_user = checker_instance.ajo_user
        phone_number = ajo_user.phone_number
        id_number = checker_instance.id_number
        id_type = checker_instance.id_type

        const = ConstantTable.get_constant_table_instance()
        debt_threshold = const.debt_threshold
        debt_institution_count = const.debt_institution_count

        if id_type == "NIN":
            bvn = phone_number
        else:
            bvn = id_number

        credit_bureau = FirstcentralCreditCheck(
            bvn=bvn,
            debt_threshold=debt_threshold,
            ajo_user=ajo_user,
            max_debt_institution_count=debt_institution_count,
        ).get_credit_status()

        stage = CheckerStage.CRC_CHECK
        checker_instance.crc_result = credit_bureau
        checker_instance.save()

        credit_bureau_status = credit_bureau.get("status")
        reason = credit_bureau.get("reason", "")

        data = {
            "status": False,
            "message": reason,
            "checker_id": checker_id,
            "stage": stage,
            "crc_check": credit_bureau,
        }
        if isinstance(credit_bureau_status, bool):

            bad_loans_institions_count = credit_bureau.get("bad_loans_institions_count")
            high_outstanding_debt = credit_bureau.get("high_outstanding_debt")
            count_of_open_loans = credit_bureau.get("count_of_open_loans")

            checker_instance.bad_loans_institions_count = bad_loans_institions_count
            checker_instance.high_outstanding_debt = high_outstanding_debt
            checker_instance.count_of_open_loans = count_of_open_loans
            checker_instance.stage = stage
            checker_instance.save()

            if credit_bureau_status:
                stage = CheckerStage.LOAN_AMOUNT
                checker_instance.stage = stage
                checker_instance.count_of_open_loans = count_of_open_loans
                checker_instance.save()

                data = {
                    "status": credit_bureau_status,
                    "checker_id": checker_id,
                    "message": "Success",
                    "stage": stage,
                    "crc_check": credit_bureau,
                }

        return Response(data=data, status=status.HTTP_200_OK)


class CheckerIntendedAmountVerification(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    serializer_class = CheckerIntendedAmountVerificationSerializer

    def post(self, request):

        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        checker_instance = validated_data["checker_instance"]
        checker_id = checker_instance.id
        intended_amount = validated_data["intended_amount"]
        duration = validated_data["duration"]
        ajo_user = validated_data["ajo_user"]
        img_string = validated_data["img_string"]

        loan_type = checker_instance.loan_type

        const = ConstantTable.get_constant_table_instance()
        eligibility_checker_data = DictToObject(validated_data)

        loancalc = LoanCalculator(
            constant=const,
            duration=duration,
            eligibility_checker_data=eligibility_checker_data,
        )

        try:
            result = loancalc.evaluate_loan_affordability(
                eligible_amount=intended_amount
            )
        except ValueError as err:
            data = {
                "status": False,
                "error": str(err),
                "message": "Unable to proceed with the request. Kindly contact support.",
                "summary": None,
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        verification_instance = (
            LoanEligibilityVerification.handle_create_eligibility_verification_data(
                checker_id=checker_id, result=validated_data
            )
        )

        handle_image_upload_result_update_on_loan_eligiblity_verification.apply_async(
            queue="documentation",
            kwargs={
                "verification_instance_id": verification_instance.id,
                "result": result,
                "img_string": img_string,
            },
        )
        actual_data = result.get("actual_data")
        able_to_repay = actual_data.get("able_to_repay", False)

        application_queryset = LoanAffordability.objects.filter(id=checker_id)

        if isinstance(actual_data, dict) and able_to_repay is True:
            application_queryset.update(
                checker_status="SUCCESS",
                intended_amount=intended_amount,
                amount_approved=intended_amount,
            )

            summary = AjoLoan.get_boosta_loan_summary(
                loan_amount=intended_amount,
                loan_tenure=repayment_period_months,
                ajouser=ajo_user,
                boosta_xtype=loan_type,
            )

            data = {"status": True, "message": "success", "summary": summary}
            return Response(data=data, status=status.HTTP_200_OK)
        else:
            openai_review = OpenAiEligbilityReview.get_eligibility_review(
                eligibility_id=None,
                eligible_amount=intended_amount,
                phone_number=ajo_user.phone_number,
                verification_result=result,
                checker_id=checker_id,
            )
            loan_application_review = openai_review.get("loan_application_review")
            decision = loan_application_review.get("decision")

            approval_status = decision.get("status")
            score = decision.get("score")
            recommended_loan_amount = decision.get("recommended_loan_amount")
            repayment_period_months = decision.get("repayment_period_months")
            monthly_repayment_amount = decision.get("monthly_repayment_amount")
            conditions = decision.get("conditions")
            reason = decision.get("reason")
            recommendations = decision.get("recommendations")
            amount_approved = (
                intended_amount
                if recommended_loan_amount > intended_amount
                else recommended_loan_amount
            )
            application_queryset.update(
                checker_status="SUCCESS",
                intended_amount=intended_amount,
                amount_approved=intended_amount,
                ai_score=score,
                openai_status=approval_status,
                recommended_loan_amount=recommended_loan_amount,
                repayment_period_months=repayment_period_months,
                monthly_repayment_amount=monthly_repayment_amount,
                conditions=conditions,
                reason=reason,
                recommendations=recommendations,
            )

            summary = AjoLoan.get_boosta_loan_summary(
                loan_amount=amount_approved,
                loan_tenure=repayment_period_months,
                ajouser=ajo_user,
                boosta_xtype=loan_type,
            )

            data = {
                "status": True,
                "message": "success",
                "summary": summary,
            }
            return Response(data=data, status=status.HTTP_200_OK)


class CheckerHistory(generics.ListAPIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    pagination_class = CustomPagination
    serializer_class = CheckerHistorySerializer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "checker_status",
        "stage",
    )
    search_fields = ("ajo_user__phone_number",)

    def get_queryset(self):
        request_user = self.request.user
        return LoanAffordability.objects.filter(ajo_user__user=request_user)


class CreditHealthInquiryHandler(APIView):
    permission_classes = [IsWhitelistedIP]
    serializer_class = CreditHealthInquirySerializer

    def get(self, request):

        loan_amount = request.GET.get("loan_amount")
        loan_tenure = request.GET.get("loan_tenure")
        boosta_xtype = request.GET.get("boosta_xtype")
        business_suite = request.GET.get("business_suite", False)

        if not loan_tenure:
            error_res = {
                "status": False,
                "message": f"Loan tenure is required.",
                "data": None,
            }

            return Response(data=error_res, status=status.HTTP_400_BAD_REQUEST)

        try:

            data = AjoLoan.get_boosta_loan_summary(
                loan_amount=loan_amount,
                loan_tenure=loan_tenure,
                boosta_xtype=boosta_xtype,
                business_suite=business_suite,
            )
        except ValueError as err:
            error_res = {
                "status": False,
                "message": str(err),
                "data": None,
            }

            return Response(data=error_res, status=status.HTTP_400_BAD_REQUEST)

        if isinstance(data, dict):
            result = {
                "status": True,
                "message": "success",
                "data": data,
            }
            return Response(data=result, status=status.HTTP_200_OK)
        else:
            error_res = {
                "status": False,
                "message": f"The selected loan tenure: {loan_tenure} is not available. Please choose a different option.",
                "data": None,
            }

            return Response(data=error_res, status=status.HTTP_400_BAD_REQUEST)

    def post(self, request):

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        phone_number = validated_data.get("phone_number")
        verification_id = validated_data.get("verification_id")
        id_type = validated_data.get("id_type")

        get_eligibility_status = BorrowerInfo.verify_identity_get_eligiblity_status(
            verification_id=verification_id, id_type=id_type, phone_number=phone_number
        )

        return Response(
            data=get_eligibility_status,
            status=(
                status.HTTP_200_OK
                if get_eligibility_status.get("status")
                else status.HTTP_400_BAD_REQUEST
            ),
        )


class CreditHealthAmountInquiryHandler(APIView):
    permission_classes = [IsWhitelistedIP]
    serializer_class = CreditHealthAmountInquiryHandlerSerializer

    def get(self, request):

        _constant_table = ConstantTable.get_constant_table_instance()
        boosta_xtype_config = _constant_table.boosta_2x_config

        result = {
            "status": True,
            "message": "success",
            "data": boosta_xtype_config,
        }
        return Response(data=result, status=status.HTTP_200_OK)

    def post(self, request):

        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        intended_amount = validated_data["intended_amount"]
        duration = validated_data["duration"]
        loan_type = validated_data["loan_type"]
        phone_number = validated_data["phone_number"]

        const = ConstantTable.get_constant_table_instance()
        eligibility_checker_data = DictToObject(validated_data)

        loancalc = LoanCalculator(
            constant=const,
            duration=duration,
            eligibility_checker_data=eligibility_checker_data,
        )

        try:

            result = loancalc.evaluate_loan_affordability(
                eligible_amount=intended_amount
            )
        except ValueError as err:

            data = {
                "status": False,
                "error": str(err),
                "message": "Unable to proceed with the request. Kindly contact support.",
                "summary": None,
            }

            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        _checker_instance = LoanEligibilityVerification.objects.create(
            request_source="LIBERTY_LIFE",
            duration=validated_data.get("duration"),
            img_string=validated_data.get("img_string"),
            seasonal_fluctuation_description=validated_data.get(
                "seasonal_fluctuation_description"
            ),
            other_income_sources_description=validated_data.get(
                "other_income_sources_description"
            ),
            collateral_assets_description=validated_data.get(
                "collateral_assets_description"
            ),
            can_provide_references=validated_data.get("can_provide_references"),
            existing_loan_repayment=validated_data.get("existing_loan_repayment"),
            monthly_expenses=validated_data.get("monthly_expenses"),
            ajo_user=None,
            phone_number=phone_number,
        )

        LoanEligibilityVerification.handle_create_eligibility_verification_data(
            checker_id=None,
            checker_instance=_checker_instance,
            result=validated_data,
        )

        actual_data = result.get("actual_data")
        able_to_repay = actual_data.get("able_to_repay", False)
        # pprint(result)

        _checker_instance.able_to_repay = actual_data.get("able_to_repay")
        _checker_instance.proposed_loan_amount = actual_data.get("proposed_loan_amount")
        _checker_instance.eligible_amount = result.get("eligible_amount", 0.00)
        _checker_instance.alt_proposed_loan_amount = actual_data.get(
            "alt_proposed_loan_amount"
        )
        _checker_instance.total_repayment_amount = actual_data.get(
            "total_repayment_amount"
        )
        _checker_instance.alt_total_repayment_amount = actual_data.get(
            "alt_total_repayment_amount"
        )
        _checker_instance.earnings_per_unit_percentage = actual_data.get(
            "earnings_per_unit_percentage"
        )
        _checker_instance.save()

        if isinstance(actual_data, dict) and able_to_repay is True:
            summary = AjoLoan.get_boosta_loan_summary(
                loan_amount=intended_amount,
                loan_tenure=duration,
                boosta_xtype=loan_type,
            )

            data = {
                "status": True,
                "checker": "INTERNAL_LOGIC",
                "message": "success",
                "summary": summary,
            }

            return Response(data=data, status=status.HTTP_200_OK)
        else:
            openai_review = OpenAiEligbilityReview.get_eligibility_review(
                eligibility_id=None,
                eligible_amount=intended_amount,
                phone_number=phone_number,
                request_source="LIBERTY_LIFE",
                checker_id=None,
                verification_result=result,
            )
            loan_application_review = openai_review.get("loan_application_review")
            decision = loan_application_review.get("decision")

            approval_status = decision.get("status")
            score = decision.get("score")
            recommended_loan_amount = decision.get("recommended_loan_amount")
            repayment_period_months = decision.get("repayment_period_months")
            monthly_repayment_amount = decision.get("monthly_repayment_amount")
            conditions = decision.get("conditions")
            reason = decision.get("reason")
            recommendations = decision.get("recommendations")
            amount_approved = (
                intended_amount
                if recommended_loan_amount > intended_amount
                else recommended_loan_amount
            )

            summary = AjoLoan.get_boosta_loan_summary(
                loan_amount=amount_approved,
                loan_tenure=repayment_period_months,
                boosta_xtype=loan_type,
            )

            data = {
                "status": True,
                "checker": "OPEN_AI",
                "message": "success",
                "summary": summary,
            }
            return Response(data=data, status=status.HTTP_200_OK)


class PreviewLoanUpdateRecalculationView(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)

    def get(self, request):
        from loans.helpers.holiday import HolidayHelper

        loan_id = request.query_params.get("loan_id")
        holiday_class = HolidayHelper()

        try:
            loan_instance = AjoLoan.objects.get(id=loan_id)
        except AjoLoan.DoesNotExist:
            return Response(
                data={"status": False, "message": "Invalid Loan ID"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        loan_calc = AjoLoan.compute_interest_with_duration(
            principal=loan_instance.amount,
            duration=loan_instance.expected_repayment_count,
            loan_type=loan_instance.loan_type,
            ajouser=loan_instance.borrower,
            start_date=loan_instance.start_date,
        )

        holidays_count = holiday_class.count_holidays_between_dates(
            loan_calc.get("start_date"),
            loan_calc.get("end_date"),
            agent=loan_instance.agent,
        )

        old_holidays_count = holiday_class.count_holidays_between_dates(
            loan_instance.start_date, loan_instance.end_date, agent=loan_instance.agent
        )

        loan_calc["holidays_count"] = holidays_count

        current_loan_data = {
            "current_end_date": loan_instance.end_date,
            "current_tenor_in_days": loan_instance.tenor_in_days,
            "current_daily_repayment_amount": loan_instance.daily_repayment_amount,
            "current_repayment_amount": loan_instance.repayment_amount,
            "current_interest_amount": loan_instance.interest_amount,
            "current_interest_rate": loan_instance.interest_rate,
            "holidays_count": old_holidays_count,
        }

        data = {
            "loan_id": loan_id,
            "current_data": current_loan_data,
            "incoming_data": loan_calc,
            # "number_of_holidays": 0.00,
        }

        return Response(data=data, status=status.HTTP_200_OK)


class GetPendingCreditHealthFromLibertyLife(APIView):
    """
    API View to handle pending credit health requests and OTP generation from Liberty Life.

    Methods:
        - GET: Fetches a list of pending credit health requests.
        - POST: Validates loan access code, retrieves credit health details, and sends OTP to the user.

    Permission Classes:
        - permissions.IsAuthenticated: Ensures the user is authenticated.
        - IsBlackListed: Ensures the user is not blacklisted.
        - IsLoanAvailable: Ensures loans are available for the user.
    """

    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)

    def get(self, request):
        """
        Handles GET requests to fetch pending credit health requests.

        Query Parameters:
            - search (str): Optional search query to filter results.

        Returns:
            - HTTP 200: If the pending credit health requests are successfully fetched.
            - HTTP 400: If there is an error fetching the data.
        """

        access_code_or_phone_no = request.GET.get("search")

        pending_credit_health_result = (
            LibertyLifeMgr().get_pending_credit_health_request(
                access_code_or_phone_no=access_code_or_phone_no
            )
        )

        if pending_credit_health_result.get("status_code") in [200, 201]:
            result = pending_credit_health_result.get("response")["data"]
            result = {
                "status": True,
                "message": "success",
                "data": result,
            }
            return Response(data=result, status=status.HTTP_200_OK)
        else:
            result = {
                "status": False,
                "message": "Unable to fetch users",
                "data": None,
            }
            return Response(data=result, status=status.HTTP_400_BAD_REQUEST)

    def post(self, request):
        """
        Handles POST requests to validate loan access code, retrieve credit health details,
        and send an OTP to the user.

        Query Parameters:
            - loan_access_code (str): The access code required to fetch credit health details.

        Returns:
            - HTTP 200: If the loan access code is valid and OTP is successfully sent.
            - HTTP 400: If the loan access code is invalid or if there is an error.
        """

        loan_access_code = request.GET.get("loan_access_code")

        if not loan_access_code:
            result = {
                "status": False,
                "message": "Invalid Access Code",
                "data": None,
            }
            return Response(data=result, status=status.HTTP_400_BAD_REQUEST)

        _credit_health_details = LibertyLifeMgr().get_credit_health_details(
            loan_access_code=loan_access_code
        )

        if _credit_health_details.get("status_code") == 400:
            result = {
                "status": False,
                "message": "Invalid access code",
                "data": None,
            }
            return Response(data=result, status=status.HTTP_400_BAD_REQUEST)

        _response = _credit_health_details.get("response")
        result = _response.get("data")
        credit_details = result.get("credit_details")
        business_details = result.get("business_details")
        credit_health_customer = business_details.get("credit_health_customer")
        loan_summary = credit_details.get("response_payload")

        borrower_phone_number = credit_health_customer.get("phone_number")

        _ajo_user = AjoUser.objects.filter(
            phone_number__startswith=borrower_phone_number
        )

        try:
            _summary = parse_to_dict(loan_summary)
            liberty_life_summary = _summary["summary"]
        except (KeyError, ValueError) as err:

            CreditHealthRecord.create_record(
                agent=request.user,
                borrower_phone_number=borrower_phone_number,
                loan_access_code=loan_access_code,
                business_details=business_details,
                liberty_life_summary={
                    "error": {"value": loan_summary, "msg": str(err)}
                },
            )

            result = {
                "status": False,
                "message": "Unable to proceed, please contact support.",
                "data": None,
            }
            return Response(data=result, status=status.HTTP_400_BAD_REQUEST)

        CreditHealthRecord.create_record(
            agent=request.user,
            borrower_phone_number=borrower_phone_number,
            loan_access_code=loan_access_code,
            business_details=business_details,
            liberty_life_summary=liberty_life_summary,
            request_otp=True,
        )

        if _ajo_user.exclude(onboarding_source=OnboardingSource.CREDIT_HEALTH).exists():
            result = {
                "status": False,
                "message": "This service is only available to Liberty Life users. please contact support.",
                "data": None,
            }
            return Response(data=result, status=status.HTTP_400_BAD_REQUEST)

        if (
            _ajo_user.filter(onboarding_source=OnboardingSource.CREDIT_HEALTH)
            .exclude(user=request.user)
            .exists()
        ):
            result = {
                "status": False,
                "message": "The user is already associated with another agent. For further assistance, please contact support.",
                "data": None,
            }
            return Response(data=result, status=status.HTTP_400_BAD_REQUEST)

        business_details.update(liberty_life_summary)

        if not borrower_phone_number:
            result = {
                "status": False,
                "message": "Unable to send OTP. Please contact support.",
                "data": None,
            }
            return Response(data=result, status=status.HTTP_400_BAD_REQUEST)

        if environment != "development":
            sms = OTP.sms_voice_otp(
                otp_type=OTPType.SMS, phone_number=borrower_phone_number
            )
        else:
            sms = {"status": True, "data": {"status": True, "error": ""}}

        sms.pop("otp", None)
        sms["loan_access_code"] = loan_access_code
        result = {
            "status": True,
            "ussd_code": "*347*180*22#",
            "message": f"An OTP has been sent to {borrower_phone_number}. Please check your messages.",
            "data": sms,
        }
        return Response(data=result, status=status.HTTP_200_OK)


class VerifyOtpCreateUserAndSavingsUnderAgent(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    serializer_class = VerifyOtpCreateUserAndSavingsUnderAgentSerializer

    def post(self, request):

        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        loan_access_code = validated_data["loan_access_code"]
        request_instance = validated_data["request_instance"]
        CreditHealthAgentRequest.create_borrower(request_instance=request_instance)
        result = {
            "status": True,
            "message": f"User has successfully been verified and added to the Ajo users list for savings and loan processing.",
            "data": {"loan_access_code": loan_access_code},
        }
        return Response(data=result, status=status.HTTP_200_OK)


class UpdateLoanActualEnddate(APIView):
    """
    To add a new column for the correct end date of an open
    loan.
    """

    def get(self, request):
        open_loans = AjoLoan.objects.filter(
            status=LoanStatus.OPEN,
            end_date_issues=False,
        )

        for loan_instance in open_loans:
            loan_calc = AjoLoan.compute_interest_with_duration(
                principal=loan_instance.amount,
                duration=loan_instance.actual_duaration,
                loan_type=loan_instance.loan_type,
                ajouser=loan_instance.borrower,
                start_date=loan_instance.start_date,
            )

            expected_end_date = loan_calc.get("end_date")
            loan_instance.expected_end_date = expected_end_date
            loan_instance.end_date_issue = loan_instance.end_date != expected_end_date
            loan_instance.save()

        return Response(
            {"message": "Open loans end dates resolved successfully"},
            status=status.HTTP_200_OK,
        )


class GetGuarantorDetailsByVerificationID(APIView):
    # permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):

        verification_number = request.GET.get("verification_number")
        query_type = request.GET.get("query_type")
        if query_type == "GUARANTOR":
            guarantor_qs = LoanGuarantor.objects.filter(
                verification_number=verification_number
            )
            return Response(
                data={
                    "status": False,
                    "message": "success",
                    "result": guarantor_qs.values(),
                },
                status=status.HTTP_200_OK,
            )
        else:
            borrower_infoqs = BorrowerInfo.objects.filter(
                verification_number=verification_number
            )
            return Response(
                data={
                    "status": False,
                    "message": "success",
                    "result": borrower_infoqs.values(),
                },
                status=status.HTTP_200_OK,
            )


class GetHealthPlanDetailsByIDorPhone(APIView):
    # permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):

        _id = request.GET.get("id")
        query_type = request.GET.get("query_type")
        if query_type == "by_id":
            _insurance_qs = HealthInsurance.objects.filter(id=_id)
            return Response(
                data={
                    "status": True,
                    "message": "success",
                    "result": _insurance_qs.values(),
                },
                status=status.HTTP_200_OK,
            )
        else:
            _insurance_qs = HealthInsurance.objects.filter(
                ajo_user__phone_number__startswith=_id
            )
            return Response(
                data={
                    "status": True,
                    "message": "success",
                    "result": _insurance_qs.values(),
                },
                status=status.HTTP_200_OK,
            )


class ApproveEligiblityInstance(APIView):
    serializer_class = ApproveEligibilitySerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        eligibility_id = serializer.validated_data["eligibility_id"]
        email = serializer.validated_data["email"]
        password = serializer.validated_data["password"]
        approved_amount = serializer.validated_data["approved_amount"]
        reason = serializer.validated_data["reason"]

        success, message = LoanEligibility.approve_eligibility(
            eligibility_id=eligibility_id,
            email=email,
            password=password,
            approved_amount=approved_amount,
            reason=reason,
        )

        if not success:
            return Response(
                {"status": False, "message": message},
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response({"status": True, "message": message}, status=status.HTTP_200_OK)


class GetBureauDataView(APIView):
    serializer_class = GetBureauDataSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        verification_id = serializer.validated_data["verification_id"]

        try:
            bureau_data = CreditBureauMetaData.get_bureau_data_by_verification_id(
                verification_id=verification_id
            )

            if not bureau_data:
                return Response(
                    {
                        "status": False,
                        "message": "No bureau data found for this verification ID",
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )

            return Response(
                {
                    "status": True,
                    "message": "Bureau data retrieved successfully",
                    "data": bureau_data,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": False, "message": str(e)}, status=status.HTTP_400_BAD_REQUEST
            )


class AdminLoanKYCDocumentationView(APIView):
    serializer_class = LoanKYCDocumentationAdminSerializer

    def get(self, request):
        user_phone_number = request.query_params.get("phone_number")
        if not user_phone_number:
            return Response(
                {"status": False, "message": "Phone number is required."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        documentation_records = LoanKYCDocumentation.objects.filter(
            borrower__phone_number__startswith=user_phone_number
        )

        if not documentation_records.exists():
            return Response(
                {"status": False, "message": "No documentation records found."},
                status=status.HTTP_404_NOT_FOUND,
            )
        # documentation_records.order_by("-id").values(
        #             "id",
        #             "unique_ref",
        #             "has_loan",
        #             "loan_amount",
        #             "loan_tenure",
        #             "documentation_status",
        #             "borrower_phone_number",
        #             "verify_phone_number",
        #             "borrower_first_name",
        #             "borrower_middle_name",
        #             "borrower_last_name",
        #             "paid_processing_fee",
        #             "processing_fee",
        #             "borrower_nin_bvn_state",
        #             "agent_is_not_borrower_state",
        #             "borrower_livelines_state",
        #             "borrower_facematch_state",
        #             "crc_state",
        #             "guarantor_nin_bvn_state",
        #             "guarantor_facematch_state",
        #             "guarantor_livelines_state",
        #             "agent_is_not_guarantor_state",
        #             "failed_stages",
        #             "checks",
        #         )
        return Response(
            {
                "status": True,
                "message": "Documentation records retrieved successfully.",
                "data": documentation_records.order_by("-id"),
            },
            status=status.HTTP_200_OK,
        )

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        documentation_id = serializer.validated_data.get("documentation_id")
        new_amount = serializer.validated_data.get("new_amount")
        # phone_number = serializer.validated_data.get("phone_number")

        try:
            documentation_record = LoanKYCDocumentation.objects.get(id=documentation_id)

            documentation_record.loan_amount = new_amount
            documentation_record.save()
            result = {
                key: value
                for key, value in documentation_record.__dict__.items()
                if not key.startswith("_")
            }
            return Response(
                {
                    "status": True,
                    "data": result,
                    "message": "Loan amount updated successfully.",
                },
                status=status.HTTP_200_OK,
            )
        except LoanKYCDocumentation.DoesNotExist:
            return Response(
                {"status": False, "message": "Documentation record not found."},
                status=status.HTTP_404_NOT_FOUND,
            )


class AdminUpdateDisbursementDateAPIView(APIView):

    def get(self, request):
        loan_id = request.query_params.get("loan_id")
        back_date_count = request.query_params.get("back_date_count")

        try:
            ajo_loan = AjoLoan.objects.get(id=loan_id)
        except AjoLoan.DoesNotExist as err:

            return Response(
                {"status": False, "message": str(err)},
                status=status.HTTP_400_BAD_REQUEST,
            )

        date_disbursed = datetime.now() - timedelta(days=int(back_date_count))

        loan_calc = AjoLoan.compute_interest_with_duration(
            principal=ajo_loan.amount,
            duration=ajo_loan.expected_repayment_count,
            loan_type=ajo_loan.loan_type,
            ajouser=ajo_loan.borrower,
            date_disbursed=date_disbursed.date(),
        )
        start_date = loan_calc["start_date"]
        end_date = loan_calc["end_date"]
        ajo_loan.interest_amount = loan_calc["interest_amount"]
        ajo_loan.interest_rate = loan_calc["interest_rate"]
        ajo_loan.end_date = end_date
        ajo_loan.start_date = start_date
        ajo_loan.repayment_amount = loan_calc["repayment_amount"]
        ajo_loan.tenor_in_days = loan_calc["tenor_in_days"]
        ajo_loan.daily_repayment_amount = loan_calc["daily_repayment_amount"]
        ajo_loan.date_disbursed = date_disbursed

        ajo_loan.save(
            update_fields=[
                "interest_amount",
                "interest_rate",
                "end_date",
                "start_date",
                "repayment_amount",
                "tenor_in_days",
                "daily_repayment_amount",
                "date_disbursed",
            ]
        )

        return Response(
            {"status": True, "message": loan_calc},
            status=status.HTTP_200_OK,
        )


#####################################################################
#####################################################################
##################################################################### Merchant loan
class MerchantGetEligiblilityStatus(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    # serializer_class = VerifyOtpCreateUserAndSavingsUnderAgentSerializer

    def get(self, request):
        pass

    def post(self, request):

        # serializer = self.serializer_class(
        #     data=request.data, context={"request": request}
        # )
        # serializer.is_valid(raise_exception=True)
        # validated_data = serializer.validated_data
        # loan_access_code = validated_data["loan_access_code"]
        # request_instance = validated_data["request_instance"]
        # CreditHealthAgentRequest.create_borrower(request_instance=request_instance)
        # result = {
        #     "status": True,
        #     "message": f"User has successfully been verified and added to the Ajo users list for savings and loan processing.",
        #     "data": {"loan_access_code": loan_access_code},
        # }
        # return Response(data=result, status=status.HTTP_200_OK)
        pass


class GetEligibleMerchant(generics.ListAPIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    pagination_class = CustomPagination
    serializer_class = GetEligibleMerchantSerializer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    search_fields = ("phone_number", "email")

    def get_queryset(self):

        summaries = MerchantEligibilitySummary.objects.filter(
            eligible_amount__gt=0, eligible=True
        )

        return summaries

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)

        response_data = {
            "status": True,
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }

        return Response(response_data, status=status.HTTP_200_OK)


class GetSmsSummaryAPIView(APIView):

    def get(self, request):

        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        # Set start_date to today if not provided
        if not start_date:
            start_date = (datetime.today() - timedelta(days=7)).strftime("%Y-%m-%d")

        # Set end_date to today if not provided
        if not end_date:
            end_date = datetime.today().strftime("%Y-%m-%d")

        # Convert start_date and end_date to datetime objects
        start_date = make_aware(datetime.strptime(start_date, "%Y-%m-%d"))
        end_date = make_aware(datetime.strptime(end_date, "%Y-%m-%d"))

        # Total repayment SMS count
        # repayment_sms_sent_qs = AjoLoanRepayment.objects.filter(
        #     paid_date__date__gte=start_date
        # )

        # repayment_sms_sent = repayment_sms_sent_qs.count()
        # repayment_sms_delivered = repayment_sms_sent_qs.filter(
        #     borrower_notified=True
        # ).count()

        daily_repayment_record = SMSLog.objects.filter(
            sent_at__date__gte=start_date,
            sent_at__date__lte=end_date,
            msg_type="DAILY_REPAYMENT",
        )
        daily_repayment_sms_sent = daily_repayment_record.count()
        daily_repayment_sms_delivered = daily_repayment_record.filter(sent=True).count()

        loan_statement_sms_sent_qs = SMSLog.objects.filter(
            sent_at__date__gte=start_date,
            sent_at__date__lte=end_date,
            msg_type="LOAN_STATEMENT",
        )

        loan_statement_sms_sent = loan_statement_sms_sent_qs.count()
        loan_statement_sms_delivered = loan_statement_sms_sent_qs.filter(
            sent=True
        ).count()

        # Weekly SMS count
        weekly_sms_sent_qs = SMSLog.objects.filter(
            sent_at__date__gte=start_date,
            sent_at__date__lte=end_date,
            msg_type="WEEKLY_SUMMARY",
        )

        weekly_sms_sent = weekly_sms_sent_qs.count()
        weekly_sms_delivered = weekly_sms_sent_qs.filter(sent=True).count()

        # Past maturity SMS count
        past_maturity_sms_sent_qs = SMSLog.objects.filter(
            sent_at__date__gte=start_date,
            sent_at__date__lte=end_date,
            msg_type="PAST_MATURITY",
        )

        past_maturity_sms_sent = past_maturity_sms_sent_qs.count()
        past_maturity_sms_delivered = past_maturity_sms_sent_qs.filter(
            sent=True
        ).count()

        total_sent = (
            daily_repayment_sms_sent
            + loan_statement_sms_sent
            + weekly_sms_sent
            + past_maturity_sms_sent
        )
        total_delivered = (
            daily_repayment_sms_delivered
            + loan_statement_sms_delivered
            + weekly_sms_delivered
            + past_maturity_sms_delivered
        )
        summary_result = {
            "total_sent": total_sent,
            "total_delivered": total_delivered,
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d"),
            # "repayment_sms_sent": {
            #     "count": repayment_sms_sent,
            #     "delivered": repayment_sms_delivered,
            # },
            "daily_repayment_sms_sent": {
                "count": daily_repayment_sms_sent,
                "delivered": daily_repayment_sms_delivered,
            },
            "loan_statement_sms_sent": {
                "count": loan_statement_sms_sent,
                "delivered": loan_statement_sms_delivered,
            },
            "weekly_sms_sent": {
                "count": weekly_sms_sent,
                "delivered": weekly_sms_delivered,
            },
            "past_maturity_sms_sent": {
                "count": past_maturity_sms_sent,
                "delivered": past_maturity_sms_delivered,
            },
        }

        return Response(
            {"status": True, "message": summary_result}, status=status.HTTP_200_OK
        )


class SendSeedsPayrollToPayboxView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        import pandas as pd
        import json

        from collections_app.models import PayrollLog

        last_payroll_date = PayrollLog.objects.latest("created_at").last_payroll_date

        paybox_data_batch_list = []
        agent_details_list = []

        temp_metrics_file = pd.read_excel("April Payroll Seeds.xlsx")
        temp_metrics_json = temp_metrics_file.to_json(orient="records")
        temp_metrics_list = json.loads(temp_metrics_json)

        for record in temp_metrics_list:
            net_amount = record.get("net_amount", 0.00)
            print("ORIGINAL PAYABLE AMOUNT:::::::::::::::::;")
            print(record.get("Gross"))
            print(type(record.get("Gross")))
            payable_amount = (
                float(record.get("Gross", 0.00))
                if isinstance(record.get("Gross", 0.00), int)
                or isinstance(record.get("Gross", 0.00), float)
                else 0.00
            )
            bonus = record.get("Commission", 0.00)
            print(":::::::::::::::::::FINAL PAYABLE AMOUNT COMING IN:::::::::::::")
            print(":::::::::::::::::::FINAL PAYABLE AMOUNT COMING IN:::::::::::::")
            print(payable_amount)
            print("::::::::::::::MORE CHECKS::::::::::::::::::::::")
            print("::::::::::::::MORE CHECKS::::::::::::::::::::::")
            print(type(payable_amount))
            agent_email = record.get("agent_email")

            if agent_email is None or "@" not in agent_email:
                print("INVALID EMAIL:::::::::::::::::::::::::::")
                print("INVALID EMAIL:::::::::::::::::::::::::::")
                print("INVALID EMAIL:::::::::::::::::::::::::::")
                print(agent_email)
                print("INVALID EMAIL:::::::::::::::::::::::::::")
                print("INVALID EMAIL:::::::::::::::::::::::::::")
                print("INVALID EMAIL:::::::::::::::::::::::::::")
                continue

            # balance = net_amount - payable_amount

            data = {
                "email": record.get("agent_email").strip(),
                "net_amount": net_amount,
                "payable_amount": payable_amount,
                "bonus": bonus,
            }
            paybox_data_batch_list.append(data)

        data = {"employees": paybox_data_batch_list}

        PayrollLog.objects.create(
            request_data=data,
            response_data={},
            last_payroll_date=last_payroll_date,
            agent_details_data={"data": agent_details_list},
        )
        result = "Run successfully"

        return Response({"status": True, "message": "sent"}, status=status.HTTP_200_OK)


class GetMerchantEligibilitySummary(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    serializer_class = GetEligibleMerchantSerializer

    def get(self, request):
        request_user = request.user
        customer_user_id = request_user.customer_user_id

        processing_stage = [
            "OPEN_FOR_PROCESSING",
            "AWAITING_PROCESSING_FEE",
            # "CREDIT_WORTHINESS_CHECK",
            # "ACCOUNT_NUMBER_ENTRY",
            # "DISBURSEMENT",
            # "PROCESSING_COMPLETE",
            # "LOAN_AMOUNT_ENTRY",
        ]

        eligibility_instance = MerchantEligibilitySummary.objects.filter(
            user_id=customer_user_id,
            application_stage__in=processing_stage,
        ).last()
        return Response(
            {
                "status": False,
                "message": "Success",
                "amount": eligibility_instance.eligible_amount if eligibility_instance else 0,
            },
            status=status.HTTP_200_OK,
        )

    def post(self, request):
        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        # serializer.is_valid(raise_exception=True)

        const = ConstantTable.get_constant_table_instance()
        merchant_loan_config = const.merchant_loan_config
        admin_user_type = merchant_loan_config.get("user_type")
        admin_kyc_level = merchant_loan_config.get("kyc_level")

        request_user = request.user
        customer_user_id = request_user.customer_user_id
        phone_number = normalize_ngn_phone(request_user.user_phone)

        borrower = AjoUser.objects.filter(
            user=request_user, phone_number__startswith=phone_number
        ).last()
        available_loan_status = [LoanStatus.OPEN, LoanStatus.OPEN_TO_SUPERVISOR]

        if borrower:
            loan = AjoLoan.objects.filter(
                agent=request_user,
                borrower=borrower,
                status__in=available_loan_status,
            ).last()
            if loan:
                return Response(
                    {
                        "status": True,
                        "message": "success",
                        "loan": {
                            "loan_id": loan.id,
                            "loan_amount": loan.amount,
                            "borrower_full_name": borrower.fullname,
                            "phone_number": borrower.phone,
                            "duration": loan.expected_repayment_count,
                            "date_disbursed": loan.date_disbursed,
                            "loan_status": loan.status,
                            "end_date": loan.end_date,
                            "start_date": loan.start_date,
                            "total_paid_amount": loan.total_paid_amount,
                            "repayment_amount": loan.repayment_amount,
                            "daily_repayment_amount": loan.daily_repayment_amount,
                            "missed_repayments_count": int(loan.outstanding_days_today),
                            "amount_due": loan.outstanding_due_today,
                            "outstanding": loan.outstanding_due,
                        },
                        "eligibility_info": None,
                    },
                    status=status.HTTP_200_OK,
                )

        processing_stage = [
            "OPEN_FOR_PROCESSING",
            "AWAITING_PROCESSING_FEE",
            "CREDIT_WORTHINESS_CHECK",
            "ACCOUNT_NUMBER_ENTRY",
            "DISBURSEMENT",
            "PROCESSING_COMPLETE",
            "LOAN_AMOUNT_ENTRY",
        ]

        eligiblity_summary = MerchantEligibilitySummary.objects.filter(
            user_id=customer_user_id,
            application_stage__in=processing_stage,
        )
        if not eligiblity_summary.exists():
            return Response(
                {
                    "status": False,
                    "message": "Sorry, you are not eligible yet. Perform more transactions DAILY to qualify for a loan.",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if eligiblity_summary.filter(confirmed_info=True).exists():
            return Response(
                {
                    "status": True,
                    "message": "success",
                    "loan": None,
                    "eligibility_info": eligiblity_summary.values()[0],
                },
                status=status.HTTP_200_OK,
            )

        agency_banking_handler = LibertyPayMgr(config=settings)
        get_user_info = agency_banking_handler.get_agency_banking_users(
            id=customer_user_id
        )
        if get_user_info.get("status") == "success":
            user_info = get_user_info.get("response")[0]
            bvn = user_info.get("bvn")
            guarantor = user_info.get("guarantors")
            phone_number = normalize_ngn_phone(user_info.get("phone_number"))
            first_name = user_info.get("first_name")
            last_name = user_info.get("last_name")
            street = user_info.get("street")
            state = user_info.get("state")
            nearest_landmark = user_info.get("nearest_landmark")
            account_number = user_info.get("account_number")
            date_joined_str = user_info.get("date_joined")
            bank_code = user_info.get("bank_code")
            date_of_birth = user_info.get("date_of_birth")
            nearest_landmark = user_info.get("nearest_landmark")
            lga = user_info.get("lga")
            kyc_level = user_info.get("kyc_level", 0)
            user_type = user_info.get("type_of_user")

            request_user.kyc_level = kyc_level
            request_user.bvn_number = str(bvn) if bvn is not None else None
            request_user.save(update_fields=["kyc_level", "bvn_number"])

            ajo_user = AjoUser.objects.filter(
                phone_number__startswith=phone_number, user=request_user
            )

            try:
                dob = datetime.strptime(date_of_birth, "%d-%m-%Y").date()
            except ValueError as err:
                try:
                    dob = datetime.strptime(date_of_birth, "%Y-%m-%d").date()
                except ValueError:
                    dob = None

            if not ajo_user:
                ajo_user = AjoUser.objects.create(
                    user=request_user,
                    phone_number=phone_number,
                    first_name=first_name,
                    last_name=last_name,
                    # marital_status
                    dob=dob,
                    state=state,
                    lga=lga,
                    address=street,
                    bvn=bvn,
                    landmark=nearest_landmark,
                )
            else:
                ajo_user.update(
                    dob=dob,
                    state=state,
                    lga=lga,
                    first_name=first_name,
                    last_name=last_name,
                    address=street,
                    bvn=bvn,
                    landmark=nearest_landmark,
                )

            if user_type != admin_user_type:
                return Response(
                    {
                        "status": False,
                        "message": f"Thank you for your Interest, please contact customer support for assistance with User Type",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Convert the date_joined string to a datetime object
            date_joined = datetime.strptime(date_joined_str[:10], "%Y-%m-%d")
            # Convert date_const string to a datetime object
            date_const_str = "2025-05-12"
            date_const = datetime.strptime(date_const_str, "%Y-%m-%d")

            # Compare the dates
            if date_joined > date_const:

                if kyc_level < admin_kyc_level:
                    return Response(
                        {
                            "status": False,
                            "message": f"KYC level ({kyc_level}) is insufficient",
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                if not guarantor or (
                    isinstance(guarantor, list) and not guarantor[0].get("phone_number")
                ):
                    return Response(
                        {
                            "status": False,
                            "message": "Guarantor is required. Kindly update your profile",
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            else:
                if kyc_level < 2:
                    return Response(
                        {
                            "status": False,
                            "message": f"KYC level ({kyc_level}) is insufficient",
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            eligiblity_summary_instance = eligiblity_summary.last()
            eligiblity_summary_instance.confirmed_info = True
            eligiblity_summary_instance.save(update_fields=["confirmed_info"])

            ###################
            # create user withdrawal account details
            vfd_bankcodes = ["090110", "999999"]
            bank_code = "090110" if bank_code == "999999" else bank_code
            AjoUserWithdrawalAccountService.set_withdrawal_account(
                data={
                    "account_number": account_number,
                    "account_name": f"{first_name} {last_name}",
                    "user_id": request_user.id,
                    "bank_code": bank_code,
                    "bank_name": (
                        "VFD Microfinance Bank" if bank_code in vfd_bankcodes else ""
                    ),
                    "ajo_user": ajo_user.last(),
                }
            )
            ###################

            return Response(
                {
                    "status": True,
                    "message": "success",
                    "eligibility_info": eligiblity_summary.values()[0],
                },
                status=status.HTTP_200_OK,
            )

        else:
            return Response(
                {
                    "status": False,
                    "message": "Unable to get user info",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )


class ChargeMerchantLoanProccessingFee(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    serializer_class = ChargeMerchantLoanProccessingFeeSerializer

    def post(self, request):
        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        liberty_mgr = validated_data["liberty_mgr"]
        loan_processing_fee = validated_data["loan_processing_fee"]
        eligibility_summary = validated_data["eligibility_summary"]

        description = f"Loan Processing Fee charged from the user's agent wallet for loan processing"

        transfer_transaction = TransactionService.create_internal_transfer_between_accounts(
            user=request.user,
            amount=loan_processing_fee,
            transaction_description=description,
            transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
            transaction_form_type=TransactionFormType.AJO_LOAN_CHARGE_FEE,
            status=Status.PENDING,
            # ajo_user=ajo_user,
            transfer_provider=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
        )
        reference = str(transfer_transaction.transaction_id)
        charge_user_agency_banking_wallet = liberty_mgr.charge_user_wallet(
            user_id=request.user.customer_user_id,
            unique_reference=reference,
            narration=description,
            amount=loan_processing_fee,
        )

        if (
            charge_user_agency_banking_wallet.get("status") == "success"
            and charge_user_agency_banking_wallet.get("response").get("message")
            == "success"
        ):
            debit_status = Status.SUCCESS
        else:
            debit_status = Status.FAILED

        transfer_transaction.status = debit_status
        transfer_transaction.payload = charge_user_agency_banking_wallet
        transfer_transaction.save(update_fields=["payload", "status"])

        request_status = True if debit_status == Status.SUCCESS else False
        charge_user_agency_banking_wallet.pop("params")

        if request_status is True:
            eligibility_summary.application_stage = "CREDIT_WORTHINESS_CHECK"
            eligibility_summary.save(update_fields=["application_stage"])

        return Response(
            {
                "status": request_status,
                "message": "success",
                "result": charge_user_agency_banking_wallet,
            },
            status=(
                status.HTTP_200_OK if request_status else status.HTTP_400_BAD_REQUEST
            ),
        )


class GetCreditWorthinessApiView(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    serializer_class = GetCreditWorthinessSerializer

    def post(self, request):
        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        eligibility_summary = validated_data["eligibility_summary"]
        request_user = request.user
        bvn_number = request_user.bvn_number
        phone_number = normalize_ngn_phone(request_user.user_phone)
        constant = ConstantTable.get_constant_table_instance()
        debt_threshold = constant.debt_threshold
        debt_institution_count = constant.debt_institution_count

        ajo_user = AjoUser.objects.filter(
            user=request_user, phone_number__startswith=phone_number
        ).last()

        ajo_user_id = ajo_user.id
        admin_emails = ActionPermission.get_customer_service_emails()

        if request_user.email not in admin_emails:
            credit_bureau = FirstcentralCreditCheck(
                bvn=bvn_number,
                debt_threshold=debt_threshold,
                ajo_user=ajo_user,
                max_debt_institution_count=debt_institution_count,
            ).get_credit_status()
        else:
            credit_bureau = {
                "status": True,
                "reason": "Credit Bureau check not required",
                "high_outstanding_debt": False,
                "max_debt_institution_count": debt_institution_count,
                "total_outstanding": 0,
                "count_of_open_loans": 0,
                "open_loan_institutions": [],
                "bad_loans_institions_count": 0,
                "bad_loans_institions": [],
                "user_behaviour": "",
            }

        bureau_status = credit_bureau.get("status", False)
        request_status = True

        eligibility_summary.failed_bureau_check = not bureau_status

        if request_status is True:
            if (
                MerchantDirectDebitAccount.objects.filter(user=request_user).count()
                >= 3
            ):
                eligibility_summary.application_stage = "LOAN_AMOUNT_ENTRY"
            else:
                eligibility_summary.application_stage = "ACCOUNT_NUMBER_ENTRY"
            eligibility_summary.save(
                update_fields=["application_stage", "failed_bureau_check"]
            )

        msg = credit_bureau.get("reason")

        ## create all virtual wallet
        create_cash_connect_account_handler.apply_async(
            queue="communicationman",
            kwargs={
                "ajo_user_id": ajo_user_id,
            },
        )

        create_wema_account_handler.apply_async(
            queue="communicationman",
            kwargs={
                "ajo_user_id": ajo_user_id,
            },
        )

        return Response(
            {
                "status": request_status,
                "bvn_number": bvn_number,
                "application_stage": eligibility_summary.application_stage,
                "message": msg,
                "result": credit_bureau,
            },
            status=status.HTTP_200_OK,
        )


class MerchantDirectDebitAccountCreateView(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    serializer_class = MerchantDirectDebitAccountSerializer

    def post(self, request):
        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        acct_list = validated_data["account_list"]
        eligibility_summary = validated_data["eligibility_summary"]

        instances = []
        for item in acct_list:
            instance = MerchantDirectDebitAccount(
                user=request.user,
                account_number=item["account_number"],
                account_name=item["account_name"],
                bank_name=item["bank_name"],
                bank_code=item["bank_code"],
            )
            instances.append(instance)
        MerchantDirectDebitAccount.objects.bulk_create(instances)
        eligibility_summary.application_stage = "LOAN_AMOUNT_ENTRY"
        eligibility_summary.save(update_fields=["application_stage"])
        validated_data.pop("eligibility_summary")
        return Response(
            {
                "status": True,
                "application_stage": "LOAN_AMOUNT_ENTRY",
                "message": "Accounts created successfully.",
                "result": validated_data,
            },
            status=status.HTTP_201_CREATED,
        )


class DisburseMerchantLoan(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    serializer_class = DisburseMerchantLoanSerializer

    def post(self, request):
        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        eligibility_summary = validated_data["eligibility_summary"]
        loan_amount = validated_data["loan_amount"]
        _result = eligibility_summary.handle_disbursement(loan_amount=loan_amount)

        return Response(
            {
                "status": True,
                "message": "success",
                "result": _result,
            },
            status=(status.HTTP_200_OK),
        )


class MerchantLoanRepayment(APIView):
    permission_classes = (permissions.IsAuthenticated, IsBlackListed, IsLoanAvailable)
    serializer_class = MerchantLoanRepaymentSerializer

    def get(self, request):
        request_user = self.request.user
        phone_number = normalize_ngn_phone(request_user.user_phone)
        ajo_user = AjoUser.objects.filter(
            user=request_user, phone_number__startswith=phone_number
        ).last()
        const = ConstantTable.get_constant_table_instance()

        bank_info = BankAccountDetails.objects.filter(
            user=request_user,
            ajo_user=ajo_user,
            account_provider=const.loan_collection_channel,  # CASH_CONNECT, WEMA
            form_type=AccountFormType.LOAN_REPAYMENT,
        ).last()

        if not bank_info:
            bank_info = BankAccountDetails.objects.filter(
                user=request_user,
                ajo_user=ajo_user,
                form_type=AccountFormType.LOAN_REPAYMENT,
            ).last()

        if bank_info:
            data = {
                "account_number": bank_info.account_number,
                "bank_name": bank_info.bank_name,
                "account_name": bank_info.account_name,
            }

        else:
            data = {"account_number": None, "bank_name": None, "account_name": None}

        return Response(
            {
                "status": True,
                "message": "success",
                "result": data,
            },
            status=(status.HTTP_200_OK),
        )

    def post(self, request):
        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        user = request.user
        ajo_loan = validated_data["ajo_loan"]
        amount = validated_data["amount"]
        liberty_mgr = validated_data["liberty_mgr"]

        description = f"{amount} was paid in for loan id: {ajo_loan.id}"
        # Create transfer transaction
        transfer_transaction = (
            TransactionService.create_internal_transfer_between_accounts(
                user=user,
                amount=amount,
                transaction_description=description,
                transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
                transaction_form_type=TransactionFormType.LOAN_REPAYMENT,
                status=Status.PENDING,
                transfer_provider=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
            )
        )

        # Charge user's wallet
        reference = str(transfer_transaction.transaction_id)
        charge_result = liberty_mgr.charge_user_wallet(
            user_id=user.customer_user_id,
            unique_reference=reference,
            narration=description,
            amount=amount,
        )
        # charge_result = {
        #     "status": "success",
        #     "response": {"message": "success"},
        # }
        # Update transaction status
        if (
            charge_result.get("status") == "success"
            and charge_result.get("response", {}).get("message") == "success"
        ):
            txn_status = Status.SUCCESS

            # Create repayment record
            success, repayment_record = AjoLoanRepayment.loan_repayment(
                loan=ajo_loan,
                amount=amount,
                from_wallet=None,
                repayment_type=RepaymentType.MERCHANT_DEBIT,
                transaction_source_trx=transfer_transaction,
            )

        else:
            txn_status = Status.FAILED
        transfer_transaction.status = txn_status

        transfer_transaction.payload = charge_result
        transfer_transaction.save(update_fields=["payload", "status"])

        return Response(
            {
                "status": True if txn_status == Status.SUCCESS else False,
                "message": (
                    "success"
                    if txn_status == Status.SUCCESS
                    else "Unable to charge user wallet. Please contact support."
                ),
                "result": None,
            },
            status=(
                status.HTTP_200_OK
                if txn_status == Status.SUCCESS
                else status.HTTP_400_BAD_REQUEST
            ),
        )


class UpdateAccountBalancesAPIView(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):
        from django.utils import timezone
        from accounts.agency_banking import check_balances

        today = timezone.localdate()
        today_transaction = Transaction.objects.filter(
            transaction_form_type=TransactionFormType.DISBURSE_TO_EXTERNAL_ACCOUNT,
            wallet_type=WalletTypes.LOAN_DISBURSEMENT,
            status=Status.SUCCESS,
            date_created__date=today,
        )
        # TOTAL
        total_disbursement_today = today_transaction.aggregate(
            total_amount=Sum("amount")
        )
        total_disbursement_today = total_disbursement_today["total_amount"]
        if total_disbursement_today is None:
            total_disbursement_today = 0
        # print(total_disbursement_today)

        # all_time_disbursements
        alltime__disbursement_transaction = Transaction.objects.filter(
            transaction_form_type=TransactionFormType.AJO_LOAN_DISBURSEMENT,
            status=Status.SUCCESS,
        )
        alltime_total_disbursement = alltime__disbursement_transaction.aggregate(
            total_amount=Sum("amount")
        )
        alltime__disbursement_amount = alltime_total_disbursement["total_amount"]
        # print(alltime__disbursement_amount)

        repayments = AjoLoanRepayment.objects.all()
        alltime_total_repayment = repayments.aggregate(
            total_amount=Sum("repayment_amount")
        )
        alltime_repayment = alltime_total_repayment["total_amount"]
        # print(alltime_repayment,)

        todays_repayments = AjoLoanRepayment.objects.filter(created_at__date=today)
        today_total_repayment = todays_repayments.aggregate(
            total_amount=Sum("repayment_amount")
        )
        todays_repayment = today_total_repayment["total_amount"] or 0

        commissions_transaction = Transaction.objects.filter(
            transaction_form_type=TransactionFormType.AJO_LOAN_CHARGE_FEE,
            wallet_type=WalletTypes.AJO_AGENT,
            status=Status.SUCCESS,
        )
        commissions_transaction = commissions_transaction.aggregate(
            total_amount=Sum("amount")
        )
        all_time_commissions = commissions_transaction["total_amount"]
        commissions_balance = check_balances(login_purpose="COMMISSION")
        escrow_balance = check_balances(login_purpose="ESCROW")
        repayment_balance = check_balances(login_purpose="REPAYMENT")
        savings_balance = check_balances(login_purpose="SAVINGS")
        loans_balance = check_balances(login_purpose="LOAN")
        # loans_balance = get_loan_balance()

        # # total_savings_without_Loans
        total_savings_without_Loans = WalletSystem.objects.exclude(
            wallet_type__in=[
                "AJO_LOAN",
                "PROSPER_LOAN_WALLET",
                "AJO_LOAN_ESCROW",
                "LOAN_DISBURSEMENT",
                "AJO_LOAN_REPAYMENT",
            ]
        ).aggregate(total=Sum("available_balance"))["total"]
        difference = savings_balance - total_savings_without_Loans

        today_escrow_transaction = Transaction.objects.filter(
            transaction_form_type=TransactionFormType.AJO_LOAN_ESCROW_HOLDING,
            wallet_type=WalletTypes.AJO_LOAN_ESCROW,
            status=Status.SUCCESS,
        )
        total_escrow = today_escrow_transaction.aggregate(total_amount=Sum("amount"))
        all_time_escrow_balance = total_escrow["total_amount"]

        try:
            monnify_account_balance = Monnify().check_monnify_balance()
            monnify_account_balance = monnify_account_balance.get("responseBody").get(
                "availableBalance"
            )
        except Exception as e:
            monnify_account_balance = 0.00

        available_float = sum(
            [
                monnify_account_balance,
                loans_balance,
                commissions_balance,
                escrow_balance,
                repayment_balance,
            ]
        )
        # print(available_float)
        performance_statuses = [
            LoanPerformanceStatus.PERFORMING,
            LoanPerformanceStatus.DEFAULTED,
            LoanPerformanceStatus.LOST,
            LoanPerformanceStatus.PAST_MATURITY,
            LoanPerformanceStatus.OWED_BALANCE,
        ]
        all_loans = AjoLoan.objects.filter(
            status=LoanStatus.OPEN, performance_status__in=performance_statuses
        )
        total_loan_repayment = all_loans.aggregate(total_amount=Sum("repayment_amount"))
        total_loan_repayment = total_loan_repayment["total_amount"]
        total_paid_repayment = all_loans.aggregate(
            total_amount=Sum("total_paid_amount")
        )
        total_paid_repayment = total_paid_repayment["total_amount"]

        spend_wallet_balance = (
            WalletSystem.objects.filter(wallet_type=WalletTypes.AJO_SPENDING).aggregate(
                total_amount=Sum("available_balance")
            )["total_amount"]
            or 0.00
        )

        corebanking_repayment_amount = (
            todays_repayments.filter(
                repayment_type=RepaymentType.TRANSFER,
            ).aggregate(
                total_amount=Sum("repayment_amount")
            )["total_amount"]
            or 0.00
        )

        vfd_repayment_amount = (
            todays_repayments.filter(
                repayment_type=RepaymentType.AGENT_DEBIT,
            ).aggregate(total_amount=Sum("repayment_amount"))["total_amount"]
            or 0.00
        )

        # total_outstanding_repayment = total_loan_repayment - total_paid_repayment

        AccountBalances.objects.create(
            savings_account_balance=round(savings_balance, 2),
            total_savings_without_Loans=round(total_savings_without_Loans, 2),
            difference=round(difference, 2),
            total_disbursement_today=round(total_disbursement_today, 2),
            total_repayments_today=round(todays_repayment, 2),
            loans_account_balance=round(loans_balance, 2),
            commissions_account_balance=round(commissions_balance, 2),
            escrow_account_balance=round(escrow_balance, 2),
            repayment_account_balance=round(repayment_balance, 2),
            available_float=round(available_float, 2),
            monnify_account_balance=monnify_account_balance,
            spend_wallet_balance=round(spend_wallet_balance, 2),
            corebanking_repayment_amount=round(corebanking_repayment_amount, 2),
            vfd_repayment_amount=round(vfd_repayment_amount, 2),
        )

        return Response(
            {"message": "Account balances updated successfully"},
            status=status.HTTP_200_OK,
        )
