import pandas as pd
import openpyxl, datetime
from openpyxl import load_workbook

df = pd.DataFrame()

# open_loans_df = pd.read_excel("Ajo loan merge.xlsx")
# date_today = datetime.datetime.today().date()
# performance_statuses = [
#         "DEFAULTED",
#         "LOST",
#         "PAST_MATURITY",
#     ]
# df_list = []


# # print(open_loans_df)

# for i, loan in open_loans_df.iterrows():
#     end_date = datetime.datetime.strptime(loan["end_date"], "%Y-%m-%d").date()
#     days_to_end = (end_date - date_today).days
#     missed_days = loan["Outstanding Days Today"] + 1
#     repayment_amount = loan["repayment_amount"]
#     total_repayment = loan["total_paid_amount"]
#     performance_status = loan["performance_status"]
#     escrow_offset = loan["escrow_offset"]
#     loan_balance = repayment_amount - total_repayment
#     borrower_phone = loan["borrower_phone_number"]

#     ajo_escrow_wallet_balance = loan["wallet_balance"]
#     escrow_to_bal_target = 10
#     bal_to_repayment_perc = (loan_balance / repayment_amount) * 100
#     bal_to_repayment_perc_bool = bal_to_repayment_perc <= escrow_to_bal_target

#     # print("end_date::::", end_date)
#     # print("days to end::::", days_to_end)
#     # print("missed days::::", missed_days)
#     # print("repayment amount::::", repayment_amount)
#     # print("total repayment::::", total_repayment)
#     # print("Performance status::::", performance_status)
#     # print("Loan Bal::::", loan_balance)
    
#     if (
#         (days_to_end < 15 and missed_days > 10)
#         or performance_status in performance_statuses
#         or bal_to_repayment_perc_bool
#         ):
#         to_be_liquidated = True
#     else:
#         to_be_liquidated = False

#     df_list.append(
#         {
#         "borrower_phone_number": borrower_phone,
#         "days_to_end": (end_date - date_today).days,
#         "missed_days": loan["Outstanding Days Today"] + 1,
#         "repayment_amount": loan["repayment_amount"],
#         "total_repayment": loan["total_paid_amount"],
#         "performance_status": loan["performance_status"],
#         # "escrow_offset": loan["escrow_offset"],
#         "to_be_liquidated": to_be_liquidated,
#         "loan_balance": loan_balance,
#         "wallet_balance": ajo_escrow_wallet_balance,
#         "bal_to_repayment_perc": bal_to_repayment_perc,
#         "bal_to_repayment_perc_bool": bal_to_repayment_perc_bool,
#         }
#     )

# df.from_dict(df_list).to_excel("Escrow Liquidations Check.xlsx")


# New Analysis
# disbursements_excel = "September 5 Disbursements.xlsx"
# borrower_info_excel = "borrower info.xlsx"
# disbursement_date = "Sep 5"

# disbursements_df = pd.read_excel(disbursements_excel)
# borrower_info_df = pd.read_excel(borrower_info_excel)
# disbursements_df = pd.read_excel(disbursements_excel)

# borrowers_with_bvn_list = borrower_info_df.query('verification_type == "BVN"')
# borrower_values = borrowers_with_bvn_list["borrower"]
# borrowers_list = borrower_values.to_list()

# have_bvn_list = []
# no_bvn_list = []

# # print(borrowers_list)
# with pd.ExcelFile('Disbursement Path Analysis Sept.xlsx', engine='openpyxl') as xls:
#     # Load existing sheets into a dictionary
#     sheets = {sheet_name: xls.parse(sheet_name) for sheet_name in xls.sheet_names}


# for i, disbursement in disbursements_df.iterrows():
#     onboarded_user = disbursement["onboarded_user"]
#     # print(onboarded_user, onboarded_user in borrowers_list)

#     data = {
#         "borrower": disbursement["onboarded_user"],
#         "provider": disbursement["transfer_provider"],
#         "amount": disbursement["amount"],
#         "status": disbursement["status"],
#     }
#     if onboarded_user in borrowers_list:
#         have_bvn_list.append(data)
#     else:
#         no_bvn_list.append(data)

# with pd.ExcelWriter("Disbursement Path Analysis Sept.xlsx", engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
#     for sheet_name, df in sheets.items():
#         df.to_excel(writer, sheet_name=sheet_name, index=False)
#     # Add new sheet
#     # new_df.to_excel(writer, sheet_name='NewSheetName', index=False)
#     df.from_dict(have_bvn_list).to_excel(writer, sheet_name=f"Borrowers With Bvn - {disbursement_date}.xlsx", index=False)
#     df.from_dict(no_bvn_list).to_excel(writer, sheet_name=f"Borrowers With No Bvn - {disbursement_date}.xlsx", index=False)
#     df.from_dict(disbursements_df).to_excel(writer, sheet_name=f"Disbursements - {disbursement_date}.xlsx", index=False)

# bvn_sheet = f"Borrowers With Bvn - {disbursement_date}"
# no_bvn_sheet = f"Borrowers With No Bvn - {disbursement_date}"

# exel_file = "health plans.xlsx"

# df = pd.read_excel(exel_file)
# borrowers_with_bvn_list = df.query('verification_type == "BVN"')
# borrower_values = df["onboarded_user"]
# borrowers_list = borrower_values.to_list()

# print(len(borrowers_list))
# print(len(set(borrowers_list)))

# duplicate_borrowers_list = []
# for i, borrower in df.iterrows():
#     onboarded_user = borrower["onboarded_user"]
#     date_created = borrower["date_created"]
#     status = borrower["status"]
#     number_of_transfers = borrowers_list.count(onboarded_user)

#     data = {
#         "onboarded_user": onboarded_user,
#         "date_created": date_created,
#         "status": status
#     }

#     if number_of_transfers > 1:
#         duplicate_borrowers_list.append(data)
#     print("number_of_transfers::::::", number_of_transfers)

# print(duplicate_borrowers_list)
# excel_file = pd.DataFrame().from_dict(duplicate_borrowers_list)
# excel_file.to_excel("duplicate_health_plan_transfers.xlsx")

# health_plan_df = pd.read_csv("Health Plan.csv")
# remitted_df = pd.read_excel("remitted.xlsx")
# plans_list = []

# for i, row in health_plan_df.iterrows():
#     account_number = row["account_number"]
#     amount_sent = 0
#     ajo_user = None
#     loan = None
#     for n, item in remitted_df.iterrows():
#         if item["account_number"] == account_number:
#             amount_sent = item["amount"]
#             ajo_user = item["ajo_user"]
#             laon = item["loan"]
#     data = {
#         "id": row["id"],
#         "user_phone": row["user_phone_number"],
#         "enrolee": row["enrolee"],
#         "first_name": row["first_name"],
#         "last_name": row["last_name"],
#         "amount": row["amount"],
#         "amount_sent": amount_sent,
#         "account": row["account_number"],
#         "ajo_user": ajo_user,
#         "loan": loan,
#     }
#     plans_list.append(data)

# healt_pl_df = pd.DataFrame().from_dict(plans_list)
# healt_pl_df.to_excel("Compare Health Plan Amounts.xlsx")


with pd.ExcelFile('All Time Monnify Disbursements.xlsx', engine='openpyxl') as xls:
    # Load existing sheets into a dictionary
    sheets = {sheet_name: xls.parse(sheet_name) for sheet_name in xls.sheet_names}

url = "saving"
import requests
def temp_monnify_verf(reference):
    url = f"https://savingsback.libertypayng.com/payment/monnify-transaction/verify/?reference={reference}"
    headers = {
        "Authorization": "Bearer",
        # "Content-Type": "application/json"
        }
    response = requests.get(url, headers=headers)
    return response.json()

#FAILED TRANSACTIONS ANALYTICS*****************************
failed_disbursements_excel_list = []
failed_disbursements_excel = "All Time Failed Monnify Disbursements.xlsx"
failed_disbursements_df = pd.read_excel(failed_disbursements_excel)
verf_url = "https://api.paystack.co/bank/resolve?account_number=**********&bank_code=000"

# for i, disbursement in failed_disbursements_df.iterrows():
#     unique_reference = disbursement["transaction_id"]
#     check_status = temp_monnify_verf(unique_reference)

#     # Check Disubursement Status
#     monify_status = check_status.get("responseBody", {}).get("status", "FAILED")
#     destination_account = check_status.get("responseBody", {}).get("destinationAccountNumber")
#     destination_bank = check_status.get("responseBody", {}).get("destinationBankName")

#     data = {
#         "borrower": disbursement["onboarded_user"],
#         "amount": disbursement["amount"],
#         "status": disbursement["status"],
#         "monnify_status": monify_status,
#         "date": disbursement["date_created"],
#         "reference": disbursement["transaction_id"],
#         "status": disbursement["status"],
#         "destination_account": destination_account,
#         "destination_bank": destination_bank,
#     }
#     print(data)
#     failed_disbursements_excel_list.append(data)

# failed_disbursements_excel_df = pd.DataFrame().from_dict(failed_disbursements_excel_list)
# print(failed_disbursements_excel_df)

# with pd.ExcelWriter(
#     "All Time Failed Monnify Disbursements.xlsx",
#     engine='openpyxl', mode='a', if_sheet_exists='replace'
#     ) as writer:
#     for sheet_name, df in sheets.items():
#         df.to_excel(writer, sheet_name=sheet_name, index=False)
#     # Add new sheet
#     failed_disbursements_excel_df.to_excel(
#         writer, sheet_name=f"With Monnify Status.xlsx", index=False
        # )


# ANALYSE ALL TRANSACTIONS ************************************
all_disbursements_excel_list = []
all_disbursements_excel = "All Time Monnify Disbursements.xlsx"
all_disbursements_df = pd.read_excel(all_disbursements_excel)
verf_url = "https://api.paystack.co/bank/resolve?account_number=**********&bank_code=000"

# for i, disbursement in all_disbursements_df.iterrows():
#     unique_reference = disbursement["transaction_id"]
#     check_status = temp_monnify_verf(unique_reference)

#     # Check Disubursement Status
#     monify_status = check_status.get("responseBody", {}).get("status")
#     destination_account = check_status.get("responseBody", {}).get("destinationAccountNumber")
#     destination_bank = check_status.get("responseBody", {}).get("destinationBankName")

#     data = {
#         "borrower": disbursement["onboarded_user"],
#         "amount": disbursement["amount"],
#         "status": disbursement["status"],
#         "monnify_status": monify_status,
#         "date": disbursement["date_created"],
#         "reference": disbursement["transaction_id"],
#         "status": disbursement["status"],
#         "destination_account": destination_account,
#         "destination_bank": destination_bank,
#     }
#     print(data)
#     all_disbursements_excel_list.append(data)

# all_disbursements_excel_df = pd.DataFrame().from_dict(all_disbursements_excel_list)

# with pd.ExcelWriter(
#     "All Time Monnify Disbursements.xlsx",
#     engine='openpyxl', mode='a', if_sheet_exists='replace'
#     ) as writer:
#     for sheet_name, df in sheets.items():
#         df.to_excel(writer, sheet_name=sheet_name, index=False)
#     # Add new sheet
#     all_disbursements_excel_df.to_excel(
#         writer, sheet_name=f"With Monnify Status.xlsx", index=False
#         )


file_path = "All Time Monnify Disbursements.xlsx"
# Load the existing workbook
wb = openpyxl.load_workbook(file_path)

# Get the source sheet
if "Monnify Disbursements" in wb.sheetnames:
    source_sheet = wb["Monnify Disbursements"]
else:
    print(f"Sheet 'Monnify Disbursements' does not exist.")
    exit()

# Check if the target sheet exists, if not, create it
if "With Monnify Status" in wb.sheetnames:
    target_sheet = wb["With Monnify Status"]
else:
    target_sheet = wb.create_sheet("With Monnify Status")

headers = ["Borrower", "Amount", "Status", "Monnify Status", "Date", "Reference", "Destination Account", "Destination Bank"]
target_sheet.append(headers)

# Update the target sheet row by row from source sheet
for disbursement in source_sheet.iter_rows(values_only=True):
    get_index = ('id', 'date_created', 'user', 'transaction_form_type', 
                 'amount', 'transaction_id', 'status', 'transaction_source', 
                 'transaction_destination', 'description', 'transaction_type', 
                 'wallet_type', 'unique_reference', 'transaction_date_completed', 
                 'failure_reason', 'masked_pan_of_card', 'wallet_balance_before', 
                 'wallet_balance_after', 'plan_balance_before', 'plan_balance_after', 
                 'plan_type', 'quotation_id', 'rotation_group_id', 'credit_balance', 
                 'due_balance', 'onboarded_user', 'transaction_source_id', 'transfer_provider', 
                 'last_updated', 'email_sent', 'request_data', 'payload', 'paystack_webhook_payload')
    disbursement = {
        "onboarded_user": disbursement[get_index.index("onboarded_user")],
        "amount": disbursement[get_index.index("amount")],
        "status": disbursement[get_index.index("status")],
        "date": disbursement[get_index.index("date_created")],
        "transaction_id": disbursement[get_index.index("transaction_id")],
        "date_created": disbursement[get_index.index("date_created")],
        }
    unique_reference = disbursement["transaction_id"]
    check_status = temp_monnify_verf(unique_reference)

    # Check Disubursement Status
    monify_status = check_status.get("responseBody", {}).get("status")
    destination_account = check_status.get("responseBody", {}).get("destinationAccountNumber")
    destination_bank = check_status.get("responseBody", {}).get("destinationBankName")

    data = {
        "borrower": disbursement["onboarded_user"],
        "amount": disbursement["amount"],
        "status": disbursement["status"],
        "monnify_status": monify_status,
        "date": disbursement["date_created"],
        "reference": disbursement["transaction_id"],
        "destination_account": destination_account,
        "destination_bank": destination_bank,
    }
    row = [
        data["borrower"],
        data["amount"],
        data["status"],
        data["monnify_status"],
        data["date"],
        data["reference"],
        data["destination_account"],
        data["destination_bank"],
    ]
    print(data)
    target_sheet.append(row)

    # Save the updated workbook
    wb.save(file_path)
    print(f"Sheet 'With Monnify Status' updated successfully!")
