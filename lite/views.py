import random
from rest_framework.views import APIView
from accounts.agency_banking import AgencyBankingClass
from accounts.models import CustomUser
from accounts.paystack import PaystackApi
from ajo.model_choices import AccountFormType
from ajo.models import AjoUser, BankAccountDetails
from ajo.serializers.ajo_serializers import ExistingAjoUserSerializer
from loans.enums import RepaymentType
from loans.models import AjoLoan, AjoLoanRepayment
from loans.serializers import LoanHistorySerializer
from loans.tasks import (
    categorize_missed_loan,
    celery_handle_loandisk_loan_repayment,
    update_loan_repayment_schedules,
)
from payment.model_choices import TransactionFormType, WalletTypes
from payment.models import Transaction, WalletSystem
from payment.serializers import VerifyAccountDetailsSerializer
from .serializers import (
    ChangePasscodeSerializer,
    ChangePinSerializer,
    ConfirmOtpSerializer,
    ForgotPasswordSerializer,
    LiteUserDeviceSerializer,
    LiteUserRegistrationSerializer,
    LoginSerializer,
    PasscodeSerializer,
    RepayLoanSerializer,
    ResetPasswordSerializer,
    ResetTransactionPinSerializer,
    TransactionPinSerializer,
    AjoUserCheckSerializer,
    TransactionSerializer,
    TransferSerializer,
    BeneficiarySerializer,
    ValidatePhoneSerializer,
)
from .models import Beneficiary, LiteUserDevice, PasswordResetOTP
from .utils import (
    get_kyc_stage_limit,
    get_total_transfer_out,
    parse_date,
    retrieve_wallet,
    send_sms,
    success_response,
    failure_response,
)
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.contrib.auth.hashers import make_password
from libertyBiometrics.biometrics import Biometrics
from django.contrib.auth.hashers import check_password
from rest_framework import serializers, generics, status, exceptions
from django.db import transaction
from accounts.models import ConstantTable


class CheckAjoUserView(APIView):
    authentication_classes = []
    permission_classes = []

    def post(self, request):

        serializer = AjoUserCheckSerializer(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
            phone = serializer.validated_data.get("phone")
            base_64_image = serializer.validated_data.get("base_64_image")

            if not phone or not base_64_image:
                return failure_response(
                    message="Phone number and image are required",
                    errors={
                        "phone_number": "This field is required",
                        "image": "This field is required",
                    },
                    status_code=400,
                )

            # Note the phone number field is not unique; we need to know how to handle this. to get the correct user.
            user: AjoUser = AjoUser.objects.filter(phone_number=phone).first()
            print("Existing User Found", user)

            if user:
                if user.image:

                    biometrics = Biometrics()
                    request_ref = biometrics.generate_unique_ref()

                    image_bytes1 = user.image.file.read()
                    image_bytes2 = Biometrics.convert_b64_2_jpg(base_64_image)

                    # Perform face matching
                    face_match_response = biometrics.face_match(
                        request_ref=request_ref,
                        face_one=image_bytes1,
                        face_two=image_bytes2,
                    )

                    similarity = float(
                        face_match_response.get("data", {})
                        .get("data", {})
                        .get("similarity", 0)
                    )
                    print("Lite user similarity is:", similarity)

                    if similarity >= 80:

                        serializer = ExistingAjoUserSerializer(user)
                        return success_response(data=serializer.data)
                    else:
                        return failure_response(
                            message="unable to match image",
                            status_code=400,
                        )
                else:
                    return failure_response(
                        message="no image record for this user",
                        status_code=400,
                    )
            return failure_response(
                message="no data for given credentials",
                status_code=404,
            )
        except Exception as e:
            return failure_response(
                message=str(e), status_code=400, errors=serializer.errors
            )


class ValidatePhoneNumber(APIView):
    def post(self, request):
        serializer = ValidatePhoneSerializer(data=request.data)
        if serializer.is_valid():
            phone = serializer.validated_data.get("phone")
            user: AjoUser = AjoUser.objects.filter(phone_number=phone).first()
            if user:
                verification_code = str(random.randint(100000, 999999))

                otp_entry = PasswordResetOTP(ajo_user=user, otp=verification_code)
                otp_entry.save()

                message = f"Hello, your OTP code is {verification_code}. Please enter this code to complete your onboarding."
                send_sms(phone, message)
                return success_response(
                    data=verification_code,
                    message="Verification code sent",
                )
            return failure_response(
                message="unable to validate user's phone", errors=serializer.errors
            )
        return failure_response(message="Invalid data", errors=serializer.errors)


class ConfirmValidatePhoneNumber(APIView):
    def post(self, request):
        serializer = ConfirmOtpSerializer(data=request.data)
        if serializer.is_valid():
            phone = serializer.validated_data.get("phone")
            verification_code = serializer.validated_data.get("verification_code")
            try:
                otp_entry = PasswordResetOTP.objects.get(
                    ajo_user__phone_number=phone, otp=verification_code
                )
                if otp_entry.is_expired():
                    return failure_response(message="Verification code has expired.")
                return success_response(
                    data={"message": "OTP confirmed successfully."},
                    message="OTP confirmed successfully",
                )
            except PasswordResetOTP.DoesNotExist:
                return failure_response(message="Invalid verification code.")
        return failure_response(message="Invalid data", errors=serializer.errors)


class CreatePasscodeView(APIView):
    """
    Endpoint to create passcode for existing seeds and pennie users
    """

    def post(self, request):
        try:
            with transaction.atomic():
                serializer = PasscodeSerializer(data=request.data)
                if serializer.is_valid():
                    user = serializer.save()
                    token = RefreshToken.for_user(user)
                    refresh_token = str(token)
                    access_token = str(token.access_token)
                    data_serializer = ExistingAjoUserSerializer(user.lite_users)
                    payload = {
                        "data": data_serializer.data,
                        "access_token": access_token,
                        "refresh_token": refresh_token,
                    }
                    print("Payload:", payload)
                    response = success_response(
                        data=payload, message="Passcode created successfully"
                    )
                    print("Response:", response.data)
                    return response
                print("Errors:", serializer.errors)
                return failure_response(
                    message="Invalid data", errors=serializer.errors
                )
        except Exception as err:
            print("Error:", str(err))
            return failure_response(message=str(err), status_code=500)


class CreateTransactionPinView(APIView):
    """
    Endpoint to create a transaction pin for the authenticated user.
    The user must be authenticated with a valid JWT token.
    """

    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = TransactionPinSerializer(data=request.data)
        if serializer.is_valid():
            user = request.user
            ajo_user = user.lite_users
            ajo_user.lite_transaction_pin = make_password(
                serializer.validated_data["pin"]
            )
            ajo_user.save()
            return success_response(
                data=serializer.data, message="Transaction pin created successfully"
            )
        return failure_response(message="Invalid data", errors=serializer.errors)


class LiteUserRegistrationView(APIView):
    def post(self, request):
        try:
            serializer = LiteUserRegistrationSerializer(data=request.data)
            if serializer.is_valid():
                validated_data = serializer.validated_data
                phone = validated_data.get("phone_number")
                email = validated_data.get("email")
                bvn = validated_data.get("bvn")

                users = CustomUser.objects.all()
                ajo_user = AjoUser.objects.all()

                if users.filter(user_phone=phone).exists():
                    response = failure_response(
                        message="user with this phone number already exists",
                        status_code=400,
                    )
                    print("Response:", response.data)
                    return response
                if users.filter(email=email).exists():
                    response = failure_response(
                        message="user with this email already exists", status_code=400
                    )
                    print("Response:", response.data)
                    return response
                if ajo_user.filter(bvn=bvn).exists():
                    response = failure_response(
                        message="an ajo user with this bvn already exists",
                        status_code=400,
                    )
                    print("Response:", response.data)
                    return response

                ajo_user = serializer.save()
                serializer = ExistingAjoUserSerializer(ajo_user)
                token = RefreshToken.for_user(ajo_user.lite_user)
                refresh_token = str(token)
                access_token = str(token.access_token)
                payload = {
                    "data": serializer.data,
                    "access_token": access_token,
                    "refresh_token": refresh_token,
                }
                print("Payload:", payload)
                response = success_response(
                    data=payload, message="Registration successful"
                )
                print("Response:", response.data)
                return response
            print("Errors:", serializer.errors)
            response = failure_response(
                message="Invalid data", errors=serializer.errors
            )
            print("Response:", response.data)
            return response
        except Exception as e:
            response = failure_response(message="An error occurred", errors=str(e))
            print("Error:", str(e))
            return response


class AjoUserDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """View for retrieving logged-in user details"""
        try:
            ajo_user = AjoUser.objects.get(lite_user=request.user)
            serializer = ExistingAjoUserSerializer(ajo_user)
            return success_response(
                data=serializer.data, message="User details retrieved successfully"
            )
        except AjoUser.DoesNotExist:
            return failure_response(message="User not found", status_code=404)

    def put(self, request):
        """View for updating user details"""
        try:
            ajo_user = AjoUser.objects.get(lite_user=request.user)
            serializer = LiteUserRegistrationSerializer(
                ajo_user, data=request.data, partial=True
            )
            if serializer.is_valid():
                updated_user = serializer.save()
                return success_response(
                    data=ExistingAjoUserSerializer(updated_user).data,
                    message="User details updated successfully",
                )
            return failure_response(message="Invalid data", errors=serializer.errors)
        except AjoUser.DoesNotExist:
            return failure_response(message="User not found", status_code=404)
        except Exception as e:
            return failure_response(message="An error occurred", errors=str(e))


class LoginView(APIView):
    def post(self, request):
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            tokens = serializer.create(serializer.validated_data)
            response_data = {"data": tokens, "message": "Login successful"}
            print("Payload:", request.data)
            print("Response:", response_data)
            return success_response(**response_data)
        print("Error:", serializer.errors)
        return failure_response(message="Invalid data", errors=serializer.errors)


class ForgotPasswordView(APIView):
    def post(self, request):
        serializer = ForgotPasswordSerializer(data=request.data)
        if serializer.is_valid():
            serializer.create(serializer.validated_data)
            return success_response(
                message="Verification code sent",
            )
        return failure_response(message="Invalid data", errors=serializer.errors)


class ResetPasswordView(APIView):
    def post(self, request):
        serializer = ResetPasswordSerializer(data=request.data)
        if serializer.is_valid():
            serializer.create(serializer.validated_data)
            return success_response(
                data={"message": "Password reset successfully"}, message="Success"
            )
        return failure_response(message="Invalid data", errors=serializer.errors)


class ChangePasscodeView(APIView):
    """
    Endpoint to change the user's passcode.
    """

    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = ChangePasscodeSerializer(data=request.data)
        if serializer.is_valid():
            user = request.user
            old_passcode = serializer.validated_data["old_passcode"]

            if not check_password(old_passcode, user.user_password):
                return failure_response(
                    message="passcode is incorrect.", status_code=400
                )

            user.user_password = (make_password(serializer.validated_data["passcode"]),)
            user.save()

            return success_response(message="Passcode changed successfully.")
        return failure_response(message="Invalid data", errors=serializer.errors)


class ChangePinView(APIView):
    """
    Endpoint to change the user's transaction pin.
    """

    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = ChangePinSerializer(data=request.data)
        if serializer.is_valid():
            user = request.user
            ajo_user = user.lite_users
            old_pin = serializer.validated_data["old_pin"]

            if not check_password(old_pin, ajo_user.lite_transaction_pin):
                return failure_response(
                    message="Old pin is incorrect.", status_code=400
                )

            ajo_user.lite_transaction_pin = make_password(
                serializer.validated_data["pin"]
            )
            ajo_user.save()

            return success_response(message="Transaction pin changed successfully.")
        return failure_response(message="Invalid data", errors=serializer.errors)


class ForgotTransPinView(APIView):
    def post(self, request):
        serializer = ForgotPasswordSerializer(data=request.data)
        if serializer.is_valid():
            serializer.create(serializer.validated_data, pin_type="transaction pin")
            return success_response(
                message="Verification code sent",
            )
        return failure_response(message="Invalid data", errors=serializer.errors)


class ResetTransactionPinView(APIView):
    def post(self, request):
        serializer = ResetTransactionPinSerializer(data=request.data)
        if serializer.is_valid():
            serializer.create(serializer.validated_data)
            return success_response(
                data={"message": "transaction pin reset successfully"},
                message="Success",
            )
        return failure_response(message="Invalid data", errors=serializer.errors)


###### WALLETS


class FinanceDataView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):

        try:
            user = request.user
            ajouser: AjoUser = user.lite_users
            wallet_data = {}
            wallet, account = retrieve_wallet(ajouser)

            if wallet:
                wallet_data = {
                    "main_balance": wallet.available_balance,
                    "account_number": account.account_number,
                    "account_name": account.account_name,
                    "bank": account.bank_name,
                }
            user_loan = AjoLoan.objects.filter(
                borrower=user.lite_users, is_disbursed=True, status="OPEN"
            ).first()
            loan_data = {}
            loan_account = {}
            if user_loan:
                loan_data = LoanHistorySerializer(user_loan).data
                const = ConstantTable.get_constant_table_instance()

                # loan_wallet = WalletSystem.objects.get(
                #     user=ajouser.user,
                #     wallet_type=WalletTypes.AJO_LOAN_REPAYMENT,
                #     onboarded_user=ajouser,
                # )

                # __loan_account = BankAccountDetails.objects.get(
                #     account_number=loan_wallet.wallet_number
                # )

                __loan_account = BankAccountDetails.objects.filter(
                    user=ajouser.user,
                    ajo_user=ajouser,
                    account_provider=const.loan_collection_channel,
                    form_type=AccountFormType.LOAN_REPAYMENT,
                ).last()

                if not __loan_account:
                    __loan_account = BankAccountDetails.objects.filter(
                        user=ajouser.user,
                        ajo_user=ajouser,
                        form_type=AccountFormType.LOAN_REPAYMENT,
                    ).last()

                loan_account = {
                    "account_number": __loan_account.account_number,
                    "account_name": __loan_account.account_name,
                    "bank": __loan_account.bank_name,
                }

            return success_response(
                data={
                    "main_account_details": wallet_data,
                    "loan_data": loan_data,
                    "loan_account": loan_account,
                },
                message="financial details retrieved",
            )
        except Exception as err:
            return failure_response(message=str(err), status_code=500)


class TransferView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = TransferSerializer(data=request.data)
        if serializer.is_valid():
            data = serializer.validated_data
            user = request.user
            ajo_user: AjoUser = user.lite_users
            amount = data.get("amount")

            if not check_password(data.pop("pin"), ajo_user.lite_transaction_pin):
                return failure_response(message="invalid credentials", status_code=400)

            limit = get_kyc_stage_limit(ajo_user)

            single_outflow_limit = limit.get("single_outflow_limit")

            if amount > single_outflow_limit:
                return failure_response(
                    message="your have exceeded your limit for a single transaction",
                    status_code=400,
                )

            wallet, _ = retrieve_wallet(ajo_user)
            if wallet.transfer_suspended:
                return failure_response(
                    message="temporary suspension on transfer. please contact support",
                    status_code=400,
                )

            if wallet.available_balance < amount:
                return failure_response(message="insufficient funds", status_code=400)

            total_outflow_limit = limit.get("total_outflow_limit")
            total_transfer_out = get_total_transfer_out(wallet)

            if total_transfer_out >= total_outflow_limit:
                return failure_response(
                    message="your have exceeded your transaction limit today",
                    status_code=400,
                )

            const_table = ConstantTable.get_constant_table_instance()
            payout_response = const_table.get_transfer_provider()(
                wallet=wallet, data=data
            )

            if payout_response.get("status"):
                return success_response(
                    data=payout_response, message="Transfer successful"
                )
            return failure_response(message="Transfer failed", errors=payout_response)
        return failure_response(message="Invalid data", errors=serializer.errors)


class BeneficiaryListCreateView(generics.ListCreateAPIView):
    """
    GET: List all beneficiaries
    POST: Create a new beneficiary
    """

    queryset = Beneficiary.objects.all()
    serializer_class = BeneficiarySerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return self.queryset.filter(user=self.request.user)

    def get(self, request, *args, **kwargs):
        beneficiaries = self.get_queryset()
        serializer = self.get_serializer(beneficiaries, many=True)
        return success_response(serializer.data)

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            serializer.save(user=request.user)
            return success_response(
                serializer.data,
                message="Beneficiary created successfully",
                status_code=status.HTTP_201_CREATED,
            )
        return failure_response(
            message="Invalid data",
            errors=serializer.errors,
            status_code=status.HTTP_400_BAD_REQUEST,
        )


class BeneficiaryRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    """
    GET: Retrieve a beneficiary
    PUT/PATCH: Update a beneficiary
    DELETE: Delete a beneficiary
    """

    queryset = Beneficiary.objects.all()
    serializer_class = BeneficiarySerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def check_object_permissions(self, request, obj):
        if obj.user != request.user:
            raise exceptions.PermissionDenied()
        return super().check_object_permissions(request, obj)

    def get(self, request, *args, **kwargs):
        beneficiary = self.get_object()
        serializer = self.get_serializer(beneficiary)
        return success_response(serializer.data)

    def put(self, request, *args, **kwargs):
        beneficiary = self.get_object()
        serializer = self.get_serializer(beneficiary, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return success_response(
                serializer.data, message="Beneficiary updated successfully"
            )
        return failure_response(
            message="Invalid data",
            errors=serializer.errors,
            status_code=status.HTTP_400_BAD_REQUEST,
        )

    def delete(self, request, *args, **kwargs):
        beneficiary = self.get_object()
        beneficiary.delete()
        return success_response(
            data=None,
            message="Beneficiary deleted successfully",
            status_code=status.HTTP_204_NO_CONTENT,
        )


class TransactionHistory(generics.ListAPIView):
    queryset = Transaction.objects.all()
    serializer_class = TransactionSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        ajo_user = user.lite_users
        wallet, _ = retrieve_wallet(ajo_user)
        queryset = (
            self.queryset.filter(
                onboarded_user=ajo_user,
                user=wallet.user,
                wallet_type=wallet.wallet_type,
            )
            .order_by("-date_created")
            .exclude(transaction_form_type=TransactionFormType.BANK_DEPOSIT)
        )

        # Retrieve filter parameters from the request
        transaction_id = self.request.query_params.get("transaction_id")
        transaction_form_type = self.request.query_params.get("transaction_form_type")
        transaction_type = self.request.query_params.get("transaction_type")
        transaction_date_start = parse_date(
            self.request.query_params.get("transaction_date_start")
        )
        transaction_date_end = parse_date(
            self.request.query_params.get("transaction_date_end")
        )
        date_created_start = parse_date(
            self.request.query_params.get("date_created_start")
        )
        date_created_end = parse_date(self.request.query_params.get("date_created_end"))

        # Apply filters if parameters are provided
        if transaction_id:
            queryset = queryset.filter(transaction_id=transaction_id)

        if transaction_form_type:
            queryset = queryset.filter(transaction_form_type=transaction_form_type)

        if transaction_type:
            queryset = queryset.filter(transaction_type=transaction_type)

        # Date range filtering for transaction_date
        if transaction_date_start and transaction_date_end:
            queryset = queryset.filter(
                transaction_date_completed__range=[
                    transaction_date_start,
                    transaction_date_end,
                ]
            )

        # Date range filtering for date_created
        if date_created_start and date_created_end:
            queryset = queryset.filter(
                date_created__range=[date_created_start, date_created_end]
            )

        return queryset

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return success_response(serializer.data)


class TransactionRetrieveView(generics.RetrieveAPIView):
    """
    GET: Retrieve a single transaction by ID
    """

    queryset = Transaction.objects.all()
    serializer_class = TransactionSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return success_response(serializer.data)


class LoanRepaymentHistory(APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):

        user = self.request.user
        ajo_user = user.lite_users
        ajo_loan = ajo_user.ajoloan_set.filter(status="OPEN").last()

        repayments = AjoLoanRepayment.objects.filter(
            ajo_loan=ajo_loan,
            borrower=ajo_loan.borrower,
        ).values("id", "repayment_amount", "paid_date")

        data = {
            "loan_id": ajo_loan.id,
            "loan_amount": ajo_loan.amount,
            "total_paid_amount": ajo_loan.total_paid_amount,
            "expected_repayment_amount": ajo_loan.repayment_amount,
            "frequent_repayment_amount": ajo_loan.daily_repayment_amount,
            "total_repayment_amount": ajo_loan.total_paid_amount,
            "loan_tenure": ajo_loan.tenor,
            "maturity_date": ajo_loan.end_date,
            "date_disbursed": ajo_loan.start_date,
            "repayment_count": ajo_loan.tenor_in_days,
            "history": repayments,
        }

        return success_response(data)


class AjoLoanRepaymentAPIView(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = RepayLoanSerializer

    def post(self, request):
        try:
            serializer = self.serializer_class(
                data=request.data, context={"request": request}
            )
            serializer.is_valid(raise_exception=True)
            validated_data = serializer.validated_data
            loan: AjoLoan = validated_data.get("loan")
            amount = validated_data.get("amount")
            wallet = validated_data.get("wallet")

            repayment_status, repayment = AjoLoanRepayment.loan_repayment(
                loan=loan,
                amount=amount,
                from_wallet=wallet,
                repayment_type=RepaymentType.LITE_DEBIT,
            )

            if repayment_status == True:
                """Post to loan disk when ready"""

                # POST REPAYMENT RECORD TO LOANDISK
                celery_handle_loandisk_loan_repayment.delay(
                    ajo_loan_id=loan.id,
                    amount=amount,
                    repayment_type=RepaymentType.LITE_DEBIT,
                )

            update_loan_repayment_schedules.delay(loan.id, repayment.id)
            categorize_missed_loan.delay(loan.id)

            return success_response(message="repayment successful", status_code=202)

        except serializers.ValidationError as err:
            return failure_response(message=str(err), errors=serializer.errors)

        except Exception as err:
            return failure_response(message=str(err), status_code=500)


class ResolveAccountView(generics.GenericAPIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = VerifyAccountDetailsSerializer

    def post(self, request, *args, **kwargs):
        """
        This endpoint checks if the account number is valid and
        returns the account name.
        """
        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)

            # obtain the account number, bank code and ajo user phone number
            account_number = serializer.validated_data.get("account_number")
            bank_code = serializer.validated_data.get("bank_code")
            bank_name = serializer.validated_data.get("bank_name")

            # USE BANK CODE FIRST BEFORE TRYING NIP BANK CODE --> TARGET PAYSTACK
            cbn_bank_code = AgencyBankingClass.get_bank_cbn_code(nip_code=bank_code)
            paystack_direct = PaystackApi.resolve_account_number(
                account_number=account_number, bank_code=cbn_bank_code
            )

            if paystack_direct.get("status"):
                return success_response(
                    data={
                        **paystack_direct.get("data").get("data"),
                        "bank_code": bank_code,
                        "bank_name": bank_name,
                    }
                )

            return failure_response(message="unable to resolve account")

        except serializers.ValidationError as err:
            return failure_response(message=str(err), errors=serializer.errors)

        except Exception as err:
            return failure_response(message=str(err), status_code=500)


class RegisterDeviceView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):

        if LiteUserDevice.objects.filter(user=request.user).exists():
            return failure_response(
                errors={
                    "error": "You already have a registered device. contact support."
                },
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        serializer = LiteUserDeviceSerializer(
            data=request.data, context={"request": request}
        )

        if serializer.is_valid():
            serializer.save(user=request.user)
            return success_response(
                data=serializer.data, status_code=status.HTTP_201_CREATED
            )

        return failure_response(
            errors=serializer.errors, status_code=status.HTTP_400_BAD_REQUEST
        )
