import json
from rest_framework.response import Response
import sentry_sdk
import base64, uuid, datetime
from accounts.agency_banking import AgencyBankingClass
from accounts.helper_files.monify import Monnify
from accounts.models import CustomUser
from ajo.model_choices import AccountFormType
from ajo.models import BankAccountDetails
from ajo.third_party import TextMessages
from lite.models import MonifyTransferLog, WemaTransferLog, CashConnectTransferLog
from loans.enums import IdentityRequestSource
from loans.helpers.core_banking import CoreBankingManager
from payment.model_choices import (
    WalletTypes,
    TransactionDestination,
    TransactionFormType,
    TransactionSource,
    Status,
    TransactionTypeCreditOrDebitChoices,
    DisbursementProviderType,
)
from payment.models import Transaction, WalletSystem
from payment.services import TransactionService
from savings import settings
from django.db import transaction, models


def success_response(data=None, message="Success", status_code=200):
    """
    Returns a success response with status, message, and data.

    Parameters:
    - data: Data to be included in the response.
    - message: Message to be included in the response (default is "Success").

    Returns:
    - Response object with success status, message, and data.
    """
    return Response(
        {"status": "success", "message": message, "data": data}, status=status_code
    )


def failure_response(message="Failure", errors=None, status_code=400):
    """
    Returns a failure response with status, message, and errors.

    Parameters:
    - message: Message to be included in the response (default is "Failure").
    - errors: Dictionary of errors to be included in the response (default is None).
    - status_code: HTTP status code (default is 400).

    Returns:
    - Response object with failure status, message, and errors.
    """

    if status_code == 500:
        sentry_sdk.capture_exception(error=errors)
    return Response(
        {"status": "failure", "message": message, "errors": errors}, status=status_code
    )


def image_to_base64(image_field):
    """
    Convert a Django ImageField or FileField to a base64 string.

    :param image_field: The image field (instance of ImageField or FileField)
    :return: Base64 encoded string of the image
    """
    if image_field and hasattr(image_field, "file"):
        # Read the file
        image_data = image_field.file.read()

        # Convert to base64
        encoded_image = base64.b64encode(image_data).decode("utf-8")

        # Return as a single base64 image string with MIME type
        base64_image = encoded_image
        return base64_image
    return ""


def send_sms(phone_number: str, message: str) -> bool:
    """
    This sends the first message when an ajo plan is activated with payment

    Args:
        ajo_user (AjoUser): the ajo user
        phone_number (str): the phone number
        amount (float): the amount
        balance (float): the balance

    Returns:
        bool: True or False
    """

    placeholders = {"message": message}

    sms = TextMessages.dynamic_send_sms(
        phone_number=phone_number,
        template_id=settings.EMPTY_TEMPLATE_ID,
        placeholders=placeholders,
    )

    print(sms)

    return sms.get("status")


def create_user_agency(ajo_user) -> Response:
    first_name = ajo_user.first_name
    last_name = ajo_user.last_name
    email = f"{ajo_user.first_name}.{ajo_user.phone_number}@{ajo_user.last_name}.com".lower()
    state = ajo_user.state
    lga = ajo_user.lga
    street = ajo_user.address
    nearest_landmark = ajo_user.trade_location
    business_name = ajo_user.trade
    gender = ajo_user.gender.title()

    payload = {
        "phone_number": ajo_user.phone_number,
        "first_name": first_name,
        "last_name": last_name,
        "email": email,
        "username": ajo_user.phone_number,
        "state": state,
        "lga": lga,
        "nearest_landmark": nearest_landmark,
        "street": street,
        "type_of_user": "MICRO_SAVER",  # confirm this user type
        "business_name": business_name,
        "gender": gender,
    }

    agency_response = AgencyBankingClass.create_account_on_liberty(payload)

    if (
        agency_response.get("status") == "success"
        or agency_response.get("error_code") == "10"
    ):
        customer_user_id = agency_response["user_id"]
        user = CustomUser.objects.get(email=email, user_phone=ajo_user.phone_number)
        user.customer_user_id = customer_user_id
        user.save()

        # user_id = agency_response.get("data").get("user_id")
        # access_token = generate_micro_saver_access_token(user_id)

        # response = {
        #     "status": "success",
        #     "message": "login successful",
        #     "response_code": "00",
        #     "access": access_token,
        # }

        return True

    return False


@transaction.atomic
def create_wema_account(ajo_user):
    """
    Creates a Wema account for the given Ajo user if one does not already exist.

    Args:
        ajo_user (AjoUser): The Ajo user for whom the account is to be created.

    Returns:
        None: This function does not return a value. It creates a bank account
        entry in the database if the account does not already exist.
    """

    spend_account = BankAccountDetails.objects.filter(
        ajo_user=ajo_user,
        form_type=AccountFormType.AJO_SPENDING,
        bank_name="Wema Bank Plc",
        user=ajo_user.lite_user if ajo_user.provider == "lite" else ajo_user.user,
    )
    if not spend_account.exists():
        request_payload = {
            "first_name": ajo_user.first_name,
            "last_name": ajo_user.last_name,
            "middle_name": ajo_user.alias,
            "email": ajo_user.lite_user.email,
            "phone": ajo_user.lite_user.user_phone,
            "bvn": ajo_user.bvn,
            "date_of_birth": str(ajo_user.dob),
        }
        create_account_response = CoreBankingManager().create_multiple_account(
            **request_payload
        )
        status_code = create_account_response.get("status_code")
        if status_code == 201:
            data = create_account_response.get("data").get("account_details")
            account_number = data.get("account_number")
            acct_name = data.get("first_name") + " " + data.get("last_name")
            bank_code = data.get("bank_code")
            bank_name = data.get("bank_name")

            # create bank account details on success response
            BankAccountDetails.objects.create(
                user=(
                    ajo_user.lite_user if ajo_user.provider == "lite" else ajo_user.user
                ),
                ajo_user=ajo_user,
                account_number=account_number,
                account_name=acct_name,
                bank_code=bank_code,
                bank_name=bank_name,
                account_type="Spend",
                consent=True,
                form_type=AccountFormType.AJO_SPENDING,
                payload=create_account_response,
                initial_payload=request_payload,
            )

            ajo_user_wallet, _ = WalletSystem.objects.get_or_create(
                user=(
                    ajo_user.lite_user if ajo_user.provider == "lite" else ajo_user.user
                ),
                wallet_type=WalletTypes.AJO_SPENDING,
                onboarded_user=ajo_user,
            )
            ajo_user_wallet.wallet_number = account_number
            ajo_user_wallet.save()
    else:
        account = spend_account[0]

        ajo_user_wallet, _ = WalletSystem.objects.get_or_create(
            user=account.user,
            wallet_type=WalletTypes.AJO_SPENDING,
            onboarded_user=ajo_user,
        )

        ajo_user_wallet.wallet_number = account.account_number
        ajo_user_wallet.save()


def retrieve_wallet(ajo_user):
    from accounts.models import ConstantTable
    from ajo.model_choices import AccountProvider

    const = ConstantTable.get_constant_table_instance()
    try:
        if const.spend_account_provider == DisbursementProviderType.CASH_CONNECT:
            spend_account = BankAccountDetails.objects.filter(
                ajo_user=ajo_user,
                form_type="AJO_SPENDING",
                account_provider=AccountProvider.CASH_CONNECT,
                user=(
                    ajo_user.lite_user if ajo_user.provider == "lite" else ajo_user.user
                ),
            )
        else:
            spend_account = BankAccountDetails.objects.filter(
                ajo_user=ajo_user,
                form_type="AJO_SPENDING",
                bank_name="Wema Bank Plc",
                user=(
                    ajo_user.lite_user if ajo_user.provider == "lite" else ajo_user.user
                ),
            )
        account = spend_account[0]

        try:
            wallet = WalletSystem.objects.get(
                user=account.user,
                wallet_type=WalletTypes.AJO_SPENDING,
                onboarded_user=ajo_user,
                wallet_number=account.account_number,
            )
        except WalletSystem.DoesNotExist:
            wallet = WalletSystem.objects.get(
                user=account.user,
                wallet_type="AJO_SPENDING",
                onboarded_user=ajo_user,
            )
            wallet.wallet_number = account.account_number
            wallet.save()

        return wallet, account
    except Exception:
        return None, None


# @transaction.atomic
def payout_with_wema(wallet: WalletSystem, data: dict):
    # Do balance inflow/outflow reconciliation checks here
    from payment.utils import compare_transfer_out_with_wema_inflows

    check_inflow_outflow_balance = compare_transfer_out_with_wema_inflows(
        amount=float(data.get("amount")),
    )

    ajo_user = wallet.onboarded_user
    user = wallet.user
    transaction_instance = (
        TransactionService.create_transfer_to_from_wallet_transaction(
            user=user,
            amount=float(data.get("amount")),
            wallet_type=WalletTypes.AJO_SPENDING,
            transaction_form_type=TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT,
            status=Status.PENDING,
            transaction_source=TransactionSource.WALLET,
            transaction_destination=TransactionDestination.BANK_ACCOUNT,
            transaction_description=data.get("narration"),
            ajo_user=ajo_user,
            transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
            transfer_provider=DisbursementProviderType.WEMA_PROVIDER,
        )
    )

    ref = str(uuid.uuid4())

    data["ref"] = ref

    try:
        bal_before = wallet.available_balance

        WalletSystem.deduct_balance(
            wallet=wallet,
            amount=float(data.get("amount")),
            transaction_instance=transaction_instance,
            unique_reference=ref,
            onboarded_user=ajo_user,
        )
        wallet.refresh_from_db()
        transaction_instance.beneficiary_name = data.get("account_name")
        transaction_instance.beneficiary_account = data.get("account_number")
        transaction_instance.beneficiary_bank = data.get("bank_code")
        transaction_instance.unique_reference = ref
        transaction_instance.wallet_balance_before = bal_before
        transaction_instance.wallet_balance_after = wallet.available_balance
        transaction_instance.save()

        if check_inflow_outflow_balance == False:
            transaction_instance.marked_for_excess_transfer = True
            transaction_instance.save()
            res_data = {
                "status": False,
                "errors": "Transfer would exceed wema inflow total",
            }
            response.status_code = 400
        else:
            response = CoreBankingManager.transfer_from_wema(wallet=wallet, data=data)
            res_data = response.json()

        WemaTransferLog.objects.create(
            transaction=transaction_instance,
            wallet=wallet,
            payload=data,
            response=res_data,
        )

        if response.status_code == 200 and res_data.get("status") == "success":
            return {"status": True, "data": res_data.get("data")}
        else:
            return {"status": False, "data": res_data.get("errors")}
    except json.JSONDecodeError:
        WemaTransferLog.objects.create(
            transaction=transaction_instance,
            wallet=wallet,
            payload=data,
            content=response.content,
        )
        return {"status": False, "data": response.content}


def payout_with_monify(wallet: WalletSystem, data: dict):
    ajo_user = wallet.onboarded_user
    user = wallet.user
    transaction_instance = (
        TransactionService.create_transfer_to_from_wallet_transaction(
            user=user,
            amount=float(data.get("amount")),
            wallet_type=WalletTypes.AJO_SPENDING,
            transaction_form_type=TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT,
            status=Status.PENDING,
            transaction_source=TransactionSource.WALLET,
            transaction_destination=TransactionDestination.BANK_ACCOUNT,
            transaction_description=data.get("narration"),
            ajo_user=ajo_user,
            transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
            transfer_provider=DisbursementProviderType.MONNIFY_PROVIDER,
        )
    )

    try:
        bal_before = wallet.available_balance

        WalletSystem.deduct_balance(
            wallet=wallet,
            amount=float(data.get("amount")),
            transaction_instance=transaction_instance,
            unique_reference=transaction_instance.transaction_id,
            onboarded_user=ajo_user,
        )
        wallet.refresh_from_db()
        transaction_instance.beneficiary_name = data.get("account_name")
        transaction_instance.beneficiary_account = data.get("account_number")
        transaction_instance.beneficiary_bank = data.get("bank_code")
        transaction_instance.unique_reference = transaction_instance.transaction_id
        transaction_instance.wallet_balance_before = bal_before
        transaction_instance.wallet_balance_after = wallet.available_balance
        transaction_instance.save()

        monnify_class = Monnify()

        data["reference"] = str(transaction_instance.transaction_id)
        data["narration"] = data.get("narration")
        data["destinationBankCode"] = data.get("bank_code")
        data["destinationAccountNumber"] = data.get("account_number")

        transfer_response = monnify_class.initiate_single_transfer(**data)
        response = transfer_response.get("status")

        MonifyTransferLog.objects.create(
            transaction=transaction_instance,
            wallet=wallet,
            payload=data,
            response=transfer_response,
        )

        if response:
            return {"status": True, "data": {}}
        else:
            return {"status": False, "data": transfer_response}
    except json.JSONDecodeError:
        MonifyTransferLog.objects.create(
            transaction=transaction_instance,
            wallet=wallet,
            payload=data,
            response=transfer_response,
        )
        return {"status": False, "data": response.content}


def get_total_transfer_out(wallet: WalletSystem):
    today = datetime.date.today()
    total_transfer_out = (
        Transaction.objects.filter(
            user=wallet.user,
            onboarded_user=wallet.onboarded_user,
            date_created__date=today,
            transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
            transaction_form_type=TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT,
            status=Status.SUCCESS,
        ).aggregate(total=models.Sum("amount"))["total"]
        or 0
    )
    return total_transfer_out


def parse_date(date_str):
    """
    Parse date string into datetime object.

    Arguments:
    date_str -- date string in format '%d-%m-%Y'

    Returns:
    date_obj -- datetime object
    """
    if date_str:
        try:
            date_obj = datetime.datetime.strptime(date_str, "%d-%m-%Y")
            return date_obj
        except ValueError:
            # Handle invalid date format gracefully
            return None
    return None


def validate_kyc(verification_number):
    from loans.models import YouVerifyRequest

    id_instance = YouVerifyRequest.get_create_identity_result(
        id=verification_number, id_type="BVN", source=IdentityRequestSource.EXTERNAL
    )
    res = YouVerifyRequest.objects.filter(id=id_instance.id).values()[0]
    print(res)
    data = res.get("response", {}).get("data")
    if res.get("is_valid"):
        response_body = {
            "status": True,
            "message": "success",
            "data": data,
        }
    else:
        response_body = {
            "status": False,
            "message": "failed to verify bvn",
            "data": data,
        }

    return response_body


def get_kyc_stage_limit(ajouser):
    teir = ajouser.lite_kyc_teir

    match teir:
        case 3:
            return {
                "single_inflow_limit": float("inf"),
                "total_inflow_limit": 1000000,
                "single_outflow_limit": float("inf"),
                "total_outflow_limit": 5000000,
            }
        case 2:
            return {
                "single_inflow_limit": 100000,
                "total_inflow_limit": 500000,
                "single_outflow_limit": 100000,
                "total_outflow_limit": 500000,
            }
        case 1:
            return {
                "single_inflow_limit": 50000,
                "total_inflow_limit": 200000,
                "single_outflow_limit": 50000,
                "total_outflow_limit": 200000,
            }


def check_and_update_referral(referral_code: str, user: CustomUser) -> None:
    referrer: CustomUser = CustomUser.objects.filter(
        referral_code__iexact=referral_code
    ).first()
    if referrer:
        referrer.referred_emails = user.email
        referrer.referrals_count += 1
        referrer.save()


def payout_with_cash_connect(wallet: WalletSystem, data: dict):
    # Do balance inflow/outflow reconciliation checks here
    # from payment.utils import compare_transfer_out_with_wema_inflows

    # check_inflow_outflow_balance = compare_transfer_out_with_wema_inflows(
    #     amount=float(data.get("amount")),
    # )

    ajo_user = wallet.onboarded_user
    user = wallet.user
    transaction_instance = (
        TransactionService.create_transfer_to_from_wallet_transaction(
            user=user,
            amount=float(data.get("amount")),
            wallet_type=WalletTypes.AJO_SPENDING,
            transaction_form_type=TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT,
            status=Status.PENDING,
            transaction_source=TransactionSource.WALLET,
            transaction_destination=TransactionDestination.BANK_ACCOUNT,
            transaction_description=data.get("narration"),
            ajo_user=ajo_user,
            transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
            transfer_provider=DisbursementProviderType.CASH_CONNECT,
        )
    )

    ref = str(transaction_instance.id)

    data["ref"] = ref

    try:
        bal_before = wallet.available_balance

        WalletSystem.deduct_balance(
            wallet=wallet,
            amount=float(data.get("amount")),
            transaction_instance=transaction_instance,
            unique_reference=ref,
            onboarded_user=ajo_user,
        )

        wallet.refresh_from_db()
        transaction_instance.beneficiary_name = data.get("account_name")
        transaction_instance.beneficiary_account = data.get("account_number")
        transaction_instance.beneficiary_bank = data.get("bank_code")
        transaction_instance.unique_reference = ref
        transaction_instance.wallet_balance_before = bal_before
        transaction_instance.wallet_balance_after = wallet.available_balance
        transaction_instance.request_data = data
        transaction_instance.save()

        # if check_inflow_outflow_balance == False:
        #     transaction_instance.marked_for_excess_transfer = True
        #     transaction_instance.save()
        #     res_data = {
        #         "status": False,
        #         "errors": "Transfer would exceed wema inflow total",
        #     }
        #     response.status_code = 400
        # else:
        response = CoreBankingManager.transfer_with_cash_connect(
            transaction_reference=ref,
            amount=float(data.get("amount")),
            beneficiary_account_number=data.get("account_number"),
            beneficiary_account_name=data.get("account_name"),
            bank_code=data.get("bank_code"),
            narration=data.get("narration"),
        )
        res_data = response.json()

        transaction_instance.payload = res_data
        transaction_instance.save()

        CashConnectTransferLog.objects.create(
            transaction=transaction_instance,
            wallet=wallet,
            payload=data,
            response=res_data,
        )

        if response.status_code == 200 and res_data.get("status") == "success":
            return {"status": True, "data": res_data.get("data")}
        else:
            return {"status": False, "data": res_data.get("errors")}
    except json.JSONDecodeError:
        CashConnectTransferLog.objects.create(
            transaction=transaction_instance,
            wallet=wallet,
            payload=data,
            content=response.content,
        )
        return {"status": False, "data": response.content}
