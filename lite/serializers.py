import time
from rest_framework import serializers
from ajo.models import AjoUser
from django.contrib.auth.hashers import make_password
from accounts.models import CustomUser
from rest_framework_simplejwt.tokens import RefreshToken
import random
from drf_extra_fields.fields import Base64<PERSON>mageField
from libertyBiometrics.biometrics import Biometrics
from lite.models import Beneficiary, LiteUserDevice, PasswordResetOTP
from lite.utils import (
    check_and_update_referral,
    create_wema_account,
    retrieve_wallet,
    send_sms,
    validate_kyc,
)
from django.db import transaction
from django.contrib.auth.hashers import check_password
from loans.enums import LoanStatus
from loans.models import AjoLoan
from payment.model_choices import WalletTypes
from rest_framework.exceptions import APIException
from payment.models import Transaction, WalletSystem
from django.core.files.base import ContentFile
import base64
import re
from django.conf import settings


class AjoUserCheckSerializer(serializers.Serializer):
    """
    Serializer for checking AjoUser details.
    """

    phone = serializers.CharField()

    base_64_image = serializers.CharField()

    def validate_phone(self, value):
        pattern = r"^(080|081|070|091|090)\d{8}$"
        if not re.match(pattern, value):
            raise serializers.ValidationError(
                "Phone number must be in the format 080..., 081..., 070..., 090... or 091... and must be 11 digits long."
            )
        return value


class PasscodeSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=15)
    email = serializers.EmailField(required=False, allow_blank=True, allow_null=True)
    password = serializers.CharField(write_only=True)
    re_password = serializers.CharField(write_only=True)
    referral_code = serializers.CharField(
        required=False, allow_blank=True, allow_null=True
    )
    device_fingerprint = serializers.CharField(max_length=255)
    device_name = serializers.CharField(max_length=255)

    def validate(self, data):
        device_fingerprint = data.get("device_fingerprint")

        if LiteUserDevice.objects.filter(
            device_fingerprint=device_fingerprint
        ).exists():
            raise serializers.ValidationError(
                "another user is already registered with this device."
            )

        if data["password"] != data["re_password"]:
            raise serializers.ValidationError("passwords do not match")
        if not AjoUser.objects.filter(phone_number=data["phone_number"]).exists():
            raise serializers.ValidationError("not a phone number")
        return data

    def create(self, validated_data):
        try:
            with transaction.atomic():
                # Check if user already exists
                if CustomUser.objects.filter(
                    user_phone=validated_data["phone_number"]
                ).exists():
                    raise serializers.ValidationError(
                        detail={
                            "phone": "A user with this phone number already exists."
                        }
                    )
                if validated_data.get("email", None) is None:
                    email = f"{validated_data['phone_number']}@liberty-lite.com"
                else:
                    email = validated_data.get("email")
                # Create custom user
                user = CustomUser.objects.create(
                    user_phone=validated_data["phone_number"],
                    user_password=make_password(validated_data["password"]),
                    user_type="lite",
                    email=email,
                    customer_user_id=int(time.time() * 1000),
                )
                # Get and update ajo user
                ajo_user = AjoUser.objects.get(
                    phone_number=validated_data["phone_number"]
                )
                ajo_user.lite_user = user
                ajo_user.lite_bvn_check = True
                ajo_user.save()

                # Create Wema account
                create_wema_account(ajo_user=ajo_user)
                referral_code = validated_data.get("referral_code", None)

                if referral_code:
                    check_and_update_referral(referral_code, user)

                LiteUserDevice.objects.create(
                    user=user,
                    device_fingerprint=validated_data["device_fingerprint"],
                    device_name=validated_data["device_name"],
                )

                return user

        except (AjoUser.DoesNotExist, serializers.ValidationError, Exception) as e:
            raise serializers.ValidationError(str(e))


class TransactionPinSerializer(serializers.Serializer):
    pin = serializers.CharField(write_only=True)
    re_pin = serializers.CharField(write_only=True)

    def validate(self, data):
        if data["pin"] != data["re_pin"]:
            raise serializers.ValidationError("Pins do not match")
        return data


class ChangePasscodeSerializer(serializers.Serializer):
    old_passcode = serializers.CharField(required=True)
    passcode = serializers.CharField(required=True)
    confirm_passcode = serializers.CharField(required=True)

    def validate(self, attrs):
        if attrs["passcode"] != attrs["confirm_passcode"]:
            raise serializers.ValidationError("passcodes don't match")
        return attrs


class ChangePinSerializer(serializers.Serializer):
    old_pin = serializers.CharField(required=True)
    pin = serializers.CharField(required=True)
    confirm_pin = serializers.CharField(required=True)

    def validate(self, attrs):
        if attrs["pin"] != attrs["confirm_pin"]:
            raise serializers.ValidationError("pins don't match")
        return attrs


class LiteUserRegistrationSerializer(serializers.Serializer):
    image = serializers.CharField()
    phone_number = serializers.CharField(max_length=15, required=True)
    first_name = serializers.CharField(max_length=30, required=True)
    last_name = serializers.CharField(max_length=30, required=True)
    email = serializers.EmailField(required=True)
    gender = serializers.ChoiceField(choices=["Male", "Female", "Other"], required=True)
    house_number = serializers.CharField(max_length=10, required=True)
    address = serializers.CharField(max_length=255, required=True)
    state = serializers.CharField(max_length=50, required=True)
    bvn = serializers.CharField(max_length=11, required=True)
    lga = serializers.CharField(max_length=50, required=True)
    dob = serializers.DateField(required=False)
    password = serializers.CharField(write_only=True, required=True)
    re_password = serializers.CharField(write_only=True, required=True)
    referral_code = serializers.CharField(
        required=False, allow_blank=True, allow_null=True
    )
    device_fingerprint = serializers.CharField(max_length=255)
    device_name = serializers.CharField(max_length=255)

    def validate(self, data):
        device_fingerprint = data.get("device_fingerprint")

        if LiteUserDevice.objects.filter(
            device_fingerprint=device_fingerprint
        ).exists():
            raise serializers.ValidationError(
                "another user is already registered with this device."
            )

        if data.get("password") != data.get("re_password"):
            raise serializers.ValidationError("Passwords do not match")
        # if not data.get("image"):
        #     raise serializers.ValidationError(detail={"image": ["Image is required"]})
        bvn = data.get("bvn")
        res = validate_kyc(bvn)
        if not res.get("status", False):
            raise serializers.ValidationError(
                detail={"bvn": ["BVN verification failed. please pass a valid BVN"]}
            )
        res_data = res.get("data")
        uploaded_img = data["image"]
        biometrics = Biometrics()
        request_ref = biometrics.generate_unique_ref()
        image_bytes1 = Biometrics.convert_b64_2_jpg(uploaded_img)
        image_bytes2 = Biometrics.convert_b64_2_jpg(res_data.get("image"))

        # Perform face matching
        face_match_response = biometrics.face_match(
            request_ref=request_ref,
            face_one=image_bytes1,
            face_two=image_bytes2,
        )

        similarity = float(
            face_match_response.get("data", {}).get("data", {}).get("similarity", 0)
        )

        if settings.ENVIRONMENT == "development":
            return data
        if similarity < 80:
            raise serializers.ValidationError(
                detail={"image": ["image doesn't match BVN image"]}
            )
        return data

    def create(self, validated_data):
        validated_data["address"] = (
            f"{validated_data['house_number']}, {validated_data['address']}"
        )
        image_base64 = validated_data.pop("image")

        with transaction.atomic():
            # Create CustomUser instance
            custom_user = CustomUser.objects.create(
                user_phone=validated_data["phone_number"],
                user_password=make_password(validated_data["password"]),
                user_type="lite",
                email=validated_data["email"],
                customer_user_id=int(time.time() * 1000),
            )

            # Create AjoUser instance
            ajo_user = AjoUser.objects.create(
                phone_number=validated_data["phone_number"],
                first_name=validated_data["first_name"],
                last_name=validated_data["last_name"],
                gender=validated_data["gender"],
                address=validated_data["address"],
                state=validated_data["state"],
                bvn=validated_data["bvn"],
                lga=validated_data["lga"],
                lite_user=custom_user,
                dob=validated_data["dob"],
                lite_bvn_check=True,
                provider="lite",
            )

            try:
                ext = image_base64.split("/")[-1]
                image_data = ContentFile(
                    base64.b64decode(image_base64),
                    name=f"{validated_data['first_name']}_{validated_data['last_name']}.{ext}",
                )

            except Exception as e:
                print(e)
                raise serializers.ValidationError(
                    {"image": "Invalid Base64 image string."}
                )

            ajo_user.image.save(image_data.name, image_data, save=True)
            # if not create_user_agency(ajo_user=ajo_user):
            #     raise serializers.ValidationError("unable to create user on agency banking")

            data = create_wema_account(ajo_user=ajo_user)
            if isinstance(data, dict):
                raise APIException(detail=data, code=400)

            referral_code = validated_data.get("referral_code", None)

            if referral_code:
                check_and_update_referral(referral_code, custom_user)

            LiteUserDevice.objects.create(
                user=custom_user,
                device_fingerprint=validated_data["device_fingerprint"],
                device_name=validated_data["device_name"],
            )
            return ajo_user

    def update(self, instance, validated_data):
        """Update the CustomUser and AjoUser instances"""
        ajo_user = instance

        for attr, value in validated_data.items():
            if attr == "password":
                ajo_user.lite_user.user_password = make_password(value)
            else:
                setattr(ajo_user, attr, value)

        ajo_user.save()
        ajo_user.lite_user.save()

        return ajo_user


class LoginSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=15)
    password = serializers.CharField(write_only=True)
    device_fingerprint = serializers.CharField(max_length=255)
    device_name = serializers.CharField(max_length=255)

    def validate(self, data):
        user = CustomUser.objects.filter(
            user_phone=data["phone_number"],
            user_type="lite",
        ).first()

        if user is None:
            raise serializers.ValidationError("invalid credentials")

        if not check_password(data["password"], user.user_password):
            raise serializers.ValidationError("invalid credentials")

        if not LiteUserDevice.objects.filter(user=user).exists():
            device_fingerprint = data.get("device_fingerprint")

            if LiteUserDevice.objects.filter(
                device_fingerprint=device_fingerprint
            ).exists():
                raise serializers.ValidationError(
                    "another user is already registered with this device."
                )

            LiteUserDevice.objects.create(
                user=user,
                device_fingerprint=data["device_fingerprint"],
                device_name=data["device_name"],
            )

        if user.liteuserdevice.device_fingerprint != data["device_fingerprint"]:
            raise serializers.ValidationError(
                "unable to login from unregistered device"
            )
        return user

    def create(self, validated_data):
        user = validated_data
        refresh = RefreshToken.for_user(user)
        return {"refresh": str(refresh), "access": str(refresh.access_token)}


class ForgotPasswordSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=15)
    device_fingerprint = serializers.CharField(max_length=255)

    def validate_phone_number(self, value):
        if not CustomUser.objects.filter(user_phone=value).exists():
            raise serializers.ValidationError("Phone number not registered")
        return value

    def create(self, validated_data, pin_type="passcode"):
        # Generate a random verification code
        verification_code = str(random.randint(100000, 999999))

        # Save the OTP in the database
        user = CustomUser.objects.get(user_phone=validated_data["phone_number"])

        if not LiteUserDevice.objects.filter(user=user).exists():
            raise serializers.ValidationError("No device registered for this user.")

        if (
            user.liteuserdevice.device_fingerprint
            != validated_data["device_fingerprint"]
        ):
            raise serializers.ValidationError(
                "unable to login from unregistered device"
            )

        otp_entry = PasswordResetOTP(
            user=user, otp=verification_code, ajo_user=user.lite_users
        )
        otp_entry.save()

        # Send the SMS with the verification code
        message = f"Hello, your OTP code is {verification_code}. Please enter this code to reset your {pin_type}."
        send_sms(validated_data["phone_number"], message)

        return {"message": "OTP sent successfully.", "code": verification_code}


class ValidatePhoneSerializer(serializers.Serializer):
    phone = serializers.CharField(max_length=15)


class ConfirmOtpSerializer(serializers.Serializer):
    phone = serializers.CharField(max_length=15)
    verification_code = serializers.IntegerField()


class ResetPasswordSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=15)
    verification_code = serializers.IntegerField()
    new_password = serializers.CharField(write_only=True)
    re_new_password = serializers.CharField(write_only=True)

    def validate(self, data):
        try:
            otp_entry = PasswordResetOTP.objects.get(
                user__user_phone=data["phone_number"], otp=data["verification_code"]
            )
            if otp_entry.is_expired():
                raise serializers.ValidationError("Verification code has expired.")
            if data["new_password"] != data["re_new_password"]:
                raise serializers.ValidationError(
                    detail={
                        "new_password": "passwords dont match",
                        "re_new_password": "passwords dont match",
                    }
                )
        except PasswordResetOTP.DoesNotExist:
            raise serializers.ValidationError("Invalid verification code.")

        return data

    def create(self, validated_data):
        user = CustomUser.objects.get(user_phone=validated_data["phone_number"])
        user.user_password = make_password(validated_data["new_password"])
        user.save()
        return user


class ResetTransactionPinSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=15)
    verification_code = serializers.IntegerField()
    new_pin = serializers.CharField(write_only=True)
    re_new_pin = serializers.CharField(write_only=True)

    def validate(self, data):
        try:
            otp_entry = PasswordResetOTP.objects.get(
                user__user_phone=data["phone_number"], otp=data["verification_code"]
            )
            if otp_entry.is_expired():
                raise serializers.ValidationError("Verification code has expired.")
            if data["new_pin"] != data["re_new_pin"]:
                raise serializers.ValidationError(
                    detail={
                        "new_pin": "transaction dont match",
                        "re_new_pin": "transaction dont match",
                    }
                )
        except PasswordResetOTP.DoesNotExist:
            raise serializers.ValidationError("Invalid verification code.")
        self.instance = otp_entry.ajo_user
        return data

    def create(self, validated_data):
        ajo_user: AjoUser = self.instance
        ajo_user.lite_transaction_pin = make_password(validated_data["new_pin"])
        ajo_user.save()
        return ajo_user


class TransferSerializer(serializers.Serializer):
    amount = serializers.FloatField()
    narration = serializers.CharField(max_length=255)
    account_name = serializers.CharField(max_length=255)
    account_number = serializers.CharField(max_length=255)
    bank_code = serializers.CharField(max_length=255)
    bank_name = serializers.CharField(max_length=255)
    pin = serializers.CharField(max_length=4)

    def validate_amount(self, value):
        if value < 0:
            raise serializers.ValidationError("Amount must not be negative.")
        if value == 0:
            raise serializers.ValidationError("Amount must not be zero.")
        return value


class BeneficiarySerializer(serializers.ModelSerializer):
    class Meta:
        model = Beneficiary
        fields = "__all__"


class TransactionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        fields = [
            "date_created",
            "transaction_form_type",
            "amount",
            "transaction_id",
            "status",
            "transaction_source",
            "description",
            "transaction_type",
            "transaction_date_completed",
            "wallet_balance_before",
            "wallet_balance_after",
            "beneficiary_name",
            "beneficiary_account",
            "beneficiary_bank",
        ]


class RepayLoanSerializer(serializers.Serializer):
    loan_id = serializers.IntegerField()
    amount = serializers.FloatField()
    transaction_pin = serializers.CharField(max_length=4)

    def validate(self, data):
        request = self.context.get("request")
        loan_id = data.get("loan_id")
        amount = data.get("amount")
        transaction_pin = data.get("transaction_pin")

        ajo_user: AjoUser = request.user.lite_users

        if not check_password(transaction_pin, ajo_user.lite_transaction_pin):
            raise serializers.ValidationError("invalid credentials")

        try:
            ajo_loan = AjoLoan.objects.get(id=loan_id, borrower=ajo_user)
        except AjoLoan.DoesNotExist:
            raise serializers.ValidationError("Invalid loan detail")

        if ajo_loan.status != LoanStatus.OPEN:
            raise serializers.ValidationError(f"Current loan is {ajo_loan.status}")

        spend_wallet, _ = retrieve_wallet(ajo_user)
        if spend_wallet.available_balance < amount:
            raise serializers.ValidationError("Insufficient funds")

        data["loan"] = ajo_loan
        data["amount"] = amount
        data["wallet"] = spend_wallet

        return data


class LiteUserDeviceSerializer(serializers.ModelSerializer):
    class Meta:
        model = LiteUserDevice
        fields = ["device_fingerprint", "device_name", "registered_at"]
        read_only_fields = ["registered_at"]

    def create(self, validated_data):
        user = self.context["request"].user
        existing_device = LiteUserDevice.objects.filter(user=user).first()

        if existing_device:
            raise serializers.ValidationError(
                "A device is already registered. Unregister it first."
            )

        # Create new device entry
        return LiteUserDevice.objects.create(user=user, **validated_data)
