from django.db import models
from django.utils import timezone
from datetime import timedelta


class PasswordResetOTP(models.Model):
    user = models.ForeignKey("accounts.CustomUser", on_delete=models.CASCADE, null=True)
    ajo_user = models.ForeignKey("ajo.AjoUser", on_delete=models.CASCADE, null=True)
    otp = models.CharField(max_length=6)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()

    def save(self, *args, **kwargs):
        self.expires_at = timezone.now() + timedelta(minutes=10)
        super().save(*args, **kwargs)

    def is_expired(self):
        return timezone.now() > self.expires_at

    def __str__(self):
        return f"OTP for {self.user.username} - Expires at {self.expires_at}"


class Beneficiary(models.Model):
    user = models.ForeignKey("accounts.CustomUser", on_delete=models.CASCADE, null=True)
    account_number = models.CharField(max_length=255)
    account_name = models.CharField(max_length=255)
    bank_code = models.CharField(max_length=255)
    bank_name = models.CharField(max_length=255)

    class Meta:
        verbose_name_plural = "beneficiaries"


class WemaTransferLog(models.Model):
    transaction = models.ForeignKey("payment.Transaction", on_delete=models.PROTECT)
    wallet = models.ForeignKey("payment.WalletSystem", on_delete=models.PROTECT)
    payload = models.JSONField()
    response = models.JSONField()
    content = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)


class MonifyTransferLog(models.Model):
    transaction = models.ForeignKey("payment.Transaction", on_delete=models.PROTECT)
    wallet = models.ForeignKey("payment.WalletSystem", on_delete=models.PROTECT)
    payload = models.JSONField()
    response = models.JSONField()
    created_at = models.DateTimeField(auto_now_add=True)


class LiteUserDevice(models.Model):
    user = models.OneToOneField("accounts.CustomUser", on_delete=models.CASCADE)
    device_fingerprint = models.CharField(max_length=64, unique=True)
    device_name = models.CharField(max_length=100, blank=True, null=True)
    registered_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username} - {self.device_name or 'Unnamed Device'}"

class CashConnectTransferLog(models.Model):
    transaction = models.ForeignKey("payment.Transaction", on_delete=models.PROTECT)
    wallet = models.ForeignKey("payment.WalletSystem", on_delete=models.PROTECT)
    payload = models.JSONField()
    response = models.JSONField()
    created_at = models.DateTimeField(auto_now_add=True)
