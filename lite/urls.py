from django.urls import path
from .views import (
    AjoLoanRepaymentAPIView,
    AjoUserDetailView,
    ChangePasscodeView,
    ChangePinView,
    CheckAjoUserView,
    ConfirmValidatePhoneNumber,
    CreatePasscodeView,
    CreateTransactionPinView,
    ForgotPasswordView,
    LiteUserRegistrationView,
    LoanRepaymentHistory,
    LoginView,
    RegisterDeviceView,
    ResetPasswordView,
    ResolveAccountView,
    TransactionHistory,
    TransactionRetrieveView,
    TransferView,
    FinanceDataView,
    BeneficiaryListCreateView,
    BeneficiaryRetrieveUpdateDestroyView,
    ValidatePhoneNumber,
    ForgotTransPinView,
    ResetTransactionPinView
)

urlpatterns = [
    path("check-ajo-user/", CheckAjoUserView.as_view(), name="check-ajo-user"),
    path(
        "check-ajo-user/phone/",
        ValidatePhoneNumber.as_view(),
        name="check-ajo-user-with-phone",
    ),
    path(
        "check-ajo-user/validate-otp/",
        ConfirmValidatePhoneNumber.as_view(),
        name="confirm-phone-otp",
    ),
    path("user/", AjoUserDetailView.as_view(), name="user-detail"),
    path("create-passcode/", CreatePasscodeView.as_view(), name="create-passcode"),
    path(
        "create-transaction-pin/",
        CreateTransactionPinView.as_view(),
        name="create-transaction-pin",
    ),
    path(
        "change-pin/",
        ChangePinView.as_view(),
        name="change-transaction-pin",
    ),
    path(
        "change-password/",
        ChangePasscodeView.as_view(),
        name="create-password",
    ),
    path(
        "register/",
        LiteUserRegistrationView.as_view(),
        name="register-lite-user",
    ),
    path("login/", LoginView.as_view(), name="login"),
    path("forgot-password/", ForgotPasswordView.as_view(), name="forgot-password"),
    path(
        "forgot-password/confirm/",
        ResetPasswordView.as_view(),
        name="confirm-forgot-password",
    ),
    path("forgot-pin/", ForgotTransPinView.as_view(), name="forgot-pin"),
    path(
        "forgot-pin/confirm/",
        ResetTransactionPinView.as_view(),
        name="confirm-forgot-pin",
    ),
    path("wallet/data/", FinanceDataView.as_view(), name="finance-data"),
    path("wallet/transfer/", TransferView.as_view(), name="transfer"),
    path(
        "wallet/beneficiaries/",
        BeneficiaryListCreateView.as_view(),
        name="beneficiary-list-create",
    ),
    path(
        "wallet/beneficiaries/<int:pk>/",
        BeneficiaryRetrieveUpdateDestroyView.as_view(),
        name="beneficiary-detail",
    ),
    path("wallet/transactions/", TransactionHistory.as_view(), name="transaction-list"),
    path(
        "wallet/transactions/<uuid:pk>/",
        TransactionRetrieveView.as_view(),
        name="transaction-detail",
    ),
    path(
        "wallet/resolve-account/",
        ResolveAccountView.as_view(),
        name="resolve-account",
    ),
    path(
        "loans/repayment_history/",
        LoanRepaymentHistory.as_view(),
        name="lite-user-loan-repayment-history",
    ),
    path(
        "loans/repay/",
        AjoLoanRepaymentAPIView.as_view(),
        name="repay-loan",
    ),
    path("register-device/", RegisterDeviceView.as_view(), name="register-device"),

]
