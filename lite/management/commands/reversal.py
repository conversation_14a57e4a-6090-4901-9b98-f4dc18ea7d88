from ajo.payment_actions import perform_reversal_for_failed_external_transfers
from loans.helpers.core_banking import CoreBankingManager
from payment.model_choices import DisbursementProviderType, Status, TransactionFormType
from payment.models import Transaction
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = "Verify and reverse pending Wema transactions"

    def handle(self, *args, **kwargs):
        pending_transactions = Transaction.objects.filter(
            status=Status.PENDING,
            transaction_form_type=TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT,
            transfer_provider=DisbursementProviderType.WEMA_PROVIDER,
        )

        for transaction in pending_transactions:
            res = CoreBankingManager.verify_from_wema(transaction.unique_reference)
            print(
                f"Transaction ID: {transaction.transaction_id}, \nVerification Result: {res}"
            )

            user_input = input(
                "Do you want to continue and reverse this transaction? (yes/no): "
            )
            if user_input.lower() == "yes":
                reversal = perform_reversal_for_failed_external_transfers(transaction)
                print(f"Transaction {transaction.transaction_id} reversed: {reversal}")
            else:
                print(f"Skipping transaction {transaction.transaction_id}.")
