from django.core.management.base import BaseCommand
from django.db.models import Count
from ajo.models import AjoUser
from loans.models import AjoLoan
from accounts.models import CustomUser
import random


class Command(BaseCommand):
    help = "Update phone numbers of duplicate users without loans"

    def handle(self, *args, **kwargs):
        duplicate_phone_numbers = (
            AjoUser.objects.values("user_phone")
            .annotate(phone_count=Count("user_phone"))
            .filter(phone_count__gt=1)
            .values_list("user_phone", flat=True)
        )

        duplicate_users = AjoUser.objects.filter(
            user_phone__in=duplicate_phone_numbers
                )

            # duplicate_users_with_loans = [
            #     user
            #     for user in duplicate_users
            #     if AjoLoan.objects.filter(borrower=user).exists()
            # ]
        duplicate_users_ajo_users = [
            user
            for user in duplicate_users
            if AjoUser.objects.filter(user=user).exists()
        ]

        users_without_loans = [
            user for user in duplicate_users if user not in duplicate_users_ajo_users
        ]

        for user in users_without_loans:
            user.phone_number += f"---{random.randint(50,100)}"
            user.save()

        # AjoUser.objects.all().delete()
        # CustomUser.objects.all().delete()
        self.stdout.write(
            self.style.SUCCESS(
                "Successfully updated phone numbers of duplicate users without loans."
            )
        )


# with open(
#     "/home/<USER>/savings/static/duplicate_ajousers_with_loans.csv",
#     "w",
#     newline="",
# ) as csvfile:
#     writer = csv.writer(csvfile)
#     writer.writerow(
#         [
#             "id",
#             "phone_number",
#             "agent",
#             "first_name",
#             "last_name",
#             "image",
#             "bvn",
#             "gender",
#             "dob",
#         ]
#     )
#     writer.writerows(
#         [
#             [
#                 user.id,
#                 user.phone_number,
#                 user.user.email,
#                 user.first_name,
#                 user.last_name,
#                 user.image,
#                 user.bvn,
#                 user.gender,
#                 user.dob,
#             ]
#             for user in duplicate_users_with_loans
#         ]
#     )


# all_users = AjoUser.objects.all()

# for user in all_users:
#     # Check if the user has any loans
#     has_loans = AjoLoan.objects.filter(borrower=user).exists()
    
#     # If the user has no loans and phone number is longer than 11 and does not already contain '---'
#     if not has_loans and len(user.phone_number) > 11 and '---' not in user.phone_number:
#         old_phone_number = user.phone_number  # Store the old phone number
#         user.phone_number = user.phone_number[:11] + '---' + user.phone_number[11:]
#         user.save()
#         print(f"Updated phone number for user {user.id}: {old_phone_number} -> {user.phone_number}")
