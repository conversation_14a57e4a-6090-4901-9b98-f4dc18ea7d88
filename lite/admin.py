from django.contrib import admin

from lite.models import WemaTransferLog, MonifyTransferLog, LiteUserDevice



@admin.register(WemaTransferLog)
class WemaTransferLogAdmin(admin.ModelAdmin):
    list_display = (
        "transaction",
        "wallet",
        "payload",
        "response",
        "content",
        "created_at",
    )
    list_filter = ("created_at",)


@admin.register(MonifyTransferLog)
class MonifyTransferLogAdmin(admin.ModelAdmin):
    list_display = ("transaction", "wallet", "payload", "response", "created_at")
    list_filter = ("created_at",)


@admin.register(LiteUserDevice)
class LiteUserDeviceAdmin(admin.ModelAdmin):
    list_display = ("user", "device_fingerprint", "device_name", "registered_at")
    search_fields = ("user__user_phone", "device_fingerprint", "user__id")
    list_filter = ("registered_at",)
