import requests
from bs4 import BeautifulSoup
import pandas as pd
from datetime import datetime
import time
import csv
import os
from anthropic import Anthropic
from openai import OpenAI
import openai
from typing import Dict, List, Optional
import json
import logging
import io
import random


def get_completion(prompt, client_instance, system_msg=None, model="gpt-4o"):
    messages = [{"role": "user", "content": prompt}]
    if system_msg:
        messages.append({"role": "system", "content": system_msg})

    response = client_instance.chat.completions.create(
        model=model,
        messages=messages,
        temperature=0.0,
    )
    return response


class LLMEnhancedScraper:
    def __init__(self, base_url: str, openai_key: str, anthropic_key: str):
        self.base_url = base_url
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/91.0.4472.124"
        }
        # Initialize LLM clients
        self.anthropic = Anthropic(api_key=anthropic_key)
        openai.api_key = openai_key

        # Set up logging
        logging.basicConfig(
            filename="scraper.log",
            level=logging.INFO,
            format="%(asctime)s - %(levelname)s - %(message)s",
        )

        # Create necessary directories
        os.makedirs("price_data", exist_ok=True)
        os.makedirs("analysis", exist_ok=True)

    def send_prompt_to_chatgpt(self, prompt, system_msg=None, model="gpt-4o"):

        api_keys = ["***************************************************"]

        # Shuffle the API keys randomly
        random.shuffle(api_keys)

        # Try each API key until a successful request is made or all keys are exhausted
        for api_key in api_keys:
            client = OpenAI(api_key=api_key)

            try:
                response = get_completion(prompt, client, system_msg, model)

                # Extract relevant information from the response object
                response_data = response.choices[0].message.content

                return response_data
            except SyntaxError as err:
                print(f"An error occurred with API key {api_key}: {err}")
                continue  # Try the next API key if the current one fails

        raise Exception("All API keys failed to get a successful response from OpenAI.")

    def analyze_html_structure(self, html: str) -> Dict[str, str]:

        response_content = {
            "container": "article.prd",  # Product container
            "price": ".prc",  # Price selector
            "title": ".name",  # Title selector
            "description": ".desc",  # Description selector
            "availability": ".availability",  # Availability selector
            "image": "img[src]",  # Image selector
        }

        return response_content

    def extract_product_data(self, html: str, selectors: Dict[str, str]) -> List[Dict]:
        """Extract all product data using identified selectors."""
        soup = BeautifulSoup(html, "html.parser")
        all_products = []

        # Use the container selector identified by the LLM
        product_containers = soup.select(selectors["container"])

        for container in product_containers:
            data = {}
            for key, selector in selectors.items():
                try:
                    if key == "image":  # Handle image extraction
                        element = container.select_one(selector)
                        if element:
                            # Check for data-src first, then fall back to src
                            data[key] = element.get("data-src") or element.get("src")
                    else:
                        element = container.select_one(selector)
                        if element:
                            data[key] = element.text.strip()
                except Exception as e:
                    logging.error(f"Error extracting {key}: {str(e)}")
                    data[key] = None

            if data:  # Only add if we found data
                data.update(
                    {"url": self.base_url, "date": datetime.now().strftime("%Y-%m-%d")}
                )
                all_products.append(data)

        return all_products

    def validate_and_clean_price(self, price_text: str) -> float:
        """Use GPT to validate and clean price data."""
        try:
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {
                        "role": "user",
                        "content": f"Extract the numerical price value from this text: '{price_text}'. "
                        f"Return only the number with two decimal places, no currency symbols or commas.",
                    }
                ],
            )
            price = float(response.choices[0].message.content.strip())
            return price
        except Exception as e:
            logging.error(f"Error validating price: {str(e)}")
            return None

    def scrape_product(self, url: str) -> List[Dict]:
        """Scrape all products with LLM assistance."""
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()

        # Analyze HTML structure
        selectors = self.analyze_html_structure(response.text)

        # Extract data using identified selectors
        raw_data_list = self.extract_product_data(response.text, selectors)

        return raw_data_list


# scraper = LLMEnhancedScraper('', "***************************************************","")

# scrapped_file_jumia = scraper.scrape_product("https://www.jumia.com.ng/catalog/?q=phones")
# print(scrapped_file_jumia)
