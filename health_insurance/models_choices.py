from django.db import models
from django.utils.translation import gettext_lazy as _


class HealthInsurancePlanType(models.TextChoices):
    ROSE = "ROSE", _("Rose")
    LOTUS = "LOTUS", _("Lotus")
    TULIP = "TULIP", _("Tulip")
    HAZEL = "<PERSON><PERSON><PERSON><PERSON>", _("<PERSON>")
    IVY = "IVY", _("Ivy")
    MARIGOLD = "MARIGOLD", _("Marigold")
    MARIGOLD_PLUS = "MARIGOLD_PLUS", _("Marigold Plus")


class HealthInsuranceStatus(models.TextChoices):
    PENDING = "PENDING", _("Pending")
    ACTIVATED = "ACTIVATED", _("Activated")
    COMPLETED = "COMPLETED", _("Completed")
    CANCELLED = "CANCELLED", _("Cancelled")
    
    
class PlanDuration(models.TextChoices):
        ONE_MONTH = 1, "1 month"
        SIX_MONTHS = 6, "6 months"
        TWELVE_MONTHS = 12, "12 months"
        
class SavingsType(models.TextChoices):
    AJO_SAVINGS = "AJO_SAVINGS", _("AJO SAVINGS")
    HEALTH_SAVINGS = "HEALTH_SAVINGS", _("HEALTH SAVINGS")
    
class SaveHealthTypes(models.TextChoices):
    ACTIVATION = "ACTIVATION", "ACTIVATION"
    FULL_PLAN = "FULL_PLAN", "FULL_PLAN"

class LogsStatus(models.TextChoices):
    SUCCESS = "SUCCESS", "SUCCESS"
    PENDING = "PENDING", "PENDING"
    FAILED = "FAILED", "FAILED"