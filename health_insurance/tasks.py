from celery import shared_task

from ajo.model_choices import SavingsType
from health_insurance.models import SaveForHealthInsurance
from health_insurance.models_choices import LogsStatus, SaveHealthTypes
from loans.helpers.libertylife import LibertyLifeMgr
from ajo.models import AjoSaving
from .models import SaveForHealthLogs

@shared_task
def request_savings_plan_creation(
    policy_fee: float,
    activation_fee: float,
    payment_reference: str,
    plan_for: str,
    plan_type: str,
    savings_type: SavingsType,
    request_type: SaveHealthTypes,
    save_for_health_log: SaveForHealthLogs = None,
    ajo_savings_id: int = None,
    health_savings_id: int = None,
):
    if health_savings_id is not None:
        health_savings = SaveForHealthInsurance.objects.filter(id=health_savings_id).first()
        
        liberty_life_plan = LibertyLifeMgr().create_plan_for_savings(
            policy_fee=policy_fee,
            activation_fee=activation_fee,
            payment_reference=payment_reference,
            plan_for=plan_for,
            plan_type=plan_type,
            savings_type=savings_type,
            health_savings=health_savings,
        )
    else:
        ajo_savings = AjoSaving.objects.filter(id=ajo_savings_id).first()
        health_savings = SaveForHealthInsurance.objects.filter(savings=ajo_savings).first()
        
        liberty_life_plan = LibertyLifeMgr().create_plan_for_savings(
            policy_fee=policy_fee,
            activation_fee=activation_fee,
            payment_reference=payment_reference,
            plan_for=plan_for,
            plan_type=plan_type,
            savings_type=savings_type,
            ajo_savings=ajo_savings,
        )

    
    status_code = liberty_life_plan.get("status_code")

    save_for_health_log.request_result = liberty_life_plan
    save_for_health_log.request_type = request_type
    save_for_health_log.save()
   
    success_code = [200, 201]
    if status_code in success_code:
        response = liberty_life_plan.get("response")
        liberty_life_plan = response.get("health_plan_data")
        enrolee_id = liberty_life_plan.get("enrolee").get("id")
        health_savings.liberty_life_enrolee_id = enrolee_id
        health_savings.liberty_life_plan_id = liberty_life_plan.get("id")
        health_savings.save()
        
        save_for_health_log.status = LogsStatus.SUCCESS
        save_for_health_log.save()
        if request_type == SaveHealthTypes.ACTIVATION:
            health_savings.is_test_plan_created = True
            health_savings.save()
        else:
            health_savings.is_full_plan_created = True
            health_savings.save()

    else:
        save_for_health_log.status = LogsStatus.FAILED
        save_for_health_log.failure_reason = liberty_life_plan
        save_for_health_log.status = LogsStatus.FAILED
        save_for_health_log.save()
       