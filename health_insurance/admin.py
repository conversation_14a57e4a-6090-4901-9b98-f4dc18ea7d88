from django.contrib import admin

# Register your models here.

from .models import SaveForHealthInsurance, SaveForHealthLogs
from import_export import resources
from import_export.admin import ImportExportModelAdmin

class SaveForHealthInsuranceResource(resources.ModelResource):

    class Meta:
        model = SaveForHealthInsurance

class SaveForHealthInsuranceAdmin(ImportExportModelAdmin):
    resource_class = SaveForHealthInsuranceResource
    search_fields = [
        "name",
        "plan_type",
        "quotation_id",
        "user__email",
        "ajo_user__phone_number",
    ]
    list_filter = [
        "plan_type",
        "status",
        "is_active",
        "is_activated",
        "completed",
    ]

    # def get_list_display(self, request):
    #     return [field.name for field in self.model._meta.concrete_fields]

    # def update_plan_fees(self, request, queryset):
    #     """Update the policy and activation fees based on the plan type"""
    #     response = {}
    #     for instance in queryset:
    #         response_key = f"{instance.name} - {instance.plan_type}"
    #         result = instance.update_plan_fees()
    #         response[response_key] = "Fees updated successfully" if result else "Failed to update fees"

    #     self.message_user(request, str(response))

    # def calculate_total_amount(self, request, queryset):
    #     """Calculate the balance amount needed for the health insurance plan"""
    #     response = {}
    #     for instance in queryset:
    #         response_key = f"{instance.name} - {instance.plan_type}"
    #         total = instance.calculate_total_amount()
    #         response[response_key] = f"Balance to save: {total} (Policy fee: {instance.policy_fee}, Activation fee: {instance.activation_fee})"

    #     self.message_user(request, str(response))

    # def activate_plans(self, request, queryset):
    #     """Activate eligible health insurance plans"""
    #     response = {}
    #     for instance in queryset:
    #         response_key = f"{instance.name} - {instance.plan_type}"
    #         result = instance.activate_plan()
    #         response[response_key] = "Plan activated successfully" if result else "Failed to activate plan"

    #     self.message_user(request, str(response))

    # def create_health_plan_from_savings(self, request, queryset):
    #     """Create a health insurance plan from an AjoSaving instance"""
    #     response = {}
    #     for instance in queryset:
    #         if not instance.savings:
    #             response[f"{instance.name}"] = "No savings associated with this plan"
    #             continue

    #         # Get the KYC instance if available
    #         kyc_instance = None
    #         try:
    #             from loans.models import LoanKYCDocumentation
    #             kyc_instance = LoanKYCDocumentation.objects.filter(savings=instance.savings).first()
    #         except Exception:
    #             pass

    #         _, message = SaveForHealthInsurance.create_savings_health_plan(
    #             savings=instance.savings,
    #             plan_type=instance.plan_type,
    #             name=instance.name,
    #             kyc_instance=kyc_instance,
    #         )

    #         response[f"{instance.name}"] = message

    #     self.message_user(request, str(response))

    # update_plan_fees.short_description = "Update plan fees from ConstantTable"
    # calculate_total_amount.short_description = "Calculate balance to save"
    # activate_plans.short_description = "Activate eligible plans"
    # create_health_plan_from_savings.short_description = "Create health plan from savings"

    # actions = [
    #     update_plan_fees,
    #     calculate_total_amount,
    #     activate_plans,
    #     create_health_plan_from_savings,
    # ]

class SaveForHealthLogsResource(resources.ModelResource):
    class Meta:
        model = SaveForHealthLogs

class SaveForHealthLogsAdmin(ImportExportModelAdmin):
    resource_class = SaveForHealthLogsResource
    search_fields = [
        "health_savings__name",
        "health_savings__quotation_id",
        "health_savings__user__email",
        "health_savings__ajo_user__phone_number",
        "request_type",
    ]
    list_filter = [
        "status",
        "request_type",
        "created_at",
        "updated_at",
    ]

    list_display = [
        "id",
        "health_savings",
        "status",
        "request_type",
        "created_at",
        "updated_at",
    ]

    readonly_fields = [
        "created_at",
        "updated_at",
    ]

admin.site.register(SaveForHealthInsurance, SaveForHealthInsuranceAdmin)
admin.site.register(SaveForHealthLogs, SaveForHealthLogsAdmin)
