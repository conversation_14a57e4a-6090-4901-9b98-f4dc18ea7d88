from django.db import IntegrityError, models

# Create your models here.
from django.conf import settings

from accounts.helpers import BaseModel
from accounts.models import ConstantTable
from ajo.models import AjoSaving, AjoUser, generate_quotation_id
from health_insurance.models_choices import HealthInsurancePlanType, HealthInsuranceStatus, LogsStatus, PlanDuration, SaveHealthTypes
from loans.enums import InsuranceRequestStatus, InsuranceRequestType
from payment.model_choices import DisbursementProviderType, Status, TransactionFormType, TransactionTypeCreditOrDebitChoices
from payment.services import TransactionService

from django.utils import timezone
from datetime import timedelta

class SaveForHealthInsurance(BaseModel):
    """
    Model for users to save towards getting a health insurance plan.
    This model is related to AjoSavings and allows users to save
    towards different health insurance plan types.
    """
    # User information
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="health_insurance_savings",
        db_index=True,
    )
    ajo_user = models.ForeignKey(
        AjoUser,
        on_delete=models.CASCADE,
        related_name="health_insurance_savings",
        null=True,
        blank=True,
    )

    # Plan information
    name = models.CharField(max_length=100)
    plan_type = models.CharField(
        max_length=100,
        choices=HealthInsurancePlanType.choices,
        default=HealthInsurancePlanType.ROSE,
    )
    status = models.CharField(
        max_length=100,
        choices=HealthInsuranceStatus.choices,
        default=HealthInsuranceStatus.PENDING,
    )

    # Savings information
    savings = models.OneToOneField(
        AjoSaving,
        on_delete=models.CASCADE,
        related_name="health_insurance_plan",
        null=True,
        blank=True,
    )
    quotation_id = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        unique=True,
        default=generate_quotation_id,
    )

    # Financial information
    policy_fee = models.FloatField(default=0.0, help_text="The total cost of the health insurance policy")
    activation_fee = models.FloatField(default=0.0, help_text="The upfront fee to activate the health insurance plan")
    total_amount = models.FloatField(default=0.0, help_text="The total fee (policy + activation) to create the health insurance plan")
    balance_amount = models.FloatField(default=0.0, help_text="The balance to be saved (policy_fee - activation_fee)")
    amount_saved = models.FloatField(default=0.0, help_text="The amount saved so far towards the total amount")

    # Dates
    start_date = models.DateField(null=True, blank=True)
    # maturity_date = models.DateField(null=True, blank=True)
    activated_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    plan_duration = models.IntegerField(
        choices=PlanDuration.choices,
        default=PlanDuration.TWELVE_MONTHS
    )

    # Status flags
    is_active = models.BooleanField(default=True)
    is_activated = models.BooleanField(default=False)
    completed = models.BooleanField(default=False)
    is_full_plan_created = models.BooleanField(default=False) # Depends on a success response from the call to liberty life
    is_test_plan_created = models.BooleanField(default=False) # Depends on a success response from the call to liberty life
    activation_email_sent = models.BooleanField(default=False)
    full_email_sent = models.BooleanField(default=False)

    liberty_life_enrolee_id = models.CharField(max_length=100, null=True, blank=True)
    liberty_life_plan_id = models.CharField(max_length=100, null=True, blank=True)
    
    fund_transfer_status = models.CharField(
        max_length=100,
        choices=InsuranceRequestStatus.choices,
        default=InsuranceRequestStatus.PENDING,
    )
    liberty_life_notified = models.BooleanField(default=False)
    liberty_life_notification_payload = models.TextField(null=True, blank=True)
    liberty_life_notified_count = models.IntegerField(default=0)

    class Meta:
        verbose_name = "Savings For Health Insurance"
        verbose_name_plural = "Savings For Health Insurance"
        constraints = [
            models.UniqueConstraint(
                fields=["user", "ajo_user", "name"],
                name="unique_user_ajo_user_health_insurance_plan",
                violation_error_message="A health insurance plan with this name exists already",
            )
        ]

    def __str__(self):
        return f"{self.name} - {self.plan_type} - {self.quotation_id}"

    def save(self, *args, **kwargs):
        # Check if a health insurance plan already exists for this savings
        if self.savings and self.__class__.objects.filter(savings=self.savings).exclude(pk=self.pk).exists():
            raise IntegrityError("A health insurance plan for this savings exists already")
        return super().save(*args, **kwargs)

    def calculate_balance_amount(self):
        """
        Calculate the balance amount needed for the health insurance plan
        The balance amount is the total fee, minus the amount saved.
        """
        self.balance_amount = self.total_amount - self.amount_saved
        self.save(update_fields=["balance_amount"])
        return self.balance_amount
    
    def update_saved_amount(self, amount):
        """
        Update the amount saved so far for the insurance plan
        The amount saved the cumulative sum of all amount successfully added.
        """
        self.amount_saved = self.amount_saved + amount
        self.save(update_fields=["amount_saved"])
        return self.amount_saved

    def calculate_total_amount(self):
        """
        Calculate the total amount needed for the health insurance plan
        The total amount is the policy fee plus the activation fee.
        """
        self.total_amount = self.policy_fee + self.activation_fee
        self.save(update_fields=["total_amount"])
        return self.total_amount

    def complete_plan(self):
        """
        Mark the health insurance plan as completed
        """
        self.completed = True
        self.status = HealthInsuranceStatus.COMPLETED
        self.completed_at = timezone.now()
        self.save(update_fields=["completed", "status", "completed_at"])

    def activate_plan(self, activation_fee: float, policy_fee: float):
        """
        Mark the health insurance plan as activated
        """
        self.is_activated = True
        self.activation_fee = activation_fee
        self.policy_fee = policy_fee
        self.status = HealthInsuranceStatus.ACTIVATED
        self.activated_at = timezone.now()
        self.save(update_fields=["is_activated", "status", "activated_at", "activation_fee", "policy_fee"])

    @classmethod
    def create_savings_health_plan(cls, savings, plan_type, name=None, kyc_instance=None):
        """
        Create a health insurance savings plan for a user based on their AjoSaving.

        Args:
            savings (AjoSaving): The savings instance to associate with the health plan
            plan_type (str): The type of health insurance plan (from HealthInsurancePlanType)
            duration (int): The duration of the plan in days
            name (str, optional): The name for the health insurance plan
            kyc_instance (object, optional): KYC documentation instance if available

        Returns:
            SaveForHealthInsurance: The created health insurance plan instance
        """
        const = ConstantTable.get_constant_table_instance()

        if not name:
            plan_name = dict(HealthInsurancePlanType.choices)[plan_type]
            name = f"{plan_name} Plan - {savings.ajo_user.phone_number}"
        else:
            name = name

        health_plan = cls.objects.create(
            user=savings.user,
            ajo_user=savings.ajo_user,
            name=name,
            plan_type=plan_type,
            savings=savings,
            start_date=timezone.now().date(),
        )
        return health_plan

    def send_money_to_liberty_nem_account(self, amount, savings):
        from accounts.agency_banking import agent_login
        savings_access_token = agent_login(token_not_valid=True).get("access")

        if not self.is_activated:
            description = f"Savings Health Insurance Activation Fee/{savings.ajo_user.phone}"

            transfer_transaction = TransactionService.create_internal_transfer_between_accounts(
                user=savings.user,
                amount=amount,
                transaction_description=description,
                transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
                transaction_form_type=TransactionFormType.SAVINGS_HEALTH_INSURANCE_ACTIVATION,
                status=Status.PENDING,
                ajo_user=savings.ajo_user,
                transfer_provider=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
                quotation_id=self.quotation_id,
            )
        else: # Means we need to send money for the full plan
            description = f"Savings Health Insurance Complete Fee/{savings.ajo_user.phone}"

            transfer_transaction = TransactionService.create_internal_transfer_between_accounts(
                user=savings.user,
                amount=amount,
                transaction_description=description,
                transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
                transaction_form_type=TransactionFormType.SAVINGS_HEALTH_INSURANCE,
                status=Status.PENDING,
                ajo_user=savings.ajo_user,
                transfer_provider=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
                quotation_id=self.quotation_id,
            )

        buddy_phone_number = settings.NEM_BUDDY_PHONE_NUMBER
        reference = str(transfer_transaction.transaction_id)
        ##
        request_data = {
            "from_wallet_type": "COLLECTION",
            "to_wallet_type": "COLLECTION",
            "data": [
                {
                    "buddy_phone_number": buddy_phone_number,
                    "amount": amount,
                    "narration": description,
                    "is_beneficiary": "False",
                    "save_beneficiary": "False",
                    "remove_beneficiary": "False",
                    "is_recurring": "False",
                    "custom_reference": reference,
                }
            ],
            "transaction_pin": "",
        }
        transaction_pin = settings.AGENCY_BANKING_TRANSACTION_PIN
        # Import here to avoid circular imports
        from accounts.agency_banking import AgencyBankingClass
        send_money_to_liberty_nem_account = AgencyBankingClass.send_money_from_an_account_in_liberty_to_user_through_pay_buddy(
            transaction_pin=transaction_pin,
            phone_number=buddy_phone_number,
            amount=amount,
            transaction_reference=reference,
            access_token=savings_access_token,
            narration=description,
        )
    
        if (
                send_money_to_liberty_nem_account.get("data", {}).get("message")
                == "success"
            ):
            # create plan with handler handler
                transfer_transaction.status = Status.SUCCESS
                self.fund_transfer_status = (
                    InsuranceRequestStatus.SUCCESS
                )
                self.save(
                update_fields=[
                    "fund_transfer_status",
                ]
                )
                transfer_transaction.request_data = request_data
                transfer_transaction.payload = send_money_to_liberty_nem_account
                transfer_transaction.save(
                    update_fields=["request_data", "payload", "status"]
                )#
                return True, reference
        else:
            self.fund_transfer_status = (
                InsuranceRequestStatus.FAILED
            )

            self.status = Status.FAILED
            self.save(
                update_fields=[
                    "fund_transfer_status",
                ]
            )
            transfer_transaction.request_data = request_data
            transfer_transaction.payload = send_money_to_liberty_nem_account
            transfer_transaction.save(
                update_fields=["request_data", "payload", "status"]
            )#

            return False, str(send_money_to_liberty_nem_account.get("data", {}).get("message"))
        
    
class SaveForHealthLogs(BaseModel):
    health_savings = models.ForeignKey(SaveForHealthInsurance, on_delete=models.CASCADE, null=True, blank=True)
    status = models.CharField(max_length=100, choices=LogsStatus.choices, default=LogsStatus.PENDING)
    failure_reason = models.TextField(null=True, blank=True)
    request_type = models.CharField(
        max_length=100, choices=SaveHealthTypes.choices, null=True, blank=True
    )
    request_result = models.TextField(null=True, blank=True)

    def __str__(self):
        return f"{self.health_savings}"

    class Meta:
        verbose_name = "Save For Health Log"
        verbose_name_plural = "Save For Health Logs"

   