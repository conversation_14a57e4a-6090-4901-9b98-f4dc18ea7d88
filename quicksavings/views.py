import uuid

from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.db import IntegrityError
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics, permissions, serializers, status
from rest_framework.response import Response

from accounts.responses import serializer_validation_error_response
from accounts.utils import ResourceCreationLimiting
from payment.models import Transaction

from .models import QuickSavings
from .serializers import (
    EditQuickSavingsSerializer,
    ListOfAllQuickSavingsSerializer,
    QSCurrentPlanDetailsSerializer,
    QSEditPlanDetailsSerializer,
    QSPlanDetailsSerializer,
    QSTotalBalanceAndSavingsSerializer,
    QSTransactionHistorySerializer,
    QuickSavingsDetailsSerializer,
    RetrieveQuickSavingsInstanceSerializer,
    TotalBalanceSavingsListOfAllQuickSavingsSerializer,
)
from .utils import (
    defined_plan_details_information,
    extended_plan_details,
    plan_information_details,
)

User: AbstractUser = get_user_model()


class PlanDetailsAPIView(generics.GenericAPIView):
    """
    This view intends to get the plan name, duration, target and frequency of the customer
    and returns the daily, weekly and monthly breakdowns as well as the just once.
    *The Frequency choices are as follows -> "DAILY", "WEEKLY", "MONTHLY", "JUST_ONCE"*
    """

    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = QSPlanDetailsSerializer

    def post(self, request, *args, **kwargs):
        data = request.data
        serializer = self.serializer_class(data=data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        response_data = {}
        plan = serializer.validated_data["plan"]
        # check if the plan exists
        if QuickSavings.objects.filter(plan=plan, user=request.user).exists():
            return Response(
                {
                    "error": "614",
                    "status": False,
                    "message": "a plan with this name exists already",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        duration = int(serializer.validated_data["duration"])
        target = round(float(serializer.validated_data["target"]), 2)
        frequency = serializer.validated_data["frequency"]

        # Working based on the minimum duration being 3 months
        # Assuming that the duration comes in days
        response_data = {
            "status": True,
            "data": {
                "plan": plan,
                **defined_plan_details_information(duration=duration, amount=target, frequency=frequency),
            },
        }

        return Response(response_data, status=status.HTTP_200_OK)


class QuickSavingsDetailsAPIView(generics.GenericAPIView):
    """
    This view gets all the details for the quicksavings plan and generates a quotation id
    _The Frequency choices are as follows -> "DAILY", "WEEKLY", "MONTHLY", "JUST_ONCE"_
    """

    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = QuickSavingsDetailsSerializer

    def post(self, request, *args, **kwargs):
        user = request.user
        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        if ResourceCreationLimiting.is_resource_created_recently(user_id=user.id):
            return Response(
                {
                    "error": "429",
                    "status": False,
                    "message": "wait a while before you create a new plan",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        plan = serializer.validated_data["plan"]
        duration = int(serializer.validated_data["duration"])
        target = serializer.validated_data["target"]
        frequency_method = serializer.validated_data["frequency"]
        hour = serializer.validated_data["hour"]

        details = extended_plan_details(
            duration=duration,
            amount=target,
            frequency=frequency_method,
        )

        # Get the details
        frequency = details["frequency"]
        periodic_amount = details["periodic_amount"]
        interest_rate = details["interest_rate"]
        maturity_date = details["maturity_date"]
        estimated_amount = details["estimated_amount"]

        # Create a quotation id
        quotation_id = f"LI-QSV-{uuid.uuid4()}"

        try:
            quicksavings = QuickSavings.objects.create(
                user=user,
                plan=plan,
                duration=duration,
                target=target,
                interest_rate=interest_rate,
                frequency=frequency,
                hour=hour,
                periodic_amount=periodic_amount,
                maturity_date=maturity_date,
                estimated_amount=estimated_amount,
                quotation_id=quotation_id,
            )

            ResourceCreationLimiting.create_resource(user_id=user.id)

        except IntegrityError:
            return Response(
                {
                    "error": "605",
                    "status": False,
                    "message": "you already have a plan with this name: " + str(plan),
                },
                status=status.HTTP_403_FORBIDDEN,
            )
            # raise serializers.ValidationError("You already have a plan with this name: " + str(plan))

        response_data = {"status": "success", "data": {}}
        response_data["data"]["id"] = quicksavings.id
        response_data["data"]["plan"] = quicksavings.plan
        response_data["data"]["duration"] = quicksavings.duration
        response_data["data"]["target"] = quicksavings.target
        response_data["data"]["quotation_id"] = quicksavings.quotation_id

        return Response(response_data, status=status.HTTP_201_CREATED)


# class ListOfAllQuickSavingsAPIView(generics.GenericAPIView):
#     """
#     Shows all the quicksavings savings a user has
#     """

#     permission_classes = (permissions.IsAuthenticated,)
#     serializer_class = ListOfAllQuickSavingsSerializer
#     queryset = QuickSavings.objects.all()

#     def get(self, request, *args, **kwargs):
#         quicksavings_qs = self.queryset.filter(user=request.user).order_by("-id")

#         serializer = self.serializer_class(quicksavings_qs, many=True)

#         return Response({"status": "success", "data": serializer.data}, status=status.HTTP_200_OK)


class CurrentPlanDetailsAPIView(generics.GenericAPIView):
    """
    Shows the amount saved so far, savings amount and maturity date for plans in progress

    """

    permission_classes = (permissions.IsAuthenticated,)

    @swagger_auto_schema(
        query_serializer=RetrieveQuickSavingsInstanceSerializer,
        responses={
            "200": QSCurrentPlanDetailsSerializer,
            "400": "Bad Request",
        },
    )
    def get(self, request, *args, **kwargs):
        id = request.query_params.get("id")
        user = request.user

        quicksavings_instance = QuickSavings.objects.filter(id=id, user=user, deleted=False).first()
        if not quicksavings_instance:
            return Response(
                {"status": False, "error": "585", "message": "This plan does not exist for this user"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            serializer = QSCurrentPlanDetailsSerializer(quicksavings_instance)
            return Response({"status": "success", "data": serializer.data}, status=status.HTTP_200_OK)


class TransactionHistoryAPIView(generics.GenericAPIView):
    """
    Shows the list of transactions done for a particular quicksavings plan
    """

    permission_classes = (permissions.IsAuthenticated,)

    @swagger_auto_schema(
        query_serializer=RetrieveQuickSavingsInstanceSerializer,
        responses={
            "200": QSTransactionHistorySerializer,
            "400": "Bad Request",
        },
    )
    def get(self, request, *args, **kwargs):
        id = request.query_params.get("id")
        user = request.user

        quicksavings = QuickSavings.objects.filter(id=id, user=user, deleted=False).first()
        if not quicksavings:
            return Response(
                {"status": False, "error": "585", "message": "This plan does not exist for this user"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            transaction_qs = Transaction.objects.filter(quotation_id=quicksavings.quotation_id)
            serializer = QSTransactionHistorySerializer(transaction_qs, many=True)
            return Response({"status": "success", "data": serializer.data}, status=status.HTTP_200_OK)


class TotalBalanceAndSavingsAPIView(generics.GenericAPIView):
    """
    This shows the total balance of all the savings and the number of savings
    """

    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = TotalBalanceSavingsListOfAllQuickSavingsSerializer
    queryset = QuickSavings.objects.select_related("user")

    def get(self, request, *args, **kwargs):
        quicksavings_qs = self.queryset.filter(user=request.user, deleted=False).order_by("-id")

        total_amount = 0

        for i in quicksavings_qs.values("amount_saved"):
            total_amount += i["amount_saved"]

        savings = quicksavings_qs.count()

        data = {
            "total_amount": total_amount,
            "savings": savings,
        }

        serializer = QSTotalBalanceAndSavingsSerializer(data)
        quicksavings_serializer = ListOfAllQuickSavingsSerializer(quicksavings_qs, many=True)

        return Response(
            {
                "status": "success",
                "data": {
                    "summary": serializer.data,
                    "quicksavings_list": quicksavings_serializer.data,
                },
            },
            status=status.HTTP_200_OK,
        )


class EditGeneratePlanDetailsAPIView(generics.GenericAPIView):
    """
    generate new plan details of an existing plan that has not been activated yet
    """

    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = QSEditPlanDetailsSerializer

    def perform_all_the_checks(
        self, request, serializer_class: serializers.Serializer
    ) -> Response | serializers.Serializer:
        # obtain the id of the plan from the request parameters
        plan_id = request.query_params.get("plan_id")
        if not plan_id:
            return Response(
                {
                    "error": "603",
                    "status": False,
                    "message": "plan id is required as a query parameter in this request",
                }
            )

        # obtain the user
        user = request.user

        # check if the plan does not exist
        if not QuickSavings.objects.filter(user=user, id=plan_id, deleted=False).exists():
            return Response(
                {
                    "error": "614",
                    "status": False,
                    "message": "a quicksavings plan for with this id for this user does not exist",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # obtain the quicksavings plan
        qsv_plan = QuickSavings.objects.filter(user=user, id=plan_id).last()

        # check if the plan has been activated before
        if qsv_plan.is_activated is True:
            return Response(
                {
                    "error": "715",
                    "status": False,
                    "message": "this plan cannot be edited because it has been activated before",
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        serializer = serializer_class(qsv_plan, data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        # check if another plan has the same name as that one
        plan_name = serializer.validated_data.get("plan")
        if QuickSavings.objects.filter(plan=plan_name).exists():
            return Response(
                {
                    "error": "605",
                    "status": False,
                    "message": "a plan with this name exists already",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        return serializer

    @swagger_auto_schema(
        request_body=QSEditPlanDetailsSerializer,
        manual_parameters=[
            openapi.Parameter(
                "plan_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="the id of the plan to be edited",
            ),
        ],
    )
    def put(self, request, *args, **kwargs):
        """
        generate new plan details of an existing plan that has not been activated yet
        """

        checks = self.perform_all_the_checks(request, serializer_class=self.serializer_class)

        if isinstance(checks, Response):
            return checks
        else:
            serializer = checks

        # get info from the validated
        plan = serializer.validated_data.get("plan")
        duration = int(serializer.validated_data.get("duration"))
        target = round(float(serializer.validated_data.get("target")))
        frequency = serializer.validated_data.get("frequency")

        # get the data ready
        response_data = {
            "status": True,
            "data": {
                "plan": plan,
                **defined_plan_details_information(
                    duration=duration,
                    amount=target,
                    frequency=frequency,
                ),
            },
        }

        return Response(response_data, status=status.HTTP_200_OK)


class EditQuickSavingsPlanAPIView(generics.GenericAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = EditQuickSavingsSerializer

    @swagger_auto_schema(
        request_body=EditQuickSavingsSerializer,
        manual_parameters=[
            openapi.Parameter(
                "plan_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="the id of the plan to be edited",
            ),
        ],
    )
    def put(self, request, *args, **kwargs):
        """
        edit the existing plan that has not been activated yet in the database
        """
        checks = EditGeneratePlanDetailsAPIView.perform_all_the_checks(
            self, request, serializer_class=self.serializer_class
        )

        if isinstance(checks, Response):
            return checks
        else:
            serializer = checks

        # save the information
        serializer.save()

        # get the information
        response_data = {}
        response_data.update(dict(serializer.data))

        # get the quicksavings plan in order to display the information to the frontend
        plan_id = request.query_params.get("plan_id")
        user = request.user
        qs_plan = QuickSavings.objects.filter(id=plan_id, user=user).last()
        response_data.update(
            {
                "amount_saved": qs_plan.amount_saved,
                "maturity_date": qs_plan.maturity_date,
                "periodic_amount": qs_plan.periodic_amount,
                "is_active": qs_plan.is_active,
                "is_activated": qs_plan.is_activated,
            }
        )
        return Response(
            {
                "status": True,
                "data": response_data,
            },
            status=status.HTTP_200_OK,
        )
