from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models

from accounts.helpers import RoundedFloatField
from payment.model_choices import PaymentMethod, RecurrentSavingStatus

from .model_choices import Frequency


class QuickSavings(models.Model):
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="quicksavings",
        db_index=True,
    )
    plan = models.CharField(max_length=100, null=True, blank=True)
    duration = models.CharField(max_length=100, null=True, blank=True)
    target = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="target should be >= 0"),
        ],
    )
    interest_rate = models.CharField(max_length=100, null=True, blank=True)
    periodic_amount = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="periodic amount should be >= 0"),
        ],
    )
    frequency = models.CharField(max_length=100, null=True, blank=True, choices=Frequency.choices)
    hour = models.PositiveIntegerField(
        null=True,
        blank=True,
        validators=[
            MaxValueValidator(limit_value=23, message="hour should be between 0-23"),
        ],
    )
    maturity_date = models.DateField(null=True, blank=True)
    estimated_amount = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="estimated amount should be >= 0"),
        ],
    )
    quotation_id = models.CharField(max_length=100, null=True, blank=True, unique=True)
    amount_saved = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="periodic amount should be >= 0"),
        ],
    )

    # plan information
    is_activated = models.BooleanField(default=False, null=True, blank=True)
    completed = models.BooleanField(default=False, null=True, blank=True)
    plan_balance_before = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="plan balance before should be >= 0"),
        ],
    )
    plan_balance_after = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="plan balance after should be >= 0"),
        ],
    )

    # metadata
    is_active = models.BooleanField(default=True, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    # this field is used for deleting plans instead of actually deleting them
    deleted = models.BooleanField(default=False)

    # payment method
    payment_method = models.CharField(max_length=200, null=True, blank=True, choices=PaymentMethod.choices)
    recurrent_saving_status = models.CharField(
        max_length=100, choices=RecurrentSavingStatus.choices, default=RecurrentSavingStatus.NONE
    )

    # email sent on creation
    email_sent = models.BooleanField(default=False)

    # # This is the periodic task that handles the automatic charging
    # user_recurrent_charge = models.OneToOneField(
    #     UserRecurrentChargeTable, on_delete=models.SET_NULL, null=True, blank=True
    # )
    # task = models.OneToOneField(to=PeriodicTask, on_delete=models.CASCADE, null=True, blank=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["user", "plan"],
                name="unique_user_quicksavings_plan",
                violation_error_message="A plan with this name already exists",
            ),
        ]

        verbose_name = "QuickSavings"
        verbose_name_plural = "QuickSavings"

    def __str__(self) -> str:
        return f"plan:{self.plan} amount:{self.amount_saved} quotation_id:{self.quotation_id}"

    # def save(self, *args, **kwargs):
    #     self.target = round(self.target, 2) if self.target is not None else None
    #     self.periodic_amount = round(self.periodic_amount, 2) if self.periodic_amount is not None else None
    #     self.estimated_amount = round(self.estimated_amount, 2) if self.estimated_amount is not None else None
    #     self.amount_saved = round(self.amount_saved, 2) if self.amount_saved is not None else None
    #     self.plan_balance_before = round(self.plan_balance_before, 2) if self.plan_balance_before is not None else None
    #     self.plan_balance_after = round(self.plan_balance_after, 2) if self.plan_balance_after is not None else None
    #     super(QuickSavings, self).save(*args, **kwargs)

    # def delete(self, *args, **kwargs):
    #     if self.task is not None:
    #         self.task.delete()
    #     return super(self.__class__, self).delete(*args, **kwargs)
