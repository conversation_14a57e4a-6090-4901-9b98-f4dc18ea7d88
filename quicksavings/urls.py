from django.urls import path

from .views import (
    CurrentPlanDetailsAPIView,
    EditGeneratePlanDetailsAPIView,
    EditQuickSavingsPlanAPIView,
    PlanDetailsAPIView,
    QuickSavingsDetailsAPIView,
    TotalBalanceAndSavingsAPIView,
    TransactionHistoryAPIView,
)

urlpatterns = [
    # URL collects the plan, duration and target
    path("generate-plan-details/", PlanDetailsAPIView.as_view(), name="plan_details"),
    # URL to submit the quicksavings details to the DB and get a quotation id
    path("initiate-quicksavings-plan/", QuickSavingsDetailsAPIView.as_view(), name="quicksavings_details"),
    # URL to list all the quicksavings plans a user has
    # path("listquicksavingsplans/", ListOfAllQuickSavingsAPIView.as_view(), name="list_of_all_quicksavings"),
    # URL to obtain the current plan details of a quicksavings instance
    path("current-plan-details/", CurrentPlanDetailsAPIView.as_view(), name="current_plan_details"),
    # URL to get the transaction history of a plan
    path("transaction-history/", TransactionHistoryAPIView.as_view(), name="transaction_history"),
    # URL to get total balance and savings of the chestlock
    path("total-list-quicksavings/", TotalBalanceAndSavingsAPIView.as_view(), name="total_balance_savings"),
    # edit the plan information
    path(
        "edit-generate-plan-details/", EditGeneratePlanDetailsAPIView.as_view(), name="edit_and_generate_plan_details"
    ),
    path("save-edited-plan/", EditQuickSavingsPlanAPIView.as_view(), name="edit_quicksavings_plan"),
]
