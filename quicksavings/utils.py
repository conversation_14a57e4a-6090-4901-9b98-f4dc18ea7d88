import datetime
from enum import Enum

from rest_framework import serializers

from accounts.models import ConstantTable
from quicksavings.model_choices import Frequency


def calculate_frequency_breakdowns(amount: float, duration: int) -> dict:
    """
    Takes in amount in float and duration in days(int) and returns the following dict:
    {
        daily: float,
        weekly: float,
        monthly: float
    }
    """

    breakdowns = {}
    breakdowns["daily"] = round(amount / duration, 2)
    breakdowns["weekly"] = round(amount / (duration / 7), 2)
    if duration < 30:
        breakdowns["monthly"] = round(amount, 2)
    else:
        breakdowns["monthly"] = round(amount / (duration / 30), 2)
    breakdowns["just_once"] = round(amount, 2)

    return breakdowns


def extended_plan_details(duration: int, amount: float, frequency: Enum) -> dict:
    """
    takes in duration in days(int), amount in float, and one of the Frequency enum and returns the following dict:
    {
        "target": float,
        "interest_rate": float,
        "frequency": one of the choices of the Frequency Enum choice from models_choices,
        "periodic_amount": float,
        "maturity_date": datetime.date() object,
        "estimated_amount": float
    }
    """
    constants = ConstantTable.get_constant_table_instance()

    details = {}

    details["target"] = round(amount, 2)
    # TODO: The interest rates will change according to values of amount
    interest_rate = float(constants.quicksavings_interest_rate)
    details["interest_rate"] = interest_rate

    if frequency in Frequency:
        details["frequency"] = getattr(Frequency, frequency)
    else:
        raise serializers.ValidationError("Choose from daily, weekly, monthly or just_once")

    breakdown = calculate_frequency_breakdowns(amount=round(float(amount), 2), duration=duration)
    details["periodic_amount"] = breakdown[frequency.lower()]

    details["maturity_date"] = datetime.date.today() + datetime.timedelta(days=duration)

    estimated_amount = round(
        amount + (amount * (interest_rate / 100)),
        2,
    )

    details["estimated_amount"] = estimated_amount

    return details


def plan_information_details(duration: int, amount: float) -> dict:
    """
    takes in duration in days(int), amount in float, and returns the following dict:
    {
        "target": float,
        "interest_rate": float,
        "frequency_choices": list,
        "daily_breakdown": float,
        "weekly_breakdown": float,
        "monthly_breakdown": float,
        "maturity_date": "Month, Day",
        "estimated_amount": float
    }

    """
    details = {}

    details["target"] = round(amount, 2)
    # TODO: The interest rates will change according to values of amount
    interest_rate = 5.5
    details["interest_rate"] = interest_rate

    details["frequency_choices"] = list(Frequency.values)

    breakdown = calculate_frequency_breakdowns(amount=round(float(amount), 2), duration=duration)
    details["daily_breakdown"] = breakdown["daily"]
    details["weekly_breakdown"] = breakdown["weekly"]
    details["monthly_breakdown"] = breakdown["monthly"]
    details["just_once"] = breakdown["just_once"]

    maturity_date = datetime.date.today() + datetime.timedelta(days=duration)
    details["maturity_date"] = f"{maturity_date.strftime('%B')}, {maturity_date.strftime('%d')}"

    estimated_amount = round(
        amount + (amount * (interest_rate / 100)),
        2,
    )

    details["estimated_amount"] = estimated_amount

    return details


def defined_plan_details_information(duration: int, amount: float, frequency: Enum) -> dict:
    """
    takes in duration in days(int), amount in float, and returns the following dict:
    {
        "target": float,
        "interest_rate": float,
        "frequency_choice": String,
        "periodic_amount": float,
        "maturity_date": "Month, Day",
        "estimated_amount": float
    }
    """
    constants = ConstantTable.get_constant_table_instance()
    details = {}

    details["target"] = round(amount, 2)
    # TODO: The interest rates will change according to values of amount
    interest_rate = float(constants.quicksavings_interest_rate)
    details["interest_rate"] = interest_rate

    details["frequency_choice"] = frequency

    breakdown = calculate_frequency_breakdowns(amount=round(float(amount), 2), duration=duration)
    details["periodic_amount"] = breakdown[f"{frequency.lower()}"]

    maturity_date = datetime.date.today() + datetime.timedelta(days=duration)
    details["maturity_date"] = f"{maturity_date.strftime('%B')}, {maturity_date.strftime('%d')}"

    estimated_amount = round(
        amount + (amount * (interest_rate / 100)),
        2,
    )

    details["estimated_amount"] = estimated_amount

    return details
