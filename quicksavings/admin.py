from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from savings.admin import set_readonly_fields

from .models import QuickSavings

####################################################################################
# RESOURCES


class QuickSavingsResource(resources.ModelResource):
    class Meta:
        model = QuickSavings


####################################################################################
# RESOURCE ADMINS


class QuicksavingsResourceAdmin(ImportExportModelAdmin):
    resource_class = QuickSavingsResource
    search_fields = ["user__email", "plan", "quotation_id", "is_activated"]
    readonly_fields = set_readonly_fields(
        "duration",
        "target",
        "interest_rate",
        "periodic_amount",
        "estimated_amount",
        # "amount_saved",
        # "plan_balance_before",
        # "plan_balance_after",
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


####################################################################################
# REGISTER MODELS

admin.site.register(QuickSavings, QuicksavingsResourceAdmin)
