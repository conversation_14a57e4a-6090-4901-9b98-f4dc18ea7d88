import re

from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import IntegrityError
from rest_framework import serializers

from accounts.models import ConstantTable
from payment.models import Transaction

from .model_choices import Frequency
from .models import QuickSavings
from .utils import extended_plan_details

pattern = r"^[a-zA-Z][a-zA-Z0-9 ]*$"


class QSPlanDetailsSerializer(serializers.Serializer):
    plan = serializers.CharField(min_length=2)
    duration = serializers.CharField(min_length=1)
    target = serializers.FloatField()
    frequency = serializers.CharField(min_length=5)

    def validate(self, attrs):
        plan = attrs.get("plan", "")
        duration = attrs.get("duration", "")
        target = attrs.get("target", "")
        frequency = attrs.get("frequency", "")

        constants = ConstantTable.get_constant_table_instance()

        if not re.match(pattern=pattern, string=plan):
            raise serializers.ValidationError(
                "the plan name should start with a letter and can have numbers and spaces"
            )

        if not duration.isdigit():
            raise serializers.ValidationError("The duration should be numeric only")

        if target < constants.quicksavings_minimum_target:
            raise serializers.ValidationError(f"minimum target of {constants.quicksavings_minimum_target} is required")

        if frequency.upper() in Frequency:
            attrs["frequency"] = getattr(Frequency, frequency.upper())
        else:
            raise serializers.ValidationError("Choose from daily, weekly, monthly or just_once")

        return attrs


# Serializer that creates the quicksavings instance in the DB
class QuickSavingsDetailsSerializer(serializers.ModelSerializer):
    plan = serializers.CharField(min_length=2)
    duration = serializers.CharField(min_length=1)
    target = serializers.FloatField()
    frequency = serializers.CharField(min_length=5)
    hour = serializers.IntegerField(
        validators=[
            MinValueValidator(limit_value=0, message="hour should not be less than 0"),
            MaxValueValidator(limit_value=23, message="hour should not be more than 23"),
        ]
    )

    class Meta:
        model = QuickSavings
        fields = (
            "id",
            "plan",
            "target",
            "duration",
            "frequency",
            "hour",
        )

    def validate(self, attrs):
        plan = attrs.get("plan", "")
        duration = attrs.get("duration", "")
        target = attrs.get("target", "")
        frequency = attrs.get("frequency", "")

        constants = ConstantTable.get_constant_table_instance()

        if not re.match(pattern=pattern, string=plan):
            raise serializers.ValidationError(
                "the plan name should start with a letter and can have numbers and spaces"
            )

        if not duration.isdigit():
            raise serializers.ValidationError("The duration should be numeric only")

        if target < constants.quicksavings_minimum_target:
            raise serializers.ValidationError(f"minimum target of {constants.quicksavings_minimum_target} is required")

        if frequency.upper() in Frequency:
            attrs["frequency"] = frequency.upper()
        else:
            raise serializers.ValidationError("Choose from daily, weekly, monthly or just_once")

        return attrs

    def create(self, validated_data):
        try:
            obj = QuickSavings.objects.create(**validated_data)
            return obj

        # The error below occurs when a user tries to create a new plan with the same name
        except IntegrityError:
            raise serializers.ValidationError("You already have a plan with this name")


class ListOfAllQuickSavingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuickSavings
        fields = [
            "id",
            "plan",
            "target",
            "amount_saved",
            "target",
            "frequency",
            "periodic_amount",
            "duration",
            "maturity_date",
            "is_activated",
            "completed",
            "is_active",
            "recurrent_saving_status",
        ]


class QSCurrentPlanDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuickSavings
        fields = [
            "id",
            "plan",
            "amount_saved",
            "target",
            "duration",
            "frequency",
            "maturity_date",
            "periodic_amount",
            "frequency",
            "hour",
            "is_activated",
            "completed",
            "is_active",
            "recurrent_saving_status",
        ]


class RetrieveQuickSavingsInstanceSerializer(serializers.Serializer):
    id = serializers.IntegerField()


class QSTransactionHistorySerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()
    amount = serializers.FloatField()

    class Meta:
        model = Transaction
        fields = [
            "id",
            "amount",
            "transaction_id",
            "transaction_form_type",
            "status",
            "date_created",
            "transaction_date_completed",
        ]


class QSTotalBalanceAndSavingsSerializer(serializers.Serializer):
    total_amount = serializers.FloatField()
    savings = serializers.IntegerField()


class TotalBalanceSavingsListOfAllQuickSavingsSerializer(serializers.ModelSerializer):
    total_balance = serializers.FloatField()
    savings = serializers.IntegerField()

    class Meta:
        model = QuickSavings
        fields = [
            "total_balance",
            "savings",
            "id",
            "plan",
            "target",
            "amount_saved",
            "duration",
            "maturity_date",
        ]


class QSEditPlanDetailsSerializer(serializers.Serializer):
    plan = serializers.CharField(min_length=3)
    duration = serializers.CharField(min_length=1)
    target = serializers.FloatField()
    frequency = serializers.CharField(min_length=5)

    def validate(self, attrs):
        plan = attrs.get("plan", "")
        duration = attrs.get("duration", "")
        target = attrs.get("target", "")
        frequency = attrs.get("frequency", "")

        constants = ConstantTable.get_constant_table_instance()

        if not re.match(pattern=pattern, string=plan):
            raise serializers.ValidationError(
                "the plan name should start with a letter and can have numbers and spaces"
            )

        if not duration.isdigit():
            raise serializers.ValidationError("The duration should be numeric only")

        if target < constants.quicksavings_minimum_target:
            raise serializers.ValidationError(f"minimum target of {constants.quicksavings_minimum_target} is required")

        if frequency.upper() in Frequency:
            attrs["frequency"] = getattr(Frequency, frequency.upper())
        else:
            raise serializers.ValidationError("Choose from daily, weekly, monthly or just_once")

        return attrs


class EditQuickSavingsSerializer(serializers.ModelSerializer):
    plan = serializers.CharField(min_length=3)
    duration = serializers.CharField(min_length=1)
    target = serializers.FloatField()
    frequency = serializers.CharField(min_length=5)
    hour = serializers.IntegerField(
        validators=[
            MinValueValidator(limit_value=0, message="hour should not be less than 0"),
            MaxValueValidator(limit_value=23, message="hour should not be more than 23"),
        ]
    )

    class Meta:
        model = QuickSavings
        fields = [
            "plan",
            "target",
            "duration",
            "frequency",
            "hour",
        ]

    def validate(self, attrs):
        plan = attrs.get("plan", "")
        duration = attrs.get("duration", "")
        target = attrs.get("target", "")
        frequency = attrs.get("frequency", "")

        constants = ConstantTable.get_constant_table_instance()

        if not re.match(pattern=pattern, string=plan):
            raise serializers.ValidationError(
                "the plan name should start with a letter and can have numbers and spaces"
            )

        if not duration.isdigit():
            raise serializers.ValidationError("The duration should be numeric only")

        if target < constants.quicksavings_minimum_target:
            raise serializers.ValidationError(f"minimum target of {constants.quicksavings_minimum_target} is required")

        if frequency.upper() in Frequency:
            attrs["frequency"] = getattr(Frequency, frequency.upper())
        else:
            raise serializers.ValidationError("Choose from daily, weekly, monthly or just_once")

        return attrs

    def update(self, instance, validated_data):
        plan = validated_data.get("plan", instance.plan)
        target = validated_data.get("target", instance.target)
        duration = int(validated_data.get("duration", instance.duration))
        frequency = validated_data.get("frequency", instance.frequency)
        hour = validated_data.get("hour", instance.hour)
        details = extended_plan_details(duration=duration, amount=target, frequency=frequency)
        instance.plan = plan
        instance.target = target
        instance.duration = duration
        instance.frequency = details.get("frequency")
        instance.hour = hour
        instance.periodic_amount = details.get("periodic_amount")
        instance.interest_rate = details.get("interest_rate")
        instance.maturity_date = details.get("maturity_date")
        instance.estimated_amount = details.get("estimated_amount")
        instance.save()
        return instance
