from django.db.models.signals import post_save
from django.core.cache import cache
from django.dispatch import receiver
from accounts.models import ConstantTable


# Signal receiver to clear the cache when the instance or model is updated
@receiver(post_save, sender=ConstantTable)
def clear_constant_table_cache(sender, instance, **kwargs):
    cache_key = 'constant_table_instance'
    cache.delete(cache_key)
