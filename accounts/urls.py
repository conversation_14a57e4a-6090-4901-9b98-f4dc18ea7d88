from django.urls import path
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView

from .views import *

# TransactionPinAPIView

urlpatterns = [
    path("constants/", ConstantsAPIView.as_view(), name="constants"),
    path("ajouser_agent_details/", GetAgentDetails.as_view(), name="constants"),
    path("register/", RegisterAPIView.as_view(), name="register"),
    path("token/", TokenObtainPairView.as_view(), name="token_obtain_pair"),
    path("token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    path("celery-status-check/", CeleryHealthCheckAPIView.as_view(), name="celery_health_status_check"),
    path("login_with_otp/", LoginWithOTP.as_view()),
    path("next-of-kin/", NextOfKinAPIView.as_view(), name="user-next-of-kin"),
    path("checks/", UserChecksAPIView.as_view(), name="user_checks"),
    # path("pin/", TransactionPinAPIView.as_view(), name="set-pin"),
    # path("firstentry/", FirstEntryPointAPIView.as_view(), name="first_entry_point"),
]
