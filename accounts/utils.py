import base64
from typing import Any, Dict

import jwt
import requests
from celery import shared_task
from celery.exceptions import TimeoutError
from cryptography.fernet import Fernet
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.core.cache import cache
from django.template.loader import render_to_string

from savings.celery import app

from .agency_banking import AgencyBankingClass
from .model_choices import UserType


@shared_task
def test_task():
    return "Celery worker is running successfully!"


def is_celery_worker_alive() -> bool:
    """
    Sends a task to the celery worker and expects
    a response under 5 seconds.

    Returns:
        bool: True/False
    """
    try:
        # Define a unique task name for testing
        test_task_name = "accounts.utils.test_task"

        # Send a test task to the Celery worker
        result = app.send_task(test_task_name)

        # print(result)
        # Wait for the result with a timeout
        result.get(timeout=5)

        # If the result is obtained successfully, the worker is alive
        return True
    except TimeoutError:
        # TimeoutError indicates that the task execution took too long
        # The worker is likely alive but the task is not responding as expected
        return False
    except Exception as e:
        # Any other exception indicates an issue with the Celery worker
        return False


def get_celery_worker_status() -> Dict[str, Any]:
    """
    Checks the status of the celery worker

    Returns:
        Dict[str, Any]: the information about the celery worker
    """
    inspection = app.control.inspect()
    availability = inspection.ping()
    stats = inspection.stats()
    registered_tasks = inspection.registered()
    active_tasks = inspection.active()
    scheduled_tasks = inspection.scheduled()

    results = {
        "availability": availability,
        "stats": stats,
        "registered_tasks": registered_tasks,
        "active_tasks": active_tasks,
        "scheduled_tasks": scheduled_tasks,
    }

    return results


def serializer_error(err) -> str | dict:
    """
    takes in the validation error and returns either a specific problem as a string or the whole error message as a dict
    """
    error_message = err.detail.pop("error", None)
    return error_message[0] if error_message is not None and type(error_message) is list else err.detail


def decode_access_token(access_token: str, signature: str) -> dict:
    try:
    
        verification = jwt.decode(access_token, signature, algorithms=["HS256"])

        # print(verification, "VERIFICATION====================")
        return verification

    except jwt.exceptions.PyJWTError as e:
        return {"status": False, "message": e}


class EmailUtil:
    mailgun_url = settings.MAILGUN_URL
    mailgun_api_key = settings.MAILGUN_API_KEY

    @classmethod
    def send_mailgun_text_email(cls, data: dict) -> None:
        """
        Send purely text based email using mailgun

        Args:
            data (dict): dictionary should contain 1. "to_email" 2."email_subject" 3. "email_body"
        """
        url = cls.mailgun_url
        auth = ("api", cls.mailgun_api_key)
        data = {
            "from": "Liberty Pay <<EMAIL>>",
            "to": data["to_email"],
            "subject": data["email_subject"],
            "text": data["email_body"],
        }
        try:
            requests.post(url=url, auth=auth, data=data)
        except Exception as err:
            pass
            # sprint(str(err))

    @classmethod
    def send_mailgun_html_email(cls, data: dict) -> bool:
        """
        Send purely html based email using mailgun

        Args:
            data (dict): dictionary should contain 1. "to_email" 2."email_subject" 3. "email_body"
        """
        url = cls.mailgun_url
        auth = ("api", cls.mailgun_api_key)
        data = {
            "from": "Liberty Pay <<EMAIL>>",
            "to": data["to_email"],
            "subject": data["email_subject"],
            "html": data["email_body"],
        }
        try:
            requests.post(url=url, auth=auth, data=data)
            return True
        except Exception as err:
            # print(str(err))
            return False

    @classmethod
    def custom_send_email_api(cls, subject: str, recipient_list: list, context: dict, template_dir: str):
        # USES API

        email_body = render_to_string(template_dir, context)

        for email in recipient_list:
            mail_gun_url = cls.mailgun_url
            mail_gun_auth = ("api", cls.mailgun_api_key)
            mail_gun_data = {
                "from": "Liberty Pay <<EMAIL>>",
                "to": email,
                "subject": subject,
                "html": email_body,
            }
            try:
                res = requests.post(mail_gun_url, auth=mail_gun_auth, data=mail_gun_data)
            except Exception as e:
                pass


class ResourceCreationLimiting:
    # Constants
    CREATION_INTERVAL = 120  # 2 minutes

    @classmethod
    def is_resource_created_recently(cls, user_id: int) -> bool:
        """
        This checks if an endpoint created a resource recently

        Args:
            user_id (int): the user ID

        Returns:
            bool: True/False
        """
        return cache.get(f"limiting_{user_id}") is not None

    @classmethod
    def create_resource(cls, user_id: int) -> bool:
        """
        Creates a resource limit cache

        Args:
            user_id (int): the user ID

        Returns:
            bool: True if created, else False
        """
        if cls.is_resource_created_recently(user_id):
            # return HttpResponseBadRequest("Resource creation is temporarily disabled for this user.")
            return False

        # Store information about the created resource for this user in the cache
        cache.set(key=f"limiting_{user_id}", value=True, timeout=cls.CREATION_INTERVAL)

        return True

    # Clear the cache for a specific user after the specified duration
    # def clear_cache_for_user(user_id):
    #     cache.delete(f"recent_resource_created_{user_id}")
