import random
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Optional

from celery import shared_task
from django.conf import settings
from django.db import transaction as django_transaction
from django.db.models import Sum
from django.utils import timezone

from ajo.model_choices import RoscaGroupStatus
from ajo.models import RotationGroup, RotationGroupMember
from ajo.payment_actions import RotationGroupPay
from ajo.selectors import AjoUserSelector, RotationGroupMemberSelector, RotationGroupSelector
from ajo.services import RotationGroupMemberService
from chestlock.model_choices import ChestlockType, OnlendingType
from chestlock.models import ChestLock, Onlending
from chestlock.selectors import OnlendingSelector
from chestlock.utils import OnlendingDynamicTransactions
from payment.make_transactions import (
    SavedAgencyBankingDebitCardPaymentProcessor,
    UserAgencyBankingWalletPaymentProcessor,
)
from payment.model_choices import (
    PaymentMethod,
    PlanType,
    Status,
    TransactionFormType,
    WalletTypes,
)
from payment.models import Transaction, WalletSystem
from payment.services import TransactionService
from payment.utils import Utility, format_currency
from payment.wrapper import create_transaction_and_charge_card
from savings.celery import app

from .agency_banking import AgencyBankingClass, get_agency_banking_super_token
from .email_replacers import EmailReplacer
from .email_templates.template_choices import EmailTemplates, EmailTypes
from .models import (
    ConstantTable,
    CustomUser,
    InterestsPaidTable,
    RecurrentChargeLogs,
    StaffCommissionAllocation,
    UserRecurrentChargeTable,
)
from .utils import EmailUtil


###TODO: MAKE THESE TASKS FASTER WITH FASTER QUERIES AND TRANSACTION WRITES
@shared_task
def auto_debit_user(auto_charge_table_id, payment_method, amount, user_email, plan_id, plan_type):
    now = timezone.localtime()
    auto_charge_table_instance = UserRecurrentChargeTable.objects.filter(id=auto_charge_table_id).last()

    try:
        if auto_charge_table_instance:
            # obtain the information needed
            user = CustomUser.objects.get(email=user_email)
            plan_instance = Utility.get_plan_instance(user=user, plan_id=plan_id, plan_type=plan_type)

            # regulator
            if ConstantTable.get_constant_table_instance().pay_from_wallet_regulator == False:
                raise Exception(f"pay wallet regulator inactive while charging for {user.email}.")

            if plan_instance.is_activated is False:
                raise Exception(f"please first activate the plan for {user.email}.")

            if plan_instance.maturity_date <= now.date():
                # print(
                #     f"plan's maturity date: {plan_instance.maturity_date.strftime('%d/%m/%Y')}, today's date: {now.date().strftime('%d/%m/%Y')}"
                # )
                auto_charge_table_instance.is_active = False
                auto_charge_table_instance.save()
                raise Exception(
                    f"{user.email}'s plan, {plan_instance.plan}, with a maturity date of {plan_instance.maturity_date.strftime('%d/%m/%Y')} has been exceeded."
                )

            if plan_instance.payment_method.upper() not in PaymentMethod:
                raise Exception("the payment method is not valid payment method choice.")

            if plan_instance.completed is True:
                auto_charge_table_instance.is_active = False
                auto_charge_table_instance.save()
                raise Exception(
                    f"this plan, {plan_instance.plan}, belonging to {user.email} is marked as complete and hence will not run again."
                )

            if plan_instance.is_active is False:
                raise Exception(
                    f"this plan, {plan_instance.plan}, owned by {user.email} plan is marked as inactive for some reason, please investigate."
                )

            # if the user recurrent task sends an amount with 0 from the next_charge_amount
            if amount == 0:
                plan_instance.completed = True
                auto_charge_table_instance.is_active = False
                auto_charge_table_instance.save()
                plan_instance.save()
                raise Exception(
                    f"this plan, {plan_instance.plan}, belonging to {user.email} has reached its completion."
                )

            if payment_method == PaymentMethod.WALLET:
                # call the AgencyBanking method to collect the cash
                
                if plan_type.upper() == "ONLENDING":
                    onlending_wallet = OnlendingSelector(user=user, ajo_user=plan_instance.ajo_user).get_onlending_wallet()
                    
                    funding_wallet = OnlendingSelector(
                            user=user, ajo_user=plan_instance.ajo_user
                        ).get_main_onlending_wallet()
                    
                    if funding_wallet.available_balance < amount:
                        funding_wallet = AjoUserSelector(
                            ajo_user=plan_instance.ajo_user
                        ).get_spending_wallet()
 
                    charge = OnlendingDynamicTransactions.dynamic_onlending_transactions_record(
                        user=user,
                        ajo_user=plan_instance.ajo_user,
                        amount=amount,
                        from_wallet=funding_wallet,
                        onlending_plan=plan_instance,
                        onlending_wallet=onlending_wallet,
                    )
                else:
                    charge = AgencyBankingClass.charge_user_wallet(
                        user=user,
                        plan_type=plan_type,
                        quotation_id=plan_instance.quotation_id,
                        transaction_pin=str(settings.AGENCY_BANKING_TRANSACTION_PIN),
                        amount=amount,
                        super_token=get_agency_banking_super_token(),
                        # super_token=settings.AGENCY_BANKING_SUPER_TOKEN,
                        transaction_form_type=TransactionFormType.WALLET_AUTO_CHARGE_DEPOSIT,
                    )

                RecurrentChargeLogs.objects.create(
                    user=user,
                    charge_type="WALLET",
                    charge_call_response=charge,
                    plan_type=plan_type,
                    plan_id=plan_id,
                    recurrent_object=auto_charge_table_instance
                )

                # if the charge was successful
                if (charge.get("status") == True) and ("debit_credit_info" in charge.keys()):
                    # get auto_charge_table

                    # get debit_credit_info
                    debit_credit = charge.get("debit_credit_info")

                    # get the transaction instance
                    transaction = charge.get("transaction")

                    # set the transaction wallet_balance_before, plan_balance_before
                    # and plan instance's balance_before
                    transaction.wallet_balance_before = debit_credit.get("balance_before")
                    transaction.plan_balance_before = plan_instance.amount_saved
                    plan_instance.plan_balance_before = plan_instance.amount_saved

                    # increase the amount saved
                    plan_instance.amount_saved += transaction.amount

                    # set the transaction balance after
                    transaction.wallet_balance_after = debit_credit.get("balance_after")
                    transaction.plan_balance_after = transaction.plan_balance_before + transaction.amount
                    plan_instance.plan_balance_after = plan_instance.plan_balance_before + transaction.amount

                    # # set the plan balance before and plan balance after
                    # plan_instance.plan_balance_before = debit_credit.get("balance_before")
                    # plan_instance.plan_balance_after = debit_credit.get("balance_after")

                    # save changes
                    with django_transaction.atomic():
                        plan_instance.save()
                        transaction.save()

                    ##################SEND THE EMAIL#################################################

                    # obtain the next savings date and make it human readable
                    if auto_charge_table_instance:
                        next_savings_date = auto_charge_table_instance.next_run_time
                        next_savings_date = next_savings_date.strftime("%b %d, %Y %H:%M")
                    else:
                        next_savings_date = None

                    # obtain the time the transaction took place and make it human readable
                    now = timezone.localtime()
                    date_string = now.strftime("%b %d, %Y %H:%M")

                    # obtain the maturity date and make it human readable
                    maturity_date = plan_instance.maturity_date
                    maturity_date = maturity_date.strftime("%B %d, %Y")

                    # send the email using the email task
                    send_html_email.delay(
                        template_name=EmailTemplates.SAVEDFROMWALLET,
                        user_email=user.email,
                        email_type=EmailTypes.SAVING,
                        plan_type=plan_type,
                        plan_transaction_object_id=transaction.id,
                        email_subject="[CREDIT] Transaction Notification",
                        plan_name=plan_instance.plan,
                        amount=format_currency(transaction.amount),
                        date_and_time=date_string,
                        savings_wallet=getattr(WalletTypes, plan_type.upper()),
                        next_savings_date=next_savings_date,
                        maturity_date=maturity_date,
                    )
                    # This checks if the plan is complete
                    remaining_amount = float(plan_instance.target) - float(plan_instance.amount_saved)
                    if remaining_amount <= 0:
                        # the plan is complete
                        # if plan_type.upper() == PlanType.HALAL and plan_instance.lock == True:
                        #     plan_instance.is_active = True
                        # else:
                        #     plan_instance.is_active = False

                        plan_instance.completed = True
                        auto_charge_table_instance.is_active = False
                        auto_charge_table_instance.next_charge_amount = 0

                        with django_transaction.atomic():
                            plan_instance.save()
                            auto_charge_table_instance.save()

                    # if remainder_became_amount:
                    #     # double check
                    #     if (amount == remaining_amount) and (plan_instance.target == plan_instance.amount_saved):
                    #         # the plan is complete
                    #         plan_instance.completed = True
                    #         if plan_instance.user_recurrent_charge:
                    #             plan_instance.user_recurrent_charge.is_active = False
                    #             plan_instance.user_recurrent_charge.save()
                    #         plan_instance.is_active = False

                    #         # check if the plan is a halal plan and if the lock is true
                    #         if plan_type.upper() == PlanType.HALAL:
                    #             if plan_instance.lock == True:
                    #                 plan_instance.is_active = True

                    #         plan_instance.save()

                    # # if remainder never became the amount but it's completed
                    # if (amount == remaining_amount) and (plan_instance.target == plan_instance.amount_saved):
                    #     # the plan is complete
                    #     plan_instance.completed = True
                    #     if plan_instance.user_recurrent_charge:
                    #         plan_instance.user_recurrent_charge.is_active = False
                    #         plan_instance.user_recurrent_charge.save()
                    #     plan_instance.is_active = False

                    #     # check if the plan is a halal plan and if the lock is true
                    #     if plan_type.upper() == PlanType.HALAL:
                    #         if plan_instance.lock == True:
                    #             plan_instance.is_active = True

                    #     plan_instance.save()

                    #     auto_charge_table_instance.is_active = False
                    #     auto_charge_table_instance.next_charge_amount = 0
                    #     auto_charge_table_instance.save()

                    else:
                        Utility.set_auto_charge_table_next_charge_amount(
                            auto_charge_table_instance=auto_charge_table_instance, amount=amount
                        )

                    # if the charge response is not successful

                    # check for  insufficient balance failed transaction
                elif (charge.get("status") == False) and ("insufficient" in charge.get("message")):
                    raise Exception(f"{user.email} has insufficient balance to process this transaction.")

                else:
                    # print(charge.get("message"))
                    raise Exception(
                        f"either user_id or error in amount while processing transaction for {user.email}."
                    )

            elif payment_method == PaymentMethod.DEBIT_CARD:
                # the conditions above (before if payment_method == PaymentMethod.WALLET) settles every condition we can think, so let's go straight to charging

                # obtain the masked_pan
                masked_pan = str(auto_charge_table_instance.masked_pan)
                # obtain the auth_code
                authorization_code = str(auto_charge_table_instance.auth_code)

                charge_dict = create_transaction_and_charge_card(
                    user=user,
                    auth_code=authorization_code,
                    plan_type=plan_type,
                    quotation_id=plan_instance.quotation_id,
                    amount=amount,
                    transaction_form_type=TransactionFormType.CARD_AUTO_CHARGE_DEPOSIT,
                )

                RecurrentChargeLogs.objects.create(
                    user=user,
                    charge_type="DEBIT_CARD",
                    charge_call_response=charge_dict,
                    plan_type=plan_type,
                    plan_id=plan_id,
                    recurrent_object=auto_charge_table_instance
                )

                # check if the charge response is successful
                if (charge_dict.get("status") is True) and ("debit_credit_info" in charge_dict.keys()):
                    # get debit_credit_info and transaction instance
                    debit_credit = charge_dict.get("debit_credit_info")
                    transaction = charge_dict.get("transaction")

                    # set the transaction's wallet_balance_before, plan_balance_before
                    # and the plan_instance's balance_before
                    transaction.wallet_balance_before = debit_credit.get("balance_before")
                    transaction.plan_balance_before = plan_instance.amount_saved
                    plan_instance.plan_balance_before = plan_instance.amount_saved

                    # increase the amount saved
                    plan_instance.amount_saved += transaction.amount

                    # set the transaction balance after
                    transaction.wallet_balance_after = debit_credit.get("balance_after")
                    transaction.plan_balance_after = transaction.plan_balance_before + transaction.amount
                    plan_instance.plan_balance_after = plan_instance.plan_balance_before + transaction.amount

                    # save the changes
                    with django_transaction.atomic():
                        plan_instance.save()
                        transaction.save()

                    # obtain the next savings date and make it human readable
                    if auto_charge_table_instance:
                        next_savings_date = auto_charge_table_instance.next_run_time
                        next_savings_date = next_savings_date.strftime("%b %d, %Y %H:%M")
                    else:
                        next_savings_date = None

                    # obtain the time the transaction took place and make it human readable
                    now = timezone.localtime(timezone.now())
                    date_string = now.strftime("%b %d, %Y %H:%M")

                    # obtain the maturity date and make it human readable
                    maturity_date = plan_instance.maturity_date
                    maturity_date = maturity_date.strftime("%B %d, %Y")

                    # send the email using the email task
                    send_html_email.delay(
                        template_name=EmailTemplates.SAVEDFROMCARD,
                        user_email=user.email,
                        email_type=EmailTypes.SAVING,
                        plan_type=plan_type,
                        plan_transaction_object_id=transaction.id,
                        email_subject="[CREDIT] Transaction Notification",
                        plan_name=plan_instance.plan,
                        amount=format_currency(transaction.amount),
                        date_and_time=date_string,
                        masked_pan=masked_pan,
                        savings_wallet=getattr(WalletTypes, plan_type.upper()),
                        next_savings_date=next_savings_date,
                        maturity_date=maturity_date,
                    )

                    # check if the plan is complete and if not, set the next charge
                    remaining_amount = float(plan_instance.target) - float(plan_instance.amount_saved)
                    if remaining_amount <= 0:
                        # the plan is complete
                        # if (plan_type.upper() == PlanType.HALAL) and plan_instance.lock is True:
                        #     plan_instance.is_active = True
                        # else:
                        #     plan_instance.is_active = False

                        plan_instance.completed = True
                        auto_charge_table_instance.is_active = False
                        auto_charge_table_instance.next_charge_amount = 0

                        # save the changes
                        with django_transaction.atomic():
                            plan_instance.save()
                            auto_charge_table_instance.save()

                    else:
                        Utility.set_auto_charge_table_next_charge_amount(
                            auto_charge_table_instance=auto_charge_table_instance,
                            amount=amount,
                        )
                # insufficient balance failed transaction
                elif (charge_dict.get("status") is False) and ("insufficient" in charge_dict.get("message")):
                    raise Exception(f"{user.email} has insufficient balance to process this transaction.")

                else:
                    # print(charge_dict.get("message"))
                    raise Exception(f"there was a problem while charging card for {user.email}.")

            # not a valid payment method
            else:
                raise Exception(f"the payment method for {plan_instance.plan} for {user.email} is not a valid method.")
    except Exception as err:
        # print(str(err))  # Custom error message
        return  # Abort that particular task


def close_auto_charge(auto_charge_table_instance: UserRecurrentChargeTable) -> None:
    auto_charge_table_instance.is_active = False
    auto_charge_table_instance.save()


@shared_task
@django_transaction.atomic
def auto_debit_rosca_group(
    auto_charge_table_id,
    payment_method,
    amount,
    user_email,
    plan_id,
    plan_type,
) -> str:
    auto_charge_table_instance = UserRecurrentChargeTable.objects.filter(id=auto_charge_table_id).last()
    user = CustomUser.objects.get(email=user_email)

    try:
        quotation_id = RotationGroupMemberService.get_quotation_id_from_member(
            member_quotation_id=auto_charge_table_instance.plan_quotation_id
        )

        rotation_group = RotationGroup.objects.get(quotation_id=quotation_id)

        ###CHECKS###

        # if the group is closed
        if rotation_group.status == RoscaGroupStatus.CLOSED:
            close_auto_charge(auto_charge_table_instance)
            return f"plan {auto_charge_table_instance.plan_id} for {auto_charge_table_instance.user.email} deactivated because group is closed"

        # if the member is not owing anybody at all
        paying_member: RotationGroupMember = rotation_group.members.filter(user=user).first()
        if paying_member:
            if len(RotationGroupMemberSelector.get_unpaid_positions(paying_member)) == 0:
                close_auto_charge(auto_charge_table_instance)
                return f"plan {auto_charge_table_instance.plan_id} for {auto_charge_table_instance.user.email} deactivated because user has paid completely"

        contribution_amount = RotationGroupPay.rotation_group_checks_before_payment(rotation_group=rotation_group)
        collector_info = RotationGroupPay.collecting_rotation_group_member_checks_before_payment(
            rotation_group=rotation_group,
            user=user,
        )
        collector_wallet: WalletSystem = collector_info.get("wallet")
        collector: RotationGroupMember = collector_info.get("member")

        transaction_description = f"{amount} was credited to collector {collector.position}, {collector.user.email}, for {rotation_group.name} rotation group, paid by {user.email}"
        if auto_charge_table_instance.payment_method == PaymentMethod.DEBIT_CARD:
            reference = f"SAVINGS-CR_{uuid.uuid4()}"
            transaction = TransactionService.create_deposit_by_card_transaction(
                user=collector.user,
                amount=contribution_amount,
                wallet_type=WalletTypes.ROSCA_PERSONAL,
                quotation_id=quotation_id,
                plan_type=plan_type,
                description=transaction_description,
                transaction_form_type=TransactionFormType.CARD_AUTO_CHARGE_DEPOSIT,
                unique_reference=reference,
            )

            charge = SavedAgencyBankingDebitCardPaymentProcessor.pay(
                amount=contribution_amount,
                masked_pan=auto_charge_table_instance.masked_pan,
                access_token=None,
                transaction=transaction,
                user=user,
                wallet=collector_wallet,
                auth_code=auto_charge_table_instance.auth_code,
            )

            if not charge.get("status"):
                return f"encountered this problem for plan {auto_charge_table_instance.plan_id}; {auto_charge_table_instance.user.email}: {charge.get('message')}"

        elif auto_charge_table_instance.payment_method == PaymentMethod.WALLET:
            transaction = TransactionService.create_deposit_by_wallet_transaction(
                user=collector.user,
                amount=contribution_amount,
                wallet_type=WalletTypes.ROSCA_PERSONAL,
                quotation_id=quotation_id,
                plan_type=plan_type,
                description=transaction_description,
                transaction_form_type=TransactionFormType.WALLET_DEPOSIT,
            )

            charge = UserAgencyBankingWalletPaymentProcessor.pay(
                wallet=collector_wallet,
                amount=contribution_amount,
                transaction_pin=str(settings.AGENCY_BANKING_TRANSACTION_PIN),
                transaction=transaction,
                user=user,
                super_token=get_agency_banking_super_token(),
            )

            if not charge.get("status"):
                return f"encountered this problem for plan {auto_charge_table_instance.plan_id}: {auto_charge_table_instance.user.email}: {charge.get('message')}"

        RotationGroupPay.pay_rotation_group_contribution(
            rotation_group=rotation_group,
            user=user,
            transaction=transaction,
            contribution_amount=contribution_amount,
            debit_credit_info=charge.get("debit_credit_info"),
        )

        return (
            f"plan {auto_charge_table_instance.plan_id} for {auto_charge_table_instance.user.email} paid successfully"
        )

    except Exception as err:
        return f"encountered this problem for plan {auto_charge_table_instance.plan_id}; {auto_charge_table_instance.user.email}: {str(err)}"


def generate_a_random_time_hours_from_now(now: Optional[datetime] = None) -> datetime:
    """
    Generates a new time within 2 to 5 hours from now. Returns a date and time that begins at a new hour

    Args:
        now (Optional[datetime], optional): the timezone to use for calculating. Defaults to None.

    Returns:
        datetime: e.g. datetime.datetime(2024, 2, 26, 17, 0, 0, tzinfo=zoneinfo.ZoneInfo(key='Africa/Lagos'))
                        datetime.datetime(2024, 2, 26, 18, 0, tzinfo=datetime.timezone.utc)
    """
    if not now:
        now = timezone.localtime()

    random_time = random.randrange(start=2, stop=6)

    new_time = (now + timezone.timedelta(hours=random_time)).replace(
        minute=0,
        second=0,
        microsecond=0,
    )

    return new_time


@shared_task
def set_next_charge_time():
    now = timezone.localtime()
    start_time = now - timezone.timedelta(minutes=15)
    end_time = now + timezone.timedelta(minutes=15)

    start_hour = start_time.replace(minute=0, second=0, microsecond=0).hour
    end_hour = end_time.replace(minute=59, second=59, microsecond=999999).hour

    # catch ACTIVE plans meant to run in the given time range on that day or days before.
    instances = UserRecurrentChargeTable.objects.filter(
        next_run_time__date__lte=now.date(),
        next_run_time__hour__range=[start_hour, end_hour],
        is_active=True,
    )

    for instance in instances:
        if instance.plan_type == PlanType.ROTATIONGROUP:
            quotation_id = RotationGroupMemberService.get_quotation_id_from_member(
                member_quotation_id=instance.plan_quotation_id
            )
            rotation_selector = RotationGroupSelector(quotation_id=quotation_id)
            next_date = rotation_selector.calculate_next_payment_day()
            if not next_date:
                next_run_time = generate_a_random_time_hours_from_now(now)
            else:
                next_run_time = datetime.combine(next_date, datetime.min.time()) + timedelta(hours=instance.plan_hour)

            if next_run_time:
                next_run_time_naive = next_run_time.replace(tzinfo=None)

            instance.last_run_time = now
            instance.next_run_time = timezone.make_aware(next_run_time_naive) if next_run_time else None

            instance.save()

            # call the task to debit the user
            auto_debit_rosca_group.delay(
                instance.id,
                instance.payment_method,
                instance.next_charge_amount,
                instance.user.email,
                instance.plan_id,
                instance.plan_type,
            )

        else:
            now = timezone.localtime()

            if instance.plan_frequency == "DAILY":
                next_run_time = now + timedelta(days=1)
                next_run_time2 = datetime.combine(next_run_time, datetime.min.time()) + timedelta(
                    hours=instance.plan_hour
                )

            elif instance.plan_frequency == "WEEKLY":
                next_run_time = now + timedelta(days=7)
                next_run_time2 = datetime.combine(next_run_time, datetime.min.time()) + timedelta(
                    hours=instance.plan_hour
                )

            elif instance.plan_frequency == "MONTHLY":
                next_run_time = now.replace(month=now.month + 1)

                # Handle the case where the current month is December
                if next_run_time.month == 1:
                    next_run_time = next_run_time.replace(year=next_run_time.year + 1)

                next_run_time2 = datetime.combine(next_run_time, datetime.min.time()) + timedelta(
                    hours=instance.plan_hour
                )

            else:
                break

            instance.last_run_time = now
            instance.next_run_time = timezone.make_aware(next_run_time2)

            instance.save()

            # call the task to debit the user
            auto_debit_user.delay(
                instance.id,
                instance.payment_method,
                instance.next_charge_amount,
                instance.user.email,
                instance.plan_id,
                instance.plan_type,
            )


@shared_task
def pay_daily_interests():
    # get the time
    today = timezone.localtime(timezone.now()).date()

    # get the chestlock plans that meet the following requirements:
    # 1: the plan is active: is_active = True
    # 2: the plan has been activated with payment: is_activated = True
    # 3: the plan is within the maturity date window i.e. today is less than the maturity date: maturity_date__gt = today
    # 4: the plan is a RECURRENT chestlock type
    chestlock_plans = ChestLock.objects.filter(
        is_active=True,
        is_activated=True,
        maturity_date__gt=today,
        chestlock_type=ChestlockType.RECURRENT,
    )

    # get the chestlock interest rate
    chestlock_interest = ConstantTable.get_constant_table_instance().chestlock_interest_rate

    # get the number of days in the current year
    current_year = timezone.localtime(timezone.now()).year
    days_in_current_year = (timezone.datetime(current_year + 1, 1, 1) - timezone.datetime(current_year, 1, 1)).days

    # calculate the daily interest rate
    daily_interest_rate = float(chestlock_interest / int(days_in_current_year))

    for chestlock in chestlock_plans:
        # code below is changed due to implementation update: InterestPaidTable would be created per interest
        # obtain or create the InterestPaidTable instance of the user
        # interest_paid_table_instance, created = InterestsPaidTable.objects.get_or_create(
        #     user=chestlock.user, plan_type=PlanType.CHESTLOCK, plan_quotation_id=chestlock.quotation_id
        # )

        # if (interest_paid_table_instance.last_updated.date() == today) and (
        #     interest_paid_table_instance.comm_amount is not None
        # ):
        #     continue

        # if daily interest has been paid for today, skip this plan
        if InterestsPaidTable.objects.filter(
            user=chestlock.user,
            plan_type=PlanType.CHESTLOCK,
            plan_quotation_id=chestlock.quotation_id,
            date_paid=today,
        ).exists():
            continue

        # obtain the number of days since the plan was made
        number_of_days_since_creation = (today - chestlock.date_created.date()).days + 1

        # calculate the interest amount
        comm_amount = round(float(float(chestlock.amount_saved) * daily_interest_rate / 100), 2)

        # create a transaction instance for the interest
        transaction = TransactionService.create_interest_transaction(
            user=chestlock.user,
            amount=comm_amount,
            wallet_type=WalletTypes.CHESTLOCK,
            quotation_id=chestlock.quotation_id,
            plan_type=PlanType.CHESTLOCK,
        )

        # obtain the chestlock wallet of the user
        wallet = WalletSystem.get_or_create_wallet(
            user=chestlock.user,
            wallet_type=WalletTypes.CHESTLOCK,
        )

        fund_wallet = WalletSystem.fund_balance(
            wallet=wallet,
            amount=comm_amount,
            transaction_instance=transaction,
        )

        # set the balance before for plan and transaction
        transaction.wallet_balance_before = round(fund_wallet.get("balance_before"), 2)
        transaction.plan_balance_before = round(chestlock.amount_saved, 2)
        chestlock.plan_balance_before = round(chestlock.amount_saved, 2)

        # increase the  amount saved
        chestlock.amount_saved += comm_amount
        chestlock.amount_saved = round(chestlock.amount_saved, 2)

        # set the transaction balance after
        transaction.wallet_balance_after = round(fund_wallet.get("balance_after"), 2)
        transaction.plan_balance_after = round(transaction.plan_balance_before + comm_amount, 2)
        chestlock.plan_balance_after = round(chestlock.plan_balance_before + comm_amount, 2)

        # set the transaction date completed and status
        transaction.transaction_date_completed = timezone.localtime(timezone.now())
        transaction.status = Status.SUCCESS

        # calculate the total_interest_paid
        total_interest_paid_previously = (
            InterestsPaidTable.objects.filter(
                user=chestlock.user, plan_type=PlanType.CHESTLOCK, plan_quotation_id=chestlock.quotation_id
            )
            .aggregate(Sum("comm_amount"))
            .get("comm_amount__sum")
        )
        total_interest_paid = (
            (comm_amount + total_interest_paid_previously)
            if total_interest_paid_previously is not None
            else comm_amount
        )

        # change the chestlock total_interest_paid
        chestlock.total_interest_earned = total_interest_paid

        # save changes for transaction and plan instance
        transaction.save()
        chestlock.save()

        # interest_paid_table_instance.comm_amount = comm_amount
        # interest_paid_table_instance.wallet_balance_before = transaction.wallet_balance_before
        # interest_paid_table_instance.wallet_balance_after = transaction.wallet_balance_after
        # interest_paid_table_instance.plan_balance_before = chestlock.plan_balance_before
        # interest_paid_table_instance.plan_balance_after = chestlock.plan_balance_after
        # interest_paid_table_instance.total_interest_paid += comm_amount
        # interest_paid_table_instance.total_interest_paid = round(interest_paid_table_instance.total_interest_paid, 2)

        # # save changes
        # interest_paid_table_instance.save()

        # Create a new InterestPaid instance for today's interest
        InterestsPaidTable.objects.create(
            user=chestlock.user,
            plan_type=PlanType.CHESTLOCK,
            plan_quotation_id=chestlock.quotation_id,
            comm_amount=comm_amount,
            day=number_of_days_since_creation,
            wallet_balance_before=transaction.wallet_balance_before,
            wallet_balance_after=transaction.wallet_balance_after,
            plan_balance_before=chestlock.plan_balance_before,
            plan_balance_after=chestlock.plan_balance_after,
            total_interest_paid=total_interest_paid,
            date_paid=today,
        )

        # COMMENTED OUT AS INSTRUCTED****
        #######THIS IS THE PART OF THE CODE THAT BACKTRACKS AND CHECKS FOR INTERESTS NOT PAID ON PREVIOUS DAYS#####################
        # Backtrack and pay interest for previous days if not paid
        # for i in range(1, number_of_days_since_creation):
        #     date_paid = today - timedelta(days=i)
        #     day = number_of_days_since_creation - i

        #     # If interest has already been paid for this day, skip it
        #     if InterestsPaidTable.objects.filter(
        #         user=chestlock.user,
        #         plan_type=PlanType.CHESTLOCK,
        #         plan_quotation_id=chestlock.quotation_id,
        #         date_paid=date_paid,
        #         day=day,
        #     ).exists():
        #         continue

        #     # Calculate the daily interest for this day
        #     comm_amount = round(float(float(chestlock.amount_saved) * daily_interest_rate / 100), 2)

        #     # create a transaction instance for the interest
        #     transaction = TransactionService.create_interest_transaction(
        #         user=chestlock.user,
        #         amount=comm_amount,
        #         wallet_type=WalletTypes.CHESTLOCK,
        #         quotation_id=chestlock.quotation_id,
        #         plan_type=PlanType.CHESTLOCK,
        #     )

        #     # obtain the chestlock wallet of the user
        #     wallet = WalletSystem.get_or_create_wallet(
        #         user=chestlock.user,
        #         wallet_type=WalletTypes.CHESTLOCK,
        #     )

        #     fund_wallet = WalletSystem.fund_balance(
        #         wallet=wallet,
        #         amount=comm_amount,
        #         transaction_instance=transaction,
        #     )

        #     # set the balance before for plan and transaction
        #     transaction.wallet_balance_before = round(fund_wallet.get("balance_before"), 2)
        #     transaction.plan_balance_before = round(chestlock.amount_saved, 2)
        #     chestlock.plan_balance_before = round(chestlock.amount_saved, 2)

        #     # increase the  amount saved
        #     chestlock.amount_saved += comm_amount
        #     chestlock.amount_saved = round(chestlock.amount_saved, 2)

        #     # set the transaction balance after
        #     transaction.wallet_balance_after = round(fund_wallet.get("balance_after"), 2)
        #     transaction.plan_balance_after = round(transaction.plan_balance_before + comm_amount, 2)
        #     chestlock.plan_balance_after = round(chestlock.plan_balance_before + comm_amount, 2)

        #     # set the transaction date completed and status
        #     transaction.transaction_date_completed = timezone.now()
        #     transaction.status = Status.SUCCESS

        #     # calculate the total_interest_paid
        #     total_interest_paid_previously = (
        #         InterestsPaidTable.objects.filter(
        #             user=chestlock.user, plan_type=PlanType.CHESTLOCK, plan_quotation_id=chestlock.quotation_id
        #         )
        #         .aggregate(Sum("comm_amount"))
        #         .get("comm_amount__sum")
        #     )
        #     total_interest_paid = (
        #         (comm_amount + total_interest_paid_previously)
        #         if total_interest_paid_previously is not None
        #         else comm_amount
        #     )

        #     # change the chestlock total_interest_paid
        #     chestlock.total_interest_paid = total_interest_paid

        #     # save changes for transaction and plan instance
        #     transaction.save()
        #     chestlock.save()

        #     # Create a new InterestPaid instance for that day's interest

        #     InterestsPaidTable.objects.create(
        #         user=chestlock.user,
        #         plan_type=PlanType.CHESTLOCK,
        #         plan_quotation_id=chestlock.quotation_id,
        #         comm_amount=comm_amount,
        #         day=day,
        #         wallet_balance_before=transaction.wallet_balance_before,
        #         wallet_balance_after=transaction.wallet_balance_after,
        #         plan_balance_before=chestlock.plan_balance_before,
        #         plan_balance_after=chestlock.plan_balance_after,
        #         total_interest_paid=total_interest_paid,
        #         date_paid=date_paid,
        #     )

@shared_task
def pay_onlending_recurrent_interests():
    today = timezone.localtime(timezone.now()).date()

    onlending_plans = Onlending.objects.filter(
        is_active=True,
        is_activated=True,
        maturity_date__gt=today,
        onlending_type=OnlendingType.RECURRENT,
    )

    onlending_interest = ConstantTable.get_constant_table_instance().onlending_interest_rate / 100
    interest_rate_per_day = onlending_interest / 365
     
    for onlending in onlending_plans:
        # if daily interest has been paid for today, skip this plan
        if InterestsPaidTable.objects.filter(
            user=onlending.user,
            plan_type=PlanType.ONLENDING,
            plan_quotation_id=onlending.quotation_id,
            date_paid=today,
        ).exists():
            continue

        interest_wallet = WalletSystem.get_or_create_interest_wallet(
            user=onlending.user,
            wallet_type=WalletTypes.ONLENDING_INTEREST,
            quotation_id=onlending.quotation_id
        )

        plan_balance = onlending.amount_saved
        interest_balance = interest_wallet.available_balance
        actual_balance = plan_balance + interest_balance

        interest_for_today = actual_balance * interest_rate_per_day

        transaction = TransactionService.create_interest_transaction(
            user=onlending.user,
            amount=interest_for_today,
            wallet_type=WalletTypes.ONLENDING_INTEREST,
            quotation_id=onlending.quotation_id,
            plan_type=PlanType.ONLENDING,
        )

        fund_interest_wallet = WalletSystem.fund_balance(
            wallet=interest_wallet,
            amount=interest_for_today,
            transaction_instance=transaction
        )

        # set the balance before for plan and transaction
        transaction.wallet_balance_before = round(fund_interest_wallet.get("balance_before"), 2)
        transaction.plan_balance_before = round(interest_balance, 2)
        # onlending.plan_balance_before = round(onlending.amount_saved, 2)

        # set the transaction balance after
        transaction.wallet_balance_after = round(fund_interest_wallet.get("balance_after"), 2)
        transaction.plan_balance_after = round(transaction.plan_balance_before + interest_for_today, 2)
        # onlending.plan_balance_after = round(chestlock.plan_balance_before + comm_amount, 2)

        # set the transaction date completed and status
        transaction.transaction_date_completed = timezone.localtime(timezone.now())
        transaction.status = Status.SUCCESS

        # # save changes for transaction and plan instance
        transaction.save()
        # onlending.save()
        
        number_of_days_since_creation = (today - onlending.date_created.date()).days + 1

        # # Create a new InterestPaid instance for today's interest
        InterestsPaidTable.objects.create(
            user=onlending.user,
            plan_type=PlanType.ONLENDING,
            plan_quotation_id=onlending.quotation_id,
            comm_amount=interest_for_today,
            day=number_of_days_since_creation,
            wallet_balance_before=transaction.wallet_balance_before,
            wallet_balance_after=transaction.wallet_balance_after,
            total_interest_paid=interest_wallet.available_balance,
            date_paid=today,
        )


@app.task
def send_html_email(
    template_name: EmailTemplates | str,
    user_email: str,
    email_type: EmailTypes,
    plan_type: PlanType,
    plan_transaction_object_id: int,
    email_subject: str,
    **kwargs,
):
    """
    This is a dynamic function, intended to attend to the email sending of three things:

        1. Savings made
        2. Creation of plan
        3. Withdrawal made

    If it 1. Savings made, you can save from wallet, cards, connected bank accounts

        1. Wallet, utilize the following keyword arguments after the positional keyword arguments:
            plan_name, amount, date_and_time, savings_wallet, next_savings_wallet, maturity_date

        2. Cards, utilize the following keyword arguments after the positional keyword arguments:
            plan_name, amount, date_and_time, masked_pan, savings_wallet, next_savings_date, maturity_date

    Args:
        template_name (EmailTemplates | str): the name of the template you want, an enum has been provided so that you don't need to know the template to use
        user_email (str): email of the user
        email_type (EmailTypes): What type of email do you want to send, from the three things described above
        plan_type (PlanType): plan type
        plan_transaction_object_id (int): the id of the plan instance or transaction instance
        email_subject (str): subject of the email
    """
    try:
        # file path for where all the email templates are saved
        file_path = f"accounts/email_templates/{template_name}.html"

        # replace the information
        htmp_temp = EmailReplacer.replace_placeholders_in_html_file(file_path=file_path, **kwargs)

        # obtain the User model object
        user = CustomUser.objects.filter(email=user_email).last()

        # obtain either the transaction or the plan instance
        if email_type.upper() in (EmailTypes.SAVING, EmailTypes.WITHDRAWAL):
            plan_transaction_object = Transaction.objects.filter(user=user, id=plan_transaction_object_id).last()
        elif email_type.upper() == EmailTypes.PLAN_CREATION:
            plan_transaction_object = Utility.get_plan_instance(
                user=user, plan_id=plan_transaction_object_id, plan_type=plan_type
            )

        # prepare the email data
        email_data = {
            "to_email": user_email,
            "email_subject": email_subject,
            "email_body": htmp_temp,
        }

        # send the email using the EmailUtil class method for sending HTML emails
        email = EmailUtil.send_mailgun_html_email(data=email_data)

        # the email is a boolean
        # if True
        if email:
            plan_transaction_object.email_sent = True
            plan_transaction_object.save()

    except Exception as e:
        pass
        # print(str(e))


@app.task
def send_custom_email_task(subject: str, recipient_list: list, context: dict, template_dir: str):
    from accounts.utils import EmailUtil

    EmailUtil.custom_send_email_api(subject, recipient_list, context, template_dir)
    return



@shared_task
def schedule_commission_disbursement():
    """
    Periodically disburses commission to staff beneficiaries.

    This task fetches all `StaffCommissionAllocation` instances where:
    - The commission hasn't been shared (`shared=False`).
    - The commission amount is greater than zero.

    For each instance, it calls `disburse_to_beneficiaries()` and returns a list of disbursement results.

    Returns:
        list: Results from each disbursement.
    """
    comm_queryset = (
        StaffCommissionAllocation.objects.prefetch_related(
        "beneficiaries").filter(
        shared=False, locked=False, commission_amount__gt=0,
        )
    )
    comm_result = []
    for comm_instance in comm_queryset:
        comm_instance.locked = True
        comm_instance.save()
        comm = (
            StaffCommissionAllocation
            .disburse_to_beneficiaries(comm_instance)
        )
        comm_result.append(comm)
    return comm_result
