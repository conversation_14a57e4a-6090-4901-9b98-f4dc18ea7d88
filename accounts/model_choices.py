from django.db import models
from django.utils.translation import gettext_lazy as _


class UserType(models.TextChoices):
    MERCHANT = "MERCHANT", _("MERCHANT")
    AGENT = "AGENT", _("AGENT")
    STAFF_AGENT = "STAFF_AGENT", _("STAFF_AGENT")
    KEY_ACCOUNT = "KEY_ACCOUNT", _("KEY_ACCOUNT")
    PROSPER_AGENT = "PROSPER_AGENT", _("PROSPER_AGENT")
    PERSONAL = "PERSONAL", _("PERSONAL")
    DMO_AGENT = "DMO_AGENT", _("DMO_AGENT")
    AJO_AGENT = "AJO_AGENT", _("AJO_AGENT")
    LIBERTY_RETAIL = "LIBERTY_RETAIL", _("LIBERTY_RETAIL")
    LOTTO_AGENT = "LOTTO_AGENT", _("LOTTO_AGENT")


class InterestType(models.TextChoices):
    DAILY = "DAILY", _("DAILY")
    ONEOFF = "ONEOFF", _("ONEOFF")


class IDType(models.TextChoices):
    NIN = "NIN", _("NIN")
    BVN = "BVN", _("BVN")


class IDVerificationSources(models.TextChoices):
    LIBERTYBVN = "LIBERTYBVN", _("LIBERTYBVN")
    IDENTITYPASS = "IDENTITYPASS", _("IDENTITYPASS")
    DOJAH = "DOJAH", _("DOJAH")
    YOUVERIFY = "YOUVERIFY", _("YOUVERIFY")


class AccountType(models.TextChoices):
    REGULAR = "REGULAR", _("REGULAR")
    KEY_ACCOUNT = "KEY_ACCOUNT", _("KEY_ACCOUNT")


class AgencyUserTypes(models.TextChoices):
    SAVINGS = "SAVINGS", _("SAVINGS")
    REPAYMENTS = "REPAYMENTS", _("REPAYMENTS")
    ESCROW = "ESCROW", _("ESCROW")
    COMMISSION = "COMMISSION", _("COMMISSION")
    LOANS = "LOANS", _("LOANS")