from django.db import models
from django.utils.translation import gettext_lazy as _


class EmailTemplates(models.TextChoices):
    SAVEDFROMWALLET = "savingsWallet", _("savingsWallet")
    SAVEDFROMCARD = "savingsDebitCard", _("savingsDebitCard")
    SAVEDFROMBANK = "savingsConnectedBanks", _("savingsConnectedBanks")


class EmailTypes(models.TextChoices):
    SAVING = "SAVING", _("SAVING")
    PLAN_CREATION = "PLAN_CREATION", _("PLAN_CREATION")
    WITHDRAWAL = "WITHDRAWAL", _("WITHDRAWAL")
