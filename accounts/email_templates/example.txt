'<!DOCTYPE html\n  PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">\n<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"\n  xmlns:o="urn:schemas-microsoft-com:office:office">\n\n<head>\n  <!--[if gte mso 9]>\n            <xml>\n                <o:OfficeDocumentSettings>\n                <o:AllowPNG/>\n                <o:PixelsPerInch>96</o:PixelsPerInch>\n                </o:OfficeDocumentSettings>\n            </xml>\n            <![endif]-->\n  <meta http-equiv="Content-type" content="text/html; charset=utf-8" />\n  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />\n  <meta http-equiv="X-UA-Compatible" content="IE=edge" />\n  <meta name="format-detection" content="date=no" />\n  <meta name="format-detection" content="address=no" />\n  <meta name="format-detection" content="telephone=no" />\n  <meta name="x-apple-disable-message-reformatting" />\n  <!--[if !mso]><!-->\n  <link href="https://fonts.googleapis.com/css?family=Muli:400,400i,700,700i" rel="stylesheet" />\n  <link rel="preconnect" href="https://fonts.googleapis.com">\n  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>\n  <link href="https://fonts.googleapis.com/css2?family=Nunito&display=swap" rel="stylesheet">\n  <link href="http://fonts.cdnfonts.com/css/clash-display" rel="stylesheet">\n  <link href="http://fonts.cdnfonts.com/css/montserrat" rel="stylesheet">\n  <link href="http://fonts.cdnfonts.com/css/raleway-5" rel="stylesheet">\n\n\n  <!--<![endif]-->\n  <title>|Liberty Email Template|</title>\n  <!--[if gte mso 9]>\n            <style type="text/css" media="all">\n                sup { font-size: 100% !important; }\n            </style>\n            <![endif]-->\n\n\n  <style type="text/css" media="screen">\n    /* Linked Styles */\n    body {\n      padding: 0 !important;\n      margin: 0 !important;\n      display: block !important;\n      min-width: 100% !important;\n      width: 100% !important;\n      background: #000000;\n      -webkit-text-size-adjust: none\n    }\n\n    a {\n      color: #66c7ff;\n      text-decoration: none\n    }\n\n    p {\n      padding: 0 !important;\n      margin: 0 !important\n    }\n\n    img {\n      -ms-interpolation-mode: bicubic;\n      /* Allow smoother rendering of resized image in Internet Explorer */\n    }\n\n    .mcnPreviewText {\n      display: none !important;\n    }\n\n    .cke_editable,\n    .cke_editable a,\n    .cke_editable span,\n    .cke_editable a span {\n      color: #000001 !important;\n    }\n\n    /* Mobile styles */\n    @media only screen and (max-device-width: 480px),\n    only screen and (max-width: 480px) {\n      .mobile-shell {\n        width: 100% !important;\n        min-width: 100% !important;\n      }\n\n      .text-header,\n      .m-center {\n        text-align: center !important;\n      }\n\n      .center {\n        margin: 0 auto !important;\n      }\n\n      .container {\n        padding: 20px 10px !important\n      }\n\n      .td {\n        width: 100% !important;\n        min-width: 100% !important;\n      }\n\n      .m-br-15 {\n        height: 15px !important;\n      }\n\n      .p30-15 {\n        padding: 30px 15px !important;\n      }\n\n      .m-block {\n        display: block !important;\n      }\n\n      .fluid-img img {\n        width: 100% !important;\n        max-width: 100% !important;\n        height: auto !important;\n      }\n\n      .column,\n      .column-top,\n      .column-empty,\n      .column-empty2,\n      .column-dir-top {\n        float: left !important;\n        width: 100% !important;\n        display: block !important;\n      }\n\n      .column-empty {\n        padding-bottom: 10px !important;\n      }\n\n      .column-empty2 {\n        padding-bottom: 30px !important;\n      }\n\n      .content-spacing {\n        width: 15px !important;\n      }\n    }\n  </style>\n</head>\n\n<body class="body"\n  style="padding:0 !important; margin:0 !important; display:block !important; min-width:100% !important; width:100% !important;  -webkit-text-size-adjust:none;">\n  <!--|IF:MC_PREVIEW_TEXT|-->\n  <!--[if !gte mso 9]><!-->\n  <!--<![endif]-->\n  <!--|END:IF|-->\n  <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#E0F2FE">\n    <tr>\n      <td align="center" valign="top">\n        <table width="750" border="0" cellspacing="0" cellpadding="0" class="mobile-shell"\n          style="padding: 0px 0px; background: #ffffff80;">\n          <tr>\n            <td class="td container"\n              style="width:650px; min-width:650px; font-size:0pt; line-height:0pt; margin:0; font-weight:normal; padding:5px 0px;">\n\n              <!-- HEADER -->\n              <div mc:repeatable="Select" mc:variant="Intro">\n                <table width="100%" border="0" cellspacing="0" cellpadding="0">\n                  <tr>\n                    <td style="padding-bottom: 10px;">\n                      <table width="100%" border="0" cellspacing="0" cellpadding="0">\n                        <tr>\n                          <td class="tbrr p30-15">\n                            <img\n                              src="https://res.cloudinary.com/djfz912mh/image/upload/v1663338210/Frame_1_aifose.png" />\n                          </td>\n                        </tr>\n                      </table>\n                    </td>\n                  </tr>\n                </table>\n              </div>\n              <!-- END HEADER -->\n\n              <!-- Intro -->\n              <div mc:repeatable="Select" mc:variant="Intro" style="text-align:center; z-index: 999;">\n                <table width="100%" border="0" cellspacing="0" cellpadding="0"\n                  style="margin-top: -10px; position: relative; z-index: 999;">\n                  <tr>\n                    <td style="padding-bottom: 10px;">\n                      <table width="90%" border="0" cellspacing="0" cellpadding="0" style="margin-left: 40px;">\n                        <tr>\n                          <td class="tbrr p30-15"\n                            style="padding: 30px 30px 20px 30px; border-radius:0px 0px 0px 0px; margin-top: -20px;"\n                            bgcolor="#ffffff">\n                            <table width="100%" border="0" cellspacing="0" cellpadding="0">\n                              <tr>\n                                <td class="text-left pb25"\n                                  style="color:#272727; font-family:\'Nunito\', Arial,sans-serif; font-size:19px; line-height:30px; text-align:left; padding-bottom:5px;">\n                                  <div mc:edit="text_3">\n                                    <span style="color:#073D9F;font-family: Raleway; font-weight: 700;">\n                                      You just successfully saved on LibertyPay!</span>\n                                    <br><br>\n                                    <span class="m-hide"\n                                      style="font-size:13px;color:#073D9F; font-family: Raleway; font-weight: 700;">\n                                      Hey John,\n                                    </span>\n                                    <br>\n                                    <span class="m-hide"\n                                      style="font-size:13px;color:#073D9F; font-family: Raleway; font-weight: 300;">\n                                      You have successfully saved into your regular plan.\n                                    </span><br><br>\n\n\n                                    <span class="m-hide"\n                                      style="font-size:20px;color:#073D9F; font-family: Raleway; font-weight: 700;">\n                                      ₦{amount}\n                                    </span>\n                                    <br><span class="m-hide"\n                                      style="font-size:13px;color:#261940; font-family: Raleway; font-weight: 300;">\n                                      {date_and_time}\n                                    </span><br>\n\n                                  </div>\n                                </td>\n                              </tr>\n                              <br>\n                              <br>\n                            </table>\n                            <table width="80%" border="0" cellspacing="0" cellpadding="0">\n\n                              <tr class="">\n                                <td class="h1 pb25" style="color:#4E4E4E; font-family:\'Raleway\', Arial,sans-serif; font-size:13px; line-height:35px; text-align:left; padding-bottom:5px;\n                                  border-bottom: 1px solid rgba(153, 163, 190, 0.2);;">\n                                  Savings Wallet:\n                                </td>\n                                <td class="h1 pb25" style="color:#073D9F; font-family:\'Raleway\', Arial,sans-serif; font-size:13px; line-height:35px; text-align:left; padding-bottom:5px;\n                                  border-bottom: 1px solid rgba(153, 163, 190, 0.2);font-weight: 500;">\n                                  {savings_wallet}\n                                </td>\n                              </tr>\n                              <tr class="">\n                                <td class="h1 pb25"\n                                  style="color:#4E4E4E; font-family:\'Raleway\', Arial,sans-serif; font-size:13px; line-height:35px; text-align:left; padding-bottom:5px;\n                                                                                            border-bottom: 1px solid rgba(153, 163, 190, 0.2);;">\n\n                                  Next Savings Date:\n                                </td>\n                                <td class="h1 pb25"\n                                  style="color:#073D9F; font-family:\'Raleway\', Arial,sans-serif; font-size:13px; line-height:35px; text-align:left; padding-bottom:5px;\n                                                                                            border-bottom: 1px solid rgba(153, 163, 190, 0.2);font-weight: 500;">\n\n                                  {next_savings_date}\n                                </td>\n                              </tr>\n                              <!-- <tr class="">\n                                <td class="h1 pb25"\n                                  style="color:#4E4E4E; font-family:\'Raleway\', Arial,sans-serif; font-size:13px; line-height:35px; text-align:left; padding-bottom:5px;\n                                                                                            border-bottom: 1px solid rgba(153, 163, 190, 0.2);;">\n\n                                  Service Charge:\n                                </td>\n                                <td class="h1 pb25"\n                                  style="color:#073D9F; font-family:\'Raleway\', Arial,sans-serif; font-size:13px; line-height:35px; text-align:left; padding-bottom:5px;\n                                                                                            border-bottom: 1px solid rgba(153, 163, 190, 0.2);font-weight: 500;">\n\n                                  {service_charge}\n                                </td>\n                              </tr> -->\n                              <tr class="">\n                                <td class="h1 pb25"\n                                  style="color:#4E4E4E; font-family:\'Raleway\', Arial,sans-serif; font-size:13px; line-height:35px; text-align:left; padding-bottom:5px;\n                                                                                                                                  border-bottom: 1px solid rgba(153, 163, 190, 0.2);;">\n\n                                  Maturity Date:\n                                </td>\n                                <td class="h1 pb25"\n                                  style="color:#073D9F; font-family:\'Raleway\', Arial,sans-serif; font-size:13px; line-height:35px; text-align:left; padding-bottom:5px;\n                                                                                                                                  border-bottom: 1px solid rgba(153, 163, 190, 0.2);font-weight: 500;">\n\n                                  {maturity_date}\n                                </td>\n                              </tr>\n\n                            </table>\n                          </td>\n                        </tr>\n                      </table>\n                    </td>\n                  </tr>\n                </table>\n\n              </div>\n              <!-- END Intro -->\n\n              <!-- SUb INTRO -->\n              <div mc:repeatable="Select" mc:variant="Intro">\n                <table width="90%" border="0" cellspacing="0" cellpadding="0"\n                  style="margin-left: 40px; margin-top: 10px;">\n                  <tr>\n                    <td style="padding-bottom: 10px;">\n                      <table width="100%" border="0" cellspacing="0" cellpadding="0">\n                        <tr>\n                          <td class="tbrr p30-15"\n                            style="padding: 30px 30px 20px 30px; border-radius:0px 0px 0px 0px; margin-top: -20px;"\n                            bgcolor="#ffffff">\n                            <table width="100%" border="0" cellspacing="0" cellpadding="0">\n                              <tr>\n                                <td class="h1 pb25"\n                                  style="color:#808080; font-family:\'Muli\', Arial,sans-serif; font-size:18px; line-height:35px; text-align:left; padding-bottom:10px;">\n                                  <div mc:edit="text_2">\n                                    Do you have any Question or need our help?\n                                    <br />\n                                    <span\n                                      style="color:#000000; font-family:\'Nunito\', Arial,sans-serif; font-size:14px;">Browse\n                                      our\n                                      <a href="" style="text-decoration: underline;">FAQ\n                                        on our help page</a> </span>\n                                  </div>\n                                </td>\n                              </tr>\n                              <tr>\n                                <td class="text-center pb25"\n                                  style="color:#000000; font-family:\'Nunito\', Arial,sans-serif; font-size:14px; line-height:30px; text-align:left; padding-bottom:25px;">\n                                  <div mc:edit="text_3">Or contact our customer\n                                    services\n                                  </div>\n                                </td>\n                              </tr>\n                              <tr>\n                                <td class="text-center pb25"\n                                  style="color:#0934F6; font-family:\'Nunito\', Arial,sans-serif; font-size:14px; line-height:25px; text-align:left; padding-bottom:25px;">\n                                  <div mc:edit="text_3">\n                                    <EMAIL> <br />\n                                    090 4000 1444\n                                  </div>\n                                </td>\n                              </tr>\n                            </table>\n                          </td>\n                        </tr>\n                      </table>\n                    </td>\n                  </tr>\n                </table>\n              </div>\n              <!-- END Intro -->\n\n              <!-- Footer -->\n              <table width="100%" border="0" cellspacing="0" cellpadding="0">\n                <tr>\n                  <td class="p30-15 bbrr"\n                    style="padding: 50px 30px; border-radius:0px 0px 26px 26px; background: #ffffff80;">\n                    <table width="100%" border="0" cellspacing="0" cellpadding="0">\n                      <tr>\n                      <tr>\n                        <td class="text-footer1 pb10"\n                          style="color:#4C1961; font-family:\'Nunito\', Arial,sans-serif; font-size:20px; line-height:20px; text-align:center; padding-bottom:30px;">\n                          <div mc:edit="text_36">Follow Us:\n                          </div>\n                        </td>\n                      </tr>\n                      <td align="center" style="padding-bottom: 30px;">\n\n                        <table border="0" cellspacing="0" cellpadding="0">\n                          <tr>\n                            <td class="img" width="55" style="font-size:0pt; line-height:0pt; text-align:left;">\n                              <a href="#" target="_blank"><img\n                                  src="https://res.cloudinary.com/eddiewurld/image/upload/v1650370220/Group_344_aaedtf.png"\n                                  width="38" height="38" mc:edit="image_14" style="max-width:38px;" border="0"\n                                  alt="" /></a>\n                            </td>\n                            <td class="img" width="55" style="font-size:0pt; line-height:0pt; text-align:left;">\n                              <a href="#" target="_blank"><img\n                                  src="https://res.cloudinary.com/eddiewurld/image/upload/v1650370220/Group_342_gxzkt9.png"\n                                  width="38" height="38" mc:edit="image_15" style="max-width:38px;" border="0"\n                                  alt="" /></a>\n                            </td>\n                            <td class="img" width="55" style="font-size:0pt; line-height:0pt; text-align:left;">\n                              <a href="#" target="_blank"><img\n                                  src="https://res.cloudinary.com/eddiewurld/image/upload/v1650370220/Group_343_llysyo.png"\n                                  width="38" height="38" mc:edit="image_16" style="max-width:38px;" border="0"\n                                  alt="" /></a>\n                            </td>\n                          </tr>\n                        </table>\n                      </td>\n                </tr>\n                <tr>\n                  <td class="text-footer2"\n                    style="color:#073D9F; font-family:\'Muli\', Arial,sans-serif; font-size:12px; line-height:26px; text-align:center;">\n                    <div mc:edit="text_37">© 2021, Liberty Assured Limted .\n                      <br />Terms | Privacy\n                    </div>\n                  </td>\n                </tr>\n              </table>\n            </td>\n          </tr>\n          <tr>\n            <td class="img" style="font-size:0pt; line-height:0pt; text-align:left;">\n              <div mc:edit="text_39">\n                <!--[if !mso]><!-->\n                |LIST:DESCRIPTION|\n                |LIST:ADDRESS|\n                |REWARDS_TEXT|\n                <!--<![endif]-->\n              </div>\n            </td>\n          </tr>\n        </table>\n        <!-- END Footer -->\n      </td>\n    </tr>\n  </table>\n  </td>\n  </tr>\n  </table>\n</body>\n\n</html>'