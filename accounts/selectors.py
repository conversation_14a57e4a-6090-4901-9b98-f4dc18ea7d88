from typing import List, Optional

from .models import CustomUser


class UserSelector:
    def __init__(self, user: CustomUser) -> None:
        self.user = user

    def get_referred_emails(self):
        return self.user.referred_emails.split(",") if self.user.referred_emails else []

    def get_referring_user(self) -> Optional[CustomUser]:
        return CustomUser.objects.filter(referred_emails__contains=self.user.email).last()
