from django.conf import settings
from django.contrib import admin
from django.contrib.admin.models import LogEntry
from django.utils.timezone import now
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from loans.models import LoanAnalysisLog
from savings.admin import set_readonly_fields

from .models import (
    ActionPermission,
    AgentLocations,
    AgentSwitchLog,
    AjoLoanCommissionRecipient,
    BlackListed,
    CommissionDisbursementLog,
    ConstantTable,
    CustomUser,
    FailuresLog,
    IDVerificationDumps,
    InterestsPaidTable,
    IPWhitelist,
    ManualAdjustment,
    MonnifyLog,
    NextOfKin,
    OTPLoginLog,
    RecurrentChargeLogs,
    SMSResponse,
    StaffCommissionAllocation,
    UserRecurrentChargeTable,
    USSDLog,
    PaystackLog,
    AgencyBankingToken,
)


# LogEntry
@admin.register(LogEntry)
class LogEntryAdmin(admin.ModelAdmin):
    date_hierarchy = "action_time"

    list_filter = [
        # 'user',
        "content_type",
        "action_flag",
    ]

    search_fields = [
        "user__email",
        "object_repr",
        "change_message",
    ]

    list_display = [
        "action_time",
        "user",
        "change_message",
        "content_type",
        # 'object_link',
        "action_flag",
    ]


####################################################################################
# RESOURCES


class CustomUserResource(resources.ModelResource):
    class Meta:
        model = CustomUser


class ConstantTableResource(resources.ModelResource):
    class Meta:
        model = ConstantTable


class UserRecurrentChargeTableResource(resources.ModelResource):
    class Meta:
        model = UserRecurrentChargeTable


class InterestsPaidTableResource(resources.ModelResource):
    class Meta:
        model = InterestsPaidTable


class SMSResponseResource(resources.ModelResource):
    class Meta:
        model = SMSResponse


class FailuresLogResource(resources.ModelResource):
    class Meta:
        model = FailuresLog


class USSDLogResource(resources.ModelResource):
    class Meta:
        model = USSDLog


class OTPLoginLogResource(resources.ModelResource):
    class Meta:
        model = OTPLoginLog


class BlackListedResource(resources.ModelResource):
    class Meta:
        model = BlackListed


class AgentSwitchLogResource(resources.ModelResource):
    class Meta:
        model = AgentSwitchLog


class NextOfKinResource(resources.ModelResource):
    class Meta:
        model = NextOfKin


class MonnifyLogResource(resources.ModelResource):
    class Meta:
        model = MonnifyLog


class IDVerificationDumpsResource(resources.ModelResource):
    class Meta:
        model = IDVerificationDumps


class IPWhitelistResource(resources.ModelResource):
    class Meta:
        model = IPWhitelist


class StaffCommissionAllocationResource(resources.ModelResource):
    class Meta:
        model = StaffCommissionAllocation


class AjoLoanCommissionRecipientResource(resources.ModelResource):
    class Meta:
        model = AjoLoanCommissionRecipient


class ManualAdjustmentResource(resources.ModelResource):
    class Meta:
        model = ManualAdjustment


class CommissionDisbursementLogResource(resources.ModelResource):
    class Meta:
        model = CommissionDisbursementLog


class ActionPermissionResource(resources.ModelResource):
    class Meta:
        model = ActionPermission


class PaystackLogResource(resources.ModelResource):
    class Meta:
        model = PaystackLog


class RecurrentChargeLogsResource(resources.ModelResource):
    class Meta:
        model = RecurrentChargeLogs


class AgencyBankingTokenResource(resources.ModelResource):
    class Meta:
        model = AgencyBankingToken


####################################################################################
# RESOURCE ADMINS


class LoanAnalysisLogInline(admin.TabularInline):
    model = LoanAnalysisLog
    extra = 0

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.filter(created_at__date=now().date())


class CustomUserResourceAdmin(ImportExportModelAdmin):
    resource_class = CustomUserResource
    search_fields = [
        "email",
        "user_phone",
        "username",
        "customer_user_id",
    ]
    readonly_fields = [
        "user_password",
    ]

    list_filter = (
        "is_staff",
        "is_superuser",
        "user_type",
    )
    inlines = [LoanAnalysisLogInline]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def generate_lite_referral_code(self, request, queryset):
        import secrets

        message = "Invalid action"
        for query_instance in queryset:
            if query_instance.user_type.lower() != "lite":
                message = f"user type of user id {query_instance.id} is not valid for this action"
                break

            if query_instance.referral_code:
                continue

            query_instance.referral_code = secrets.token_hex(4).upper()
            query_instance.save()
            message = "referral code generated successfully"

        self.message_user(request, str(message))

    generate_lite_referral_code.short_description = (
        "Generate referral code for lite users"
    )
    generate_lite_referral_code.allow_tags = True

    actions = [generate_lite_referral_code]


class ConstantTableResourceAdmin(ImportExportModelAdmin):
    resource_class = ConstantTableResource
    search_fields = [
        "chestlock_minimum_target",
        "chestlock_interest_rate",
        "halal_minimum_target",
        "quicksavings_minimum_target",
        "quicksavings_interest_rate",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UserRecurrentChargeTableResourceAdmin(ImportExportModelAdmin):
    resource_class = UserRecurrentChargeTableResource
    search_fields = [
        "user__email",
        "plan_type",
        "plan_id",
        "plan_quotation_id",
        "plan_frequency",
        "payment_method",
        "last_charge_amount",
        "last_run_time",
    ]
    readonly_fields = set_readonly_fields(
        "last_charge_amount",
        "next_charge_amount",
    )
    list_filter = (
        "plan_type",
        "is_active",
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RecurrentChargeLogsAdmin(ImportExportModelAdmin):
    resource_class = RecurrentChargeLogsResource
    search_fields = [
        "user__email",
        "charge_type",
        "plan_type",
        "plan_id",
        "recurrent_object__plan_id",
        "recurrent_object__plan_type",
        "date_created",
    ]
    readonly_fields = set_readonly_fields(
        "charge_call_response",
        "date_created",
        "last_updated",
    )
    list_filter = (
        "charge_type",
        "plan_type",
        "recurrent_object__plan_type",
    )
    list_display = (
        "user",
        "charge_type",
        "plan_type",
        "plan_id",
        "recurrent_object",
        "date_created",
        "last_updated",
    )


class InterestsPaidTableResourceAdmin(ImportExportModelAdmin):
    resource_class = InterestsPaidTableResource
    search_fields = [
        "user__email",
        "plan_quotation_id",
        "comm_amount",
        "day",
        "total_interest_paid",
        "date_paid",
    ]
    readonly_fields = set_readonly_fields(
        "comm_amount",
        "wallet_balance_before",
        "wallet_balance_after",
        "plan_balance_before",
        "plan_balance_after",
        "total_interest_paid",
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SMSResponseResourceAdmin(ImportExportModelAdmin):
    resource_class = SMSResponseResource
    search_fields = [
        "user__email",
        "onboarded_user__phone_number",
        "sent",
        "receiver",
    ]
    readonly_fields = set_readonly_fields(
        "payload",
        "response_payload",
        "message",
        "receiver",
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class FailuresLogResourceAdmin(ImportExportModelAdmin):
    resource_class = FailuresLogResource
    search_fields = [
        "user__email",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class USSDLogAdmin(ImportExportModelAdmin):
    resource_class = USSDLogResource
    search_fields = [
        "phone_number",
    ]
    list_filter = [
        "text",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class OTPLoginLogAdmin(ImportExportModelAdmin):
    resource_class = OTPLoginLogResource
    search_fields = [
        "phone_number",
    ]
    list_filter = [
        "created_at",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class BlackListedResourceAdmin(ImportExportModelAdmin):
    resource_class = BlackListedResource

    # search_fields = []

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AgentSwitchLogResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["old_agent", "new_agent"]
    resource_class = AgentSwitchLogResource
    search_fields = [
        "old_agent__email",
        "new_agent__email",
    ]
    list_filter = [
        "created_at",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class NextOfKinResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["user", "ajo_user"]
    resource_class = NextOfKinResource
    search_fields = [
        "user__email",
        "ajo_user__phone_number",
    ]
    list_filter = [
        "created_at",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class MonnifyLogResourceAdmin(ImportExportModelAdmin):
    resource_class = MonnifyLogResource
    search_fields = [
        "reference",
    ]
    list_filter = [
        "created_at",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class IDVerificationDumpsResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["user", "ajo_user"]
    resource_class = IDVerificationDumpsResource
    search_fields = [
        "user__email",
        "ajo_user__phone_number",
    ]
    list_filter = [
        "created_at",
        "source",
    ]

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields] + ["result"]
        data.remove("dump")
        return data


class IPWhitelistResourceAdmin(ImportExportModelAdmin):
    resource_class = IPWhitelistResource

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        return data


class StaffCommissionAllocationResourceAdmin(ImportExportModelAdmin):
    resource_class = StaffCommissionAllocationResource
    search_fields = [
        "loan_id",
    ]
    list_filter = [
        "transferred_to_commision",
        "created_at",
        "shared",
    ]

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        return data

    def unlock_locked_allocations_not_shared(self, request, queryset):
        """Unlock Locked Allocations that have not been Shared"""

        response_data = {}
        for query in queryset:
            query: StaffCommissionAllocation
            if query.shared is False and query.locked and query.commission_amount > 0:
                query.locked = False
                query.save()
                response_data[query.id] = "Unlocked"
            else:
                response_data[query.id] = "Not Unlocked"
        self.message_user(request, response_data)

    def run_staff_commission_disbursement(self, request, queryset):
        comm_result = []
        message = "No action taken"

        for query in queryset:
            query: StaffCommissionAllocation
            if (
                query.shared is False
                and query.locked is False
                and query.commission_amount > 0
            ):
                query.locked = True
                query.save(update_fields=["locked"])
                message = f"Locked {query.id}"
                comm = StaffCommissionAllocation.disburse_to_beneficiaries(query)
                comm_result.append(comm)
                message = comm_result
        self.message_user(request, message)

    unlock_locked_allocations_not_shared.short_description = (
        "Unlock Locked Allocations Not Shared"
    )
    unlock_locked_allocations_not_shared.allowed_tags = True

    run_staff_commission_disbursement.short_description = (
        "Run staff commission disbursement"
    )
    run_staff_commission_disbursement.allowed_tags = True

    actions = [
        unlock_locked_allocations_not_shared,
        run_staff_commission_disbursement,
    ]


class AjoLoanCommissionRecipientResourceAdmin(ImportExportModelAdmin):
    resource_class = AjoLoanCommissionRecipientResource
    search_fields = [
        "phone_number",
        "email",
    ]
    list_filter = [
        "created_at",
        "is_active",
    ]

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields] + [
            "recipient_unique_id"
        ]
        data.remove("phone_number")
        return data


class ManualAdjustmentResourceAdmin(ImportExportModelAdmin):
    resource_class = ManualAdjustmentResource
    search_fields = [
        "phone_number",
        "email",
    ]
    list_filter = [
        "status",
        "created_at",
        "correction_type",
    ]

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        return data


class CommissionDisbursementLogResourceAdmin(ImportExportModelAdmin):
    resource_class = CommissionDisbursementLogResource
    search_fields = ["id", "recipient_id"]
    list_filter = [
        "status_code",
        "created_at",
    ]

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields] + ["payload"]
        data.remove("request_result")
        return data


class ActionPermissionResourceAdmin(ImportExportModelAdmin):
    resource_class = ActionPermissionResource
    # search_fields = []
    # list_filter = []

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        return data


class PaystackLogResourceAdmin(ImportExportModelAdmin):
    resource_class = PaystackLogResource
    # search_fields = []
    # list_filter = []

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        return data


class AgencyBankingTokenResourceAdmin(ImportExportModelAdmin):
    resource_class = AgencyBankingTokenResource
    # search_fields = []
    list_filter = ['token_owner',]

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields if field.name not in ["token"]]
        return data

    def get_exclude(self, request, obj=None):
        if not request.user.is_superuser:
            return ('token_owner',)
        else:
            return super().get_exclude(request, obj)



####################################################################################
# REGISTER MODELS

admin.site.register(CustomUser, CustomUserResourceAdmin)
admin.site.register(ConstantTable, ConstantTableResourceAdmin)
admin.site.register(UserRecurrentChargeTable, UserRecurrentChargeTableResourceAdmin)
admin.site.register(InterestsPaidTable, InterestsPaidTableResourceAdmin)
admin.site.register(SMSResponse, SMSResponseResourceAdmin)
admin.site.register(FailuresLog, FailuresLogResourceAdmin)
admin.site.register(USSDLog, USSDLogAdmin)
admin.site.register(OTPLoginLog, OTPLoginLogAdmin)
admin.site.register(BlackListed, BlackListedResourceAdmin)
admin.site.register(AgentSwitchLog, AgentSwitchLogResourceAdmin)
admin.site.register(NextOfKin, NextOfKinResourceAdmin)
admin.site.register(MonnifyLog, MonnifyLogResourceAdmin)
admin.site.register(IDVerificationDumps, IDVerificationDumpsResourceAdmin)
admin.site.register(IPWhitelist, IPWhitelistResourceAdmin)
admin.site.register(StaffCommissionAllocation, StaffCommissionAllocationResourceAdmin)
admin.site.register(AjoLoanCommissionRecipient, AjoLoanCommissionRecipientResourceAdmin)
admin.site.register(ManualAdjustment, ManualAdjustmentResourceAdmin)
admin.site.register(CommissionDisbursementLog, CommissionDisbursementLogResourceAdmin)
admin.site.register(ActionPermission, ActionPermissionResourceAdmin)
admin.site.register(PaystackLog, PaystackLogResourceAdmin)
admin.site.register(RecurrentChargeLogs, RecurrentChargeLogsAdmin)
admin.site.register(AgencyBankingToken, AgencyBankingTokenResourceAdmin)
admin.site.register(AgentLocations)
