from typing import Any

from django.contrib.humanize.templatetags import humanize
from django.conf import settings
from django.db import models
from django.utils.translation import gettext as _
import datetime
import uuid
import jwt

class BaseModel(models.Model):
    updated_at = models.DateTimeField(auto_now=True, blank=True)
    created_at = models.DateTimeField(_("date created"), auto_now_add=True, blank=True)

    class Meta:
        abstract = True

    def time_updated(self):
        return f"{humanize.naturaltime(self.updated_at)} - {self.updated_at.date()}"

    def time_created(self):
        return f"{humanize.naturaltime(self.created_at)} -  {self.created_at.date()}"


class RoundedFloatField(models.FloatField):
    description = _("Rounded FloatField that rounds up all values to 2 decimal places")

    def to_python(self, value: Any) -> Any:
        if value is None:
            return value
        return round(float(value), 2)

    def get_prep_value(self, value: Any) -> Any:
        if value is None:
            return value
        return round(float(value), 2)


def generate_micro_saver_access_token(user_id):
  
    expiration_time = datetime.datetime.utcnow() + datetime.timedelta(days=1)
    jwt_id = str(uuid.uuid4())
    payload = {
        "token_type": "access",
        'exp': expiration_time,
        'jti': jwt_id,
        'user_id': user_id,
    }
    return jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')