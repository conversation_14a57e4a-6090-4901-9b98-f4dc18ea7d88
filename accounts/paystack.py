from django.conf import settings
from accounts.models import PaystackLog
import requests


class PaystackApi:
    base_url = settings.PAYSTACK_BASE_URL

    @classmethod
    def resolve_account_number(cls, account_number, bank_code):
        url = f"{cls.base_url}/bank/resolve"
        params = {"account_number": account_number, "bank_code": bank_code}

        headers = {"Authorization": f"Bearer {settings.PAYSTACK_API_SECRET}"}

        try:
            response = requests.get(url=url, headers=headers, params=params)
            res = response.json()

            if response.status_code == 200:
                data = {"status": True, "data": res}
            else:
                data = {"status": False, "data": res}
            return data
        except:
            return {"status": False, "data": []}

    @classmethod
    def create_single_transfer_recipient(cls, account_number, bank_code, name, type):
        url = f"{cls.base_url}/transferrecipient"
        headers = {
            "Authorization": f"Bearer {settings.PAYSTACK_API_SECRET}",
            "Content-Type": "application/json",
        }

        data = {
            "type": type,
            "name": name,
            "account_number": account_number,
            "bank_code": bank_code,
        }

        # Create log
        recipient_request_log = PaystackLog.objects.create(
            payload=data,
            request_type="RECIPIENT_ID",
        )

        try:
            response = requests.post(url=url, headers=headers, json=data)
            res = response.json()
            print("paystack response::::", res)
            recipient_request_log.response = res
            recipient_request_log.save()

            print(response.status_code)

            if response.status_code == 201:
                data = {"status": True, "data": res}
            else:
                data = {"status": False, "data": res}
            return data
        except:
            recipient_request_log.response = {"message": "An error occured"}
            recipient_request_log.save()
            return {"status": False, "data": []}

    @classmethod
    def initiate_single_transfer(cls, payload):
        url = f"{cls.base_url}/transfer"
        headers = {
            "Authorization": f"Bearer {settings.PAYSTACK_API_SECRET}",
            "Content-Type": "application/json",
        }

        # Create transfer recipient
        recipient_request = cls.create_single_transfer_recipient(
            account_number=payload["account_number"],
            bank_code=payload["bank_code"],
            name=payload["recipient_name"],
            type="nuban",
        )

        if recipient_request["status"] == False:
            return {
                "status": False,
                "data": recipient_request["data"],
            }

        # print("recipient_request::::", recipient_request)

        transfer_request_log = PaystackLog.objects.create(
            payload=payload,
            request_type="TRANSFER",
        )

        data = {
            "source": "balance",
            "amount": payload["amount"],
            "recipient": recipient_request["data"]["data"]["recipient_code"],
            "reason": payload["reason"],
            "reference": payload["reference"],
        }

        try:
            response = requests.post(url=url, headers=headers, json=data)
            res = response.json()
            print("paystack response::::", res)

            transfer_request_log.response = res
            transfer_request_log.save()

            if response.status_code == 200:
                data = {"status": True, "data": res}
            else:
                data = {"status": False, "data": res}
            return data
        except:
            data = {"status": False, "data": []}
            transfer_request_log.response = data
            transfer_request_log.save()
            return data

    @classmethod
    def verify_transaction_status(cls, reference):
        url = f"{cls.base_url}/transfer/verify/{reference}"
        headers = {
            "Authorization": f"Bearer {settings.PAYSTACK_API_SECRET}",
        }

        try:
            response = requests.get(url=url, headers=headers)
            res = response.json()
            print("paystack response::::", res)

            if response.status_code == 200:
                if res["data"]["status"].lower() == "success":
                    data = {
                        "status": True,
                        "reversed": False,
                        "transferred": True,
                        "data": res,
                    }
                else:
                    data = {
                        "status": True,
                        "reversed": False,
                        "transferred": False,
                        "data": res,
                    }
            else:
                data = {
                    "status": False,
                    "reversed": False,
                    "transferred": False,
                    "data": res,
                }
            return data
        except:
            return {
                "status": False,
                "reversed": False,
                "transferred": False,
                "data": [],
            }

    @classmethod
    def check_balance(cls):
        url = f"{cls.base_url}/balance"
        headers = {
            "Authorization": f"Bearer {settings.PAYSTACK_API_SECRET}",
        }

        try:
            response = requests.get(url=url, headers=headers)
            res = response.json()
            print("paystack response::::", res)

            if response.status_code == 200:
                data = {"status": True, "data": res}
            else:
                data = {"status": False, "data": res}
            return data
        except:
            return {"status": False, "data": []}

    @classmethod
    def initiate_disable_otp(cls):
        url = f"{cls.base_url}/transfer/disable_otp"
        headers = {
            "Authorization": f"Bearer {settings.PAYSTACK_API_SECRET}",
            "Content-Type": "application/json",
        }

        data = {}

        try:
            response = requests.post(url=url, headers=headers, json=data)
            res = response.json()
            print("paystack response::::", res)

            if response.status_code == 200:
                data = {"status": True, "data": res}
            else:
                data = {"status": False, "data": res}
            return data
        except:
            return {"status": False, "data": []}

    @classmethod
    def disable_otp(cls, otp):
        url = f"{cls.base_url}/transfer/disable_otp_finalize"
        headers = {
            "Authorization": f"Bearer {settings.PAYSTACK_API_SECRET}",
            "Content-Type": "application/json",
        }

        data = {
            "otp": otp,
        }

        try:
            response = requests.post(url=url, headers=headers, json=data)
            res = response.json()
            print("paystack response::::", res)

            if response.status_code == 200:
                data = {"status": True, "data": res}
            else:
                data = {"status": False, "data": res}
            return data
        except:
            return {"status": False, "data": []}
