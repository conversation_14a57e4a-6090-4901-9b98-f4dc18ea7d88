from pprint import pprint

from django.core.management.base import BaseCommand

from accounts.agency_banking import AgencyBankingClass, agent_login
from accounts.models import AjoLoanCommissionRecipient, StaffCommissionAllocation
from accounts.tasks import schedule_commission_disbursement
from loans.models import AjoLoan


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        # loan = AjoLoan.objects.get(id=16)
        # k = StaffCommissionAllocation.create_commission_distribution(loan_id=loan.id)
        # # k = AjoLoanCommissionRecipient.get_share_percentage()

        # k = AgencyBankingClass.disburse_staff_loan_commission(
        #     transaction_ref="LBGTY-123",
        #     recipient="*************",
        #     amount=100,
        #     profit=50,
        # )
        # pprint(k)

        comm = schedule_commission_disbursement()
        # comm = agent_login()
        print(comm)
