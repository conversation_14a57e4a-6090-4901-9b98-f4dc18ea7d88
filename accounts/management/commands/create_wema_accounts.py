from django.core.management.base import BaseCommand

from ajo.models import BankAccountDetails
from ajo.services import BankAccountService


class Command(BaseCommand):
    help = "Invalidate all Ajo User Cooperate Accounts and Create Wema Accounts for them"

    def handle(self, *args, **kwargs):

        all_bank_accounts = BankAccountDetails.objects.filter(
            account_type="AJO_USER_CORPORATE",
            is_active=True,
            ajo_user__isnull=False,
        )

        for account in all_bank_accounts:

            try:

                self.stdout.write(
                    self.style.HTTP_INFO(
                        f"ACCOUNT:: {account} - AJO USER:: {account.ajo_user}",
                    )
                )

                if account.ajo_user is None:
                    self.stdout.write(self.style.ERROR("this account has no ajo user"))
                    continue

                ajo_user = account.ajo_user
                account_form_type = account.form_type
                accounts = BankAccountDetails.objects.filter(
                    ajo_user=ajo_user,
                    # form_type=account_form_type,
                )
                accounts_count = accounts.count()

                wema_account_details = BankAccountService.create_wema_account(
                    ajo_user=ajo_user,
                    acct_form_type=account_form_type,
                    new_account=True,
                    previous_created_count=accounts_count,
                )

                self.stdout.write(
                    self.style.HTTP_INFO(
                        f"Wema Account created for this ajo user. Log: {wema_account_details}",
                    )
                )

                account.is_active = False
                account.save()

            except Exception as e:

                print(str(e))

        self.stdout.write(
            msg="Wema Accounts created for all Ajo User Cooperate Accounts and old accounts deactivated",
            style_func=self.style.SUCCESS,
        )
