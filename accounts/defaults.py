TEIR_DEFAULT_DATA = [
    {"tier": 1, "min_loans": 0, "max_loans": 10, "percentage": 90},
    {"tier": 2, "min_loans": 10, "max_loans": 20, "percentage": 85},
    {"tier": 3, "min_loans": 21, "max_loans": ********, "percentage": 80},
]

BANKS_LIST = [
        {
            "bank_code": "090310",
            "cbn_code": "",
            "name": "EdFin MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100026",
            "cbn_code": "565",
            "name": "Carbon",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090267",
            "cbn_code": "50211",
            "name": "Kuda Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "060003",
            "cbn_code": "561",
            "name": "NOVA MB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100002",
            "cbn_code": "",
            "name": "Paga",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "999999",
            "cbn_code": "566",
            "name": "VFD MICROFINANCE BANK",
            "bank_short_name": "vfd",
            "ussd_short_code": ""
        },
        {
            "bank_code": "000012",
            "cbn_code": "221",
            "name": "StanbicIBTC Bank",
            "bank_short_name": "Stanbic IBTC Plc",
            "ussd_short_code": "909"
        },
        {
            "bank_code": "100003",
            "cbn_code": "311",
            "name": "Parkway-ReadyCash",
            "bank_short_name": "parkway",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100007",
            "cbn_code": "304",
            "name": "Stanbic IBTC @ease Wallet",
            "bank_short_name": "stanbic-mobile",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090001",
            "cbn_code": "401",
            "name": "ASOSavings",
            "bank_short_name": "aso-savings",
            "ussd_short_code": ""
        },
        {
            "bank_code": "000023",
            "cbn_code": "101",
            "name": "Providus Bank",
            "bank_short_name": "providus",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090325",
            "cbn_code": "51310",
            "name": "Sparkle",
            "bank_short_name": "SPKL",
            "ussd_short_code": ""
        },
        {
            "bank_code": "000021",
            "cbn_code": "068",
            "name": "StandardChartered",
            "bank_short_name": "standard-chartered",
            "ussd_short_code": ""
        },
        {
            "bank_code": "000022",
            "cbn_code": "100",
            "name": "SUNTRUST BANK",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "000026",
            "cbn_code": "302",
            "name": "Taj Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090115",
            "cbn_code": "51211",
            "name": "TCF",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "000025",
            "cbn_code": "102",
            "name": "Titan Trust Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "000015",
            "cbn_code": "057",
            "name": "Zenith Bank",
            "bank_short_name": "zenith",
            "ussd_short_code": "966"
        },
        {
            "bank_code": "000017",
            "cbn_code": "035",
            "name": "Wema Bank",
            "bank_short_name": "wema",
            "ussd_short_code": "945"
        },
        {
            "bank_code": "000011",
            "cbn_code": "215",
            "name": "Unity Bank",
            "bank_short_name": "unity",
            "ussd_short_code": "7799"
        },
        {
            "bank_code": "000004",
            "cbn_code": "033",
            "name": "UBA",
            "bank_short_name": "uba",
            "ussd_short_code": "919"
        },
        {
            "bank_code": "000018",
            "cbn_code": "032",
            "name": "Union Bank",
            "bank_short_name": "unionbank",
            "ussd_short_code": "826"
        },
        {
            "bank_code": "000001",
            "cbn_code": "232",
            "name": "Sterling Bank",
            "bank_short_name": "sterling",
            "ussd_short_code": "822"
        },
        {
            "bank_code": "090121",
            "cbn_code": "50383",
            "name": "HASAL MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "000013",
            "cbn_code": "058",
            "name": "GTBank",
            "bank_short_name": "gtb",
            "ussd_short_code": "737"
        },
        {
            "bank_code": "000003",
            "cbn_code": "214",
            "name": "FCMB",
            "bank_short_name": "fcmb",
            "ussd_short_code": ""
        },
        {
            "bank_code": "000014",
            "cbn_code": "063",
            "name": "Access Bank",
            "bank_short_name": "access",
            "ussd_short_code": "901"
        },
        {
            "bank_code": "100025",
            "cbn_code": "",
            "name": "Zinternet - KongaPay",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100018",
            "cbn_code": "",
            "name": "ZenithMobile",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090142",
            "cbn_code": "",
            "name": "Yes MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090124",
            "cbn_code": "",
            "name": "XSLNCE Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090120",
            "cbn_code": "",
            "name": "WETLAND MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100012",
            "cbn_code": "",
            "name": "VTNetworks",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090139",
            "cbn_code": "",
            "name": "Visa Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090150",
            "cbn_code": "",
            "name": "Virtue MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090123",
            "cbn_code": "",
            "name": "Verite Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090341",
            "cbn_code": "",
            "name": "UNIILORIN MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090193",
            "cbn_code": "",
            "name": "Unical MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090266",
            "cbn_code": "",
            "name": "Uniben Microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090461",
            "cbn_code": "",
            "name": "UNIBADAN MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090005",
            "cbn_code": "",
            "name": "Trustbond",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090146",
            "cbn_code": "",
            "name": "Trident Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100010",
            "cbn_code": "",
            "name": "TeasyMobile",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090426",
            "cbn_code": "",
            "name": "TANGERINE MONEY",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100023",
            "cbn_code": "",
            "name": "TagPay",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090446",
            "cbn_code": "",
            "name": "SUPPORT MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090302",
            "cbn_code": "",
            "name": "SUNBEAM MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090340",
            "cbn_code": "",
            "name": "STOCKCORP MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090262",
            "cbn_code": "",
            "name": "Stellas Microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "070022",
            "cbn_code": "",
            "name": "STB Mortgage Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090162",
            "cbn_code": "",
            "name": "Stanford MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090436",
            "cbn_code": "",
            "name": "Spectrum MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "120004",
            "cbn_code": "",
            "name": "SmartCash PSB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090112",
            "cbn_code": "",
            "name": "Seed Capital Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100004",
            "cbn_code": "999992",
            "name": "Paycom (Opay)",
            "bank_short_name": "paycom",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100009",
            "cbn_code": "315",
            "name": "GTMobile",
            "bank_short_name": "gtb-mobile",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090380",
            "cbn_code": "50200",
            "name": "KREDI MONEY MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "000024",
            "cbn_code": "502",
            "name": "Rand Merchant Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090175",
            "cbn_code": "125",
            "name": "Rubies MFB",
            "bank_short_name": "RMB",
            "ussd_short_code": ""
        },
        {
            "bank_code": "000008",
            "cbn_code": "076",
            "name": "POLARIS BANK",
            "bank_short_name": "PBL",
            "ussd_short_code": "966"
        },
        {
            "bank_code": "000002",
            "cbn_code": "082",
            "name": "Keystone Bank",
            "bank_short_name": "keystone",
            "ussd_short_code": "7111"
        },
        {
            "bank_code": "000006",
            "cbn_code": "301",
            "name": "JAIZ Bank",
            "bank_short_name": "jaiz",
            "ussd_short_code": ""
        },
        {
            "bank_code": "000020",
            "cbn_code": "030",
            "name": "Heritage",
            "bank_short_name": "heritage",
            "ussd_short_code": "322"
        },
        {
            "bank_code": "090140",
            "cbn_code": "",
            "name": "Sagamu Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090006",
            "cbn_code": "",
            "name": "SafeTrust",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090485",
            "cbn_code": "",
            "name": "SAFEGATE MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090286",
            "cbn_code": "",
            "name": "Safe Haven MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090138",
            "cbn_code": "",
            "name": "Royal Exchange Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090405",
            "cbn_code": "",
            "name": "Moniepoint/Rolez Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090433",
            "cbn_code": "",
            "name": "RIGO MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090132",
            "cbn_code": "",
            "name": "RICHWAY MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090198",
            "cbn_code": "",
            "name": "Renmoney microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090173",
            "cbn_code": "",
            "name": "Reliance MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090125",
            "cbn_code": "",
            "name": "REGENT MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "070011",
            "cbn_code": "",
            "name": "Refuge Mortgage Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090170",
            "cbn_code": "",
            "name": "Rahama MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090261",
            "cbn_code": "",
            "name": "QuickFund Microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090274",
            "cbn_code": "",
            "name": "Prestige Microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "070013",
            "cbn_code": "",
            "name": "PLATINUM MORTGAGE BANK",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090165",
            "cbn_code": "",
            "name": "Petra Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090135",
            "cbn_code": "",
            "name": "Personal Trust Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090196",
            "cbn_code": "",
            "name": "Pennywise Microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090137",
            "cbn_code": "",
            "name": "Pecan Trust Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090402",
            "cbn_code": "",
            "name": "PEACE MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "110001",
            "cbn_code": "",
            "name": "PayAttitude Online",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090390",
            "cbn_code": "",
            "name": "PARKWAY MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "000030",
            "cbn_code": "",
            "name": "Parallex",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100033",
            "cbn_code": "",
            "name": "PalmPay",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "070008",
            "cbn_code": "",
            "name": "PAGE FINANCIALS",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090456",
            "cbn_code": "",
            "name": "OSPOLY MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090460",
            "cbn_code": "",
            "name": "ORITABASORUN MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "070007",
            "cbn_code": "",
            "name": "Omoluabi Mortgage Bank Plc",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090272",
            "cbn_code": "",
            "name": "Olabisi Onabanjo university Microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090161",
            "cbn_code": "",
            "name": "Okpoga MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090119",
            "cbn_code": "",
            "name": "OHAFIA MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090437",
            "cbn_code": "",
            "name": "OakLand MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "070001",
            "cbn_code": "",
            "name": "NPF MicroFinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090194",
            "cbn_code": "",
            "name": "NIRSAL National microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090459",
            "cbn_code": "",
            "name": "NICE MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090205",
            "cbn_code": "",
            "name": "Newdawn Microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090108",
            "cbn_code": "",
            "name": "New Prudential Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090128",
            "cbn_code": "",
            "name": "Ndiorah MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090263",
            "cbn_code": "",
            "name": "Navy Microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090152",
            "cbn_code": "",
            "name": "Nargata MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090151",
            "cbn_code": "",
            "name": "Mutual Trust Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090190",
            "cbn_code": "",
            "name": "Mutual Benefits MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090129",
            "cbn_code": "",
            "name": "MONEYTRUST MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100020",
            "cbn_code": "",
            "name": "MoneyBox",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100011",
            "cbn_code": "",
            "name": "Mkudi",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090281",
            "cbn_code": "",
            "name": "MINT-FINEX MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090136",
            "cbn_code": "",
            "name": "Microcred Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090275",
            "cbn_code": "",
            "name": "Meridian MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090432",
            "cbn_code": "",
            "name": "Memphis MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090280",
            "cbn_code": "",
            "name": "Megapraise Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090423",
            "cbn_code": "",
            "name": "MAUTECH MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090174",
            "cbn_code": "",
            "name": "Malachy MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090171",
            "cbn_code": "",
            "name": "Mainstreet MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090265",
            "cbn_code": "",
            "name": "Lovonus Microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "000029",
            "cbn_code": "",
            "name": "LOTUS BANK",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090435",
            "cbn_code": "",
            "name": "LINKS MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090420",
            "cbn_code": "",
            "name": "LETSHEGO MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "070012",
            "cbn_code": "",
            "name": "LBIC Mortgage Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090271",
            "cbn_code": "",
            "name": "Lavender Microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090177",
            "cbn_code": "",
            "name": "Lapo MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090422",
            "cbn_code": "",
            "name": "LANDGOLD MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090155",
            "cbn_code": "",
            "name": "La Fayette Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090450",
            "cbn_code": "",
            "name": "KWASU MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100015",
            "cbn_code": "",
            "name": "Kegow",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090191",
            "cbn_code": "",
            "name": "KCMB MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090003",
            "cbn_code": "",
            "name": "JubileeLife",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090421",
            "cbn_code": "",
            "name": "Izon Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090149",
            "cbn_code": "",
            "name": "IRL Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100027",
            "cbn_code": "",
            "name": "Intellifin",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090434",
            "cbn_code": "",
            "name": "Insight MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100029",
            "cbn_code": "",
            "name": "Innovectives Kesh",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "070016",
            "cbn_code": "",
            "name": "Infinity trust  Mortgage Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090157",
            "cbn_code": "",
            "name": "Infinity MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100024",
            "cbn_code": "",
            "name": "Imperial Homes Mortgage Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090258",
            "cbn_code": "",
            "name": "Imo Microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090350",
            "cbn_code": "",
            "name": "ILORIN MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090279",
            "cbn_code": "",
            "name": "Ikire MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090118",
            "cbn_code": "",
            "name": "IBILE Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "120002",
            "cbn_code": "",
            "name": "Hope PSB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090175",
            "cbn_code": "",
            "name": "HighStreet MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090418",
            "cbn_code": "",
            "name": "HIGHLAND MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100017",
            "cbn_code": "",
            "name": "Hedonmark",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "070017",
            "cbn_code": "",
            "name": "Haggai Mortgage Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090147",
            "cbn_code": "",
            "name": "Hackman Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090195",
            "cbn_code": "",
            "name": "Grooming Microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100008",
            "cbn_code": "307",
            "name": "Ecobank Xpress Account",
            "bank_short_name": "ecobank-mobile",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100013",
            "cbn_code": "323",
            "name": "AccessMobile",
            "bank_short_name": "access-mobile",
            "ussd_short_code": ""
        },
        {
            "bank_code": "400001",
            "cbn_code": "501",
            "name": "FSDH",
            "bank_short_name": "fsdh",
            "ussd_short_code": ""
        },
        {
            "bank_code": "060001",
            "cbn_code": "902",
            "name": "Coronation",
            "bank_short_name": "cmfb",
            "ussd_short_code": ""
        },
        {
            "bank_code": "000009",
            "cbn_code": "023",
            "name": "Citi Bank",
            "bank_short_name": "citibankng",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090154",
            "cbn_code": "50823",
            "name": "CEMCS MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090097",
            "cbn_code": "562",
            "name": "Ekondo MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "060002",
            "cbn_code": "911",
            "name": "FBNQuest MERCHANT BANK",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "000016",
            "cbn_code": "011",
            "name": "First Bank",
            "bank_short_name": "firstbank",
            "ussd_short_code": "894"
        },
        {
            "bank_code": "000007",
            "cbn_code": "070",
            "name": "Fidelity Bank",
            "bank_short_name": "fidelity",
            "ussd_short_code": "770"
        },
        {
            "bank_code": "000010",
            "cbn_code": "050",
            "name": "ECOBANK",
            "bank_short_name": "ecobank",
            "ussd_short_code": "326"
        },
        {
            "bank_code": "060004",
            "cbn_code": "",
            "name": "GREENWICH Merchant Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090269",
            "cbn_code": "",
            "name": "Greenville Microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090178",
            "cbn_code": "",
            "name": "GreenBank MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090122",
            "cbn_code": "",
            "name": "GOWANS MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100022",
            "cbn_code": "",
            "name": "GoMoney",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090278",
            "cbn_code": "",
            "name": "Glory MFB ",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090441",
            "cbn_code": "",
            "name": "GIWA MICROFINANCE BANK",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090411",
            "cbn_code": "",
            "name": "GIGINYA MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "070009",
            "cbn_code": "",
            "name": "GATEWAY MORTGAGE BANK",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090168",
            "cbn_code": "",
            "name": "Gashua Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090158",
            "cbn_code": "",
            "name": "FUTO MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090438",
            "cbn_code": "",
            "name": "FUTMINNA MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090145",
            "cbn_code": "",
            "name": "Full range MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100016",
            "cbn_code": "",
            "name": "FortisMobile",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "070002",
            "cbn_code": "",
            "name": "Fortis Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090164",
            "cbn_code": "",
            "name": "First Royal Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090163",
            "cbn_code": "",
            "name": "First Multiple MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "070014",
            "cbn_code": "",
            "name": "First Generation Mortgage Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090111",
            "cbn_code": "",
            "name": "FinaTrust Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090126",
            "cbn_code": "",
            "name": "FidFund MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100019",
            "cbn_code": "",
            "name": "Fidelity Mobile",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090153",
            "cbn_code": "",
            "name": "FFS Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100001",
            "cbn_code": "",
            "name": "FET",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100031",
            "cbn_code": "",
            "name": "FCMB Easy Account",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100014",
            "cbn_code": "",
            "name": "FBNMobile",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090107",
            "cbn_code": "",
            "name": "FBN Morgages Limited",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090179",
            "cbn_code": "",
            "name": "FAST MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090330",
            "cbn_code": "",
            "name": "Fame MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090328",
            "cbn_code": "",
            "name": "Eyowo",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100006",
            "cbn_code": "",
            "name": "eTranzact",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090166",
            "cbn_code": "",
            "name": "Eso-E Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090189",
            "cbn_code": "",
            "name": "Esan MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "000019",
            "cbn_code": "",
            "name": "Enterprise Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090114",
            "cbn_code": "",
            "name": "EmpireTrust Microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090273",
            "cbn_code": "",
            "name": "Emeralds MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100021",
            "cbn_code": "",
            "name": "Eartholeum",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090156",
            "cbn_code": "",
            "name": "e-BARCs MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090167",
            "cbn_code": "",
            "name": "Daylight Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090414",
            "cbn_code": "",
            "name": "CRUTECH MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090429",
            "cbn_code": "",
            "name": "CROSS RIVER MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090159",
            "cbn_code": "",
            "name": "Credit Afrique MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "070006",
            "cbn_code": "",
            "name": "Covenant MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "070021",
            "cbn_code": "",
            "name": "COOP Mortgage Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100032",
            "cbn_code": "",
            "name": "Contec Global",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090130",
            "cbn_code": "",
            "name": "CONSUMER  MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090144",
            "cbn_code": "",
            "name": "CIT Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090141",
            "cbn_code": "",
            "name": "Chikum Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090416",
            "cbn_code": "",
            "name": "CHIBUEZE MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090440",
            "cbn_code": "",
            "name": "CHERISH MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090470",
            "cbn_code": "",
            "name": "Changan RTS MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090397",
            "cbn_code": "",
            "name": "Chanelle MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100005",
            "cbn_code": "",
            "name": "Cellulant",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090445",
            "cbn_code": "",
            "name": "CAPSTONE MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090363",
            "cbn_code": "",
            "name": "Bridgeway MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090293",
            "cbn_code": "",
            "name": "Brethren MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "070015",
            "cbn_code": "",
            "name": "Brent Mortgage Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090148",
            "cbn_code": "",
            "name": "Bowen MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090176",
            "cbn_code": "",
            "name": "Bosak MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090319",
            "cbn_code": "",
            "name": "BONGHE MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090117",
            "cbn_code": "",
            "name": "Boctrust Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090431",
            "cbn_code": "",
            "name": "BLUEWHALES MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090127",
            "cbn_code": "",
            "name": "BC Kash MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090425",
            "cbn_code": "",
            "name": "Banex MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090188",
            "cbn_code": "",
            "name": "Baines Credit MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090264",
            "cbn_code": "",
            "name": "Auchi Microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090172",
            "cbn_code": "",
            "name": "Astrapolis MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090282",
            "cbn_code": "",
            "name": "Arise MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090143",
            "cbn_code": "",
            "name": "APEKS Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090116",
            "cbn_code": "",
            "name": "AMML MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090180",
            "cbn_code": "",
            "name": "Amju MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090169",
            "cbn_code": "",
            "name": "Alphakapital MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090131",
            "cbn_code": "",
            "name": "ALLWORKERS MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090277",
            "cbn_code": "",
            "name": "Alhayat MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090259",
            "cbn_code": "",
            "name": "Alekun Microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090133",
            "cbn_code": "",
            "name": "AL-BARKAH MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100028",
            "cbn_code": "",
            "name": "AG MORTGAGE BANK PLC",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090268",
            "cbn_code": "",
            "name": "Adeyemi College Staff Microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090160",
            "cbn_code": "",
            "name": "Addosser MFBB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090134",
            "cbn_code": "",
            "name": "ACCION MFB",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "100052",
            "cbn_code": "",
            "name": "Access Yelo & Beta",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "000005",
            "cbn_code": "",
            "name": "Access Bank (Diamond)",
            "bank_short_name": "",
            "ussd_short_code": "901"
        },
        {
            "bank_code": "090424",
            "cbn_code": "",
            "name": "Abucoop Microfinance Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090197",
            "cbn_code": "",
            "name": "ABU Microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090260",
            "cbn_code": "",
            "name": "Above Only Microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "070010",
            "cbn_code": "",
            "name": "ABBEY MORTGAGE BANK",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "090270",
            "cbn_code": "",
            "name": "AB Microfinance bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "120001",
            "cbn_code": "",
            "name": "9 Payment Service Bank",
            "bank_short_name": "",
            "ussd_short_code": ""
        },
        {
            "bank_code": "000027",
            "cbn_code": "00103",
            "name": "GLOBUS Bank",
            "bank_short_name": None,
            "ussd_short_code": "989"
        }
    ]