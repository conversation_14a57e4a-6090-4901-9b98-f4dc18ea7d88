import json
from datetime import date
from typing import Any, Dict

import redis
import requests
from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.core.cache import cache
from django.utils import timezone
from django.utils.timezone import timedelta
from rest_framework import serializers

from accounts.models import CustomUser, AgencyBankingToken
from accounts.model_choices import AgencyUserTypes

from ajo.models import AgencyDumps, AjoUser, RotationGroup
from payment.model_choices import PlanType, Status, TransactionFormType, WalletTypes
from payment.models import RequestDump, WalletSystem
from payment.services import TransactionService
from payment.utils import convert_string_to_aware_datetime_object

logger = settings.LOGGER

# my_access_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************.Ll8P2k4CTVYHH0hFZaeeqIanNroPnKQWR3XS5mHwhHM"


# #expected responses
# {
#     "status": "success",
#     "data": {
#         "id": 95,
#         "email": "<EMAIL>",
#         "password": "pbkdf2_sha256$320000$diB6QQyeUG7ixfyPWmqGba$YF+"
#     }
# }
# {
#     "status": "error",
#     "message": "user with ID does not exist"
# }


def agent_login(token_not_valid: bool = None, retry_count: int = 5):
    ## ---------- REDIS STORAGE ------------ ##
    # redis_db = redis.StrictRedis(
    #     host="localhost",
    #     port="6379",
    #     db=0,
    #     decode_responses=True,
    #     encoding="utf-8",
    # )

    # token_not_valid = True

    # if token_not_valid:
    #     redis_db.delete("agent_login_token")

    # redis_db.delete("agent_login_token")

    # agency_user_token = redis_db.get("agent_login_token")

    agency_user_token = AgencyBankingToken.retrieve_token(
        token_owner=AgencyUserTypes.SAVINGS
    )

    if agency_user_token.get("status") is True:
        return agency_user_token

    if retry_count > 0:
        # logger.info("SAVINGS USER IS LOGGING IN::::::::::::::::")
        email = settings.AGENCY_BANKING_USEREMAIL
        password = settings.AGENCY_BANKING_PASSWORD

        url = f"{settings.AGENCY_BANKING_BASE_URL}/user/login/create/"

        payload = {
            "email": email,
            "password": password,
            "device_type": "MOBILE",
        }

        headers = {
            "Content-Type": "application/json",
        }

        try:
            response = requests.post(url, json=payload, headers=headers)
            res = response.json()
            token = res["access"]

            AgencyBankingToken.create_token(
                token=token, token_owner=AgencyUserTypes.SAVINGS
            )

            return res

        except:
            return agent_login(token_not_valid, retry_count - 1)

    else:

        # res = {"access": agency_user_token}

        return agency_user_token


# def agent_login(token_not_valid: bool = None, retry_count: int = 5):
#     ## ---------- REDIS STORAGE ------------ ##
#     redis_db = redis.StrictRedis(
#         host="localhost",
#         port="6379",
#         db=0,
#         decode_responses=True,
#         encoding="utf-8",
#     )

#     if token_not_valid:
#         redis_db.delete("agent_login_token")

#     agency_user_token = redis_db.get("agent_login_token")

#     if agency_user_token is None and retry_count > 0:
#         email = settings.AGENCY_BANKING_USEREMAIL
#         password = settings.AGENCY_BANKING_PASSWORD

#         url = f"{settings.AGENCY_BANKING_BASE_URL}/user/login/create/"

#         payload = {
#             "email": email,
#             "password": password,
#             "device_type": "MOBILE",
#         }

#         headers = {
#             "Content-Type": "application/json",
#         }

#         try:
#             response = requests.post(url, json=payload, headers=headers)
#             res = response.json()
#             token = res["access"]
#             redis_db.set("agent_login_token", token, ex=timedelta(days=3))

#             # print("-------------------------------------------------------")
#             # print("-------------------------------------------------------")
#             # print("I am res from try:", res)
#             # print("-------------------------------------------------------")
#             # print("-------------------------------------------------------")

#             return res

#         except:
#             return agent_login(token_not_valid, retry_count - 1)

#     else:
#         # print(
#         #     f"""
#         #     agency_user_token
#         #     FROM REDIS AGENT LOGIN TOKEN: {agency_user_token}
#         #     type: {type(agency_user_token)}

#         # """
#         # )
#         res = {"access": agency_user_token}

#         # print("-------------------------------------------------------")
#         # print("-------------------------------------------------------")
#         # print("I am res from else:", res)
#         # print("-------------------------------------------------------")
#         # print("-------------------------------------------------------")

#         # print("got token from redis", res, "------------------", "\n\n\n\n")
#         return res


def loan_agent_login(token_not_valid: bool = None):
    ## ---------- REDIS STORAGE ------------ ##
    # redis_db = redis.StrictRedis(
    #     host="localhost",
    #     port="6379",
    #     db=0,
    #     decode_responses=True,
    #     encoding="utf-8",
    # )

    # if token_not_valid:
    #     redis_db.delete("loan_agent_token")

    # loan_agent_token = redis_db.get("loan_agent_token")
    loan_agent_token = None

    if loan_agent_token is None:
        email = settings.LOAN_AGENCY_BANKING_USEREMAIL
        password = settings.LOAN_AGENCY_BANKING_PASSWORD

        url = f"{settings.AGENCY_BANKING_BASE_URL}/user/login/create/"

        payload = {
            "email": email,
            "password": password,
            "device_type": "MOBILE",
        }

        headers = {
            "Content-Type": "application/json",
        }

        try:
            response = requests.post(url, json=payload, headers=headers)
            res = response.json()
            token = res["access"]
            # redis_db.set("loan_agent_token", token, ex=timedelta(days=3))

            # print("-------------------------------------------------------")
            # print("-------------------------------------------------------")
            # print("I am res from try:", res)
            # print("-------------------------------------------------------")
            # print("-------------------------------------------------------")

            return res

        except:
            return loan_agent_login()

    else:
        # print(
        #     f"""
        #     agency_user_token
        #     FROM REDIS AGENT LOGIN TOKEN: {agency_user_token}
        #     type: {type(agency_user_token)}

        # """
        # )
        res = {"access": loan_agent_token}

        # print("-------------------------------------------------------")
        # print("-------------------------------------------------------")
        # print("I am res from else:", res)
        # print("-------------------------------------------------------")
        # print("-------------------------------------------------------")

        # print("got token from redis", res, "------------------", "\n\n\n\n")
        return res


def repayment_agent_login(token_not_valid: bool = None):
    ## ---------- REDIS STORAGE ------------ ##
    redis_db = redis.StrictRedis(
        host="localhost",
        port="6379",
        db=0,
        decode_responses=True,
        encoding="utf-8",
    )

    if token_not_valid:
        redis_db.delete("repay_agent_token")

    repay_agent_token = redis_db.get("repay_agent_token")
    repay_agent_token = None
    if repay_agent_token is None:
        email = settings.LOAN_REPAYMENT_USER_EMAIL
        password = settings.LOAN_REPAYMENT_USER_PASSWORD

        url = f"{settings.AGENCY_BANKING_BASE_URL}/user/login/create/"

        payload = {
            "email": email,
            "password": password,
            "device_type": "MOBILE",
        }

        headers = {
            "Content-Type": "application/json",
        }

        try:
            response = requests.post(url, json=payload, headers=headers)
            res = response.json()
            token = res["access"]
            redis_db.set("loan_agent_token", token, ex=timedelta(days=3))

            # print("-------------------------------------------------------")
            # print("-------------------------------------------------------")
            # print("I am res from try:", res)
            # print("-------------------------------------------------------")
            # print("-------------------------------------------------------")

            return res

        except:
            return repayment_agent_login()

    else:
        # print(
        #     f"""
        #     agency_user_token
        #     FROM REDIS AGENT LOGIN TOKEN: {agency_user_token}
        #     type: {type(agency_user_token)}

        # """
        # )
        res = {"access": repay_agent_token}

        # print("-------------------------------------------------------")
        # print("-------------------------------------------------------")
        # print("I am res from else:", res)
        # print("-------------------------------------------------------")
        # print("-------------------------------------------------------")

        # print("got token from redis", res, "------------------", "\n\n\n\n")
        return res


def escrow_agent_login(token_not_valid: bool = None):
    ## ---------- REDIS STORAGE ------------ ##
    redis_db = redis.StrictRedis(
        host="localhost",
        port="6379",
        db=0,
        decode_responses=True,
        encoding="utf-8",
    )

    if token_not_valid:
        redis_db.delete("escrow_agent_token")

    escrow_agent_token = redis_db.get("escrow_agent_token")

    if escrow_agent_token is None:
        email = settings.ESCROW_USER_EMAIL
        password = settings.ESCROW_USER_PASSWORD

        url = f"{settings.AGENCY_BANKING_BASE_URL}/user/login/create/"

        payload = {
            "email": email,
            "password": password,
            "device_type": "MOBILE",
        }

        headers = {
            "Content-Type": "application/json",
        }

        try:
            response = requests.post(url, json=payload, headers=headers)
            res = response.json()
            token = res["access"]
            redis_db.set("loan_agent_token", token, ex=timedelta(days=3))

            # print("-------------------------------------------------------")
            # print("-------------------------------------------------------")
            # print("I am res from try:", res)
            # print("-------------------------------------------------------")
            # print("-------------------------------------------------------")

            return res

        except:
            return escrow_agent_login()

    else:
        # print(
        #     f"""
        #     agency_user_token
        #     FROM REDIS AGENT LOGIN TOKEN: {agency_user_token}
        #     type: {type(agency_user_token)}

        # """
        # )
        res = {"access": escrow_agent_token}

        # print("-------------------------------------------------------")
        # print("-------------------------------------------------------")
        # print("I am res from else:", res)
        # print("-------------------------------------------------------")
        # print("-------------------------------------------------------")

        # print("got token from redis", res, "------------------", "\n\n\n\n")
        return res


def commissions_agent_login(token_not_valid: bool = None):
    ## ---------- REDIS STORAGE ------------ ##
    redis_db = redis.StrictRedis(
        host="localhost",
        port="6379",
        db=0,
        decode_responses=True,
        encoding="utf-8",
    )

    if token_not_valid:
        redis_db.delete("commissions_agent_token")

    commissions_agent_token = redis_db.get("commissions_agent_token")

    if commissions_agent_token is None:
        email = settings.LOAN_COMMISSIONS_USER_EMAIL
        password = settings.LOAN_COMMISSIONS_USER_PASSWORD

        url = f"{settings.AGENCY_BANKING_BASE_URL}/user/login/create/"

        payload = {
            "email": email,
            "password": password,
            "device_type": "MOBILE",
        }

        headers = {
            "Content-Type": "application/json",
        }

        try:
            response = requests.post(url, json=payload, headers=headers)
            res = response.json()
            token = res["access"]
            redis_db.set("loan_agent_token", token, ex=timedelta(days=3))

            # print("-------------------------------------------------------")
            # print("-------------------------------------------------------")
            # print("I am res from try:", res)
            # print("-------------------------------------------------------")
            # print("-------------------------------------------------------")

            return res

        except:
            return commissions_agent_login()

    else:
        # print(
        #     f"""
        #     agency_user_token
        #     FROM REDIS AGENT LOGIN TOKEN: {agency_user_token}
        #     type: {type(agency_user_token)}

        # """
        # )
        res = {"access": commissions_agent_token}


def bnpl_agent_login(token_not_valid: bool = None):
    ## ---------- REDIS STORAGE ------------ ##
    redis_db = redis.StrictRedis(
        host="localhost",
        port="6379",
        db=0,
        decode_responses=True,
        encoding="utf-8",
    )

    if token_not_valid:
        redis_db.delete("bnpl_agent_token")

    bnpl_agent_token = redis_db.get("bnpl_agent_token")

    if bnpl_agent_token is None:
        email = settings.BNPL_USER_EMAIL
        password = settings.BNPL_USER_PASS

        url = f"{settings.AGENCY_BANKING_BASE_URL}/user/login/create/"

        payload = {
            "email": email,
            "password": password,
            "device_type": "MOBILE",
        }

        headers = {
            "Content-Type": "application/json",
        }

        try:
            response = requests.post(url, json=payload, headers=headers)
            res = response.json()
            token = res["access"]
            redis_db.set("loan_agent_token", token, ex=timedelta(days=3))

            return res

        except:
            return bnpl_agent_login()

    else:
        res = {"access": bnpl_agent_token}


def get_agency_banking_super_token(token_not_valid: bool = None) -> str:
    """
    Returns the super token for auto debit of the savings user

    Responses:
        response = {
            "error": "890",
            "message": "Incorrect Service User on Agency Banking List or Cannot Generate Super Token"
        }



        response = {
                        "error": "891",
                        "message": "IP not whitelisted"
                    }


        response = {
                    "status": "success",
                    "super_token": "dcfjdscfdsfffwefewfewfaefaesfraeferagfergergergergreagdfraervdscsdcmnjDSJOCADKBVCKJDSHCOIDSHIEHVIUDSGCYHBWJDBGASYCGXUAIHDKjxhUHkjhuaguGHSKJCDJVIDHJVKIHVUDGHUJV"
                }
    """

    redis_db = redis.StrictRedis(
        host="localhost",
        port="6379",
        db=0,
        decode_responses=True,
        encoding="utf-8",
    )

    if token_not_valid:
        redis_db.delete("agency_banking_super_token")

    agency_super_token = redis_db.get("agency_banking_super_token")

    if agency_super_token is None:
        url = f"{settings.AGENCY_BANKING_BASE_URL}/agency/generate_super_token/"

        auth_token = agent_login().get("access")

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {auth_token}",
        }

        try:
            response = requests.get(url, headers=headers)
            res = response.json()

            if res.get("status") == "success":
                redis_db.set("agency_banking_super_token", res.get("super_token"))
                redis_db.expire(
                    "agency_banking_super_token", seconds_until_end_of_day()
                )
                return res.get("super_token")

            else:
                return None

        except:
            return get_agency_banking_super_token()
    else:
        return agency_super_token


def seconds_until_end_of_day() -> int:
    """
    Returns the number of seconds unitl the end of the current day
    """
    now = timezone.localtime()
    end_of_day = timezone.make_aware(
        timezone.datetime.combine(now.date(), timezone.datetime.max.time())
    )
    time_until_end_of_day = end_of_day - now
    return int(time_until_end_of_day.total_seconds())


def disbursement_account_login(token_not_valid: bool = None, retry_count: int = 5):
    ## ---------- REDIS STORAGE ------------ ##
    redis_db = redis.StrictRedis(
        host="localhost",
        port="6379",
        db=0,
        decode_responses=True,
        encoding="utf-8",
    )

    if token_not_valid:
        redis_db.delete("disbursement_login_token")

    agency_user_token = redis_db.get("disbursement_login_token")

    if agency_user_token is None and retry_count > 0:
        email = settings.AGENCY_BANKING_DISBURSEMENT_USEREMAIL
        password = settings.AGENCY_BANKING_DISBURSEMENT_PASSWORD

        url = f"{settings.AGENCY_BANKING_BASE_URL}/user/login/create/"

        payload = {
            "email": email,
            "password": password,
            "device_type": "MOBILE",
        }

        headers = {
            "Content-Type": "application/json",
        }

        try:
            response = requests.post(url, json=payload, headers=headers)
            res = response.json()
            token = res["access"]
            redis_db.set("disbursement_login_token", token, ex=timedelta(days=3))

            return res

        except:
            return disbursement_account_login(token_not_valid, retry_count - 1)

    else:
        res = {"access": agency_user_token}
        return res


def liberty_life_agent_login(token_not_valid: bool = None):
    ## ---------- REDIS STORAGE ------------ ##
    redis_db = redis.StrictRedis(
        host="localhost",
        port="6379",
        db=0,
        decode_responses=True,
        encoding="utf-8",
    )

    if token_not_valid:
        redis_db.delete("liberty_life_agent_token")

    liberty_life_agent_token = redis_db.get("liberty_life_agent_token")

    if liberty_life_agent_token is None:
        email = settings.LIBERTY_LIFE_AGENCY_USER_EMAIL
        password = settings.LIBERTY_LIFE_AGENCY_USER_PASSWORD

        url = f"{settings.AGENCY_BANKING_BASE_URL}/user/login/create/"

        payload = {
            "email": email,
            "password": password,
            "device_type": "MOBILE",
        }

        headers = {
            "Content-Type": "application/json",
        }

        try:
            response = requests.post(url, json=payload, headers=headers)
            res = response.json()
            token = res["access"]
            redis_db.set("liberty_life_agent_token", token, ex=timedelta(days=3))
            return res
        except:
            return liberty_life_agent_login()
    else:
        res = {"access": liberty_life_agent_token}
        return res


def paybox_login(token_not_valid: bool = None):
    ## ---------- REDIS STORAGE ------------ ##
    redis_db = redis.StrictRedis(
        host="localhost",
        port="6379",
        db=0,
        decode_responses=True,
        encoding="utf-8",
    )

    if token_not_valid:
        redis_db.delete("paybox_token")

    paybox_token = redis_db.get("paybox_token")

    if paybox_token is None:
        email = settings.PAYBOX_USER_EMAIL
        password = settings.PAYBOX_USER_PASSWORD

        url = f"{settings.AGENCY_BANKING_BASE_URL}/user/login/create/"

        payload = {
            "email": email,
            "password": password,
            "device_type": "MOBILE",
        }

        headers = {
            "Content-Type": "application/json",
        }

        try:
            response = requests.post(url, json=payload, headers=headers)
            res = response.json()
            token = res["access"]
            redis_db.set("paybox_token", token, ex=timedelta(days=3))
            return res
        except:
            return paybox_login()
    else:
        res = {"access": paybox_token}
        return res


class AgencyBankingClass:
    base_url = settings.AGENCY_BANKING_BASE_URL
    auth_token = settings.AGENCY_AUTH_TOKEN
    agency_banking_transaction_pin = settings.AGENCY_BANKING_TRANSACTION_PIN
    agency_banking_loan_transaction_pin = settings.LOAN_AGENCY_BANKING_TRANSACTION_PIN
    email = settings.AGENCY_BANKING_USEREMAIL
    password = settings.AGENCY_BANKING_PASSWORD
    loan_agency_email = settings.LOAN_AGENCY_BANKING_USEREMAIL
    loan_agency_password = settings.LOAN_AGENCY_BANKING_PASSWORD

    @classmethod
    def handle_api_response(cls, payload, response, url, others=None):
        """
        Handles the response from API requests, returning a structured result.

        Args:
            payload (dict): The data sent with the request.
            response (requests.Response): The response object returned by the API.
            url (str): The URL that was called.

        Returns:
            dict: A dictionary containing the request result, including status, response,
                  and other related information.
        """
        try:
            response = {
                "url": url,
                "status_code": response.status_code,
                "method": response.request.method,
                "status": "success",
                "response": response.json(),
                "payload": payload,
                "others": others,
            }
        except (
            requests.exceptions.RequestException,
            Exception,
            json.decoder.JSONDecodeError,
        ) as e:
            response = {
                "url": url,
                "error": str(e),
                "method": response.request.method,
                "status_code": response.status_code,
                "status": "failed",
                "response": response.text,
                "0thers": others,
            }
        return response

    @classmethod
    def get_token(cls, email, password, cache_key_pfrefix="Dis-Com"):
        """
        Retrieves an authentication token from LIBERTY PAY.
        """
        cache_key = f"{cache_key_pfrefix}{password}"
        cache_token = cache.get(key=cache_key)
        if cache_token:
            return cache_token

        url = f"{cls.base_url}/user/login/create/"

        payload = {
            "email": email,
            "password": password,
            "device_type": "MOBILE",
        }

        headers = {
            "Content-Type": "application/json",
        }

        response = requests.post(url, json=payload, headers=headers)

        response_result = cls.handle_api_response(
            payload=payload, response=response, url=url
        )
        status_code = response_result.get("status_code")
        if status_code == 200:
            token = response_result.get("response").get("access")
            cache.set(key=cache_key, value=token, timeout=60 * 30)  # last 30 minutes
            return token
        else:
            return response_result

    @classmethod
    def get_all_user_information(cls, access_token: str) -> dict:
        """
        Obtains all the information about the user on the agency banking server

        Args:
            access_token (str): Accepts the access token of the user

        Returns:
            dict: A dictionary containing all the information of the user
        """
        headers = {"Authorization": f"Bearer {access_token}"}
        url = f"{cls.base_url}/agency/user/get_user_details/"

        try:
            r = requests.get(
                url=url,
                headers=headers,
                timeout=10,
            )

            response = r.json()

            if r.status_code != 200:
                main_response = {
                    "status": False,
                    "message": response,
                }

            else:
                main_response = {
                    "status": True,
                    **response,
                }

            return main_response

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "session expired, try again or replace the token",
            }

    @classmethod
    def get_current_user_details(cls, user_id: int) -> dict:
        """
        Takes the user_id and the access token to call the agency banking savings url to query for and return the
        hashed user password, email and user_id.
        """
        authorization_header = {"Authorization": f"Bearer {cls.auth_token}"}
        url = f"{cls.base_url}/agency/savings_get_user_pass/"
        params = {"user_id": user_id}

        try:
            r = requests.get(
                url=url,
                headers=authorization_header,
                params=params,
            )

            resp = r.json()

            if r.status_code == 401:
                new_resp = {"status": False, "message": resp}

            else:
                new_resp = resp

            return new_resp

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "session expired, try again",
            }

    @classmethod
    def verify_user_pin(cls, transaction_pin: str, access_token: str) -> dict:
        """
        Takes the user_id and verifies if the pin is correct from agency banking.
        """
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }
        url = f"{cls.base_url}/agency/user/verify_transaction_pin/"
        data = json.dumps({"transaction_pin": transaction_pin})

        try:
            r = requests.post(
                url=url,
                headers=headers,
                data=data,
            )

            resp = r.json()

            if r.status_code != 200:
                new_resp = {"status": False, "message": resp}

            else:
                new_resp = {"status": True, "message": resp}

            return new_resp

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "session expired, try again",
            }

    @classmethod
    def charge_user_wallet(
        cls,
        user,
        plan_type: str,
        transaction_pin: str,
        amount: float,
        transaction_form_type: TransactionFormType = TransactionFormType.WALLET_DEPOSIT,
        quotation_id: str | None = None,
        ajo_user: AjoUser | None = None,
        access_token: str | None = None,
        super_token: str | None = None,
        rotation_group_instance: RotationGroup | None = None,
        retry_count=0,
    ) -> dict:
        """
        Takes the user id, transaction pin, amount and access token to charge the wallet
        Returns a status, message, and/or transaction object
        NB: the status being True does not mean that the charging was successful
        """
        if plan_type.upper() not in PlanType:
            raise serializers.ValidationError(
                detail="invalid plan type while charging wallet"
            )

        if super_token is not None:
            headers = {
                "Super-Token": super_token,
                "Authorization": f"Token {cls.auth_token}",
                "Content-Type": "application/json",
            }
        else:
            headers = {
                "Authorization": f"Token {cls.auth_token}",
                "Content-Type": "application/json",
            }
        url = f"{cls.base_url}/send/other_services_charge/"

        if rotation_group_instance:
            # create rotation group deposit transaction
            transaction = TransactionService.create_rotation_group_deposit_transaction(
                user=user,
                amount=amount,
                rotation_group_id=rotation_group_instance.group_id,
                transaction_form_type=transaction_form_type,
            )
        else:
            transaction = TransactionService.create_deposit_by_wallet_transaction(
                user=user,
                amount=amount,
                wallet_type=getattr(WalletTypes, plan_type.upper()),
                quotation_id=quotation_id,
                plan_type=getattr(PlanType, plan_type.upper()),
                transaction_form_type=transaction_form_type,
                ajo_user=ajo_user,
            )

        data = json.dumps(
            {
                "user_id": int(user.customer_user_id),
                "service_name": "SAVINGS",
                "narration": "SAVINGS",
                "total_amount": amount,
                "service_comm": amount,
                "agent_comm": 0,
                "unique_reference": str(transaction.transaction_id),
                "transaction_pin": transaction_pin,
            }
        )

        try:
            r = requests.post(
                url=url,
                headers=headers,
                data=data,
            )

            response = r.json()

            # if the token is not valid
            # {"error": "589", "message": "Invalid Super-Token"}
            # make sure the recursion does not happen more than once
            # THIS SMALL PROCESS ENSURES THAT THIS RECURSION DOES NOT OCCUR MORE THAN TWICE
            # DECLARE VARIABLES

            if response.get("message") == "Invalid Super-Token":
                # if a super token was utilized in charging
                transaction.failure_reason = response.get("message")
                transaction.status = Status.FAILED
                transaction.payload = json.dumps(response)
                transaction.save()

                if retry_count > 2:
                    return

                # increase the count by 1
                retry_count += 1

                if rotation_group_instance:
                    return cls.charge_user_wallet(
                        user=user,
                        plan_type=plan_type,
                        amount=amount,
                        transaction_pin=transaction_pin,
                        rotation_group_instance=rotation_group_instance,
                        super_token=get_agency_banking_super_token(
                            token_not_valid=True
                        ),
                        retry_count=retry_count,
                    )
                else:
                    return cls.charge_user_wallet(
                        user=user,
                        plan_type=plan_type,
                        quotation_id=quotation_id,
                        transaction_pin=transaction_pin,
                        amount=amount,
                        transaction_form_type=transaction_form_type,
                        super_token=get_agency_banking_super_token(
                            token_not_valid=True
                        ),
                        retry_count=retry_count,
                    )

            if r.status_code != 202:
                response_message = ""
                if "error" in response.keys():
                    if "sufficient" in response.get("message"):
                        # return the insufficient balance

                        response_message = (
                            "insufficient balance, please fund your account"
                        )

                    elif "KYC" in response.get("message").upper():
                        # return the message
                        response_message = response.get("message")

                    else:
                        ##### Wrong user id passed
                        ###and
                        ###service and agent comm don't equal to total comm
                        ###response
                        response_message = f"Error Occurred: {response.get('message')}"

                # set the transaction status to failed and log the transaction failure reason,
                # response data and save all these
                transaction.failure_reason = response.get("message")
                transaction.status = Status.FAILED
                transaction.payload = json.dumps(response)
                transaction.save()

                # create a response
                return {
                    "status": False,
                    "transaction": transaction,
                    "message": response_message,
                }

            else:
                if "message" in response.keys():
                    if response.get("message") == "success":
                        # Sample response
                        # "message": "success",
                        # "data": {
                        #     "message": "Transaction completed successfully",
                        #     "amount_sent": 1040.57,
                        #     "escrow_id": "06c6f716-e800-46b8-bf6c-d57a165bd113"
                        # },
                        # "date_completed": "2023-02-22T18:11:24.908519"

                        ##Update the information
                        # change the status
                        transaction.status = Status.SUCCESS
                        # Collect the escrow_id and save to the transaction table
                        transaction.transaction_date_completed = (
                            convert_string_to_aware_datetime_object(
                                response.get("date_completed")
                            )
                        )
                        transaction.unique_reference = response.get("data").get(
                            "escrow_id"
                        )
                        transaction.payload = json.dumps(response)
                        transaction.save()

                        ## Create a DebitCredit Instance with the WalletSystem methods

                        # Get the wallet
                        if rotation_group_instance:
                            wallet = WalletSystem.get_or_create_wallet(
                                user=rotation_group_instance.user,
                                wallet_type=WalletTypes.ROTATIONGROUP,
                                rotation_group_id=rotation_group_instance.group_id,
                            )
                        else:
                            wallet = WalletSystem.get_or_create_wallet(
                                user=user,
                                wallet_type=getattr(WalletTypes, plan_type.upper()),
                                ajo_user=ajo_user,
                            )
                        # wallet = WalletSystem.objects.get_or_create(
                        #     user=user,
                        #     wallet_type=getattr(WalletTypes, plan_type.upper()),
                        # )
                        # use the method, returns a dictionary
                        # #{
                        #     "debit_credit_record_id": debit_credit_record.id,
                        #     "balance_before": balance_before,
                        #     "balance_after": balance_after,
                        # }
                        fund = WalletSystem.fund_balance(
                            wallet=wallet,
                            amount=amount,
                            transaction_instance=transaction,
                        )

                        new_resp = {
                            "status": True,
                            "transaction": transaction,
                            "debit_credit_info": fund,
                            "message": response,
                        }
                        return new_resp

            # Base condition if any of the conditions don't go through?
            # the idea of this is that we will check with another endpoint for the transaction_id/unique_reference
            # and the service_name on Agency backend with a background worker
            # to see if the transaction went through
            return {
                "status": True,
                "transaction": transaction,
                "message": response,
            }

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "session expired, try again",
                "error": str(err),
            }

    @classmethod
    def get_user_cards(
        cls,
        access_token: str | None,
    ) -> dict:
        """
        Takes the access token to retrieve all the cards that a user has
        """
        authorization_header = {"Authorization": f"Bearer {access_token}"}
        url = f"{cls.base_url}/accounts/get_credit_cards/"

        try:
            r = requests.get(
                url=url,
                headers=authorization_header,
            )

            resp = r.json()

            if r.status_code != 200:
                new_resp = {
                    "status": False,
                    "message": resp,
                }

            else:
                new_resp = {
                    "status": True,
                    "message": resp.get("message"),
                    "data": resp.get("data"),
                }

            return new_resp

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "error occured while retrieving cards",
            }

    @classmethod
    def get_user_info(cls, access_token: str, user_id: int) -> dict:
        """
        This method is basically for obtaining the phone number of the user
        to enable withdrawal

        Args:
            access_token (str): the access token of the user is passed here to use the url
            user_id (int): the id of the user of the server
        """
        authorization_header = {"Authorization": f"Bearer {access_token}"}
        url = f"{cls.base_url}/agency/get_user_superagent/"
        params = {"user_id": user_id}

        try:
            r = requests.get(
                url=url,
                headers=authorization_header,
                params=params,
            )

            resp = r.json()

            if r.status_code == 200:
                new_resp = {
                    "status": True,
                    "data": resp.get("data"),
                }

            else:
                new_resp = {"status": False, "message": resp}

            return new_resp

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "session expired, try again",
            }

    @classmethod
    def pay_buddy_fetch_account(cls, access_token: str, phone_number: str) -> dict:
        """
        This method fetches the account's name through mobile number and basically
        validates the account

        Args:
            access_token (str): the jwt access token of the user
            phone_number (str): the phone number of the user

        Returns:
            dict: a dictionary of "status", "message" and/or "data"
        """
        authorization_header = {"Authorization": f"Bearer {access_token}"}
        url = f"{cls.base_url}/send/send_money_paybuddy/{phone_number}/"

        try:
            r = requests.get(
                url=url,
                headers=authorization_header,
            )

            resp = r.json()

            if r.status_code == 200:
                new_resp = {
                    "status": True,
                    "data": resp,
                }

            else:
                new_resp = {"status": False, "message": resp}

            return new_resp

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "session expired, try again",
            }

    @classmethod
    def initiate_withdrawal_through_pay_buddy(
        cls,
        user,
        plan_type: str,
        phone_number: str,
        quotation_id: str,
        transaction_pin: str,
        amount: float,
        transaction_form_type: TransactionFormType = TransactionFormType.WALLET_WITHDRAWAL,
        access_token: str | None = None,
        retry_count=0,
    ) -> dict:
        """
        Initiating the withdrawal through the pay buddy Agency endpoint

        Args:
            user (User): a queryset instance of the User object
            plan_type (str): an instance of the PlanType Enums class
            quotation_id (str): the quotation_id of the plan to be withdrawn
            transaction_pin (str): the transaction pin of the user
            amount (float): the amount wished to be withdrawn (only Halal and QuickSavings)
            transaction_form_type (TransactionFormType, optional): _description_. Defaults to TransactionFormType.WALLET_WITHDRAWAL.
            access_token (str | None, optional): _description_. Defaults to a function to obtain access token.

        Returns:
            dict: returns a response object of the transaction
        """
        if access_token is None:
            access_token = agent_login().get("access")

        if plan_type.upper() not in PlanType:
            raise serializers.ValidationError(
                detail="invalid plan type while attempting withdrawal"
            )

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }
        url = f"{cls.base_url}/send/send_money_paybuddy/"
        # print(f"this is url: {url} this is headers: {headers}")

        transaction = TransactionService.create_withdrawal_to_wallet_transaction(
            user=user,
            amount=amount,
            plan_type=plan_type,
            wallet_type=getattr(WalletTypes, plan_type.upper()),
            quotation_id=quotation_id,
            transaction_form_type=transaction_form_type,
            request_data=None,
        )

        data = json.dumps(
            {
                "from_wallet_type": "COLLECTION",
                "to_wallet_type": "COLLECTION",
                "data": [
                    {
                        "buddy_phone_number": phone_number,
                        "amount": amount,
                        "narration": "SAVINGS",
                        "is_beneficiary": "False",
                        "save_beneficiary": "False",
                        "remove_beneficiary": "False",
                        "is_recurring": "False",
                    }
                ],
                "transaction_pin": transaction_pin,
            }
        )
        # print(data)

        try:
            r = requests.post(
                url=url,
                headers=headers,
                data=data,
            )

            response = r.json()

            # if the agency token is not valid

            if response.get("code") == "token_not_valid":
                # TODO: STOP THE INFINITE LOOP
                # {
                #     "detail": "Given token not valid for any token type",
                #     "code": "token_not_valid",
                #     "messages": [
                #         {
                #             "token_class": "AccessToken",
                #             "token_type": "access",
                #             "message": "Token is invalid or expired",
                #         }
                #     ],
                # }
                transaction.failure_reason = "Token is invalid or expired"
                transaction.status = Status.FAILED
                transaction.payload = json.dumps(response)
                transaction.save()

                if retry_count > 2:
                    return

                return cls.initiate_withdrawal_through_pay_buddy(
                    user=user,
                    plan_type=plan_type,
                    phone_number=phone_number,
                    quotation_id=quotation_id,
                    transaction_pin=transaction_pin,
                    amount=amount,
                    access_token=agent_login(token_not_valid=True).get("access"),
                    retry_count=retry_count,
                )

            if r.status_code != 202:
                transaction.failure_reason = response.get("message")
                transaction.status = Status.FAILED
                transaction.payload = json.dumps(response)
                transaction.save()

                return {
                    "status": False,
                    "transaction": transaction,
                    "message": (
                        response.get("message") if response.get("message") else response
                    ),
                    # response.get("data").get("message") ,
                }
            else:
                if "message" in response.keys():
                    if response.get("message") == "success":
                        # change status
                        transaction.status = Status.SUCCESS
                        # collect escrow_id and save to the transaction table instance
                        transaction.transaction_date_completed = response.get(
                            "date_completed"
                        )
                        transaction.unique_reference = response.get("data").get(
                            "escrow_id"
                        )
                        transaction.payload = json.dumps(response)
                        transaction.save()

                        # create a DebitCredit Instance with the WalletSystem mdodel
                        # get the wallet
                        wallet = WalletSystem.get_or_create_wallet(
                            user=user,
                            wallet_type=getattr(WalletTypes, plan_type.upper()),
                        )

                        deduct = WalletSystem.deduct_balance(
                            wallet=wallet,
                            amount=amount,
                            transaction_instance=transaction,
                        )

                        new_resp = {
                            "status": True,
                            "transaction": transaction,
                            "debit_credit_info": deduct,
                            "message": response,
                        }

                        return new_resp

            # Base condition if any of the conditions don't go through?
            # the idea of this is that we will check with another endpoint for the transaction_id/unique_reference
            # and the service_name on Agency backend with a background worker
            # to see if the transaction went through
            return {
                "status": True,
                "transaction": transaction,
                "message": response,
            }

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "session expired, try again",
            }

    @classmethod
    def send_money_from_liberty_to_user_through_pay_buddy(
        cls,
        phone_number: str,
        amount: float,
        transaction_reference: str,
        access_token: str | None = None,
        retry_count=0,
    ) -> dict:
        if access_token is None:
            access_token = agent_login().get("access")

        if not access_token:
            return {
                "status": False,
                "message": "no provider token to continue with this transaction",
                "status_code": 400,
            }

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        url = f"{cls.base_url}/send/send_money_paybuddy/"

        data = json.dumps(
            {
                "from_wallet_type": "COLLECTION",
                "to_wallet_type": "COLLECTION",
                "data": [
                    {
                        "buddy_phone_number": phone_number,
                        "amount": amount,
                        "narration": "LOAN_SAVINGS_REPAYMENT",
                        "is_beneficiary": "False",
                        "save_beneficiary": "False",
                        "remove_beneficiary": "False",
                        "is_recurring": "False",
                        "customer_reference": transaction_reference,
                    }
                ],
                "transaction_pin": cls.agency_banking_transaction_pin,
            }
        )

        try:
            r = requests.post(
                url=url,
                headers=headers,
                data=data,
            )

            response = r.json()

            try:
                request_dump = RequestDump(
                    request_data=data,
                    response=response,
                )
                request_dump.save()
            except:
                pass

            # if the agency token is not valid
            if response.get("code") == "token_not_valid":
                if retry_count > 2:
                    return {
                        "status": False,
                        "message": "max recursion depth reached. token is invalid",
                        "response": response,
                    }

                # increase the count by 1
                retry_count += 1
                return cls.send_money_from_liberty_to_user_through_pay_buddy(
                    phone_number=phone_number,
                    amount=amount,
                    access_token=agent_login(token_not_valid=True).get("access"),
                    retry_count=retry_count,
                    transaction_reference=transaction_reference,
                )

            data = {
                "data": response,
                "status_code": r.status_code,
            }

            if r.status_code != 202:
                ## ---------- REDIS STORAGE ------------ ##
                redis_db = redis.StrictRedis(
                    host="localhost",
                    port="6379",
                    db=0,
                    decode_responses=True,
                    encoding="utf-8",
                )

                redis_db.delete("agent_login_token")

                data["status"] = False
            else:
                data["status"] = True

            return data

        except requests.RequestException as err:
            return {
                "status": False,
                "message": "it didn't go through, try again",
                "status_code": 400,
            }

    @classmethod
    def create_agent_vfd_wallet(cls, user: AbstractUser, new: bool = False):
        """
        Request for a VFD account to be created for an agency banking user


        RESPONSES:
        SUCCESS RESPONSE:
        {   "data":{
                "status": "success",
                "message": "account successfully created",
                "account_provider": created_acc.account_provider,
                "user_id": user.id,
                "data": {
                        "account_number": created_acc.account_number,
                        "account_name": created_acc.account_name,
                        "account_type": created_acc.account_type,
                        "bank_name": created_acc.bank_name,
                        "bank_code": created_acc.bank_code,
                        "is_test": created_acc.is_test,
                        "is_active": created_acc.is_active,
                        "timestamp": created_acc.date_created,
                    },
            }
        }
        {'data': {'status': 'success', 'message': 'account successfully created', 'account_provider': 'VFD', 'user_id': 682, 'data': {'account_number': '**********', 'account_name': 'JUDE ODIONYE', 'account_type': 'COLLECTION', 'bank_name': 'VFD Microfinance Bank', 'bank_code': '999999', 'is_test': True, 'is_active': True, 'timestamp': '2023-08-15T11:22:40.876896Z'}}, 'overide': False}
        FAILED RESPONSE:
        'data': {'status': 'error', 'error_code': '91', 'message': 'account failed to create', 'account_provider': 'VFD', 'user_id': 682, 'data': {}}, 'overide': False}
            "status": "error",
                "error_code": "92",
                "message": "account failed to create",
                "account_provider": "VFD",
                "user_id": user.id,
                "data": {}
            }
        """

        url = f"{cls.base_url}/accounts/create_personal_account/"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {agent_login().get('access')}",
        }

        request_data = {
            "user_id": user.customer_user_id,
            "provider": "VFD",
        }

        if new:
            request_data["extra"] = True

        data = json.dumps(request_data)

        try:
            r = requests.post(url=url, data=data, headers=headers)
            response: dict[str, Any] = r.json()
            return data, response

        except requests.exceptions.RequestException as err:
            return data, {
                "status": False,
                "message": "something went wrong, check 'error_message' and try again",
                "error_message": str(err),
            }

    @classmethod
    def verify_transaction_from_agency(cls, escrow_id: str) -> dict:
        """
        This classmethod checks the status of a transaction using the escrow_id

        Args:
            escrow_id (str): the escrow_id of the transaction to be checked

        Returns:
            dict: view below to see the sample response

        {
            "status":"SUCCESSFUL",
            "transaction_type":"FUND_BUDDY",
            "amount":616.44,
            "escrow_id":"bffb1fea-ed12-42b7-953f-4bfd516ba22b_**********",
            "transaction_leg":"EXTERNAL",
            "narration":"SAVINGS",
            "liberty_commission":0.0,
            "sms_charge":2.99,
            "balance_before":********.7,
            "balance_after":********.*********,
            "beneficiary_nuban":null,
            "beneficiary_account_name":null,
            "beneficiary_bank_code":null,
            "liberty_reference":"LGLP_FND_BUDDY-**********-c971c8c8-3025-4fb1-b912-efc4f3308ddf",
            "is_reversed":false,
            "customer_reference":null,
            "bulk_id":null
        }
        """
        url = f"{cls.base_url}/accounts/fetch_trans_escrow_id"
        authorization_header = {
            "Authorization": f"Bearer {agent_login().get('access')}"
        }
        params = {"escrow_id": escrow_id}

        try:
            r = requests.get(
                url=url,
                headers=authorization_header,
                params=params,
            )

            resp = r.json()

            if resp.get("status") == "SUCCESSFUL" and r.status_code == 200:
                new_resp = {
                    "status": True,
                    "data": resp,
                }

            else:
                new_resp = {
                    "status": False,
                    "message": resp,
                }

            return new_resp

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "session expired, try again",
            }

    @classmethod
    def verify_agency_banking_for_vfd_funding(cls, liberty_reference: str) -> dict:
        """
        Verify agency banking transaction for Ajo agent wallet funding

        Args:
            liberty_reference (str): the liberty reference from the callback

        Returns:
            dict: a dictionary of the response, look below

        RESPONSE SAMPLE:

        {
            "status":"transaction found",
            "data":{
                "status":"SUCCESSFUL",
                "transaction_type":"FUND_LOTTO_WALLET",
                "liberty_reference":"LGLP_FND_LOTTO_WALL-3c1f7f2f-7216-4ea0-9db4-14f4b3abcec1",
                "unique_reference":null,
                "account_provider":null,
                "amount":10.0,
                "liberty_commission":0.0,
                "sms_charge":0.0,
                "total_amount_charged":null,
                "transaction_leg":"EXTERNAL",
                "balance_before":189218.12,
                "balance_after":189228.12,
                "beneficiary_account_name":null,
                "source_account_name":"CHUKWUEMEKA NWAOMA",
                "date_created":"2022-12-09T12:25:56.777100+01:00",
                "lotto_agent_user_id":"145",
                "lotto_agent_user_phone":"*************",
                "type_of_user":"PERSONAL"
            }
        }



        {
            "status":"transaction found",
            "status_code": "00",
            "data":{
                "status":"SUCCESSFUL",
                "transaction_type":"FUND_COLLECTION_ACCOUNT",
                "liberty_reference":"LGLP_FND_COLL_ACCT-**********-31b48d6a-9c66-4959-878b-2c9609c6af7c",
                "unique_reference":"f1077fe6-3d5e-405b-8196-2bc567b1c5df_1682712175",
                "account_provider":null,
                "amount":3500.0,
                "liberty_commission":0.0,
                "sms_charge":0.0,
                "total_amount_charged":null,
                "transaction_leg":"EXTERNAL",
                "balance_before":5985.14,
                "balance_after":9485.14,
                "beneficiary_account_name":null,
                "source_account_name":"CHUKWUEMEKA NWAOMA",
                "date_created":"2023-04-28T21:02:57.330958+01:00",
                "lotto_agent_user_id":null,
                "lotto_agent_user_phone":null,
                "type_of_user":"PERSONAL"
            }
        }
        #######LATEST RESPONSE############
        {
            'status': True,
            'data': {
                'status': 'SUCCESSFUL',
                'transaction_type': 'FUND_BUDDY',
                'liberty_reference': 'LGLP_FND_BUDDY-**********-c971c8c8-3025-4fb1-b912-efc4f3308ddf',
                'unique_reference': None,
                'account_provider': None,
                'amount': 616.44,
                'liberty_commission': 0.0,
                'sms_charge': 2.99,
                'total_amount_charged': None,
                'transaction_leg': 'EXTERNAL',
                'balance_before': ********.7,
                'balance_after': ********.*********,
                'beneficiary_account_name': None,
                'source_account_name': 'CHUKWUEMEKA NWAOMA',
                'date_created': '2023-08-09T10:36:11.582819+01:00',
                'lotto_agent_user_id': None,
                'lotto_agent_user_phone': None,
                'type_of_user': 'PERSONAL'
            }
        }
        """
        url = f"{cls.base_url}/accounts/transaction_enquiry/"
        authorization_header = {"Authorization": f"Token {cls.auth_token}"}
        data = {"liberty_reference": liberty_reference}

        try:
            r = requests.post(
                url=url,
                headers=authorization_header,
                data=data,
            )

            resp = r.json()

            if resp.get("status_code") == "00":
                new_resp = {
                    "status": True,
                    "data": resp.get("data"),
                }

            else:
                new_resp = {
                    "status": False,
                    "data": resp,
                }

            return new_resp

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "session expired, try again",
                "error_message": str(err),
            }

    @classmethod
    def get_liberty_card_price(cls):
        url = f"{cls.base_url}/cards/card_pricing/"
        authorization_header = {"Authorization": f"Token {cls.auth_token}"}

        try:
            r = requests.get(
                url=url,
                headers=authorization_header,
            )

            # print(r.status_code)
            resp = r.json()

            if r.status_code == 200:
                new_resp = {
                    "status": True,
                    "price": resp.get("physical_card_issuance_fee"),
                }

            else:
                new_resp = {
                    "status": False,
                    "price": None,
                }

            return new_resp

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "Error Occured",
                "error_message": str(err),
            }

    @classmethod
    def agent_card_request(cls, reference):
        url = f"{cls.base_url}/cards/create_physical_virtual_card/"
        authorization_header = {"Authorization": f"Token {cls.auth_token}"}
        data = {
            "from_wallet_type": "COLLECTION",
            "card_type": "PHYSICAL",
            "card_brand": "1",
            "delivery_address_id": (
                "5" if settings.ENVIRONMENT == "development" else "476"
            ),
            "transaction_pin": settings.AGENCY_BANKING_TRANSACTION_PIN,
            "unique_reference": reference,
        }

        try:
            r = requests.post(url=url, headers=authorization_header, json=data)

            # print(r.status_code)
            resp = r.json()
            # print(resp)

            if r.status_code == 200 and resp.get("status") == "success":
                new_resp = {
                    "status": True,
                    "data": resp,
                }

            else:
                new_resp = {
                    "status": False,
                    "data": resp,
                }

            return new_resp

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "Error Occured",
                "error_message": str(err),
            }

    @classmethod
    def map_agent_card(cls, card_pan, card_expiry_month, card_expiry_year, reference):
        url = f"{cls.base_url}/cards/map_physical_card/"
        authorization_header = {"Authorization": f"Token {cls.auth_token}"}
        data = {
            "card_pan": card_pan,
            "card_expiry_month": card_expiry_month,
            "card_expiry_year": card_expiry_year,
            "unique_reference": reference,
        }

        try:
            r = requests.post(url=url, headers=authorization_header, json=data)

            resp = r.json()

            if r.status_code == 200 and resp.get("status") == "00":
                new_resp = {
                    "status": True,
                    "data": resp,
                }

            else:
                new_resp = {
                    "status": False,
                    "data": resp,
                }

            return new_resp

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "Error Occured",
                "error_message": str(err),
            }

    @classmethod
    def create_cooperate_personal_bank_account_for_ajo_user(
        cls,
        user: CustomUser,
        phone_number: str,
        bank_type: str,
        unique_ref_app: str,
        first_name: str | None = None,
        last_name: str | None = None,
        dob=None,
        address: str | None = None,
        gender: str | None = None,
        bvn: str | None = None,
        corporate_id: str | None = None,
    ):
        """
        Request for a bank account from Agency banking

        SUCCESS for ajo users with BVN:
            {
                'status': 'success',
                'message': 'account successfully created',
                'account_provider': 'VFD',
                'user_id': 95,
                'data': {
                    'account_number': '**********',
                    'account_name': 'Jude Odionye',
                    'account_type': 'AJO_USER_PERSONAL',
                    'bank_name': 'VFD Microfinance Bank',
                    'bank_code': '999999',
                    'is_test': True,
                    'is_active': True,
                    'timestamp': '2023-09-22T15:01:23.693742Z',
                    'unique_reference': '***********',
                    'ajo_collector': 1
                    }
            }
        SUCCESS FOR CORPORATE:
            {
                'status': 'success',
                'message': 'account successfully created',
                'account_provider': 'VFD',
                'user_id': 95,
                'data': {
                    'account_number': '**********',
                    'account_name': 'LIBERTYPAY SAVINGS - *********** SPEND',
                    'account_type': 'AJO_USER_CORPORATE',
                    'vfd_account_type': 'CORPORATE',
                    'bank_name': 'VFD Microfinance Bank',
                    'bank_code': '999999', 'is_test': True,
                    'is_active': False,
                    'timestamp': '2023-09-22T15:18:39.475117Z',
                    'unique_reference':
                    '***********_s',
                    'ajo_collector': 1
                    }
            }
        """

        url = f"{cls.base_url}/accounts/create_ajo_user_accounts/"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {agent_login().get('access')}",
        }

        if not bvn and not corporate_id:
            return {
                "status": False,
                "message": "you need to input either the bvn details or corporate_id",
            }

        if bvn and corporate_id:
            return {
                "status": False,
                "message": "provide either BVN or Corporate ID but not both",
            }

        data = json.dumps(
            {
                "unique_reference": f"{phone_number}_{unique_ref_app}",
                "account_name": f"LIBERTYPAY SAVINGS - {phone_number} {bank_type.upper()}",
                "ajo_collector": user.customer_user_id,
                "bvn_details": (
                    {
                        "firstname": first_name,
                        "lastname": last_name,
                        "dob": (
                            dob.strftime("%Y-%m-%d") if isinstance(dob, date) else dob
                        ),
                        "address": address,
                        "gender": gender,
                        "phone": phone_number,
                        "bvn": bvn,
                    }
                    if bvn
                    else None
                ),
                "corporate_id": corporate_id if not bvn else "",
            }
        )

        try:
            r = requests.post(
                url=url,
                data=data,
                headers=headers,
            )
            response = r.json()

            resp_data = {
                "status": None,
                "data": response,
            }

            if r.status_code == 201 and response.get("status") == "success":
                resp_data["status"] = True
            else:
                resp_data["status"] = False

            return data, resp_data

        except requests.exceptions.RequestException as err:
            return data, {
                "status": False,
                "message": "something went wrong, check 'error_message' and try again",
                "error_message": str(err),
            }

    @classmethod
    def fetch_account_name(
        cls,
        account_number: str,
        bank_code: str,
        retry_count=0,
    ) -> Dict[str, Any]:
        url = f"{cls.base_url}/send/fetch_account_name/"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {agent_login(token_not_valid=True).get('access')}",
        }

        data = json.dumps(
            {
                "account_number": account_number,
                "bank_code": bank_code,
            }
        )

        try:
            r = requests.post(
                url=url,
                data=data,
                headers=headers,
                timeout=60,
            )

            response = r.json()

            resp_data = {
                "status": None,
                "data": response,
            }

            # handle invalid super token, recursive call should happen just twice.
            if response.get("detail", None):
                if "token" in response.get("detail"):
                    if retry_count >= 2:
                        return {
                            "status": False,
                            "message": "agent login token keeps being invalid",
                            "error_message": response,
                        }

                    retry_count += 1
                    agent_login(token_not_valid=True)

                    return cls.fetch_account_name(
                        account_number=account_number,
                        bank_code=bank_code,
                        retry_count=retry_count,
                    )

            if r.status_code == 200 and "found" in response.get("message"):
                resp_data["status"] = True
            else:
                resp_data["status"] = False

            return resp_data

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "something went wrong, check 'error_message' and try again",
                "error_message": str(err),
            }

    @classmethod
    def charge_user_agency_banking_wallet(
        cls,
        user: AbstractUser,
        transaction_pin: str,
        amount: float,
        transaction_id: str,
        super_token: str | None = None,
        retry_count: int = 0,
    ) -> Dict[str, Any]:
        # set headers
        headers = {
            "Authorization": f"Token {cls.auth_token}",
            "Content-Type": "application/json",
        }

        if super_token:
            headers["Super-Token"] = super_token

        # set url
        url = f"{cls.base_url}/send/other_services_charge/"

        # set payload/data
        data = json.dumps(
            {
                "user_id": int(user.customer_user_id),
                "service_name": "SAVINGS",
                "narration": "SAVINGS",
                "total_amount": amount,
                "service_comm": amount,
                "agent_comm": 0,
                "unique_reference": str(transaction_id),
                "transaction_pin": transaction_pin,
            }
        )

        try:
            r = requests.post(
                url=url,
                headers=headers,
                data=data,
                timeout=30,
            )

            res: Dict[str, Any] = r.json()

            # set a default response
            response = {
                "status": False,
                "message": "something went wrong in the charging process",
                "data": res,
            }

            # handle invalid super token, recursive call should happen just twice.
            if res.get("message") == "Invalid Super-Token":
                if retry_count >= 2:
                    return {
                        "status": False,
                        "message": "super token keeps being invalid",
                        "error_message": res,
                    }

                retry_count += 1

                return cls.charge_agency_banking_wallet(
                    user=user,
                    transaction_pin=transaction_pin,
                    amount=amount,
                    transaction_id=transaction_id,
                    super_token=super_token,
                    retry_count=retry_count,
                )

            # 202 is the success code
            if r.status_code == 202:
                # ensure the success code means that the money was transferred
                if "message" in res.keys() and res.get("message", "") == "success":
                    return {
                        "status": True,
                        "message": "charging done successfully",
                        "data": res,
                    }

            else:
                # specific errors catching
                if "error" in res.keys():
                    error_message: str = res.get("message")
                    if "sufficient" in error_message:
                        response["message"] = (
                            "insufficient balance, please fund your account"
                        )
                    if "KYC" in error_message.upper():
                        response["message"] = error_message

            return response

        except requests.RequestException as err:
            return {
                "status": False,
                "message": "there was an error while making this request",
                "error_message": str(err),
            }

    @classmethod
    def send_money_to_external_account_through_agency(
        cls,
        amount: float,
        account_number: str,
        account_name: str,
        bank_code: str,
        bank_name: str,
        customer_reference: str,
        narration: str,
        access_token: str | None = None,
        transaction_pin: str | None = None,
    ) -> Dict[str, Any]:
        """
        This class method sends money to an external account through
        agency banking

        Args:
            access_token (str): the access token.
            amount (float): the amount to be transferred.
            account_number (str): the account number to be transferred to.
            account_name (str): the name of the account
            bank_code (str): the bank code.
            bank_name (str): the name of the bank.
            customer_reference (str): the customer reference for agency banking.
            narration (str): the narration on the transfer.

        Returns:
            Dict[str, Any]: the response regarding the transfer.
            {
                'status': True,
                'data': {
                    'message': 'success',
                    'data': {
                        'message': 'Transaction In Progress',
                        'amount_sent': 100.0,
                        'escrow_id': '3f8a4495-56ef-4487-b3a7-9049b52a298d_1702305293',
                        'trans_started': '2023-12-11T15:34:52.454109',
                        'trans_stopped': '2023-12-11T15:34:54.016932',
                        'trans_time': '0:00:01.562823'
                    },
                    'date_completed': '2023-12-11T15:34:54.016961'
                }
            }
        """
        if not access_token:
            access_token = agent_login().get("access")
            transaction_pin = str(cls.agency_banking_transaction_pin)

        url = f"{cls.base_url}/send/send_money_bank_account/"

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        data = json.dumps(
            {
                "from_wallet_type": "COLLECTION",
                "data": [
                    {
                        "account_number": account_number,
                        "account_name": account_name,
                        "bank_code": bank_code,
                        "bank_name": bank_name,
                        "amount": amount,
                        "narration": narration if narration else "",
                        "is_beneficiary": "False",
                        "save_beneficiary": "True",
                        "remove_beneficiary": "False",
                        "is_recurring": "False",
                        "ledger_commission": 0.00,
                        "commission_type": None,
                        "customer_reference": (
                            str(customer_reference) if customer_reference else ""
                        ),
                    }
                ],
                "total_amount": 0.00,
                "total_amount_with_charge": 0.00,
                "transaction_pin": transaction_pin,
            }
        )

        try:
            r = requests.post(
                url=url,
                headers=headers,
                data=data,
                timeout=90,
            )

            resp = r.json()

            if r.status_code == 202 and resp.get("message") == "success":
                response = {
                    "status": True,
                    "data": resp,
                }

            else:
                response = {
                    "status": False,
                    "data": resp,
                }

            return response

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "error occurred",
                "error_message": str(err),
            }

    @classmethod
    def verify_transaction_from_agency_v2(
        cls,
        reference: str,
        access_token: str | None = None,
    ) -> Dict[str, Any]:
        url = f"{cls.base_url}/accounts/fetch_transaction"

        if not access_token:
            # access_token = agent_login().get("access")
            token = cls.auth_token

        headers = {
            "Authorization": (
                f"Bearer {access_token}" if access_token else f"Token {token}"
            ),
            "Content-Type": "application/json",
        }

        params = {"reference": reference}

        try:
            r = requests.get(
                url=url,
                headers=headers,
                params=params,
                timeout=120,
            )

            res = r.json()

            response = {
                "status": False,
                "data": res,
            }

            if r.status_code == 200:
                if (
                    res.get("status") == "success"
                    and res.get("message") == "transaction retrieved"
                ):
                    response["status"] = True
            elif r.status_code == 404:
                response["status"] = True

            return response
        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "an error occurred, see 'error_message'",
                "error_message": str(err),
            }

    @classmethod
    def send_money_from_loan_to_external_account_through_agency(
        cls,
        amount: float,
        account_number: str,
        account_name: str,
        bank_code: str,
        bank_name: str,
        customer_reference: str,
        narration: str,
        access_token: str | None = None,
    ) -> Dict[str, Any]:
        """
        This class method sends money to an external account through
        agency banking

        Args:
            access_token (str): the access token.
            amount (float): the amount to be transferred.
            account_number (str): the account number to be transferred to.
            account_name (str): the name of the account
            bank_code (str): the bank code.
            bank_name (str): the name of the bank.
            customer_reference (str): the customer reference for agency banking.
            narration (str): the narration on the transfer.

        Returns:
            Dict[str, Any]: the response regarding the transfer.
            {
                'status': True,
                'data': {
                    'message': 'success',
                    'data': {
                        'message': 'Transaction In Progress',
                        'amount_sent': 100.0,
                        'escrow_id': '3f8a4495-56ef-4487-b3a7-9049b52a298d_1702305293',
                        'trans_started': '2023-12-11T15:34:52.454109',
                        'trans_stopped': '2023-12-11T15:34:54.016932',
                        'trans_time': '0:00:01.562823'
                    },
                    'date_completed': '2023-12-11T15:34:54.016961'
                }
            }
        """
        if not access_token:
            access_token = loan_agent_login().get("access")

        url = f"{cls.base_url}/send/send_money_bank_account/"

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        data = json.dumps(
            {
                "from_wallet_type": "COLLECTION",
                "data": [
                    {
                        "account_number": account_number,
                        "account_name": account_name,
                        "bank_code": bank_code,
                        "bank_name": bank_name,
                        "amount": amount,
                        "narration": narration if narration else "",
                        "is_beneficiary": "False",
                        "save_beneficiary": "True",
                        "remove_beneficiary": "False",
                        "is_recurring": "False",
                        "ledger_commission": 0.00,
                        "commission_type": None,
                        "customer_reference": (
                            str(customer_reference) if customer_reference else ""
                        ),
                    }
                ],
                "total_amount": 0.00,
                "total_amount_with_charge": 0.00,
                "transaction_pin": str(cls.agency_banking_loan_transaction_pin),
            }
        )

        try:
            r = requests.post(
                url=url,
                headers=headers,
                data=data,
                timeout=30,
            )

            resp = r.json()

            if r.status_code == 202 and resp.get("message") == "success":
                response = {
                    "status": True,
                    "data": resp,
                }

            else:
                response = {
                    "status": False,
                    "data": resp,
                }

            return response

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "error occurred",
                "error_message": str(err),
            }

    @classmethod
    def send_money_from_loans_to_savings_through_pay_buddy(
        cls,
        phone_number: str,
        amount: float,
        transaction_reference: str,
        access_token: str | None = None,
        retry_count=0,
        narration: str = "SAVINGS",
    ) -> dict:

        # print(phone_number, "BBBBBBBBBBBBBBBBBBBBBBBBBBB")
        if access_token is None:
            access_token = loan_agent_login().get("access")

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        url = f"{cls.base_url}/send/send_money_paybuddy/"

        data = json.dumps(
            {
                "from_wallet_type": "COLLECTION",
                "to_wallet_type": "COLLECTION",
                "data": [
                    {
                        "buddy_phone_number": phone_number,
                        "amount": amount,
                        "narration": narration,
                        "is_beneficiary": "False",
                        "save_beneficiary": "False",
                        "remove_beneficiary": "False",
                        "is_recurring": "False",
                        "customer_reference": transaction_reference,
                    }
                ],
                "transaction_pin": cls.agency_banking_loan_transaction_pin,
            }
        )

        try:
            r = requests.post(
                url=url,
                headers=headers,
                data=data,
            )

            response = r.json()

            AgencyDumps.objects.create(
                status=r.status_code,
                source="SEND MONEY BUDDY",
                payload=data,
                response=r.text,
            )
            # if the agency token is not valid
            if response.get("code") == "token_not_valid":
                if retry_count > 2:
                    return

                # increase the count by 1
                retry_count += 1
                return cls.send_money_from_loans_to_savings_through_pay_buddy(
                    phone_number=phone_number,
                    amount=amount,
                    access_token=loan_agent_login(token_not_valid=True).get("access"),
                    retry_count=retry_count,
                )

            data = {"data": response}
            if r.status_code != 202:
                ## ---------- REDIS STORAGE ------------ ##
                redis_db = redis.StrictRedis(
                    host="localhost",
                    port="6379",
                    db=0,
                    decode_responses=True,
                    encoding="utf-8",
                )

                redis_db.delete("loan_agent_token")

                data["status"] = False
            else:
                data["status"] = True

            return data

        except requests.RequestException as err:
            return {
                "status": False,
                "message": "it didn't go through, try again",
            }

    @classmethod
    def get_user_with_phone(cls, phone_number: str, referal_code: str) -> dict:
        """
        Takes the access token to validate if a user exists with their phone number
        """

        access_token = agent_login().get("access")

        authorization_header = {"Authorization": f"Bearer {access_token}"}
        url = f"{cls.base_url}/agency/user/get_user_with_phone/"

        payload = {
            "phone_number": phone_number,
            "type_of_user": "AGENT",
            "referral_code": referal_code,
        }

        try:
            r = requests.post(url=url, headers=authorization_header, json=payload)

            resp = r.json()

            if r.status_code != 200:
                new_resp = {
                    "status": False,
                    "message": resp,
                    "search_code": None,
                }

            else:
                new_resp = {
                    "status": True,
                    "message": resp,
                    "search_code": resp.get("search_code"),
                }

            return new_resp

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": f"error occured while retrieving user {err}",
                "search_code": None,
            }

    @classmethod
    def create_account_on_liberty(
        cls,
        payload: str,
    ) -> dict:
        """
        Takes the payload create a user on agency banking
        """

        url = f"{cls.base_url}/agency/user/create_user_detail/"

        try:
            r = requests.post(url=url, json=payload)

            resp = r.json()

            return resp

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "error occured while creating user",
                "data": str(err),
            }

    @classmethod
    def get_user_referral_code(
        cls,
        access_token: str,
    ) -> Dict[str, Any]:
        """
        Get the referral code of a user from Agency Banking
        """

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }
        url = f"{cls.base_url}/api/admin/user/get_referer_code/"

        try:
            r = requests.get(
                url=url,
                headers=headers,
                timeout=90,
            )

            response = r.json()

            new_response = {
                "status": None,
                "message": response,
            }

            if r.status_code == 200 and "found" in response.get("message", "").lower():
                new_response["status"] = True

            else:
                new_response["status"] = False

            return new_response

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": f"error occured while retrieving data: {err}",
            }

    @classmethod
    def get_agency_user_details(cls, user_id, start_date):
        if settings.ENVIRONMENT == "development":
            authorization_header = {"Authorization": f"Bearer {cls.auth_token}"}
        else:
            authorization_header = {"Authorization": f"Token {cls.auth_token}"}
        url = f"{cls.base_url}/agency/savings-user-details/"
        params = {"user_id": user_id, "start_date": start_date.strftime("%Y-%m-%d")}

        try:
            r = requests.get(
                url=url,
                headers=authorization_header,
                params=params,
            )

            resp = r.json()

            if r.status_code == 401:
                new_resp = {"status": False, "message": resp}

            else:
                new_resp = resp

            return new_resp

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "session expired, try again",
            }

    @classmethod
    def get_agency_supervisor_details(cls):
        if settings.ENVIRONMENT == "development":
            authorization_header = {"Authorization": f"Bearer {cls.auth_token}"}
        else:
            authorization_header = {"Authorization": f"Token {cls.auth_token}"}
        url = f"{cls.base_url}/agency/savings-supervisor-details/"
        # params = {"user_id": user_id, "start_date": start_date.strftime("%Y-%m-%d")}

        try:
            r = requests.get(
                url=url,
                headers=authorization_header,
            )

            resp = r.json()

            if r.status_code == 401:
                new_resp = {"status": False, "message": resp}

            else:
                new_resp = resp

            return new_resp

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "session expired, try again",
            }

    @classmethod
    def get_ajo_user_loan_branch_team(cls, user_id):

        from loans.models import AjoUserAgencyBankingBranchRequestDataDump

        """
        This class method gets the branch team of an ajo user
        """

        access_token = settings.AGENCY_AUTH_TOKEN

        # if settings.ENVIRONMENT == "development":
        #     access_token = settings.AGENCY_AUTH_TOKEN_TEST

        url = f"{cls.base_url}/agency/get_user_branch_team/"
        params = {"user_id": user_id}

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        """
        SAMPLE RESPONSE
        {
            "status": "success",
            "data": {
                "branch": "EPE",
                "head_supervisor": {
                    "id": 0,
                    "email": "com",
                    "first_name": "ria",
                    "last_name": "ma",
                    "phone_number": "9999",
                    "unique_id": "7F",
                    "customer_id": "b95886764"
                },
                "team_lead": {
                    "id": 0,
                    "email": "icom",
                    "first_name": "e",
                    "last_name": "mz",
                    "phone_number": "16",
                    "unique_id": "B",
                    "customer_id": "8e12"
                }
            }
        }
        """

        try:
            response = requests.get(
                url=url,
                params=params,
                headers=headers,
            )
        except requests.exceptions.RequestException as e:
            response = {
                "error": "error",
                "message": str(e),
            }

        if isinstance(response, dict):
            AjoUserAgencyBankingBranchRequestDataDump.create_or_update_record(
                user_id=user_id,
                response_payload=response,
            )
            return response

        AjoUserAgencyBankingBranchRequestDataDump.create_or_update_record(
            user_id=user_id,
            response_payload=response.text,
        )

        try:
            response = response.json()
            return response
        except Exception as e:
            return {
                "error": "error",
                "message": str(e),
            }

    @classmethod
    def send_money_from_an_account_in_liberty_to_user_through_pay_buddy(
        cls,
        transaction_pin: str,
        narration: str,
        phone_number: str,
        amount: float,
        transaction_reference: str,
        access_token: str | None = None,
    ) -> dict:
        if not access_token:
            return {
                "status": False,
                "message": "no provider token to continue with this transaction",
                "status_code": 400,
            }

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        url = f"{cls.base_url}/send/send_money_paybuddy/"

        data = json.dumps(
            {
                "from_wallet_type": "COLLECTION",
                "to_wallet_type": "COLLECTION",
                "data": [
                    {
                        "buddy_phone_number": phone_number,
                        "amount": amount,
                        "narration": narration,
                        "is_beneficiary": "False",
                        "save_beneficiary": "False",
                        "remove_beneficiary": "False",
                        "is_recurring": "False",
                        "customer_reference": transaction_reference,
                    }
                ],
                "transaction_pin": transaction_pin,
            }
        )

        try:
            r = requests.post(
                url=url,
                headers=headers,
                data=data,
            )

            response = r.json()

            try:
                request_dump = RequestDump(
                    request_data=data,
                    response=response,
                )
                request_dump.save()
            except:
                pass

            data = {
                "data": response,
                "status_code": r.status_code,
            }
            return data

        except (
            requests.RequestException,
            Exception,
            json.decoder.JSONDecodeError,
        ) as err:

            return {
                "status": False,
                "message": "it didn't go through, try again",
                "status_code": 400,
                "error_msg": str(err),
            }

    @classmethod
    def fetch_banks(cls):
        if settings.ENVIRONMENT == "development":
            authorization_header = {"Authorization": f"Bearer {cls.auth_token}"}
        else:
            authorization_header = {"Authorization": f"Token {cls.auth_token}"}
        url = f"{cls.base_url}/accounts/bank_list"

        try:
            r = requests.get(
                url=url,
                headers=authorization_header,
            )

            resp = r.json()

            if r.status_code == 401:
                new_resp = {"status": False, "message": resp}

            else:
                new_resp = resp

            return new_resp

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "session expired, try again",
            }

    @classmethod
    def get_bank_cbn_code(cls, nip_code):
        from accounts.models import ConstantTable

        const_instance = ConstantTable.get_constant_table_instance()
        banks_list = const_instance.banks_list

        for data in banks_list:
            data_bank_nip_code = data.get("bank_code")
            if nip_code == data_bank_nip_code:
                return data.get("cbn_code")
        return nip_code

    @classmethod
    def disburse_staff_loan_commission(cls, transaction_ref, recipient, amount, profit):
        """
        Sends a commission disbursement request to the API.

        Args:
            transaction_ref (str): The reference of the transaction.
            recipient (str): The recipient ID.
            amount (int): The amount to be disbursed.
            profit (int): The profit from the transaction.

        Returns:
            dict: A dictionary containing the status and response of the API request.
        """
        url = f"{cls.base_url}/accounts/give_commision_from_me/"

        token = cls.get_token(
            email=cls.loan_agency_email, password=cls.loan_agency_password
        )

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
        }

        data = {
            "transaction_ref": transaction_ref,
            "recipient": recipient,
            "amount": amount,
            "profit": profit,
            "transaction_type": "AJO_LOAN_COMMISSIONS",
            "transaction_reason": "AJO LOAN DISBURSEMENT COMMISSION",
        }

        response = requests.post(url, headers=headers, json=data)
        others = None if isinstance(token, str) else token
        return cls.handle_api_response(
            payload=data, response=response, url=url, others=others
        )

    @classmethod
    def get_agency_acquisition_officer_details(cls, agent_id):
        if settings.ENVIRONMENT == "development":
            authorization_header = {"Authorization": f"Bearer {cls.auth_token}"}
        else:
            authorization_header = {"Authorization": f"Token {cls.auth_token}"}
        url = f"{cls.base_url}/agency/acquisition-officers"
        params = {"agent_id": agent_id}

        try:
            r = requests.get(
                url=url,
                headers=authorization_header,
                params=params,
            )

            resp = r.json()

            if r.status_code == 401:
                new_resp = {"status": False, "message": resp}

            else:
                new_resp = resp

            return new_resp

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "session expired, try again",
            }


def get_login_function_for_purpose(purpose: str):
    if purpose == "COMMISSION":
        return commissions_agent_login
    elif purpose == "REPAYMENT":
        return repayment_agent_login
    elif purpose == "ESCROW":
        return escrow_agent_login
    # elif purpose == "LOAN":
    #     return loan_agent_login
    elif purpose == "SAVINGS":
        return agent_login
    else:
        raise ValueError("Invalid login purpose")


def check_balances(login_purpose: str, tries: int = 0, access_token=None):
    if access_token is None:
        if login_purpose == "COMMISSION":
            loan_access_token = commissions_agent_login().get("access")
        elif login_purpose == "REPAYMENT":
            loan_access_token = repayment_agent_login().get("access")
        elif login_purpose == "ESCROW":
            loan_access_token = escrow_agent_login().get("access")
        elif login_purpose == "LOAN":
            loan_access_token = loan_agent_login().get("access")
        elif login_purpose == "SAVINGS":
            loan_access_token = agent_login().get("access")
        else:
            raise ValueError("Invalid login purpose")
    else:
        loan_access_token = access_token

    user_info = AgencyBankingClass.get_all_user_information(
        access_token=loan_access_token
    )
    if (
        not user_info.get("status")
        and user_info.get("message").get("message") == "authentication failed"
    ):
        if tries >= 2:
            raise ValueError("Error occurred, contact customer care")
        login_function = get_login_function_for_purpose(login_purpose)
        login_function(token_not_valid=True)
        tries += 1
        # return cls.check_balances(login_purpose, tries=tries)

    # if login_purpose == "SAVINGS":
        # with open(settings.LOG_FILE, "a") as log_file:
        #     log_file.write(f"LOGGER LOGIN token: {loan_access_token}\n")
        #     log_file.write(f"LOGIN PURPOSE: {login_purpose}\n")
        #     log_file.write(f"LOGGER USER INFO LOG: {user_info}\n")
        #     log_file.write(f"DATA: {user_info.get('user_data', {})}\n")
        #     log_file.write(
        #         f"BALANCE: {user_info.get('user_data', {}).get('wallet_balance', 0.00)}\n"
        #     )

    available_balance = user_info.get("user_data", {}).get("wallet_balance", 0.00)
    return available_balance


# def get_loan_balance(tries: int = 0):
#     loan_access_token = loan_agent_login().get("access")
#     user_info = AgencyBankingClass.get_all_user_information(access_token=loan_access_token)
#     # print(user_info, "\n\n")
#     if not user_info.get("status") and user_info.get("message").get("message") == "authentication failed":
#         if tries >= 2:
#             raise ValueError("error occurred, contact customer care")
#         loan_agent_login(token_not_valid=True)
#         tries += 1
#         # return cls.get_loan_balance(tries=tries)
#     available_balance = user_info.get("user_data").get("wallet_balance")
#     return available_balance
