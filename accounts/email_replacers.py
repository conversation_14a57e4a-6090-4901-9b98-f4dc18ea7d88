from django.template.loader import get_template


class EmailReplacer:
    @staticmethod
    def substitute_placeholders(email_template: str, **kwargs) -> str:
        """
        Replaces placeholders in an email template with corresponding values.

        Args:
            email_template (str): The email template to substitute placeholders in.
            **kwargs: Keyword arguments representing the placeholders and their corresponding values.
            the keyword arguments here are:
            name: name of the saver
            amount: amount that was credited
            date_and_time: date and time that the credit took place
            savings_wallet: savings_wallet
            next_savings_date: next savings date
            maturity_date: maturity date of the plan


        Returns:
            str: The email template with placeholders replaced by corresponding values.
        """
        for key, value in kwargs.items():
            placeholder = f"{{{key}}}"
            email_template = email_template.replace(placeholder, str(value))
        return email_template

    @staticmethod
    def render_email_template(template_name: str, context: dict):
        """
        Renders an email template with dynamic values.

        Args:
            template_name (str): The name of the email template file.
            context (dict): A dictionary containing dynamic values to substitute in the template.

        Returns:
            str: The rendered email template with dynamic values substituted.
        """
        # Load the template from the file
        template = get_template(f"accounts/templates/{template_name}.html")

        # Substitute the placeholders with dynamic values
        rendered_template = EmailReplacer.substitute_placeholders(template.render(context), **context)

        return rendered_template

    @staticmethod
    def replace_placeholders_in_html_file(file_path: str, **kwargs) -> str:
        """
        Reads an HTML file and replaces placeholders with actual values.

        Args:
            file_path (str): The path to the HTML file.
            **kwargs: Keyword arguments representing the placeholders and their corresponding values.

        Returns:
            str: The HTML file with placeholders replaced by corresponding values.
        """
        with open(file_path, "r") as file:
            html = file.read()

        for key, value in kwargs.items():
            placeholder = f"{{{key}}}"
            html = html.replace(placeholder, str(value))

        return html
