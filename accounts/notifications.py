# class CloudMessaging:
#     def __init__(self, base_dir):
#         path = f"{base_dir}/main/liberty-pay-agency-356418-firebase-adminsdk-vvf0z-41c433032f.json"
#         cred = credentials.Certificate(path)

#         default_app = firebase_admin.initialize_app(cred)

#     def send_broadcast(self, token, title, body, data={}):
#         # See documentation on defining a message payload.
#         try:
#             message = messaging.Message(
#                 notification=messaging.Notification(
#                     title=title,
#                     body=body,
#                 ),
#                 data=data,
#                 token=token,
#             )

#             # Send a message to the device corresponding to the provided
#             # registration token.
#             response = messaging.send(message)
#             # Response is a message ID string.
#         except Exception as e:
#             pass


# class InAppTransactionNotification(models.Model):
#     user = models.ForeignKey(User, on_delete=models.CASCADE)
#     title = models.Char<PERSON>ield(max_length=200)
#     message = models.CharField(max_length=500)
#     date_created = models.DateTime<PERSON>ield(auto_now_add=True)
#     last_updated = models.DateTime<PERSON>ield(auto_now=True)

#     @classmethod
#     def create_in_app_transaction_notification(cls, user, title, message_body):

#         if user.sms_subscription:
#             notification_message = f"{message_body}. SMS CHARGE - {settings.WHISPER_CHARGE}"
#         else:
#             notification_message = message_body

#         cls.objects.create(user=user, title=title, message=notification_message)


# ##########################################################################################
# # SEND OUT APP NOTIFICATION
# receiver_not_token = user_instance.firebase_key
# receiver_not_title = "Payment Received"
# receiver_not_body = f"You have received a CREDIT of N{amount} from your QR CODE"
# receiver_not_data = {"amount_sent": f"{amount}", "available_balance": f"{fund_wallet['balance_after']}"}

# send_out_notification = cloud_messaging.send_broadcast(
#     token=receiver_not_token, title=receiver_not_title, body=receiver_not_body, data=receiver_not_data
# )

# InAppTransactionNotification.create_in_app_transaction_notification(
#     user=user_instance,
#     title=receiver_not_title,
#     message_body=receiver_not_body,
# )

# ##########################################################################################


# import requests

# def new_send_email(email, template, subject, meta_data):
#     # MailGun
#     mail_gun_url = "https://api.mailgun.net/v3/libertypayng.com/messages"
#     mail_gun_auth = ("api", settings.MAILGUN_API_KEY)
#     mail_gun_data = {
# <AUTHOR> <EMAIL>",
#         "to": email,
#         "subject": f"{meta_data} {subject}",
#         "html": template
#     }
#     try:
#         res = requests.post(mail_gun_url, auth=mail_gun_auth, data=mail_gun_data)
#     except Exception as e:
#         pass

# @staticmethod
#     def create_transaction(user, data):

#         card_details = data.get("data").get("authorization")
#         logs = data.get("data").get("log")
#         amount = (data.get("data").get("amount"))/100

#         payment_reference = data.get("data").get("reference")
#         device_provider = data.get("device_provider")
#         # similar_payments = PayStackTransaction.objects.filter(reference = payment_reference)

#         if isinstance(logs, dict):
#             mobile = logs.get("mobile")
#         else:
#             mobile = True

#         liberty_reference = Transaction.create_liberty_reference("LGLP-INW_PYSTCK")
#         wallet_instance = WalletSystem.objects.filter(wallet_type="COLLECTION").last()
#         if wallet_instance:
#             wallet_id = wallet_instance.wallet_id
#             wallet_type = wallet_instance.wallet_type
#             wallet_balance = wallet_instance.available_balance
#         else:
#             wallet_id = None
#             wallet_type = None
#             wallet_balance = None


#         charge_on_paystack = (amount/100) * 3


#         if not Transaction.objects.filter(unique_reference = payment_reference).exists():

#             paystack_transaction = PayStackTransaction.objects.create(
#                 user=user,
#                 amount=amount,
#                 reference=payment_reference,
#                 event=data.get(
#                     "event"),
#                 paid_at=standard_str_to_dt(
#                     data.get("data", {}).get("paid_at")),
#                 created_at=standard_str_to_dt(
#                     data.get("data", {}).get("created_at")),
#                 channel=data.get(
#                     "data", {}).get("channel"),
#                 currency=data.get(
#                     "data", {}).get("currency"),
#                 raw_data=data.get(
#                     "raw_data"),
#                 reason=data.get(
#                     "data", {}).get("reason"),
#                 mobile=mobile,
#                 card_type=data.get("data", {}).get(
#                     "authorization", {}).get("card_type"),
#                 bank=data.get("data", {}).get(
#                     "authorization", {}).get("bank"),
#                 gateway_response=data.get(
#                     "data", {}).get("gateway_response"),
#                 device_provider=device_provider,
#                 cash_balance = wallet_balance
#             )


#             user_balance_before = wallet_instance.available_balance if wallet_instance else 0.0

#             user_balance_after = WalletSystem.get_balance_after(
#                 user = user,
#                 balance_before=user_balance_before,
#                 total_amount=amount - charge_on_paystack,
#                 is_credit=True
#             )

#             transaction_instance = Transaction.objects.create(
#                 user = user,
#                 wallet_id = wallet_id,
#                 wallet_type = wallet_type,
#                 transaction_type = "FUND_PAYSTACK",
#                 amount = amount,
#                 liberty_commission = charge_on_paystack,
#                 balance_before = user_balance_before,
#                 balance_after = user_balance_after,
#                 total_amount_received = amount,
#                 provider_status = paystack_transaction.event,
#                 liberty_reference = liberty_reference,
#                 unique_reference = payment_reference,
#                 transaction_mode = "FUND_PAYSTACK_ONLINE",
#                 payload = str(data)
#             )


#             if paystack_transaction.event == "charge.success" and "Approved" in paystack_transaction.gateway_response or paystack_transaction.gateway_response == "Payment successful" or "authorized" in paystack_transaction.gateway_response:

#                 verify_transaction = paystack_verify_funding_transaction(reference=payment_reference, amount=amount)


#                 if wallet_instance and verify_transaction:
#                     # SETTLE MONEY

#                     PayStackTransaction.settle_money_function(
#                         user=user,
#                         amount=amount,
#                         wallet_type=wallet_type,
#                         transaction_instance_id=transaction_instance.transaction_id,
#                         charge_on_paystack=charge_on_paystack
#                     )

#                     transaction_instance.status = "SUCCESSFUL"
#                     transaction_instance.save()

# ##########################################################################################
#                     card_sender_name = card_details.get("account_name", "")

#                     # SEND OUT APP NOTIFICATION
#                     receiver_not_token=user.firebase_key
#                     receiver_not_title="Payment Received"
#                     receiver_not_body=f"You have recieved a CREDIT of N{amount} from {card_sender_name}"
#                     receiver_not_data={"amount_sent": f"{amount}", "available_balance": f"{wallet_instance.available_balance}"}

#                     send_out_notification = cloud_messaging.send_broadcast(
#                         token=receiver_not_token,
#                         title=receiver_not_title,
#                         body=receiver_not_body,
#                         data=receiver_not_data
#                     )

#                 else:
#                     pass

#             else:
#                 pass

#         else:
#             transaction_instance = Transaction.objects.filter(unique_reference = payment_reference).last()
#             paystack_transaction = PayStackTransaction.objects.filter(reference=payment_reference).last()
#             debit_credit = DebitCreditRecordOnAccount.objects.filter(transaction_instance_id=transaction_instance.transaction_id).last()


#             if transaction_instance.status in ["SUCCESSFUL", "FAILED"] or debit_credit is not None:
#                 pass
#             else:
#                 if paystack_transaction.event == "charge.success" and "Approved" in paystack_transaction.gateway_response or paystack_transaction.gateway_response == "Payment successful" or "authorized" in paystack_transaction.gateway_response:

#                     verify_transaction = paystack_verify_funding_transaction(reference=payment_reference, amount=amount)


#                     if wallet_instance and verify_transaction:
#                         # SETTLE MONEY

#                         PayStackTransaction.settle_money_function(
#                             user=user,
#                             amount=amount,
#                             wallet_type=wallet_type,
#                             transaction_instance_id=transaction_instance.transaction_id,
#                             charge_on_paystack=charge_on_paystack
#                         )

#                         transaction_instance.status = "SUCCESSFUL"
#                         transaction_instance.save()

#     ##########################################################################################
#                         card_sender_name = card_details.get("account_name", "")

#                         # SEND OUT APP NOTIFICATION
#                         receiver_not_token=user.firebase_key
#                         receiver_not_title="Payment Received"
#                         receiver_not_body=f"You have recieved a CREDIT of N{amount} from {card_sender_name}"
#                         receiver_not_data={"amount_sent": f"{amount}", "available_balance": f"{wallet_instance.available_balance}"}

#                         send_out_notification = cloud_messaging.send_broadcast(
#                             token=receiver_not_token,
#                             title=receiver_not_title,
#                             body=receiver_not_body,
#                             data=receiver_not_data
#                         )

#                     else:
#                         pass

#                 else:
#                     pass


#         return True
