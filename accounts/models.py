import json
import random
import string
import uuid
from datetime import datetime
from pprint import pprint
from typing import Iterable
import secrets

from cryptography.fernet import Fernet
from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.contrib.contenttypes.fields import <PERSON>ricF<PERSON>ign<PERSON><PERSON>
from django.contrib.contenttypes.models import ContentType
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from django.core.cache import cache
from django.core.exceptions import ValidationError
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from django.db import models, transaction
from django.db.models import Count, Sum
from django.template.defaultfilters import truncatechars
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from ajo.model_choices import AccountProvider, AjoAgentStatus
from payment.model_choices import DisbursementProviderType, PaymentMethod, Status

from .defaults import BANKS_LIST, TEIR_DEFAULT_DATA
from .helpers import BaseModel, RoundedFloatField
from .model_choices import (
    AccountType,
    IDType,
    IDVerificationSources,
    InterestType,
    AgencyUserTypes,
)




def agent_id() -> int:
    from random import randint

    num = randint(100, 999)
    return num


class AgentLocations(BaseModel):
    branch_location = models.CharField(max_length=200, null=True, blank=True)

    def __str__(self):
        return self.branch_location
    

class CustomUser(AbstractUser, BaseModel):
    email = models.EmailField(
        _("email address"), db_index=True, unique=True, blank=True, null=True
    )
    username = models.CharField(max_length=100, unique=False, blank=True, null=True)
    customer_user_id = models.PositiveBigIntegerField(
        _("customer user id"), db_index=True, unique=True, null=True, blank=True
    )
    user_password = models.CharField(max_length=500, null=True, blank=True)
    user_type = models.CharField(max_length=100, null=True, blank=True)
    account_type = models.CharField(
        max_length=100, choices=AccountType.choices, default=AccountType.REGULAR
    )
    user_phone = models.CharField(max_length=100, null=True, blank=True, unique=True)
    user_branch = models.CharField(max_length=150, null=True, blank=True)
    bvn_number = models.CharField(max_length=100, null=True, blank=True)
    kyc_level = models.PositiveSmallIntegerField(default=0)
    performance_status = models.CharField(
        max_length=100, blank=True, null=True, choices=AjoAgentStatus.choices
    )
    referred_emails = models.TextField(blank=True)
    referrals_count = models.PositiveIntegerField(default=0)
    referral_code = models.CharField(max_length=100, blank=True, null=True)

    #### Call Center ####
    call_agent_id = models.PositiveIntegerField(unique=True, null=True, blank=True)
    is_call_agent = models.BooleanField(default=False, blank=True, null=True)
    is_call_agent_admin = models.BooleanField(default=False, blank=True, null=True)
    is_caller_active = models.BooleanField(default=False, blank=True, null=True)
    daily_total_expected_calls = models.IntegerField(default=0, blank=True, null=True)
    no_successful_calls = models.IntegerField(default=0, blank=True, null=True)
    bucket = models.JSONField(default=list, blank=True, null=True)
    bucket_designation = models.CharField(max_length=5, null=True, blank=True)
    recovery_agent_location = models.ManyToManyField(AgentLocations, null=True, blank=True)

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = ["username"]


class ConstantTable(models.Model):
    AGENCY_BANKING_PROVIDER = "AGENCY_BANKING"
    MONNIFY_PROVIDER = "MONNIFY_PROVIDER"

    DISBURSEMENT_PROVIDER_CHOICES = (
        (AGENCY_BANKING_PROVIDER, "agency_banking"),
        (MONNIFY_PROVIDER, "monnify"),
    )
    can_save = models.BooleanField(default=False)
    # chestlock constants
    chestlock_minimum_target = RoundedFloatField(
        default=50_000.00,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="chestlock minimum target should be >= 0"
            ),
        ],
    )
    onlending_minimum_target = RoundedFloatField(
        default=50_000.00,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="onlending minimum target should be >= 0"
            ),
        ],
    )
    chestlock_interest_rate = RoundedFloatField(
        default=5.5,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="chestlock interest rate should be >= 0"
            ),
        ],
    )
    onlending_interest_rate = RoundedFloatField(
        default=5.5,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="onlending interest rate should be >= 0"
            ),
        ],
    )

    # halal constants
    halal_minimum_target = RoundedFloatField(
        default=5_000,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="halal minimum target should be >= 0"
            ),
        ],
    )

    # quicksavings
    quicksavings_minimum_target = RoundedFloatField(
        default=5_000,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="quicksavings minimum target should be >= 0"
            ),
        ],
    )
    quicksavings_interest_rate = RoundedFloatField(
        default=0.0,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="quicksavings interest rate should be >= 0"
            ),
        ],
    )

    # minimum_withdrawal
    minimum_withdrawal_amount = RoundedFloatField(
        default=50.00,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="minimum withdrawal amount should be >= 0"
            ),
        ],
    )

    # commissions
    commissions_rate = RoundedFloatField(
        default=60.00,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="commissions rate should be >= 0"
            ),
        ],
        help_text="This field states the percentage of the commissions that the company will take, the other will be for the agent",
    )
    commissions_service_charge = RoundedFloatField(
        default=100.00,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="commissions service charge should be >= 0"
            ),
        ],
        help_text="This field states how much will be taken as service charge before the commission is split",
    )

    # ajo loans
    ajo_loan_interest_rate = RoundedFloatField(
        default=2.5,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="ajo loan interest rate should be >= 0"
            ),
        ],
    )

    card_creation_fee = RoundedFloatField(
        default=100.00,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="card creation fee should be >= 0"
            ),
        ],
    )
    card_withdrawal_charge = RoundedFloatField(
        default=50.00,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="card withdrawal charge fee should be >= 0"
            ),
        ],
    )

    card_trans_daily_limit = RoundedFloatField(
        default=100.00,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="card daily limit fee should be >= 0"
            ),
        ],
    )

    # prefunding constant
    prefunding_amount = RoundedFloatField(
        default=10_000,
        validators=[
            MinValueValidator(
                limit_value=1_000.00, message="prefunding constant shoukd be >= 1000"
            ),
        ],
    )

    # payment
    pay_from_wallet_regulator = models.BooleanField(default=True)
    # withdrawal
    withdraw_from_wallet_regulator = models.BooleanField(default=True)
    withdraw_to_external_regulator = models.BooleanField(default=True)

    # Ajo Plan Types Determinant
    main_ajo_daily_charge = RoundedFloatField(default=3.3333)
    monthly_ajo_charge = RoundedFloatField(default=2.3333)
    cards_liberty_comm = RoundedFloatField(default=10)
    target_based_plans = models.JSONField(default=dict, null=True)

    # Personal Ajo
    personal_rosca_commissions_rate = RoundedFloatField(
        default=0.5,
        validators=[
            MinValueValidator(
                limit_value=0.0,
                message="personal rosca commissions rate should be >= 0",
            ),
        ],
        help_text="This field states the percentage of the target that will be generated as fees",
    )

    # Dojah Charges
    dojah_bvn_charge = RoundedFloatField(default=60.00)
    dojah_nin_charge = RoundedFloatField(default=120.00)

    # metadata
    calculate_ajo_plan_types = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True, db_index=True)
    rosca_commision = models.FloatField(default=0.0)
    agent_rosca_commision_rate = models.FloatField(default=0.0)
    # loan
    # loan_interest_by_percentage = models.FloatField(default=12)
    # processing_fee_by_percentage = models.FloatField(default=5)
    staff_eligibility_percentage = models.FloatField(default=25)
    prosper_agent_eligibility_percentage = models.FloatField(default=35)
    savings_multiplier = models.FloatField(default=4)
    repayment_grace_period = models.PositiveIntegerField(default=4)
    crc_monthly_indebtedness = models.FloatField(default=2)
    debt_threshold = models.FloatField(default=2)
    debt_institution_count = models.PositiveIntegerField(default=4)
    gliding_days_to_last = models.PositiveIntegerField(default=7)
    loan_amount_multiplier = models.PositiveIntegerField(default=4)
    loan_multiplied_amount = models.FloatField(default=40000)
    use_gliding = models.BooleanField(default=False)
    prosper_loan_toggle = models.BooleanField(default=True)
    other_loans_config = models.JSONField(default=dict)
    use_propser_gliding = models.BooleanField(default=True)
    is_loan_available = models.BooleanField(default=True)
    staff_loans_config = models.JSONField(default=dict)
    ussd_rate_limit_no_hours = models.PositiveSmallIntegerField(default=12)
    branch_location_codes = models.JSONField(default=dict)
    credit_bureau_check_fee = models.FloatField(default=400)
    use_warning_screen = models.BooleanField(default=False)
    use_address_screen = models.BooleanField(default=False)
    percent_checker = RoundedFloatField(
        default=90.00,
        help_text="This field is used to update the percentage for loan due yesterday to loan repayment ratio",
    )
    preliminary_checker = RoundedFloatField(default=80.00)
    tier_data = models.JSONField(
        default=TEIR_DEFAULT_DATA,
        help_text="This field is used to check total collection percentage in the percentage checker",
    )
    outstanding_days_threshold = models.PositiveIntegerField(
        default=5,
        help_text="This is the threshold for the average days missed for loans greater than 3days.",
    )
    loan_with_days_threshold = models.PositiveIntegerField(
        default=5,
        help_text="This overrides the Outstanding days threshold and checks for loans with missed days greater than the threshold.",
    )
    past_maturity_days_threshold = models.PositiveIntegerField(
        default=5,
        help_text="This is used as the threshold for checking past maturity days on the 90% checker",
    )
    past_maturity_amount_threshold = models.PositiveIntegerField(
        default=5,
        help_text="This is used as the threshold for checking past maturity amount on the 90% checker",
    )
    use_threshold_constant_for_loan_checker = models.BooleanField(
        default=False,
        help_text="This allows us to use the loan with days threshold in percent checker.",
    )
    borrower_max_age = models.PositiveBigIntegerField(default=55)
    borrower_min_age = models.PositiveBigIntegerField(default=23)
    guarantor_max_age = models.PositiveBigIntegerField(default=60)
    guarantor_min_age = models.PositiveBigIntegerField(default=24)
    transfer_provider = models.CharField(
        max_length=100,
        choices=DisbursementProviderType.choices,
        default=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
        null=True,
        blank=True,
    )
    verify_guarantor_phone_no = models.BooleanField(default=False)

    # loan health
    repayment_weight = models.FloatField(default=0.5)
    timeliness_weight = models.FloatField(default=0.5)
    defaulting_loan_threshold = RoundedFloatField(default=3.0)
    average_rhs_threshold = RoundedFloatField(default=50.00)
    internal_transfer_limit = models.FloatField(default=3000000)
    health_insurance_fee = models.FloatField(default=3000)
    health_insurance_activation_fee = models.FloatField(default=1500)
    health_insurance_daily_fee = models.FloatField(default=150)
    insurance_duration = models.IntegerField(default=2)
    upper_internal_transfer_limit = models.FloatField(default=9000000)
    min_loan_amount = models.FloatField(
        default=150000, help_text="The minimum loan amount that can be requested."
    )

    # SNPL
    snpl_savings_milestone = RoundedFloatField(
        default=70.00,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="the SNPL savings milestone should be >= 0"
            ),
        ],
        help_text="The percentage to be calculated on the amount saved on a savings to get a device",
    )

    {
        "investment_tiers": {
            "1": {
                "deposit": 60000,
                "limit": 400000,
                "salary": 40000,
            },
            "2": {
                "deposit": 120000,
                "limit": 800000,
                "salary": 72000,
            },
            "3": {
                "deposit": 150000,
                "limit": 1000000,
                "salary": 100000,
            },
        }
    }
    escrow_to_bal_target = RoundedFloatField(
        default=10.0,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="ajo loan interest rate should be >= 0"
            ),
        ],
    )
    vfd_transfer_charge = RoundedFloatField(
        default=10.0,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="ajo loan interest rate should be >= 0"
            ),
        ],
    )
    # ajosepo
    individual_quota = models.IntegerField(default=4)
    group_quota = models.IntegerField(default=6)
    charge_health_insurance_fee = models.BooleanField(default=False)
    boosta_loan_config = models.JSONField(
        default=dict,
        help_text="""{
                "60":{
                    "spreadable_interest":26,
                    "processing_fee_percent":2
                },
                "90":{
                    "spreadable_interest":30,
                    "processing_fee_percent":2
                },
                "120":{
                    "spreadable_interest":33,
                    "processing_fee_percent":2
                }
                }""",
    )
    savings_health_plan_details = models.TextField(null=True, blank=True)
    vfd_disbursement_threshold = models.FloatField(default=0.00)
    checker_fee = models.FloatField(default=400)
    loan_assessment_days_to_last = models.IntegerField(default=15)
    vfd_interest_rate = models.FloatField(default=0.025)
    banks_list = models.JSONField(default=BANKS_LIST)
    last_payroll_date = models.DateTimeField(blank=True, null=True)
    manual_monnify_trans_pin = models.CharField(max_length=10000, blank=True, null=True)

    create_staff_loan_commission = models.BooleanField(default=True)
    boosta_2x_config = models.JSONField(
        default=dict,
        help_text="""{
                "60":{
                    "interest":20
                },
                "90":{
                    "interest":26
                },
                "120":{
                    "interest":26
                },
                "150":{
                    "interest":40
                },
                "180":{
                    "interest":40
                },
                "min_savings":100000,
                "available_to":[
                    "KEY_ACCOUNT"
                ],
                "max_tries":3,
                "day_count":25,
                "deposit_rate":10,
                "insurance_rate":10
                }""",
    )

    topup_config = models.JSONField(
        default=dict,
        help_text="""{
         "top_up_ratio": 33,
         "expected_repayment_ratio": 60
      }""",
    )
    disburse_to_spend_available = models.BooleanField(default=True)
    disburse_to_external_available = models.BooleanField(default=True)

    lite_transfer_provider = models.CharField(
        max_length=1000,
        choices=[
            ("wema", "Wema"),
            ("monify", "Monify"),
            ("cash_connect", "Cash_connect"),
        ],
        default="monify",
    )
    spend_account_provider = models.CharField(
        max_length=500,
        choices=DisbursementProviderType.choices,
        default=DisbursementProviderType.WEMA_PROVIDER,
        blank=True,
        null=True,
    )
    use_guarantor_verification = models.BooleanField(
        default=False,
        help_text="Indicates whether to use guarantor verification for the user.(BNPL)",
    )
    loan_types_minimum_required = models.JSONField(
        default=list,
        help_text="List of loan types that must meet or exceed the minimum loan amount. Example: ['AJO', 'BOOSTA_2X', 'BOOSTA_2X_MINI', 'CREDIT_HEALTH']",
    )
    loan_collection_channel = models.CharField(
        max_length=100, choices=AccountProvider.choices, default=AccountProvider.WEMA
    )
    admin_sms_variable = models.JSONField(
        default=dict,
        help_text="Holds configuration variables that allow the admin to toggle SMS notifications and other system settings.",
    )
    merchant_loan_config = models.JSONField(default=dict, help_text="")
    use_exclusion = models.BooleanField(
        default=True,
        help_text="Indicates whether to exclude certain loans from checks.",
    )
    exclusion_percent = RoundedFloatField(
        default=80.0,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="Exclusion percent should be >= 0"
            ),
            MaxValueValidator(
                limit_value=100.0, message="Exclusion percent should be <= 100"
            ),
        ],
        help_text="Specifies the percentage of loans to exclude from checks.",
    )
    use_exclusion_assistance = models.BooleanField(
        default=False,
        help_text="If True, adds the excluded balance to total repayment to assist the agent in loan checks.",
    )
    loan_performance_config = models.JSONField(
        default=dict,
        help_text="Holds configuration variables that allow the admin to set loans considered to be lost ('loan_overdue_days':180, 'use_config':false).",
    )
    providus_transfer_amount = models.FloatField(default=2_000_000.00)
    merchant_overdraft_admin_emails = models.JSONField(
        default=list, blank=True, null=True
    )

    @classmethod
    def get_constant_table_instance(cls):
        """
        This function always returns an instance of the constant table
        """
        # Try to retrieve the cached data
        constant_instance = cache.get("constant_table_instance")

        if not constant_instance:
            get_multiplier = cls.objects.filter(is_active=True)
            if get_multiplier:
                constant_instance = get_multiplier.latest("last_updated")
            else:
                constant_instance = cls.objects.latest("last_updated")

        # cache the data for future calls
        # the cache is valid for 20 minutes
        cache.set(
            key="constant_table_instance", value=constant_instance, timeout=60 * 30
        )

        return constant_instance

    @property
    def max_loan_amount(self) -> float:
        amount = self.loan_multiplied_amount * self.loan_amount_multiplier
        return amount

    def get_agent_teir_percent(self, number_of_loans):
        for tier in self.tier_data:
            if tier["min_loans"] <= number_of_loans <= tier["max_loans"]:
                return tier["percentage"]
        return

    # class InterestVirtualWallet(BaseModel):
    #     user = models.ForeignKey(
    #         settings.AUTH_USER_MODEL, on_delete=models.CASCADE, db_index=True
    #     )
    #     plan_type = models.CharField(max_length=200, db_index=True)
    #     plan_id = models.PositiveIntegerField()
    #     plan_quotation_id = models.CharField(max_length=350, db_index=True, blank=True, null=True)
    #     interest_balance = RoundedFloatField(null=True, blank=True)
    #     total_balance = RoundedFloatField(null=True, blank=True)

    #     date_created = models.DateField(auto_now_add=True)
    #     last_updated = models.DateTimeField(auto_now=True)
    def get_transfer_provider(self):
        from lite.utils import (
            payout_with_monify,
            payout_with_wema,
            payout_with_cash_connect,
        )

        providers = {
            "wema": payout_with_wema,
            "monify": payout_with_monify,
            "cash_connect": payout_with_cash_connect,
        }

        return providers.get(self.lite_transfer_provider)


class UserRecurrentChargeTable(models.Model):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    plan_type = models.CharField(max_length=200)
    plan_id = models.PositiveIntegerField()
    plan_quotation_id = models.CharField(max_length=350, db_index=True, unique=True)
    plan_frequency = models.CharField(max_length=150)
    payment_method = models.CharField(max_length=150)
    plan_hour = models.PositiveIntegerField(validators=[MinValueValidator(0)])
    last_charge_amount = RoundedFloatField(
        blank=True, null=True, validators=[MinValueValidator(0)]
    )
    last_run_time = models.DateTimeField(null=True, blank=True)
    next_charge_amount = RoundedFloatField(validators=[MinValueValidator(0)])
    next_run_time = models.DateTimeField(null=True, blank=True, db_index=True)
    is_active = models.BooleanField(default=True)
    date_created = models.DateTimeField(default=timezone.localtime)
    last_updated = models.DateTimeField(auto_now=True)

    # for saving with cards
    masked_pan = models.CharField(max_length=100, null=True, blank=True)
    auth_code = models.CharField(max_length=100, null=True, blank=True)


class InterestsPaidTable(models.Model):
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, db_index=True
    )
    interest_type = models.CharField(
        max_length=100, choices=InterestType.choices, default=InterestType.DAILY
    )
    plan_type = models.CharField(max_length=200, db_index=True)
    plan_quotation_id = models.CharField(max_length=350, db_index=True)
    comm_amount = RoundedFloatField(null=True, blank=True)
    day = models.IntegerField(null=True, blank=True)
    wallet_balance_before = RoundedFloatField(null=True, blank=True)
    wallet_balance_after = RoundedFloatField(null=True, blank=True)
    plan_balance_before = RoundedFloatField(null=True, blank=True)
    plan_balance_after = RoundedFloatField(null=True, blank=True)
    total_interest_paid = RoundedFloatField(default=0.0)
    date_paid = models.DateField(null=True, blank=True, db_index=True)

    # metadata
    is_active = models.BooleanField(default=True)
    date_created = models.DateField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    # def save(self, *args, **kwargs):
    #     self.comm_amount = round(self.comm_amount, 2) if self.comm_amount is not None else None
    #     self.wallet_balance_before = round(self.wallet_balance_before, 2) if self.wallet_balance_before is not None else None
    #     self.wallet_balance_after = round(self.wallet_balance_after, 2) if self.wallet_balance_after is not None else None
    #     self.plan_balance_before = round(self.plan_balance_before, 2)
    #     self.plan_balance_after = round(self.plan_balance_after, 2)
    #     self.total_interest_paid = round(self.total_interest_paid, 2)
    #     super(InterestsPaidTable, self).save(*args, **kwargs)

    @classmethod
    def create_interest_instance(
        cls,
        user: CustomUser,
        plan_type: str,
        plan_quotation_id: str,
        comm_amount: float,
        balance_before: float,
        balance_after: float,
    ):
        interests_paid_table_instance = InterestsPaidTable.objects.create(
            user=user,
            plan_type=plan_type,
            plan_quotation_id=plan_quotation_id,
            comm_amount=comm_amount,
            balance_before=balance_before,
            balance_after=balance_after,
        )

        return interests_paid_table_instance


class RecurrentChargeLogs(BaseModel):

    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    charge_type = models.CharField(
        max_length=100, choices=PaymentMethod.choices, blank=True, null=True
    )
    charge_call_response = models.TextField(blank=True, null=True)
    plan_type = models.CharField(max_length=200, null=True, blank=True)
    plan_id = models.PositiveIntegerField(null=True, blank=True)
    recurrent_object = models.ForeignKey(
        to=UserRecurrentChargeTable, on_delete=models.CASCADE, null=True, blank=True
    )

    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Recurrent Charge Log"
        verbose_name_plural = "Recurrent Charge Logs"


class SMSResponse(BaseModel):
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True
    )
    # onboarded_user = models.ForeignKey(to=AjoUser, on_delete=models.SET_NULL, null=True, blank=True)
    receiver = models.CharField(max_length=100)
    message = models.TextField(null=True, blank=True)
    payload = models.TextField(null=True, blank=True)
    response_payload = models.TextField(null=True, blank=True)
    sent = models.BooleanField(default=False)


class FailuresLog(models.Model):
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True
    )
    ajo_user = models.ForeignKey(
        to="ajo.AjoUser", on_delete=models.SET_NULL, null=True, blank=True
    )
    dump = models.TextField()
    date_created = models.DateTimeField(auto_now=True)


class USSDLog(BaseModel):
    phone_number = models.CharField(max_length=100)
    service_code = models.CharField(max_length=100)
    text = models.CharField(max_length=100, blank=True)
    session_id = models.CharField(max_length=100, blank=True)


class OTPLoginLog(BaseModel):
    phone_number = models.CharField(max_length=100)


class BlackListed(BaseModel):
    loan_disbursement = models.JSONField(
        help_text='{"emails": ["<EMAIL>", "<EMAIL>"],'
        '"phones": ["************", "************"]}',
        blank=True,
        null=True,
    )
    create_plan = models.JSONField(
        help_text='{"emails": ["<EMAIL>", "<EMAIL>"],'
        '"phones": ["************", "************"]}',
        blank=True,
        null=True,
    )

    class Meta:
        verbose_name = "Black Listed"
        verbose_name_plural = "Black Listed"

    @classmethod
    def can_disburse_loan(cls, email=None) -> bool:
        black_list_instance = cls.objects.last()
        if black_list_instance:
            black_listed_emails = black_list_instance.loan_disbursement.get(
                "emails", ""
            )
            if email in black_listed_emails:
                return False
            else:
                return True
        else:
            return False

    @classmethod
    def can_create_plan(cls, email=None) -> bool:
        black_list_instance = cls.objects.last()
        if black_list_instance:
            black_listed_emails = black_list_instance.create_plan.get("emails", "")
            if email in black_listed_emails:
                return False
            else:
                return True
        else:
            return False


class AgentSwitchLog(BaseModel):
    old_agent = models.ForeignKey(
        CustomUser, related_name="old_agent_switch_log", on_delete=models.CASCADE
    )
    new_agent = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey("content_type", "object_id")

    def save(self, *args, **kwargs):
        content_model_class = self.content_type.model_class()

        custom_user_field_exists = False
        for field in content_model_class._meta.fields:
            if (
                isinstance(field, models.ForeignKey)
                and field.related_model == CustomUser
            ):
                custom_user_field_exists = True
                break

        if not custom_user_field_exists:
            raise ValidationError(
                "content_object must have a foreign key to CustomUser"
            )
        return super().save(*args, **kwargs)


class NextOfKin(BaseModel):
    user = models.OneToOneField(
        to=CustomUser,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="next_of_kin",
    )
    ajo_user = models.OneToOneField(
        to="ajo.AjoUser",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="next_of_kin",
    )
    full_name = models.CharField(max_length=255)
    phone_number = models.CharField(max_length=100)
    email = models.EmailField(_("email address"))
    address = models.CharField(max_length=255)
    relationship = models.CharField(max_length=100)


class MonnifyLog(BaseModel):
    reference = models.CharField(max_length=200)
    status = models.CharField(max_length=100)
    response = models.TextField(blank=True, null=True)
    payload = models.TextField(blank=True, null=True)


class IDVerificationDumps(BaseModel):
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="verification_dumps",
    )
    ajo_user = models.ForeignKey(
        to="ajo.AjoUser",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="verification_dumps",
    )
    id_type = models.CharField(
        max_length=100,
        choices=IDType.choices,
        default=IDType.BVN,
    )
    source = models.CharField(
        max_length=100,
        choices=IDVerificationSources.choices,
        default=IDVerificationSources.LIBERTYBVN,
    )
    dump = models.TextField()

    class Meta:
        verbose_name = "ID Verification Dumps"
        verbose_name_plural = "ID Verification Dumps"

    @property
    def result(self):
        return truncatechars(self.dump, 60)

    @property
    def json_result(self):
        # data = self.dump
        # dict_data = eval(data)
        dict_data = json.loads(self.dump)
        return dict_data

    @property
    def reusable_response_structure(self):
        """
        Generate a reusable response structure based on the JSON result.

        Returns:
            dict: A dictionary containing standardized response structure.
                It includes status code, and data attributes extracted from the JSON result.
        """
        # Extract main result from the JSON result
        main_result = self.json_result
        # pprint(main_result)
        # Extract nested data attributes from the main result
        data_one = main_result.get("data")
        data_two = data_one.get("data")

        # Extract verification data
        verification_data = data_two.get("data")
        # print(verification_data, "\n\n")
        # Extract ID type from verification data
        id_type = verification_data.get("type").upper()
        identity_number = verification_data.get("idNumber")
        image = verification_data.get("photo")
        dateOfBirth = verification_data.get("dateOfBirth")

        if image and dateOfBirth:
            statusCode = 200
            status = "found"
        else:
            statusCode = 400
            status = "Could not fetch expected details"

        # lastName = verification_data.get("last_name")
        # firstName = verification_data.get("first_name")
        # middleName = verification_data.get("middle_name")
        # mobile = verification_data.get("phone_number")

        lastName = verification_data.get("lastName")
        firstName = verification_data.get("firstName")
        middleName = verification_data.get("middleName")
        mobile = verification_data.get("mobile")

        result = {
            "identity_number": identity_number,
            "success": statusCode == 200,
            "response": {
                "statusCode": statusCode,
                "data": {
                    "status": status,
                    "image": image,
                    "dateOfBirth": dateOfBirth,
                    "lastName": lastName,
                    "firstName": firstName,
                    "middleName": middleName,
                    "mobile": mobile,
                },
            },
            "dojah_result": verification_data,
            "youverify_result": verification_data,
            "verification_provider": "ONBOARDING_DATA",
        }

        if id_type == "NIN":
            result["response"]["data"]["nin"] = identity_number
        else:
            result["response"]["data"]["bvn"] = identity_number
        return result

    @classmethod
    def get_id_verification_data(
        cls, ajo_user, id_type, verification_id
    ) -> None | dict:
        """
        Retrieve ID verification data for a specific user and ID type.

        Args:
            ajo_user (User): The user for whom ID verification data is to be retrieved.
            id_type (str): The type of ID for which verification data is to be retrieved.

        Returns:
            Union[None, dict]: A dictionary containing ID verification data if found,
                or None if no data is found for the specified user and ID type.
        """
        # Query the database for ID verification data matching the user and ID type
        id_verification_qs = cls.objects.filter(ajo_user=ajo_user, id_type=id_type)

        # Check if any data exists
        if not id_verification_qs.exists():
            return None
        else:
            # Retrieve the latest verification instance
            verification_instance = id_verification_qs.last()
            result = verification_instance.reusable_response_structure
            identity_number = result.get("identity_number")
            # print(identity_number, verification_id)
            # print(identity_number, verification_id)
            # print(identity_number, verification_id)
            if identity_number == verification_id:
                return result
            else:
                return None


class IPWhitelist(BaseModel):
    ip_address = models.CharField(max_length=255, blank=True, null=True)
    api_key = models.CharField(max_length=300, blank=True, null=True, editable=False)
    whitelist_type = models.CharField(
        max_length=100,
        choices=[
            ("IP", "IP Address"),
            ("API", "API Key"),
        ],
        default="IP",
    )
    allowed = models.BooleanField(default=False)

    def save(self, *args, **kwargs):
        # Create a new API key if one is not provided
        if not self.id:
            if not self.api_key and self.whitelist_type == "API":
                self.api_key = self.generate_api_key()  # Generate a unique API key

        super().save(*args, **kwargs)

    @staticmethod
    def generate_api_key(length=100):
        """Generate a secure random API key."""
        characters = string.ascii_letters + string.digits
        return "".join(secrets.choice(characters) for _ in range(length))

    def __str__(self):
        return f"{self.ip_address}"

    class Meta:
        verbose_name = "WhiteListed IP"
        verbose_name_plural = "WhiteListed IP"


class StaffCommissionAllocation(BaseModel):
    loan = models.OneToOneField(
        "loans.AjoLoan",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    loan_amount = models.FloatField(
        default=0.0, validators=[MinValueValidator(0.0)], editable=False
    )
    commission_amount = models.FloatField(validators=[MinValueValidator(0.0)])
    split_percentage = models.FloatField(
        help_text="Indicates percentage in decimal e.g 20% = 0.2 which is the total share percentage"
    )
    date_shared = models.DateTimeField(null=True, blank=True, editable=False)
    shared = models.BooleanField(
        default=False,
        help_text="Indicates whether the loan commission has been fully processed.",
    )
    transferred_to_commision = models.BooleanField(default=False)
    locked = models.BooleanField(default=False)
    staff_count = models.IntegerField(default=0)
    successful_share_count = models.IntegerField(default=0)
    beneficiaries = models.ManyToManyField(
        "AjoLoanCommissionRecipient",
        blank=True,
    )

    def __str__(self):
        return f"{self.loan}"

    def save(self, *args, **kwargs):
        self.split_percentage = round(self.split_percentage, 3)
        super().save(*args, **kwargs)

    class Meta:
        verbose_name = "Staff Commission Allocation"
        verbose_name_plural = "Staff Commission Allocation"

    @classmethod
    def create_commission_distribution(cls, loan_id):
        from loans.models import AjoLoan

        """
        Create a commission distribution for a given loan.

        This method checks if a commission distribution already exists for the specified loan.
        If not, it calculates the commission amount based on the loan's total amount and the
        total share percentage of eligible recipients. It then creates a new commission distribution
        instance and associates the eligible recipients as beneficiaries.

        Args:
            loan_id (int): The ID of the loan for which to create the commission distribution.

        Returns:
            LoanCommissionDistribution or None: 
                - Returns the newly created LoanCommissionDistribution instance if successful.
                - Returns None if the loan does not exist, if the distribution already exists,
                  or if there are no eligible recipients.
        """

        const = ConstantTable.get_constant_table_instance()
        create_staff_loan_commission = const.create_staff_loan_commission

        if create_staff_loan_commission is True:
            try:
                # Attempt to retrieve the loan instance by its ID
                loan_instance = AjoLoan.objects.get(id=loan_id)
            except AjoLoan.DoesNotExist:
                # Return None if the loan with the specified ID does not exist
                return None

            # Check if a commission distribution already exists for this loan
            if cls.objects.filter(loan=loan_instance).exists():
                return (
                    None  # Return None to indicate that the distribution already exists
                )
            else:
                # Get the total share percentage and count of eligible recipients
                get_total_share_percentage = (
                    AjoLoanCommissionRecipient.get_share_percentage()
                )
                staff_count = get_total_share_percentage.get("total_count", 0)
                commission_recipients = get_total_share_percentage.get(
                    "commission_recipients", []
                )
                total_share_percentage = get_total_share_percentage.get(
                    "total_share_percentage", 0
                )

                # Proceed if there are eligible recipients
                if staff_count > 0:
                    loan_amount = loan_instance.amount
                    # Calculate the commission amount based on the loan amount and total share percentage
                    commission_amount = total_share_percentage * loan_amount

                    # Create a new commission distribution instance
                    commission_share_instance = cls.objects.create(
                        loan=loan_instance,
                        loan_amount=loan_amount,
                        commission_amount=commission_amount,
                        split_percentage=total_share_percentage,
                        staff_count=staff_count,
                    )
                    # Associate the commission recipients as beneficiaries
                    commission_share_instance.beneficiaries.add(*commission_recipients)
                    return (
                        commission_share_instance  # Return the newly created instance
                    )
                else:
                    return None  # Return None if there are no eligible recipients

    @classmethod
    def disburse_to_beneficiaries(cls, comm_instance: "StaffCommissionAllocation"):
        from accounts.agency_banking import AgencyBankingClass

        if comm_instance.shared is False:
            beneficiaries = comm_instance.beneficiaries.filter(
                is_active=True, share_percentage__gt=0
            )
            count_successful_transfer = 0
            transfer_result = []
            for beneficiary in beneficiaries:
                beneficiary: AjoLoanCommissionRecipient

                # timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                # unique_id = str(uuid.uuid4()).split("-")[0]
                # transaction_ref = f"LCOMM-{timestamp}-{unique_id}{beneficiary.id}"
                transaction_ref = f"LCOMM-{comm_instance.id}{beneficiary.id}"

                recipient = beneficiary.phone_number
                share_percentage = beneficiary.share_percentage

                profit = comm_instance.loan_amount * share_percentage
                loan_amount = comm_instance.loan_amount

                comm_log = CommissionDisbursementLog.objects.filter(
                    reference=transaction_ref
                ).first()

                if comm_log is None:
                    comm_log = CommissionDisbursementLog.objects.create(
                        allocaton_id=comm_instance.id,
                        recipient_id=beneficiary.id,
                        loan_amount=loan_amount,
                        amount=profit,
                        share_percentage=share_percentage,
                        reference=transaction_ref,
                    )

                if comm_log.status_code == "200":
                    continue

                _transafer = AgencyBankingClass.disburse_staff_loan_commission(
                    transaction_ref=transaction_ref,
                    recipient=recipient,
                    amount=loan_amount,
                    profit=profit,  # recipient commission
                )

                status_code = _transafer.get("status_code")
                comm_log.status_code = status_code
                comm_log.request_result = _transafer
                comm_log.save()
                transfer_result.append(_transafer)
                success_code = [200, 201]
                if status_code in success_code:
                    beneficiary.update_disbursement_details(amount=profit)
                    count_successful_transfer += 1

            if count_successful_transfer > 0:
                comm_instance.date_shared = timezone.now()
                comm_instance.shared = True
                comm_instance.transferred_to_commision = True
                comm_instance.successful_share_count = count_successful_transfer
                comm_instance.save()
            return transfer_result
        else:
            return None


class AjoLoanCommissionRecipient(BaseModel):
    # email = models.CharField(max_length=100, null=True, blank=True)
    phone_number = models.CharField(max_length=300, unique=True)
    total_sent = models.FloatField(
        default=0, validators=[MinValueValidator(0.0)], editable=False
    )
    share_percentage = models.FloatField(
        help_text="Indicates percentage in decimal e.g 20% = 0.2"
    )
    last_shared_amount = models.FloatField(
        default=0, validators=[MinValueValidator(0.0)], editable=False
    )
    last_sent_date = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.phone_number}"

    class Meta:
        verbose_name = "Staff Receiving Loan Commission"
        verbose_name_plural = "Staff Receiving Loan Commission"

    @property
    def recipient_unique_id(self):
        return f"********{self.phone_number[-3:]}"

    @classmethod
    def get_share_percentage(cls) -> dict:
        """
        Calculate the total count of active recipients and their total share percentage.

        This method aggregates the share percentages of recipients who are
        marked as active and have a share percentage greater than zero.

        Returns:
            dict: A dictionary containing:
                - total_count: The number of active recipients with share percentages greater than zero.
                - total_share_percentage: The sum of share percentages for active recipients.
                - commission_recipients: A queryset of the active recipients.
        """
        # Retrieve all active recipients with a share percentage greater than zero
        recipients_queryset = cls.objects.filter(is_active=True, share_percentage__gt=0)

        # Aggregate the total count and sum of share percentages
        aggregated_data = recipients_queryset.aggregate(
            total_count=Count("id"),  # Count of active recipients
            total_share_percentage=Sum(
                "share_percentage"
            ),  # Sum of their share percentages
        )

        # Add the queryset of recipients to the aggregated data
        aggregated_data["commission_recipients"] = recipients_queryset

        return aggregated_data

    def update_disbursement_details(self, amount):
        self.total_sent += amount
        self.last_shared_amount = amount
        self.last_sent_date = timezone.now()
        self.save()


class CommissionDisbursementLog(BaseModel):
    allocaton_id = models.CharField(max_length=100, null=True, blank=True)
    recipient_id = models.CharField(max_length=100, null=True, blank=True)
    amount = models.FloatField(null=True, blank=True)
    loan_amount = models.FloatField(null=True, blank=True)
    share_percentage = models.FloatField(null=True, blank=True)
    reference = models.CharField(max_length=300, unique=True, null=True, blank=True)
    status_code = models.CharField(max_length=100, null=True, blank=True)
    request_result = models.TextField(null=True, blank=True)

    def __str__(self) -> str:
        return f"Log -> {self.id} For Allocation ID {self.allocaton_id}"

    class Meta:
        verbose_name = "Commission Disbursement Log"
        verbose_name_plural = "Commission Disbursement Log"

    @property
    def payload(self):
        result = json.dumps(self.request_result)
        return truncatechars(result, 250)


class ManualAdjustment(BaseModel):
    LOAN = "loan"
    WALLET = "wallet"
    OTHER = "other"
    MANUAL_UPDATE = "manual_update"
    CRC_MANUAL_UPDATE = "crc_manual_update"

    CORRECTION_TYPES = [
        (LOAN, "Loan"),
        (WALLET, "Wallet"),
        (OTHER, "Other"),
        (MANUAL_UPDATE, "Manual Update"),
        (CRC_MANUAL_UPDATE, "CRC Manual Update"),
    ]

    DEBIT = "debit"
    CREDIT = "credit"

    TRANSACTION_TYPES = [
        (DEBIT, "Debit"),
        (CREDIT, "Credit"),
    ]

    # corrected_by = models.ForeignKey(
    #     to=settings.AUTH_USER_MODEL,
    #     on_delete=models.SET_NULL,
    #     null=True,
    #     blank=True,
    #     related_name="correction",
    # )
    ticket_id = models.CharField(max_length=300, null=True, blank=True)
    model_name = models.CharField(max_length=300, null=True, blank=True)
    phone_number = models.CharField(max_length=100, null=True, blank=True)
    email = models.CharField(max_length=100, null=True, blank=True)
    correction_type = models.CharField(
        max_length=100, choices=CORRECTION_TYPES, null=True, blank=True
    )
    amount = models.FloatField(default=0.0, null=True, blank=True)
    # transaction_type = models.CharField(
    #     max_length=6, choices=TRANSACTION_TYPES, null=True, blank=True
    # )
    reason = models.TextField(null=True, blank=True)
    status = models.CharField(
        max_length=20, choices=Status.choices, null=True, blank=True
    )
    notes = models.TextField(null=True, blank=True)

    def __str__(self):
        return f"{self.correction_type}"

    @classmethod
    def generate_ticket_id(cls, model_instance):
        """
        Generates a unique ticket ID string for a given model instance.

        This method generates a random alphanumeric ticket ID of 8 characters,
        then appends the model name and instance ID to ensure uniqueness across
        the model. The ticket ID is created in the following format:

            <random_ticket_id><model_name><instance_id>

        Example output: 'A1B2C3D4mymodel123'

        Args:
            model_instance: The instance of the model for which the ticket ID is generated.
                        This instance must have a primary key (ID) assigned.

        Returns:
            str: A unique ticket ID string that includes a random 8-character alphanumeric string,
                the model name, and the instance's primary key (ID).

        Raises:
            AttributeError: If the model instance does not have an ID (i.e., it hasn't been saved yet).
        """

        # Generate a random alphanumeric string of length 8
        ticket_id = "".join(random.choices(string.ascii_uppercase + string.digits, k=8))

        # Format the result with the random ticket ID, model name, and instance ID
        result = f"{ticket_id}{model_instance._meta.model_name}{model_instance.id}"

        return result

    @classmethod
    def create_adjusment_record(
        cls,
        model_instance,
        email,
        phone_number=None,
        correction_type=None,
        amount=None,
        reason=None,
        status=None,
        notes=None,
    ):
        ticket_id = cls.generate_ticket_id(model_instance=model_instance)
        model_name = model_instance.__class__.__name__
        cls.objects.create(
            ticket_id=ticket_id,
            model_name=model_name,
            email=email,
            phone_number=phone_number,
            correction_type=correction_type,
            amount=amount,
            reason=reason,
            status=status,
            notes=notes,
        )


class ActionPermission(BaseModel):
    admin_emails = models.TextField(
        null=True,
        blank=True,
        help_text="Comma-separated list of emails for users with admin actions",
    )
    customer_service_emails = models.TextField(
        null=True,
        blank=True,
        help_text="Comma-separated list of emails for users with customer service actions",
    )
    operation_lead_emails = models.TextField(
        null=True,
        blank=True,
        help_text="Comma-separated list of emails for users with customer service actions",
    )

    def __str__(self):
        return "Action Permissions"

    @classmethod
    def get_instance(cls):
        return cls.objects.first()

    class Meta:
        verbose_name = "Action Permission"
        verbose_name_plural = "Action Permission"

    @classmethod
    def get_admin_emails(cls):
        """Return admin emails as a list"""
        permission_instance = cls.get_instance()
        return (
            permission_instance.admin_emails.split(",")
            if permission_instance.admin_emails
            else []
        )

    @classmethod
    def get_customer_service_emails(cls):
        """Return customer service emails as a list"""
        permission_instance = cls.get_instance()
        cs_emails = (
            permission_instance.customer_service_emails.split(",")
            if permission_instance.customer_service_emails
            else []
        )
        admin_emails = cls.get_admin_emails()
        return cs_emails + admin_emails

    @classmethod
    def get_operation_lead_emails(cls):
        """Return operation lead emails as a list"""
        permission_instance = cls.get_instance()
        operlead_emails = (
            permission_instance.operation_lead_emails.split(",")
            if permission_instance.operation_lead_emails
            else []
        )
        admin_emails = cls.get_admin_emails()
        return operlead_emails + admin_emails


class PaystackLog(BaseModel):
    PAYSTACK_REQUEST_CHOICES = (
        ("RECIPIENT_ID", "RECIPIENT_ID"),
        ("TRANSFER", "TRANSFER"),
        ("TRANSFER_VERIFICATION", "TRANSFER_VERIFICATION"),
        # ("refund", "refund"),
    )
    # reference = models.CharField(max_length=200, blank=True, null=True)
    # status = models.CharField(max_length=100, blank=True, null=True)
    response = models.TextField(blank=True, null=True)
    payload = models.TextField(blank=True, null=True)
    request_type = models.CharField(
        max_length=100,
        choices=PAYSTACK_REQUEST_CHOICES,
        blank=True,
        null=True,
    )


class AgencyBankingToken(BaseModel):
    token = models.CharField(max_length=5000, editable=False)  # Add editable False
    token_owner = models.CharField(
        max_length=1200,
        choices=AgencyUserTypes.choices,
        editable=False,
    )  # Add Editable False

    def __str__(self):
        return self.token

    @classmethod
    def retrieve_token(cls, token_owner):
        try:
            recent_token = cls.objects.filter(token_owner=token_owner).latest(
                "created_at"
            )

            if recent_token.token_age_in_minutes >= 5:
                data = {"status": False, "access": ""}
            else:
                data = {"status": True, "access": recent_token.token}
            return data
        except AgencyBankingToken.DoesNotExist:
            data = {"status": False, "access": ""}
            return data

    @classmethod
    def create_token(cls, token, token_owner):
        new_token = cls.objects.create(token=token, token_owner=token_owner)
        new_token.token

    @property
    def token_age_in_minutes(self):
        created_at = self.created_at
        time_now = timezone.now()
        time_difference = (time_now - created_at).seconds / 60

        return time_difference
