from typing import Any, Dict

from rest_framework import serializers, status
from rest_framework.response import Response

from .utils import serializer_error


class CustomResponse(Response):
    def __init__(
        self,
        data=None,
        message: str = "",
        success: bool = True,
        status=status.HTTP_200_OK,
        template_name=None,
        headers=None,
        exception=False,
        content_type=None,
        error=None,
        error_message=None,
    ):
        response = {
            "status": success,
            "message": message,
        }

        if data:
            response.update({"data": data})

        if not success:
            response.update(
                {
                    "error": error if error else "495",
                    "error_message": error_message,
                }
            )

        super().__init__(
            data=response,
            status=status,
            template_name=template_name,
            headers=headers,
            exception=exception,
            content_type=content_type,
        )


def success_response(
    data=None,
    message: str = "",
    status: status = status.HTTP_200_OK,
):
    return CustomResponse(
        data=data,
        message=message,
        success=True,
        status=status,
    )


def error_response(
    message: str = "",
    error: str | None = None,
    error_message: str | None = None,
    status=status.HTTP_400_BAD_REQUEST,
):
    return CustomResponse(
        data=None,
        message=message,
        success=False,
        status=status,
        error=error,
        error_message=error_message,
    )


def pagination_page_not_found_response(message: str | None = None) -> Response:
    """
    this response is special for when the page in the pagination requests is not found

    Args:
        message (str | None, optional): the message you want to put, else there is a default message 'invalid page'. Defaults to None.

    Returns:
        Response: A response object with error 404, status False and message formatted message key value pairs
    """
    message: str = "invalid page"
    if message:
        message = message

    return Response(
        {
            "error": "404",
            "status": False,
            "message": message,
        },
        status=status.HTTP_400_BAD_REQUEST,
    )


def serializer_validation_error_response(error: serializers.ValidationError) -> Response:
    """
    this response is special for when a serializers.ValidationError is raised while trying to validate a request

    Args:
        err (serializers.ValidationError): the error raised as serializers.ValidationError, e.g. except serializers.ValidationError as error

    Returns:
        Response: A response object with error 603, status False and message formatted serializer error key value pairs
    """
    return Response(
        {
            "error": "603",
            "status": False,
            "message": serializer_error(error),
        },
        status=status.HTTP_422_UNPROCESSABLE_ENTITY,
    )


def value_error_response(error: ValueError, code: str = "605") -> Response:
    """
    This Response is special for when a ValueError is raised while trying to perform any operations

    Args:
        error (ValueError): A Value error caught in a try except block
        code (str): the error code to be displayed in the response

    Returns:
        Response: A Response object with error 605, status False and message in the ValueError as a string
    """
    return Response(
        {
            "error": code,
            "status": False,
            "message": str(error),
        },
        status=status.HTTP_400_BAD_REQUEST,
    )


def failed_celery_response(
    message: str,
    error_message: str | None = None,
) -> Dict[str, Any]:
    base = {
        "status": "failed",
        "message": message,
    }

    if error_message is not None:
        base["error_message"] = error_message

    return base


def success_celery_response(
    message: str,
    data: Any | None = None,
) -> Dict[str, Any]:
    base = {
        "status": "success",
        "message": message,
    }

    if data is not None:
        base["data"] = data

    return base
