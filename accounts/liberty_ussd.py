import json
from typing import Any, Dict

import requests
from django.conf import settings
from requests.auth import HTTPBasicAuth


class LibertyUSSD:
    base_url: str = settings.LIBERTY_USSD_BASE_URL
    username: str = settings.LIBERTY_USSD_USERNAME
    password: str = settings.LIBERTY_USSD_PASSWORD
    _BASIC_AUTH = HTTPBasicAuth(username.encode("utf-8"), password.encode("utf-8"))
    _BASE_HEADERS = {"Content-Type": "application/json"}

    @classmethod
    def get_processing_loans(cls) -> Dict[str, Any]:
        """
        gets a list of pending loan requests or processing loans

        Returns:
            Dict[str, Any]: {
                'status': True,
                'data': [
                    {
                        'loan_amount': 18900,
                        'mandate_reference': '321039916711',
                        'status': 'processing',
                        'borrowers_name': 'SHEHU NUHU',
                        'borrowers_ministry': 'FGN: FEDERAL GOVERNMENT OF NIGERIA',
                        'request_date': '2024-05-01T18:45:38.406436+01:00',
                        'loan_duration': 3
                    },
                ]
            }
        """
        url = f"{cls.base_url}api/service/fetch-all-processing-loans/"

        try:
            r = requests.get(
                url=url,
                auth=cls._BASIC_AUTH,
                timeout=90,
                headers=cls._BASE_HEADERS,
            )

            response = r.json()

            new_response = {
                "status": None,
                "data": response,
            }

            new_response["status"] = True if r.status_code == 200 else False

            return new_response

        except requests.RequestException as err:
            return {
                "status": False,
                "message": "encountered an err",
                "error_message": str(err),
            }

    @classmethod
    def create_lender(
        cls,
        name: str,
        email: str,
        phone_number: str,
        customer_id: int,
    ) -> Dict[str, Any]:
        """
        Create a lender on liberty USSD

        Args:
            name (str): the full name of the lender
            email (str): the email of the lender
            phone_number (str): the phone number of the lender
            customer_id (int): the customer ID of the lender

        Returns:
            Dict[str, Any]: {
                'status': True,
                'data': {
                    'id': 1,
                    'name': 'Joseph Chinedu',
                    'email': '<EMAIL>',
                    'phone': '234801234567',
                    'customer_id': '201',
                    'created_at': '2024-04-03T08:57:07.857308+01:00',
                    'updated_at': '2024-04-03T08:57:07.857332+01:00'
                }
            }
        """
        url = f"{cls.base_url}api/service/on-lender/"

        payload = {
            "name": name,
            "email": email,
            "phone": phone_number,
            "customer_id": customer_id,
        }

        try:

            r = requests.post(
                url=url,
                json=payload,
                auth=cls._BASIC_AUTH,
                timeout=60,
            )

            response = r.json()

            new_response = {
                "status": None,
                "data": response,
            }

            new_response["status"] = True if r.status_code == 201 else False

            return new_response

        except requests.RequestException as err:
            return {
                "status": False,
                "message": "encountered an err",
                "error_message": str(err),
            }

    @classmethod
    def get_lender(cls, customer_id: int) -> Dict[str, Any]:
        """
        Get the information of a lender

        Args:
            customer_id (int): the customer ID

        Returns:
            Dict[str, Any]: {
                'status': True,
                'data': {
                    'id': 1,
                    'name': 'Joseph Chinedu',
                    'email': '<EMAIL>',
                    'phone': '234801234567',
                    'customer_id': '201',
                    'created_at': '2024-04-03T08:57:07.857308+01:00',
                    'updated_at': '2024-04-03T08:57:07.857332+01:00'
                }
            }
        """
        url = f"{cls.base_url}api/service/on-lender/"
        params = {"customer_id": customer_id}

        try:
            r = requests.get(
                url=url,
                headers=cls._BASE_HEADERS,
                params=params,
                auth=cls._BASIC_AUTH,
            )

            response = r.json()

            new_response = {
                "status": None,
                "data": response,
            }

            new_response["status"] = True if r.status_code == 200 else False

            return new_response

        except requests.RequestException as err:
            return {
                "status": False,
                "message": "encountered an err",
                "error_message": str(err),
            }

    @classmethod
    def update_lender(cls, customer_id: int, name: str, phone_number: str) -> Dict[str, Any]:
        """
        Update the information of a lender

        Args:
            customer_id (int): the customer ID
            name (str): name to update with
            phone_number (str): phone number to update with

        Returns:
            Dict[str, Any]: {
                'status': True,
                'data': {
                    'id': 1,
                    'name': 'Joseph C',
                    'email': '<EMAIL>',
                    'phone': '234801234567',
                    'customer_id': '201',
                    'created_at': '2024-04-03T08:57:07.857308+01:00',
                    'updated_at': '2024-04-03T08:57:07.857332+01:00'
                }
            }
        """
        url = f"{cls.base_url}api/service/on-lender/"
        params = {"customer_id": customer_id}
        payload = {
            "name": name,
            "phone": phone_number,
        }

        try:
            r = requests.put(
                url=url,
                headers=cls._BASE_HEADERS,
                params=params,
                auth=cls._BASIC_AUTH,
                json=payload,
            )

            response = r.json()

            new_response = {
                "status": None,
                "data": response,
            }

            new_response["status"] = True if r.status_code == 200 else False

            return new_response

        except requests.RequestException as err:
            return {
                "status": False,
                "message": "encountered an err",
                "error_message": str(err),
            }

    @classmethod
    def create_loan(cls) -> Dict[str, Any]:
        raise NotImplementedError("to be implemented")

    @classmethod
    def get_lender_loans(cls, customer_id: int) -> Dict[str, Any]:
        """
        Get the lender's loans

        Args:
            customer_id (int): the customer ID

        Returns:
            Dict[str, Any]: {
                'status': True,
                'data': {[]}
            }
        """
        url = f"{cls.base_url}api/service/fetch_lenders_loan/"
        params = {"customer_id": customer_id}

        try:
            r = requests.get(
                url=url,
                headers=cls._BASE_HEADERS,
                params=params,
                auth=cls._BASIC_AUTH,
            )

            response = r.json()

            new_response = {
                "status": None,
                "data": response,
            }

            new_response["status"] = True if r.status_code == 200 else False

            return new_response

        except requests.RequestException as err:
            return {
                "status": False,
                "message": "encountered an err",
                "error_message": str(err),
            }

    @classmethod
    def disburse_ajo_loan(cls, payload):
        url = f"{cls.base_url}api/seeds/v1/create_loan/"

        headers = {
            # "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        # print("payload::::::::", payload)
        # return

        try:
            r = requests.post(
                url=url,
                headers=headers,
                data=json.dumps(payload),
            )

            response = r.text
            # print("response", response)
            # print("r", response)

            if r.status_code != 200:
                data = {
                    "status": False,
                    "data": response,
                    "status_code": r.status_code,
                }
            elif r.status_code == 200:
                data = {
                    "status": True,
                    "data": response,
                    "status_code": r.status_code,
                }
            else:
                raise ValueError("Invalid input")
            return data
        
        except Exception as err:
            print(err)
            return {
                "status": False,
                "message": "it didn't go through, try again",
                "status_code": 400
            }
        
    @classmethod
    def verify_vfd_overdraft_disbursement(cls, reference):
        url = f"{cls.base_url}api/seeds/v1/verify_transaction/"

        if not reference:
            return {
                    "status": True,
                    "data": {'data': '{"trans_ref":["This field may not be null."]'},
                    "status_code": 400,
                    "transferred": False,
                    "reversed": False
                    }

        headers = {
            # "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }

        payload = {
            "trans_ref": str(reference)
            }

        try:
            r = requests.post(
                url=url,
                headers=headers,
                data=json.dumps(payload)
            )

            response = r.text
            # print("response", response)
            # print("r", r)

            if r.status_code != 200:
                data = {
                    "status": False,
                    "data": response,
                    "status_code": r.status_code,
                    "transferred": False,
                    "reversed": False
                }
            else:
                response = json.loads(response)
                j_response = response.get("response")
                transfer_status = j_response.get("trans_status")
                # print("::::::::::::::TRANS_STATUS::::::::::::")
                # print(transfer_status)
                # print("::::::::::::::TRANS_STATUS::::::::::::")

                if transfer_status == "SUCCESS":
                    data = {
                        "status": True,
                        "data": response,
                        "status_code": r.status_code,
                        "transferred": True,
                        "reversed": False
                    }
                elif transfer_status == "PENDING":
                    data = {
                        "status": False,
                        "data": response,
                        "status_code": r.status_code,
                        "transferred": False,
                        "reversed": False
                    }
                elif transfer_status == "FAILED":
                    data = {
                        "status": True,
                        "data": response,
                        "status_code": r.status_code,
                        "transferred": False,
                        "reversed": True
                    }
                else:
                    data = {
                        "status": True,
                        "data": response,
                        "status_code": r.status_code,
                        "transferred": False,
                        "reversed": False
                    }
            return data

        except Exception as err:
            # print(err)
            return {
                "status": False,
                "message": f"Verification failed with error: {err}",
                "status_code": 400,
                "transferred": False,
                "reversed": False
            }

    @classmethod
    def get_disbursement_provider(cls):
        url = f"{cls.base_url}api/seeds/v1/get_seeds_constant/"

        headers = {
            # "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }

        try:
            r = requests.get(
                url=url,
                headers=headers,
            )
            response = r.text

            if r.status_code != 200:
                "NOT FOUND"
            else:
                response = json.loads(response)
                j_response = response.get("disbursement_source")
                data = j_response
            return data

        except Exception as err:
            # print(err)
            return "NOT FOUND"



