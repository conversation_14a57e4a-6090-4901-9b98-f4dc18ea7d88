from rest_framework import permissions, status
from rest_framework.exceptions import APIException, PermissionDenied
from django.core.cache import cache
from accounts.models import BlackListed, ConstantTable, IPWhitelist


class CanDisburseLoanException(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "message": "Agent is unable to process disbursement requests at the moment. Please contact support.",
    }
    default_code = "Not permitted"


class CannotCreatePlanException(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "message": "User is not allowed to create a savings plan at the moment. Thank you.",
    }
    default_code = "Not permitted"


class LoanIsAvailableException(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "message": "Service is currently not available.",
    }
    default_code = "Not permitted"


class IsBlackListed(permissions.IsAuthenticated):

    def has_permission(self, request, view):
        super().has_permission(request, view)
        user_instance = request.user
        email = user_instance.email

        is_not_blacklisted = BlackListed.can_disburse_loan(email=email)
        if is_not_blacklisted:
            return True
        else:
            raise CanDisburseLoanException


class CanCreatePlanPermission(permissions.IsAuthenticated):

    def has_permission(self, request, view):
        super().has_permission(request, view)
        user_instance = request.user
        email = user_instance.email

        is_not_blacklisted = BlackListed.can_create_plan(email=email)
        if is_not_blacklisted:
            return True
        else:
            raise CannotCreatePlanException


class IsLoanAvailable(permissions.IsAuthenticated):
    def has_permission(self, request, view):
        super().has_permission(request, view)
        const = ConstantTable.get_constant_table_instance()
        loan_is_available = const.is_loan_available
        if loan_is_available:
            return True
        else:
            raise LoanIsAvailableException


class IsStaffException(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "message": "You do not have the necessary permisson to perform this action",
    }
    default_code = "Not permitted"


class SavingsNotAvailableException(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = {
        "error": "error",
        "message": "We're sorry, but saving is currently not available.",
    }
    default_code = "Not Allowed"


class IsStaff(permissions.IsAuthenticated):

    def has_permission(self, request, view):
        super().has_permission(request, view)
        user_instance = request.user

        if user_instance.is_staff:
            return True
        else:
            raise IsStaffException


class IpWhiteListPermission(permissions.BasePermission):

    def has_permission(self, request, view):

        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")

        try:
            IPWhitelist.objects.get(ip_address=ip, allowed=True)
        except IPWhitelist.DoesNotExist:
            raise PermissionDenied()
        return True


class IsWhitelistedIP(permissions.BasePermission):

    def has_permission(self, request, view):
        # Get the client's IP address
        client_ip = self.get_client_ip(request)

        try:
            IPWhitelist.objects.get(ip_address=client_ip, allowed=True)
        except IPWhitelist.DoesNotExist:
            try:
                IPWhitelist.objects.get(ip_address=client_ip, allowed=False)
                raise PermissionDenied()
            except IPWhitelist.DoesNotExist:
                IPWhitelist.objects.create(ip_address=client_ip, allowed=False)
                raise PermissionDenied()

        return True

    @staticmethod
    def get_client_ip(request):
        """
        Retrieve the client's IP address from the request, handling multiple scenarios.
        """
        # Check for the 'X-Forwarded-For' header (used by proxies/load balancers)
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            # Extract the first IP address in the chain
            ip = x_forwarded_for.split(",")[0].strip()
            return ip

        # Check for the 'X-Real-IP' header (used by some proxies, e.g., Nginx)
        ip = request.META.get("HTTP_X_REAL_IP")
        if ip:
            return ip

        # # Check for other headers that might contain the IP
        # ip = (
        #     request.META.get("HTTP_CF_CONNECTING_IP")  # Cloudflare
        #     or request.META.get("HTTP_TRUE_CLIENT_IP")  # Akamai
        #     or request.META.get("HTTP_X_CLIENT_IP")  # Custom headers
        # )
        # if ip:
        #     return ip

        # Fallback to REMOTE_ADDR (direct connection or unconfigured proxy)
        return request.META.get("REMOTE_ADDR")


class CustomAPIKeyPermission(permissions.BasePermission):

    def has_permission(self, request, view):
        api_key = self.get_http_x_api_key(request)

        if not api_key:
            raise PermissionDenied("API Key is required.")

        # Check cache first
        cache_key = f"api_key_whitelist:{api_key}"
        is_allowed = cache.get(cache_key)

        if is_allowed is None:
            # If not in cache, query the database
            try:
                IPWhitelist.objects.get(api_key=api_key, allowed=True)
                cache.set(cache_key, True, timeout=6 * 60 * 60)  # Cache for 6 hours
            except IPWhitelist.DoesNotExist:
                cache.set(
                    cache_key, False, timeout=6 * 60 * 60
                )  # Cache failure for 6 hours
                raise PermissionDenied()

        elif not is_allowed:
            raise PermissionDenied()

        return True

    @staticmethod
    def get_http_x_api_key(request):
        return request.META.get("HTTP_X_API_KEY") or request.headers.get("X-Api-Key")


class AllowSavingsPermission(permissions.IsAuthenticated):
    def has_permission(self, request, view):
        super().has_permission(request, view)
        const = ConstantTable.get_constant_table_instance()
        can_save = const.can_save
        if can_save:
            return True
        else:
            raise SavingsNotAvailableException
