from django.core.exceptions import ObjectDoesNotExist
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics, permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken

from ajo.permissions import HookAuthorizationPermission

from .agency_banking import AgencyBankingClass
from .helpers import generate_micro_saver_access_token
from .models import *
from .responses import serializer_validation_error_response, value_error_response
from .serializers import *
from .services import NextOfKinService
from .utils import get_celery_worker_status, is_celery_worker_alive


# Register Client View
class RegisterAPIView(generics.GenericAPIView):
    serializer_class = RegisterSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()
        refresh = RefreshToken.for_user(user)
        return Response(
            {
                "user": UserSerializer(
                    user, context=self.get_serializer_context()
                ).data,
                "refresh": str(refresh),
                "access": str(refresh.access_token),
            }
        )


class ConstantsAPIView(generics.GenericAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = ConstantsSerializer

    def get(self, request, *args, **kwargs):
        serializer = self.serializer_class(ConstantTable.get_constant_table_instance())
        return Response(
            {"status": True, "data": serializer.data}, status=status.HTTP_200_OK
        )


class CeleryHealthCheckAPIView(generics.GenericAPIView):
    permission_classes = (HookAuthorizationPermission,)
    serializer_class = CeleryHealthCheckSerializer

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "Authorization",
                openapi.IN_HEADER,
                description="Token in the format 'Hook {token}'",
                type=openapi.TYPE_STRING,
                required=True,
            )
        ]
    )
    def get(self, request, *args, **kwargs):
        """
        This endpoint performs two types of status checks on the celery worker:
            1. It sends a simple task to return a string to the worker and expects a response
            under 5 seconds. If there is no response, please restart the server
            2. It will return the status, registered and active tasks currently working on the server

        If any of the check fails, please endeavour to restart the server or check if any task has run
        successfully

        Please note that this endpoint is not intended to run fast and will typical take about ~10 seconds to perform all the checks
        """
        send_task_test = is_celery_worker_alive()
        celery_status_checks = get_celery_worker_status()
        overall_status_checks = all(
            value is not None for value in celery_status_checks.values()
        )

        response_dictionary = {
            "send_task_test": send_task_test,
            "overall_status_checks": overall_status_checks,
            "information": celery_status_checks,
        }

        # response_serializer = self.serializer_class(response_dictionary)

        return Response(
            response_dictionary,
            status.HTTP_200_OK,
        )


class LoginWithOTP(APIView):
    serializer_class = LoginWithOTPSerializer

    @swagger_auto_schema(
        request_body=LoginWithOTPSerializer,
        responses={
            status.HTTP_201_CREATED: openapi.Schema(type=openapi.TYPE_OBJECT),
            status.HTTP_400_BAD_REQUEST: openapi.Schema(type=openapi.TYPE_OBJECT),
            status.HTTP_401_UNAUTHORIZED: openapi.Schema(type=openapi.TYPE_OBJECT),
        },
    )
    def post(self, request):
        """
        Parameters:
        - request: The HTTP request.
        Returns:
        - Response: HTTP response.
        """

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        phone_number = serializer.validated_data["phone_number"]
        otp = serializer.validated_data["otp"]
        referal_code = serializer.validated_data.get("referal_code")

        # Verify OTP
        from ajo.models import AjoUser
        from ajo.utils.ussd_utils import verify_ussd_otp

        otp_verifcation = verify_ussd_otp(
            otp=otp,
            phone_number=phone_number,
        )

        # print(otp_verifcation)

        if not otp_verifcation:
            response = {
                "status": False,
                "error": "613",
                "message": "invalid OTP, please try again",
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        validate_phone = AgencyBankingClass.get_user_with_phone(
            phone_number=phone_number, referal_code=referal_code
        )
        # print(validate_phone)

        message = "User Exists on Agency Or Error Occured"
        response_code = "99"

        if validate_phone["search_code"] == "129":
            check_ajo_saver = AjoUser.objects.filter(
                onboarding_verified=True, phone_number__startswith=phone_number
            ).first()

            message = "No Ajo User Exists"
            response_code = "09"

            if check_ajo_saver:
                first_name = check_ajo_saver.first_name
                last_name = check_ajo_saver.last_name
                email = f"{first_name}.{phone_number}@{last_name}.com".lower()
                state = check_ajo_saver.state
                lga = check_ajo_saver.lga
                street = check_ajo_saver.address
                nearest_landmark = check_ajo_saver.trade_location
                business_name = check_ajo_saver.trade
                gender = check_ajo_saver.gender.title()

                payload = {
                    "phone_number": phone_number,
                    "first_name": first_name,
                    "last_name": last_name,
                    "email": email,
                    "username": phone_number,
                    "state": state,
                    "lga": lga,
                    "nearest_landmark": nearest_landmark,
                    "street": street,
                    "type_of_user": "MICRO_SAVER",
                    "referral_code": referal_code,
                    "business_name": business_name,
                    "gender": gender,
                }

                message = "Could not create user"
                response_code = "25"

                agency_response = AgencyBankingClass.create_account_on_liberty(payload)
                # print(agency_response)

                if (
                    agency_response.get("status") == "success"
                    or agency_response.get("error_code") == "10"
                ):

                    customer_user_id = agency_response["user_id"]
                    # Create User
                    CustomUser.objects.get_or_create(
                        email=email,
                        customer_user_id=customer_user_id,
                        user_phone=f"234{phone_number}",
                    )

                    # Log Login
                    OTPLoginLog.objects.create(phone_number=phone_number)

                    user_id = agency_response.get("data").get("user_id")
                    access_token = generate_micro_saver_access_token(user_id)

                    response = {
                        "status": "success",
                        "message": "login successful",
                        "response_code": "00",
                        "access": access_token,
                    }

                    return Response(response, status=status.HTTP_200_OK)

        elif validate_phone["search_code"] == "100" and validate_phone.get(
            "message", {}
        ).get("user_id"):

            user_id = validate_phone.get("message", {}).get("user_id")
            access_token = generate_micro_saver_access_token(user_id)

            response = {
                "status": "success",
                "message": "login successful",
                "response_code": "00",
                "access": access_token,
            }
            return Response(response, status=status.HTTP_200_OK)

        response = {
            "status": "error",
            "message": message,
            "response_code": response_code,
        }

        return Response(response, status=status.HTTP_400_BAD_REQUEST)


class NextOfKinAPIView(generics.GenericAPIView):
    serializer_class = NextOfKinSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        user = request.user

        serializer = self.serializer_class(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        data = dict(serializer.validated_data)

        try:
            nok = NextOfKinService(user=user).create_next_of_kin_for_user(data=data)
        except (ValueError, TypeError) as err:
            return value_error_response(error=err)

        response_serializer = self.serializer_class(nok)

        return Response(
            {
                "status": True,
                "data": response_serializer.data,
            },
            status.HTTP_201_CREATED,
        )

    def get(self, request, *args, **kwargs):
        user = request.user

        try:
            nok = user.next_of_kin
        except ObjectDoesNotExist as err:
            return Response(
                {
                    "error": "404",
                    "status": False,
                    "message": "no next of kin set for this user",
                },
                status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.serializer_class(nok)

        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status.HTTP_200_OK,
        )


class UserChecksAPIView(generics.GenericAPIView):
    serializer_class = UserChecksSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request, *args, **kwargs):
        user = request.user
        data = {}
        try:
            user.next_of_kin
            data["next_of_kin"] = True
        except ObjectDoesNotExist as err:
            data["next_of_kin"] = False

        serializer = self.serializer_class(data)
        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status.HTTP_200_OK,
        )


# class TransactionPinAPIView(generics.GenericAPIView):

#     permission_classes = (permissions.IsAuthenticated,)
#     serializer_class = TransactionPinSerializer

#     def get_object(self, email):
#         try:
#             user = CustomUser.objects.get(email=email)
#             return user
#         except:
#             raise serializers.ValidationError("User does not exist in the database")

#     def post(self, request, *args, **kwargs):
#         """Set a transaction pin with your password for the first time, doesn't allow you to reset the pin."""
#         data = request.data
#         user = self.get_object(request.user.email)
#         serializer = self.serializer_class(instance=user, data=data)

#         if serializer.is_valid(raise_exception=True):
#             password = data["password"]
#             if user.transaction_pin:
#                 return Response(
#                     {"message": "You have set a transaction pin previously"}, status=status.HTTP_400_BAD_REQUEST
#                 )
#             if check_password(password, request.user.password):
#                 serializer.save()
#                 return Response({"message": "Transaction pin set successfully"}, status=status.HTTP_200_OK)
#             else:
#                 return Response({"message": "Incorrect password, try again"}, status=status.HTTP_400_BAD_REQUEST)


# class FirstEntryPointAPIView(generics.GenericAPIView):
#     permission_classes = [
#         permissions.IsAuthenticated,
#     ]
#     serializer_class = None

#     def get(self, request, *args, **kwargs):
#         # email = request.headers.get("Email-Address", None)
#         # auth_header = request.headers.get("Authorization", "")

#         # token_type, _, access_token = auth_header.partition(" ")

#         # # if email is None:
#         # #     return Response({"message": "no email in header"}, status=status.HTTP_400_BAD_REQUEST)

#         # payload = decode_access_token(access_token=access_token, signature=settings.SECRET_KEY)

#         # if payload.get("user_id"):
#         #     print(payload)
#         #     jwt_user_id = payload["user_id"]
#         #     print(jwt_user_id)

#         #     user_qs = CustomUser.objects.filter(email=email).last()
#         #     if user_qs is not None:
#         #         if user_qs.customer_user_id == jwt_user_id:
#         #             return Response({"message": "proceed"}, status=status.HTTP_200_OK)

#         # else:
#         #     return Response(payload, status=status.HTTP_400_BAD_REQUEST)

#         return Response(
#             {
#                 # "email": email,
#                 # "access_token": access_token,
#                 "email": "email",
#             }
#         )


class GetAgentDetails(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):

        user_result = CustomUser.objects.filter(id=request.user.id)
        result_values = user_result.values(
            "email",
            "username",
            "user_type",
            "account_type",
            "user_phone",
            "user_branch",
            "bvn_number",
            "kyc_level",
            "performance_status",
            "referred_emails",
            "referrals_count",
            "referral_code",
        ).first()
        data = {"status": True, "data": result_values}

        return Response(data=data, status=status.HTTP_200_OK)
