from rest_framework import serializers

from .models import ConstantTable, CustomUser, NextOfKin


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomUser
        fields = ("id", "username", "email")


class LoginSerializer(serializers.ModelSerializer):
    username = serializers.CharField(max_length=20, min_length=2)
    password = serializers.CharField(max_length=30, min_length=6, write_only=True)

    class Meta:
        model = CustomUser
        fields = ("username", "password")


class RegisterSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomUser
        fields = ("username", "email", "password")
        extra_kwargs = {"password": {"write_only": True}}

    def create(self, validated_data):
        user = CustomUser.objects.create_user(**validated_data)

        return user


# class TransactionPinSerializer(serializers.ModelSerializer):
#     transaction_pin = serializers.CharField(max_length=4, min_length=4)
#     password = serializers.Char<PERSON>ield(min_length=6)

#     class Meta:
#         model = CustomUser
#         fields = [
#             "transaction_pin",
#             "password",
#         ]

#     def validate_transaction_pin(self, value):
#         if not value.isdigit():
#             raise serializers.ValidationError("Enter a 4 digit pin")
#         return value

#     def update(self, instance, validated_data):
#         instance.transaction_pin = validated_data.get("transaction_pin", instance.transaction_pin)
#         instance.save()
#         return instance


class ConstantsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ConstantTable
        fields = [
            "chestlock_minimum_target",
            "chestlock_interest_rate",
            "halal_minimum_target",
            "quicksavings_minimum_target",
            "dojah_bvn_charge",
            "dojah_nin_charge",
            "snpl_savings_milestone",
        ]


class CeleryHealthCheckSerializer(serializers.Serializer):
    send_task_test = serializers.BooleanField(help_text="if the task is returned in the allotted time")
    overall_status_checks = serializers.BooleanField(help_text="if none of the checks do not return None")
    information = serializers.DictField(child=serializers.Field())


class LoginWithOTPSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=11, min_length=11)
    otp = serializers.CharField(max_length=6, min_length=6)
    referal_code = serializers.CharField(required=True, allow_null=True, allow_blank=True)


class NextOfKinSerializer(serializers.ModelSerializer):

    class Meta:
        model = NextOfKin
        fields = [
            "full_name",
            "phone_number",
            "email",
            "address",
            "relationship",
        ]


class UserChecksSerializer(serializers.Serializer):
    next_of_kin = serializers.BooleanField()
