import base64
from typing import Any, Dict, List, Optional

from cryptography.fernet import <PERSON><PERSON><PERSON>
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.core.exceptions import ObjectDoesNotExist

from ajo.models import AjoUser
from payment.model_choices import PlanType

from .agency_banking import AgencyBankingClass
from .model_choices import InterestType, UserType
from .models import InterestsPaidTable, NextOfKin, SMSResponse


class InterestsPaidTableMethods:
    @staticmethod
    def create_upfront_interest_instance(
        user: AbstractUser,
        plan_type: PlanType,
        plan_quotation_id: str,
        total_interest_paid: float,
        interest_type: InterestType = InterestType.ONEOFF,
    ) -> None:
        InterestsPaidTable.objects.create(
            user=user,
            plan_type=plan_type,
            plan_quotation_id=plan_quotation_id,
            total_interest_paid=total_interest_paid,
            interest_type=interest_type,
        )


class SMSResponseService:
    @staticmethod
    def create_sms_response_instance(
        user: AbstractUser,
        receiver: str,
        payload: str,
        message: str,
        ajo_user: AjoUser | None = None,
    ):
        sms_instance = SMSResponse.objects.create(
            user=user,
            receiver=receiver,
            payload=payload,
            message=message,
            # onboarded_user=ajo_user,
        )

        return sms_instance

    @staticmethod
    def update_after_request(
        sms_instance: SMSResponse,
        response_payload: str,
        sent: bool = False,
    ):
        sms_instance.response_payload = (response_payload,)
        sms_instance.sent = sent
        sms_instance.save()


class NextOfKinService:

    def __init__(self, user: Optional[AbstractUser] = None, ajo_user: Optional[AjoUser] = None) -> None:
        """
        Instantiate this class with either a User instance or an AjoUser instance

        Args:
            user (Optional[AbstractUser], optional): the user instance. Defaults to None.
            ajo_user (Optional[AjoUser], optional): the ajo user instance. Defaults to None.

        Raises:
            TypeError: please pass either a user or an ajo user
        """
        error = TypeError("please pass either a user or an ajo user")
        if not user and not ajo_user:
            raise error
        if user and ajo_user:
            raise error

        self.user = user
        self.ajo_user = ajo_user

    def create_next_of_kin_for_user(self, data: Dict[str, str]) -> NextOfKin:
        """
        Create the next of kin for a user

        Args:
            data (Dict[str, str]): the data should contain full_name, email, phone_number, address, relationship

        Raises:
            ValueError: class was instantiated for ajo user
            ValueError: next of kin exists for this user

        Returns:
            NextOfKin: the Next of kin object
        """
        if not self.user:
            raise ValueError("class was instantiated for ajo user")

        next_of_kin, created = NextOfKin.objects.update_or_create(
            user=self.user, defaults=data
        )


        return next_of_kin

    def create_next_of_kin_for_ajo_user(self, data: Dict[str, str]) -> NextOfKin:
        """
        Create the next of kin for an ajo user

        Args:
            data (Dict[str, str]): the data should contain full_name, email, phone_number, address, relationship

        Raises:
            ValueError: class was instantiated for ajo user
            ValueError: next of kin exists for this user

        Returns:
            NextOfKin: the Next of kin object
        """
        if not self.ajo_user:
            raise ValueError("this class was instantiated for user")


        next_of_kin, created = NextOfKin.objects.update_or_create(
            ajo_user=self.ajo_user, defaults=data
        )


        return next_of_kin


class UserService:
    @staticmethod
    def get_user_instance(id: int) -> AbstractUser:
        """
        Gets the user instance

        Args:
            id (int): the id of the user

        Raises:
            ValueError: "user not found"

        Returns:
            AbstractUser: the user model instance
        """
        try:
            return get_user_model().objects.get(id=id)
        except get_user_model().DoesNotExist:
            raise ValueError("user not found")

    @staticmethod
    def get_user_type(user, access_token: str = None) -> UserType | Exception:
        """
        Obtains the User Type

        Args:
            user (CustomUser): user object
            access_token (str, optional): Access token of the user. Defaults to None.

        Raises:
            Exception: "No access token passed"
            Exception: "{response of the request to obtain user information}"

        Returns:
            str | Exception: either the user_type is returned or an Exception is raised
        """
        # try to obtain the user type
        user_type = user.user_type

        # if nothing is returned
        if user_type is None:
            # if no access token was passed
            if access_token is None:
                raise Exception("no access token passed to obtain the user type")

            # obtain user information from a request
            user_info_dict = AgencyBankingClass.get_all_user_information(access_token=access_token)

            # if the request returns a False
            if user_info_dict.get("status") is False:
                raise Exception(f"{user_info_dict}")

            # obtain the type of user from information returned
            type_of_user = user_info_dict.get("user_data").get("type_of_user")

            # if something is found
            if type_of_user:
                user.user_type = user_type = getattr(UserType, type_of_user.upper())
                user.save()

        else:
            user_type = getattr(UserType, user_type.upper())

        return user_type

    @staticmethod
    def pad_secret_key(secret_key: str) -> bytes:
        key_length = len(secret_key)
        if key_length < 32:
            secret_key = secret_key.ljust(32, "X")
        elif key_length > 32:
            secret_key = secret_key[:32]

        return base64.urlsafe_b64encode(secret_key.encode())

    @staticmethod
    def encrypt_bvn(user, bvn: str) -> None:
        """
        This saves the BVN of the user

        Args:
            bvn (str): the bvn of the user

        Returns: None
        """
        key = UserService.pad_secret_key(settings.SECRET_KEY)  # Ensure the key is 32 bytes
        cipher_suite = Fernet(key)
        encrypted_value = cipher_suite.encrypt(bvn.encode())
        user.bvn_number = encrypted_value
        user.save()

    def decrypt_bvn(user: AbstractUser) -> str | None:
        if user.bvn_number:
            key = UserService.pad_secret_key(settings.SECRET_KEY)
            cipher_suite = Fernet(key)
            user_bvn_bytes = bytes(user.bvn_number)
            decrypted_value = cipher_suite.decrypt(user_bvn_bytes).decode()
            return decrypted_value
        return None

    @staticmethod
    def get_user_bvn(user, access_token: str) -> str | Exception:
        """
        Obtains the BVN of the user

        Args:
            user (_type_): user object
            access_token (str): access token of the user

        Raises:
            Exception: "{response of the information from request if request failed}"

        Returns:
            str: the bvn of the user
        """
        # try to obtain the bvn from the model
        bvn_number = UserService.decrypt_bvn(user)
        # if the BVN is found
        if bvn_number:
            # return the obtained BVN
            return bvn_number

        user_info_dict = AgencyBankingClass.get_all_user_information(access_token=access_token)

        # if the request returns a False
        if user_info_dict.get("status") is False:
            raise Exception(f"{user_info_dict}")

        bvn_number = user_info_dict.get("user_data").get("bvn_number")

        if not bvn_number:
            raise Exception("problem obtaining BVN, please ensure you registered with your BVN on the application")

        UserService.encrypt_bvn(user, bvn_number)

        return bvn_number

    @classmethod
    def add_referred_emails(cls, user: AbstractUser, referred_emails: List[str]):
        if not user.referred_emails:
            user.referred_emails = ",".join(referred_emails)
        else:
            existing_emails = user.referred_emails.split(",")
            for referred_email in referred_emails:
                if referred_email not in existing_emails:
                    existing_emails.append(referred_email)
            user.referred_emails = ",".join(existing_emails)
        user.save()

    @classmethod
    def update_user_fields_from_agency(cls, user, access_token: str) -> AbstractUser:
        """
        Update the user fields from Agency banking

        Args:
            user (AbstractUser): the user model
            access_token (str): the access token of the user

        Raises:
            ValueError: error occurred while obtaining user information

        Returns:
            AbstractUser: the updated user instance
        """
        user_data_response = AgencyBankingClass.get_all_user_information(access_token=access_token)
        user_data: dict = user_data_response.get("user_data")
        if user_data:
            user.user_type = user_data.get("type_of_user", None)
            user.user_phone = user_data.get("phone_number", None)
            user.user_branch = user_data.get("user_branch", None)
            user.first_name = user_data.get("first_name")
            user.last_name = user_data.get("last_name")
            user.kyc_level = user_data.get("kyc_level", 0)

            user.referral_code = user_data_response.get("referral_details", {}).get("referral_code", None)
            user.referral_counts = user_data_response.get("referral_details", {}).get("referrals_count", None)
            user.save()

            referred_emails = user_data_response.get("referral_details", {}).get("referrals_emails", [])
            if referred_emails:
                cls.add_referred_emails(user=user, referred_emails=referred_emails)

        else:
            raise ValueError("error occurred while obtaining user information")

        return user
