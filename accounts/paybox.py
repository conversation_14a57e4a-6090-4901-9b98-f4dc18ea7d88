import json
from typing import Any, Dict, List

import requests
from django.conf import settings
from requests.auth import HTTPBasicAuth


class PayBox:
    BASE_URL: str = settings.PAYBOX_URL
    TOKEN: str = settings.PAYBOX_TOKEN
    _BASE_HEADERS = {"Content-Type": "application/json"}
    _AUTHORIZED_HEADERS = {
        **_BASE_HEADERS,
        "Authorization": f"Bearer {TOKEN}",
    }

    @classmethod
    def register_sale(
        cls,
        company: str,
        branch: str,
        items: List[Dict[str, str | int | float]],
        access_token: str | None = None,
    ) -> Dict[str, Any]:
        """
        Register a sale and reduce the stock on PayBox

        Returns:
            Dict[str, Any]: {
                'status': True,
                'data': {
                    "status": "success",
                    "status_code": 201,
                    "data": {
                        "invoice_id": "INV-JUN-0000040835",
                        "batch_id": "20240616-mxXJmepY",
                        "sub_total": 99750,
                        "discount_value": 1250,
                        "delivery_fee": 5750,
                        "vat": 7.5,
                        "total": 111731.25,
                        "items": [
                            {
                                "item_description": "Sparkling Water",
                                "unit_price": 1750,
                                "quantity": 15
                            },
                        ]
                    },
                    "errors": null
            }
        """
        url = f"{cls.BASE_URL}/api/v1/sales/register/"

        headers = cls._AUTHORIZED_HEADERS
        if access_token:
            headers["Authorization"] = f"Bearer {access_token}"

        payload = json.dumps(
            {
                "company": company,
                "branch": branch,
                "sales": items,
                # "sales": [
                #     {"item_id": "3e3a3a67-e30a-4fb4-8fc4-d3504759ddf6", "quantity": 20, "amount": 1750},
                #     {"item_id": "cebea349-3023-44d4-9a11-0f5db8844190", "quantity": 67, "amount": 1750},
                #     {"item_id": "a1e72a39-dad6-46e8-8cb5-f4f26618f6db", "quantity": 12, "amount": 1750},
                # ],
                "device": "WEB",
                "discount_value": None,
                "delivery_fee": None,
            }
        )

        try:
            r = requests.post(
                url=url,
                headers=cls._AUTHORIZED_HEADERS,
                data=payload,
                timeout=90,
            )

            response = r.json()

            new_response = {
                "status": None,
                "data": response,
            }

            new_response["status"] = True if r.status_code == 201 else False

            return new_response

        except requests.RequestException as err:
            return {
                "status": False,
                "message": "encountered an err",
                "error_message": str(err),
            }
