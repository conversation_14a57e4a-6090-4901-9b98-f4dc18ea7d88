

from rest_framework import serializers
from accounts.models import CustomUser
from ajo.models import BankAccountDetails
from loans.models import Assignment, AjoLoan, AjoLoanRepayment, FraudMarkers, GeneralComments
from .models import SMS, CallRecord, SMSTemplates



class LoanSerializerOut(serializers.ModelSerializer):
    agent = serializers.SerializerMethodField()
    borrower = serializers.SerializerMethodField()
    guarantor = serializers.SerializerMethodField()
    
    class Meta:
        model = AjoLoan
        fields = [
            "id",
            "agent",
            "borrower",
            "borrower_full_name",
            "guarantor_full_name",
            "guarantor_phone_number",
            "guarantor",
            "amount",
            "agent_disbursement_amount",
            "status",
            "performance_status",
            "total_paid_amount",
            "total_outstanding_balance",
            "count_of_recorded_repayments",
            "daily_repayment_amount",
            "expected_end_date",
            "assigned_agent",
            "loan_due_date",
            "moved_to_recovery",
            "promise_to_pay",
            "recovered",
            "promise_status",
            "comment",
        ]

    def get_agent(self, instance):
        if instance.agent:
            return {
                "id": instance.agent.id,
                "agent_name": f"{instance.agent.first_name} {instance.agent.last_name}",
                "phone": instance.agent.user_phone,
                "branch": instance.agent.user_branch,
                "email": instance.agent.email
            }
        
    def get_borrower(self, instance):
        if instance.borrower:
            bank_account = BankAccountDetails.objects.filter(
                ajo_user=instance.borrower,
                form_type="LOAN_RECOVERY",
            ).first()
            result = {
                "full_name": f"{instance.borrower.first_name} {instance.borrower.last_name}",
                "phone": instance.borrower.phone_number,
                "first_name": instance.borrower.first_name,
                "last_name": instance.borrower.last_name,
                "branch": instance.borrower.branch_name,
                "gender": instance.borrower.gender,
                "marital_status": instance.borrower.marital_status,
                "age": instance.borrower.age,
                "state": instance.borrower.state,
                "lga": instance.borrower.lga,
                "address": instance.borrower.address,
                "dob": instance.borrower.dob,
                "city": instance.borrower.location_city,
            }

        if bank_account:
            result.update({
                "account_number": bank_account.account_number,
                "account_name": bank_account.account_name,
                "bank_name": bank_account.bank_name,
                "bank_code": bank_account.bank_code,
                "account_type": bank_account.account_type,
            })
        else:
            result.update({
                "account_number": "",
                "account_name": "",
                "bank_name": "",
                "bank_code": "",
                "account_type": "",
            })

        return result

        
    def get_guarantor(self, instance):
        if instance.guarantor:
            return {
                "phone": instance.guarantor.phone_number,
                "surname": instance.guarantor.surname,
                "last_name": instance.guarantor.last_name,
                "email": instance.guarantor.email,
                "relationship_to_borrower": instance.guarantor.relationship_to_borrower,
                "age": instance.guarantor.age,
                "date_of_birth": instance.guarantor.date_of_birth,
                "address": instance.guarantor.address,
            }
        


class LoanSerializerDetailOut(serializers.ModelSerializer):
    agent = serializers.SerializerMethodField()
    borrower = serializers.SerializerMethodField()
    guarantor = serializers.SerializerMethodField()
    class Meta:
        model = AjoLoan
        exclude = []

    def get_agent(self, instance):
        if instance.agent:
            return {
                "id": instance.agent.id,
                "agent_name": f"{instance.agent.first_name} {instance.agent.last_name}",
                "phone": instance.agent.user_phone,
                "branch": instance.agent.user_branch,
            }
        
    def get_borrower(self, instance):
        if instance.borrower:
            return {
                "id": instance.borrower.id,
                "full_name": f"{instance.borrower.first_name} {instance.borrower.last_name}",
                "phone": instance.borrower.phone_number,
                "first_name": instance.borrower.first_name,
                "last_name": instance.borrower.last_name,
                "branch": instance.borrower.branch_name,
                "gender": instance.borrower.gender,
                "marital_status": instance.borrower.marital_status,
                "age": instance.borrower.age,
                "state": instance.borrower.state,
                "lga": instance.borrower.lga,
                "address": instance.borrower.address,
                "dob": instance.borrower.dob,
                "city": instance.borrower.location_city,
                # "image": instance.borrower.image,
            }
        
    def get_guarantor(self, instance):
        if instance.guarantor:
            return {
                "phone": instance.guarantor.phone_number,
                "surname": instance.guarantor.surname,
                "last_name": instance.guarantor.last_name,
                "email": instance.guarantor.email,
                "relationship_to_borrower": instance.guarantor.relationship_to_borrower,
                "age": instance.guarantor.age,
                "date_of_birth": instance.guarantor.date_of_birth,
                "address": instance.guarantor.address,
                "image": instance.guarantor.base_64_img_string,
                "image2": instance.guarantor.snapped_image,
            }
    


        


class LoanSerializerIn(serializers.ModelSerializer):
    # agent = serializers.SerializerMethodField()
    class Meta:
        model = AjoLoan
        fields = [
            "promise_to_pay",
            "moved_to_recovery",
            "promise_status",
            "additional_instruction",
        ]


class AjoLoanCallerModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = AjoLoan
        fields = [
            
            'moved_to_recovery',
            'recovered',
            'promise_to_pay',
            'promise_to_pay_amount',
            'promise_status',
            'additional_instruction',
            'comment',
            'recovery_reason'
        ]
    
    # def update(self, instance, validated_data):
    #     instance.moved_to_recovery = validated_data.get('moved_to_recovery', instance.moved_to_recovery)
    #     instance.recovered = validated_data.get('recovered', instance.recovered)
    #     instance.promise_to_pay = validated_data.get('promise_to_pay', instance.promise_to_pay)
    #     instance.promise_status = validated_data.get('promise_status', instance.promise_status)
    #     instance.additional_instruction = validated_data.get('additional_instruction', instance.additional_instruction)
    #     instance.comment = validated_data.get('comment', instance.comment)
    #     instance.save()
    #     return instance
    


class AjoRepaymentHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = AjoLoanRepayment
        exclude = []




# class DebtorsSerializerOut(serializers.ModelSerializer):
#     loan = serializers.SerializerMethodField()
    

#     class Meta:
#         model = Debtor
#         field = [
#             "loan_due_date",
#             "moved_to_recovery",
#             "recovered",
#             "promise_status",
#             "comment"
#         ]

#     def get_loan(self, instance):
#         if instance.loan:
#             return {
#                 "agent_name": f"{instance.loan.agent.first_name} {instance.loan.agent.last_name}",
#                 "agen_phone": instance.loan.agent.user_phone,
#                 "branch": instance.loan.agent.user_branch,
#                 "borrower_name": instance.loan.borrower_full_name,
#                 "borrower_phone": instance.loan.borrower_phone_number,
#                 "loan_amount": instance.loan.amount,
#                 "status": instance.loan.status,
#                 "total_amount_paid": instance.loan.total_paid_amount,
#                 "count_of_recorded_repayments": instance.loan.count_of_recorded_repayments,
#                 "daily_repayment_amount": instance.loan.daily_repayment_amount,
#                 "expected_end_date": instance.loan.expected_end_date,
#             }
#         return "No loan created"
        






class AgentSerializerOut(serializers.ModelSerializer):
    
    class Meta:
        model = CustomUser
        fields = [
            "id",
            "first_name",
            "last_name",
            "call_agent_id",
            "customer_user_id",
            "is_call_agent_admin",
            "is_caller_active",
            "daily_total_expected_calls",
            "no_successful_calls",
            "bucket"
        ]





class CallRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = CallRecord
        fields = '__all__'



class MakeCallSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=13)
    dnnumber = serializers.CharField(max_length=256)



class AjoLoanRepaymentDetailSerializer(serializers.ModelSerializer):
    # ajo_loan = serializers.SerializerMethodField(read_only=True)
    ajo_user = serializers.SerializerMethodField(read_only=True)
    repayment_amount_actual = serializers.SerializerMethodField(read_only=True)
    
    class Meta:
        model = AjoLoanRepayment
        fields = [
            'id',
            'repayment_type', 'loan_amount', 'repayment_amount',
            'health_insurance', 'paid_date', 'repayment_ref',
            'settlement_status', 'repayment_meta_response',
            'ledger_amount', 'comment', "repayment_amount_actual",
            "ajo_user",
        ]
        
    def get_repayment_amount_actual(self, instance):
        return instance.repayment_amount
    
    
    def get_ajo_user(self, instance):
        if instance.borrower:
            return instance.borrower.phone_number

    
    # def get_ajo_loan(self, instance):
    #     if instance.ajo_loan:
    #         return {
    #             'id': instance.ajo_loan.id,
    #             'loan_amount': getattr(instance.ajo_loan, 'amount', None),
    #             'loan_status': getattr(instance.ajo_loan, 'status', None),
    #             'loan_type': getattr(instance.ajo_loan, 'loan_type', None)
    #         }
    
        
class CommentsSerializer(serializers.ModelSerializer):
    class Meta:
        model = GeneralComments
        fields = ['id', 'loan', 'comment', 'call_agent_username', 'created_at']
        read_only_fields = ["id", "loan", "created_at"]
    
    def validate(self, data):
        if not data.get('comment'):
            raise serializers.ValidationError({"comment": "Comment cannot be empty"})
        return data


class FraudMarkersSerializer(serializers.ModelSerializer):
    class Meta:
        model = FraudMarkers
        fields = [
            'id',
            'fraud_loan',
            'unremitted_collections',
            'partly_remitted_collections',
            'others',
            'additional_comment'
        ]
        read_only_fields = ['id']



class SMSTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = SMSTemplates
        fields = "__all__"


class SMSSerializer(serializers.ModelSerializer):
    class Meta:
        model = SMS
        fields = [
            "id", "phone_number", "full_name", "message"
        ]
        read_only_fields = ["id"]

