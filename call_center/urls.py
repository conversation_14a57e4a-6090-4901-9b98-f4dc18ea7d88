

from django.urls import path
from .views import (
    AgentBucketView, BucketDesignationAPIView, CommentView, DebtorsListAPIView, AgentsListAPIView, GetAgentSupDetailAPIView, LoanStatisticsAPIView, DebtorAPIViewDetail,
    AjoRepaymentView, FraudMarkersView,FraudLoanView, PopulateLocationAPIView, RecoveryLoansView, MoveToRecoveryAPIView, SMSTemplateView, SMSView
) 






urlpatterns = [
    path("debtor_list/", DebtorsListAPIView.as_view(), name="debtor_list"),
    path("agent_list/", AgentsListAPIView.as_view(), name="agent_list"),
    path("loan_statistics/", LoanStatisticsAPIView.as_view(), name="loan_statistics_list"),
    path("debtor_detail/<int:id>/", DebtorAPIViewDetail.as_view(), name="debtor_detail"),
    path("repayment_history/borrower/<int:borrower_id>/", AjoRepaymentView.as_view(), name="repayment_history"),
    path("comments/loan/<int:loan_id>/", CommentView.as_view(), name="comment_history"),
    path("fraud_marker/<int:loan_id>/", FraudMarkersView.as_view(), name="fraud_loan"),
    path("fraud/loan/", FraudLoanView.as_view(), name="fraud_loan"),
    path("recovery_loans/", RecoveryLoansView.as_view(), name='recovery_loans'),
    path('agent/bucket/', AgentBucketView.as_view(), name='agent-bucket'),
    path('recovery/<int:id>/', MoveToRecoveryAPIView.as_view(), name='recovery'),
    path('designation', BucketDesignationAPIView.as_view(), name='designation'),
    path('sms_template', SMSTemplateView.as_view(), name='sms_template'),
    path('send_sms', SMSView.as_view(), name='send_sms'),
    path('populate-location', PopulateLocationAPIView.as_view(), name='populate_location'),
    path('supervisor-detail', GetAgentSupDetailAPIView.as_view(), name='supervisor_detail'),
    
]