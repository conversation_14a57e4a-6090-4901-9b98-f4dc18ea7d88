

from datetime import timezone
from accounts.models import CustomUser
from loans.enums import LoanPerformanceStatus, LoanStatus
from loans.models import AjoLoan
from django.db.models import Q



def categorize_debtors_into_buckets():

    today = timezone.now().date()

    query =(
        Q(performance_status=LoanPerformanceStatus.DEFAULTED)
        | Q(performance_status=LoanPerformanceStatus.PAST_MATURITY)
        | Q(performance_status=LoanPerformanceStatus.OWED_BALANCE)
    ) 
    
    loans = AjoLoan.objects.filter(
        query,
        total_outstanding_balance__gt=0,
        # status=LoanStatus.ACTIVE,
        is_disbursed=True,
    )
    
    bucket_a = []
    bucket_b = []
    bucket_c = []
    bucket_d = []
    
    for loan in loans:
        if loan.loan_due_date:
            due_date = loan.loan_due_date
        elif loan.expected_end_date:
            due_date = loan.expected_end_date
        else:
            continue
            
        days_overdue = (today - due_date).days
        
        if days_overdue <= 0:
            continue
        elif 1 <= days_overdue <= 30:
            bucket_a.append(loan.id)
        elif 31 <= days_overdue <= 60:
            bucket_b.append(loan.id)
        elif 61 <= days_overdue <= 90:
            bucket_c.append(loan.id)
        else: 
            bucket_d.append(loan.id)
    
    return {
        'A': bucket_a,
        'B': bucket_b,
        'C': bucket_c,
        'D': bucket_d
    }

def assign_buckets_to_agents():
    
    call_agents = CustomUser.objects.filter(
        is_call_agent=True,
        # is_caller_active=True
    )
    
    if not call_agents.exists():
        return "No active call agents found"

    buckets = categorize_debtors_into_buckets()
    
    total_debtors = sum(len(bucket) for bucket in buckets.values())

    agents_count = call_agents.count()
    avg_debtors_per_agent = total_debtors / agents_count if agents_count > 0 else 0
    
    for agent in call_agents:
        agent.bucket = []
        agent.save()
    
    for bucket_name in ['D', 'C', 'B', 'A']:
        bucket_debtors = buckets[bucket_name]
        
        if not bucket_debtors:
            continue

        agents_by_workload = sorted(call_agents, key=lambda a: len(a.bucket))
        
        for i, debtor_id in enumerate(bucket_debtors):
            agent = agents_by_workload[i % agents_count]
            
            if agent.bucket is None:
                agent.bucket = []
                
            agent.bucket.append(debtor_id)
            agent.bucket_designation = bucket_name
            agent.daily_total_expected_calls = len(agent.bucket)
            agent.save()
    
    return f"Successfully assigned {total_debtors} debtors to {agents_count} agents"




def get_agent_assigned_debtors(agent_id):

    try:
        agent = CustomUser.objects.get(id=agent_id, is_call_agent=True)
        
        if not agent.bucket:
            return []
            
        assigned_loans = AjoLoan.objects.filter(id__in=agent.bucket)
        
        debtors_data = []
        for loan in assigned_loans:
            borrower_name = loan.borrower_full_name or (loan.borrower.full_name if loan.borrower else "Unknown")
            borrower_phone = loan.borrower_phone_number or (loan.borrower.phone_number if loan.borrower else "Unknown")
            
            if loan.loan_due_date:
                days_overdue = (timezone.now().date() - loan.loan_due_date).days
            elif loan.expected_end_date:
                days_overdue = (timezone.now().date() - loan.expected_end_date).days
            else:
                days_overdue = "Unknown"
                
            debtors_data.append({
                'loan_id': loan.id,
                'borrower_name': borrower_name,
                'borrower_phone': borrower_phone,
                'outstanding_amount': loan.total_outstanding_balance,
                'days_overdue': days_overdue,
                'bucket': agent.bucket_designation,
                'promise_to_pay': loan.promise_to_pay,
                'promise_date': loan.promise_status,
                'promise_amount': loan.promise_to_pay_amount
            })
            
        return debtors_data
        
    except CustomUser.DoesNotExist:
        return "Agent not found"
    



def record_call_outcome(loan_id, agent_id, call_status, notes=None, promise_date=None, promise_amount=None):

    try:
        loan = AjoLoan.objects.get(id=loan_id)
        agent = CustomUser.objects.get(id=agent_id, is_call_agent=True)
        
        if call_status == 'successful':
            agent.no_successful_calls += 1
            
            if promise_date:
                loan.promise_to_pay = True
                loan.promise_status = promise_date
                loan.promise_to_pay_amount = promise_amount or loan.total_outstanding_balance
                
        elif call_status == 'resolved':
            loan.resolved = True
            
            if loan.id in agent.bucket:
                agent.bucket.remove(loan.id)
                agent.daily_total_expected_calls = len(agent.bucket)
        
        if notes:
            loan.comment = notes

        loan.save()
        agent.save()
        
        return f"Call outcome recorded: {call_status}"
        
    except AjoLoan.DoesNotExist:
        return "Loan not found"
    except CustomUser.DoesNotExist:
        return "Agent not found"



class DailyBucketAssignmentTask:
    @staticmethod
    def execute():
        buckets = categorize_debtors_into_buckets()
        
        bucket_counts = {name: len(ids) for name, ids in buckets.items()}

        assignment_result = assign_buckets_to_agents()
        
        return {
            'bucket_counts': bucket_counts,
            'assignment_result': assignment_result,
            'execution_time': timezone.now()
        }