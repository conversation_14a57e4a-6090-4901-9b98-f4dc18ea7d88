

from django.contrib import admin
from loans.models import Assignment

from .models import AgentLocations, Buckets, CallRecord, SMS, SMSTemplates

# Register your models here.


class callRecordAdmin(admin.ModelAdmin):
    list_display = [
        "phone_number", "start_time", "end_time",
        "duration", "status", "notes", "call_id"
    ]

admin.site.register(admin_class=callRecordAdmin, model_or_iterable=CallRecord)


class SMSAdmin(admin.ModelAdmin):
    list_display = [
        "phone_number", "full_name", "message", "sent_by"
    ]

admin.site.register(admin_class=SMSAdmin, model_or_iterable=SMS)


class SMSTemplateAdmin(admin.ModelAdmin):
    list_display = [
        "title", "message",
    ]


admin.site.register(Buckets)

admin.site.register(AgentLocations)

admin.site.register(admin_class=SMSTemplateAdmin, model_or_iterable=SMSTemplates)
