

from datetime import timezone
from venv import logger
from celery import shared_task

from call_center.services import assign_buckets_to_agents, categorize_debtors_into_buckets
from .three_cx_services import ThreeCXService
from .models import CallRecord
from time import time

@shared_task
def initiate_auto_dialing_campaign(phone_numbers, delay=5):
    service = ThreeCXService()
    results = []
    
    for phone_number in phone_numbers:
        result = service.make_call(phone_number)
        results.append(result)
        time.sleep(delay)
    
    return results


@shared_task
def check_and_update_call_statuses():
    service = ThreeCXService()
    active_calls = CallRecord.objects.filter(
        status__in=['dialing', 'in_progress']
    )
    
    for call in active_calls:
        if call.call_id:
            result = service.get_call_status(call.call_id)
            if result.get('success'):
                call_data = result.get('data', {})
                call.status = call_data.get('status', call.status)
                

                if call.status in ['completed', 'failed', 'no_answer'] and not call.end_time:
                    call.end_time = call_data.get('endTime') or timezone.now()
                
                call.save()
    
    return f"Updated {active_calls.count()} active calls"




@shared_task
def execute_daily_bucket_assignment():

    try:
        logger.info("Starting daily bucket assignment task")
        
        buckets = categorize_debtors_into_buckets()
        
        bucket_counts = {name: len(ids) for name, ids in buckets.items()}
        logger.info(f"Bucket counts: {bucket_counts}")
        
        assignment_result = assign_buckets_to_agents()
        logger.info(f"Assignment result: {assignment_result}")
        
        results = {
            'bucket_counts': bucket_counts,
            'assignment_result': assignment_result,
            'execution_time': timezone.now().isoformat()
        }
        logger.info(f"Daily bucket assignment completed: {results}")
        
        return results
    
    except Exception as e:
        logger.error(f"Error in daily bucket assignment: {str(e)}")
        raise







# service = ThreeCXService()
# result = service.make_call(phone_number='1234567890')


# service.end_call(call_id='abc123')



# phone_numbers = []
# initiate_auto_dialing_campaign.delay(phone_numbers, delay=10)