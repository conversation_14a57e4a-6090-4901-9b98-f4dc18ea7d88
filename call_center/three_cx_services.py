

import requests
import json
import logging
from django.conf import settings
from django.utils import timezone
from .models import CallRecord


logger = logging.getLogger(__name__)

class ThreeCXService:
    def __init__(self):
        self.api_url = settings.THREECX_API_URL
        self.api_key = settings.THREECX_API_KEY
        self.username = settings.THREECX_USERNAME
        self.password = settings.THREECX_PASSWORD
        self.token = None
        
    def authenticate(self):
        auth_url = f"{self.api_url}/auth"
        payload = {
            "username": self.username,
            "password": self.password
        }
        headers = {
            "Content-Type": "application/json",
            "X-API-KEY": self.api_key
        }
        
        try:
            response = requests.post(auth_url, headers=headers, data=json.dumps(payload))
            response.raise_for_status()
            self.token = response.json().get('token')
            return self.token
        except requests.exceptions.RequestException as e:
            logger.error(f"3CX Authentication error: {e}")
            return None
    
    def get_headers(self):
        """Get headers with authentication token"""
        if not self.token:
            self.authenticate()
            
        return {
            "Content-Type": "application/json",
            "X-API-KEY": self.api_key,
            "Authorization": f"Bearer {self.token}"
        }
    
    def make_call(self, dnnumber, phone_number):
        call_url = f"{self.api_url}/callcontrol/{dnnumber}/makecall"
        
        payload = {
            "reason": "Debt retrieval",
            "destination": phone_number,
            "timeout": 17
        }
            
        try:
            headers = self.get_headers()
            response = requests.post(call_url, headers=headers, data=json.dumps(payload))
            # response.raise_for_status()
            
            call_data = response.json()
            call_record = CallRecord.objects.create(
                phone_number=phone_number,
                start_time=timezone.now(),
                status='dialing',
                call_id=call_data.get('callId')
            )
            
            return {
                "success": True,
                "call_id": call_data.get('callId'),
                "record_id": call_record.id
            }
        except requests.exceptions.RequestException as e:
            logger.error(f"3CX Make Call error: {e}")
            CallRecord.objects.create(
                phone_number=phone_number,
                start_time=timezone.now(),
                status='failed',
                notes=f"API Error: {str(e)}"
            )
            return {"success": False, "error": str(e)}
    
    def end_call(self, dnnumber, call_id):

        end_url = f"{self.api_url}/callcontrol/{dnnumber}/participants/{call_id}/drop"
        
        try:
            headers = self.get_headers()
            response = requests.post(end_url, headers=headers)
            response.raise_for_status()
            
            try:
                call_record = CallRecord.objects.get(call_id=call_id)
                call_record.end_time = timezone.now()
                call_record.status = 'completed'
                call_record.save()
            except CallRecord.DoesNotExist:
                logger.warning(f"Call record not found for call_id {call_id}")
            
            return {"success": True}
        except requests.exceptions.RequestException as e:
            logger.error(f"3CX End Call error: {e}")
            return {"success": False, "error": str(e)}
    
    def get_call_status(self, dnnumber, call_id):
        status_url = f"{self.api_url}/callcontrol/{dnnumber}/participants/{call_id}/stream"
        
        try:
            headers = self.get_headers()
            response = requests.get(status_url, headers=headers)
            # response.raise_for_status()
            
            call_data = response.json()
            return {
                "success": True,
                "status": call_data.get('status'),
                "data": call_data
            }
        except requests.exceptions.RequestException as e:
            logger.error(f"3CX Get Call Status error: {e}")
            return {"success": False, "error": str(e)}