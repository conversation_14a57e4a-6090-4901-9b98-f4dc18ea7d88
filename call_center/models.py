

from django.db import models
from accounts.models import CustomUser
from accounts.helpers import BaseModel
from django.utils import timezone
import uuid

from loans.models import (
    BorrowerInfo, AjoLoanSchedule, AjoLoanRepayment, AjoLoan
)

import random
from django.utils.timezone import now



def agent_id() -> int:
    num = random.randint(100, 999)
    return num


class CallAgent(CustomUser):
    # agent_id = models.PositiveIntegerField(default=agent_id, primary_key=True)
    # is_call_agent = models.BooleanField(default=True)
    # is_call_agent_admin = models.BooleanField(default=False)
    # is_caller_active = models.BooleanField(default=False)
    # daily_total_expected_calls = models.IntegerField(default=0)
    # no_successful_calls = models.IntegerField(default=0)
    # bucket = models.JSONField(default=list, blank=True, null=True)

    def __str__(self):
        return self.username
    


class Debtor(BaseModel):
    loan = models.ForeignKey(A<PERSON><PERSON>oan, on_delete=models.SET_NULL, null=True, blank=True)
    assigned_agent = models.ForeignKey(CallAgent, on_delete=models.SET_NULL, null=True, blank=True)
    loan_due_date = models.DateField()
    moved_to_recovery = models.BooleanField(default=False)
    recovered = models.BooleanField(default=False)
    promise_status = models.DateField(blank=True, null=True)
    comment = models.CharField(max_length=256, blank=True, null=True)


    @property
    def get_loan_due_date(self):
        self.loan_due_date = self.name.ajo_loan.end_date
        return self.loan.ajo_loan.end_date


    @property
    def days_missed(self):
        return (now().date() - self.loan_due_date).days
    

    @property
    def bucket(self):
        days = self.days_missed
        if days <= 5:
            return 'A'
        elif days <= 14:
            return 'B'
        elif days <= 21:
            return 'C'
        else:
            return 'D'
        
    def __str__(self):
        return self.name


class Assignment(models.Model):
    debtor = models.ForeignKey(Debtor, on_delete=models.CASCADE)
    call_agent = models.ForeignKey(CallAgent, on_delete=models.CASCADE)
    assigned_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.debtor} -> {self.call_agent.username}"






class CallRecord(models.Model):

    class CallStatus(models.TextChoices):
        PENDING ='Pending'
        DAILING ='Dialing'
        IN_PROGRESS ='In Progress'
        COMPLETED = 'Completed'
        FAILED ='Failed'
        NO_ANSWER ='No Answer'
    
    phone_number = models.CharField(max_length=20)
    start_time = models.DateTimeField(null=True, blank=True)
    end_time = models.DateTimeField(null=True, blank=True)
    duration = models.IntegerField(null=True, blank=True)  # in seconds o
    status = models.CharField(max_length=20, choices=CallStatus.choices, default=CallStatus.PENDING)
    call_id = models.CharField(max_length=50, null=True, blank=True)
    notes = models.TextField(blank=True)
    
    def __str__(self):
        return f"Call to {self.phone_number} ({self.status})"
    
    def save(self, *args, **kwargs):
        if self.start_time and self.end_time:
            self.duration = (self.end_time - self.start_time).total_seconds()
        super().save(*args, **kwargs)




class SMSTemplates(BaseModel):
    title = models.CharField(max_length=50)
    message = models.CharField(max_length=300)

    def __str__(self):
        return self.title


class SMS(BaseModel):
    phone_number = models.CharField(max_length=50)
    full_name = models.CharField(max_length=256, blank=True, null=True)
    message = models.CharField(max_length=256)
    sent_by = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name="message_sender")

    def __str__(self):
        return self.full_name
    


class Buckets(BaseModel):
    name = models.CharField(max_length=200)
    description = models.CharField(max_length=200)
    agent = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name="agent")
    debtors = models.ManyToManyField(AjoLoan)

    def __str__(self):
        return f"Bucket name: {self.name} | Agent: {self.agent}"
    



class AgentLocations(BaseModel):
    branch_location = models.CharField(max_length=200, null=True, blank=True)

    def __str__(self):
        return self.branch_location
    

