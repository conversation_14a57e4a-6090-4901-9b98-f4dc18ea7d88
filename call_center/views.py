

from django.http import Http404
from django.shortcuts import get_object_or_404, render
from ajo.models import Ajo<PERSON>ser
from collections_app.helpers.helpers import get_agent_supervisor_details, get_loan_branches_list
from loans.models import (
    <PERSON>joLoan, AjoLoanRepayment, AjoLoanSchedule, BorrowerInfo, Assignment, FraudMarkers, GeneralComments
)
from rest_framework.generics import ListAPIView
from rest_framework.views import APIView
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from savings.pagination import CustomPagination
from loans.enums import LoanStatus, LoanPerformanceStatus
from .serializers import AgentSerializerOut, AjoLoanCallerModelSerializer, AjoLoanRepaymentDetailSerializer, AjoRepaymentHistorySerializer, CommentsSerializer, FraudMarkersSerializer, LoanSerializerOut, LoanSerializerIn, LoanSerializerDetailOut, SMSSerializer, SMSTemplateSerializer
from accounts.models import CustomUser, AgentLocations
from django.db.models import Sum, Q, Count
# from .models import <PERSON><PERSON><PERSON>, Debtor, Assignment

from .three_cx_services import ThreeCXService
from rest_framework import viewsets, status
from rest_framework.decorators import action
from .models import SMS, CallRecord, SMSTemplates
from .serializers import CallRecordSerializer, MakeCallSerializer
from datetime import datetime, timedelta, date
from django.db.models import Q
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils import timezone
from ajo.third_party import TextMessages







# class DebtorsListAPIView(APIView):
#     permission_classes = [IsAuthenticated]
#     pagination_class = CustomPagination
#     serializer_class = LoanSerializerOut

#     def get(self, request, *args, **kwargs):
#         user = self.request.user

        # search = self.request.query_params.get("search")
        # branch = self.request.query_params.get("branch")
        # loan_due_date = self.request.query_params.get("loan_due_date")
        # promise_status = self.request.query_params.get("promise_status")
        # month = self.request.query_params.get("month")
        # performance_status = self.request.query_params.get("performance_status")
        # promise_to_pay = self.request.query_params.get("promise_to_pay")

        # query = (
        #     # Q(status=LoanStatus.DEFAULTED)
        #     Q(performance_status=LoanPerformanceStatus.DEFAULTED)
        #     | Q(performance_status=LoanPerformanceStatus.OWED_BALANCE)
        #     | Q(performance_status=LoanPerformanceStatus.PAST_MATURITY)
        #     # & Q(assigned_agent=user)
        # )
#         # search
#         # branch 
#         # loan_due_date
#         # month
#         # promise_status
        

#         if search:
#             query &= (
#                 Q(agent__user_branch__iexact=search) |
#                 Q(borrower_phone_number=search) |
#                 Q(borrower__user__first_name__icontains=search) |
#                 Q(borrower__phone_number=search)
#             )


#         if performance_status:
#             query &= Q(performance_status=performance_status)

#         if promise_to_pay:
#             query &= Q(promise_to_pay=promise_to_pay)

#         if branch:
#             query &= Q(agent__user_branch=branch)
#         if loan_due_date:
#             try:
#                 year, month, day = map(int, loan_due_date.split("-"))
#                 query &= Q(end_date__year=year, end_date__month=month, end_date__day=day)
#             except ValueError:
#                 return Response({"message": "Invalid loan_due_date format. Use YYYY-MM-DD"}, status=400)
            
#         if month:
#             try:
#                 year, month = map(int, loan_due_date.split("-"))
#                 query &= Q(end_date__year=year, end_date__month=month)
#             except ValueError:
#                 return Response({"message": "Invalid loan_due_date format. Use YYYY-MM-DD"}, status=400)
#         if promise_status:
#             try:
#                 year, month, day = map(int, promise_status.split("-"))
#                 query &= Q(promise_status__year=year, promise_status__month=month, promise_status__day=day)
#             except ValueError:
#                 return Response({"message": "Invalid promise_status format. Use YYYY-MM-DD"}, status=400)

#         queryset = AjoLoan.objects.filter(query)

#         # paginator = self.pagination_class()
#         # page = paginator.paginate_queryset(queryset, request)
#         serializer = self.serializer_class(queryset, many=True)
#         return Response(
#             {
#                 "message": "success",
#                 "data": serializer.data
#             }
#         )


class DebtorsListAPIView(APIView):
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination
    serializer_class = LoanSerializerOut

    def get(self, request, *args, **kwargs):
        user = self.request.user

        user = CustomUser.objects.get(id=user.id)

        agent_location: list = []

        for location in user.recovery_agent_location.all():
            agent_location.append(location.branch_location)

        search = self.request.query_params.get("search")
        branch = self.request.query_params.get("branch")
        loan_due_date = self.request.query_params.get("loan_due_date")
        promise_status = self.request.query_params.get("promise_status")
        month = self.request.query_params.get("month")
        performance_status = self.request.query_params.get("performance_status")
        promise_to_pay = self.request.query_params.get("promise_to_pay")

        query = (
            # Q(status=LoanStatus.DEFAULTED)
            Q(performance_status=LoanPerformanceStatus.DEFAULTED)
            | Q(performance_status=LoanPerformanceStatus.OWED_BALANCE)
            | Q(performance_status=LoanPerformanceStatus.PAST_MATURITY)
            # & Q(assigned_agent=user)
        )
        # search
        # branch 
        # loan_due_date
        # month
        # promise_status
        

        if search:
            query &= (
                Q(agent__user_branch__iexact=search) |
                Q(borrower_phone_number=search) |
                Q(borrower__user__first_name__icontains=search) |
                Q(borrower__phone_number=search)
            )


        if performance_status:
            query &= Q(performance_status=performance_status)

        if promise_to_pay:
            query &= Q(promise_to_pay=promise_to_pay)

        if branch:
            query &= Q(agent__user_branch=branch)
        if loan_due_date:
            try:
                year, month, day = map(int, loan_due_date.split("-"))
                query &= Q(end_date__year=year, end_date__month=month, end_date__day=day)
            except ValueError:
                return Response({"message": "Invalid loan_due_date format. Use YYYY-MM-DD"}, status=400)
            
        if month:
            try:
                year, month = map(int, loan_due_date.split("-"))
                query &= Q(end_date__year=year, end_date__month=month)
            except ValueError:
                return Response({"message": "Invalid loan_due_date format. Use YYYY-MM-DD"}, status=400)
        if promise_status:
            try:
                year, month, day = map(int, promise_status.split("-"))
                query &= Q(promise_status__year=year, promise_status__month=month, promise_status__day=day)
            except ValueError:
                return Response({"message": "Invalid promise_status format. Use YYYY-MM-DD"}, status=400)
    
        # query = (
        #     Q(performance_status=LoanPerformanceStatus.DEFAULTED)
        #     | Q(performance_status=LoanPerformanceStatus.OWED_BALANCE)
        #     | Q(performance_status=LoanPerformanceStatus.PAST_MATURITY)
        #     # & Q(assigned_agent=user)
        # )
        if request.user.is_staff:
            queryset = AjoLoan.objects.filter(query)
        else:
            queryset = AjoLoan.objects.filter(
                query & (Q(agent__user_branch__in=agent_location) | Q(borrower__branch__in=agent_location))
            )


        # paginator = self.pagination_class()
        # page = paginator.paginate_queryset(purchase_invoices, request)
        # serializer = self.serializer_class(page, many=True)

        serializer = self.serializer_class(queryset, many=True)

        # branches = get_loan_branches_list()
        # print(branches)
        # for branch in branches:
        #     AgentLocations.objects.create(branch_location=branch)
        return Response(
            {
                "message": "success",
                "data": serializer.data
            }
        )
    
class PopulateLocationAPIView(APIView):

    def get(self, request, *args, **kwargs):

        agent_location: list = []

        branches = get_loan_branches_list()
        for branch in branches:
            AgentLocations.objects.create(branch_location=branch)
        return Response(
            {
                "message": "success",
                "data": branches
            }
        )

    
class GetAgentSupDetailAPIView(APIView):

    def get(self, request, *args, **kwargs):

        agent_location: list = []
        agent_supervisor_detail = dict()
        
        agents = CustomUser.objects.filter(user_type="STAFF_AGENT")
        for agent in agents:
            agent_supervisor_detail = get_agent_supervisor_details(agent.customer_user_id)
            agent.user_branch = agent_supervisor_detail["branch"]
            print(agent_supervisor_detail)
            
        return Response(
            {
                "message": "success",
                "data": agent_supervisor_detail
            }
        )

    

class DebtorAPIViewDetail(APIView):
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination

    def get(self, request, id: int):
        user = self.request.user

        try:
            debtor = AjoLoan.objects.get(pk=id)
            past_maturity = debtor.amount - debtor.due_yesterday
            serializer = LoanSerializerDetailOut(debtor).data
            
            ordered_debtors = AjoLoan.objects.all().order_by('id')
            
            previous_debtor = ordered_debtors.filter(id__lt=id).order_by('-id').first()
            previous_id = previous_debtor.id if previous_debtor else None
            
            next_debtor = ordered_debtors.filter(id__gt=id).order_by('id').first()
            next_id = next_debtor.id if next_debtor else None

            days_missed: float = debtor.days_missed
            missed_repayment_amount = days_missed * debtor.daily_repayment_amount

            borrower = BorrowerInfo.objects.filter(borrower=debtor.borrower).last()
            if borrower is not None:
                borrower_image = borrower.base_64_img_string
            else:
                return Response(
                    {
                        "error": "Borrower has no data in borrower info"
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            return Response(
                {
                    "message": "success",
                    "previous_id": previous_id,
                    "next_id": next_id,
                    "past_maturity": past_maturity,
                    "days_missed": days_missed,
                    "missed_repayment_amount": missed_repayment_amount,
                    "borrower_image": borrower_image,
                    "data": serializer,
                },
                status=status.HTTP_200_OK
            )

        except AjoLoan.DoesNotExist as e:
            return Response(
                {
                    "message": "error",
                    "info": str(e)
                },
                status=status.HTTP_404_NOT_FOUND
                
            )
        
    
    def put(self, request, id, format=None):
        debtor = AjoLoan.objects.get(pk=id) or None
        serializer = AjoLoanCallerModelSerializer(data=request.data)
        if serializer.is_valid():
            debtor.promise_status = serializer.validated_data.get('promise_status')
            debtor.promise_to_pay_amount = serializer.validated_data.get('promise_to_pay_amount')
            debtor.additional_instruction = serializer.validated_data.get('additional_instruction')
            debtor.promise_to_pay = True
            debtor.save()
            return Response(
                {
                    "message": "succcessful",
                    "data": serializer.data 
                }
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    

class MoveToRecoveryAPIView(APIView):

    def put(self, request, id, format=None):
        debtor = AjoLoan.objects.get(pk=id) or None
        serializer = AjoLoanCallerModelSerializer(data=request.data)
        if serializer.is_valid():
            # debtor.comment = serializer.validated_data.get('comment')
            debtor.recovery_reason = serializer.validated_data.get('recovery_reason')
            debtor.moved_to_recovery = serializer.validated_data.get('moved_to_recovery')
            debtor.recovered = serializer.validated_data.get('recovered')
            debtor.additional_instruction = serializer.validated_data.get('additional_instruction')
            debtor.save()
            return Response(
                {
                    "message": "succcessful",
                    "data": serializer.data 
                }
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class AgentsListAPIView(APIView):
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination
    serializer_class = AgentSerializerOut

    def get(self, request, *args, **kwargs):
        user = self.request.user

        # agent = self.request.query_params.get("agent")

        # query = Q(username=agent)

        try:
            
            call_agents = CustomUser.objects.filter(is_call_agent=True)
            paginator = self.pagination_class()
            page = paginator.paginate_queryset(call_agents, request)
            serializer = self.serializer_class(page, many=True)

            return paginator.get_paginated_response(
                {
                    "message": "success",
                    "data": serializer.data
                }
            )
        except Exception as err:
            return Response(
                {
                    "message": "failed",
                    "details": str(err)
                },
                status=status.HTTP_506_VARIANT_ALSO_NEGOTIATES
            )


            

class LoanStatisticsAPIView(APIView):
    permission_classes = [IsAuthenticated]
    # pagination_class = CustomPagination
    # serializer_class = AgentSerializerOut

    def get(self, request,  *args, **kwargs):
        user = self.request.user        

        query = (
            Q(performance_status=LoanPerformanceStatus.DEFAULTED)
            | Q(performance_status=LoanPerformanceStatus.OWED_BALANCE)
            | Q(performance_status=LoanPerformanceStatus.PAST_MATURITY)
        ) 
        
        fraud_loans = AjoLoan.objects.filter(query)

        time_period = request.query_params.get('time_period')
        if time_period:
            today = timezone.now().date()
            
            if time_period.lower() == 'weekly':
                start_date = today - timedelta(days=7)
            
            elif time_period.lower() == 'monthly':
                start_date = today - timedelta(days=30)
            
            elif time_period.lower() == 'yearly':
                start_date = today - timedelta(days=365)
            
            elif time_period.lower() == 'current_week':
                start_date = today - timedelta(days=today.weekday())
            
            elif time_period.lower() == 'current_month':

                start_date = date(today.year, today.month, 1)
            
            elif time_period.lower() == 'current_year':
                start_date = date(today.year, 1, 1)

            else:
                return Response({"error": "Invalid time_period"}, status=status.HTTP_400_BAD_REQUEST)
            
            fraud_loans = fraud_loans.filter(date_disbursed__date__gte=start_date)

        loan_count = AjoLoan.objects.filter(
                        performance_status__in=[
                            LoanPerformanceStatus.PAST_MATURITY,
                            LoanPerformanceStatus.OWED_BALANCE,
                            LoanPerformanceStatus.DEFAULTED
                        ]
                    ).count() or 0
        
        query_overall = AjoLoan.objects.filter(
                        performance_status__in=[
                            LoanPerformanceStatus.PAST_MATURITY,
                            LoanPerformanceStatus.OWED_BALANCE,
                            LoanPerformanceStatus.DEFAULTED
                        ]
                    ).count() or 0
        
        total_recovered = AjoLoan.objects.filter(
                        performance_status__in=[
                            LoanPerformanceStatus.PAST_MATURITY,
                            LoanPerformanceStatus.OWED_BALANCE,
                            LoanPerformanceStatus.DEFAULTED
                        ],
                        recovered=True
                    ).count() or 0
        
        moved_to_recovery = AjoLoan.objects.filter(
                        performance_status__in=[
                            LoanPerformanceStatus.PAST_MATURITY,
                            LoanPerformanceStatus.OWED_BALANCE,
                            LoanPerformanceStatus.DEFAULTED
                        ],
                        moved_to_recovery=True
                    ).count() or 0

        total_overdue = query_overall.aggregate(
            total_overdue=Sum('total_outstanding_balance')
        )
        promise_to_pay = query_overall.aggregate(
            promise_to_pay_amount=Sum('promise_to_pay_amount'),
            count=Count("id")
        )

        total_recovered = total_recovered.aggregate(
            total_recovered=Sum('total_paid_amount'),
            count=Count("id")
        )

        moved_to_recovery = moved_to_recovery.aggregate(
            total_recovered=Sum('total_paid_amount'),
            count=Count("id")
        )

        aggregated_data = fraud_loans.aggregate(
            count=Count('id'),
            total_amount=Sum('total_outstanding_balance')
        )

        return Response(
            {
                "loan_count": loan_count,
                "total_expected_calls": user.daily_total_expected_calls or 0,
                "total_overdue": total_overdue,
                "promise_to_pay_amount": promise_to_pay,
                "total_recovered": total_recovered,
                "moved_to_recovery": moved_to_recovery,
                "aggregated_data": aggregated_data,
            }
        )




class AutoDialerViewSet(viewsets.ModelViewSet):
    queryset = CallRecord.objects.all()
    # serializer_class = CallRecordSerializer
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.threecx_service = ThreeCXService()
    
    @action(detail=True, methods=['post'])
    def make_call(self, request):

        serializer = MakeCallSerializer(data=request.data, context={"request": request})
        if serializer.is_valid():
            phone_number = serializer.validated_data.get("phone_number")
            dnnumber = serializer.validated_data.get("dnnumber")

        
        if not phone_number:
            return Response(
                {"error": "Phone number is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        result = self.threecx_service.make_call(
            dnnumber=dnnumber,
            phone_number=phone_number,
        )
        
        if result.get('success'):
            return Response(result, status=status.HTTP_201_CREATED)
        else:
            return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['post'])
    def end_call(self, request):

        serializer = MakeCallSerializer(data=request.data, context={"request": request})
        if serializer.is_valid():
            dnnumber = serializer.validated_data.get("dnnumber")

        try:
            call_record = self.get_object()
            
            if not call_record.call_id:
                return Response(
                    {"error": "No call_id associated with this record"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            result = self.threecx_service.end_call(call_record.call_id, dnnumber)
            
            if result.get('success'):
                return Response(result, status=status.HTTP_200_OK)
            else:
                return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        except CallRecord.DoesNotExist:
            return Response(
                {"error": "Call record not found"},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=True, methods=['get'])
    def check_status(self, request, pk=None):
        try:
            call_record = self.get_object()
            
            if not call_record.call_id:
                return Response(
                    {"error": "No call_id associated with this record"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            result = self.threecx_service.get_call_status(call_record.call_id, dnnumber)
            
            if result.get('success'):
                call_record.status = result.get('status', call_record.status)
                call_record.save()
                
                return Response(result, status=status.HTTP_200_OK)
            else:
                return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        except CallRecord.DoesNotExist:
            return Response(
                {"error": "Call record not found"},
                status=status.HTTP_404_NOT_FOUND
            )




class AjoRepaymentView(APIView):
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination
    queryset = AjoLoanRepayment.objects.all()

    def get(self, request, borrower_id: int):
        try:
            ajo_user = AjoUser.objects.get(pk=borrower_id)
            repayment_history = self.queryset.filter(borrower=ajo_user)
            
            serializer = AjoLoanRepaymentDetailSerializer(repayment_history, many=True)
            # serializer = AjoRepaymentHistorySerializer(repayment_history, many=True)
            return Response(
                {
                    "message": "success",
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK
            )
        except AjoUser.DoesNotExist as e:
            return Response(
                {
                    "message": "No repayment data for this user",
                    "error": str(e)
                },
                status=status.HTTP_404_NOT_FOUND
            )

            


class CommentView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, loan_id: int):
        call_agent_username = self.request.user.username
        comments = GeneralComments.objects.filter(loan=loan_id)
        serializer = CommentsSerializer(comments, many=True).data
        return Response(
            {
                "message": "success",
                "data": serializer
            }
        )
    
    def post(self, request, loan_id: int):
        call_agent_username = self.request.user.username
        loan = get_object_or_404(AjoLoan, id=loan_id)

        serializer = CommentsSerializer(data=request.data)
        if serializer.is_valid():
            comment_instance = GeneralComments.objects.create(
                loan=loan,
                comment=serializer.validated_data["comment"],
                call_agent_username=call_agent_username
            )
            return Response(
                {
                    "message": "success",
                    "data": CommentsSerializer(comment_instance).data
                },
                status=status.HTTP_201_CREATED
            )
        return Response(
            {"message": "Invalid data", "errors": serializer.errors}, 
            status=status.HTTP_400_BAD_REQUEST
        )    



class FraudMarkersView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, loan_id: int):

        fraud_loans = FraudMarkers.objects.get(fraud_loan=loan_id)
        serializer = FraudMarkersSerializer(fraud_loans).data
        return Response(
            {
                "message": "success",
                "data": serializer
            }
        )

    def post(self, request, loan_id: int):
        loan = get_object_or_404(AjoLoan, id=loan_id)  # Raises 404 if not found
        serializer = FraudMarkersSerializer(data=request.data)

        if serializer.is_valid():
            fraud_loans_instance = FraudMarkers.objects.create(
                fraud_loan=loan,
                **serializer.validated_data
            )
            loan.is_fraud = True
            loan.save()
            return Response(
                {
                    "message": "success",
                    "data": FraudMarkersSerializer(fraud_loans_instance).data
                }
            )
        
        return Response(serializer.errors, status=400)




class FraudLoanView(APIView):
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination
    serializer_class = LoanSerializerOut
    
    def get(self, request, *args, **kwargs):
        if not hasattr(self.request.user, 'is_call_agent_admin') or not self.request.user.is_call_agent_admin == True:
            return Response(
                {"message": "You don't have permission to view fraud loans"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        fraud_loans = AjoLoan.objects.filter(is_fraud=True)

        active_fraud_stats = fraud_loans.filter(active=True, resolved=False).aggregate(
            total_amount=Sum('amount'),
            count=Count('id')
        )
        
        resolved_fraud_stats = fraud_loans.filter(resolved=True).aggregate(
            total_amount=Sum('amount'),
            count=Count('id')
        )


        fraud_loans = AjoLoan.objects.filter(is_fraud=True)

        time_period = request.query_params.get('time_period')
        if time_period:
            today = timezone.now().date()
            
            if time_period.lower() == 'weekly':
                start_date = today - timedelta(days=7)
                fraud_loans.filter(date_disbursed__date__gte=start_date)
            
            elif time_period.lower() == 'monthly':
                start_date = today - timedelta(days=30)
                fraud_loans = fraud_loans.filter(date_disbursed__date__gte=start_date)
            
            elif time_period.lower() == 'yearly':
                start_date = today - timedelta(days=365)
                fraud_loans = fraud_loans.filter(date_disbursed__date__gte=start_date)
            
            elif time_period.lower() == 'current_week':
                start_of_week = today - timedelta(days=today.weekday())
                fraud_loans = fraud_loans.filter(date_disbursed__date__gte=start_of_week)
            
            elif time_period.lower() == 'current_month':
                start_of_month = date(today.year, today.month, 1)
                fraud_loans = fraud_loans.filter(date_disbursed__date__gte=start_of_month)
            
            elif time_period.lower() == 'current_year':
                start_of_year = date(today.year, 1, 1)
                fraud_loans = fraud_loans.filter(date_disbursed__date__gte=start_of_year)

        active_param = request.query_params.get('active')
        if active_param is not None:
            active_value = active_param.lower() == 'true'
            fraud_loans = fraud_loans.filter(active=active_value)
        
        resolved_param = request.query_params.get('resolved')
        if resolved_param is not None:
            resolved_value = resolved_param.lower() == 'true'
            fraud_loans = fraud_loans.filter(resolved=resolved_value)

        agent_id = request.query_params.get('agent_id')
        if agent_id:
            fraud_loans = fraud_loans.filter(agent_id=agent_id)
        
        end_date = request.query_params.get('end_date')
        if end_date:
            try:
                end_date_instance = datetime.strptime(end_date, '%Y-%m-%d').date()
                fraud_loans = fraud_loans.filter(end_date=end_date_instance)
            except ValueError:
                return Response(
                    {"message": "Invalid date format. Use YYYY-MM-DD"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
        search_query = request.query_params.get('search', '')
        if search_query:
            fraud_loans = fraud_loans.filter(
                Q(borrower__user__username__icontains=search_query) |
                Q(borrower_full_name__icontains=search_query) |
                Q(borrower_phone_number__icontains=search_query) |
                Q(loan_ref__icontains=search_query) |
                Q(unique_loan_ref__icontains=search_query)
            )
        
        # paginator = self.pagination_class()
        # page = paginator.paginate_queryset(fraud_loans, request)
        serializer = self.serializer_class(fraud_loans, many=True).data

        

        ### Serach Query
        # fraud/loans/?agent_id=123
        # fraud/loans/?end_date=2025-03-01
        # fraud/loans/?search=john
        # fraud/loans/?agent_id=123&end_date=2025-03-01&search=john
        # fraud/loans/?resolved=true
        # fraud/loans/?active=true
        # fraud/loans/?time_period=weekly
        # fraud/loans/?time_period=monthly&active=true
        # fraud/loans/?time_period=current_year&resolved=true


        filtered_total = fraud_loans.aggregate(
            total_amount=Sum('amount'),
            count=Count('id')
        )

        total_paid_amount = fraud_loans.aggregate(
            total_paid_amount=Sum('total_paid_amount'),
            count=Count('id')
        )
        
        recovered_fraud_loans = AjoLoan.objects.filter(is_fraud=True, recovered=True)
        recovered_fraud_loans = recovered_fraud_loans.aggregate(
            recovered_fraud_loans=Sum('total_paid_amount'),
            count=Count('id')
        )

    
        return Response({
            "message": "success",
            "statistics": {
                "active_fraud": {
                    "count": active_fraud_stats['count'] or 0,
                    "total_amount": active_fraud_stats['total_amount'] or 0.0
                },
                "resolved_fraud": {
                    "count": resolved_fraud_stats['count'] or 0,
                    "total_amount": resolved_fraud_stats['total_amount'] or 0.0
                },
                "recovered_fraud_loans": {
                    "count": recovered_fraud_loans['count'] or 0,
                    "recovered_fraud_loans_amount": recovered_fraud_loans["recovered_fraud_loans"] or 0.0
                },

                "total_fraud_cases": {
                    "count": filtered_total['count'] or 0,
                    "total_fraud_cases": filtered_total['total_amount'] or 0.0,
                }
            },
            "data": serializer
        })


class RecoveryLoansView(APIView):
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination
    serializer_class = LoanSerializerOut

    def get(self, request, *args, **kwargs):
        if not hasattr(self.request.user, 'is_call_agent_admin') or not self.request.user.is_call_agent_admin:
            return Response(
                {"message": "You don't have permission to view recovery loans"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        today = timezone.now().date()
        
        base_queryset = AjoLoan.objects.all()
        
        recovery_type = request.query_params.get('recovery_type', '')
        
        if recovery_type == 'moved_to_recovery':
            queryset = base_queryset.filter(moved_to_recovery=True)
            
        elif recovery_type == 'days_30':
            thirty_days_ago = today - timedelta(days=30)
            queryset = base_queryset.filter(
                end_date__lt=today,
                end_date__gte=thirty_days_ago,
                status=LoanStatus.DEFAULTED,
                recovered=False
            )
            
        elif recovery_type == 'days_60':
            sixty_days_ago = today - timedelta(days=60)
            thirty_days_ago = today - timedelta(days=30)
            queryset = base_queryset.filter(
                end_date__lt=thirty_days_ago,
                end_date__gte=sixty_days_ago,
                status=LoanStatus.DEFAULTED,
                recovered=False
            )
            
        elif recovery_type == 'days_90':
            ninety_days_ago = today - timedelta(days=90)
            sixty_days_ago = today - timedelta(days=60)
            queryset = base_queryset.filter(
                end_date__lt=sixty_days_ago,
                end_date__gte=ninety_days_ago,
                status=LoanStatus.DEFAULTED,
                recovered=False
            )
            
        elif recovery_type == 'days_90_plus':
            ninety_days_ago = today - timedelta(days=90)
            queryset = base_queryset.filter(
                end_date__lt=ninety_days_ago,
                status=LoanStatus.DEFAULTED,
                recovered=False
            )
            
        else:
            queryset = base_queryset.filter(
                Q(moved_to_recovery=True) |
                Q(end_date__lt=today, status=LoanStatus.DEFAULTED, recovered=False)
            )
        
        agent_id = request.query_params.get('agent_id')
        if agent_id:
            queryset = queryset.filter(agent=agent_id)
        

        search_query = request.query_params.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(borrower__user__username__icontains=search_query) |
                Q(borrower_full_name__icontains=search_query) |
                Q(borrower_phone_number__icontains=search_query) |
                Q(loan_ref__icontains=search_query) |
                Q(unique_loan_ref__icontains=search_query)
            )
        
        recovery_stats = {
            'moved_to_recovery': base_queryset.filter(moved_to_recovery=True).aggregate(
                count=Count('id'),
                total_amount=Sum('total_outstanding_balance')
            ),
            'days_30': base_queryset.filter(
                end_date__lt=today,
                end_date__gte=today - timedelta(days=30),
                status=LoanStatus.DEFAULTED,
                recovered=False
            ).aggregate(
                count=Count('id'),
                total_amount=Sum('total_outstanding_balance')
            ),
            'days_60': base_queryset.filter(
                end_date__lt=today - timedelta(days=30),
                end_date__gte=today - timedelta(days=60),
                status=LoanStatus.DEFAULTED,
                recovered=False
            ).aggregate(
                count=Count('id'),
                total_amount=Sum('total_outstanding_balance')
            ),
            'days_90': base_queryset.filter(
                end_date__lt=today - timedelta(days=60),
                end_date__gte=today - timedelta(days=90),
                status=LoanStatus.DEFAULTED,
                recovered=False
            ).aggregate(
                count=Count('id'),
                total_amount=Sum('total_outstanding_balance')
            ),
            'days_90_plus': base_queryset.filter(
                end_date__lt=today - timedelta(days=90),
                status=LoanStatus.DEFAULTED,
                recovered=False
            ).aggregate(
                count=Count('id'),
                total_amount=Sum('total_outstanding_balance')
            )
        }

        # recovery_loans/?recovery_type=moved_to_recovery
        # recovery_loans/?recovery_type=days_30 
        # recovery_loans/?recovery_type=days_90_plus
        # recovery_loans/?agent_id=123

        paginator = self.pagination_class()
        # page = paginator.paginate_queryset(queryset, request)
        serializer = self.serializer_class(queryset, many=True)
        
        filtered_total = queryset.aggregate(
            count=Count('id'),
            total_amount=Sum('total_outstanding_balance')
        )
        expected_recovery = queryset.aggregate(
            count=Count('id'),
            total_amount=Sum('total_outstanding_balance')
        )
        total_recovered = base_queryset.filter(moved_to_recovery=True, recovered=True).aggregate(
            count=Count('id'),
            total_amount=Sum('amount')
        )
        
        return Response({
            "message": "success",
            "statistics": {
                "expected_recovery": expected_recovery,
                "total_recovered": total_recovered,
                "all_outstanding": {
                    "count": recovery_stats['moved_to_recovery']['count'] or 0,
                    "total_amount": recovery_stats['moved_to_recovery']['total_amount'] or 0.0
                },
                "0-29_days": {
                    "count": recovery_stats['days_30']['count'] or 0,
                    "total_amount": recovery_stats['days_30']['total_amount'] or 0.0
                },
                "30-59_days": {
                    "count": recovery_stats['days_60']['count'] or 0,
                    "total_amount": recovery_stats['days_60']['total_amount'] or 0.0
                },
                "60-89_days": {
                    "count": recovery_stats['days_90']['count'] or 0,
                    "total_amount": recovery_stats['days_90']['total_amount'] or 0.0
                },
                "over_90_days": {
                    "count": recovery_stats['days_90_plus']['count'] or 0,
                    "total_amount": recovery_stats['days_90_plus']['total_amount'] or 0.0
                },
                "total_outstanding_debt": {
                    "count": filtered_total['count'] or 0,
                    "total_amount": filtered_total['total_amount'] or 0.0
                }
            },
            "data": serializer.data
        })
    


class AgentBucketView(APIView):
  
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        user = self.request.user
        
        if not user.is_call_agent:
            return Response(
                {"error": "You do not have permission to view this info"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # if not user.bucket:
        #     return Response({
        #         "agent_name": user.get_full_name() or user.username or user.email,
        #         "agent_id": user.id,
        #         # "bucket_designation": user.bucket_designation,
        #         "successful_calls": user.no_successful_calls,
        #         "expected_calls": user.daily_total_expected_calls,
        #         "data": []
        #     })
        
        loans = AjoLoan.objects.filter(id__in=user.bucket)
        
        serialized_loans = LoanSerializerOut(loans, many=True).data
        
        bucket_distribution = {}
        if user.bucket_designation:
            bucket_designation = user.bucket_designation
            bucket_distribution[bucket_designation] = len(loans)
        
        total_outstanding = sum(loan.total_outstanding_balance for loan in loans)
        total_promised = loans.filter(promise_to_pay=True).count()
        
        return Response({
            "agent_name": user.get_full_name() or user.username or user.email,
            "agent_id": user.id,
            "bucket_designation": user.bucket_designation,
            "successful_calls": user.no_successful_calls,
            "expected_calls": user.daily_total_expected_calls,
            "summary": {
                "total_assigned": len(loans),
                "total_promised": total_promised,
                "total_outstanding": total_outstanding,
                "bucket_distribution": bucket_distribution
            },
            "data": serialized_loans
        }, status=status.HTTP_200_OK)
    
    

class BucketDesignationAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        user = self.request.user
        bucket_designation = user.bucket_designation or None

        print(bucket_designation)

        return Response(
            {
                "bucket_designation": bucket_designation
            }
        )



class SMSTemplateView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        try:
            sms_template = SMSTemplates.objects.all()
            serializer = SMSTemplateSerializer(sms_template, many=True).data
            return Response(
                {
                    "message": "success",
                    "data": serializer
                },
                status=status.HTTP_200_OK
            )
        except SMSTemplates.DoesNotExist as e:
            return Response(
                {
                    "message": str(e)
                },
                status=status.HTTP_404_NOT_FOUND
            )


class SMSView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        user = self.request.user

        serializer = SMSSerializer(data=request.data)
        if serializer.is_valid():
            phone_number=serializer.validated_data.get("phone_number")
            message=serializer.validated_data.get("message")
            send_sms = TextMessages.send_sms(phone_number, message)
            save_sms = SMS.objects.create(
                phone_number=phone_number,
                message=message,
                full_name=serializer.validated_data.get("full_name"),
                sent_by=user
            )
            return Response(
                {
                    "message": "message sent",
                    "data": send_sms
                },
                status=status.HTTP_201_CREATED
            )
        return Response(
            {
                "message": "failed",
            },
            status=status.HTTP_400_BAD_REQUEST
        )


