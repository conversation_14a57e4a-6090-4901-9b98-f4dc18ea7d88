import uuid

from django.db import IntegrityError
from django.shortcuts import render
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics, permissions, serializers, status
from rest_framework.response import Response

from accounts.responses import serializer_validation_error_response
from accounts.utils import ResourceCreationLimiting
from payment.models import Transaction

from .models import Halal
from .serializers import (
    HalalDetailsSerializer,
    HLCurrentPlanDetailsSerializer,
    HLPlanDetailsSerializer,
    HLTotalBalanceAndSavingsSerializer,
    HLTransactionHistorySerializer,
    ListOfAllHalalSerializer,
    RetrieveHalalInstanceSerializer,
    TotalBalanceSavingsListOfAllHalalSavingsSerializer,
)
from .utils import (
    defined_plan_details_information,
    extended_plan_details,
    plan_information_details,
)


class PlanDetailsAPIView(generics.GenericAPIView):
    """
    This view intends to get the plan name, duration, target and frequency of the customer
    and returns the daily, weekly and monthly breakdowns as well as the just once.
    *The Frequency choices are as follows -> "DAILY", "WEEKLY", "MONTHLY", "JUST_ONCE"*
    """

    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = HLPlanDetailsSerializer

    def post(self, request, *args, **kwargs):
        data = request.data
        serializer = self.serializer_class(data=data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        response_data = {}
        plan = serializer.validated_data.get("plan")
        # check if the plan exists
        if Halal.objects.filter(plan=plan, user=request.user).exists():
            return Response(
                {
                    "error": "614",
                    "status": False,
                    "message": "a plan with this name exists already",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        duration = int(serializer.validated_data["duration"])
        target = round(float(serializer.validated_data["target"]), 2)
        frequency = serializer.validated_data["frequency"]

        # Working based on the minimum duration being 3 months
        # Assuming that the duration comes in days
        response_data = {
            "status": "success",
            "data": {
                "plan": plan,
                **defined_plan_details_information(duration=duration, amount=target, frequency=frequency),
            },
        }

        return Response(response_data, status=status.HTTP_200_OK)


class HalalDetailsAPIView(generics.GenericAPIView):
    """
    This view gets all the details for the quicksavings plan and generates a quotation id
    _The Frequency choices are as follows -> "DAILY", "WEEKLY", "MONTHLY", "JUST_ONCE"_
    """

    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = HalalDetailsSerializer

    def post(self, request, *args, **kwargs):
        user = request.user
        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        if ResourceCreationLimiting.is_resource_created_recently(user_id=user.id):
            return Response(
                {
                    "status": "429",
                    "status": False,
                    "message": "wait a while before you create a new plan",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        plan = serializer.validated_data["plan"]
        duration = int(serializer.validated_data["duration"])
        target = serializer.validated_data["target"]
        frequency_method = serializer.validated_data["frequency"]
        lock = serializer.validated_data["lock"]
        hour = serializer.validated_data["hour"]

        details = extended_plan_details(
            duration=duration,
            amount=target,
            frequency=frequency_method,
        )

        # Get the details
        frequency = details["frequency"]
        periodic_amount = details["periodic_amount"]
        interest_rate = details["interest_rate"]
        maturity_date = details["maturity_date"]
        estimated_amount = details["estimated_amount"]

        # Create a quotation id
        quotation_id = f"LI-HAL-{uuid.uuid4()}"

        try:
            halal = Halal.objects.create(
                user=user,
                plan=plan,
                duration=duration,
                lock=lock,
                target=target,
                interest_rate=interest_rate,
                frequency=frequency,
                hour=hour,
                periodic_amount=periodic_amount,
                maturity_date=maturity_date,
                estimated_amount=estimated_amount,
                quotation_id=quotation_id,
            )

            ResourceCreationLimiting.create_resource(user_id=user.id)

        except IntegrityError as err:
            return Response(
                {
                    "error": "605",
                    "status": False,
                    "message": f"you already have a plan with this name: {plan}",
                },
                status=status.HTTP_403_FORBIDDEN,
            )
            # raise serializers.ValidationError("You already have a plan with this name: " + str(plan))

        response_data = {"status": "success", "data": {}}
        response_data["data"]["id"] = halal.id
        response_data["data"]["plan"] = halal.plan
        response_data["data"]["duration"] = halal.duration
        response_data["data"]["target"] = halal.target
        response_data["data"]["lock"] = halal.lock
        response_data["data"]["quotation_id"] = halal.quotation_id

        return Response(response_data, status=status.HTTP_201_CREATED)


# class ListOfAllHalalAPIView(generics.GenericAPIView):
#     """
#     Shows all the halal savings a user has
#     """

#     permission_classes = (permissions.IsAuthenticated,)
#     serializer_class = ListOfAllHalalSerializer
#     queryset = Halal.objects.all()

#     def get(self, request, *args, **kwargs):
#         halal_qs = self.queryset.filter(user=request.user).order_by("-id")

#         serializer = self.serializer_class(halal_qs, many=True)

#         return Response({"status": "success", "data": serializer.data}, status=status.HTTP_200_OK)


class CurrentPlanDetailsAPIView(generics.GenericAPIView):
    """
    Shows the amount saved so far, savings amount and maturity date for plans in progress

    """

    permission_classes = (permissions.IsAuthenticated,)

    @swagger_auto_schema(
        query_serializer=RetrieveHalalInstanceSerializer,
        responses={
            "200": HLCurrentPlanDetailsSerializer,
            "400": "Bad Request",
        },
    )
    def get(self, request, *args, **kwargs):
        id = request.query_params.get("id")
        user = request.user

        halal = Halal.objects.filter(id=id, user=user).first()
        if not halal:
            return Response(
                {"status": False, "error": "585", "message": "This plan does not exist for this user"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            serializer = HLCurrentPlanDetailsSerializer(halal)
            return Response({"status": "success", "data": serializer.data}, status=status.HTTP_200_OK)


class TransactionHistoryAPIView(generics.GenericAPIView):
    """
    Shows the list of transactions done for a particular halal plan
    """

    permission_classes = (permissions.IsAuthenticated,)

    @swagger_auto_schema(
        query_serializer=RetrieveHalalInstanceSerializer,
        responses={
            "200": HLTransactionHistorySerializer,
            "400": "Bad Request",
        },
    )
    def get(self, request, *args, **kwargs):
        id = request.query_params.get("id")
        user = request.user

        halal = Halal.objects.filter(id=id, user=user).first()
        if not halal:
            return Response(
                {"status": False, "error": "585", "message": "This plan does not exist for this user"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            transaction_qs = Transaction.objects.filter(quotation_id=halal.quotation_id)
            serializer = HLTransactionHistorySerializer(transaction_qs, many=True)
            return Response({"status": "success", "data": serializer.data}, status=status.HTTP_200_OK)


class TotalBalanceAndSavingsAPIView(generics.GenericAPIView):
    """
    This shows the total balance of all the savings and the number of savings
    """

    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = TotalBalanceSavingsListOfAllHalalSavingsSerializer
    queryset = Halal.objects.select_related("user")

    def get(self, request, *args, **kwargs):
        halal_qs = self.queryset.filter(user=request.user).order_by("-id")

        total_amount = 0

        for i in halal_qs.values("amount_saved"):
            total_amount += i["amount_saved"]

        savings = halal_qs.count()

        data = {
            "total_amount": total_amount,
            "savings": savings,
        }

        serializer = HLTotalBalanceAndSavingsSerializer(data)
        halal_serializer = ListOfAllHalalSerializer(halal_qs, many=True)

        return Response(
            {
                "status": "success",
                "data": {
                    "summary": serializer.data,
                    "halal_savings_list": halal_serializer.data,
                },
            },
            status=status.HTTP_200_OK,
        )
