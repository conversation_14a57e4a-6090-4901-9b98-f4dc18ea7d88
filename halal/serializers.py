import re

from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import IntegrityError
from rest_framework import serializers

from accounts.models import ConstantTable
from payment.models import Transaction

from .model_choices import Frequency
from .models import Halal

pattern = r"^[a-zA-Z][a-zA-Z0-9 ]*$"


class HLPlanDetailsSerializer(serializers.Serializer):
    plan = serializers.CharField(min_length=2)
    duration = serializers.CharField(min_length=1)
    target = serializers.FloatField()
    frequency = serializers.CharField(min_length=5)

    def validate(self, attrs):
        plan = attrs.get("plan", "")
        duration = attrs.get("duration", "")
        target = attrs.get("target", "")
        frequency = attrs.get("frequency", "")

        constants = ConstantTable.get_constant_table_instance()

        if not re.match(pattern=pattern, string=plan):
            raise serializers.ValidationError(
                "the plan name should start with a letter and can have numbers and spaces"
            )

        if not duration.isdigit():
            raise serializers.ValidationError("The duration should be numeric only")

        if target < constants.halal_minimum_target:
            raise serializers.ValidationError(f"minimu target of {constants.halal_minimum_target}")

        if frequency.upper() in Frequency:
            attrs["frequency"] = frequency.upper()
        else:
            raise serializers.ValidationError("Choose from daily, weekly, monthly or just_once")

        return attrs


# Serializer that creates the Halal instance in the DB
class HalalDetailsSerializer(serializers.ModelSerializer):
    plan = serializers.CharField(min_length=2)
    duration = serializers.CharField(min_length=1)
    target = serializers.FloatField()
    frequency = serializers.CharField(min_length=5)
    lock = serializers.BooleanField()
    hour = serializers.IntegerField(
        validators=[
            MinValueValidator(limit_value=0, message="hour should not be less than 0"),
            MaxValueValidator(limit_value=23, message="hour should not be more than 23"),
        ]
    )

    class Meta:
        model = Halal
        fields = (
            "id",
            "plan",
            "target",
            "duration",
            "frequency",
            "hour",
            "lock",
        )

    def validate(self, attrs):
        plan = attrs.get("plan", "")
        duration = attrs.get("duration", "")
        target = attrs.get("target", "")
        frequency = attrs.get("frequency", "")

        constants = ConstantTable.get_constant_table_instance()

        if not re.match(pattern=pattern, string=plan):
            raise serializers.ValidationError(
                "the plan name should start with a letter and can have numbers and spaces"
            )

        if not duration.isdigit():
            raise serializers.ValidationError("The duration should be numeric only")

        if target < constants.halal_minimum_target:
            raise serializers.ValidationError(f"minimum target of {constants.halal_minimum_target} is required")

        if frequency.upper() in Frequency:
            attrs["frequency"] = frequency.upper()
        else:
            raise serializers.ValidationError("Choose from daily, weekly, monthly or just_once")

        return attrs

    def create(self, validated_data):
        try:
            obj = Halal.objects.create(**validated_data)
            return obj

        # The error below occurs when a user tries to create a new plan with the same name
        except IntegrityError as err:
            raise serializers.ValidationError("you already have a plan with this name")


class ListOfAllHalalSerializer(serializers.ModelSerializer):
    class Meta:
        model = Halal
        fields = [
            "id",
            "plan",
            "amount_saved",
            "target",
            "duration",
            "maturity_date",
            "periodic_amount",
            "frequency",
            "hour",
            "is_activated",
            "completed",
            "is_active",
            "lock",
            "recurrent_saving_status",
        ]


class HLCurrentPlanDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = Halal
        fields = [
            "id",
            "plan",
            "amount_saved",
            "target",
            "duration",
            "maturity_date",
            "periodic_amount",
            "frequency",
            "hour",
            "is_activated",
            "completed",
            "is_active",
            "lock",
            "recurrent_saving_status",
        ]


class RetrieveHalalInstanceSerializer(serializers.Serializer):
    id = serializers.IntegerField()


class HLTransactionHistorySerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()
    amount = serializers.FloatField()

    class Meta:
        model = Transaction
        fields = [
            "id",
            "amount",
            "transaction_id",
            "transaction_form_type",
            "status",
            "date_created",
            "transaction_date_completed",
        ]


class HLTotalBalanceAndSavingsSerializer(serializers.Serializer):
    total_amount = serializers.FloatField()
    savings = serializers.IntegerField()


class TotalBalanceSavingsListOfAllHalalSavingsSerializer(serializers.ModelSerializer):
    total_balance = serializers.FloatField()
    savings = serializers.IntegerField()

    class Meta:
        model = Halal
        fields = [
            "total_balance",
            "savings",
            "id",
            "plan",
            "target",
            "amount_saved",
            "duration",
            "maturity_date",
        ]
