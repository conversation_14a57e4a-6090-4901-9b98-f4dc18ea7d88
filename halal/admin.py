from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from savings.admin import set_readonly_fields

from .models import Halal

####################################################################################
# RESOURCES


class HalalResource(resources.ModelResource):
    class Meta:
        model = Halal


####################################################################################
# RESOURCE ADMINS


class HalalResourceAdmin(ImportExportModelAdmin):
    resource_class = HalalResource
    search_fields = ["user__email", "plan", "quotation_id", "lock", "is_activated"]
    readonly_fields = set_readonly_fields(
        "lock",
        "duration",
        "target",
        "interest_rate",
        "periodic_amount",
        "estimated_amount",
        "amount_saved",
        "plan_balance_before",
        "plan_balance_after",
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


####################################################################################
# REGISTER MODELS

admin.site.register(Halal, HalalResourceAdmin)
