from django.urls import path

from .views import (
    CurrentPlanDetailsAPIView,
    HalalDetailsAPIView,
    PlanDetailsAPIView,
    TotalBalanceAndSavingsAPIView,
    TransactionHistoryAPIView,
)

urlpatterns = [
    # URL collects the plan, duration and target
    path("generate-plan-details/", PlanDetailsAPIView.as_view(), name="plan_details"),
    # URL to submit the halal details to the DB and get a quotation id
    path("initiate-halal-plan/", HalalDetailsAPIView.as_view(), name="halal_details"),
    # URL to list all the halal plans a user has
    # path("listhalalplans/", ListOfAllHalalAPIView.as_view(), name="list_of_all_halal"),
    # URL to obtain the current plan details of a halal instance
    path("current-plan-details/", CurrentPlanDetailsAPIView.as_view(), name="current_plan_details"),
    # URL to get the transaction history of a plan
    path("transaction-history/", TransactionHistoryAPIView.as_view(), name="transaction_history"),
    # URL to get total balance and savings of the chestlock and to list all the halal plans a user has
    path("total-list-halal/", TotalBalanceAndSavingsAPIView.as_view(), name="total_balance_savings"),
]
