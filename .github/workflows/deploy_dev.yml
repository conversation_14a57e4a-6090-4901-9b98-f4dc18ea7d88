name: Deploy to Dev Server

on:
  push:
    branches:
      - development

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Deploy to dev
      env:
        DEV_SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
        DEV_SERVER_IP: ${{ secrets.DEV_SERVER_IP }}
        DEV_SERVER_USER: ${{ secrets.SERVER_USER }}
      run: |
        echo "$DEV_SSH_PRIVATE_KEY" > deploy_key
        chmod 600 deploy_key
        ssh -i deploy_key -o StrictHostKeyChecking=no $DEV_SERVER_USER@$DEV_SERVER_IP << 'EOF'
          cd /home/<USER>/savingsmain
          git pull origin development
          source venv/bin/activate && python manage.py makemigrations && python manage.py migrate
          systemctl restart celerysavings.service
          systemctl restart savingscelerybeat.service
          systemctl restart savings.gunicorn
          echo "Done"
        EOF

