name: Deploy to Server

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Deploy to prod
      env:
        SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
        SERVER_IP: ${{ secrets.SERVER_IP }}
        SERVER_USER: ${{ secrets.SERVER_USER }}
      run: |
        mkdir -p ~/.ssh
        echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh -o StrictHostKeyChecking=no -i ~/.ssh/id_rsa $SERVER_USER@$SERVER_IP << EOF
          cd /home/<USER>
          git pull origin main
          source venv/bin/activate && python manage.py makemigrations && python manage.py migrate
          systemctl restart gunicorn celery celerybeat celerysavingselegibles
          sleep 10
          systemctl restart gunicorn2
          echo "Done"
        EOF
