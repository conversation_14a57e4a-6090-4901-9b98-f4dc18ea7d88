from django.db.models import Q
from django.db.models.signals import pre_save
from django.dispatch import receiver

from .models import Loan, Prefunding


############LOAN SIGNALS####################
@receiver(pre_save, sender=Loan)
def check_active_loan_signal(sender, instance, **kwargs):
    if (
        instance.is_active
        and Loan.objects.filter(user=instance.user, ajo_user=instance.ajo_user, completed=False)
        .exclude(pk=instance.pk)
        .exists()
    ):
        raise ValueError("an active, uncompleted loan already exists for this user")


#########PREFUNDING SIGNALS#################
@receiver(pre_save, sender=Prefunding)
def check_active_prefunding_signal(sender, instance, **kwargs):
    if (
        not instance.completed
        and Prefunding.objects.filter(Q(completed=False) | Q(balance_left__gt=0), user=instance.user)
        .exclude(pk=instance.pk)
        .exists()
    ):
        raise ValueError("an active, incomplete prefunding already exists for this user")
