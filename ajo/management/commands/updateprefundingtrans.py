import time
from typing import Any, List, Optional

from django.core.management.base import BaseCommand

from payment.model_choices import (
    TransactionFormType,
    TransactionTypeCreditOrDebitChoices,
    WalletTypes,
)
from payment.models import Transaction


class Command(BaseCommand):
    help = "this will update all the ajo prefunding repayment transactions form type and transaction type from WALLET_DEPOSIT and CREDIT to PREFUNDING_REPAYMENT and DEBIT respectively"

    def handle(self, *args: Any, **options: Any) -> str | None:
        start_time = time.time()
        self.stdout.write(self.style.SUCCESS("Beginning the operation to change the transaction type and form type"))

        # obtain the queryset
        prefunding_transaction_qs = Transaction.objects.filter(
            transaction_form_type=TransactionFormType.WALLET_DEPOSIT,
            wallet_type=WalletTypes.AJO_PREFUNDING,
            transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
            unique_reference__icontains="remit",
        )
        print("prefunding transactions queryset: ", prefunding_transaction_qs)

        # update the transactions
        prefunding_transaction_qs.update(
            transaction_form_type=TransactionFormType.PREFUNDING_REPAYMENT,
            transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
        )

        end_time = time.time()
        elapsed_time = end_time - start_time

        self.stdout.write(
            self.style.SUCCESS(f"updated the transactions type and form type in {elapsed_time:.4f} seconds.")
        )
