from django.core.management import BaseCommand
from accounts.models import CustomUser

from loans.helpers.loan_decisioning import loan_decisoining_logic

class Command(BaseCommand):
    help = ""

    def handle(self, *args, **options):
        user = CustomUser.objects.filter(id=2).last()
        all = loan_decisoining_logic()
        print(user)
        

        print("\n\nn\nn\n\n\n")
        print("this is th response", all)
       