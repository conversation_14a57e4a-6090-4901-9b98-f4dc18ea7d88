from typing import Any

from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand, CommandParser

from ...models import AjoUser
from ...selectors import AjoAgentSelector, AjoUserSelector
from ...services import remit_not_withdrawn_closed_plans_to_spend


class Command(BaseCommand):
    help = "Moves money from closed ajo plans into the ajo user's spend wallet"

    def add_arguments(self, parser: CommandParser) -> None:
        # define command-line arguments
        parser.add_argument(
            "-number", type=str, help="put the phone number to remit ajo user's closed plans to spend wallet"
        )

        parser.add_argument("-email", type=str, help="the email of the user the ajo user belongs to")

    def handle(self, *args: Any, **options: Any) -> str | None:
        phone_number = options["number"]
        user_email = options["email"]

        if phone_number and not user_email:
            self.stdout.write(self.style.ERROR("you need to add an email with '-email' to obtain ajo user"))
            return

        self.stdout.write(self.style.SUCCESS("OPERATION BEGINNING..."))

        # For One Ajo User Only
        if phone_number:
            self.stdout.write(self.style.SUCCESS("Particular Ajo User Mode"))

            try:
                user = get_user_model().objects.get(email=user_email)
                ajo_user = AjoUserSelector(phone_number=phone_number, user=user).get_ajo_user()

                result = remit_not_withdrawn_closed_plans_to_spend(ajo_user=ajo_user)

                self.stdout.write(self.style.SUCCESS(result))
                return

            except get_user_model().DoesNotExist:
                self.stdout.write(self.style.ERROR(f"no account with the email: {user_email}"))
            except ValueError as err:
                self.stdout.write(self.style.ERROR(str(err)))
            except Exception as err:
                self.stdout.write(self.style.ERROR(str(err)))

        # For all the Ajo Users under an Agent
        elif user_email and not phone_number:
            self.stdout.write(self.style.SUCCESS("Agent's Ajo Users Mode"))
            try:
                user = get_user_model().objects.get(email=user_email)

                ajo_agent_selector = AjoAgentSelector(user=user)

                ajo_users = ajo_agent_selector.ajo_users_qs

                responses = []
                for ajo_user in ajo_users:
                    try:
                        result = remit_not_withdrawn_closed_plans_to_spend(ajo_user=ajo_user)
                        responses.append(result)
                    except ValueError as err:
                        responses.append(str(err))

                self.stdout.write(self.style.SUCCESS(f"results: {responses}"))
                return

            except get_user_model().DoesNotExist:
                self.stdout.write(self.style.ERROR(f"no account with the email: {user_email}"))
            except Exception as err:
                self.stdout.write(self.style.ERROR(str(err)))

        # For All Ajo Users in System
        else:
            self.stdout.write(self.style.SUCCESS("All Ajo Users Mode"))

            ajo_users = AjoUser.objects.all()

            responses = []

            for ajo_user in ajo_users:
                try:
                    result = remit_not_withdrawn_closed_plans_to_spend(ajo_user=ajo_user)
                    responses.append(result)
                except ValueError as err:
                    responses.append(str(err))

            self.stdout.write(self.style.SUCCESS(f"results: {responses}"))
