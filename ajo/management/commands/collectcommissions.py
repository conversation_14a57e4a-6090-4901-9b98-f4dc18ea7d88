from typing import Any

from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from django.db.models import Sum
from django.utils import timezone

from payment.model_choices import CommissionType, PlanType
from payment.models import Commission
from payment.services import TransactionService

from ...payment_actions import fund_wallet_and_update_transaction
from ...selectors import AjoCommissionsSelector


class Command(BaseCommand):
    help = "Collects all the commissions that have not been withdrawn from the commissions table and moves that balance to the Agent's Commission's wallet"

    def add_arguments(self, parser):
        # Define command-line arguments
        parser.add_argument(
            "--company",
            action="store_true",
            help="To specify that company commissions should be moved to the company's wallet",
        )

        parser.add_argument(
            "--user",
            type=str,
            help="To specify that a user's commissions should be moved to the user's wallet. Insert Email Address",
        )

    def handle(self, *args: Any, **options: Any) -> str | None:
        company_mode = options["company"]
        user_mode = options["user"]

        if company_mode and user_mode:
            self.stdout.write(self.style.ERROR("you need to choose either company mode or user mode"))
            return

        # date when PR for new commissions method was made
        date_limit = timezone.make_aware(
            timezone.datetime(
                year=2023,
                month=9,
                day=14,
                hour=16,
                minute=26,
            )
        )

        if company_mode:
            self.stdout.write(self.style.SUCCESS("Company Mode"))

            # obtain the company's user account
            try:
                company = get_user_model().objects.get(email="<EMAIL>")
            except get_user_model().DoesNotExist:
                self.stdout.write(
                    self.style.ERROR("create a company account with '<EMAIL>' as the email")
                )

            # update the commissions table to reflect the company's account as user
            company_commissions_without_user_qs = Commission.objects.filter(
                commission_type=CommissionType.COMPANY,
                user__isnull=True,
            )

            if company_commissions_without_user_qs:
                company_commissions_without_user_qs.update(user=company)

            # fetch all the company commissions not withdrawn
            company_commissions_not_withdrawn_qs = Commission.objects.filter(
                user=company,
                withdrawn=False,
            )
            # total amount
            total_amount = round(
                company_commissions_not_withdrawn_qs.aggregate(total=Sum("amount"))["total"] or 0,
                2,
            )

            if total_amount <= 0:
                self.stdout.write(self.style.ERROR(f"No commissions to move for the company"))
                return

            # proceed to move the money to the company's commission wallet
            company_commissions_wallet = AjoCommissionsSelector(user=company).get_commissions_wallet()

            # create the transaction instance stating that the money to the commissions wallet
            increment_transaction = TransactionService.create_agent_commissions_increment_transaction(
                user=company,
                amount=total_amount,
                quotation_id=None,
                transaction_description=f"{total_amount} in commissions was moved to the company's commissions wallet",
            )

            # incremenet the balance of the company's commisisons wallet
            fund_wallet_and_update_transaction(
                wallet=company_commissions_wallet,
                amount=total_amount,
                transaction_instance=increment_transaction,
            )

            # update the commissions table
            company_commissions_not_withdrawn_qs.update(withdrawn=True)

            self.stdout.write(
                self.style.SUCCESS(f"{total_amount} was successfully moved to the company's commissions wallet")
            )

        elif user_mode:
            self.stdout.write(self.style.SUCCESS("Particular User Mode"))

            try:
                user = get_user_model().objects.get(email=user_mode)
            except get_user_model().DoesNotExist:
                self.stdout.write(self.style.ERROR(f"no account with the email: {user_mode}"))

            commissions_selector = AjoCommissionsSelector(user=user)

            commissions_qs = Commission.objects.filter(
                user=user,
                plan_type=PlanType.AJO,
            )

            commissions_not_withdrawn_qs = commissions_qs.filter(
                withdrawn=False,
                created_at__lte=date_limit,
            )

            total_amount_of_commissions = round(
                commissions_not_withdrawn_qs.aggregate(total=Sum("amount"))["total"] or 0, 2
            )

            if total_amount_of_commissions <= 0:
                self.stdout.write(self.style.ERROR(f"No commissions to move for {user.email}"))
                return

            # obtain the commissions wallet
            commissions_wallet = commissions_selector.get_commissions_wallet()

            # create the transaction instance stating that the money to the commissions wallet
            increment_transaction = TransactionService.create_agent_commissions_increment_transaction(
                user=user,
                amount=total_amount_of_commissions,
                quotation_id=None,
                transaction_description=f"{total_amount_of_commissions} of your commissions was moved to your commissions wallet",
            )

            # increment the balance of the agent's commissions wallet
            fund_wallet_and_update_transaction(
                wallet=commissions_wallet,
                amount=total_amount_of_commissions,
                transaction_instance=increment_transaction,
            )

            # update the commmissions table
            # not_withdrawn_commissions_qs = commissions_selector.get_list_of_not_withdrawn_commissions()
            commissions_not_withdrawn_qs.update(withdrawn=True)

            self.stdout.write(
                self.style.SUCCESS(
                    f"{total_amount_of_commissions} was successfully moved to {user.email}'s commissions wallet"
                )
            )

        else:
            self.stdout.write(self.style.SUCCESS("Agent Mode"))
            # obtain all the users
            users_qs = get_user_model().objects.all()

            for user in users_qs:
                # call the commissions selector
                commissions_selector = AjoCommissionsSelector(user=user)

                # # obtain the total amount of commissions not withdrawn
                # total_amount_of_commissions = commissions_selector.get_total_amount_of_commissions_not_withdrawn()

                # NOTE: I wrote a custom query because of inconsistency.
                # This query works with the time that the new change was merged into the main repo
                # and went live

                commissions_qs = Commission.objects.filter(
                    user=user,
                    plan_type=PlanType.AJO,
                )

                commissions_not_withdrawn_qs = commissions_qs.filter(
                    withdrawn=False,
                    created_at__lte=date_limit,
                )

                total_amount_of_commissions = round(
                    commissions_not_withdrawn_qs.aggregate(total=Sum("amount"))["total"] or 0,
                    2,
                )

                if total_amount_of_commissions <= 0:
                    self.stdout.write(self.style.ERROR(f"No commissions to move for {user.email}"))
                    continue

                # obtain the commissions wallet
                commissions_wallet = commissions_selector.get_commissions_wallet()

                # create the transaction instance stating that the money to the commissions wallet
                increment_transaction = TransactionService.create_agent_commissions_increment_transaction(
                    user=user,
                    amount=total_amount_of_commissions,
                    quotation_id=None,
                    transaction_description=f"{total_amount_of_commissions} of your commissions was moved to your commissions wallet",
                )

                # increment the balance of the agent's commissions wallet
                fund_wallet_and_update_transaction(
                    wallet=commissions_wallet,
                    amount=total_amount_of_commissions,
                    transaction_instance=increment_transaction,
                )

                # update the commmissions table
                # not_withdrawn_commissions_qs = commissions_selector.get_list_of_not_withdrawn_commissions()
                commissions_not_withdrawn_qs.update(withdrawn=True)

                self.stdout.write(
                    self.style.SUCCESS(
                        f"{total_amount_of_commissions} was successfully moved to {user.email}'s commissions wallet"
                    )
                )

        self.stdout.write(
            msg="Commissions wallet reflection operation completed successfully",
            style_func=self.style.SUCCESS,
        )
