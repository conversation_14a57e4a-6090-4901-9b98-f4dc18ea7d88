from typing import Any, Optional

from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand

from ajo.model_choices import TransactionDescriptionType
from ajo.selectors import AjoAgentSelector, AjoUserSelector
from payment.model_choices import WalletTypes

from ...payment_actions import move_money_from_wallet_to_wallet


class Command(BaseCommand):
    help = "Moves money from the spend wallet to the agent's wallet"
    help += " e.g. python manage.py movespendtoagent --number 08073231219 --email <EMAIL> --amount 80000.50"

    def add_arguments(self, parser):
        # Define command-line arguments
        parser.add_argument(
            "--number",
            type=str,
            help="the phone number of the ajo user",
        )

        parser.add_argument(
            "--email",
            type=str,
            help="the email of the ajo user's agent",
        )

        parser.add_argument(
            "--amount",
            type=str,
            help="the amount to move",
        )

    def handle(self, *args: Any, **options: Any) -> str | None:
        number: Optional[str] = options["number"]
        email: Optional[str] = options["email"]
        amount: Optional[str] = options["amount"]

        if not number:
            self.stdout.write(self.style.ERROR(f"use the --number to put the ajo user's phone number"))
            return

        if not email:
            self.stdout.write(self.style.ERROR(f"use the --email to put the agent's email"))
            return

        if amount:
            try:
                amount = float(amount)
            except ValueError:
                self.stdout.write(self.style.ERROR(f"--amount value is not a digit"))
                return
            amount = round(amount, 2)

        try:
            user = get_user_model().objects.get(email=email)
            ajo_selector = AjoUserSelector(phone_number=number, user=user)
            ajo_user = ajo_selector.get_ajo_user()
        except get_user_model().DoesNotExist:
            self.stdout.write(self.style.ERROR("check the email or phone number, one or both does not exist"))
            return
        except ValueError as err:
            self.stdout.write(self.style.ERROR(f"an error occurred: {str(err)}"))
            return

        spend_wallet = ajo_selector.get_spending_wallet()
        agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()

        if not amount:
            amount = spend_wallet.available_balance

        move_money_from_wallet_to_wallet(
            amount=amount,
            from_wallet=spend_wallet,
            from_wallet_description=f"{amount} was transferred to agent's wallet",
            to_wallet=agent_wallet,
            to_wallet_description=f"{amount} was deposited to your ajo agent wallet from {number}",
        )

        self.stdout.write(self.style.SUCCESS(f"{amount} was successfully moved from spend to agent wallet"))
        return
