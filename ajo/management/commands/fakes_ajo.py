from django.core.management.base import BaseCommand
from payment.models import Transaction


class Command(BaseCommand):
   
    def handle(self, *args, **kwargs):
        # for trans in Transaction.objects.filter(transaction_form_type="LIB_PROSPER_LOAN_DEPOSIT"):
        #     trans.transaction_form_type = "WALLET_DEPOSIT"
        #     trans.description = f"{trans.amount} was funded into your wallet from Liberty Pay."
        #     trans.save()

        from django.db.models import OuterRef, Subquery
        from accounts.models import CustomUser
        from ajo.models import AjoSaving

  
        subquery = AjoSaving.objects.filter(user_id=OuterRef("pk"), amount_saved__gt=30).values("user_id")

        # Query to fetch User instances which have Ajosaving records with age savings than 30
        users_with_savings_gt_30 = CustomUser.objects.filter(pk__in=subquery)

        # Execute the query
        result = users_with_savings_gt_30.all()

        print(users_with_savings_gt_30)
        print(type(users_with_savings_gt_30))

        print(result)
        print(type(result))
