from typing import Any

from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from django.db.models import Sum
from django.utils import timezone

from accounts.model_choices import UserType
from payment.model_choices import CommissionType, PlanType, WalletTypes
from payment.models import Commission, WalletSystem
from payment.services import TransactionService

from ...payment_actions import move_money_from_wallet_to_wallet
from ...selectors import AjoCommissionsSelector


class Command(BaseCommand):
    help = "Clear all the Ajo Commissions present in Staff Agents Ajo Commission Wallets"

    def handle(self, *args: Any, **options: Any) -> str | None:
        # obtain the staff ajo commissions wallets with positive wallet balance
        wallets_with_value = WalletSystem.objects.filter(
            user__user_type=UserType.STAFF_AGENT,
            wallet_type=WalletTypes.AJO_COMMISSION,
            available_balance__gt=0,
        )

        if wallets_with_value.count() <= 0:
            self.stdout.write(self.style.ERROR("no staff agent has money in the ajo commissions wallet to be moved"))
            return

        # obtain the company user
        company_user = get_user_model().objects.filter(email="<EMAIL>").last()

        for wallet in wallets_with_value:
            user = wallet.user

            commissions_qs = Commission.objects.filter(
                user=user,
                plan_type=PlanType.AJO,
            )

            commissions_not_withdrawn_qs = commissions_qs.filter(
                withdrawn=False,
            )

            # obtain the company's commission wallet
            company_commissions_wallet = AjoCommissionsSelector(user=company_user).get_commissions_wallet()

            amount = wallet.available_balance
            # move the money from the ajo commissions wallet to the savings commissions wallet
            move_money_from_wallet_to_wallet(
                amount=amount,
                from_wallet=wallet,
                from_wallet_description="staff agent clearing operation",
                to_wallet=company_commissions_wallet,
                to_wallet_description="commissions from staff agent clearing operation in order to begin commissions distribution",
            )

            commissions_not_withdrawn_qs.update(withdrawn=True)

            self.stdout.write(
                self.style.SUCCESS(
                    f"{amount} was successfully moved from {user.email}'s ajo commissions wallet to the company's commissions wallet"
                )
            )

        self.stdout.write(
            msg="Commissions in staff agents' wallets have been moved successfully",
            style_func=self.style.SUCCESS,
        )
