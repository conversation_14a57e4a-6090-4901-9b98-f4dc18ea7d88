import time
from typing import Any, List, Optional

from django.core.management.base import BaseCommand
from django.db import models
from django.db.models import Duration<PERSON><PERSON>, ExpressionWrapper, F
from django.utils import timezone

from ...models import AjoSaving


class Command(BaseCommand):
    help = "Reflects the maturity date in all the Ajo plans"

    def handle(self, *args: Any, **options: Any) -> str | None:
        start_time = time.time()
        self.stdout.write(self.style.SUCCESS("Beginning the operation."))

        # Calculate the maturity date based on the created_at date and duration
        maturity_expression = ExpressionWrapper(
            F("created_at")
            + ExpressionWrapper(
                F("duration") * timezone.timedelta(days=1),
                output_field=DurationField(),
            ),
            output_field=models.DateField(),
        )

        # Update the AjoSaving records that have a null maturity date
        AjoSaving.objects.filter(maturity_date__isnull=True).update(maturity_date=maturity_expression)

        end_time = time.time()
        elapsed_time = end_time - start_time

        self.stdout.write(
            msg=f"Updating the maturity date of all Ajo plans completed in {elapsed_time: .4f} seconds.",
            style_func=self.style.SUCCESS,
        )
