import datetime
import os
import random
import secrets
import string
import uuid
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Dict

from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.core.validators import MinValueValidator
from django.db import models
from django.db import transaction as django_transaction
from django.db.models import Count
from django.utils import timezone
from django.utils.crypto import get_random_string
from django.utils.translation import gettext as _

from accounts.helpers import BaseModel, RoundedFloatField
from accounts.models import AgentSwitchLog, ConstantTable, ContentType
from loans.enums import AddressVerificationStage, RepaymentFrequency, Tenure


from .model_choices import (
    AccountFormType,
    AccountProvider,
    AjoAgentStatus,
    AjoPlanTypes,
    Gender,
    GroupStatus,
    OnboardingSource,
    OnboardingStage,
    CategoryNames,
    ProfileChangeStatus,
    RoscaCollectionType,
    RoscaGroupStatus,
    RoscaType,
    SavingsFrequency,
    SavingsType,
)


################## AJO USER FUNCTIONS FOR MODEL FIELDS######################
def generate_filename(instance, filename):
    """
    The aim of this is to control what the filename will be saved as and
    in what directory it should be saved
    """
    # extract the first name and last name from the model instance
    first_name = instance.first_name
    last_name = instance.last_name

    # generate a small random string to add to the file name to eliminate
    # issues like two people having the same name and uploading the file
    # replaces an existing file
    random_string = get_random_string(length=3, allowed_chars="**********")

    # Split the original filename into name and extension
    name, extension = os.path.splitext(filename)

    # Construct the final filename using the first, last names, random string
    final_filename = f"{first_name}_{last_name}_{random_string}{extension}"

    # Return the path
    return f"ajo_capture/{final_filename}"


def generate_util_filename(instance, filename):
    """
    The aim of this is to control what the filename will be saved as and
    in what directory it should be saved
    """
    # extract the first name and last name from the model instance
    first_name = instance.first_name
    last_name = instance.last_name

    # generate a small random string to add to the file name to eliminate
    # issues like two people having the same name and uploading the file
    # replaces an existing file
    random_string = get_random_string(length=3, allowed_chars="**********")

    # Split the original filename into name and extension
    name, extension = os.path.splitext(filename)

    # Construct the final filename using the first, last names, random string
    final_filename = f"{first_name}_{last_name}_{random_string}{extension}"

    # Return the path
    return f"ajo_util/{final_filename}"


def generate_user_pin() -> str:
    """
    Generates a 4 digit pin

    Returns:
        str: the four digits pin
    """
    return "".join(random.sample("**********", 4))


class AjoUser(BaseModel):
    # the user that is the agent for the ajo user
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        db_index=True,
    )

    # the user created for the liberty lite app
    lite_user = models.OneToOneField(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        db_index=True,
        related_name="lite_users",
    )

    # necessary field
    phone_number = models.CharField(max_length=100, db_index=True, unique=True)
    pin = models.CharField(max_length=100, editable=False, default=generate_user_pin)
    lite_transaction_pin = models.CharField(max_length=255, editable=False, null=True)

    # user data
    first_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100, blank=True, null=True)
    alias = models.CharField(max_length=100, blank=True, null=True)
    gender = models.CharField(
        max_length=100, blank=True, null=True, choices=Gender.choices
    )
    marital_status = models.CharField(max_length=100, blank=True, null=True)
    dob = models.DateField(null=True, blank=True)
    state = models.CharField(max_length=100, blank=True, null=True)
    lga = models.CharField(max_length=100, blank=True, null=True)
    address = models.CharField(max_length=355, blank=True, null=True)
    bus_stop = models.CharField(max_length=100, blank=True, null=True)
    landmark = models.CharField(max_length=550, blank=True, null=True)
    trade = models.CharField(max_length=100, blank=True, null=True)
    trade_location = models.CharField(max_length=255, blank=True, null=True)
    bvn = models.CharField(max_length=100, null=True, blank=True)
    nin = models.CharField(max_length=100, unique=True, null=True, blank=True)
    image = models.FileField(upload_to=generate_filename, blank=True, null=True)

    # fields for location collection
    longitude = models.FloatField(null=True, blank=True, default=0.0)
    latitude = models.FloatField(null=True, blank=True, default=0.0)
    location_state = models.CharField(max_length=150, blank=True, null=True)
    location_city = models.CharField(max_length=150, blank=True, null=True)
    location_country = models.CharField(max_length=100, blank=True, null=True)
    location_address = models.CharField(max_length=550, blank=True, null=True)

    # fields for location verification
    verified_location_lga = models.CharField(max_length=150, blank=True, null=True)
    verified_location_state = models.CharField(max_length=150, blank=True, null=True)
    verified_location_city = models.CharField(max_length=150, blank=True, null=True)
    verified_location_country = models.CharField(max_length=100, blank=True, null=True)
    verified_location_address = models.CharField(max_length=550, blank=True, null=True)
    verified_landmark = models.CharField(max_length=550, blank=True, null=True)
    verified_bus_stop = models.CharField(max_length=100, blank=True, null=True)
    verification_retries = models.IntegerField(default=1)

    verification_stage = models.CharField(
        max_length=100,
        choices=AddressVerificationStage.choices,
        default="ADDRESS_VERIFICATION",
    )
    verification_status = models.CharField(max_length=255, blank=True, null=True)
    verification_score = models.CharField(max_length=255, blank=True, null=True)
    verification_date = models.DateTimeField(null=True, blank=True)
    verification_data = models.JSONField(null=True, blank=True)
    verification_message = models.CharField(max_length=255, blank=True, null=True)
    verification_landmark_list = models.JSONField(null=True, blank=True)
    location_verification_is_started = models.BooleanField(default=False)
    location_is_verified = models.BooleanField(default=False)

    verification_utility_bill = models.FileField(
        upload_to=generate_util_filename, blank=True, null=True
    )
    second_location_city = models.CharField(max_length=150, blank=True, null=True)
    second_location_lga = models.CharField(max_length=150, blank=True, null=True)
    second_location_state = models.CharField(max_length=150, blank=True, null=True)
    second_location_address = models.CharField(max_length=550, blank=True, null=True)
    second_location_country = models.CharField(max_length=100, blank=True, null=True)
    second_location_landmark = models.CharField(max_length=100, blank=True, null=True)
    second_location_bus_top = models.CharField(max_length=100, blank=True, null=True)

    # fields for checks
    onboarding_verified = models.BooleanField(default=False, db_index=True)
    need_card = models.BooleanField(default=False, db_index=True)
    card_assigned = models.BooleanField(default=False)
    card_issued = models.BooleanField(default=False, db_index=True)
    bvn_verified = models.BooleanField(default=False, db_index=True)
    agent_bvn_used = models.BooleanField(default=False)
    sent_user_info_text = models.BooleanField(default=False)
    onboarding_complete = models.BooleanField(default=False, db_index=True)
    charge_verification = models.BooleanField(default=True)
    loan_verification_completed = models.BooleanField(default=False)
    onboarding_stage = models.CharField(
        max_length=100, choices=OnboardingStage.choices, null=True, blank=True
    )
    lite_bvn_check = models.BooleanField(default=False)
    lite_address_check = models.BooleanField(default=False)
    provider = models.CharField(
        max_length=5, choices=(("ajo", "ajo"), ("lite", "lite")), default="ajo"
    )

    # data tracking(money)
    total_money_saved = RoundedFloatField(
        default=0.00,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="total money saved should be >= 0"
            ),
        ],
    )
    total_money_withdrawn = RoundedFloatField(
        default=0.00,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="tota money withdrawn should be >= 0"
            ),
        ],
    )
    total_withdrawals = models.IntegerField(
        default=0,
        validators=[
            MinValueValidator(
                limit_value=0, message="total withdrawals should be >= 0"
            ),
        ],
    )
    loandisk_borrower_id = models.CharField(max_length=300, blank=True, null=True)
    branch_loandisk_borrower_id = models.CharField(
        max_length=300, blank=True, null=True
    )
    branch_name = models.CharField(max_length=100, null=True, blank=True)
    age = models.IntegerField(null=True, blank=True)
    passcode = models.CharField(max_length=500, editable=False, null=True, blank=True)
    onboarding_source = models.CharField(
        max_length=100,
        choices=OnboardingSource.choices,
        default=OnboardingSource.MOBILE_APP,
        null=True,
        blank=True,
    )
    used_web_via_agent = models.BooleanField(
        default=False,
        help_text="True if the saver onboarded by an agent has also accessed the web platform.",
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["user", "phone_number"],
                name="unique_user_ajo_user_relation",
                violation_error_message="an ajo user with this phone number already exists for this agent",
            ),
        ]

    def __str__(self) -> str:
        if self.user:
            string_name = "agent: {}, fullname: {}, number: {}".format(
                self.user.email,
                self.fullname,
                self.phone_number,
            )
        else:
            string_name = "fullname: {}, number: {}".format(
                self.fullname,
                self.phone_number,
            )

        return string_name

    @property
    def fullname(self) -> str:
        first_name = self.first_name if self.first_name else ""
        last_name = self.last_name if self.last_name else ""
        return f"{first_name} {last_name}"

    @classmethod
    def calculate_ajo_user_loan_eligibility(self):
        return self.first_name

    @property
    def phone(self):
        """Extracts and returns the corrected phone number from a string that may contain a separator.

        This method is designed to correct phone numbers that contain the '---' separator.
        It works by splitting the phone number string at the '---' and returning the first part,
        which represents the actual phone number before the separator.

        Args:
            phone_number (str): The input phone number, which may contain '---' as a separator.

        Returns:
            str: The corrected phone number (portion before '---').

        Example:
            >>> get_phone_number('1234567890---extra info')
            '1234567890'
        """
        ajouser_phonumber = self.phone_number.split("---")
        return ajouser_phonumber[0]

    def validate_onboarding_stage(self):
        required_stage = "ADDRESS_VERIFIED"

        onboarding_stage = self.onboarding_stage

        if onboarding_stage is not None and onboarding_stage != required_stage:
            self.onboarding_complete = False
            self.save()
            raise NotImplementedError(
                f"Your onboarding process is not complete. You are currently in the ({onboarding_stage}) stage. "
                "Please complete all required steps to proceed."
            )

    def mark_address_verified(self, verification_data):
        self.verification_retries += 1
        self.lga = verification_data.get("lga")
        self.state = verification_data.get("state")
        self.city = verification_data.get("city")
        self.country = verification_data.get("country")
        self.address = verification_data.get("address")
        self.landmark = verification_data.get("landmark")
        self.bus_stop = verification_data.get("bus_stop")
        self.verification_status = verification_data.get("status")
        self.verification_score = verification_data.get("score")
        self.verification_data = verification_data.get("data")
        self.verification_message = verification_data.get("message")
        self.verification_landmark_list = verification_data.get("all_landmark")
        self.location_verification_is_started = True
        self.verification_status = "CONFIRMED"
        # self.verification_date = end_date
        self.location_is_verified = True
        self.verification_stage = "ADDRESS_VERIFIED"
        self.onboarding_stage = "ADDRESS_VERIFIED"
        self.onboarding_complete = True
        self.save()

    def update_address_with_address_verification_log(self):
        from location_app.enums import VerificationMethod
        from location_app.models import AddressVerificationLog

        _log = AddressVerificationLog.objects.filter(
            ajo_user=self, status="CONFIRMED"
        ).last()

        if _log:
            if _log.verification_method == VerificationMethod.AUTO:

                self.verification_retries += 1
                self.verified_location_lga = _log.lga
                self.verified_location_state = _log.state
                self.verified_location_city = _log.city
                self.verified_location_country = _log.country
                self.verified_location_address = (
                    _log.address if not _log.address_line else _log.address_line
                )

                self.verification_landmark_list = _log.all_landmark

                self.save(
                    update_fields=[
                        "verification_retries",
                        "verified_location_lga",
                        "verified_location_state",
                        "verified_location_city",
                        "verified_location_country",
                        "verified_location_address",
                        "verification_landmark_list",
                        "location_verification_is_started",
                    ]
                )
            else:
                self.verification_retries += 1
                self.verified_location_lga = _log.lga
                self.verified_location_state = _log.state
                self.verified_location_city = _log.city
                self.verified_location_country = _log.country
                self.verified_location_address = _log.verified_address

                self.verification_landmark_list = _log.all_landmark

                self.save(
                    update_fields=[
                        "verification_retries",
                        "verified_location_lga",
                        "verified_location_state",
                        "verified_location_city",
                        "verified_location_country",
                        "verified_location_address",
                        "verification_landmark_list",
                        "location_verification_is_started",
                    ]
                )

            return "CONFIRMED"
        else:
            return False

    @property
    def lite_kyc_teir(self):
        """returns kyc teir level"""

        if self.onboarding_verified:
            # all verifications done from the ajo user onboarding
            return 3
        if self.lite_bvn_check:
            # bvn verification done from lite onboarding
            return 2
        elif self.lite_address_check:
            # address verification done from lite app
            return 3

    def update_user_image_from_base64(self, base64_img_str):
        """
        Takes a base64 image string and updates the user's image field

        Args:
            base64_img_str (str): Base64 encoded image string
        """
        try:
            import base64
            from django.core.files.base import ContentFile

            # Split the base64 string if it contains the data URI prefix
            split_string = base64_img_str.split(",")
            imgstr = split_string[1] if len(split_string) > 1 else base64_img_str

            # Convert base64 to file
            image_file = ContentFile(base64.b64decode(imgstr), name="user_image.jpg")

            # Update image field
            self.image = image_file
            self.save()
        except Exception as e:
            # Log error but don't break execution
            print(f"Error updating user image: {str(e)}")


class Card(BaseModel):
    # the user which is agent for the cards
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        db_index=True,
    )

    # card information
    card_number = models.CharField(max_length=100, unique=True, db_index=True)
    expiry_date = models.DateField()
    cvv = models.CharField(max_length=100, db_index=True)
    card_pin = models.CharField(max_length=100, null=True, blank=True)
    card_type = models.CharField(max_length=100, null=True, blank=True)

    # data about card owner
    full_name = models.CharField(max_length=255, blank=True, null=True)
    bvn = models.CharField(max_length=100, blank=True, null=True)
    phone_number = models.CharField(max_length=100, blank=True, null=True)

    # ajo_user
    ajo_user = models.OneToOneField(
        to=AjoUser,
        on_delete=models.CASCADE,
        related_name="card",
        null=True,
        blank=True,
        db_index=True,
    )

    # indicate if the Agent's BVN was used and if it has been assigned
    agent_bvn_used = models.BooleanField(default=False)
    card_assigned = models.BooleanField(default=False)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["ajo_user", "card_number"],
                name="unique_ajo_user_card_relation",
                violation_error_message="this card has already been assigned to an ajo user",
            ),
            models.UniqueConstraint(
                fields=["user", "card_number"],
                name="unique_user_card_relation",
                violation_error_message="this card has already been given to an agent",
            ),
        ]


class AjoSavingsPlanType(BaseModel):
    # constant_instance = models.ForeignKey(ConstantTable, on_delete=models.CASCADE, null=True, blank=True, related_name="ajo_plan_types")
    name = models.CharField(max_length=100, db_index=True)
    frequency = models.CharField(
        max_length=100, choices=SavingsFrequency.choices, default="DAILY"
    )
    month_count = models.PositiveSmallIntegerField(default=1)
    lower_limit = RoundedFloatField(
        default=1.0,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="plan balance before should be >= 0"
            ),
        ],
    )
    upper_limit = RoundedFloatField(
        default=1.0,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="plan balance before should be >= 0"
            ),
        ],
    )

    def __str__(self) -> str:
        return f"{self.name} min: {self.lower_limit} max: {self.upper_limit}"

    def save(self, *args, **kwargs):
        self.name = self.name.capitalize()
        return super().save(*args, **kwargs)

    @classmethod
    def get_week_count_30_days(cls, duration_in_days: int):
        number = (duration_in_days / 30) * 4
        if isinstance(number, (int, float)) and number.is_integer():
            return number
        raise Exception("Weeks Duration in days is not an exact multiple of 30 days.")

    @classmethod
    def get_week_count(cls, duration_in_days: int):
        if duration_in_days % 7 == 0:
            return duration_in_days // 7
        else:
            raise Exception(
                "The duration in days is not a multiple of 7 days per week."
            )

    @classmethod
    def get_month_count(cls, duration_in_days: int):
        number = duration_in_days / 30
        if isinstance(number, (int, float)) and number.is_integer():
            return number
        raise Exception("Months Duration in days is not an exact multiple of 30 days.")

    @classmethod
    def round_to_nearest_50(cls, number):
        remainder = number % 50
        if remainder < 25:
            return number - remainder
        else:
            return number + (50 - remainder)

    @classmethod
    def calculate_daily_weekly_one_month_plan(
        cls,
        frequency: str,
        duration_in_days: int,
    ) -> Dict[str, Any]:
        month_count = cls.get_month_count(duration_in_days)
        # week_count = cls.get_week_count(duration_in_days)

        if frequency == "DAILY" and duration_in_days > 30:
            raise Exception("Daily plan must be 30 days")

        if frequency == "MONTHLY" and duration_in_days < 60:
            raise Exception("Monthly plan must be greater than 60 days")

        data = []
        const = ConstantTable.get_constant_table_instance()
        charge = const.main_ajo_daily_charge
        current_date = timezone.localdate()
        end_date = current_date + relativedelta(days=duration_in_days - 1)

        for plan_type in [choice[0] for choice in AjoPlanTypes.choices]:
            plan, created = cls.objects.get_or_create(
                # constant_instance = const,
                frequency=frequency,
                name=plan_type,
                # month_count=month_count,
            )

            lower_limit = plan.lower_limit
            upper_limit = plan.upper_limit

            lower_target = lower_limit * duration_in_days
            upper_target = upper_limit * duration_in_days

            data.append(
                {
                    f"{frequency}_{duration_in_days}_days": {
                        "plan_type": plan_type,
                        "duration_in_days": duration_in_days,
                        "duration_in_months": month_count,
                        "end_date": end_date,
                        "lower_limit": lower_limit,
                        "upper_limit": upper_limit,
                        "lower_target": lower_target,
                        "upper_target": upper_target,
                        "charge": charge,
                    }
                }
            )

        return data

    @classmethod
    def calculate_weekly_monthly_target_plan(cls, frequency, duration_in_days, target):
        if frequency == "MONTHLY":
            count_base = cls.get_month_count(duration_in_days)
        elif frequency == "WEEKLY":
            if duration_in_days > 30:
                count_base = cls.get_week_count(duration_in_days)
            else:
                count_base = cls.get_week_count_30_days(duration_in_days)

        else:
            count_base = duration_in_days

        if target > 900000:
            raise Exception("You cannot save more than N900,000 on an ajo plan")

        if frequency == "DAILY" and duration_in_days > 30:
            raise Exception("Daily plan must be 30 days")

        if frequency == "MONTHLY" and duration_in_days < 60:
            raise Exception(
                "This Monthly plan must be greater than or equal to 60 days"
            )

        const = ConstantTable.get_constant_table_instance()

        if not const.target_based_plans:
            raise Exception("No Target Limits Exist")

        get_from_daily = None

        current_date = timezone.localdate()
        end_date = current_date + relativedelta(days=duration_in_days - 1)

        if frequency == "MONTHLY" or (frequency == "WEEKLY" and duration_in_days > 30):
            for category, limits in const.target_based_plans.items():
                if limits["lower_target"] <= target <= limits["upper_target"]:
                    new_limits = category, limits
                    break
                else:
                    new_limits = None, None
        else:
            get_from_daily = cls.objects.filter(
                frequency=frequency,
                upper_limit__gte=target / count_base,
                lower_limit__lte=target / count_base,
            ).last()

            if get_from_daily:
                new_limits = (
                    get_from_daily.name,
                    {
                        "lower_target": get_from_daily.lower_limit * duration_in_days,
                        "upper_target": get_from_daily.upper_limit * duration_in_days,
                    },
                )
            else:
                new_limits = None, None

        if new_limits == (None, None):
            raise Exception("please increase your target to or above 20,000")

        lower_target = new_limits[1]["lower_target"]
        upper_target = new_limits[1]["upper_target"]

        lower_limit = (
            (lower_target) / (count_base)
            if not get_from_daily
            else get_from_daily.lower_limit
        )
        upper_limit = (
            (upper_target) / (count_base)
            if not get_from_daily
            else get_from_daily.upper_limit
        )

        if frequency == "DAILY":
            charge = const.main_ajo_daily_charge / 100
            commission = round(target * charge)
            contribution = target / count_base

        elif frequency == "WEEKLY" and duration_in_days <= 60:
            charge = const.main_ajo_daily_charge / 100
            commission = round(target * charge)
            contribution = target / count_base

        elif frequency == "WEEKLY" and duration_in_days > 60:
            charge = const.monthly_ajo_charge / 100
            commission = round(target * charge)
            contribution = target / (count_base)

        elif frequency == "MONTHLY" and count_base == 2:
            charge = const.main_ajo_daily_charge / 100
            commission = round(target * charge)
            contribution = target / count_base

        elif frequency == "MONTHLY" and count_base > 2:
            charge = const.monthly_ajo_charge / 100
            commission = round(target * charge)
            contribution = target / count_base
        else:
            charge = 0
            commission = None
            contribution = None

        agent_commission = None
        company_commission = None

        if commission:
            agent_rate: float = (100 - const.commissions_rate) / 100
            agent_commission = round(commission * agent_rate, 2)
            company_commission = round(commission - agent_commission, 2)

        # data = [
        #     {
        #         # f"spot_{frequency}_{duration_in_days}_days": {
        #         f"plan": {
        #             "plan_type": new_limits[0],
        #             "duration_in_days": duration_in_days,
        #             f"{frequency.lower()}_duration": count_base,
        #             "target": target,
        #             "contribution": cls.round_to_nearest_50(contribution),
        #             "commission": commission,
        #             "charge": round(charge * 100, 5),
        #         }
        #     },
        #     # {
        #     #     f"{frequency}_{duration_in_days}_days": {
        #     #         "plan_type": new_limits[0],
        #     #         "duration_in_days": duration_in_days,
        #     #         f"{frequency.lower()}_duration": count_base,
        #     #         "lower_limit": lower_limit,
        #     #         "upper_limit": upper_limit,
        #     #         "lower_target": lower_target,
        #     #         "upper_target": upper_target,
        #     #         "charge": charge,
        #     #     }
        #     # },

        # ]

        return {
            "plan_type": new_limits[0],
            "duration_in_days": duration_in_days,
            f"{frequency.lower()}_duration": count_base,
            "end_date": end_date,
            "target": target,
            "contribution": cls.round_to_nearest_50(contribution),
            "commission": commission,
            "agent_commission": agent_commission,
            "company_commission": company_commission,
            "charge": round(charge * 100, 5),
            "withdrawal_amount": target - commission,
        }

    # Obsolete. Currently not being use. but leave it here
    @classmethod
    def get_contribution_amount(cls, frequency, duration_in_days, target):
        month_count = cls.get_month_count(duration_in_days)

        if frequency == "DAILY" and duration_in_days > 30:
            raise Exception("Daily plan must be 30 days")

        if frequency == "WEEKLY" and duration_in_days < 120:
            raise Exception(f"Weekly plan must be greater than or equal to 4 weeks")

        if frequency == "MONTHLY" and duration_in_days < 60:
            raise Exception("Monthly plan must be greater than 2 months")

        const = ConstantTable.get_constant_table_instance()

        if not const.target_based_plans:
            raise Exception("No Target Limits Exist")

        if frequency == "DAILY" and target < 900000:
            charge = const.main_ajo_daily_charge / 100
            commission = round(target * charge)
            contribution = target / duration_in_days

        elif frequency == "WEEKLY" and target < 900000 and duration_in_days == 120:
            charge = const.main_ajo_daily_charge / 100
            commission = round(target * charge)
            contribution = target / 4

        elif frequency == "WEEKLY" and duration_in_days > 120:
            charge = const.monthly_ajo_charge / 100
            commission = round(target * charge)
            contribution = target / (duration_in_days / 4)

        elif frequency == "MONTHLY" and month_count > 4:
            charge = const.monthly_ajo_charge / 100
            commission = round(target * charge)
            contribution = target / month_count

        else:
            charge = None
            commission = None
            contribution = None

        return {
            f"{month_count} {'months' if month_count > 1 else 'months'}": {
                "duration_in_days": duration_in_days,
                "duration_in_months": month_count,
                "target": target,
                "contribution": contribution,
                "commission": commission,
                "charge": round(charge * 100, 5),
            }
        }


################default rotation group_id for RotationGroup model#########################
def generate_rotation_group_id() -> str:
    """
    Generate a unique group id
    """

    characters = string.ascii_uppercase + string.digits
    id = "LP" + "".join(secrets.choice(characters) for i in range(10))

    if RotationGroup.objects.filter(group_id=id).exists():
        return generate_rotation_group_id()
    else:
        return id


def generate_rotation_group_quotation_id() -> str:
    return f"LI-ROT-{uuid.uuid4()}"


class RotationGroup(BaseModel):
    # FIELDS
    # user that owns the rotation group

    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        related_name="rotation_groups",
        on_delete=models.CASCADE,
        db_index=True,
        help_text="this is the user that created the rotation group",
    )

    ajo_members = models.ManyToManyField(
        "ajo.RotationGroupMember",
        blank=True,
        related_name="rotation_group",
    )

    # Group information
    group_id = models.CharField(
        max_length=100,
        unique=True,
        editable=False,
        default=generate_rotation_group_id,
        db_index=True,
        help_text="this is the unique ID generated for each rotation group upon creation",
    )

    name = models.CharField(max_length=100)
    amount = RoundedFloatField(
        default=0.00,
        help_text="this is the amount each member of the rotation group is to pay, excluding fees",
    )
    number_of_participants = models.IntegerField(default=0)
    frequency = models.CharField(max_length=100, choices=SavingsFrequency.choices)
    duration = models.IntegerField(
        null=True,
        blank=True,
        help_text="this is how long the plan is intended to be active, it's base unit is 'days'",
    )
    starting_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    admin_fee = RoundedFloatField(
        default=0.00,
        help_text="this is intended for Personal Users to determine how much of the fees will be for the creator of the group",
    )
    agent_commission = RoundedFloatField(
        default=0.00,
        help_text="this is intended for Agent ROSCA to determine how much commission the agent is expected to make",
    )
    auto_debit = models.BooleanField(default=False)
    fees = RoundedFloatField(
        default=0.00,
        help_text="this is the fees that will be shared amongst group members",
    )

    # calculated information
    collection_amount = RoundedFloatField(
        null=True,
        blank=True,
        help_text="this is the amount each user will collect on their collection day",
    )
    contribution_amount = RoundedFloatField(
        null=True,
        blank=True,
        help_text="this is the amount + share of 'fees' that each member will pay",
    )
    # colllection type either by additional commission or by due collection deduction
    collection_type = models.CharField(
        max_length=100,
        choices=RoscaCollectionType.choices,
        default="ONE_OFF_DEDUCTION",
    )
    rosca_type = models.CharField(max_length=100, choices=RoscaType.choices)

    quotation_id = models.CharField(
        max_length=100,
        default=generate_rotation_group_quotation_id,
    )

    # saving fields
    total_amount_saved = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="total amount saved should be >= 0"
            ),
        ],
    )
    balance_before = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="group balance before should be >= 0"
            ),
        ],
    )
    status = models.CharField(
        max_length=100, choices=RoscaGroupStatus.choices, default="PENDING"
    )
    balance_after = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="plan balance before should be >= 0"
            ),
        ],
    )
    # meta fields
    is_active = models.BooleanField(default=False)
    is_activated = models.BooleanField(default=False)

    # metadata
    email_sent = models.BooleanField(default=False)
    # starting date email
    starting_date_email_sent = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    # for adashe use
    no_of_collectors = models.IntegerField(default=1)

    def __str__(self) -> str:
        return (
            f"group name: {self.name}, for: {self.user} with group id: {self.group_id}"
        )

    def save(self, *args, **kwargs):
        if self.rosca_type == RoscaType.AGENT_ROSCA:
            self.commissions()
            self.calculate_collection_amount()

        if not self.end_date:
            self.calculate_end_date()

        if self.no_of_collectors > 1:
            self.commissions()

        super().save(*args, **kwargs)

    @property
    def has_started(self) -> bool:
        now = timezone.localtime()
        return self.starting_date <= now.date()

    @property
    def commision(self) -> bool:
        constant = ConstantTable.get_constant_table_instance()
        fee = constant.rosca_commision
        return fee

    @property
    def group_wallet_balance(self):
        from ajo.selectors import get_ajo_rosca_group_wallet

        wallet_balance = get_ajo_rosca_group_wallet(user=self.user, group_id=self.id)
        return wallet_balance.available_balance

    # @property
    def progress_amount(self):
        overall_amount = self.amount * self.number_of_participants
        if self.no_of_collectors > 1:
            return overall_amount / self.no_of_collectors

        return overall_amount

    # Calculate collection amount
    def calculate_collection_amount(self):
        if self.no_of_collectors > 1:
            if self.collection_type == "ONE_OFF_DEDUCTION":
                fees = self.admin_fee / self.number_of_participants
                self.collection_amount = round(
                    self.amount / self.no_of_collectors - fees, 2
                )
                self.contribution_amount = round(
                    self.amount / self.number_of_participants, 2
                )

            if self.collection_type == "RECURING_DEDUCTION":
                self.collection_amount = self.amount / self.no_of_collectors
                amount = self.admin_fee / self.number_of_participants / self.duration
                self.contribution_amount = round(
                    self.amount / self.number_of_participants + amount, 2
                )

            self.duration = round(self.collection_amount / self.contribution_amount)

        if self.no_of_collectors <= 1:
            total_collection = self.number_of_participants * self.amount
            collection_amount = total_collection - self.admin_fee
            self.collection_amount = collection_amount

            if self.collection_type == "ONE_OFF_DEDUCTION":
                fee = self.admin_fee / self.duration
                collection_amount = self.amount - fee
                self.collection_amount = collection_amount
                contribution_amount = self.amount / self.number_of_participants
                self.contribution_amount = round(contribution_amount, 2)

            if self.collection_type == "RECURING_DEDUCTION":
                self.collection_amount = self.amount
                amount = self.admin_fee / self.number_of_participants / self.duration
                self.contribution_amount = round(
                    self.amount / self.number_of_participants + amount, 2
                )

    def calculate_end_date(self):
        # if self.rosca_type == "AGENT_ROSCA":
        duration = self.duration

        if self.frequency == "WEEKLY":
            self.end_date = self.starting_date + datetime.timedelta(weeks=duration)

        elif self.frequency == "DAILY":
            self.end_date = self.starting_date + datetime.timedelta(days=duration)

        elif self.frequency == "MONTHLY":
            year = self.starting_date.year
            month = self.starting_date.month + duration

            while month > 12:
                year += 1
                month -= 12
            self.end_date = datetime.date(year, month, self.starting_date.day)
        else:
            pass

    def commissions(self):
        from accounts.models import ConstantTable

        if self.rosca_type == "AGENT_ROSCA":
            original_amount = self.amount
            constant = ConstantTable.get_constant_table_instance()
            percentage = constant.rosca_commision
            result = (percentage / 100) * original_amount
            admin_fee = self.admin_fee = result
            self.agent_commission = 0.60 * admin_fee
            self.fees = admin_fee / self.number_of_participants / self.duration

        else:
            pass

    @classmethod
    def create_group(
        cls,
        user,
        name,
        amount: float,
        number_of_participants: int,
        frequency: str,
        duration: int,
        collection_type: str,
        starting_date: str,
        no_of_collectors: int,
        rosca_type=None,
    ):
        group_instance = cls.objects.create(
            user=user,
            name=name,
            amount=amount,
            no_of_collectors=no_of_collectors,
            number_of_participants=number_of_participants,
            frequency=frequency,
            duration=duration,
            collection_type=collection_type,
            starting_date=starting_date,
            rosca_type=rosca_type,
        )
        return group_instance


def calculate_group_details_end_date(frequency, duration, starting_date):
    duration = duration

    if frequency == "WEEKLY":
        end_date = starting_date + datetime.timedelta(weeks=duration)

    elif frequency == "DAILY":
        end_date = starting_date + datetime.timedelta(days=duration)

    elif frequency == "MONTHLY":
        year = starting_date.year
        month = starting_date.month + duration

        while month > 12:
            year += 1
            month -= 12
        end_date = datetime.date(year, month, starting_date.day)

    return end_date


def generate_ajosepo_group_id() -> str:
    return f"LI-ASP-{uuid.uuid4()}"


class AjoSepo(BaseModel):
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="ajosepo",
        db_index=True,
    )
    name = models.CharField(max_length=100)
    group_id = models.CharField(
        max_length=100,
        default=generate_ajosepo_group_id,
    )
    participants = models.IntegerField(
        default=6,
        help_text="the number of participants that will be in the group",
    )
    leader = models.ForeignKey(
        to=AjoUser,
        on_delete=models.CASCADE,
        related_name="ajosepo_leader",
        null=True,
        blank=True,
        db_index=True,
    )
    meeting_days = models.CharField(max_length=100)
    loan_tenure = models.CharField(
        max_length=100, choices=Tenure.choices, default=Tenure.THREE_MONTH
    )
    repayment_type = models.CharField(
        max_length=100,
        choices=RepaymentFrequency.choices,
        default=RepaymentFrequency.DAILY,
    )
    status = models.CharField(
        max_length=100, choices=GroupStatus.choices, default=GroupStatus.PENDING
    )
    min_members = models.IntegerField(
        default=6,
        help_text="minimum number of savings before the group can be activated",
    )
    max_members = models.IntegerField(
        default=12,
        help_text="maximum number of savings/loans that can be associated with this group",
    )
    is_active = models.BooleanField(
        default=False,
        help_text="this indicates that this group is active and in use currently",
    )
    is_activated = models.BooleanField(
        default=False,
        help_text="this indicates this group has reached it's minimum required and can begin",
    )
    date_closed = models.DateField(null=True, blank=True)

    def __str__(self):
        return f"{self.name} - {self.group_id}"

    class Meta:
        verbose_name = "AjoSepo"
        verbose_name_plural = "AjoSepo"


##############default quotation_id function for AjoSaving Model##########################
def generate_quotation_id() -> str:
    """
    Generate a unique quotation id
    """
    id = f"LI-AJO-{uuid.uuid4()}"

    if AjoSaving.objects.filter(quotation_id=id).exists():
        return generate_quotation_id
    else:
        return id


class AjoSaving(BaseModel):
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="ajo_savings",
        db_index=True,
    )

    # ajo user
    ajo_user = models.ForeignKey(
        to=AjoUser,
        on_delete=models.CASCADE,
        related_name="ajo_savings",
        null=True,
        blank=True,
        db_index=True,
    )
    # plan type
    plan_type = models.ForeignKey(
        to=AjoSavingsPlanType,
        on_delete=models.CASCADE,
        related_name="ajo_savings",
        null=True,
        blank=True,
        db_index=True,
    )

    plan_data = models.TextField(null=True, blank=True)

    # information about plan
    name = models.CharField(max_length=100, null=True, blank=True, db_index=True)
    frequency = models.CharField(
        max_length=100, null=True, blank=True, choices=SavingsFrequency.choices
    )
    periodic_amount = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="periodic amount should be >= 0"
            ),
        ],
    )

    duration = models.IntegerField(default=30, null=True, blank=True)
    quotation_id = models.CharField(
        max_length=100, unique=True, default=generate_quotation_id
    )
    amount_saved = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="periodic amount should be >= 0"
            ),
        ],
    )
    lock = models.BooleanField(default=False)

    # calculated fields
    frequency_paid = models.IntegerField(
        default=0,
        validators=[
            MinValueValidator(
                limit_value=0,
                message="frequency paid should be positive integers only",
            )
        ],
        help_text="this indicates the number of times the periodic amount has been paid",
    )
    commission_amount = RoundedFloatField(default=0.00)
    expected_amount = RoundedFloatField(default=0.00, null=True, blank=True)
    maturity_date = models.DateField(
        verbose_name=_("date of maturity"), null=True, blank=True, db_index=True
    )

    # plan metadata
    is_activated = models.BooleanField(default=False, null=True, blank=True)
    completed = models.BooleanField(default=False, null=True, blank=True)
    completed_at = models.DateTimeField(
        verbose_name=_("date completed"), null=True, blank=True, db_index=True
    )
    plan_balance_before = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="plan balance before should be >= 0"
            ),
        ],
    )
    plan_balance_after = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="plan balance before should be >= 0"
            ),
        ],
    )
    is_active = models.BooleanField(default=True, null=True, blank=True, db_index=True)
    withdrawn = models.BooleanField(default=False)

    # commission_paid
    commission_paid = models.BooleanField(default=False)

    # loan savings
    loan = models.BooleanField(default=False)

    # buy now pay later, save now pay later
    savings_type = models.CharField(
        max_length=100, choices=SavingsType.choices, null=True, blank=True
    )
    # BNPL/SNPL item info
    product_info = models.ForeignKey(
        to="ProductInformation",
        on_delete=models.CASCADE,
        related_name="product_info",
        null=True,
        blank=True,
        db_index=True,
    )
    stock_marked = models.BooleanField(
        default=False,
        help_text="Indicates if this sale has been registered and stock accounted for on Paybox",
    )
    invoice_id = models.CharField(max_length=100, null=True, blank=True)

    # group loans
    group = models.ForeignKey(
        AjoSepo,
        null=True,
        blank=True,
        related_name="savings",
        on_delete=models.SET_NULL,
    )
    must_belong_to_group = models.BooleanField(default=False)

    # email
    email_sent = models.BooleanField(default=False)
    is_loan_repayment = models.BooleanField(default=False)
    loan_id = models.CharField(max_length=100, blank=True, null=True)
    loan_addable_amount = RoundedFloatField(
        default=0.00,
    )
    ineligible_for_loan = models.BooleanField(default=False)

    # loan disk id's
    savings_id_on_loan_disk = models.CharField(max_length=100, null=True, blank=True)
    savings_id_on_loan_disk_branch = models.CharField(
        max_length=100, null=True, blank=True
    )
    escrow_meta_response = models.TextField(null=True, blank=True)
    escrow_settlement_status = models.BooleanField(default=False)
    boosta_tenure = models.IntegerField(
        null=True, blank=True, help_text="Boosta Tenure in days"
    )
    business_suite = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.frequency}-{self.id} - {self.quotation_id}"

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["user", "ajo_user", "name"],
                name="unique_user_ajo_user_savings_plan",
                violation_error_message="A plan with this name exists already",
            )
        ]

    # def __str__(self) -> str:
    #     return f"plan: {self.name} quotation_id: {self.quotation_id}"

    def calculate_expected_amount(self) -> float:
        """
        This calculates how much should be accumulated at the end
        of an ajo savings plan

        Raises:
            ValueError: Invalid frequency

        Returns:
            float: the expected amount
        """
        frequency = self.frequency
        duration = self.duration
        periodic_amount = self.periodic_amount

        if frequency == SavingsFrequency.DAILY:
            expected_amount = periodic_amount * duration
        elif frequency == SavingsFrequency.WEEKLY:
            weeks = duration // 7  # 7 days in a week
            expected_amount = periodic_amount * weeks
        elif frequency == SavingsFrequency.MONTHLY:
            months = duration // 30  # Assuming a month is 30 days
            expected_amount = periodic_amount * months
        else:
            raise ValueError("Invalid frequency")

        return expected_amount

    def save(self, *args, **kwargs):
        if self.plan_type:
            self.commission_amount = self.periodic_amount

        if not self.expected_amount or self.expected_amount <= 0:
            self.expected_amount = self.calculate_expected_amount()
        super().save(*args, **kwargs)

    def get_maximum_frequency(self) -> int:
        """
        Get the maximum frequency paid for a savings plan

        Returns:
            int: maximum frequency
        """
        frequency = self.frequency
        duration = self.duration

        if frequency == SavingsFrequency.DAILY:
            return 30

        elif frequency == SavingsFrequency.WEEKLY:
            return duration // 7

        elif frequency == SavingsFrequency.MONTHLY:
            return duration // 30

    def calculate_frequency_paid(self, save: bool = False) -> int:
        """
        Calculate the total days/weeks/months paid for an Ajo plan and decide whether to save it or not

        Args:
            save (bool, optional): True/False. Defaults to False.

        Returns:
            int: total days/weeks/months paid
        """
        periodic_amount = self.periodic_amount
        amount_paid = self.amount_saved

        if amount_paid < periodic_amount:
            return 0

        # check if the plan is completed
        # return the already saved days paid
        if self.completed:
            return self.frequency_paid

        # calculate days paid
        max_frequency = self.get_maximum_frequency()
        whole_frequency_paid = min(amount_paid // periodic_amount, max_frequency)
        self.frequency_paid = whole_frequency_paid

        # check if the amount_paid is greater than or equal to expected amount
        if amount_paid >= self.expected_amount:
            self.completed = True
            self.completed_at = timezone.localtime()

        # if the instruction is to save
        if save:
            self.save()

        return whole_frequency_paid

    def close_plan(self):
        """
        close an ajo savings plan and update is_active,
        withdrawn, completed and completed_at fields
        """
        self.is_active = False
        if self.amount_saved <= 0:
            self.withdrawn = True

        if self.amount_saved >= self.expected_amount:
            self.completed = True

            if not self.completed_at:
                self.completed_at = timezone.localtime()

        # save the changes
        self.save()

    @property
    def tenure_str(self):
        # Create a reverse dictionary that maps the loan tenure {"3_MONTH":"90"}
        # to {"90":"3_MONTH"}
        REVERSE_TENURE_DICT = {value: key for key, value in Tenure.choices}

        # Retrieve the key corresponding to the string value of self.boosta_tenure.
        # If self.boosta_tenure doesn't exist in the reverse dictionary, return None.
        return REVERSE_TENURE_DICT.get(str(self.boosta_tenure), None)


class AjoTradeCategory(BaseModel):
    name = models.CharField(max_length=250)

    def __str__(self) -> str:
        return self.name


class AjoTradeSubCategory(BaseModel):
    category = models.ForeignKey(AjoTradeCategory, on_delete=models.CASCADE)
    name = models.CharField(max_length=350)

    def __str__(self) -> str:
        return self.name


class RotationGroupMember(BaseModel):
    # group member information
    group = models.ForeignKey(
        to=RotationGroup,
        on_delete=models.CASCADE,
        related_name="members",
        db_index=True,
    )
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, on_delete=models.CASCADE, db_index=True
    )
    position = models.PositiveIntegerField(db_index=True)
    ajo_user_member = models.ForeignKey(
        AjoUser,
        on_delete=models.CASCADE,
        related_name="group_member",
        null=True,
        blank=True,
    )
    # Savings fields
    total_amount_contributed = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="total amount contributed should be >= 0"
            ),
        ],
    )
    total_amount_collected = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="total amount collected should be >= 0"
            ),
        ],
    )
    balance_before = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="plan balance before should be >= 0"
            ),
        ],
    )
    balance_after = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="plan balance before should be >= 0"
            ),
        ],
    )
    collected = models.BooleanField(default=False)
    withdrawn = models.BooleanField(default=False)
    collection_date = models.DateField(null=True, blank=True)
    frequency_paid = models.IntegerField(default=0)
    paid_positions = models.TextField(blank=True, null=True)
    # metadata
    email_sent = models.BooleanField(default=False)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["group", "position"],
                name="unique_group_position",
                violation_error_message="this position is already taken in this group",
            ),
            # models.CheckConstraint(
            #     check=models.Q(position__lte=models.F("group__number_of_participants")),
            #     name="position_must_be_within_number_of_participants",
            #     violation_error_message="this position is not within the number of participants",
            # ),
        ]

    def __str__(self) -> str:
        return f"{self.user.email}, is a member of {self.group.name} group"

    # NOTE: Moved all class methods to RotationGroupMemberService in ajo/services.py
    # This method is used to calculate how much a person has saved on the fly
    def savings_percentage(self):
        today = timezone.localtime().date()
        duration = self.group.duration
        frequency = self.group.frequency
        periodic_amount = self.group.amount

        if frequency == SavingsFrequency.DAILY:
            # days_passed = (today - self.group.starting_date).days
            days_paid = self.total_amount_contributed // periodic_amount
            return f"{days_paid}/{duration}"
        elif frequency == SavingsFrequency.WEEKLY:
            weeks_passed = ((today - self.group.starting_date).days // 7) + 1
            duration_in_weeks = duration // 7
            weeks_paid = self.total_amount_contributed // periodic_amount
            return f"{weeks_paid}/{duration_in_weeks}"
        elif frequency == SavingsFrequency.MONTHLY:
            months_passed = (today.year - self.group.starting_date.year) * 12 + (
                today.month - self.group.starting_date.month
            )
            duration_in_months = duration // 30
            months_paid = self.total_amount_contributed / periodic_amount
            return f"{months_paid}/{duration_in_months}"

    @classmethod
    def position_in_group(cls, group: RotationGroup, position: int):
        try:
            return RotationGroupMember.objects.get(group=group, position=position)
        except RotationGroupMember.DoesNotExist:
            return None

    @property
    def full_name(self):
        first_name = self.ajo_user_member.first_name
        last_name = self.ajo_user_member.last_name

        return f"{first_name} {last_name}"

    @property
    def phone_number(self):
        phone_number = self.ajo_user_member.phone_number

        return phone_number


class CardRequests(BaseModel):
    # agent user that made the request
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="card_requests",
        db_index=True,
    )

    # information about the request
    amount = models.FloatField(default=0)
    quantity = models.IntegerField(default=1)
    unique_reference = models.CharField(max_length=100)
    reference = models.CharField(max_length=200, db_index=True)
    state = models.CharField(max_length=100, null=True, blank=True)
    lga = models.CharField(max_length=100, blank=True, null=True)
    nearest_landmark = models.CharField(max_length=255, null=True, blank=True)
    street = models.CharField(max_length=255, null=True, blank=True)
    address_type = models.CharField(max_length=100, blank=True, null=True)
    card_id = models.CharField(max_length=100, blank=True, null=True)
    ajo_user = models.ForeignKey(
        AjoUser,
        on_delete=models.SET_NULL,
        related_name="ajo_user_card_map",
        db_index=True,
        null=True,
        blank=True,
    )

    # metadata
    is_frozen = models.BooleanField(default=False)
    is_assigned = models.BooleanField(default=False)
    email_sent = models.BooleanField(default=False)
    date_assigned = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "Card Request"
        verbose_name_plural = "Card Requests"

    def __str__(self) -> str:
        return f"{self.user} requested for {self.quantity} cards"

    @classmethod
    def create_card_request(cls, card_request_data: dict):
        try:
            card_request = cls.objects.create(**card_request_data)
        except:
            raise ValueError(f"there was an error in creating this request")

        return card_request

    @classmethod
    def get_unique_card_request_delivery_location_combinations(cls, user):
        """
        Args:
            agent (User): the CustomUser object
        """
        unique_combinations = (
            cls.objects.filter(user=user)
            .values("state", "lga", "nearest_landmark", "street", "address_type")
            .annotate(count=Count("*"))
            .filter(count__gte=1)
        )

        return unique_combinations


##############default quotation_id function for AjoSaving Model##########################
def generate_loan_quotation_id() -> str:
    """
    Generate a unique quotation id
    """
    id = f"LI-LOAN-{uuid.uuid4()}"

    if Loan.objects.filter(quotation_id=id).exists():
        return generate_quotation_id
    else:
        return id


class Loan(BaseModel):
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="ajo_users_loans",
        db_index=True,
    )

    ajo_user = models.ForeignKey(
        to=AjoUser,
        on_delete=models.CASCADE,
        related_name="ajo_loans",
        null=True,
        blank=True,
        db_index=True,
    )

    loaned_amount = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="loan amount should be >= 0")
        ],
    )

    interest = RoundedFloatField(
        default=2.5,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="loan interest rate should be >= 0"
            ),
        ],
    )

    repayment_amount = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="loan repayment amount should be >= 0"
            )
        ],
    )

    frequency = models.CharField(
        max_length=100,
        choices=SavingsFrequency.choices,
        default=SavingsFrequency.DAILY,
    )

    quotation_id = models.CharField(
        max_length=100, unique=True, default=generate_loan_quotation_id
    )

    periodic_amount = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="periodic amount should be >= 0"
            ),
        ],
    )

    amount_paid = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="amount paid rate should be >= 0"
            ),
        ],
    )

    # plan metadata
    is_activated = models.BooleanField(default=False, null=True, blank=True)
    completed = models.BooleanField(default=False, null=True, blank=True)
    completed_at = models.DateTimeField(
        verbose_name=_("date completed"), null=True, blank=True, db_index=True
    )
    plan_balance_before = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="plan balance before should be >= 0"
            ),
        ],
    )
    plan_balance_after = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="plan balance before should be >= 0"
            ),
        ],
    )
    is_active = models.BooleanField(default=True, null=True, blank=True)

    # calculated fields
    days_paid = models.IntegerField(
        default=0,
        validators=[
            MinValueValidator(
                limit_value=0, message="days paid should be positive integers only"
            )
        ],
    )

    def save(self, *args, **kwargs):
        self.days_paid = self.calculate_days_paid()
        super().save(*args, **kwargs)

    def calculate_days_paid(self, save: bool = False) -> int:
        """
        Calculates the total days paid in completion of loan payback

        Returns:
            int: total days paid
        """
        periodic_amount = self.periodic_amount
        amount_paid = self.amount_paid

        if amount_paid < periodic_amount:
            return 0

        # calculate days paid
        whole_days_paid = amount_paid // periodic_amount
        self.days_paid = whole_days_paid

        # check if the amount_paid is greater or equal to repayment amount
        if amount_paid >= self.repayment_amount:
            self.completed = True
            self.is_active = False
            self.completed_at = timezone.localtime()

        # if the instruction is to save
        if save:
            self.save()

        return whole_days_paid


class BankAccountDetails(BaseModel):
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="agent_vfd_account",
        db_index=True,
    )
    ajo_user = models.ForeignKey(
        to=AjoUser,
        on_delete=models.CASCADE,
        related_name="ajo_user_vfd_accounts",
        db_index=True,
        null=True,
        blank=True,
    )
    user_wallet = models.ForeignKey(
        to="payment.WalletSystem",
        on_delete=models.CASCADE,
        related_name="ajo_user_wallet",
        null=True,
        blank=True,
    )
    account_number = models.CharField(max_length=100, db_index=True)  # Regex validators
    unique_account_number = models.CharField(
        max_length=100, unique=True, null=True, blank=True
    )
    account_provider = models.CharField(
        max_length=100, choices=AccountProvider.choices, null=True, blank=True
    )
    account_name = models.CharField(max_length=100)
    account_type = models.CharField(max_length=100)
    bank_name = models.CharField(max_length=100)
    bank_code = models.CharField(max_length=100, db_index=True)  # RegexValidator
    consent = models.BooleanField(default=False)
    bvn = models.CharField(max_length=100, null=True, blank=True)
    id_type = models.CharField(max_length=100, null=True, blank=True)
    # meta information
    form_type = models.CharField(
        max_length=200, choices=AccountFormType.choices, default=AccountFormType.AGENT
    )
    initial_payload = models.TextField(null=True, blank=True)
    payload = models.TextField()
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = "Bank Account Details"
        verbose_name_plural = "Bank Account Details"

    def __str__(self) -> str:
        return f"{self.account_number}-{self.bank_name}-{self.account_name}"

    def save(self, *args, **kwargs) -> None:

        try:
            self.unique_account_number = self.account_number
        except:
            self.account_number = f"00{self.id}-{self.account_number}"

        super().save(*args, **kwargs)


class RawFundingData(BaseModel):
    reference = models.CharField(max_length=100)
    recipient_account_number = models.CharField(max_length=255, null=True, blank=True)
    payload = models.TextField()
    source = models.CharField(max_length=100, null=True, blank=True)
    verification_payload = models.TextField(null=True, blank=True)
    authorized = models.BooleanField(default=False)
    funding_response = models.TextField(null=True, blank=True)
    result = models.TextField(null=True, blank=True)

    class Meta:
        verbose_name = "Raw Funding Data"
        verbose_name_plural = "Raw Funding Data"


class Prefunding(BaseModel):
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="prefunding",
        db_index=True,
    )

    prefunding_amount = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="prefunding amount should be >= 0"
            )
        ],
    )

    amount_paid = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="amount paid rate should be >= 0"
            ),
        ],
    )
    balance_left = RoundedFloatField(
        default=0.00,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="plan balance before should be >= 0"
            ),
        ],
    )

    # prefunding metadata
    completed = models.BooleanField(default=False, null=True, blank=True, db_index=True)
    completed_at = models.DateTimeField(
        verbose_name=_("date completed"), null=True, blank=True, db_index=True
    )

    class Meta:
        verbose_name = "Prefunding"
        verbose_name_plural = "Prefunding"


class AccountCreationsCheck(models.Model):
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True
    )
    initial_payload = models.TextField(null=True, blank=True)
    dump = models.TextField()
    date_created = models.DateTimeField(auto_now=True)


class AjoUserWithdrawalAccount(BaseModel):
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="ajo_users_withdrawal_accounts",
    )
    ajo_user = models.OneToOneField(
        to=AjoUser, on_delete=models.CASCADE, related_name="withdrawal_account"
    )
    account_number = models.CharField(max_length=100)
    account_name = models.CharField(max_length=100)
    bank_name = models.CharField(max_length=100)
    bank_code = models.CharField(max_length=100)

    class Meta:
        verbose_name = "Ajo User Withdrawal Account"
        verbose_name_plural = "Ajo User Withdrawal Accounts"


class RoscaPaymentBreakDown(models.Model):
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, on_delete=models.CASCADE, db_index=True
    )
    group = models.ForeignKey(
        RotationGroup, on_delete=models.CASCADE, null=True, blank=True
    )
    ajo_user = models.ForeignKey(
        AjoUser, on_delete=models.CASCADE, null=True, blank=True
    )
    frequency = models.CharField(max_length=100, choices=SavingsFrequency.choices)
    due_amount = models.FloatField(default=0.0, validators=[MinValueValidator(0.0)])
    paid_amount = models.FloatField(default=0.0, validators=[MinValueValidator(0.0)])
    paid = models.BooleanField(default=False)
    day_count = models.IntegerField(default=0)
    date_created = models.DateTimeField(auto_now_add=True)
    rosca_due_date = models.DateField(null=True, blank=True)
    date_paid = models.DateTimeField(null=True, blank=True)

    @classmethod
    def create_rosca_payment_break_down(
        cls, user, ajo_user, group, start_date, end_date
    ):
        break_down_object_list = []
        current_date = start_date
        count, day_count = 1, 1

        if group.frequency == "WEEKLY":
            delta = timedelta(weeks=1)
        elif group.frequency == "DAILY":
            delta = timedelta(days=1)
        elif group.frequency == "MONTHLY":
            delta = timedelta(days=30)

        next_rosca_due_date = current_date

        while current_date <= end_date:
            break_down_object_list.append(
                cls(
                    user=user,
                    ajo_user=ajo_user,
                    group=group,
                    day_count=count,
                    rosca_due_date=next_rosca_due_date,
                    due_amount=group.contribution_amount,
                    frequency=group.frequency,
                )
            )
            count += 1
            day_count += 1
            next_rosca_due_date += delta  # Increment the due date
            current_date = next_rosca_due_date + timedelta(days=1)

        if group.frequency == "MONTHLY":
            break_down_object_list = break_down_object_list[:-1]

        cls.objects.bulk_create(
            break_down_object_list, batch_size=100, ignore_conflicts=True
        )

    @classmethod
    def update_collection_dates(cls, group, member_instance):
        if group.no_of_collectors <= 1:
            member_object = cls.objects.filter(
                group=group, day_count=member_instance.position
            ).last()
            member_instance.collection_date = member_object.rosca_due_date
            member_instance.save()

        # Calculate the number of collectors per collection date dynamically
        no_participants = group.number_of_participants
        collectors = group.no_of_collectors
        position = member_instance.position
        start_date = group.starting_date
        frequency = group.frequency

        x = 1
        range_obj = []
        for i in range(1, no_participants, collectors):
            get_range = round(no_participants / i, 3)
            max_num = i + collectors - 1
            range_obj.append({"min": i, "max": max_num, "range": get_range, "num": x})
            x += 1

        for p in range_obj:
            min_num = p["min"]
            max_num = p["max"]
            collection_num = p["num"] - 1
            if position >= min_num and position <= max_num:
                if collection_num == 0:
                    member_instance.collection_date = start_date
                    member_instance.save()
                else:
                    if frequency == "DAILY":
                        collection_date = start_date + timedelta(days=collection_num)
                    elif frequency == "WEEKLY":
                        collection_date = start_date + timedelta(weeks=collection_num)
                    elif frequency == "MONTHLY":
                        # collection_date = start_date + timedelta(months=collection_num)
                        days_in_month = 30
                        total_days = days_in_month * collection_num

                        # Create a timedelta with the calculated number of days
                        delta = timedelta(days=total_days)

                        # Add the timedelta to the start_date
                        collection_date = start_date + delta

                    member_instance.collection_date = collection_date
                    member_instance.save()

        return "Collection date updated"

    @classmethod
    def contribution_check(cls, ids: list):
        paid_contributions = []
        not_found_contributions = []

        for id in ids:
            try:
                contribution = cls.objects.get(id=id)
                if contribution.paid:
                    paid_contributions.append(id)
            except cls.DoesNotExist:
                not_found_contributions.append(id)

        if not paid_contributions and not not_found_contributions:
            return {
                "status": True,
                "paid": paid_contributions,
                "not_found": not_found_contributions,
            }
        else:
            return {
                "status": False,
                "paid": paid_contributions,
                "not_found": not_found_contributions,
            }

    @property
    def phone_number(self):
        phone_number = self.ajo_user.phone_number
        return phone_number


class Branch(BaseModel):
    name = models.CharField(max_length=200, unique=True)
    supervisor = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="branch_supervisor",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
    )
    users = models.ManyToManyField(
        settings.AUTH_USER_MODEL, blank=True, related_name="branch_users"
    )
    performance_status = models.CharField(
        max_length=100, choices=AjoAgentStatus.choices, blank=True, null=True
    )
    loan_disk_id = models.CharField(max_length=200, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.name}"

    class Meta:
        verbose_name = "BRANCH"
        verbose_name_plural = "BRANCHES"


class AgencyDumps(BaseModel):

    source = models.CharField(max_length=30)
    status = models.CharField(max_length=30)
    response = models.TextField()
    payload = models.TextField()


class ProfileChangeRequest(BaseModel):
    session_id = models.CharField(max_length=100, null=True, blank=True)
    ajo_user = models.ForeignKey(
        to=AjoUser,
        on_delete=models.CASCADE,
        related_name="change_requests",
    )
    status = models.CharField(
        max_length=100,
        choices=ProfileChangeStatus.choices,
        default=ProfileChangeStatus.PROCESSING,
        null=True,
        blank=True,
    )
    is_active = models.BooleanField(default=True)
    payload = models.TextField()
    failure_reason = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )

    class Meta:
        verbose_name = "Profile Change Requests"
        verbose_name_plural = "Profile Change Requests"


class Supervisor(BaseModel):
    supervisor = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="supervisors",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
    )
    branch = models.ForeignKey(
        Branch,
        related_name="supervisor_branch",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
    )
    collectors = models.ManyToManyField(
        settings.AUTH_USER_MODEL, blank=True, related_name="supervisor_collectors"
    )
    performance_status = models.CharField(
        max_length=100, choices=AjoAgentStatus.choices, blank=True, null=True
    )
    date_created = models.DateTimeField(auto_now_add=True)

    # def __str__(self):
    #     return f"{self.supervisor.first_name} {self.supervisor.last_name}"

    class Meta:
        verbose_name = "SUPERVISOR"
        verbose_name_plural = "SUPERVISORS"


class PerformAgentSwitch(BaseModel):
    ajo_user = models.ForeignKey(AjoUser, on_delete=models.CASCADE)
    old_agent = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="switcable_old_agent",
        on_delete=models.CASCADE,
    )
    new_agent = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)

    def __str__(self) -> str:
        return f"Ajo User - {self.ajo_user.id}|{self.ajo_user.phone_number}: Switchings - {self.old_agent} -> {self.new_agent}"


class ProductInformation(BaseModel):
    category = models.CharField(max_length=200, null=True, blank=True)
    category_name = models.CharField(max_length=200, null=True, blank=True, choices=CategoryNames.choices)
    item = models.CharField(max_length=200, null=True, blank=True)
    item_id = models.CharField(max_length=100)
    branch_id = models.CharField(max_length=100)
    company_id = models.CharField(max_length=100)
    stock_price = RoundedFloatField(null=True, blank=True)
    selling_price = RoundedFloatField()
    # image = models.CharField(max_length=250, null=True, blank=True)

    def __str__(self):
        return f"{self.id}-{self.item}"

    class Meta:
        verbose_name = "Product Information"
        verbose_name_plural = "Product Information"


class BusinessSuite(BaseModel):
    savings = models.OneToOneField(
        AjoSaving, on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=300, null=True, blank=True)
    amount = models.FloatField(null=True, blank=True)

    def __str__(self):
        return f"{self.id}"

    class Meta:
        verbose_name = "Business Suite"
        verbose_name_plural = "Business Suite"
