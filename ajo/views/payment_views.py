import json
import math
from typing import Any, Dict, Optional, Union

from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.core.cache import cache
from django.core.exceptions import ObjectDoesNotExist
from django.db import IntegrityError
from django.utils import timezone
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics, permissions, serializers, status
from rest_framework.exceptions import NotFound
from rest_framework.response import Response
from rest_framework.views import APIView

from accounts.agency_banking import AgencyBankingClass
from accounts.model_choices import UserType
from accounts.models import ConstantTable, FailuresLog
from accounts.paystack import PaystackApi
from accounts.responses import (
    error_response,
    pagination_page_not_found_response,
    serializer_validation_error_response,
    value_error_response,
)
from chestlock.selectors import Onlending, OnlendingSelector
from loans.helpers.core_banking import CoreBankingManager
from loans.helpers.repayments import does_ajo_user_have_repayment_outstanding
from loans.models import LoanEligibility
from loans.tasks import create_bnpl_eligibility, run_post_savings_to_loan_disk
from payment.checks import obtain_phone_number, verify_account, verify_transaction_pin
from payment.model_choices import PlanType, WalletTypes
from payment.models import WalletSystem, WithdrawalAccount
from payment.payment_actions import pay_withdrawal_and_handle_response
from payment.services import TransactionService
from payment.tasks import settle_transfer_transaction
from payment.utils import is_past_month_year
from savings.pagination import CustomPagination

from ..model_choices import AccountProvider, CategoryNames, SavingsFrequency, SavingsType
from ..models import (
    AjoSaving,
    AjoUser,
    AjoUserWithdrawalAccount,
    BankAccountDetails,
    BusinessSuite,
    RotationGroup,
)
from ..payment_actions import (
    PlanPayments,
    cashout_from_ajo_plan,
    cashout_from_spending_wallet,
    check_if_agent_can_pay,
    check_if_amount_higher_than_remaining_loan,
    collect_loan_plan_commissions,
    debit_ajo_plan,
    debit_ajo_user_wallet_for_dojah_charge,
    debit_wallet_and_fill_transaction,
    debit_wallet_for_withdrawal,
    fund_agent_from_commissions,
    pay_commissions,
    pay_for_ajo_plan,
    pay_for_loan,
    reverse_personal_ajo_savings_debit,
    reverse_withdrawal,
    withdraw_from_ajo_agent_wallet_to_agency_wallet,
)
from ..prefunding.actions import (
    PrefundingActions,
    check_if_prefunding_wallet_can_be_used,
)
from ..prefunding.eligibility import prefunding_eligibility
from ..selectors import (
    AjoAgentSelector,
    AjoCommissionsSelector,
    AjoSavingsSelector,
    AjoUserSelector,
    CardRequestSelector,
    LoanSelector,
    PersonalAjoSavingsSelector,
    PersonalSelector,
    PrefundingSelector,
    ProfileChangeSelector,
    RotationGroupMemberSelector,
)
from ..serializers.payment_serializers import (
    AjoUserWithdrawalAccountSerializer,
    CardUserInformationSerializer,
    CashoutFromSpendingSerializer,
    CashoutRequestSerializer,
    CheckAccountDetailsSerializer,
    CommissionsSerializer,
    GetCommissionsSerializer,
    PayForAjoPlanSerializer,
    PayForDojahVerificationSerializer,
    PayForLoanSerializer,
    PayForOnlendingPlanSerializer,
    PrefundingWalletSerializer,
    ProfileChangeRequestDetailsSerializer,
    SetVerifiedAccountDetailsSerializer,
    TotalCommissionsSerializer,
    TriggerCashoutFromSpendingSerializer,
    TriggerCashoutSerializer,
    WithdrawalAccountAuthSerializer,
    WithdrawToAgencyWalletSerializer,
    WithdrawToExternalAccountSerializer,
)
from ..services import (
    AjoUserWithdrawalAccountService,
    PrefundingService,
    ProfileChangeService,
)
from ..tasks import (
    notify_snpl_savings_milestone,
    reduce_item_stock_on_paybox,
    save_for_health_insurance,
    send_sms_alert_for_ajo_payment,
)
from ..utils.otp_utils import (
    send_first_sms_alert,
    send_out_sms_otp,
    verify_sms_voice_otp,
)
from ..utils.ussd_utils import (
    generate_full_cashout_ussd_code,
    persist_cashout_request,
    ussd_code_to_generate_otp,
    verify_ussd_cashout_otp_and_get_data,
    verify_ussd_otp,
)

logger = settings.LOGGER


class PayForAjoPlanAPIView(generics.GenericAPIView):
    serializer_class = PayForAjoPlanSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        """
        Pay for an Ajo plan from Agent or Prefunding wallet
        """
        user = request.user
        self.access_token = access_token = request.headers.get(
            "Authorization", ""
        ).partition(" ")[-1]

        # validate the data using the serializer
        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        # extract validated data
        plan_id = serializer.validated_data.get("plan_id")
        agent_transaction_pin = serializer.validated_data.get("agent_transaction_pin")
        amount = serializer.validated_data.get("amount")

        # instantiate the AjoSavingsSelector class
        savings_selector = AjoSavingsSelector(id=plan_id, user=user)
        try:
            ajo_savings_plan = savings_selector.get_ajo_saving_plan()
        except ValueError as err:
            return value_error_response(error=err)

        pre_amount_saved = ajo_savings_plan.amount_saved
        product_info = ajo_savings_plan.product_info
        # perform checks on the ajo plan
        if not ajo_savings_plan.is_active:
            return Response(
                data={
                    "status": False,
                    "error": "632",
                    "message": "this plan is no longer active",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
            
        if product_info and product_info.category_name == CategoryNames.HEALTH:
            health_plan_name = product_info.item
            
            health_plan_const = ConstantTable.get_constant_table_instance()
            try:
                health_plans_data = json.loads(health_plan_const.savings_health_plan_details)
                plan_details = health_plans_data.get(health_plan_name, {})
                activation_fee = plan_details.get("activation_fee", 3000)
            except (json.JSONDecodeError, KeyError, AttributeError):
                return Response(
                    data={
                        "status": False,
                        "error": "500",
                        "message": "Unable to get health plan details. Contact Admin",
                    },
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
            
            if amount < activation_fee:
                return Response(
                    data={
                        "status": False,
                        "error": "632",
                        "message": f"First payment for {health_plan_name} Health Plan must be equal to or above activation fee of {activation_fee}.",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        ####instantiate the AjoUserSelector class
        ajo_user_methods = AjoUserSelector(ajo_user=ajo_savings_plan.ajo_user)

        # remove sensitive data from the request
        request_data = request.data
        keys = ["agent_transaction_pin", "user_transaction_pin"]
        [request_data.pop(key, None) for key in keys]

        # check the agent's ajo pin
        verify_agent_pin = verify_transaction_pin(
            transaction_pin=agent_transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )
        if isinstance(verify_agent_pin, Response):
            return verify_agent_pin

        if check_if_prefunding_wallet_can_be_used(ajo_savings=ajo_savings_plan):
            periodic_amount = ajo_savings_plan.periodic_amount
            new_amount = max(0, amount - periodic_amount)
            p_amount = min(amount, periodic_amount)

            prefunding_selector = PrefundingSelector(user=user)
            if prefunding_selector.can_available_balance_cover_payment(amount=p_amount):
                # prefunding can pay, so pay from prefunding and agent wallet
                if new_amount > 0:
                    payment = self.pay_from_agent_wallet(
                        amount=new_amount,
                        ajo_savings_plan=ajo_savings_plan,
                        request_data=request_data,
                        user=user,
                    )
                    
                    if isinstance(payment, dict):
                        if payment.get("status"):
                            if product_info and product_info.category_name == CategoryNames.HEALTH:
                                ajo_savings_plan.refresh_from_db()
                                post_amount_saved = ajo_savings_plan.amount_saved
                                
                                save_for_health_insurance.delay(
                                    ajo_savings_id=ajo_savings_plan.id, 
                                    amount=amount, 
                                    pre_amount_saved=pre_amount_saved,
                                    post_amount_saved=post_amount_saved
                                )
                           
                    if isinstance(payment, Response):
                        return payment

                prefunding_actions = PrefundingActions(user=user)
                try:
                    prefunding_actions.deduct_prefunding_wallet_and_fund_ajo_plan_position(
                        amount=p_amount,
                        ajo_savings=ajo_savings_plan,
                        request_data=json.dumps(request_data),
                    )
                except Exception as err:
                    return self.handle_payment_error(err=err)

                self.send_appropriate_sms(
                    ajo_savings_plan=ajo_savings_plan,
                    amount=amount,
                )
 
                if product_info and product_info.category_name == CategoryNames.HEALTH:
                    ajo_savings_plan.refresh_from_db()
                    post_amount_saved = ajo_savings_plan.amount_saved
                    
                    save_for_health_insurance.delay(
                        ajo_savings_id=ajo_savings_plan.id, 
                        amount=amount, 
                        pre_amount_saved=pre_amount_saved,
                        post_amount_saved=post_amount_saved
                    )

                main_response = Response(
                    {
                        "status": True,
                        "message": f"{amount} paid into plan successfully",
                    },
                    status.HTTP_200_OK,
                )

            else:
                # prefunding can't pay, so pay all from agent wallet
                main_response = self.handle_final_payment(
                    ajo_savings_plan, amount, request_data
                )

        else:
            main_response = self.handle_final_payment(
                ajo_savings_plan, amount, request_data
            )

        if not ajo_savings_plan.commission_paid:
            pay_commissions.delay(
                ajo_savings_id=ajo_savings_plan.id,
                user_id=user.id,
            )
            
        if main_response.data.get("status"):
            if product_info and product_info.category_name == CategoryNames.HEALTH:
                ajo_savings_plan.refresh_from_db()
                post_amount_saved = ajo_savings_plan.amount_saved
                
                save_for_health_insurance.delay(
                    ajo_savings_id=ajo_savings_plan.id, 
                    amount=amount, 
                    pre_amount_saved=pre_amount_saved,
                    post_amount_saved=post_amount_saved
                )


        return main_response

    def pay_from_agent_wallet(
        self,
        amount: float,
        ajo_savings_plan: AjoSaving,
        request_data: dict,
        user: AbstractUser,
    ) -> dict | Response:
        savings_type = ajo_savings_plan.savings_type

        logger.info(f"Fund savings plan: {savings_type}")
        logger.info(f"Fund savings plan: {savings_type}")
        # obtain agent wallet
        agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()

        # check if agent can pay
        if not check_if_agent_can_pay(agent_wallet=agent_wallet, amount=amount):
            return Response(
                {
                    "status": False,
                    "error": "624",
                    "message": "the agent should top up wallet",
                },
                status.HTTP_400_BAD_REQUEST,
            )

        try:
            result = pay_for_ajo_plan(
                amount=amount,
                ajo_user=ajo_savings_plan.ajo_user,
                ajo_savings=ajo_savings_plan,
                user=user,
                request_data=json.dumps(request_data),
            )

            if isinstance(result, str) and "active" in result:
                return Response(
                    {
                        "status": False,
                        "error": "754",
                        "message": result,
                    },
                    status.HTTP_400_BAD_REQUEST,
                )

            valid_savings_types = [
                "BNPL",
                "SNPL",
                "BOOSTA",
                "BOOSTA_2X",
                "BOOSTA_2X_MINI",
                "CREDIT_HEALTH",
            ]
            if savings_type in valid_savings_types:
                if savings_type == SavingsType.BNPL:
                    create_bnpl_eligibility(savings_id=ajo_savings_plan.id)

                if ajo_savings_plan.savings_type == SavingsType.SNPL:
                    notify_snpl_savings_milestone.delay(savings_id=ajo_savings_plan.id)

                boosta_savings_type = [
                    "BOOSTA",
                    "BOOSTA_2X",
                    "BOOSTA_2X_MINI",
                    "CREDIT_HEALTH",
                ]
                if savings_type in boosta_savings_type:
                    if ajo_savings_plan.business_suite is True:
                        try:
                            BusinessSuite.objects.get(savings_id=ajo_savings_plan.id)
                        except BusinessSuite.DoesNotExist:
                            BusinessSuite.objects.create(
                                savings=ajo_savings_plan, amount=150000
                            )

                    logger.info(f"Get boosta savings type eligibility \n")
                    logger.info(
                        f"""savings info for eligibility\n 
                        AGENT TYPE: {ajo_savings_plan.user.user_type}
                        AMOUNT SAVED: {ajo_savings_plan.amount_saved}
                        MATURITY DATE: {ajo_savings_plan.maturity_date}
                        IS ACTIVE: {ajo_savings_plan.is_active}
                        IS ACTIVATED: {ajo_savings_plan.is_activated} \n"""
                    )
                    logger.info(f"Get boosta savings type eligibility\n")

                    LoanEligibility.boosta_eligibility(savings_id=ajo_savings_plan.id)

                if not ajo_savings_plan.stock_marked:
                    reduce_item_stock_on_paybox.delay(
                        savings_id=ajo_savings_plan.id, access_token=self.access_token
                    )

            run_post_savings_to_loan_disk.delay(ajo_savings_id=ajo_savings_plan.id)
            return {
                "status": True,
                "message": f"{amount} paid to plan successfully",
            }

        except Exception as err:
            return self.handle_payment_error(err=err)

    def handle_payment_error(self, err):
        return Response(
            {
                "error": "794",
                "status": False,
                "message": "something went wrong during payment, contact customer care",
                "error_message": str(err),
            },
            status.HTTP_400_BAD_REQUEST,
        )

    def send_appropriate_sms(self, ajo_savings_plan: AjoSaving, amount: float) -> None:
        if not ajo_savings_plan.commission_paid:
            return send_first_sms_alert(
                ajo_savings_id=ajo_savings_plan.id,
                amount=amount,
            )

        return send_sms_alert_for_ajo_payment(
            ajo_savings_id=ajo_savings_plan.id,
            amount=amount,
        )

    def handle_final_payment(
        self,
        ajo_savings_plan: AjoSaving,
        amount: float,
        request_data,
    ) -> Response:
        user = ajo_savings_plan.user

        payment = self.pay_from_agent_wallet(
            amount=amount,
            ajo_savings_plan=ajo_savings_plan,
            request_data=request_data,
            user=user,
        )
        if isinstance(payment, Response):
            return payment

        self.send_appropriate_sms(ajo_savings_plan=ajo_savings_plan, amount=amount)

        return Response(data=payment, status=status.HTTP_200_OK)


class CommissionsAPIView(generics.GenericAPIView):
    serializer_class = GetCommissionsSerializer
    permission_classes = (permissions.IsAuthenticated,)
    pagination_class = CustomPagination

    def get(self, request, *args, **kwargs):
        """
        Obtain the total amount of commissions that can be withdrawn and
        the entire commissions history
        """
        # obtain the user
        user = request.user

        # call the commissions selector
        commissions_selector = AjoCommissionsSelector(user=user)

        # call the agent's wallet
        agent_selector = AjoAgentSelector(user=user)

        # get the total of commissions withdrawal
        # total_commissions_withdrawable = commissions_selector.get_total_amount_of_commissions_not_withdrawn()
        # total_commissions_withdrawable = agent_selector.get_agent_ajo_wallet().commission_balance
        total_commissions_withdrawable = (
            commissions_selector.get_commissions_available_balance()
        )

        # get list of commissions
        commissions = commissions_selector.get_list_of_commissions()
        try:
            paginated_commissions = self.paginate_queryset(commissions)
        except NotFound:
            return pagination_page_not_found_response()

        total_serializer = TotalCommissionsSerializer(
            {"total_commission": total_commissions_withdrawable}
        )
        commissions_serializer = CommissionsSerializer(paginated_commissions, many=True)

        response_data = total_serializer.data
        response_data["commissions"] = commissions_serializer.data

        return self.get_paginated_response(response_data)


class TransferCommissionsToAgentWalletAPIView(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        """
        Transfer the total commissions not withdrawn
        to the agent wallet
        """
        # obtain the user
        user = request.user

        #####check if there is no money to transfer
        # call the commissions selector class
        commissions_selector = AjoCommissionsSelector(user=user)

        # check if the user has an outstanding prefunding instance or balance left in prefunding wallet
        prefunding_selector = PrefundingSelector(user=user)

        if (
            prefunding_selector.how_much_prefunding_has_been_used_against_hold_balance()
            > 0
        ):
            return Response(
                {
                    "status": False,
                    "error": "654",
                    "message": "you have not cleared out your prefunding outstanding",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # if user.user_type == UserType.STAFF_AGENT:
        #     return Response(
        #         {
        #             "status": False,
        #             "error": "592",
        #             "message": "not allowed to withdraw commissions",
        #         },
        #         status=status.HTTP_400_BAD_REQUEST,
        #     )

        # if commissions_selector.get_total_amount_of_commissions_not_withdrawn() <= 0:
        if commissions_selector.get_commissions_available_balance() <= 0:
            return Response(
                {
                    "status": False,
                    "error": "671",
                    "message": "no commissions to transfer out",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # call the fund transfer function
        fund_agent_from_commissions(user=user)

        return Response(
            {
                "status": True,
                "message": "money moved to agent wallet successfully",
            },
            status=status.HTTP_200_OK,
        )


class CashoutRequestAPIView(generics.GenericAPIView):
    serializer_class = CashoutRequestSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        """
        Request for a Cashout through USSD
        """
        ###obtain the user
        user = request.user

        ###try to validate the request data
        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        # obtain the information from the data
        amount = serializer.validated_data.get("amount")
        ajo_saving_id = serializer.validated_data.get("plan_id")
        # phone_number = serializer.validated_data.get("phone_number")

        ###try to obtain the plan

        # instantiate the ajo savings selector
        ajo_saving_selector = AjoSavingsSelector(
            id=ajo_saving_id,
            user=user,
        )

        try:
            ajo_savings_plan = ajo_saving_selector.get_ajo_saving_plan()
            ajo_saving_selector.check_withdrawal_conditions_for_savings_plans()
        except ValueError as err:
            return value_error_response(error=err)

        # perform checks on the ajo savings plan
        # ajo_savings_date_created = ajo_savings_plan.created_at

        # # if ajo_savings_plan.lock and (today.month <= ajo_savings_plan.created_at.month):
        # locked_response = Response(
        #     {
        #         "status": False,
        #         "error": "871",
        #         "message": "this is a locked ajo plan",
        #     },
        #     status.HTTP_400_BAD_REQUEST,
        # )
        # if ajo_savings_plan.frequency == SavingsFrequency.DAILY:
        #     if ajo_savings_plan.lock and is_past_month_year(
        #         target_month=ajo_savings_date_created.month,
        #         target_year=ajo_savings_date_created.year,
        #     ):
        #         return locked_response
        # else:
        #     if ajo_savings_plan.lock and (ajo_savings_plan.maturity_date > timezone.localdate()):
        #         return locked_response

        # if ajo_savings_plan.withdrawn:
        #     return Response(
        #         {
        #             "status": False,
        #             "error": "877",
        #             "message": "this plan has already been withdrawn",
        #         },
        #         status.HTTP_403_FORBIDDEN,
        #     )

        # # check if the plan has a savings_type
        # if ajo_savings_plan.savings_type is not None:
        #     return error_response(message="this plan cannot be withdrawn because of its savings type")
        # if not ajo_savings_plan.is_active:
        #     return Response(
        #         {
        #             "status": False,
        #             "error": "843",
        #             "message": "this ajo plan is inactive",
        #         },
        #         status.HTTP_400_BAD_REQUEST,
        #     )

        phone_number = ajo_savings_plan.ajo_user.phone_number

        ajo_user_selector = AjoUserSelector(
            phone_number=phone_number,
            user=user,
        )

        # perform amount checks
        # if amount > ajo_saving_selector.get_amount_saved_in_plan():
        try:
            if (amount > ajo_user_selector.get_ajo_user_wallet_balance()) or (
                amount > ajo_savings_plan.amount_saved
            ):
                return Response(
                    {
                        "status": False,
                        "error": "671",
                        "message": "insufficient amount to withdraw",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        except ValueError as err:
            return value_error_response(error=err)

        # make sure there is no decimal because it involves cash
        amount = math.ceil(amount)

        # save the request in the cache
        # persist_cashout_request(
        #     amount=amount,
        #     user_id=user.id,
        #     ajo_saving_id=ajo_saving_id,
        #     phone_number=ajo_saving_plan.ajo_user.phone_number,
        # )
        persist_cashout_request(
            amount=amount,
            user_id=user.id,
            phone_number=phone_number,
        )

        # generate a USSD to give the user
        # ussd_code = generate_full_cashout_ussd_code(ajo_saving_id=ajo_saving_id, amount=amount)
        ussd_code = generate_full_cashout_ussd_code(amount=amount)

        return Response(
            {
                "status": True,
                "ussd_code": ussd_code,
            },
            status=status.HTTP_200_OK,
        )


class TriggerCashoutWithOTPAPIView(generics.GenericAPIView):
    serializer_class = TriggerCashoutSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        """
        Trigger the cashout with the OTP from the cashout
        request endpoint
        """
        # obtain the user
        user = request.user

        serializer = self.serializer_class(data=request.data)

        # try to validate the request data
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        # obtain the OTP
        otp = serializer.validated_data.get("otp")
        ajo_saving_id = serializer.validated_data.get("plan_id")
        # phone_number = serializer.validated_data.get("phone_number")
        transaction_pin = serializer.validated_data.get("agent_transaction_pin")

        # instantiate the ajo savings selector
        ajo_saving_selector = AjoSavingsSelector(
            id=ajo_saving_id,
            user=user,
        )

        try:
            ajo_saving_plan = ajo_saving_selector.get_ajo_saving_plan()
        except ValueError as err:
            return value_error_response(error=err)

        phone_number = ajo_saving_plan.ajo_user.phone_number

        # check the cache for this
        try:
            cached_data = verify_ussd_cashout_otp_and_get_data(
                otp=otp,
                # ajo_saving_id=ajo_saving_id,
                phone_number=phone_number,
                user_id=user.id,
            )
        except ValueError as err:
            return value_error_response(error=err)

        # validate the transaction pin
        check_pin = verify_transaction_pin(
            transaction_pin=transaction_pin,
            access_token=request.headers.get("Authorization", "").partition(" ")[-1],
            customer_user_id=user.customer_user_id,
        )

        if isinstance(check_pin, Response):
            return check_pin

        # obtain the amount from the cached data
        amount = cached_data.get("amount")

        # perform a 2FA to check that the amount is in the wallet and plan
        try:
            ajo_user_selector = AjoUserSelector(
                phone_number=phone_number,
                user=user,
            )
            if (amount > ajo_user_selector.get_ajo_user_wallet_balance()) or (
                amount > ajo_saving_plan.amount_saved
            ):
                return Response(
                    {
                        "status": False,
                        "error": "671",
                        "message": "insufficient amount to withdraw",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except ValueError as err:
            return value_error_response(error=err)

        # call the function to debit the ajo savings plan
        try:
            # check if it is a loan plan
            if ajo_saving_plan.loan:
                # try to collect commission
                try:
                    # collect loan commissions
                    loan_collection = collect_loan_plan_commissions(
                        ajo_savings_id=ajo_saving_id,
                        user_id=user.id,
                    )

                    collected_amount = loan_collection.get("amount")

                    # if loan commissions taken out successfully
                    amount = max(amount - collected_amount, 0)

                    if amount == 0:
                        ajo_saving_plan.close_plan()
                        raise ValueError("insufficient funds to debit")

                except ValueError as err:
                    if "commission has been taken out already" in str(err):
                        pass
                    else:
                        return value_error_response(error=err)

            cashout_from_ajo_plan(
                ajo_savings_id=ajo_saving_id,
                amount=amount,
                user_id=user.id,
            )
            # cashout_from_spending_wallet(
            #     amount=amount,
            #     phone_number=phone_number,
            #     user=user,
            # )
        except Exception as err:
            return Response(
                {
                    "error": "792",
                    "status": False,
                    "message": "something went wrong during cashout process, contact customer care",
                    "error_message": str(err),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # close plan if it is still active
        ajo_saving_plan.refresh_from_db()

        if ajo_saving_plan.is_active:
            ajo_saving_plan.is_active = False
            if ajo_saving_plan.amount_saved <= 0 and not ajo_saving_plan.withdrawn:
                ajo_saving_plan.withdrawn = True
            ajo_saving_plan.save()

        else:
            if ajo_saving_plan.amount_saved <= 0:
                if not ajo_saving_plan.withdrawn:
                    ajo_saving_plan.withdrawn = True
                    ajo_saving_plan.save()

        return Response(
            {
                "status": True,
                "message": "cashout successful",
            },
            status=status.HTTP_200_OK,
        )


class CashoutFromSpendingWalletAPIView(generics.GenericAPIView):
    serializer_class = CashoutFromSpendingSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        """
        Request Cashout from Spending wallet through USSD
        """

        user = request.user

        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        amount = serializer.validated_data.get("amount")
        phone_number = serializer.validated_data.get("phone_number")

        ajo_user_selector = AjoUserSelector(phone_number=phone_number, user=user)

        try:
            ajo_spending_wallet = ajo_user_selector.get_spending_wallet()

        except ValueError as err:
            return value_error_response(error=err)

        # make the amount without decimal because of cash
        amount = math.ceil(amount)

        if amount > ajo_user_selector.get_spending_wallet_balance():
            return Response(
                {
                    "status": False,
                    "error": "403",
                    "message": "insufficient funds to withdraw",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # persist the cashout in the cache
        persist_cashout_request(
            amount=amount,
            user_id=user.id,
            phone_number=phone_number,
        )

        # generate a USSD to give the user
        ussd_code = generate_full_cashout_ussd_code(amount=amount)

        return Response(
            {
                "status": True,
                "ussd_code": ussd_code,
            },
            status=status.HTTP_200_OK,
        )


class TriggerCashoutFromSpendingWithOTPAPIView(generics.GenericAPIView):
    serializer_class = TriggerCashoutFromSpendingSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        """
        Trigger the cashout from spending wallet the OTP from the cashout
        request endpoint
        """
        # obtain the user
        user = request.user

        serializer = self.serializer_class(data=request.data)

        # try to validate the request data
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        # obtain the OTP
        otp = serializer.validated_data.get("otp")
        phone_number = serializer.validated_data.get("phone_number")
        transaction_pin = serializer.validated_data.get("agent_transaction_pin")

        ajo_user_selector = AjoUserSelector(phone_number=phone_number, user=user)

        try:
            ajo_spending_wallet = ajo_user_selector.get_spending_wallet()

        except ValueError as err:
            return value_error_response(error=err)

        # check the cache for this
        try:
            cached_data = verify_ussd_cashout_otp_and_get_data(
                otp=otp,
                phone_number=phone_number,
                user_id=user.id,
            )

        except ValueError as err:
            return value_error_response(error=err)

        # validate the transaction pin
        check_pin = verify_transaction_pin(
            transaction_pin=transaction_pin,
            access_token=request.headers.get("Authorization", "").partition(" ")[-1],
            customer_user_id=user.customer_user_id,
        )

        if isinstance(check_pin, Response):
            return check_pin

        # obtain the amount from the cached data
        amount = cached_data.get("amount")

        # call the function to debit the ajo savings plan
        try:
            cashout_from_spending_wallet(
                amount=amount,
                phone_number=phone_number,
                user=user,
            )
        except Exception as err:
            return Response(
                {
                    "error": "792",
                    "status": False,
                    "message": "something went wrong during cashout process, contact customer care",
                    "error_message": str(err),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response(
            {
                "status": True,
                "message": "cashout successful",
            },
            status=status.HTTP_200_OK,
        )


class PayForLoanAPIView(generics.GenericAPIView):
    serializer_class = PayForLoanSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        """
        this pays for the active loan plan for a user
        """
        # obtain the user
        user = request.user

        # obtain the user's access token
        access_token = request.headers.get("Authorization", "").partition(" ")[-1]

        # try to validate the data
        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        # obtain the information from the serializers
        phone_number = serializer.validated_data.get("phone_number")
        user_transaction_pin = serializer.validated_data.get("user_transaction_pin")
        agent_transaction_pin = serializer.validated_data.get("agent_transaction_pin")
        amount = serializer.validated_data.get("amount")

        # instantiate the AjoUserSelector
        ajo_user_selector = AjoUserSelector(phone_number=phone_number, user=user)
        try:
            ajo_user = ajo_user_selector.get_ajo_user()
        except:
            return value_error_response(error=err, code="1012")

        try:
            # retrieve the Loan class
            loan = LoanSelector(ajo_user=ajo_user).get_active_loan()
        except ValueError as err:
            return value_error_response(error=err)

        # perform some checks
        # check if the amount to be paid is above the remaining loan amount
        checks = check_if_amount_higher_than_remaining_loan(
            loan=loan,
            amount=amount,
        )

        if checks.get("status"):
            return Response(
                {
                    "status": False,
                    "error": "694",
                    "message": f"you need to pay {checks.get('remaining_amount')} to complete loan",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # verify user pin
        if not ajo_user_selector.match_pin(pin=user_transaction_pin):
            return Response(
                {
                    "status": False,
                    "error": "791",
                    "message": "incorrect user pin",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        # check the agent's pin
        verify_agent_pin = verify_transaction_pin(
            transaction_pin=agent_transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )
        if isinstance(verify_transaction_pin, Response):
            return Response

        # attempt to pay for the loan
        try:
            pay_for_loan(
                amount=amount,
                ajo_user=ajo_user,
                loan=loan,
                user=user,
                request_data=json.dumps(request.data),
            )
        except Exception as err:
            return Response(
                {
                    "error": "792",
                    "status": False,
                    "message": "something went wrong during payment, contact customer care",
                    "error_message": str(err),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response(
            {
                "status": True,
                "message": "loan funded successfully",
            },
            status=status.HTTP_200_OK,
        )


class WithdrawToAgencyWalletAPIView(generics.GenericAPIView):
    serializer_class = WithdrawToAgencyWalletSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        """
        Withdraw money from Ajo to Agency Banking Wallet

        Currently Supports:

            1. Agent Wallet: "AJO_AGENT"
            2. Personal Ajo: "AJO_PERSONAL"
            3. Rotation Wallet: "ROSCA_PERSONAL"
            4. Onlending plan: "ONLENDING"
        """

        # obtain the user
        user = request.user

        # pass the data to the serializer
        serializer = self.serializer_class(data=request.data)

        # attempt to validate the data
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        ####CHECK IF THE WITHDRAWAL REGULATOR IS ACTIVE#######
        if (
            not ConstantTable.get_constant_table_instance().withdraw_from_wallet_regulator
        ):
            return Response(
                data={
                    "error": "324",
                    "status": False,
                    "message": "service currently unavailable, please try again later",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        # obtain the fields from data
        amount_to_withdraw: float = serializer.validated_data.get("amount")
        wallet_type: WalletTypes = serializer.validated_data.get("wallet_type")
        plan_id: int = serializer.validated_data.get("plan_id")
        plan: Optional[Union[RotationGroup, AjoSaving, Onlending]] = None

        request_data = request.data
        request_data.pop("agent_transaction_pin")

        ####FOR THE CASE OF AJO AGENT WALLET ONLY#####
        if wallet_type == WalletTypes.AJO_AGENT:
            # instantiate the class
            agent_methods = AjoAgentSelector(user=user)
            # obtain the wallet
            agent_wallet = agent_methods.get_agent_ajo_wallet()

            # check if the amount to withdraw to larger than what is in the wallet
            if amount_to_withdraw > agent_methods.get_agent_ajo_wallet_balance():
                return Response(
                    {
                        "status": False,
                        "error": "695",
                        "message": "insufficient amount to withdraw",
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )

        elif wallet_type == WalletTypes.AJO_COMMISSION:
            # instantiate the class
            commission_selector = AjoCommissionsSelector(user=user)
            # obtain the wallet
            agent_wallet = commission_selector.get_commissions_wallet()

            # check if the amount to withdraw to larger than what is in the wallet
            if amount_to_withdraw > agent_wallet.available_balance:
                return Response(
                    {
                        "status": False,
                        "error": "695",
                        "message": "insufficient amount to withdraw",
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )

        ### FOR THE CASE OF AJO PERSONAL WALLET ONLY###
        elif wallet_type == WalletTypes.AJO_PERSONAL:
            # obtain the personal ajo plan
            personal_ajo_selector = PersonalAjoSavingsSelector(id=plan_id, user=user)
            personal_user_selector = PersonalSelector(user=user)

            try:
                plan = personal_ajo_selector.get_ajo_savings_plan()
                ajo_wallet = personal_user_selector.get_savings_wallet()

                amount_to_withdraw = personal_ajo_selector.get_amount_saved_in_plan()

                plan_date_created = plan.created_at
                locked_response = self.handle_failed_response(
                    error_code="871",
                    message="this is a locked ajo plan",
                )
                if plan.frequency == SavingsFrequency.DAILY:
                    if plan.lock and is_past_month_year(
                        target_month=plan_date_created.month,
                        target_year=plan_date_created.year,
                    ):
                        return locked_response
                else:
                    if plan.lock and (plan.maturity_date > timezone.localdate()):
                        return locked_response

                if ((not plan.is_active) and plan.amount_saved <= 0) or (
                    plan.withdrawn
                ):
                    return self.handle_failed_response(
                        error_code="302",
                        message="plan is no longer active",
                    )

                elif (amount_to_withdraw > ajo_wallet.available_balance) or (
                    amount_to_withdraw > plan.amount_saved
                ):
                    return self.handle_failed_response(
                        error_code="301",
                        message="insufficient amount in this plan to withdraw",
                    )

                elif amount_to_withdraw <= 0:
                    return self.handle_failed_response(
                        error_code="303",
                        message="no money in plan to debit",
                    )

            except ValueError as err:
                return value_error_response(error=err, code="817")

        ### FOR THE CASE OF PERSONAL ROTATION WALLET ONLY###
        elif wallet_type == WalletTypes.ROSCA_PERSONAL:
            rotation_wallet = RotationGroupMemberSelector.get_rotation_group_wallet(
                user=user,
            )

            # check if the amount to withdraw to larger than what is in the wallet
            if amount_to_withdraw > rotation_wallet.available_balance:
                return Response(
                    {
                        "status": False,
                        "error": "695",
                        "message": "insufficient amount to withdraw",
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )

        ### For the case of Onlending wallet
        elif wallet_type == WalletTypes.ONLENDING:
            try:
                plan = OnlendingSelector.get_onlending_plan_by_id(
                    id=plan_id,
                    user=user,
                )
            except ValueError as err:
                return value_error_response(error=err)

            if plan.maturity_date > timezone.localdate():
                return self.handle_failed_response(
                    message="this plan's maturity date has not reached",
                    error_code="897",
                )

            if not plan.is_active:
                return self.handle_failed_response(
                    message="this is an inactive plan",
                    error_code="885",
                )

            if plan.withdrawn:
                return self.handle_failed_response(
                    message="this plan has already been withdrawn",
                    error_code="877",
                )

            if plan.amount_saved <= 0:
                return self.handle_failed_response(
                    error_code="874",
                    message="there is no money to withdraw in this plan",
                )

            if plan.ajo_user:
                return self.handle_failed_response(
                    error_code="733",
                    message="this plan belongs to an ajo user",
                )

            amount_to_withdraw = plan.amount_saved

            # check if the amount is in the wallet
            if (
                OnlendingSelector(user=user).get_onlending_wallet().available_balance
                < amount_to_withdraw
            ):
                return self.handle_failed_response(
                    error_code="999",
                    message="not enough to remove from wallet",
                )

        else:
            return Response(
                {
                    "status": False,
                    "error": "756",
                    "message": "not implemented yet",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        ######CHECK THE USER'S PIN#########
        # obtain the pin and access token
        transaction_pin: str = serializer.validated_data.get("agent_transaction_pin")
        access_token: str = request.headers.get("Authorization", "").partition(" ")[-1]

        # verify if the pin is correct
        check_pin: bool | Response = verify_transaction_pin(
            transaction_pin=transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )

        # check if it is a Response object
        if isinstance(check_pin, Response):
            return check_pin

        ################## INQUIRE IF THE RECEIVER IS VALID#########################
        try:
            phone_number = obtain_phone_number(
                access_token=access_token,
                user_id=user.customer_user_id,
            )
            phone_number_verification, verification_data = verify_account(
                access_token=access_token,
                phone_number=phone_number,
            )

        except Exception as err:
            return self.handle_failed_response(
                error_code="343",
                message=str(err),
            )

        if not phone_number_verification:
            FailuresLog.objects.create(user=user, dump=verification_data)
            return self.handle_failed_response(
                error_code="404",
                message="the account to be transferred to is not valid, contact customer care",
            )

        ### create a withdrawal transaction instance####
        if wallet_type == WalletTypes.ROSCA_PERSONAL:
            plan_type = PlanType.ROTATIONGROUP
        elif wallet_type == WalletTypes.ONLENDING:
            plan_type = PlanType.ONLENDING
        else:
            plan_type = PlanType.AJO

        transaction_instance = (
            TransactionService.create_withdrawal_to_wallet_transaction(
                user=user,
                amount=amount_to_withdraw,
                wallet_type=wallet_type,
                request_data=json.dumps(request_data),
                quotation_id=plan.quotation_id if plan else None,
                plan_type=plan_type,
            )
        )

        # instantiate the transaction instance service
        transaction_service = TransactionService(
            transaction_instance=transaction_instance
        )

        ######
        # TODO: Think of conditions to allow for agent wallet withdrawal
        if wallet_type in [
            WalletTypes.AJO_AGENT,
            WalletTypes.AJO_COMMISSION,
        ]:
            withdraw_from_ajo_agent_wallet_to_agency_wallet(
                amount=amount_to_withdraw,
                transaction_instance=transaction_instance,
                agent_wallet=agent_wallet,
            )

        elif wallet_type == WalletTypes.AJO_PERSONAL:
            debit_ajo_plan(
                ajo_savings=plan,
                amount=amount_to_withdraw,
                wallet=ajo_wallet,
                transaction_instance=transaction_instance,
            )

        elif wallet_type == WalletTypes.ROSCA_PERSONAL:
            debit_wallet_for_withdrawal(
                amount=amount_to_withdraw,
                wallet=rotation_wallet,
                transaction_instance=transaction_instance,
                transaction_description=f"{amount_to_withdraw} was just withdrawn to agency wallet",
            )

        elif wallet_type == WalletTypes.ONLENDING:
            PlanPayments.debit_savings_plan(
                savings=plan,
                amount=amount_to_withdraw,
                wallet=OnlendingSelector(user=user).get_onlending_wallet(),
                transaction_instance=transaction_instance,
            )

        else:
            return Response(
                {
                    "status": False,
                    "error": "756",
                    "message": "not implemented",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        # MAKE CALL TO TRANSFER FUNDS###
        transfer = pay_withdrawal_and_handle_response(
            transaction_id=transaction_instance.id,
            amount=amount_to_withdraw,
            phone_number=phone_number,
        )

        # check if it successful
        ###############CHECK IF IT IS SUCCESSFUL#############
        if transfer.get("status"):
            if wallet_type == WalletTypes.AJO_PERSONAL:
                # deactivate the plan
                if plan.is_active:
                    plan.is_active = False

                if plan.amount_saved <= 0:
                    plan.withdrawn = True

                plan.save()

            return Response(
                {
                    "status": True,
                    "message": "money has been withdrawn",
                    "data": transfer.get("data").get("message"),
                },
                status=status.HTTP_200_OK,
            )
        #############IF FAILED, REVERSE THE MONEY############
        #####Remember that the transfer call logs all data into transaction already
        else:
            status_code: int = transfer.get("status_code", 500)
            # get the response from the call
            transfer_data: dict = transfer.get("data")
            obtain_reason = (
                transfer_data.get("message")
                if transfer_data.get("data")
                else transfer_data
            )

            # process the reversal
            if status_code < 500:
                if wallet_type in [
                    WalletTypes.AJO_AGENT,
                    WalletTypes.AJO_COMMISSION,
                ]:
                    reverse_withdrawal.delay(
                        wallet_type=wallet_type,
                        amount=amount_to_withdraw,
                        user_id=user.id,
                        failed_transaction_id=str(transaction_instance.transaction_id),
                        request_data=json.dumps(request_data),
                    )

                elif wallet_type == WalletTypes.AJO_PERSONAL:
                    reverse_personal_ajo_savings_debit(
                        plan_id=plan_id,
                        user_id=user.id,
                        amount=amount_to_withdraw,
                        failed_transaction_id=str(transaction_instance.transaction_id),
                        plan_type=PlanType.AJO,
                        request_data=json.dumps(request_data),
                    )

                elif wallet_type == WalletTypes.ROSCA_PERSONAL:
                    reverse_withdrawal.delay(
                        wallet_type=WalletTypes.ROSCA_PERSONAL,
                        amount=amount_to_withdraw,
                        user_id=user.id,
                        failed_transaction_id=str(transaction_instance.transaction_id),
                        request_data=json.dumps(request_data),
                    )

                elif wallet_type == WalletTypes.ONLENDING:
                    PlanPayments.reverse_onlending_plan_debit(
                        plan_id=plan_id,
                        user_id=user.id,
                        amount=amount_to_withdraw,
                        failed_transaction_id=str(transaction_instance.transaction_id),
                        plan_type=PlanType.ONLENDING,
                        request_data=json.dumps(request_data),
                    )

            return Response(
                {
                    "error": 800,
                    "status": False,
                    "message": obtain_reason,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

    def handle_failed_response(
        self,
        error_code: str,
        message: str,
        error_message: str | None = None,
    ) -> Response:
        response = {
            "status": True,
            "error": error_code,
            "message": message,
        }

        if error_message:
            response["error_message"] = error_message

        return Response(
            data=response,
            status=status.HTTP_400_BAD_REQUEST,
        )


class PrefundingRequestAPIView(APIView):
    """
    This view handles the request for prefunding
    """

    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        """
        This sends a request to the server and returns eligibility status
        """
        # obtain the user
        user = request.user

        # check the eligibility of the user
        eligible = prefunding_eligibility(user=user)

        if not eligible:
            return Response(
                {
                    "status": False,
                    "error": "561",
                    "message": "this agent is not eligible for prefunding",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        ####call the prefunding actions and service classes to create prefunding
        # obtain the constants
        prefunding_constant_amount = (
            ConstantTable.get_constant_table_instance().prefunding_amount
        )
        # create the prefunding instance
        try:
            prefunding_instance = PrefundingService.create_prefunding(
                {
                    "user": user,
                    "prefunding_amount": prefunding_constant_amount,
                    "balance_left": prefunding_constant_amount,
                }
            )
        except ValueError as err:
            return value_error_response(error=err, code="572")

        # call the prefunding actions class
        prefunding_actions = PrefundingActions(user=user)

        # fund the prefunding wallet
        prefunding_actions.deposit_prefunding_constant_into_wallet()

        return Response(
            {
                "status": True,
                "message": f"{prefunding_constant_amount} naira has been deposited into your prefunding wallet",
            },
            status=status.HTTP_200_OK,
        )


class PrefundingWalletAPIView(generics.GenericAPIView):
    """
    this API View deals with Prefunding Wallet information
    """

    serializer_class = PrefundingWalletSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request, *args, **kwargs):
        """
        This returns the various balances for the prefunding
        wallet
        """
        # obtain the user
        user = request.user

        # call the Prefunding selector
        prefunding_selector = PrefundingSelector(user=user)

        serializer = self.serializer_class(
            {
                "available_balance": prefunding_selector.available_balance_in_prefunding_wallet(),
                "hold_balance": prefunding_selector.check_how_much_outstanding_left(),
            }
        )

        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )


class CheckAjoUserWithdrawalAccountDetailsAPIView(generics.GenericAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = CheckAccountDetailsSerializer

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
        ],
    )
    def post(self, request, *args, **kwargs):
        """
        This endpoint checks if the account number is valid and
        returns the account name, as well as send an OTP to the ajo user.
        """
        # obtain the phone number from the query parameter
        phone_number = request.query_params.get("phone_number")

        if not phone_number:
            return Response(
                {
                    "status": False,
                    "error": "1012",
                    "message": "phone number required in query parameters",
                },
                status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        # obtain the account number, bank code and ajo user phone number
        account_number = serializer.validated_data.get("account_number")
        bank_code = serializer.validated_data.get("bank_code")
        bank_name = serializer.validated_data.get("bank_name")

        cbn_bank_code = AgencyBankingClass.get_bank_cbn_code(nip_code=bank_code)
        paystack_direct = PaystackApi.resolve_account_number(
            account_number=account_number, bank_code=cbn_bank_code
        )

        if paystack_direct.get("status"):
            send_out_sms_otp(phone_number=phone_number)
            return Response(
                {
                    "status": True,
                    "message": "OTP has been sent to the ajo user's phone number",
                    "data": {
                        **paystack_direct.get("data").get("data"),
                        "bank_code": bank_code,
                        "bank_name": bank_name,
                        "ussd_code": ussd_code_to_generate_otp,
                    },
                },
                status=status.HTTP_200_OK,
            )

        paystack_account_name = AgencyBankingClass.fetch_account_name(
            account_number=account_number,
            bank_code=cbn_bank_code,
        )

        if paystack_account_name.get("status"):
            send_out_sms_otp(phone_number=phone_number)
            return Response(
                {
                    "status": True,
                    "message": "OTP has been sent to the ajo user's phone number",
                    "data": {
                        **paystack_account_name.get("data").get("data"),
                        "bank_code": bank_code,
                        "bank_name": bank_name,
                        "ussd_code": ussd_code_to_generate_otp,
                    },
                },
                status=status.HTTP_200_OK,
            )

        # call the agency banking endpoint to verify this account number
        fetch_account_name = AgencyBankingClass.fetch_account_name(
            account_number=account_number, bank_code=bank_code
        )

        if not fetch_account_name.get("status"):
            response_data = fetch_account_name.get("data")
            data = response_data if response_data else fetch_account_name

            return Response(
                {
                    "status": False,
                    "error": "404",
                    "message": "could not retrieve account name",
                    "data": fetch_account_name.get("data"),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # this is just to verify that the ajo user exists and the phone number is valid
        try:
            ajo_user = AjoUserSelector(
                phone_number=phone_number, user=request.user
            ).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        # call send sms OTP function
        print("========================")
        print("SEND OTP (CHECK AJO WITHDRAWAL DETAILS)")
        print("SEND OTP (CHECK AJO WITHDRAWAL DETAILS)")
        print("SEND OTP (CHECK AJO WITHDRAWAL DETAILS)")
        print("USER PHONE NUMBER", phone_number)
        print("USER PHONE NUMBER", phone_number)
        otp_sms = send_out_sms_otp(phone_number=phone_number)
        print("OTP SMS RESULT", otp_sms)
        print("OTP SMS RESULT", otp_sms)
        print("OTP SMS RESULT", otp_sms)
        print("========================")

        return Response(
            {
                "status": True,
                "message": "OTP has been sent to the ajo user's phone number",
                "data": {
                    **fetch_account_name.get("data").get("data"),
                    "bank_code": bank_code,
                    "bank_name": bank_name,
                    "ussd_code": ussd_code_to_generate_otp,
                },
            },
            status=status.HTTP_200_OK,
        )


class GetSetDeleteVerifiedWithdrawalAccountDetailsAPIView(generics.GenericAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_classes = {
        "GET": AjoUserWithdrawalAccountSerializer,
        "POST": SetVerifiedAccountDetailsSerializer,
    }

    def get_serializer_class(self) -> serializers.Serializer:
        return self.serializer_classes.get(self.request.method)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
        ],
    )
    def post(self, request, *args, **kwargs):
        """
        This endpoint saves the withdrawal account details to the ajo user
        as long as the OTP is valid
        """
        # obtain the phone number from the query parameter
        phone_number = request.query_params.get("phone_number")

        if not phone_number:
            return Response(
                {
                    "status": False,
                    "error": "1012",
                    "message": "phone number required in query parameter",
                },
                status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.get_serializer_class()(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        try:
            ajo_user = AjoUserSelector(
                phone_number=phone_number, user=request.user
            ).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        # obtain the information from the validated data
        validated_data = dict(serializer.data)
        otp = validated_data.pop("otp")

        # verify the OTP
        verify_otp: bool = verify_sms_voice_otp(
            otp=otp,
            phone_number=phone_number,
        ) or verify_ussd_otp(
            otp=otp,
            phone_number=phone_number,
        )

        if not verify_otp:
            return Response(
                {
                    "status": False,
                    "error": "304",
                    "message": "invalid OTP, please try again",
                },
                status.HTTP_400_BAD_REQUEST,
            )

        validated_data["ajo_user"] = ajo_user
        validated_data["user"] = request.user

        try:
            withdrawal_account = AjoUserWithdrawalAccountService.set_withdrawal_account(
                validated_data
            )
        except ValueError as err:
            return value_error_response(error=err)

        withdrawal_account_serializer = AjoUserWithdrawalAccountSerializer(
            withdrawal_account
        )

        return Response(
            {
                "status": True,
                "message": "withdrawal account set successfully",
                "data": withdrawal_account_serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """
        This endpoint returns the withdrawal account details set for the ajo user
        """
        phone_number = request.query_params.get("phone_number")

        if not phone_number:
            return Response(
                {
                    "status": False,
                    "error": "1012",
                    "message": "phone number required in query parameter",
                },
                status.HTTP_200_OK,
            )

        try:
            ajo_user = AjoUserSelector(
                phone_number=phone_number, user=request.user
            ).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        try:
            withdrawal_account = ajo_user.withdrawal_account
        except ObjectDoesNotExist as err:
            return Response(
                {
                    "status": False,
                    "error": "404",
                    "message": "no withdrawal account has been set for this ajo user",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.get_serializer_class()(withdrawal_account)

        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
        ],
    )
    def delete(self, request, *args, **kwargs):
        """
        delete the withdrawal account attached to an ajo user
        """
        phone_number = request.query_params.get("phone_number")

        if not phone_number:
            return Response(
                {
                    "status": False,
                    "error": "1012",
                    "message": "phone number required in query parameter",
                },
                status.HTTP_200_OK,
            )

        try:
            ajo_user = AjoUserSelector(
                phone_number=phone_number, user=request.user
            ).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        try:
            ajo_user.withdrawal_account
        except ObjectDoesNotExist as err:
            return Response(
                {
                    "status": True,
                    "message": "this user has no withdrawal account to delete",
                },
                status=status.HTTP_200_OK,
            )

        ajo_user.withdrawal_account.delete()

        return Response(
            {
                "status": True,
                "message": "withdrawal account deleted successfully",
            },
            status.HTTP_200_OK,
        )


class GetExternalAccountWithdrawalBalanceAPIView(generics.GenericAPIView):
    serializer_class = WithdrawalAccountAuthSerializer
    permission_classes = (permissions.IsAuthenticated,)

    status_codes = {
        400: status.HTTP_400_BAD_REQUEST,
        403: status.HTTP_403_FORBIDDEN,
        404: status.HTTP_404_NOT_FOUND,
        422: status.HTTP_422_UNPROCESSABLE_ENTITY,
    }

    def generate_bad_response(
        self,
        message: str,
        error: str = "400",
        status_code: int = 400,
    ) -> Response:
        return Response(
            {
                "status": False,
                "error": error,
                "message": message,
            },
            self.status_codes.get(status_code),
        )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
            openapi.Parameter(
                name="wallet_type",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="wallet type to check: SPENDING or SAVINGS",
            ),
            openapi.Parameter(
                name="plan_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="plan id of the ajo savings. Only when wallet_type is SAVINGS",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """
        Get the balance in an ajo savings or spending wallet alongside with
        the external withdrawal bank account details.

        Returns a 404 until the withdrawal feature is ready.
        """
        phone_number = request.query_params.get("phone_number", None)
        wallet_type = request.query_params.get("wallet_type", None)
        plan_id = request.query_params.get("plan_id", None)

        valid_wallet_types = [
            "SAVINGS",
            "SPENDING",
        ]

        if not phone_number:
            return self.generate_bad_response(
                message="no phone_number in query parameter",
                error="404",
                status_code=422,
            )

        if not wallet_type:
            return self.generate_bad_response(
                message="no wallet_type in query parameter",
                error="404",
                status_code=422,
            )

        wallet_type = wallet_type.upper()

        if wallet_type not in valid_wallet_types:
            return self.generate_bad_response(
                message=f"choose a valid wallet_type from this list: {valid_wallet_types}",
                status_code=422,
            )

        ajo_user_selector = AjoUserSelector(
            phone_number=phone_number,
            user=request.user,
        )

        try:
            ajo_user = AjoUserSelector(
                phone_number=phone_number, user=request.user
            ).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        try:
            withdrawal_account = ajo_user.withdrawal_account
        except ObjectDoesNotExist as err:
            return Response(
                {
                    "status": False,
                    "error": "404",
                    "message": "no withdrawal account has been set for this ajo user",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if wallet_type == "SAVINGS":
            if not plan_id:
                return self.generate_bad_response(
                    message="no plan_id in the query parameter",
                    error="404",
                    status_code=422,
                )

            try:
                balance = AjoSavingsSelector(
                    id=plan_id, user=request.user
                ).get_amount_saved_in_plan()
            except ValueError as err:
                return value_error_response(error=err)

        elif wallet_type == "SPENDING":
            balance = ajo_user_selector.get_spending_wallet_balance()

        serializer = AjoUserWithdrawalAccountSerializer(withdrawal_account)

        serializer_data = {}
        serializer_data["balance"] = balance
        serializer_data.update(dict(serializer.data))

        return Response(
            {
                "status": True,
                "data": serializer_data,
            },
            status.HTTP_200_OK,
        )
        # return Response(
        #     {
        #         "status": False,
        #         "error": "404",
        #         "message": "not implemented",
        #     },
        #     status.HTTP_404_NOT_FOUND,
        # )


class WithdrawToExternalAccountAPIView(generics.GenericAPIView):
    serializer_class = WithdrawToExternalAccountSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def failed_response(
        self,
        message: str,
        code: str,
        error_message: Optional[str] = None,
    ) -> Response:
        data = {
            "status": False,
            "error": code,
            "message": message,
        }

        if error_message:
            data["error_message"] = error_message

        return Response(
            data=data,
            status=status.HTTP_400_BAD_REQUEST,
        )

    def post(self, request, *args, **kwargs):
        """
        Withdraw money from your ajo plan or spending wallet
        to your external account

        Currently Supports:
            1. Ajo Savings plan: "SAVINGS"
            2. Spend Wallet: "SPENDING"
            3. Onlending plan: "ONLENDING"
            4. Onlending Main Wallet: "ONLENDING_MAIN"
        """
        # obtain the user
        user = request.user

        # pass the data to the serializer
        serializer = self.serializer_class(data=request.data)

        # attempt to validate the data
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        # CHECK IF THE WITHDRAWAL REGULATOR IS ACTIVE #
        if (
            not ConstantTable.get_constant_table_instance().withdraw_to_external_regulator
        ):
            return Response(
                {
                    "error": "324",
                    "status": False,
                    "message": "service currently unavailable, please try again later",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        # obtain the fields from validated data
        phone_number: str = serializer.validated_data.get("phone_number", None)
        wallet_type: str = serializer.validated_data.get("wallet_type")
        plan_id: int = serializer.validated_data.get("plan_id")
        amount: float | None = serializer.validated_data.get("amount", None)

        ajo_user = None
        withdrawal_account: AjoUserWithdrawalAccount | WithdrawalAccount | None = None

        if phone_number:
            ajo_user_selector = AjoUserSelector(
                phone_number=phone_number,
                user=user,
            )
            ajo_user = ajo_user_selector.get_ajo_user()

            try:
                does_ajo_user_have_repayment_outstanding(ajo_user=ajo_user)
            except ValueError as err:
                return value_error_response(error=err, code="635")

            try:
                withdrawal_account: AjoUserWithdrawalAccount = (
                    ajo_user.withdrawal_account
                )
            except ObjectDoesNotExist as err:
                return Response(
                    {
                        "status": False,
                        "error": "404",
                        "message": "no withdrawal account has been set for this ajo user",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            try:
                withdrawal_account = user.withdrawal_account
            except ObjectDoesNotExist as err:
                return Response(
                    {
                        "status": False,
                        "error": "404",
                        "message": "no withdrawal account has been set for this user",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        account_number = withdrawal_account.account_number
        bank_name = withdrawal_account.bank_name

        quotation_id = None
        # PREPARE TO DEBIT THE USER'S POSITION
        if wallet_type == "SAVINGS":
            ajo_savings_selector = AjoSavingsSelector(
                id=plan_id,
                user=user,
            )

            try:
                ajo_savings_plan = ajo_savings_selector.get_ajo_saving_plan()
                ajo_savings_selector.check_withdrawal_conditions_for_savings_plans()
            except ValueError as err:
                return value_error_response(error=err)

            # ajo_savings_date_created = ajo_savings_plan.created_at

            # if ajo_savings_plan.lock and is_past_month_year(
            #     target_month=ajo_savings_date_created.month,
            #     target_year=ajo_savings_date_created.year,
            # ):
            #     return Response(
            #         {
            #             "status": False,
            #             "error": "871",
            #             "message": "this is a locked ajo plan",
            #         },
            #         status.HTTP_400_BAD_REQUEST,
            #     )

            # if ajo_savings_plan.withdrawn:
            #     return Response(
            #         {
            #             "status": False,
            #             "error": "877",
            #             "message": "this plan has already been withdrawn",
            #         },
            #         status.HTTP_403_FORBIDDEN,
            #     )

            # # check if the plan has a savings_type
            # if ajo_savings_plan.savings_type is not None:
            #     return error_response(message="this plan cannot be withdrawn because of its savings type")

            if not amount:
                if ajo_savings_plan.amount_saved <= 0:
                    return Response(
                        {
                            "status": False,
                            "error": "874",
                            "message": "there is no money to withdraw in this plan",
                        },
                        status.HTTP_403_FORBIDDEN,
                    )

                amount = ajo_savings_plan.amount_saved

                if ajo_user_selector.get_ajo_user_wallet_balance() < amount:
                    return Response(
                        {
                            "status": False,
                            "error": "879",
                            "message": "insufficient funds to withdraw this amount",
                        },
                        status.HTTP_403_FORBIDDEN,
                    )

            else:
                if (ajo_savings_plan.amount_saved < amount) or (
                    ajo_user_selector.get_ajo_user_wallet_balance() < amount
                ):
                    return Response(
                        {
                            "status": False,
                            "error": "879",
                            "message": "insufficient funds to withdraw this amount",
                        },
                        status.HTTP_403_FORBIDDEN,
                    )

                # check if it is a loan plan
                if ajo_savings_plan.loan:
                    # try to collect commission
                    try:
                        # collect loan commissions
                        loan_collection = collect_loan_plan_commissions(
                            ajo_savings_id=ajo_savings_plan.id,
                            user_id=user.id,
                        )

                        collected_amount = loan_collection.get("amount")

                        # if loan commissions taken out successfully
                        amount = max(amount - collected_amount, 0)

                        if amount == 0:
                            ajo_savings_plan.close_plan()
                            raise ValueError("insufficient funds to debit")

                    except ValueError as err:
                        if "commission has been taken out already" in str(err):
                            pass
                        else:
                            return value_error_response(error=err)

                    except Exception as err:
                        return value_error_response(error=err)

            description = f"{amount} transferred from {ajo_savings_plan.name} ajo plan, {ajo_user.phone_number}, to {account_number}, {bank_name}."
            transaction_wallet_type = WalletTypes.AJO_USER
            quotation_id = ajo_savings_plan.quotation_id

        elif wallet_type == "SPENDING":
            try:
                ajo_spending_wallet = ajo_user_selector.get_spending_wallet()

            except ValueError as err:
                return value_error_response(error=err)

            if not amount:
                if ajo_spending_wallet.available_balance <= 0:
                    return Response(
                        {
                            "status": False,
                            "error": "874",
                            "message": "there is no money to withdraw in your spend wallet",
                        },
                        status.HTTP_403_FORBIDDEN,
                    )

                amount = ajo_spending_wallet.available_balance

            else:
                if ajo_spending_wallet.available_balance < amount:
                    return Response(
                        {
                            "status": False,
                            "error": "879",
                            "message": "insufficient funds to withdraw this amount",
                        },
                        status.HTTP_403_FORBIDDEN,
                    )

            description = f"{amount} transferred from spend wallet, {ajo_user.phone_number}, to {account_number}, {bank_name}."
            transaction_wallet_type = WalletTypes.AJO_SPENDING

        elif wallet_type == "ONLENDING":
            try:
                onlending_plan = OnlendingSelector.get_onlending_plan_by_id(
                    id=plan_id,
                    user=user,
                )
            except ValueError as err:
                return value_error_response(error=err)

            if onlending_plan.maturity_date > timezone.localdate():
                return self.failed_response(
                    message="this plan's maturity date has not reached",
                    code="897",
                )

            if not onlending_plan.is_active:
                return self.failed_response(
                    message="this is an inactive plan",
                    code="885",
                )

            if onlending_plan.withdrawn:
                return self.failed_response(
                    message="this plan has already been withdrawn",
                    code="877",
                )

            if onlending_plan.amount_saved <= 0:
                return self.failed_response(
                    message="there is no money to withdraw in this plan",
                    code="874",
                )

            if not onlending_plan.ajo_user:
                return self.failed_response(
                    message="this plan does not belong to an ajo user",
                    code="733",
                )

            amount = onlending_plan.amount_saved

            # check if the amount is in the wallet
            if (
                OnlendingSelector(user=user, ajo_user=ajo_user)
                .get_onlending_wallet()
                .available_balance
                < amount
            ):
                return self.failed_response(
                    message="not enough to remove from wallet",
                    code="999",
                )

            description = f"{amount} transferred from {onlending_plan.name} onlending, to {account_number}, {bank_name}"
            transaction_wallet_type = WalletTypes.ONLENDING

        elif wallet_type == "ONLENDING_MAIN":

            try:
                onlending_main_wallet = OnlendingSelector(
                    user=user, ajo_user=ajo_user
                ).get_main_onlending_wallet()

            except ValueError as err:
                return value_error_response(error=err)

            # return Response(
            #     {
            #         "status": False,
            #         "error": "911",
            #         "message": "onlending main withdrawals are temporarily blocked, contact customer care",
            #     },
            #     status.HTTP_400_BAD_REQUEST,
            # )

            if not amount:
                if onlending_main_wallet.available_balance <= 0:
                    return Response(
                        {
                            "status": False,
                            "error": "874",
                            "message": "there is no money to withdraw in your spend wallet",
                        },
                        status.HTTP_403_FORBIDDEN,
                    )

                amount = onlending_main_wallet.available_balance

            else:
                if onlending_main_wallet.available_balance < amount:
                    return Response(
                        {
                            "status": False,
                            "error": "879",
                            "message": "insufficient funds to withdraw this amount",
                        },
                        status.HTTP_403_FORBIDDEN,
                    )

            description = f"{amount} transferred from onlending main wallet, to {account_number}, {bank_name}."
            transaction_wallet_type = WalletTypes.ONLENDING_MAIN

        if wallet_type == "ONLENDING_MAIN":
            plan_type = PlanType.ONLENDING
        else:
            plan_type = PlanType.AJO

        # CREATE THE TRANSACTION
        external_transfer_transaction = (
            TransactionService.create_transfer_to_external_account_transaction(
                user=user,
                amount=amount,
                description=description,
                wallet_type=transaction_wallet_type,
                ajo_user=ajo_user,
                request_data=json.dumps(request.data),
                plan_type=plan_type,
                quotation_id=quotation_id,
            )
        )

        # DEBIT THE USER'S POSITION
        if wallet_type == "SAVINGS":
            debit_ajo_plan(
                ajo_savings=ajo_savings_plan,
                amount=amount,
                wallet=ajo_user_selector.get_ajo_user_wallet(),
                transaction_instance=external_transfer_transaction,
            )
            ajo_savings_plan.close_plan()
            narration = f"{amount} transferred from {ajo_user.phone_number}: {ajo_savings_plan.name} plan"

        elif wallet_type == "SPENDING":
            debit_wallet_and_fill_transaction(
                amount=amount,
                wallet=ajo_spending_wallet,
                transaction_instance=external_transfer_transaction,
            )
            narration = (
                f"{amount} transferred from {ajo_user.phone_number}: spend wallet"
            )

        elif wallet_type == "ONLENDING":
            PlanPayments.debit_savings_plan(
                savings=onlending_plan,
                amount=amount,
                wallet=OnlendingSelector(
                    user=user, ajo_user=ajo_user
                ).get_onlending_wallet(),
                transaction_instance=external_transfer_transaction,
            )
            narration = f"{amount} transferred from {ajo_user.phone_number if ajo_user else user.email}: {onlending_plan.name} plan"

        elif wallet_type == "ONLENDING_MAIN":
            debit_wallet_and_fill_transaction(
                amount=amount,
                wallet=onlending_main_wallet,
                transaction_instance=external_transfer_transaction,
            )
            narration = f"{amount} transferred from {ajo_user.phone_number if ajo_user else user.email}: onlending"

        # PERFORM THE API CALL
        # narration = f"{amount} transferred from {ajo_user.phone_number} ajo user"
        make_transfer = (
            AgencyBankingClass.send_money_to_external_account_through_agency(
                amount=amount,
                account_number=account_number,
                account_name=withdrawal_account.account_name,
                bank_code=withdrawal_account.bank_code,
                bank_name=bank_name,
                customer_reference=str(external_transfer_transaction.transaction_id),
                narration=narration,
            )
        )

        # update the transaction
        payload_data: Dict[str, Any] = make_transfer.get("data")
        if payload_data:
            external_transfer_transaction.payload = make_transfer.get("data")

            transfer_data: Dict[str, Any] | None = payload_data.get("data", None)
            if transfer_data:
                external_transfer_transaction.unique_reference = transfer_data.get(
                    "escrow_id", None
                )

        else:
            external_transfer_transaction.payload = make_transfer

        external_transfer_transaction.save()

        # RETURN THAT IT IS PROCESSING
        if make_transfer.get("status"):
            settle_transfer_transaction.delay(
                transaction_id=external_transfer_transaction.id,
            )

            return Response(
                {
                    "status": True,
                    "message": "your transfer is being processed",
                },
                status.HTTP_202_ACCEPTED,
            )

        else:
            return Response(
                {
                    "status": False,
                    "error": "400",
                    "message": "something went wrong, try again, contact customer care.",
                    "error_message": make_transfer,
                },
                status.HTTP_400_BAD_REQUEST,
            )


class SettleDueBalanceFromAgentWalletAPIView(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    @swagger_auto_schema(
        responses={
            200: "successful",
            400: "errors",
        }
    )
    def post(self, request, *args, **kwargs):
        """
        Settle due balance in prefunding from the agent wallet
        """
        user = request.user

        try:
            PrefundingActions(
                user=user
            ).settle_prefunding_outstanding_from_agent_wallet()

        except ValueError as err:
            return value_error_response(error=err)

        return Response(
            {
                "status": True,
                "message": "prefunding due balance cleared successfully",
            },
            status.HTTP_200_OK,
        )


class PayForDojahVerificationRequestAPIView(generics.GenericAPIView):
    serializer_classes = {
        "POST": PayForDojahVerificationSerializer,
        "GET": ProfileChangeRequestDetailsSerializer,
    }
    permission_classes = (permissions.IsAuthenticated,)

    def get_serializer_class(self) -> serializers.Serializer:
        return self.serializer_classes.get(self.request.method)

    @swagger_auto_schema(
        responses={
            200: ProfileChangeRequestDetailsSerializer,
            403: "active request or unused paid request",
            400: "error during payment",
            422: "data validation error",
        }
    )
    def post(self, request, *args, **kwargs):
        """
        Request for Dojah Verification and make a payment from spending wallet to begin flow
        """
        dojah_charges = {
            "BVN": ConstantTable.get_constant_table_instance().dojah_bvn_charge,
            "NIN": ConstantTable.get_constant_table_instance().dojah_nin_charge,
        }
        serializer = self.get_serializer_class()(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        user = request.user
        phone_number = serializer.validated_data.get("phone_number")
        verification_type = serializer.validated_data.get("verification_type")

        ajo_user_selector = AjoUserSelector(phone_number=phone_number, user=user)
        ajo_user = ajo_user_selector.get_ajo_user()
        profile_selector = ProfileChangeSelector(ajo_user=ajo_user)

        # check if there is any active request
        active_request = profile_selector.get_latest_change_request()
        if active_request:
            return Response(
                {
                    "status": False,
                    "error": "403",
                    "message": "there is an active change request",
                },
                status.HTTP_403_FORBIDDEN,
            )

        unused_paid_request = profile_selector.get_latest_unused_paid_request()
        if unused_paid_request:
            return Response(
                {
                    "status": False,
                    "error": "403",
                    "message": "you have an unused paid change request",
                },
                status.HTTP_403_FORBIDDEN,
            )

        ajo_user_spending_wallet = ajo_user_selector.get_spending_wallet()
        charge_amount = dojah_charges.get(verification_type)

        if ajo_user.charge_verification:
            if charge_amount > ajo_user_spending_wallet.available_balance:
                return Response(
                    {
                        "status": False,
                        "error": "403",
                        "message": "insufficient funds to make this change request",
                    },
                    status.HTTP_402_PAYMENT_REQUIRED,
                )

            try:
                debit_ajo_user_wallet_for_dojah_charge(
                    ajo_user=ajo_user,
                    amount=charge_amount,
                    ajo_user_wallet=ajo_user_spending_wallet,
                )
            except Exception as err:
                return Response(
                    {
                        "status": False,
                        "error": "800",
                        "message": "an error occurred while trying to pay. see 'error_message' for more details.",
                        "error_message": str(err),
                    },
                    status.HTTP_400_BAD_REQUEST,
                )

        try:
            new_request = ProfileChangeService.create_paid_change_request(
                ajo_user=ajo_user
            )
        except ValueError as err:
            return value_error_response(error=err)

        response_serializer = ProfileChangeRequestDetailsSerializer(new_request)

        return Response(
            {
                "status": True,
                "data": response_serializer.data,
            },
            status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """
        Get the change requests not used and session_id
        """
        phone_number = request.query_params.get("phone_number")
        user = request.user

        if not phone_number:
            return Response(
                {
                    "status": False,
                    "error": "1012",
                    "message": "phone number required in query parameter",
                },
                status.HTTP_200_OK,
            )

        ajo_user = AjoUserSelector(phone_number=phone_number, user=user).get_ajo_user()

        unused_paid_request = ProfileChangeSelector(
            ajo_user=ajo_user
        ).get_latest_unused_paid_request()

        if not unused_paid_request:
            return Response(
                {
                    "status": False,
                    "error": "404",
                    "message": "no unused paid request",
                },
                status.HTTP_404_NOT_FOUND,
            )

        else:
            serializer = self.get_serializer_class()(unused_paid_request)
            return Response(
                {
                    "status": True,
                    "data": serializer.data,
                },
                status.HTTP_200_OK,
            )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
        ],
    )
    def patch(self, request, *args, **kwargs):
        """
        close all failed requests
        """
        phone_number = request.query_params.get("phone_number")

        if not phone_number:
            return Response(
                {
                    "status": False,
                    "error": "1012",
                    "message": "phone number required in query parameter",
                },
                status.HTTP_200_OK,
            )

        ajo_user = AjoUserSelector(
            phone_number=phone_number, user=request.user
        ).get_ajo_user()
        profile_selector = ProfileChangeSelector(ajo_user=ajo_user)

        if not profile_selector.is_there_an_active_failed_request():
            return Response(
                {
                    "status": False,
                    "error": "404",
                    "message": "there is no active failed request to close",
                },
                status.HTTP_404_NOT_FOUND,
            )

        ProfileChangeService.close_failed_requests(
            ajo_user=ajo_user,
        )

        return Response(
            {
                "status": True,
                "message": "all active failed profile change requests have been closed",
            },
            status.HTTP_200_OK,
        )


class CardUserInformationAPIView(generics.GenericAPIView):
    serializer_class = CardUserInformationSerializer
    permission_classes = (permissions.IsAuthenticated,)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "card_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=True,
                description="the card ID",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """
        Get the Ajo User information of a debit card.
        """
        user = request.user
        card_id = request.query_params.get("card_id")

        if not card_id:
            return Response(
                {
                    "status": False,
                    "error": "404",
                    "message": "no card_id in query parameter",
                },
                status.HTTP_422_UNPROCESSABLE_ENTITY,
            )

        try:
            ajo_user = CardRequestSelector(
                card_id=card_id
            ).get_ajo_user_from_card_instance()

        except ValueError as err:
            return value_error_response(
                error=err,
                code="808",
            )

        if user != ajo_user.user:
            return Response(
                {
                    "status": False,
                    "error": "891",
                    "message": "this ajo user is not registered under this user",
                },
                status.HTTP_403_FORBIDDEN,
            )

        data = {
            "status": True,
            "data": ajo_user,
        }
        serializer = self.serializer_class(data)

        return Response(
            serializer.data,
            status.HTTP_200_OK,
        )


class CreateWemaWalletAPIView(APIView):
    # serializer_class = CardUserInformationSerializer
    # permission_classes = (permissions.IsAuthenticated,)

    def get(self, request, *args, **kwargs):
        ajo_user_id = request.query_params.get("ajo_user_id")
        query_type = request.query_params.get("query_type")
        url_email = request.query_params.get("email")
        url_phone = request.query_params.get("phone")
        url_bvn = request.query_params.get("bvn")
        wallet_type = request.query_params.get("wallet_type")

        try:
            ajo_user = AjoUser.objects.get(id=ajo_user_id)
        except AjoUser.DoesNotExist as err:
            return Response(
                {
                    "status": False,
                    "message": str(err),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        agent = ajo_user.user
        get_repayment_wallet = WalletSystem.objects.filter(
            user=agent,
            onboarded_user=ajo_user,
            wallet_number__isnull=False,
            wallet_type=wallet_type,
        )

        if get_repayment_wallet.exists():
            return Response(
                {
                    "status": False,
                    "data": get_repayment_wallet.values(),
                    "message": "Wallet Details exist.",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        wema_acct_details = BankAccountDetails.objects.filter(
            ajo_user=ajo_user, account_provider=AccountProvider.WEMA
        )

        lite_user = ajo_user.lite_user
        if query_type == "url":
            phone_number = url_phone
            email = url_email
            bvn = url_bvn
        else:

            if wema_acct_details.exists():
                add_number = wema_acct_details.count()
                phone_number = f"{ajo_user.phone}-{add_number + 1}"
                email = f"{lite_user.email}-{add_number + 1}" if lite_user else ""
                bvn = f"{ajo_user.bvn}-{add_number + 1}" if ajo_user.bvn else ""
            else:
                phone_number = f"{ajo_user.phone}-1"
                email = f"{lite_user.email}-1" if lite_user else ""
                bvn = f"{ajo_user.bvn}-1" if ajo_user.bvn else ""

        acct_details_with_wallet = BankAccountDetails.objects.filter(
            ajo_user_id=ajo_user_id, form_type=wallet_type
        )

        if acct_details_with_wallet.exists():
            return Response(
                {
                    "status": False,
                    "data": acct_details_with_wallet.values(),
                    "message": "Wallet exist with account number.",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
        ajo_user_wallet = ajo_user_selector.get_any_ajo_user_wallet(
            wallet_type=wallet_type
        )

        request_payload = {
            "first_name": ajo_user.first_name,
            "last_name": ajo_user.last_name,
            "middle_name": ajo_user.alias,
            "email": email,
            "phone": phone_number,
            "bvn": bvn,
            "date_of_birth": str(ajo_user.dob),
        }

        try:
            create_virtual_repayment_wallet = (
                CoreBankingManager().create_multiple_account(**request_payload)
            )
        except ValueError as err:
            create_virtual_repayment_wallet = {"status_code": 500, "message": str(err)}

        status_code = create_virtual_repayment_wallet.get("status_code")

        if isinstance(create_virtual_repayment_wallet, dict):
            create_virtual_repayment_wallet["request_payload"] = request_payload

        if status_code in [200, 201]:
            data = create_virtual_repayment_wallet.get("data", {}).get(
                "account_details", {}
            )
            account_number = data.get("account_number")
            acct_name = f"{data.get('first_name', '')} {data.get('last_name', '')}"
            bank_code = data.get("bank_code")
            bank_name = data.get("bank_name")

            try:
                BankAccountDetails.objects.create(
                    user=agent,
                    ajo_user=ajo_user,
                    account_number=account_number,
                    account_name=acct_name,
                    bank_code=bank_code,
                    bank_name=bank_name,
                    account_provider=AccountProvider.WEMA,
                    account_type=wallet_type,
                    consent=True,
                    form_type=wallet_type,
                    payload=create_virtual_repayment_wallet,
                    initial_payload=request_payload,
                )

                ajo_user_wallet.wallet_number = account_number
                ajo_user_wallet.save()
                return Response(
                    {
                        "status": True,
                        "data": create_virtual_repayment_wallet,
                        "message": "success",
                    },
                    status=status.HTTP_200_OK,
                )

            except IntegrityError as err:
                FailuresLog.objects.create(
                    user=agent,
                    ajo_user=ajo_user,
                    dump=create_virtual_repayment_wallet,
                )
                return Response(
                    {
                        "status": False,
                        "data": acct_details_with_wallet.values(),
                        "message": str(err),
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            FailuresLog.objects.create(
                user=agent,
                ajo_user=ajo_user,
                dump=create_virtual_repayment_wallet,
            )
            return Response(
                {
                    "status": False,
                    "data": create_virtual_repayment_wallet,
                    "message": "Request failed",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
