from typing import Any, Dict

from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.db import IntegrityError
from django.utils import timezone
from django.utils.timezone import datetime
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, serializers, status
from rest_framework.generics import GenericAPIView
from rest_framework.pagination import NotFound
from rest_framework.response import Response

from accounts.agency_banking import AgencyBankingClass
from accounts.responses import (
    pagination_page_not_found_response,
    serializer_validation_error_response,
    value_error_response,
)
from payment.checks import is_masked_pan_for_user
from payment.model_choices import PaymentMethod
from savings.pagination import CustomPagination

from ..model_choices import RoscaGroupStatus, RoscaType, SavingsFrequency
from ..models import RotationGroup, RotationGroupMember
from ..selectors import (
    PersonalAjoSavingsSelector,
    PersonalSelector,
    RotationGroupMemberSelector,
    RotationGroupSelector,
)
from ..serializers.personal_ajo_serializers import (
    CheckRotationGroupSerializer,
    CreateRotationGroupSerializer,
    EditRotationGroupSerializer,
    FetchRotationGroupsSerializer,
    GenerateRotationGroupDetailsSerializer,
    JoinRotationGroupSerializer,
    LeaveRotationGroupSerializer,
    PersonalAjoSavingsFullDetailsSerializer,
    PersonalAjoSavingsInformationSerializer,
    PersonalTransactionHistorySerializer,
    PersonalUserChecksSerializer,
    RemoveRotationGroupMemberSerializer,
    RotationGroupGeneratedDetailsSerializer,
    RotationGroupInformationSerializer,
    RotationGroupMemberDueInformationSerializer,
    RotationGroupMemberInformationSerializer,
    SavingsSummarySerializer,
    StartRotationGroupSerializer,
)
from ..services import RotationGroupMemberService, RotationGroupService
from ..utils.general_utils import validate_date_format
from ..utils.personal_utils import PersonalAjoUtils


class GenerateRotationGroupDetailsAPIView(GenericAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = GenerateRotationGroupDetailsSerializer

    @swagger_auto_schema(responses={200: RotationGroupGeneratedDetailsSerializer})
    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        details = PersonalAjoUtils.generate_plan_information(**serializer.validated_data)
        generated_serializer = RotationGroupGeneratedDetailsSerializer(details)

        return Response(
            {
                "status": True,
                "data": generated_serializer.data,
            },
            status.HTTP_200_OK,
        )


class CreateGetEditDeleteRotationGroupAPIView(GenericAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_classes = {
        "POST": CreateRotationGroupSerializer,
        "GET": RotationGroupInformationSerializer,
        "PUT": EditRotationGroupSerializer,
        "DELETE": LeaveRotationGroupSerializer,
    }

    def get_serializer_class(self) -> serializers.Serializer:
        return self.serializer_classes.get(self.request.method)

    def post(self, request, *args, **kwargs):
        """
        Fill the information to create a rotation group
        """
        # obtain the agent
        user = request.user

        serializer = self.get_serializer_class()(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        validated_data = serializer.validated_data
        name = validated_data.get("name")
        target_amount = validated_data.get("target_amount")
        participants = validated_data.get("participants")
        frequency = validated_data.get("frequency")
        starting_date = validated_data.get("starting_date")
        auto_debit = validated_data.get("auto_debit")

        generated_data = self.clean_up_data_for_rotation_group(
            target_amount=target_amount,
            participants=participants,
            frequency=frequency,
            starting_date=starting_date,
            auto_debit=auto_debit,
            user=user,
            name=name,
        )

        try:
            group = RotationGroupService.create_rotation_group(data=generated_data)
            rotation_group_serializer = RotationGroupInformationSerializer(group)
        except ValueError or IntegrityError as err:
            return Response(
                {
                    "error": "603",
                    "status": False,
                    "message": str(err),
                },
                status.HTTP_400_BAD_REQUEST,
            )

        return Response(
            {
                "status": True,
                "message": "group created successfully",
                "data": rotation_group_serializer.data,
                # "data": {"group_id": group.group_id},
            },
            status=status.HTTP_201_CREATED,
        )

    @swagger_auto_schema(
        request_body=EditRotationGroupSerializer,
        manual_parameters=[
            openapi.Parameter(
                "id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=True,
                description="the 'id' of the rotation group",
            ),
        ],
    )
    def put(self, request, *args, **kwargs):
        """
        Edit a Rotation Group plan
        """
        user = request.user
        # obtain the id of the rotation group
        id = request.query_params.get("id")

        # obtain the Rotation Group
        rotation_group_selector = RotationGroupSelector(id=id, user=user)
        try:
            if rotation_group_selector.has_started():
                return Response(
                    {
                        "status": False,
                        "error": "400",
                        "message": "you cannot edit this group because it is running",
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )

            rotation_group = rotation_group_selector.get_rotation_group()

        except ValueError as err:
            return Response(
                {
                    "error": "404",
                    "status": False,
                    "message": str(err),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # validate the data
        serializer = self.get_serializer_class()(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        validated_data = serializer.validated_data
        name = rotation_group.name
        target_amount = validated_data.get("target_amount")
        participants = validated_data.get("participants")
        frequency = validated_data.get("frequency")
        starting_date = validated_data.get("starting_date")
        auto_debit = validated_data.get("auto_debit")

        generated_data = self.clean_up_data_for_rotation_group(
            target_amount=target_amount,
            participants=participants,
            frequency=frequency,
            starting_date=starting_date,
            auto_debit=auto_debit,
            user=user,
            name=name,
        )

        # save the changes
        try:
            updated_rotation_group = RotationGroupService.update_rotation_group(
                rotation_group_instance=rotation_group,
                data=generated_data,
            )
        except ValueError as err:
            return value_error_response(error=err, code="932")

        info_serializer = RotationGroupInformationSerializer(updated_rotation_group)

        return Response(
            {
                "status": True,
                "data": info_serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=True,
                description="the 'id' of the rotation group",
            ),
        ],
        responses={
            200: "successful",
            400: "errors",
        },
    )
    def delete(self, request, *args, **kwargs):
        """
        Delete a rotation group and its members
        """
        user = request.user
        # obtain the group ID
        id = request.query_params.get("id")

        rotation_group_selector = RotationGroupSelector(id=id, user=user)

        # obtain the rotation group
        try:
            if rotation_group_selector.has_started():
                return Response(
                    {
                        "status": False,
                        "error": "403",
                        "message": "you can not delete this group because it is running",
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )

            rotation_group = rotation_group_selector.get_rotation_group()

            RotationGroupService.delete_rotation_group(rotation_group=rotation_group)

        except ValueError as err:
            return value_error_response(error=err, code="555")

        return Response(
            {
                "status": True,
                "message": "group deleted succesfully",
            },
            status.HTTP_200_OK,
        )

    def clean_up_data_for_rotation_group(
        self,
        target_amount: float,
        participants: int,
        frequency: SavingsFrequency,
        starting_date: datetime,
        auto_debit: bool,
        user: AbstractUser,
        name: str,
    ) -> Dict[str, Any]:
        generated_data = PersonalAjoUtils.generate_plan_information(
            target=target_amount,
            number_of_participants=participants,
            frequency=frequency,
            starting_date=starting_date,
        )

        generated_data["admin_fee"] = generated_data.get("agent_fee")
        generated_data["user"] = user
        generated_data["rosca_type"] = RoscaType.PERSONAL_ROSCA
        generated_data["name"] = name
        generated_data["auto_debit"] = auto_debit

        keys_to_remove = [
            "agent_fee",
            "company_fee",
            "converted_duration",
            "collection_fee",
        ]
        [generated_data.pop(key, None) for key in keys_to_remove]

        return generated_data


class JoinLeaveRotationGroupAPIView(GenericAPIView):
    """
    GET: get the information about the group
    PUT: join the group
    DELETE: Leave the group
    """

    permission_classes = (permissions.IsAuthenticated,)
    serializer_classes = {
        "GET": RotationGroupInformationSerializer,
        "POST": JoinRotationGroupSerializer,
        "DELETE": LeaveRotationGroupSerializer,
    }

    def get_serializer_class(self) -> serializers.Serializer:
        return self.serializer_classes.get(self.request.method)

    @swagger_auto_schema(
        responses={
            200: RotationGroupInformationSerializer,
            404: "group not found",
        },
        manual_parameters=[
            openapi.Parameter(
                name="group_id",
                in_=openapi.IN_QUERY,
                required=True,
                description="the group id of the rotation group",
                type=openapi.TYPE_STRING,
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        group_id = request.query_params.get("group_id")
        if not group_id:
            return Response(
                {
                    "status": False,
                    "error": "404",
                    "message": "group_id must be in query parameter",
                },
                status.HTTP_400_BAD_REQUEST,
            )

        try:
            rosca_selector = RotationGroupSelector(group_id=group_id)
            try:
                group = rosca_selector.get_rotation_group()
                group_info = rosca_selector.get_group_information()
                group_info["is_admin"] = request.user == group.user
                group_info["current_frequency"] = rosca_selector.get_current_frequency()
                group_info["withdrawal_count"] = rosca_selector.get_withdrawal_count()
                group_info["contribution_count"] = rosca_selector.get_contribution_count()
            except ValueError as err:
                return value_error_response(error=err, code="404")

            # if group_info.get("group_filled"):
            #     return Response(
            #         {
            #             "error": "622",
            #             "status": False,
            #             "message": "this group is completed",
            #         },
            #         status=status.HTTP_403_FORBIDDEN,
            #     )

            return Response(
                {
                    "status": True,
                    "data": group_info,
                },
                status=status.HTTP_200_OK,
            )
        except RotationGroup.DoesNotExist:
            return Response(
                {
                    "error": "404",
                    "status": False,
                    "message": f"group with group_id:{group_id} was not found",
                },
                status=status.HTTP_404_NOT_FOUND,
            )

    def post(self, request, *args, **kwargs):
        """
        Add a user to a rotation group using the group ID and the position the user is meant to occupy.

        Only add a "payment_method" key to the request if the group has auto_debit to on.
        """
        serializer: serializers.Serializer = self.get_serializer_class()(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        # obtain the user
        user = request.user
        payment_method = serializer.validated_data.get("payment_method", None)
        masked_pan = serializer.validated_data.get("masked_pan", None)
        auth_code = None

        if payment_method == PaymentMethod.DEBIT_CARD:
            access_token = request.headers.get("Authorization", "").partition(" ")[-1]

            card_data = is_masked_pan_for_user(
                access_token=access_token,
                masked_pan=masked_pan,
            )

            if not card_data.get("status"):
                return Response(
                    {
                        "status": False,
                        "error": "561",
                        "message": "this masked pan can not be found for this user",
                    },
                    status.HTTP_400_BAD_REQUEST,
                )

            auth_code = card_data.get("authorization_code")

        try:
            RotationGroupMemberService.join_group(
                user=user,
                group_id=serializer.validated_data.get("group_id"),
                position=serializer.validated_data.get("position"),
                payment_method=serializer.validated_data.get("payment_method", None),
                masked_pan=masked_pan,
                auth_code=auth_code,
            )

        except ValueError as err:
            return Response(
                {
                    "error": "603",
                    "status": False,
                    "message": str(err),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        group_selector = RotationGroupSelector(group_id=serializer.validated_data.get("group_id"))

        if group_selector.can_group_start():
            # update the rotation group and members information
            RotationGroupService.update_rotation_group_from_starting_date(
                rotation_group=group_selector.get_rotation_group(),
                starting_date=timezone.localdate(),
            )

            # start rotation group
            # UPDATE: no starting of groups automatically
            # RotationGroupService.start_rotation_group(rotation_group=group_selector.get_rotation_group())

        return Response(
            {
                "status": True,
                "message": "member added successfully",
            },
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        request_body=LeaveRotationGroupSerializer,
        responses={
            200: "successful",
            404: "group not found or not a member of the group",
        },
    )
    def delete(self, request, *args, **kwargs):
        """
        A user can leave a rotation group by inserting the group ID,
        a user can leave only if the group's starting date has not passed
        """
        serializer = self.get_serializer_class()(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        # obtain the group ID
        group_id = serializer.validated_data.get("group_id")

        try:
            message = RotationGroupMemberService.remove_member(
                group_id=group_id,
                user=request.user,
            )

            return Response(
                {
                    "status": True,
                    "message": message,
                },
                status=status.HTTP_200_OK,
            )
        except ValueError as err:
            return Response(
                {
                    "error": "607",
                    "status": False,
                    "message": str(err),
                },
                status=status.HTTP_403_FORBIDDEN,
            )


class FetchAjoSavingsAPIView(GenericAPIView):
    serializer_class = {
        "GET": PersonalAjoSavingsInformationSerializer,
    }
    permission_classes = (permissions.IsAuthenticated,)

    def get_serializer_class(self) -> serializers.Serializer:
        return self.serializer_class.get(self.request.method)

    def get(self, request, *args, **kwargs):
        """
        Get the ajo savings of the user
        """
        user = request.user

        ajo_savings = PersonalSelector(user=user).get_user_ajo_savings()

        serializer = self.get_serializer_class()(ajo_savings, many=True)

        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status.HTTP_200_OK,
        )


class SavingsSummaryAPIView(GenericAPIView):
    serializer_class = SavingsSummarySerializer
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request, *args, **kwargs):
        """
        The summary containing the balance, number of active savings,
        wallet ID and the rotation wallet balance.
        """
        user = request.user

        personal_selector = PersonalSelector(user=user)

        data = {
            "balance": personal_selector.get_ajo_user_wallet_balance(),
            "active_savings": personal_selector.get_number_of_active_ajo_savings(),
            "wallet_number": personal_selector.generate_and_set_wallet_number(),
            "rotation_balance": RotationGroupMemberSelector.get_rotation_group_wallet(user=user).available_balance,
        }

        serializer = self.serializer_class(data)

        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status.HTTP_200_OK,
        )


class TransactionHistoryAPIView(GenericAPIView):
    serializer_class = PersonalTransactionHistorySerializer
    permission_classes = (permissions.IsAuthenticated,)
    pagination_class = CustomPagination

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                name="month",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="insert month in the format 'YYYY-MM' e.g. '2023-10'",
            )
        ]
    )
    def get(self, request, *args, **kwargs):
        """
        Retrieves the Ajo transaction history of a user
        """
        user = request.user
        month: str | None = request.query_params.get("month", None)

        if month:
            if not validate_date_format(
                date_string=month,
                month_year_only=True,
            ):
                return Response(
                    {
                        "status": False,
                        "error": "422",
                        "message": "Invalid date string passed. Please check your month string",
                    },
                    status.HTTP_422_UNPROCESSABLE_ENTITY,
                )

        personal_selector = PersonalSelector(user=user)

        transactions = personal_selector.get_personal_user_transaction_history(month=month)

        try:
            paginated_transactions = self.paginate_queryset(transactions)
        except NotFound:
            return pagination_page_not_found_response()

        serializer = self.serializer_class(paginated_transactions, many=True)

        return self.get_paginated_response(serializer.data)


class FetchRotationGroupsAPIView(GenericAPIView):
    serializer_class = FetchRotationGroupsSerializer
    permission_classes = (permissions.IsAuthenticated,)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                name="status",
                in_=openapi.IN_QUERY,
                description="the status of the rotation group: PENDING, RUNNING, CLOSED.",
                required=False,
                type=openapi.TYPE_STRING,
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        """
        Get all the rotation groups a user is a member of and has created.
        Look out for the is_admin field.
        """
        user = request.user

        status_param = request.query_params.get("status", None)
        if status_param:
            if status_param.upper() not in RoscaGroupStatus:
                return Response(
                    {
                        "status": False,
                        "message": f"the status query is not a valid type, pick from: {list(RoscaGroupStatus.__members__.keys())}",
                    },
                    status.HTTP_400_BAD_REQUEST,
                )
            else:
                status_param = getattr(RoscaGroupStatus, status_param.upper())

        # get rotation groups user created
        user_rotation_groups = RotationGroupSelector.get_user_rotation_groups(user=user)

        # get rotation groups user is a member of
        member_rotation_groups = RotationGroupMemberSelector.get_all_rotation_groups_as_member(user=user)

        combined_groups = user_rotation_groups | member_rotation_groups

        combined_groups = combined_groups.distinct()

        combined_groups = combined_groups.order_by("-created_at")

        if status_param:
            combined_groups = combined_groups.filter(status=status_param)

        serializer = self.serializer_class(
            combined_groups,
            many=True,
            context={"request": self.request},
        )

        return Response(
            {
                "status": True,
                "data": serializer.data,
            }
        )


class GroupMembersAPIView(GenericAPIView):
    serializer_classes = {
        "GET": RotationGroupMemberInformationSerializer,
        "DELETE": RemoveRotationGroupMemberSerializer,
    }
    permission_classes = (permissions.IsAuthenticated,)

    def get_serializer_class(self) -> serializers.Serializer:
        return self.serializer_classes.get(self.request.method)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                name="group_id",
                in_=openapi.IN_QUERY,
                description="the Group ID of the rotation group",
                required=True,
                type=openapi.TYPE_STRING,
            )
        ]
    )
    def get(self, request, *args, **kwargs):
        """
        Get the list of all the members of a group
        """
        group_id = request.query_params.get("group_id")

        rotation_group_selector = RotationGroupSelector(group_id=group_id)

        try:
            members = rotation_group_selector.get_group_members()

        except ValueError as err:
            return value_error_response(error=err)

        serializer = self.get_serializer_class()(members, many=True)

        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        request_body=RemoveRotationGroupMemberSerializer,
    )
    def delete(self, request, *args, **kwargs):
        """
        this endpoint is for the admin of the group to remove a member from the group.
        """
        user = request.user

        serializer = self.get_serializer_class()(data=request.data)

        try:
            serializer.is_valid()
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        rotation_group_selector = RotationGroupSelector(
            id=serializer.validated_data.get("id"),
            user=user,
        )

        try:
            rotation_group = rotation_group_selector.get_rotation_group()

            if rotation_group.user != user:
                return Response(
                    {
                        "status": False,
                        "message": "this user is not the admin of the group",
                    },
                    status.HTTP_400_BAD_REQUEST,
                )

            member = RotationGroupMemberSelector.position_in_group(
                group=rotation_group,
                position=serializer.validated_data.get("position"),
            )

            if not member:
                return Response(
                    {
                        "status": False,
                        "message": "this member does not exist",
                    },
                    status.HTTP_400_BAD_REQUEST,
                )

            RotationGroupMemberService.remove_member(
                group_id=rotation_group.group_id,
                user=member.user,
            )
        except ValueError as err:
            return value_error_response(error=err)

        return Response(
            {
                "status": True,
                "message": f"member, {member.user.email}, removed from the group successfully",
            },
            status.HTTP_200_OK,
        )


class RotationGroupTransactionHistoryAPIView(GenericAPIView):
    serializer_class = {
        "GET": PersonalTransactionHistorySerializer,
    }
    permission_classes = (permissions.IsAuthenticated,)
    pagination_class = CustomPagination

    def get_serializer_class(self) -> serializers.Serializer:
        return self.serializer_class.get(self.request.method)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                name="group_id",
                in_=openapi.IN_QUERY,
                description="the Group ID of the rotation group",
                required=True,
                type=openapi.TYPE_STRING,
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        "Get the transaction history of a group"

        group_id = request.query_params.get("group_id")

        if not group_id:
            return Response(
                {
                    "status": False,
                    "error": "404",
                    "message": "pass 'group_id' as query paramaters",
                },
                status.HTTP_422_UNPROCESSABLE_ENTITY,
            )

        rotation_group_selector = RotationGroupSelector(group_id=group_id)

        try:
            transactions = rotation_group_selector.get_transaction_history()
            paginated_transactions = self.paginate_queryset(queryset=transactions)

        except ValueError as err:
            return value_error_response(error=err)

        except NotFound:
            return pagination_page_not_found_response()

        serializer = self.get_serializer_class()(paginated_transactions, many=True)

        return self.get_paginated_response(serializer.data)


class RotationGroupMemberDueInfoAPIView(GenericAPIView):
    serializer_classes = {
        "GET": RotationGroupMemberDueInformationSerializer,
    }
    permission_classes = (permissions.IsAuthenticated,)

    def get_serializer_class(self) -> serializers.Serializer:
        return self.serializer_classes.get(self.request.method)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                name="group_id",
                in_=openapi.IN_QUERY,
                description="the Group ID of the rotation group",
                required=True,
                type=openapi.TYPE_STRING,
            ),
            openapi.Parameter(
                name="position",
                in_=openapi.IN_QUERY,
                description="the position of the member of the group",
                required=True,
                type=openapi.TYPE_INTEGER,
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        """
        Get the information of a member of a group
        """
        group_id = request.query_params.get("group_id")
        position = request.query_params.get("position")

        if not group_id or not position:
            return Response(
                {
                    "status": False,
                    "error": "404",
                    "message": "pass 'group_id' and 'position' as query paramters",
                },
                status.HTTP_422_UNPROCESSABLE_ENTITY,
            )

        position = int(position)

        rotation_group_selector = RotationGroupSelector(group_id=group_id)

        try:
            group = rotation_group_selector.get_rotation_group()
            member = RotationGroupMemberSelector.position_in_group(
                group=group,
                position=position,
            )

            if not member:
                return Response(
                    {
                        "status": False,
                        "error": "404",
                        "message": "this member was not found",
                    },
                    status.HTTP_400_BAD_REQUEST,
                )

            information = self.member_information(member=member)

            serializer = self.get_serializer_class()
            serializer = serializer(information)

            return Response(
                {
                    "status": True,
                    "data": serializer.data,
                },
                status.HTTP_200_OK,
            )

        except ValueError as err:
            return value_error_response(error=err)

    def member_information(self, member: RotationGroupMember) -> Dict[str, Any]:
        group = member.group
        unpaid_positions = RotationGroupMemberSelector.get_unpaid_positions_below_next_payment(
            group=group,
            member=member,
        )

        position_status = RotationGroupMemberSelector.get_position_payment_status(member=member)

        total_unpaid_positions = len(unpaid_positions)

        response = {
            "member": member,
            "position_payment_status": position_status,
            "total_unpaid_positions": total_unpaid_positions,
        }

        return response


class PersonalAjoSavingsDetailAPIView(GenericAPIView):
    serializer_class = PersonalAjoSavingsFullDetailsSerializer
    permission_classes = (permissions.IsAuthenticated,)
    pagination_class = CustomPagination

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=True,
                description="the id of the ajo savings plan",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """
        This endpoint is for getting the details of a personal ajo plan
        as well as it's transaction history
        """

        id = request.query_params.get("id")
        user = request.user

        personal_ajo_selector = PersonalAjoSavingsSelector(id=id, user=user)

        try:
            ajo_savings = personal_ajo_selector.get_ajo_savings_plan()
            paginated_transactions = self.paginate_queryset(
                personal_ajo_selector.get_ajo_savings_transaction_history()
            )

        except ValueError as err:
            return value_error_response(error=err)

        except NotFound:
            return pagination_page_not_found_response()

        serializer = PersonalAjoSavingsInformationSerializer(ajo_savings)
        transactions_serializer = PersonalTransactionHistorySerializer(paginated_transactions, many=True)

        response_data = serializer.data
        response_data["transactions"] = transactions_serializer.data

        return self.get_paginated_response(response_data)


class StartRotationGroupAPIView(GenericAPIView):
    serializer_class = StartRotationGroupSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        id = serializer.validated_data.get("id")
        user = request.user

        group_selector = RotationGroupSelector(
            id=id,
            user=user,
        )

        try:
            group = group_selector.get_rotation_group()
        except ValueError as err:
            return value_error_response(error=err)

        if group.status != RoscaGroupStatus.PENDING:
            return Response(
                {
                    "status": False,
                    "error": "403",
                    "message": "this group cannot be started as it is already running or closed",
                },
                status.HTTP_403_FORBIDDEN,
            )

        if group_selector.get_group_members().count() != group.number_of_participants:
            return Response(
                {
                    "status": False,
                    "error": "603",
                    "message": "this group cannot start yet because it is not filled up",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        RotationGroupService.update_rotation_group_from_starting_date(
            rotation_group=group,
            starting_date=timezone.localdate(),
        )

        RotationGroupService.start_rotation_group(rotation_group=group)

        return Response(
            {
                "status": True,
                "message": "this rotation group has begun",
            },
            status.HTTP_200_OK,
        )


class PersonalUserChecksAPIView(GenericAPIView):
    serializer_class = PersonalUserChecksSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request, *args, **kwargs):
        """
        This checks if the user has done things like:
            1. KYC up to level 3
            2. Have an active debit card
        """
        access_token = request.headers.get("Authorization", "").partition(" ")[-1]

        # check the KYC level
        user_info = AgencyBankingClass.get_all_user_information(access_token=access_token)
        card_data = AgencyBankingClass.get_user_cards(access_token=access_token)

        if not (user_info.get("status") or card_data.get("status")):
            return Response(
                {
                    "status": False,
                    "error": "768",
                    "message": "error encountered while trying to retrieve data, try again.",
                },
                status.HTTP_400_BAD_REQUEST,
            )

        user_data = user_info.get("user_data")

        kyc_level = user_data.get("kyc_level")
        kyc_message = None
        if kyc_level <= 2:
            kyc_message = f"complete your level {kyc_level + 1} KYC verification"

        active_card = card_data.get("message") == "Cards Retrieved"
        active_card_message = None

        if not active_card:
            active_card_message = "perform a transaction on agency banking with a working debit card"

        data = {
            "active_card": active_card,
            "active_card_message": active_card_message,
            "kyc_level": kyc_level,
            "kyc_message": kyc_message,
        }

        serializer = self.serializer_class(data)

        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status.HTTP_200_OK,
        )
