from django.core.cache import cache
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics, permissions, serializers, status
from rest_framework.exceptions import NotFound
from rest_framework.response import Response

from accounts.responses import (
    pagination_page_not_found_response,
    serializer_validation_error_response,
    value_error_response,
)
from savings.pagination import CustomPagination

from ..loans.loan_actions import LoanActions
from ..models import AjoUser
from ..payment_actions import create_loan_transaction, move_loan_to_agent_wallet
from ..selectors import AjoUserSelector, LoanSelector
from ..serializers.loan_serializers import (
    CreateLoanPlanSerializer,
    GetLoanRequestDetails,
    ListOfEligibleAjoUsersLoansSerializer,
    LoanBalanceSerializer,
    LoanEligibiltySerializer,
    LoanSerializer,
    LoanWithdrawSerializer,
    RequestForLoanSerializer,
)
from ..services import LoanService
from ..utils.otp_utils import send_out_sms_otp
from ..utils.ussd_utils import generate_full_ussd_code


class LoanEligibilityAPIView(generics.GenericAPIView):
    serializer_classes = {
        "POST": LoanEligibiltySerializer,
        "GET": ListOfEligibleAjoUsersLoansSerializer,
    }
    permission_classes = (permissions.IsAuthenticated,)

    def get_serializer_class(self):
        return self.serializer_classes.get(self.request.method)

    def post(self, request, *args, **kwargs):
        """
        Get the loan eligibilty of a particular ajo user
        """
        # obtain the user
        user = request.user

        serializer = self.get_serializer_class()(data=request.data)

        # attempt to validate the data
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        # obtain the phone number
        phone_number = serializer.validated_data.get("phone_number")

        # call the ajo user selector class
        ajo_user_selector = AjoUserSelector(phone_number=phone_number, user=user)

        # obtain the ajo user
        try:
            ajo_user = ajo_user_selector.get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        # call the LoanActions class
        eligible_amount = LoanActions(user=user).get_the_eligibility_of_an_ajo_user(ajo_user=ajo_user)

        return Response(
            {
                "status": True,
                "data": {
                    "eligible_loan_amount": eligible_amount,
                },
            },
            status=status.HTTP_200_OK,
        )

    def get(self, request, *args, **kwargs):
        """
        Get the list all the eligible loans and
        the ajo user
        """
        # obtain the user
        user = request.user

        # get the list of data
        loan_data = LoanActions(user=user).get_all_cached_data_for_user()

        serializer = self.get_serializer_class()(loan_data, many=True)

        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )


class RequestLoanAPIView(generics.GenericAPIView):
    serializer_class = RequestForLoanSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        """
        Request for a loan from your eligible amount
        and get an OTP or USSD code to dial
        """
        # obtain the user
        user = request.user

        serializer = self.serializer_class(data=request.data)

        # try to validate the data
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        # obtain the different fields
        phone_number = serializer.validated_data.get("phone_number")
        loan_amount = serializer.validated_data.get("loan_amount")

        # call the ajo user selector and get the ajo user
        try:
            ajo_user = AjoUserSelector(phone_number=phone_number, user=user).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        # call the LoanActions class
        loan_actions = LoanActions(user=user)

        # obtain the eligible amount for the ajo user
        eligible_amount = loan_actions.get_the_eligibility_of_an_ajo_user(ajo_user=ajo_user)

        # check if the user is eligible to any loan
        if eligible_amount <= 0:
            return Response(
                {
                    "status": False,
                    "error": "901",
                    "message": "you are ineligible at the moment, keep saving and try again later",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # check if the person asked for more than he/she is eligible to
        if loan_amount > eligible_amount:
            return Response(
                {
                    "status": False,
                    "error": "902",
                    "message": "you are not eligible to this amount of loan",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # make the request persist
        loan_actions.request_loan(ajo_user=ajo_user, loan_amount=loan_amount)

        # call the OTP endpoint
        send_out_sms_otp(phone_number=phone_number)
        return Response(
            data={
                "status": True,
                "message": "OTP has been sent to the mobile number provided",
                "ussd_alternative": f"Dial {generate_full_ussd_code(user_id=user.id)} to generate an OTP",
            }
        )


class CreateGetLoanDetailsAPIView(generics.GenericAPIView):
    serializer_classes = {"POST": CreateLoanPlanSerializer, "GET": GetLoanRequestDetails}
    permission_classes = (permissions.IsAuthenticated,)

    def get_serializer_class(self):
        return self.serializer_classes.get(self.request.method)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """
        Get the information about an the loan request
        from the loan request resource.
        """
        # obtain the user
        user = request.user
        # obtain the phone number from the query params
        phone_number = request.query_params.get("phone_number")

        # obtain the ajo user
        try:
            ajo_user = AjoUserSelector(phone_number=phone_number, user=user).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        # call the loan actions class
        loan_actions = LoanActions(user=user)

        # use the method to get the cached loan request for the ajo user
        loan_request = loan_actions.get_cached_loan_request(ajo_user=ajo_user)

        if not loan_request:
            return Response(
                {
                    "status": False,
                    "error": "404",
                    "message": "could not find the loan request, request for a loan again",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.get_serializer_class()(loan_request)

        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    def post(self, request, *args, **kwargs):
        """
        This creates the loan plan with the details from the GET request
        for this resource.
        """
        # obtain the user
        user = request.user

        serializer = self.get_serializer_class()(data=request.data)

        # try to validate the data
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        # obtain the fields from the validated data
        phone_number = serializer.validated_data.get("phone_number")

        # obtain the ajo user
        try:
            ajo_user = AjoUserSelector(phone_number=phone_number, user=user).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        # obtain the information from the cache
        loan_info = LoanActions(user=user).get_cached_loan_request(ajo_user=ajo_user)

        if not loan_info:
            return Response(
                {
                    "status": False,
                    "error": "404",
                    "message": "could not find the loan request, request for a loan again",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # define the data for the creation of the plan
        data = {
            "user": user,
            "ajo_user": ajo_user,
            "loaned_amount": loan_info.get("loan_amount"),
            "interest": loan_info.get("loan_interest_rate"),
            "repayment_amount": loan_info.get("loan_repayment_amount"),
            "frequency": loan_info.get("repayment_frequency"),
            "periodic_amount": loan_info.get("periodic_amount"),
        }

        # call the LoanService
        try:
            loan = LoanService.create_loan_plan(data=data)
        except ValueError as err:
            return value_error_response(error=err)

        # call the selector class to obtain the loan wallet
        ajo_user_loan_wallet = LoanSelector(ajo_user=ajo_user).get_ajo_user_loan_wallet()

        # fund the wallet with money
        # create loan transaction
        loan_transaction = create_loan_transaction(
            user=user,
            ajo_user=ajo_user,
            quotation_id=loan.quotation_id,
            amount=data.get("loaned_amount"),
        )
        LoanService.place_loan_in_wallet(
            loan_wallet=ajo_user_loan_wallet,
            amount=data.get("loaned_amount"),
            transaction=loan_transaction,
        )

        # serialize the loan model
        loan_serializer = LoanSerializer(loan)

        # return a Response
        return Response(
            {
                "status": True,
                "data": loan_serializer.data,
            },
            status=status.HTTP_201_CREATED,
        )


class LoanHistoryAPIView(generics.GenericAPIView):
    serializer_class = LoanSerializer
    permission_classes = (permissions.IsAuthenticated,)
    pagination_class = CustomPagination

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        # obtain the user
        user = request.user

        # obtain the phone number
        phone_number = request.query_params.get("phone_number")

        # call the ajo selector to obtain the ajo user
        try:
            ajo_user = AjoUserSelector(phone_number=phone_number, user=user).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        # call the loan selector class
        loan_selector = LoanSelector(ajo_user=ajo_user)

        # obtain the qs of loans belonging to the ajo user
        loan_qs = loan_selector.get_list_of_loans(descending_order=True)
        try:
            paginated_loans = self.paginate_queryset(loan_qs)
        except NotFound:
            return pagination_page_not_found_response()

        serializer = self.serializer_class(paginated_loans, many=True)

        return self.get_paginated_response(serializer.data)


class LoanWalletBalanceWithdrawAPIView(generics.GenericAPIView):
    """
    This Resource is for viewing the balance and withdrawing the balance
    to the Agent's wallet
    """

    serializer_classes = {"POST": LoanWithdrawSerializer, "GET": LoanBalanceSerializer}
    permission_classes = (permissions.IsAuthenticated,)

    def get_serializer_class(self):
        return self.serializer_classes.get(self.request.method)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """
        View the loan balance
        """
        # obtain the user
        user = request.user

        # obtain the phone number
        phone_number = request.query_params.get("phone_number")

        # instantiate ajo user selector class
        try:
            ajo_user = AjoUserSelector(phone_number=phone_number, user=user).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        # instantiate the loan selector class
        ajo_user_wallet = LoanSelector(ajo_user=ajo_user).get_ajo_user_loan_wallet()

        serializer = self.get_serializer_class()(ajo_user_wallet)

        return Response(
            {
                "status": True,
                "data": serializer.data,
            }
        )

    def post(self, request, *args, **kwargs):
        """
        Withdraw from the loan balance to the Agent's wallet
        """
        # obtain the user
        user = request.user

        serializer = self.get_serializer_class()(data=request.data)

        # validate the data
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        # obtain the amount and phone number
        amount = serializer.validated_data.get("amount")
        phone_number = serializer.validated_data.get("phone_number")

        # call the ajo user selector class
        try:
            ajo_user = AjoUserSelector(phone_number=phone_number, user=user).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        # call the loan selector class
        loan_wallet = LoanSelector(ajo_user=ajo_user).get_ajo_user_loan_wallet()

        # checks
        if amount > loan_wallet.available_balance:
            return Response(
                {"status": False, "error": "671", "message": "insufficient funds to transfer"},
                status=status.HTTP_403_FORBIDDEN,
            )

        # call the function to move the money
        try:
            move_loan_to_agent_wallet(
                user=user,
                amount=amount,
                ajo_user=ajo_user,
                loan_wallet=loan_wallet,
            )
        except Exception as err:
            return Response(
                {
                    "error": "792",
                    "status": False,
                    "message": "something went wrong during payment, contact customer care",
                    "error_message": str(err),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response(
            {
                "status": True,
                "message": "loan amount moved to agent wallet successfully",
            },
            status=status.HTTP_200_OK,
        )
