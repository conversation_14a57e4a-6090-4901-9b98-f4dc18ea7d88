import json
from datetime import datetime, timedelta
from typing import Any, Dict

from django.contrib.auth import get_user_model
from django.db.models import Sum
from django.http import HttpResponse
from django.utils import timezone
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from accounts.agency_banking import AgencyBankingClass
from accounts.models import ConstantTable, USSDLog
from chestlock.selectors import OnlendingSelector
from loans.enums import RepaymentType
from loans.helpers.core_banking import CoreBankingManager
from loans.models import AjoLoanRepayment, RepaymentVirtuaWalletCreditRecord
from loans.serializers import RepaymentHookSerializer
from loans.tasks import celery_handle_loandisk_loan_repayment
from payment.model_choices import (
    DisbursementProviderType,
    Status,
    TransactionFormType,
    TransactionSource,
    WalletTypes,
)
from payment.models import PlanType, Transaction, WalletSystem
from payment.services import TransactionService

from ..model_choices import AccountFormType, AccountProvider
from ..models import AjoSaving, BankAccountDetails, ProfileChangeRequest, RawFundingData
from ..payment_actions import (
    fund_wallet_and_update_transaction,
    resolve_pending_funding_agent_from_liberty_transaction,
)
from ..permissions import AjoUSSDWebhook, CoreRepaymentWebhook, VFDWebhookWhitelist
from ..selectors import AjoAgentSelector, AjoUserSelector, BankAccountSelector
from ..services import ProfileChangeService
from ..utils.general_utils import format_phone_number
from ..utils.otp_utils import convert_num_to_currency
from ..utils.ussd_utils import (
    check_cashout_ussd_pattern,
    generate_ussd_cashout_otp,
    generate_ussd_otp,
    obtain_data_from_cashout_request,
)

# from ..utils.ussd_utils import check_verification_ussd_pattern, verify_ussd_otp


class USSDCallbackAPIView(APIView):
    permission_classes = (AjoUSSDWebhook,)
    # define some reusable strings
    failure_response = "END your operation could not be processed"

    def post(self, request, *args, **kwargs):
        """
        This is the callback URL for the USSD application of Ajo
        """
        # obtain the data
        data = request.data

        # get the several fields
        session_id: str = data.get("sessionId", None)
        service_code: str = data.get("serviceCode", None)
        phone_number: str = data.get("phoneNumber", None)
        text: str = data.get("text", "")

        # format the phone number
        phone_number = format_phone_number(phone_number=phone_number)

        ussd_dial = USSDLog.objects.create(
            phone_number=phone_number,
            service_code=service_code,
            text=text,
            session_id=session_id,
        )

        # if only the service code was dialed
        if text == "":
            response = (
                "CON what do you want to do \n"
                + "20. Get Ajo Balance\n"
                + "21. Generate OTP for Loan request\n"
                + "22. Generate an OTP to give to the agent\n"
                + "23. Cashout from Ajo Plan"
            )
            return HttpResponse(response)

        # elif text == "22":
        #     return HttpResponse("CON what is the number after *22* on the agent's screen")
        elif text == "20":
            # from django.db.models import Sum, F
            # from django.utils import timezone

            # now = timezone.now()
            # today = now.date()
            # const = ConstantTable.get_constant_table_instance()
            # # time_interval = const.ussd_rate_limit_no_hours
            # # time_interval_difference = now - timezone.timedelta(hours=time_interval)
            # time_interval = 5
            # time_interval_difference = now - timedelta(seconds=time_interval)

            # all_phone_dials = USSDLog.objects.exclude(id=ussd_dial.id).filter(
            #     phone_number=phone_number, created_at__date=today,
            #     created_at__gte=time_interval_difference, created_at__lte=now
            # ).order_by('-created_at').values_list('created_at', flat=True)

            # if all_phone_dials:
            #     last_dial_time = all_phone_dials[0]
            #     next_dial_hours = time_interval - (now - last_dial_time).total_seconds() / 3600
            #     response_data = f"You have exceeded the maximum number of CHECK BALANCES. Please try again in {int(round(next_dial_hours, 1))} hours."
            # else:
            #     spend_wallet_balance = WalletSystem.objects.filter(
            #         onboarded_user__phone_number=phone_number,
            #         wallet_type=WalletTypes.AJO_SPENDING
            #     ).aggregate(total=Sum('available_balance'))['total'] or 0

            #     if spend_wallet_balance:
            #         spend_data = f"Spend Wallet Bal: N{convert_num_to_currency(spend_wallet_balance)}"
            #     else:
            #         spend_data = None

            #     ajo_saver_savings = AjoSaving.objects.filter(
            #         ajo_user__phone_number=phone_number,
            #         is_active=True, is_activated=True,
            #         ajo_user__isnull=False, withdrawn=False,
            #         amount_saved__gt=0,
            #     ).annotate(total_saved=F('amount_saved')).values('total_saved')

            #     if ajo_saver_savings:
            #         total_bal = sum(saving['total_saved'] for saving in ajo_saver_savings)
            #         savings_data = '\n'.join(f"Savings {i}. N{convert_num_to_currency(saving['total_saved'])}" for i, saving in enumerate(ajo_saver_savings, start=1))
            #         response_data = f"{savings_data}\nTotal Bal: N{convert_num_to_currency(total_bal)}"
            #     else:
            #         response_data = "No Active Savings Found"

            #     if spend_data:
            #         response_data += f"\n{spend_data}"

            # return HttpResponse(f"END {response_data}")

            now = datetime.now()
            today = now.date()
            const = ConstantTable.get_constant_table_instance()
            time_interval = const.ussd_rate_limit_no_hours
            time_interval_difference = now - timedelta(hours=time_interval)
            # time_interval = 5
            # time_interval_difference = now - timedelta(seconds=time_interval)

            all_phone_dials = USSDLog.objects.exclude(id=ussd_dial.id).filter(
                phone_number=phone_number,
                created_at__date=today,
                created_at__gte=time_interval_difference,
                created_at__lte=now,
            )

            get_last_dial = all_phone_dials.last()
            if get_last_dial:

                next_dial = (
                    time_interval
                    - (timezone.now() - get_last_dial.created_at).total_seconds() / 3600
                )

                response_data = f"You have exceeded the maximum number of CHECK BALANCES. Please try again in {int(round(next_dial, 1))} hours."

            else:

                get_spend_wallet = WalletSystem.objects.filter(
                    onboarded_user__phone_number__startswith=phone_number,
                    wallet_type=WalletTypes.AJO_SPENDING,
                )
                if get_spend_wallet:
                    spend_data = response_data = (
                        f"Spend Wallet Bal: N{convert_num_to_currency(get_spend_wallet.aggregate(total=Sum('available_balance'))['total'] or 0)}"
                    )
                else:
                    spend_data = None

                response_data = ""
                ajo_saver_savings = AjoSaving.objects.filter(
                    ajo_user__phone_number__startswith=phone_number,
                    is_active=True,
                    is_activated=True,
                    ajo_user__isnull=False,
                    withdrawn=False,
                    amount_saved__gt=0,
                )

                if ajo_saver_savings:
                    total_bal = 0
                    for i, savings in enumerate(ajo_saver_savings, start=1):
                        response_data += f"Savings {i}. N{convert_num_to_currency(savings.amount_saved)}\n"

                        total_bal += savings.amount_saved

                    response_data += f"Total Bal: N{convert_num_to_currency(total_bal)}"

                else:
                    response_data = "No Active Savings Found"

                if spend_data:
                    response_data += f"\n{spend_data}"

            return HttpResponse(f"END {response_data}")
        elif text == "21":
            return HttpResponse(
                f"END Your OTP is {generate_ussd_otp(phone_number=phone_number)}"
            )
        elif text == "22":
            return HttpResponse(
                f"END Your OTP is {generate_ussd_otp(phone_number=phone_number)}"
            )
        elif text == "23":
            return HttpResponse(
                "CON please dial the entire code after the *23* on the agent's screen"
            )
        elif text == "24":
            return HttpResponse(
                f"END Your OTP is {generate_ussd_otp(phone_number=phone_number)}"
            )

        # elif check_generate_otp_ussd_pattern(text=text):
        #     # when the text is something like 22*81

        #     # split the text based on the delimiter '*'
        #     user_id = text.split("*")[-1]

        #     # TODO: consider checking the phone number is under the agent

        #     # generate an OTP with the user's id

        #     return HttpResponse(f"END Your OTP is {generate_ussd_otp(phone_number=phone_number, user_id=user_id)}")

        elif check_cashout_ussd_pattern(text=text):
            # when the text is something like 23*4000

            # split the text based on the delimiter '*'
            digits = text.split("*")

            # get the ajo savings id
            amount = int(digits[1:][0])

            retrieved_data = obtain_data_from_cashout_request(
                phone_number=phone_number,
                amount=amount,
            )

            if not retrieved_data.get("status"):
                return HttpResponse("END Cashout request not found. Please try again.")
                # return HttpResponse("END This cashout request was not made by the user with this phone number")

            #     return HttpResponse("END A cashout request was not made through the agent")

            # if phone_number != retrieved_data.get("data").get("phone_number"):
            #     return HttpResponse("END This cashout request was not made for this phone number")

            # call a function to generate an OTP and persist it in the cache
            # otp = generate_ussd_cashout_otp(
            #     phone_number=phone_number,
            #     ajo_saving_id=retrieved_data.get("data").get("ajo_saving_id"),
            #     amount=retrieved_data.get("data").get("amount"),
            #     user_id=retrieved_data.get("data").get("user_id"),
            # )

            otp = generate_ussd_cashout_otp(
                phone_number=phone_number,
                amount=retrieved_data.get("data").get("amount"),
                user_id=retrieved_data.get("data").get("user_id"),
            )

            return HttpResponse(f"END Your OTP for cashout is {otp}")

        return HttpResponse("END operation done")

    # def post(self, request, *args, **kwargs):
    #     # the content-type is 'application/x-www-form-urlencoded'
    #     # obtain the data
    #     data = request.data

    #     # get the several fields
    #     session_id: str = data.get("sessionId", None)
    #     service_code: str = data.get("serviceCode", None)
    #     phone_number: str = data.get("phoneNumber", None)
    #     text: str = data.get("text", "")

    #     print(session_id, service_code, phone_number, text, sep=";")

    #     # if only the service code was dialed
    #     if text == "":
    #         response = (
    #             "CON what would you want to do \n"
    #             + "1. Verify Onboarding\n"
    #             + "2. Verify Card Issuing\n"
    #             + "3. Cashout from Ajo Plan"
    #         )
    #         return HttpResponse(response)

    #     elif text == "1":
    #         return HttpResponse("CON what is the number after *1* on the agent's device")

    #     elif text == "2":
    #         return HttpResponse("CON what is the number after *2* on the agent's device")

    #     elif text == "3":
    #         return HttpResponse("END feature coming soon")

    #     elif check_verification_ussd_pattern(text=text):
    #         # split the text based on the * delimiter
    #         information = text.split("*")

    #         # obtain the first element in the list as this provides the information
    #         # on the operation to be performed
    #         # This assumes that the first string is a digit
    #         try:
    #             operation = int(information[0])
    #         except:
    #             return HttpResponse(self.failure_response)

    #         if verify_ussd_otp(otp=information[-1], phone_number=phone_number):
    #             response = "END "
    #             if operation == 1:
    #                 return HttpResponse(response + "onboarding verification successful")
    #             elif operation == 2:
    #                 return HttpResponse(response + "card issuing verification successful")
    #         else:
    #             return HttpResponse("END verification was unsuccessful")

    #     # return Response("END operation done", content_type="text/plain")
    #     return HttpResponse("END operation done")


def perform_bank_account_funding(
    unique_reference: str,
    account_instance: BankAccountDetails,
    amount: float,
    raw_funding: RawFundingData,
    extra_data: Dict[str, Any] | None = None,
    user=None,
    **kwargs,
) -> Dict[str, Any] | Response:
    raw_funding.authorized = True
    raw_funding.save()

    # find if the transaction exists in the database
    transaction_instance = Transaction.objects.filter(
        unique_reference=unique_reference
    ).first()

    if account_instance.account_provider == AccountProvider.CASH_CONNECT:
        transfer_provider = DisbursementProviderType.CASH_CONNECT
    elif account_instance.account_provider == AccountProvider.WEMA:
        transfer_provider = DisbursementProviderType.WEMA_PROVIDER
    else:
        transfer_provider = DisbursementProviderType.AGENCY_BANKING_PROVIDER

    if transaction_instance is None:
        if account_instance.form_type == AccountFormType.AGENT:
            wallet_type = WalletTypes.AJO_AGENT
        elif (
            account_instance.form_type == AccountFormType.LOAN_REPAYMENT
            or "LOAN_REPAYMENT" in account_instance.form_type
        ):
            wallet_type = WalletTypes.AJO_LOAN_REPAYMENT
        elif account_instance.form_type == AccountFormType.LOAN_RECOVERY:
            wallet_type = WalletTypes.LOAN_RECOVERY

        else:
            wallet_type = getattr(WalletTypes, account_instance.form_type)

        # if account_instance.account_provider == AccountProvider.CASH_CONNECT:
        #     transfer_provider = DisbursementProviderType.CASH_CONNECT
        # elif account_instance.account_provider == AccountProvider.WEMA:
        #     transfer_provider = DisbursementProviderType.WEMA_PROVIDER
        # else:
        #     transfer_provider = DisbursementProviderType.AGENCY_BANKING_PROVIDER

        transaction_instance = TransactionService.create_deposit_transaction(
            user=user,
            amount=amount,
            unique_reference=unique_reference,
            description=f"{amount} was received as a deposit from your bank account",
            wallet_type=wallet_type,
            ajo_user=account_instance.ajo_user,
            transfer_provider=transfer_provider,
        )

    if transaction_instance.status != Status.SUCCESS:
        # CHECK THE FORM TYPE
        if account_instance.form_type == AccountFormType.AGENT:
            try:
                resolve_pending_funding_agent_from_liberty_transaction(
                    transaction_instance=transaction_instance
                )

            except ValueError as err:
                return Response(
                    {
                        "status": "failed",
                        "error": "715",
                        "message": str(err),
                    }
                )

        if account_instance.form_type in [
            AccountFormType.AJO_SPENDING,
            AccountFormType.AJO_DIGITAL,
        ]:
            # the aim is to create a spending or digital wallet deposit

            ajo_user = account_instance.ajo_user

            # call the selector
            ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)

            # obtain the spending or digital wallet
            if account_instance.form_type == AccountFormType.AJO_SPENDING:

                if not account_instance.user_wallet:
                    to_wallet = ajo_user_selector.get_spending_wallet(
                        account_number=account_instance.account_number
                    )
                else:
                    to_wallet = account_instance.user_wallet

                deposit_name = "spending"
                wallet_type = WalletTypes.AJO_SPENDING
                unique_reference = f"{unique_reference}_{deposit_name}_deposit"

            else:
                to_wallet = ajo_user_selector.get_digital_wallet()
                deposit_name = "digital"
                wallet_type = WalletTypes.AJO_DIGITAL
                unique_reference = f"{unique_reference}_{deposit_name}_deposit"

            # create a transaction instance
            fund_transaction = TransactionService.create_deposit_by_virtual_account_in_ajo_transaction(
                user=(
                    ajo_user.user if ajo_user.provider == "ajo" else ajo_user.lite_user
                ),  # use the ajo agent or the lite user
                ajo_user=ajo_user,
                unique_reference=unique_reference,
                description=f"{amount} was just deposited into your {deposit_name} wallet",
                amount=amount,
                wallet_type=wallet_type,
                status=Status.PENDING,
            )

            # increment the wallet and update the transaction fields
            fund_wallet_and_update_transaction(
                wallet=to_wallet,
                amount=amount,
                transaction_instance=fund_transaction,
                unique_reference=unique_reference,
                ajo_user=ajo_user,
            )

            # update_transaction_status(
            #     wallet=to_wallet,
            #     transaction_instance=fund_transaction,
            #     success=True,
            # )

            transaction_instance.onboarded_user = ajo_user

        if account_instance.form_type == AccountFormType.ONLENDING_MAIN:
            user = account_instance.user
            ajo_user = account_instance.ajo_user
            onlending_selector = OnlendingSelector(user=user, ajo_user=ajo_user)
            onlending_main_wallet = onlending_selector.get_main_onlending_wallet()
            unique_reference = f"{unique_reference}_onlending_deposit"

            fund_transaction = TransactionService.create_deposit_by_virtual_account_in_ajo_transaction(
                user=user,
                ajo_user=ajo_user,
                unique_reference=unique_reference,
                description=f"{amount} was just deposited into your onlending main wallet",
                amount=amount,
                wallet_type=wallet_type,
                status=Status.PENDING,
                plan_type=PlanType.ONLENDING,
            )

            fund_wallet_and_update_transaction(
                wallet=onlending_main_wallet,
                amount=amount,
                transaction_instance=fund_transaction,
                unique_reference=unique_reference,
                ajo_user=ajo_user,
            )
        if account_instance.form_type in ["LOAN_REPAYMENT", "LOAN_RECOVERY"]:

            user = account_instance.user
            ajo_user = account_instance.ajo_user
            # transaction.transaction_form_type = TransactionFormType.AJO_LOAN_REPAYMENT_TOPUP_DEPOSIT
            # transaction_instance.transaction_source = TransactionSource.COREBANKING
            # transaction_instance.description = f"{amount} was received as a deposit from corebanking for ajo loan repayment"

            repayment_record, created = (
                RepaymentVirtuaWalletCreditRecord.objects.get_or_create(
                    agent=user,
                    session_id=extra_data.get("session_id"),
                    transaction_reference=unique_reference,
                )
            )

            repayment_record.agent = user
            repayment_record.company = extra_data.get("company")
            repayment_record.ajo_user = ajo_user
            repayment_record.amount = amount
            repayment_record.account_name = account_instance.account_name
            repayment_record.account_number = account_instance.account_number
            repayment_record.paid_at = extra_data.get("paid_at")
            repayment_record.save()

            if transaction_instance.status != Status.SUCCESS:
                try:
                    bank_transfer_repayment_response = AjoLoanRepayment.resolve_pending_funding_repayment_wallet_from_corebanking(
                        transaction_instance=transaction_instance,
                        ajo_user=ajo_user,
                        transfer_provider=transfer_provider,
                    )

                    # POST REPAYMENT RECORD TO LOANDISK
                    loan_id = bank_transfer_repayment_response.get("loan_id")
                    if loan_id != None:
                        celery_handle_loandisk_loan_repayment.delay(
                            ajo_loan_id=loan_id,
                            amount=amount,
                            repayment_type=RepaymentType.TRANSFER,
                        )

                except ValueError as err:
                    return Response(
                        {
                            "status": "failed",
                            "error": "715",
                            "message": str(err),
                        }
                    )

            # response = {"status": "success", "message": "Confirmed"}

        transaction_instance.status = Status.SUCCESS
        if extra_data:
            transaction_instance.beneficiary_name = extra_data.get("payer_account_name")
            transaction_instance.beneficiary_account = extra_data.get(
                "payer_account_number"
            )
            transaction_instance.beneficiary_bank = extra_data.get("payer_bank_code")
        transaction_instance.save()
        transaction_instance.refresh_from_db()
        res = {
            "status": "success",
            "message": "wallet funded succesfully",
        }

        raw_funding.funding_response = res
        raw_funding.save()

        return res


class WemaCallbackAPIView(APIView):
    permission_classes = (CoreRepaymentWebhook,)

    def post(self, request):

        try:
            serializer = RepaymentHookSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            validated_data = serializer.validated_data

            company = validated_data.get("company")
            recipient_account_name = validated_data.get("recipient_account_name")
            recipient_account_number = validated_data.get("recipient_account_number")
            amount = validated_data.get("amount")
            payer_account_name = validated_data.get("payer_account_name")
            payer_account_number = validated_data.get("payer_account_number")
            payer_bank_code = validated_data.get("payer_bank_code")
            paid_at = validated_data.get("paid_at")
            narration = validated_data.get("narration")
            transaction_reference = validated_data.get("reference")
            other_transaction_reference = validated_data.get("transaction_reference")
            session_id = validated_data.get("session_id")
            fee = validated_data.get("fee")
            amount_payable = validated_data.get("amount_payable")
            settlement_status = validated_data.get("settlement_status")
            currency = validated_data.get("currency")

            account_instance = BankAccountSelector.check_for_account_number(
                account_number=recipient_account_number,
                # bank_name="Wema",
            )

            raw_funding = RawFundingData.objects.filter(
                reference=transaction_reference
            ).last()

            if not raw_funding:
                raw_funding = RawFundingData.objects.create(
                    reference=transaction_reference,
                    recipient_account_number=recipient_account_number,
                    payload=request.data,
                    source=(
                        account_instance.account_provider
                        if account_instance
                        else "WEMA"
                    ),
                )

            if not account_instance:
                raw_funding.result = "Invalid account number"
                raw_funding.save()

                return Response(
                    "Invalid account number", status=status.HTTP_400_BAD_REQUEST
                )

            user = account_instance.user
            ajo_user = account_instance.ajo_user

            res = CoreBankingManager.verify_transaction_reference(
                reference=transaction_reference
            )
            # res = {
            #     "status": "success",
            #     "status_code": 200,
            #     "data": {
            #         "company": "Ajo Loans",
            #         "sub_company": "None",
            #         "sub_company_email": "None",
            #         "sub_company_unique_id": "None",
            #         "recipient_account_name": "Liberty/oyelaja Adekeke Emmanuel-",
            #         "recipient_account_number": "**********",
            #         "amount": 32000.0,
            #         "reference": "2a0d9dfb-b624-48a2-87b4-11e6ef5af271",
            #         "paid_at": "2025-05-21T09:11:57.119381Z",
            #         "details": "**********/**********/Liberty/oyelaja",
            #         "session_id": "090110250521100845912833246749",
            #         "fee": 0.0,
            #         "status": "SUCCESSFUL",
            #         "type": "CREDIT",
            #     },
            #     "errors": "None",
            # }

            raw_funding.result = res
            raw_funding.save()

            if (
                res.get("status") == "success"
                and res.get("data", {}).get("recipient_account_number")
                == recipient_account_number
                and res.get("data", {}).get("amount") == amount
                and res.get("data", {}).get("reference") == transaction_reference
            ):

                # Validate Reference is not repeated in Ajo
                extra_data = {
                    "session_id": session_id,
                    "company": company,
                    "paid_at": paid_at,
                    "payer_account_name": payer_account_name,
                    "payer_account_number": payer_account_number,
                    "payer_bank_code": payer_bank_code,
                    "narration": narration,
                }

                response = perform_bank_account_funding(
                    unique_reference=transaction_reference,
                    account_instance=account_instance,
                    amount=amount,
                    extra_data=extra_data,
                    raw_funding=raw_funding,
                    user=user,
                )

                if isinstance(response, Response):
                    return response

                return Response(
                    data=response,
                    status=status.HTTP_200_OK,
                )

            else:
                response = {
                    "message": "funding verification data is bad",
                    "status": "failed",
                }
                return Response(
                    data=response,
                    status=status.HTTP_400_BAD_REQUEST,
                )

        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "error": "500",
                    "error_msg": str(e),
                    "message": "An internal error occurred. ",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class LibertyPayVfdFundingCallback(APIView):
    # authentication_classes = [AgencyBankingFundingAuthentication]
    permission_classes = (VFDWebhookWhitelist,)

    def post(self, request):
        data = request.data
        if not isinstance(data, dict):
            try:
                data = json.loads(data)
            except:
                data = eval(data)

        #     data = json.loads(data)
        # print("type of data: ", type(data))
        """
        SAMPLE RESPONSE:
        {
            "amount":4500.0,
            "user_id":1,
            "reference":"LGLP_FND_COLL_ACCT-**********-6a2a4837-f71f-465e-aed9-bab8a62d44c3",
            "agent_phone":"234ASSUREDREF",
            "account_number":"**********",
            "liberty_commission":0.0
        }
        """
        user_id = data.get("user_id")
        amount = data.get("amount")
        liberty_reference = data.get("reference")
        account_number = data.get("account_number")

        if not liberty_reference:
            return Response(
                {
                    "status": "failed",
                    "error": "870",
                    "message": "no liberty reference to process this transaction",
                },
                status.HTTP_400_BAD_REQUEST,
            )

        raw_funding = RawFundingData.objects.filter(reference=liberty_reference).last()
        if not raw_funding:
            raw_funding = RawFundingData.objects.create(
                reference=liberty_reference,
                recipient_account_number=account_number,
                payload=data,
                source="AGENCY_BANKING_VFD",
            )

        if not user_id or not amount or not account_number:
            response = {
                "status": "failed",
                "error": "884",
                "message": "incomplete data for processing",
            }
            raw_funding.funding_response = response
            raw_funding.save()

            return Response(
                data=response,
                status=status.HTTP_400_BAD_REQUEST,
            )

        # check if the account number exists on my system
        account_instance = BankAccountSelector.check_for_account_number(
            account_number=account_number
        )
        if not account_instance:
            response = {
                "status": "failed",
                "error": "981",
                "message": "this account number does not exist on this server",
            }

            raw_funding.funding_response = response
            raw_funding.save()

            return Response(
                data=response,
                status=status.HTTP_400_BAD_REQUEST,
            )

        # check if the account instance and user id rhyme
        if not int(account_instance.user.customer_user_id) == int(user_id):
            response = {
                "status": "failed",
                "error": "982",
                "message": "the user id that the account belongs to is different on my server",
            }

            raw_funding.funding_response = response
            raw_funding.save()
            return Response(
                data=response,
                status=status.HTTP_400_BAD_REQUEST,
            )

        bypass = request.headers.get("BYPASS", None)

        if bypass != "INW":
            if (
                account_instance.form_type == AccountFormType.AGENT
                and not liberty_reference.startswith("LGLP_FND_COLL")
            ):
                response = {
                    "status": "failed",
                    "error": "659",
                    "message": "cannot use funding notification",
                }

                raw_funding.funding_response = response
                raw_funding.save()
                return Response(
                    data=response,
                    status=status.HTTP_200_OK,
                )

            if (
                account_instance.account_type != "AJO_USER_CORPORATE"
                and "LP-INW-VFD" in liberty_reference
            ):
                response = {
                    "status": "failed",
                    "error": "661",
                    "message": "cannot use VFD transaction for funding",
                }

                raw_funding.funding_response = response
                raw_funding.save()

                return Response(
                    data=response,
                    status=status.HTTP_400_BAD_REQUEST,
                )

        # if transaction_type == "FUND_COLLECTION_ACCOUNT":
        #     source = "AGENCY_BANKING_VFD"
        # elif transaction_type == "FUND_LOTTO_WALLET":
        #     soruce = "AGENCY_BANKING"
        # else:
        #     data = {
        #         "message": "transaction type not supported",
        #         "status": "Success",
        #     }
        #     return Response(data=data, status=status.HTTP_200_OK)
        # amount = float(amount) - float(liberty_commission)
        # Check If Agent Exists
        # agent_instance = Agent.objects.filter(user_id=agent_user_id).last()
        # obtain the user
        try:
            user = get_user_model().objects.get(customer_user_id=user_id)
        except get_user_model().DoesNotExist:
            return Response(
                {
                    "status": False,
                    "message": "this user does not exist on savings server",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # instantiate AjoAgentSelector class
        ajo_agent_methods = AjoAgentSelector(user=user)

        # obtain the wallet
        agent_wallet = ajo_agent_methods.get_agent_ajo_wallet()
        ########### Check If Reference Exists In Transaction ##############
        # if transaction_instance is not None:
        #     print(
        #         "agent vfd funding already verified \n\n\n\n\n",
        #     )
        #     response = {
        #         "message": "agent vfd funding already verified",
        #         "error": "890",
        #         "status": False,
        #     }
        #     return Response(data=response, status=status.HTTP_400_BAD_REQUEST)

        ######### Verify Transaction
        # verify_vfd_trans = verify_agency_banking_transaction_for_vfd_funding(reference)
        verify_vfd_transaction = (
            AgencyBankingClass.verify_agency_banking_for_vfd_funding(
                liberty_reference=liberty_reference
            )
        )
        raw_funding.verification_payload = verify_vfd_transaction
        raw_funding.save()

        ### the dictionary below is for testing VFD fundings in development
        # verify_vfd_transaction = {
        #     "status": True,
        #     "data": {
        #         "status": "SUCCESSFUL",
        #         "transaction_type": "FUND_COLLECTION_ACCOUNT",
        #         "liberty_reference": "LGLP_FND_COLL_ACCT-**********-6a2a4837-f71f-465e-aed9-bab8a62d44P0",
        #         "unique_reference": None,
        #         "account_provider": None,
        #         "amount": 200.0,
        #         "liberty_commission": 0.0,
        #         "sms_charge": 0.0,
        #         "total_amount_charged": None,
        #         "transaction_leg": "EXTERNAL",
        #         "balance_before": 189218.12,
        #         "balance_after": 189228.12,
        #         "beneficiary_account_name": None,
        #         "source_account_name": "CHUKWUEMEKA NWAOMA",
        #         "date_created": "2022-12-09T12:25:56.777100+01:00",
        #         "lotto_agent_user_id": "145",
        #         "lotto_agent_user_phone": "*************",
        #         "type_of_user": "PERSONAL",
        #     },
        # }
        if verify_vfd_transaction.get("status") is True:
            res = verify_vfd_transaction
            amount = round(float(amount), 2)

            calculated_amount = res.get("data", {}).get("amount") - res.get(
                "data", {}
            ).get("liberty_commission", 0)

            if (
                res.get("data", {}).get("status") == "SUCCESSFUL"
                and res.get("data", {}).get("transaction_type")
                in ["FUND_COLLECTION_ACCOUNT", "FUND_BANK_TRANSFER"]
                and res.get("data", {}).get("liberty_reference") == liberty_reference
                and calculated_amount == amount
            ):

                response = perform_bank_account_funding(
                    user=user,
                    unique_reference=liberty_reference,
                    account_instance=account_instance,
                    amount=amount,
                    raw_funding=raw_funding,
                )

                if isinstance(response, Response):
                    return response

                return Response(
                    data=response,
                    status=status.HTTP_200_OK,
                )

            else:
                response = {
                    "message": "funding could not be verified",
                    "status": "failed",
                }
                raw_funding.funding_response = response
                raw_funding.save()
                return Response(data=response, status=status.HTTP_400_BAD_REQUEST)
        else:
            response = {
                "message": "funding verification data is bad",
                "status": "failed",
            }
            raw_funding.funding_response = response
            raw_funding.save()
            return Response(data=response, status=status.HTTP_400_BAD_REQUEST)


class BankConsentCallback(APIView):
    pass


class SendtoAjoFundingViaAgencyBankingWebhookView(APIView):
    permission_classes = (VFDWebhookWhitelist,)

    def post(self, request):
        data = request.data
        """
        SAMPLE DATA:
        {'lotto_agent_user_id': '00000000000000000000000', 'lotto_agent_user_phone': '23463950553884216293', 'status': 'SUCCESSFUL', 'transaction_type': 'FUND_AJO_WALLET', 'amount': 10.0, 'liberty_commission': 0.0, 'sms_charge': 0.0, 'balance_before': 9160.0, 'balance_after': 9170.0, 'source_account_name': 'CHUKWUEMEKA NWAOMA OJUKWU 1', 'source_nuban': None, 'liberty_reference': 'LGLP_SND_BUDDY-8b125825-672e-493f-a34a-751e990cabe0', 'is_reversed': False, 'type_of_user': 'MERCHANT'}
        """
        if isinstance(data, str):
            data = json.loads(data)

        liberty_reference = data.get("liberty_reference")
        agent_user_id = data.get("lotto_agent_user_id")
        amount = data.get("amount")
        agent_phone = data.get("lotto_agent_user_phone")

        raw_funding = RawFundingData.objects.filter(reference=liberty_reference).last()

        if not raw_funding:
            raw_funding = RawFundingData.objects.create(
                reference=liberty_reference,
                payload=data,
                source="AGENCY_BANKING_AJO_WALLET",
            )

        try:
            user = get_user_model().objects.get(customer_user_id=agent_user_id)
        except get_user_model().DoesNotExist:
            return Response(
                {
                    "status": False,
                    "message": "this user does not exist on savings server",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # # instantiate AjoAgentSelector class
        # ajo_agent_methods = AjoAgentSelector(user=user)

        # # obtain the wallet
        # agent_wallet = ajo_agent_methods.get_agent_ajo_wallet()

        verify_vfd_transaction = (
            AgencyBankingClass.verify_agency_banking_for_vfd_funding(
                liberty_reference=liberty_reference
            )
        )
        raw_funding.verification_payload = verify_vfd_transaction
        raw_funding.save()

        if verify_vfd_transaction.get("status") is True:
            res = verify_vfd_transaction
            amount = round(float(amount), 2)
            transaction_type = res.get("data", {}).get("transaction_type")
            if transaction_type == "FUND_AJO_LOANS":
                wallet_type = WalletTypes.PROSPER_LOAN_WALLET
                description = (
                    f"{amount} was received as a deposit from liberty pay to ajo loan"
                )
                transaction_form_type = TransactionFormType.LIB_PROSPER_LOAN_DEPOSIT
            else:
                wallet_type = WalletTypes.AJO_AGENT
                description = (
                    f"{amount} was received as a deposit from liberty pay to ajo wallet"
                )
                transaction_form_type = TransactionFormType.FUND_FROM_LIBERTY_PAY

            if (
                res.get("data", {}).get("status") == "SUCCESSFUL"
                and res.get("data", {}).get("transaction_type")
                in ["FUND_AJO_WALLET", "FUND_AJO_LOANS"]
                and res.get("data", {}).get("liberty_reference") == liberty_reference
                and res.get("data", {}).get("amount") == amount
                and res.get("data", {}).get("lotto_agent_user_id") == agent_user_id
            ):
                raw_funding.authorized = True
                raw_funding.save()

                # find if the transaction exists in the database
                transaction_instance = Transaction.objects.filter(
                    unique_reference=liberty_reference
                ).first()

                if transaction_instance is None:
                    transaction_instance = (
                        TransactionService.create_deposit_transaction(
                            user=user,
                            amount=amount,
                            unique_reference=liberty_reference,
                            wallet_type=wallet_type,
                            transaction_form_type=transaction_form_type,
                            transaction_source=TransactionSource.LIBERTY_PAY,
                            description=description,
                        )
                    )

                if transaction_instance.status != Status.SUCCESS:
                    try:
                        resolve_pending_funding_agent_from_liberty_transaction(
                            transaction_instance=transaction_instance
                        )

                    except ValueError as err:
                        return Response(
                            {
                                "status": "failed",
                                "error": "715",
                                "message": str(err),
                            }
                        )

                if (
                    transaction_instance.transaction_form_type
                    == TransactionFormType.LIB_PROSPER_LOAN_DEPOSIT
                ):
                    from loans.models import ProsperAgent

                    ProsperAgent.create_instance(
                        user,
                        amount,
                        unique_reference=transaction_instance.unique_reference,
                    )

                res = {
                    "status": "success",
                    "message": "wallet funded succesfully",
                }

                raw_funding.funding_response = res
                raw_funding.save()

                return Response(
                    data=res,
                    status=status.HTTP_200_OK,
                )

            else:
                response = {
                    "message": "funding could not be verified",
                    "status": "failed",
                }
                raw_funding.funding_response = response
                raw_funding.save()
                return Response(data=response, status=status.HTTP_400_BAD_REQUEST)
        else:
            response = {
                "message": "funding verification data is bad",
                "status": "failed",
            }
            raw_funding.funding_response = response
            raw_funding.save()
            return Response(data=response, status=status.HTTP_400_BAD_REQUEST)


class DojahWebhookView(APIView):
    def failed_verification_response(
        self,
        failure_reason: str,
    ) -> Response:
        return Response(
            {
                "status": False,
                "error": "403",
                "message": failure_reason,
            },
            status=status.HTTP_200_OK,
        )

    def generate_response(self, field_name: str) -> Response:
        return Response(
            {
                "status": False,
                "error": "404",
                "message": f"no {field_name} in this request metadata, hence, not for this server.",
            },
            status=status.HTTP_200_OK,
        )

    def post(self, request):
        """
        SAMPLE DATA: find sample data in payment/ps.txt under 'DOJAH'
        """
        data = request.data

        if isinstance(data, str):
            data = json.loads(data)

        if not request.headers.get("X-Dojah-Signature"):
            return self.generate_response("Dojah request header")

        metadata = data.get("metadata")
        phone_number = metadata.get("ajo_user_phone_number", None)
        user_email = metadata.get("user_email", None)
        session_id = metadata.get("session_id", None)

        if not session_id:
            return self.generate_response("session_id")

        if not phone_number:
            return self.generate_response("ajo_user_phone_number")

        if not metadata.get("user_type"):
            return self.generate_response("user_type")

        if not user_email:
            return self.generate_response("user_email")

        if not metadata.get("user_type") == "CHANGE_AJO_USER_INFORMATION":
            return self.generate_response("'CHANGE_AJO_USER_INFORMATION' in user_type")

        try:
            user = get_user_model().objects.get(email=user_email)
        except get_user_model().DoesNotExist as err:
            return Response(
                {
                    "status": False,
                    "error": "404",
                    "message": "this email not found on server",
                },
                status=status.HTTP_200_OK,
            )

        ajo_user_selector = AjoUserSelector(phone_number=phone_number, user=user)
        ajo_user = ajo_user_selector.get_ajo_user()

        try:
            change_request = ProfileChangeRequest.objects.filter(
                session_id=session_id,
                ajo_user=ajo_user,
                is_active=True,
                status__isnull=True,
            )
        except ProfileChangeRequest.DoesNotExist:
            return Response(
                {
                    "status": False,
                    "error": "404",
                    "message": "profile change request not found on server",
                },
                status=status.HTTP_200_OK,
            )

        if data.get("verificationStatus") != "Completed":
            failure_reason = "verification on the dojah platform failed"
            ProfileChangeService.update_profile_change_request(
                session_id=session_id,
                ajo_user=ajo_user,
                data=data,
                failure_reason=failure_reason,
            )
            return Response(
                {
                    "status": False,
                    "error": "400",
                    "message": "verification failed",
                },
                status=status.HTTP_200_OK,
            )

        ProfileChangeService.update_profile_change_request(
            session_id=session_id,
            ajo_user=ajo_user,
            data=data,
        )

        return Response(
            {
                "status": True,
                "message": "updated the change request, now processing.",
            },
            status=status.HTTP_200_OK,
        )
