import datetime
import json
import uuid
import os
import re
from datetime import date, datetime

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.exceptions import ObjectDoesNotExist
from django.db import IntegrityError
from django.db import transaction as django_transaction
from django.db.models import Count, F, Q, Sum
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.http import HttpResponse
from django.contrib.admin.views.decorators import staff_member_required

from django.views.decorators.cache import cache_page
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import filters, generics, permissions, serializers, status
from rest_framework.exceptions import NotFound
from rest_framework.generics import GenericAPIView
from rest_framework.parsers import MultiPartParser
from rest_framework.response import Response
from rest_framework.views import APIView

from accounts.agency_banking import AgencyBankingClass
from accounts.models import ActionPermission, ConstantTable, CustomUser
from accounts.permissions import (
    AllowSavingsPermission,
    CanCreatePlanPermission,
    IsBlackListed,
)
from accounts.responses import (
    error_response,
    pagination_page_not_found_response,
    serializer_validation_error_response,
    success_response,
    value_error_response,
)
from accounts.services import NextOfKinService, UserService
from loans.helpers.core_banking import CoreBankingManager
from loans.models import AjoLoan, BorrowerInfo
from payment.checks import obtain_phone_number, verify_transaction_pin
from payment.model_choices import TransactionFormType
from payment.models import WalletSystem
from payment.services import TransactionService
from savings.cache import cache_page_per_user
from savings.pagination import CustomPagination

from ..model_choices import (
    AccountFormType,
    AccountProvider,
    AjoType,
    OnboardingStage,
    OTPType,
    RoscaGroupStatus,
    RoscaType,
    SavingsType,
    VerificationStages,
)
from ..models import (
    AjoSavingsPlanType,
    AjoTradeSubCategory,
    AjoUser,
    BankAccountDetails,
    Card,
    CardRequests,
    RoscaPaymentBreakDown,
    RotationGroup,
    RotationGroupMember,
    calculate_group_details_end_date,
)
from ..payment_actions import (
    check_if_agent_can_pay,
    debit_wallet_and_update_transaction,
    dynamic_withdraw_from_ajo_agent_wallet,
    pay_for_agent_ajo_user_contribution,
    update_transaction_status,
)
from ..permissions import (
    AjoUserOnboardedPermission,
    HookAuthorizationPermission,
    VFDWebhookWhitelist,
)
from ..prefunding.actions import check_if_initial_prefunding_can_be_given_to_agent
from ..selectors import (
    AjoAgentSelector,
    AjoSavingsSelector,
    AjoSepoSelector,
    AjoUserSelector,
    BankAccountSelector,
    PrefundingSelector,
    ProfileChangeSelector,
)
from ..serializers.ajo_serializers import (
    AddGroupMemberSerializer,
    AgencyBankingAgentInfoSerializer,
    AgentAjoSepoStatusSerializer,
    AgentCreateRotationGroupSerializer,
    AgentEditDeleteRotationGroupSerializer,
    AgentLedgerSerializer,
    AgentLogContributionSerializer,
    AgentSummarySerializer,
    AgentWalletSerializer,
    AjoNextOfKinSerializer,
    AjoSavingFullDetailsSerializer,
    AjoSavingsInformationSerializer,
    AjoSavingsPlanTypesSerializer,
    AjoSepoDetailsSerializer,
    AjoSepoSerializer,
    AjoTradesSubCategorySerializer,
    AjoUserChangeSerializer,
    AjoUserListSerializer,
    AjoUserPinSerializer,
    AjoUsersBasedOnCardsSerializer,
    AjoUserTotalBalancesSerializer,
    AssignCardSerializer,
    ConfirmCardDetailsSerializer,
    CreateAjoSavingsPlanSerializer,
    CreateAjoSepoSerializer,
    DeliveryAddressSerializer,
    ExistingAjoUserSerializer,
    ExistingUserSavingsSerializer,
    FillPersonalDetailsSerializer,
    GeneralAjoTransactionHistorySerializer,
    GetAgentCardsSerializer,
    GetPlansSerializer,
    GetSpendingDigitalSerializer,
    ImageCapturingSerializer,
    JoinAjoSepoGroupSerializer,
    OnboardAjoUserWithPhoneNumberSerializer,
    ProductInformationSerializer,
    ProfileChangeRequestSerializer,
    RequestCardSerializer,
    RoscaPaymentBreakDownSerializer,
    RotationbreakdownSerializer,
    RotationGroupMemberSerializer,
    SelectAlternativeOTPChoiceSerializer,
    TradeSearchSerializer,
    TransactionHistoryForAjoUserSerializer,
    TransactionSummarySerializer,
    UpdateAjoUserImageSerializer,
    VerifyOTPSerializer,
    VirtualAccountDetailsSerializer,
)
from ..serializers.personal_ajo_serializers import GroupSerializer
from ..services import (
    AjoSavingsService,
    AjoSepoService,
    AjoUserService,
    BankAccountService,
    ProductInformationService,
    verify_card_issuing_and_return,
    verify_onboarding_and_return,
)
from ..tasks import deposit_initial_prefunding, verify_ajo_user_bvn
from ..utils.general_utils import obtain_the_current_site, validate_date_format
from ..utils.otp_utils import (
    ajo_rosca_contribution_sms,
    send_out_sms_otp,
    send_out_voice_otp,
    send_out_whatsapp_otp,
    verify_sms_voice_otp,
    verify_whatsapp_otp,
)
from ..utils.ussd_utils import ussd_code_to_generate_otp, verify_ussd_otp

#########INFORMATION###########
# errors pertaining to Ajo Users and Cards: error code -> 1012
# errors pertaining to OTPs: error code -> 613
# errors pertaining to uploading: error code -> 801
# other errors like validation, valueerror,  absence of data etc: error code -> 603
##############################


class OTPAPIView(GenericAPIView):
    serializer_class = SelectAlternativeOTPChoiceSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        """
        This endpoint sends an OTP out to the phone number provided in the request body and the OTP type specified
        (the medium you want the OTP to be done with) SMS, VOICE, WHATSAPP or USSD
        """
        serializer = self.serializer_class(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        # obtain the ajo agent and ajo user number
        phone_number = serializer.validated_data.get("phone_number")

        # check if the ajo user belongs to the agent
        try:
            ajo_user = AjoUserSelector(
                phone_number=phone_number, user=request.user
            ).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        # obtain the OTP type
        otp_type = serializer.validated_data.get("otp_type")

        if otp_type is OTPType.SMS:
            send_out_sms_otp(phone_number=phone_number)
            return Response(
                {
                    "status": True,
                    "message": "OTP has been sent to the phone number supplied",
                },
                status=status.HTTP_200_OK,
            )

        elif otp_type is OTPType.VOICE:
            send_out_voice_otp(phone_number=phone_number)
            return Response(
                {
                    "status": True,
                    "message": "OTP has been sent to the phone number supplied",
                },
                status=status.HTTP_200_OK,
            )

        elif otp_type is OTPType.WHATSAPP:
            send = send_out_whatsapp_otp(phone_number=phone_number)
            if send:
                return Response(
                    {
                        "status": True,
                        "message": "OTP has been sent to the phone number supplied",
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "error": "613",
                        "status": False,
                        "message": "there was an error while sending out the OTP",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        elif otp_type is OTPType.USSD:
            return Response(
                {
                    "status": True,
                    "message": f"Dial this code {ussd_code_to_generate_otp} to get an OTP",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )


class VerifyOTPAPIView(GenericAPIView):
    serializer_class = VerifyOTPSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        """
        This endpoint verifies an OTP based off the the OTP type that was used for sending the OTP
        otp_type: SMS, VOICE, WHATSAPP or USSD
        verification_stage: ONBOARDING OR CARD_ISSUING
        """
        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        # obtain the ajo agent and ajo user number
        phone_number = serializer.validated_data.get("phone_number")
        user = request.user

        # check if the ajo user belongs to the agent
        try:
            ajo_user = AjoUserSelector(
                phone_number=phone_number, user=user
            ).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        # obtain the otp
        otp = serializer.validated_data.get("otp")

        # obtain the otp type
        otp_type = serializer.validated_data.get("otp_type")

        # attempt to verify the OTP
        if otp_type in [OTPType.SMS, OTPType.VOICE]:
            verify_otp = verify_sms_voice_otp(
                otp=otp,
                phone_number=phone_number,
            ) or verify_ussd_otp(
                otp=otp,
                phone_number=phone_number,
            )

        elif otp_type is OTPType.WHATSAPP:
            verify_otp = verify_whatsapp_otp(
                otp=otp,
                phone_number=phone_number,
            ) or verify_ussd_otp(
                otp=otp,
                phone_number=phone_number,
            )

        elif otp_type is OTPType.USSD:
            verify_otp = verify_ussd_otp(
                otp=otp,
                phone_number=phone_number,
            )

        # obtain the verification stage
        verification_stage = serializer.validated_data.get("verification_stage")

        # check the verification stage
        if verification_stage is VerificationStages.ONBOARDING:
            try:
                verify_change = verify_onboarding_and_return(
                    verify=verify_otp, ajo_user=ajo_user
                )
                if verify_change:

                    if ajo_user.onboarding_stage == "OTP":
                        ajo_user.onboarding_stage = OnboardingStage.PERSONAL_DETAILS
                        ajo_user.save()

                    return Response(
                        {
                            "status": True,
                            "onboarding_stage": ajo_user.onboarding_stage,
                            "message": "user verified successfully",
                        },
                        status=status.HTTP_200_OK,
                    )
                else:
                    return Response(
                        {
                            "error": "613",
                            "status": False,
                            "message": "invalid OTP, please try again",
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            except ValueError as err:
                return Response(
                    {
                        "status": True,
                        "message": "this user is already verified",
                    },
                    status=status.HTTP_200_OK,
                )

        elif verification_stage is VerificationStages.CARD_ISSUING:
            try:
                verify_change = verify_card_issuing_and_return(
                    verify=verify_otp, ajo_user=ajo_user
                )
                if verify_change:
                    return Response(
                        {
                            "status": True,
                            "message": "card issued to user successfully",
                        },
                        status=status.HTTP_200_OK,
                    )
                else:
                    return Response(
                        {
                            "error": "613",
                            "status": False,
                            "message": "invalid OTP, please try again",
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            except ValueError as err:
                return Response(
                    {
                        "status": False,
                        "error": "304",
                        "message": "a card has been issued to this user already",
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )

        elif verification_stage is VerificationStages.LOAN_REQUEST:
            if verify_otp:
                return Response(
                    {
                        "status": True,
                        "message": "loan request OTP verified successfully",
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "status": False,
                        "error": "304",
                        "message": "invalid OTP, please try again",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )


class AgentWalletAPIView(GenericAPIView):
    serializer_class = AgentWalletSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request, *args, **kwargs):
        """
        The Agent's Ajo wallet and Prefunding wallet balances
        """
        user = request.user
        available_balance = AjoAgentSelector(user=user).get_agent_ajo_wallet_balance()
        prefunding_selector = PrefundingSelector(user=user)

        # NEW DEVELOPMENT
        # check if prefunding can be given to the agent
        if check_if_initial_prefunding_can_be_given_to_agent(user=user):
            deposit_initial_prefunding(user_id=user.id)

        balances = {
            "available_balance": available_balance,
            "prefunding_balances": {
                "available_balance": prefunding_selector.available_balance_in_prefunding_wallet(),
                "hold_balance": prefunding_selector.check_how_much_outstanding_left(),
                "due_balance": prefunding_selector.how_much_prefunding_has_been_used_against_hold_balance(),
            },
        }

        serializer = self.serializer_class(balances)

        return Response(
            {
                "status": True,
                **serializer.data,
            },
            status=status.HTTP_200_OK,
        )


class SavedWithdrawnSummaryAPIView(GenericAPIView):
    serializer_class = AgentSummarySerializer
    permission_classes = (permissions.IsAuthenticated,)
    pagination_class = CustomPagination

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "month",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="insert month in the format 'YYYY-MM' e.g. '2023-01'",
            ),
        ],
        responses={200: TransactionSummarySerializer},
    )
    @method_decorator(cache_page_per_user(60))
    def get(self, request, *args, **kwargs):
        # obtain the month and user
        month: str | None = request.query_params.get("month")
        user = request.user

        agent_methods = AjoAgentSelector(user=user)
        summary_data = agent_methods.get_summary_for_agent(month=month)
        summary_data["today_collection"] = (
            agent_methods.get_total_collection_for_the_day()
        )
        # USSD CODE, NEW DEVELOPMENT :: 13/09/2023
        summary_data["ussd_otp_code"] = ussd_code_to_generate_otp
        transactions = agent_methods.get_transaction_history_summary_for_agent()
        try:
            paginated_transactions = self.paginate_queryset(transactions)
        except NotFound:
            return pagination_page_not_found_response()
        serializer = self.serializer_class(summary_data)
        transactions_serializer = GeneralAjoTransactionHistorySerializer(
            paginated_transactions, many=True
        )
        response_data = serializer.data
        response_data["transactions"] = transactions_serializer.data
        return self.get_paginated_response(response_data)


class OnboardAjoUserWithPhoneNumberAPIView(APIView):
    serializer_class = OnboardAjoUserWithPhoneNumberSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request):
        """
        Takes in the phone number of the ajo user you wish to onboard and sends an OTP through
        SMS. If a user with the  phone number already exists for the agent, it retrieves the user, checks if the user is verified
        if the user is verified, it returns a Response object to indicate, else, it sends an OTP through SMS to properly onboard the user
        """
        user = request.user
        serializer = self.serializer_class(data=request.data, context={"user": user})
        serializer.is_valid(raise_exception=True)
        phone_number = serializer.validated_data["phone_number"]
        ajo_user_instance = serializer.validated_data["ajo_user_instance"]

        if not ajo_user_instance:

            ajo_user = AjoUserService.create_ajo_user_under_agent(
                user=user, phone_number=phone_number
            )
            ajo_user.onboarding_stage = OnboardingStage.OTP
            ajo_user.save()

        send_out_sms_otp(phone_number=phone_number)

        return Response(
            data={
                "status": True,
                "message": "OTP has been sent to the mobile number provided",
                "ussd_alternative": f"Dial {ussd_code_to_generate_otp} to generate an OTP",
                "onboarding_stage": (
                    ajo_user_instance.onboarding_stage
                    if ajo_user_instance
                    else OnboardingStage.OTP
                ),
            },
            status=status.HTTP_201_CREATED,
        )


class StatesLGAAPIView(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    @method_decorator(cache_page(60 * 60 * 24))  # cache it for a whole day
    def get(self, request, *args, **kwargs):
        """
        Returns all the states and their LGAs
        """
        try:
            # Specify the path to your .txt file
            file_path = "ajo/states_and_lga.txt"

            # Open the .txt file for reading
            with open(file_path, "r") as file:
                # Read the content of the file
                file_content = file.read()

                # Parse the file content into a Python data structure
                json_data = json.loads(file_content)

                # Create a JSON response
                return Response(
                    {
                        "status": True,
                        "data": json_data,
                    },
                    status=status.HTTP_200_OK,
                )

        except Exception as err:
            # Handle any exceptions, such as file not found or JSON parsing errors
            return Response(
                {
                    "status": False,
                    "error": "500",
                    "error_message": str(err),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )


class TradesListAPIView(GenericAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    pagination_class = CustomPagination
    serializer_class = AjoTradesSubCategorySerializer

    def get(self, request, *args, **kwargs):
        """
        Returns a paginated list of trades.

        """

        list_of_trades = (
            AjoTradeSubCategory.objects.all()
            .order_by("?")
            .values_list("name", flat=True)
        )

        # list_of_trades = [
        #     "Trader",
        #     "Engineer",
        #     "Banker",
        # ]

        try:
            paginated_results = self.paginate_queryset(list_of_trades)

        except NotFound:
            return pagination_page_not_found_response()

        return self.get_paginated_response(paginated_results)


class TradeSearchAPIView(GenericAPIView):
    serializer_class = TradeSearchSerializer
    permission_classes = (permissions.IsAuthenticated,)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "trade",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="insert a trade you wish to search for",
            ),
        ],
    )
    # @method_decorator(cache_page(60 * 60))  # cache for one hour
    def get(self, request, *args, **kwargs):
        """
        Search for some letters that make up your trade.
        """
        trade_query = request.query_params.get("trade")
        if not trade_query:
            return Response(
                {
                    "status": False,
                    "error": "422",
                    "message": "to use this endpoint, you must insert a trade query parameter",
                },
                status=status.HTTP_422_UNPROCESSABLE_ENTITY,
            )

        # file_path = "ajo/trades.txt"

        # with open(file_path, "r") as file:
        #     # read the content of the file
        #     file_content = file.read()
        #     trades = file_content.split(sep="\n")

        # trade_search = [trade for trade in trades if trade_query.lower() in trade.lower()]

        sub_trade = AjoTradeSubCategory.objects.filter(name__icontains=trade_query)

        return Response(
            {
                "status": True,
                "data": sub_trade.values_list("name", flat=True),
            },
            status=status.HTTP_200_OK,
        )

    # def get_queryset(self):
    #     query_param = self.request.query_params.get("trade")
    #     if query_param:
    #         with open("trades.txt", "r") as file:
    #             trades = [line.strip() for line in file.readlines()]
    #         return [trade for trade in trades if query_param.lower() in trade.lower()]
    #     return []


class FillPersonalDetailsAPIView(GenericAPIView):
    serializer_class = FillPersonalDetailsSerializer
    permission_classes = (
        permissions.IsAuthenticated,
        AjoUserOnboardedPermission,
    )

    @swagger_auto_schema(
        request_body=FillPersonalDetailsSerializer,
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
        ],
    )
    def post(self, request, *args, **kwargs):
        """
        Fill in the personal details of the Ajo User
        """
        # obtain the phone number from the query params
        phone_number = request.query_params.get("phone_number")

        try:
            ajo_user = AjoUserSelector(
                phone_number=phone_number, user=request.user
            ).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        if ajo_user.onboarding_complete:
            return Response(
                data={
                    "status": False,
                    "message": "User Already Onboarded",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.serializer_class(
            data=request.data, context={"phone_number": phone_number}
        )

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        response_serializer = self.serializer_class(
            AjoUserService(ajo_user=ajo_user).update_ajo_user_fields(
                dict(serializer.validated_data)
            )
        )

        # ajo_user.onboarding_complete = True
        ajo_user.onboarding_stage = OnboardingStage.NEXT_OF_KIN
        ajo_user.save()

        return Response(
            data={
                "status": True,
                "message": "details updated successfully",
                "data": response_serializer.data,
            },
            status=status.HTTP_200_OK,
        )


class ImageCapturingAPIView(GenericAPIView):
    serializer_class = ImageCapturingSerializer
    permission_classes = (
        permissions.IsAuthenticated,
        AjoUserOnboardedPermission,
    )
    parser_classes = (MultiPartParser,)

    @swagger_auto_schema(
        request_body=ImageCapturingSerializer,
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
        ],
    )
    def post(self, request, *args, **kwargs):
        """
        Send a file of the picture of the Ajo User
        """
        # obtain the phone number from the query params
        phone_number = request.query_params.get("phone_number")

        # the permissisons check if it exists already
        try:
            ajo_user = AjoUserSelector(
                phone_number=phone_number, user=request.user
            ).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")
        # process the request data
        serializer = self.serializer_class(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        save_image = AjoUserService(ajo_user=ajo_user).save_image_for_ajo_user(
            image=serializer.validated_data.get("image")
        )

        if save_image.get("status"):
            ajo_user.onboarding_stage = OnboardingStage.ADDRESS_VERIFICATION
            ajo_user.save()
            # call the celery task to verify the BVN
            if ajo_user.bvn:
                verify_ajo_user_bvn.delay(
                    phone_number=phone_number,
                    user_id=request.user.id,
                    base_url=obtain_the_current_site(request),
                )

            # send the text message
            # if not ajo_user.sent_user_info_text:
            #     send_user_info_text.delay(ajo_user_id=ajo_user.id)

            return Response(
                {
                    "status": True,
                    "message": "image uploaded successfully",
                    "data": {
                        "image": save_image.get("image"),
                    },
                },
                status=status.HTTP_200_OK,
            )
        return Response(
            {
                "error": "801",
                "status": False,
                "message": "there was error while uploading, please try again",
            },
            status=status.HTTP_400_BAD_REQUEST,
        )


class AssignCardAPIView(GenericAPIView):
    """
    POST: assign a card to an ajo user
    GET: get all the cards an agent has
    """

    serializer_classes = {"GET": GetAgentCardsSerializer, "POST": AssignCardSerializer}
    queryset = Card.objects.all()
    pagination_class = CustomPagination

    def get_permissions(self):
        if self.request.method == "POST":
            return (
                permissions.IsAuthenticated(),
                AjoUserOnboardedPermission(),
            )
        else:
            return (permissions.IsAuthenticated(),)

    def get_serializer_class(self):
        return self.serializer_classes.get(self.request.method)

    @swagger_auto_schema(
        request_body=AssignCardSerializer,
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
        ],
    )
    def post(self, request, *args, **kwargs):
        """
        Put in the details for the card assignment
        """
        # Extract the phone number from the query parameters and the user
        phone_number = request.query_params.get("phone_number")
        user = request.user

        # Obtain the ajo user using the phone number and user information
        try:
            ajo_user = AjoUserSelector(
                phone_number=phone_number, user=user
            ).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        # Create a serializer instance and validate the data
        serializer = self.get_serializer_class()(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        if CardRequests.objects.filter(
            user=user, ajo_user=ajo_user, is_assigned=True
        ).exists():
            return value_error_response(error="User already has card", code="101")

        get_card_request = CardRequests.objects.filter(
            user=user, is_assigned=False
        ).first()
        if not get_card_request:
            return value_error_response(error="No Request Found Found", code="101")

        # Check if the user needs a card or not
        need_card = serializer.validated_data.get("need_card")

        ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)

        # obtain the Ajo User wallet
        ajo_user_wallet = ajo_user_selector.get_ajo_user_wallet()
        wallet_type = ajo_user_wallet.wallet_type
        amount = 0.00

        # amount = get_card_request.amount
        # if not check_if_agent_can_pay(
        #     agent_wallet=ajo_user_wallet,
        #     amount=amount,
        # ):
        #     return Response(
        #         {
        #             "error": "624",
        #             "status": False,
        #             "message": "the agent should top up his wallet",
        #         },
        #         status=status.HTTP_400_BAD_REQUEST,
        #     )

        ### create a withdrawal transaction instance####
        transaction_instance = (
            TransactionService.create_ajo_user_withdrawal_transaction(
                user=user,
                ajo_user=ajo_user,
                amount=amount,
                wallet_type=wallet_type,
                transaction_form_type="AJO_CARD_ASSIGNMENT",
                transaction_description=f"AJO_CARD_ASSIGNMENT for {ajo_user.alias}",
            )
        )

        # instantiate the transaction instance service
        transaction_service = TransactionService(
            transaction_instance=transaction_instance
        )

        # If the user needs a card, get the card number from the serializer
        # ajo_user.need_card = True
        # ajo_user.save()  # save here incase a card is not available

        # get the card number from the data
        card_number = serializer.validated_data.get("card_number")
        card_expiry_month = serializer.validated_data.get("card_expiry_month")
        card_expiry_year = serializer.validated_data.get("card_expiry_year")

        map_card = AgencyBankingClass.map_agent_card(
            card_pan=card_number,
            card_expiry_year=card_expiry_year,
            card_expiry_month=card_expiry_month,
            reference=str(get_card_request.reference),
        )

        if map_card.get("status"):
            card_id = map_card.get("data", {}).get("card_id")
            get_card_request.card_id = card_id
            get_card_request.ajo_user = ajo_user
            get_card_request.is_assigned = True
            get_card_request.date_assigned = datetime.now()
            get_card_request.save()

            # if card_number:
            #     # instantiate the card selector class
            #     card_selector = CardSelector(card_number=card_number, user=user)
            #     try:
            #         # attempt to get the card object
            #         card = card_selector.get_card_instance()
            #     except ValueError as err:
            #         return value_error_response(error=err, code="1012")

            #     # Check if the card is already assigned to an ajo user or agent
            #     if card_selector.check_if_card_assigned():
            #         return Response(
            #             {
            #                 "error": "1012",
            #                 "status": False,
            #                 "message": "this card is already assigned to an ajo user.",
            #             },
            #             status=status.HTTP_400_BAD_REQUEST,
            #         )

            #     # instantiate CardServices class
            #     card_service = CardService(card=card)

            #     # Assign the card to the ajo user
            #     card_data = dict(serializer.validated_data)
            #     card_data.pop("need_card")
            #     card_data.update(
            #         {
            #             "phone_number": phone_number,
            #             "ajo_user": ajo_user,
            #             "card_assigned": True,
            #         }
            #     )
            # {"full_name" = serializer.validated_data.get("full_name")
            # card.bvn = serializer.validated_data.get("bvn")
            # card.agent_bvn_used = serializer.validated_data.get("agent_bvn_used")
            # card.phone_number = phone_number
            # card.ajo_user = ajo_user
            # card.card_assigned = True}
            # with django_transaction.atomic():
            # card_service.update_card_fields(data=card_data)

            # Update ajo_user details to reflect the card assignmentr
            ajo_user.card_assigned = True
            ajo_user.card_issued = True
            ajo_user.save()

            update_transaction_status(
                wallet=ajo_user_wallet,
                transaction_instance=transaction_instance,
                success=True,
            )
            # ajo_user.agent_bvn_used = serializer.validated_data.get("agent_bvn_used")
            return Response(
                {
                    "status": True,
                    "message": "card assigned successfully",
                },
                status=status.HTTP_200_OK,
            )
        # if the card is not available
        update_transaction_status(
            wallet=ajo_user_wallet,
            transaction_instance=transaction_instance,
            success=False,
        )
        return Response(
            data={
                "status": False,
                "message": "card is unavailable, you can always come back to assign a card",
            },
            status=status.HTTP_400_BAD_REQUEST,
        )

    def get(self, request, *args, **kwargs):
        """
        This endpoint is used for obtaining all the cards available for the agent to
        assign to ajo users
        """
        card_qs = self.queryset.filter(user=request.user).order_by("-id")
        try:
            page = self.paginate_queryset(card_qs)
        except NotFound:
            return pagination_page_not_found_response

        if page is not None:
            serializer = self.get_serializer_class()(card_qs, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer_class()(card_qs, many=True)
        return Response(
            data={
                "status": True,
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )


class ConfirmCardAssignDetailsAPIView(GenericAPIView):
    serializer_class = ConfirmCardDetailsSerializer
    permission_classes = (
        permissions.IsAuthenticated,
        AjoUserOnboardedPermission,
    )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """
        this is used to get the information to confirm card details
        it returns the full name, the phone number and the BVN to be used incase
        the ajo user does not remit a BVN
        Please remit the information as is in assign cards post request
        """
        # obtain the phone number from the query params
        phone_number = request.query_params.get("phone_number")
        # obtain the user
        user = request.user

        # obtain the user
        try:
            ajo_user = AjoUserSelector(
                phone_number=phone_number, user=user
            ).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        # create a dictionary to hold all the data

        info = {
            "phone_number": ajo_user.phone_number,
            "first_name": ajo_user.first_name,
            "last_name": ajo_user.last_name,
        }

        # obtain the bvn for the details
        if ajo_user.bvn and ajo_user.bvn_verified:
            info["bvn"] = None
            info["agent_bvn_used"] = None
        else:
            # obtain the access token of the user
            # get the access token
            access_token = request.headers.get("Authorization", "").partition(" ")[-1]
            # try to obtain the BVN
            agent_bvn = UserService.get_user_bvn(user=user, access_token=access_token)
            info["bvn"] = None
            info["agent_bvn_used"] = None

        serializer = self.serializer_class(info)
        return Response({"status": True, "data": info}, status=status.HTTP_200_OK)


class AjoUsersBasedOnCardsAPIView(GenericAPIView):
    serializer_class = AjoUsersBasedOnCardsSerializer
    permission_classes = (permissions.IsAuthenticated,)
    queryset = AjoUser.objects.all()
    pagination_class = CustomPagination

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "filter",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="insert 'card', 'pending' or 'nocard' in the parameter to obtain a list",
            ),
            openapi.Parameter(
                "search_users",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="input characters to look for in first name, last name and alias",
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        """
        this is used to return a list of users who have gotten their cards, waiting for their
        cards or who do no need cards
        """
        list_of_valid_filters = ["card", "pending", "nocard"]

        # obtain the filter parameter
        base_filter = request.query_params.get("filter")
        search = request.query_params.get("search_users")

        if not base_filter:
            return Response(
                {
                    "error": "603",
                    "status": False,
                    "message": "there needs to be a 'filter' parameter in the request",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if base_filter not in list_of_valid_filters:
            return Response(
                {
                    "error": "603",
                    "status": False,
                    "message": "the filter should be one of ['card', 'pending', 'nocard']",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # get the agent
        user = request.user
        if base_filter == "card":
            ajo_user_qs = AjoUser.objects.filter(user=user, card_issued=True).order_by(
                "-id"
            )
        elif base_filter == "pending":
            # if the base_filter is "pending", it returns True, else it will return False
            ajo_user_qs = AjoUser.objects.none()

        elif base_filter == "nocard":
            ajo_user_qs = AjoUser.objects.filter(user=user, card_issued=False).order_by(
                "id"
            )
            # This will change

        # apply the search
        if search:
            ajo_user_qs = ajo_user_qs.filter(
                Q(first_name__icontains=search)
                | Q(last_name__icontains=search)
                | Q(alias__icontains=search)
            )

        # obtain the paginated results
        try:
            paginated_results = self.paginate_queryset(ajo_user_qs)
        except NotFound:
            return pagination_page_not_found_response()

        # serialize the paginated resullts
        serializer = self.serializer_class(paginated_results, many=True)

        # return the paginated response
        return self.get_paginated_response(serializer.data)

        # serializer = self.serializer_class(ajo_user_qs, many=True)
        # return Response(
        #     {
        #         "status": True,
        #         "data": serializer.data,
        #     },
        #     status=status.HTTP_200_OK,
        # )


class CardRequestByAgentAPIView(GenericAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_classes = {
        "POST": RequestCardSerializer,
        "GET": DeliveryAddressSerializer,
    }

    def get_serializer_class(self):
        return self.serializer_classes.get(self.request.method)

    @swagger_auto_schema(
        request_body=RequestCardSerializer,
    )
    def post(self, request, *args, **kwargs):
        """
        Fill this to obtain
        """

        # obtain the agent
        user = request.user

        # obtain the user's access token
        access_token = request.headers.get("Authorization", "").partition(" ")[-1]

        serializer = self.get_serializer_class()(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        # obtain the information from the serializers
        quantity = int(serializer.validated_data.get("quantity"))
        agent_transaction_pin = serializer.validated_data.get("agent_transaction_pin")

        # check the agent's ajo pin
        verify_agent_pin = verify_transaction_pin(
            transaction_pin=agent_transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )
        if isinstance(verify_agent_pin, Response):
            return verify_agent_pin

        # Remove Transaction Pin from Request Data
        request.data.pop("agent_transaction_pin")

        # instantiate AjoAgentSelector class
        ajo_agent_methods = AjoAgentSelector(user=user)

        get_card_price = AgencyBankingClass.get_liberty_card_price()
        card_price = get_card_price.get("price")

        if not card_price:
            return Response(
                {
                    "error": "624",
                    "status": False,
                    "message": "COuld Not Obtain Card Price",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        amount = quantity * card_price
        unique_reference = uuid.uuid4()

        # obtain the wallet
        agent_wallet = ajo_agent_methods.get_agent_ajo_wallet()
        wallet_type = agent_wallet.wallet_type

        if not check_if_agent_can_pay(
            agent_wallet=agent_wallet,
            amount=amount,
        ):
            return Response(
                {
                    "error": "624",
                    "status": False,
                    "message": "the agent should top up his wallet",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        ### create a withdrawal transaction instance####
        transaction_instance = TransactionService.dynamic_deduction_from_wallet_transaction(
            user=user,
            amount=amount,
            wallet_type=wallet_type,
            request_data=json.dumps(request.data),
            transaction_form_type=TransactionFormType.CARD_REQUEST,
            description=f"{user.email} requested for {quantity} cards and paid {amount}.",
        )

        # instantiate the transaction instance service
        transaction_service = TransactionService(
            transaction_instance=transaction_instance
        )

        dynamic_withdraw_from_ajo_agent_wallet(
            amount=amount,
            transaction_instance=transaction_instance,
            agent_wallet=agent_wallet,
        )

        send_card_request_data = []
        for _ in range(quantity):
            validated_data_dict = dict(
                user=user,
                amount=amount,
                quantity=quantity,
                unique_reference=unique_reference,
                reference=f"{user.email}_{uuid.uuid4()}",
                state=serializer.validated_data.get("state"),
                lga=serializer.validated_data.get("lga"),
                nearest_landmark=serializer.validated_data.get("nearest_landmark"),
                street=serializer.validated_data.get("nearest_landmark"),
                # address_type = serializer.validated_data.get("nearest_landmark")
            )

            try:
                card_request = CardRequests.create_card_request(
                    card_request_data=validated_data_dict
                )

                send_request_for_card = AgencyBankingClass.agent_card_request(
                    reference=str(card_request.reference)
                )

                send_card_request_data.append(send_request_for_card)

            except ValueError as err:
                return Response(
                    {
                        "error": "603",
                        "status": False,
                        "message": str(err),
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        return Response(
            {
                "status": True,
                "message": "request created successfully",
                "data": serializer.data,
                "card_details": send_card_request_data,
            },
            status=status.HTTP_201_CREATED,
        )

    @swagger_auto_schema(
        responses={
            200: DeliveryAddressSerializer,
        },
    )
    @method_decorator(cache_page(60))
    def get(self, request, *args, **kwargs):
        """
        this endpoint returns delivery addresses that have been utilized in the past
        it also includes a count field to show how many times an address has been used in the past
        """
        # obtain the agent
        user = request.user

        address_qs = (
            CardRequests.get_unique_card_request_delivery_location_combinations(
                user=user
            )
        )
        serializer = self.get_serializer_class()(address_qs, many=True)

        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )


class AllCustomersGroupsListAPIView(GenericAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    pagination_class = CustomPagination
    # serializer_classes = {"customer": AjoUsersBasedOnCardsSerializer, "group": GroupSerializer}
    serializer_classes = {
        "customer": ExistingAjoUserSerializer,
        "group": GroupSerializer,
    }

    def get_serializer_class(self, filter: str = None):
        return self.serializer_classes.get(filter)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "filter",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="insert 'customer' or 'group' to get a list",
            ),
            openapi.Parameter(
                "search_users",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="input characters to look for in first name, last name and alias",
            ),
            openapi.Parameter(
                "sort",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="'true' -> sort alphabetically, 'false' -> sort in reverse, null -> sort by order",
            ),
        ],
        responses={
            200: AjoUsersBasedOnCardsSerializer(
                many=True
            ),  # Response for Customer filter
            201: GroupSerializer(many=True),  # Response for group filter
        },
    )
    def get(self, request, *args, **kwargs):
        """
        This gets all the ajo users or groups under an ajo agent
        """

        # obtain the filter paramter from the request
        filter = request.query_params.get("filter")
        search = request.query_params.get("search_users")
        sort_q = request.query_params.get("sort")
        if sort_q:
            sort_q.lower()

        # if filter is None or not in the list of valid filters
        if not filter or filter not in self.serializer_classes:
            return Response(
                {
                    "error": "603",
                    "status": False,
                    "message": "please include 'customer' or 'group' in the filter parameter of the request",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # obtain the agent
        user = request.user

        if filter == "customer":
            qs = (
                AjoUser.objects.select_related("user").filter(user=user).order_by("-id")
            )
            if search:
                qs = qs.filter(
                    Q(first_name__icontains=search)
                    | Q(last_name__icontains=search)
                    | Q(alias__icontains=search)
                )
            if sort_q:
                if sort_q == "true":
                    qs = qs.order_by("first_name")
                elif sort_q == "false":
                    qs = qs.order_by("-first_name")
                else:
                    pass

        else:
            qs = (
                RotationGroup.objects.select_related("user")
                .filter(user=user)
                .order_by("-id")
            )

        # obtain the paginated results
        try:
            paginated_results = self.paginate_queryset(qs)
        except NotFound:
            return pagination_page_not_found_response()

        # serialize the paginated results
        serializer = self.get_serializer_class(filter=filter)(
            paginated_results, many=True
        )

        #
        if filter == "customer":
            for data in serializer.data:
                phone_number = data.get("phone_number")
                ajo_selector = AjoUserSelector(phone_number=phone_number, user=user)
                data["wallet_balance"] = ajo_selector.get_ajo_user_wallet_balance()
                data["has_active_plan"] = ajo_selector.has_active_ajo_plan()

        # return the paginated response
        return self.get_paginated_response(serializer.data)

        # return Response(
        #     {
        #         "status": True,
        #         "data": serializer.data,
        #     },
        #     status=status.HTTP_200_OK,
        # )


class ExistingUserSavingsListAPIView(GenericAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_classes = {
        "POST": ExistingUserSavingsSerializer,
        "GET": AjoSavingsInformationSerializer,
    }
    pagination_class = CustomPagination

    def get_serializer_class(self):
        return self.serializer_classes.get(self.request.method)

    def get_permissions(self):
        if self.request.method == "POST":
            return (permissions.IsAuthenticated(),)
        else:
            return (
                permissions.IsAuthenticated(),
                AjoUserOnboardedPermission(),
            )

    def post(self, request, *args, **kwargs):
        """
        Use the phone number and pin to login to the details for an Ajo user
        """
        # obtain the user
        user = request.user

        # attempt to validate the request
        serializer = self.get_serializer_class()(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        # obtain the phone number
        phone_number = serializer.validated_data.get("phone_number")
        pin = serializer.validated_data.get("pin")

        # instantiate
        ajo_user_selector = AjoUserSelector(phone_number=phone_number, user=user)

        # pass them into the selector that does the checking
        try:
            checks = ajo_user_selector.match_pin(pin=pin)
        except ValueError as err:
            return value_error_response(error=err)

        if checks:
            return Response(
                {
                    "status": True,
                    "data": ExistingAjoUserSerializer(
                        ajo_user_selector.get_ajo_user()
                    ).data,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {
                    "error": "744",
                    "status": False,
                    "message": "incorrect pin or phone number",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of the ajo user",
            ),
            openapi.Parameter(
                "is_active",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                required=False,
                description="filter the ajo savings list by is_active",
            ),
        ],
        responses={
            200: AjoSavingsInformationSerializer,
        },
    )
    def get(self, request, *args, **kwargs):
        """
        Get the list of Ajo Savings an Ajo user has
        """
        # obtain the phone number of the ajo user
        phone_number = request.query_params.get("phone_number")
        is_active = request.query_params.get("is_active")
        is_active = json.loads(is_active) if is_active else None
        # obtain the user
        user = request.user

        # get the list of ajo savings
        try:
            paginated_results = self.paginate_queryset(
                AjoUserSelector(
                    phone_number=phone_number,
                    user=user,
                ).get_ajo_user_ajo_savings(is_active=is_active)
            )
        except ValueError as err:
            return value_error_response(error=err)

        except NotFound:
            return pagination_page_not_found_response()

        # serialize the paginated results
        serializer = self.get_serializer_class()(paginated_results, many=True)
        return self.get_paginated_response(serializer.data)


class ExistingAjoUserBalancesAPIView(GenericAPIView):
    serializer_class = AjoUserTotalBalancesSerializer
    permission_classes = (permissions.IsAuthenticated,)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of the ajo user",
            ),
        ],
        responses={
            200: AjoUserTotalBalancesSerializer,
        },
    )
    def get(self, request, *args, **kwargs):
        """
        Get the total balance and total number of active savings
        an ajo user has.
        """
        # get the phone number and the user
        phone_number = request.query_params.get("phone_number")
        user = request.user

        # get the total number of ajo savings
        ajo_user_selector = AjoUserSelector(phone_number=phone_number, user=user)
        data = {}
        try:
            data["total_active_savings"] = (
                ajo_user_selector.get_number_of_active_ajo_user_savings()
            )
            data["total_amount"] = (
                ajo_user_selector.get_ajo_user_wallet_balance()
                if isinstance(ajo_user_selector.get_ajo_user_wallet_balance(), float)
                else 0.00
            )
            data["savings_wallet_balance"] = (
                (ajo_user_selector.get_digital_wallet_balance())
                if isinstance(ajo_user_selector.get_digital_wallet_balance(), float)
                else 0.00
            )
            data["spending_wallet_balance"] = (
                (ajo_user_selector.get_spending_wallet_balance())
                if isinstance(ajo_user_selector.get_spending_wallet_balance(), float)
                else 0.00
            )

            data["wallet_number"] = (
                (ajo_user_selector.generate_and_set_spending_wallet_number())
                if isinstance(
                    ajo_user_selector.generate_and_set_spending_wallet_number(), float
                )
                else 0.00
            )
            data["loan_escrow_wallet_balance"] = (
                (ajo_user_selector.get_loan_escrow_wallet_balance())
                if isinstance(ajo_user_selector.get_loan_escrow_wallet_balance(), float)
                else 0.00
            )
            data["loan_disbursement_wallet_balance"] = (
                (ajo_user_selector.get_loan_disbursement_wallet_balance())
                if isinstance(
                    ajo_user_selector.get_loan_disbursement_wallet_balance(), float
                )
                else 0.00
            )

        except ValueError as err:
            return value_error_response(error=err, code="1012")

        # pass the data to the serializer
        serializer = self.serializer_class(data)

        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status.HTTP_200_OK,
        )


class AjoUserTransactionHistory(GenericAPIView):
    pagination_class = CustomPagination
    permission_classes = (
        permissions.IsAuthenticated,
        AjoUserOnboardedPermission,
    )
    serializer_class = TransactionHistoryForAjoUserSerializer

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of the ajo user",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """
        This endpoints returns all the transactions that an ajo user has.
        """
        # obtain the phone number of the ajo user
        phone_number = request.query_params.get("phone_number")
        # obtain the user
        user = request.user

        # get the transaction history
        try:
            paginated_results = self.paginate_queryset(
                AjoUserSelector(
                    phone_number=phone_number,
                    user=user,
                ).get_ajo_user_transaction_history()
            )
        except ValueError as err:
            return value_error_response(error=err)

        except NotFound:
            return pagination_page_not_found_response()

        # serialize the paginated results
        serializer = self.get_serializer_class()(paginated_results, many=True)
        return self.get_paginated_response(serializer.data)


class AjoSavingDetailAPIView(GenericAPIView):
    serializer_class = AjoSavingFullDetailsSerializer
    permission_classes = (permissions.IsAuthenticated,)
    pagination_class = CustomPagination

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=True,
                description="the id of the ajo savings plan",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """
        This endpoint is for getting the details of an Ajo Plan as well as its transaction history
        """
        # obtain the id
        id = request.query_params.get("id")
        # obtain the user
        user = request.user

        # get the information needed
        try:
            ajo_saving_methods = AjoSavingsSelector(id=id, user=user)
            paginated_transactions = self.paginate_queryset(
                ajo_saving_methods.get_ajo_saving_transaction_history()
            )
        except ValueError as err:
            return value_error_response(error=err)
        except NotFound:
            return pagination_page_not_found_response()

        serializer = AjoSavingsInformationSerializer(
            ajo_saving_methods.get_ajo_saving_plan()
        )

        # filtered_transactions = [
        #     transaction for transaction in paginated_transactions if transaction.transaction_form_type != "COMMISSION"
        # ]

        transactions_serializer = TransactionHistoryForAjoUserSerializer(
            paginated_transactions, many=True
        )

        # transactions_serializer = TransactionHistoryForAjoUserSerializer(paginated_transactions, many=True)
        response_data = serializer.data
        response_data["transactions"] = transactions_serializer.data
        return self.get_paginated_response(response_data)

        # # serialize the data
        # serializer = self.serializer_class(data)
        # return Response(
        #     {
        #         "status": True,
        #         "data": serializer.data,
        #     },
        #     status=status.HTTP_200_OK,
        # )


class CreateAjoSavingsPlanAPIView(GenericAPIView):
    serializer_class = CreateAjoSavingsPlanSerializer
    permission_classes = (
        permissions.IsAuthenticated,
        AllowSavingsPermission,
        CanCreatePlanPermission,
        IsBlackListed,
    )
    queryset = AjoSavingsPlanType.objects.exclude(
        Q(name="Starter") | ~Q(frequency="DAILY")
    )

    @swagger_auto_schema(
        request_body=CreateAjoSavingsPlanSerializer,
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="phone number of ajo user",
            ),
            openapi.Parameter(
                "lock",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                required=False,
                description="select if plan should be locked or not",
            ),
            openapi.Parameter(
                name="loan",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                required=False,
                description="is this a loan savings or not",
            ),
            openapi.Parameter(
                name="savingstype",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="bnpl/snpl",
            ),
        ],
    )
    def post(self, request, *args, **kwargs):
        """
        create a plan for an ajo user
        """
        # obtain the access token
        # access_token = request.headers.get("Authorization", "").partition(" ")[-1]

        # obtain the user
        user = request.user

        # obtain the phone number from the query params
        phone_number = request.query_params.get("phone_number")
        lock = request.query_params.get("lock")

        business_suite = request.query_params.get("business_suite")
        business_suite = json.loads(business_suite) if business_suite else False

        lock = json.loads(lock) if lock else None
        if lock is None:
            lock = False

        loan = request.query_params.get("loan")
        loan = json.loads(loan) if loan else None
        if loan is None:
            loan = False

        savingstype: str | None = request.query_params.get("savingstype")
        if savingstype:
            savingstype = savingstype.upper()
            if savingstype not in SavingsType:
                return error_response(
                    message=f"choose from the following: {list(SavingsType.__members__.keys())}"
                )
            else:
                savingstype = getattr(SavingsType, savingstype)

        if loan and savingstype:
            return error_response(
                message="an ajo savings plan cannot be loan and buy/save now pay later"
            )

        # validate the data
        serializer = self.serializer_class(
            data=request.data, context={"savingstype": savingstype}
        )

        # attempt to validate the data
        try:
            serializer.is_valid(raise_exception=True)

        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        # obtain the ajo_type
        ajo_type = serializer.validated_data.pop("ajo_type")

        ajo_user: AjoUser | None = None

        # AGENT Type that onboards users
        if ajo_type == AjoType.AGENT:
            if not phone_number:
                return Response(
                    {
                        "error": "690",
                        "status": False,
                        "message": "phone number of ajo user is required as a query parameter in this request",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # checkout the ajo user
            try:
                ajo_user = AjoUserSelector(
                    phone_number=phone_number,
                    user=user,
                ).get_ajo_user()
            except ValueError as err:
                return value_error_response(err, "1012")
            if not ajo_user.onboarding_verified:
                return Response(
                    {
                        "error": "1012",
                        "status": False,
                        "message": "this ajo user is not verified, please properly onboard this user",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        # PERSONAL TYPE
        else:
            # if the phone number parameter was passed
            if phone_number:
                return Response(
                    {
                        "error": "697",
                        "status": False,
                        "message": "this user type is not allowed to create an ajo savings plan for an onboarded ajo user",
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )

        # check if the next person must belong to a group
        agent_selector = AjoAgentSelector(user=user)
        must_be_group = agent_selector.is_next_loan_savings_group()

        group_id = serializer.validated_data.pop("group", None)
        group = None

        if must_be_group and not group_id:
            return error_response(message="the next savings must be group")

        if group_id:
            try:
                group_selector = AjoSepoSelector(group_id=group_id, user=user)
                group = group_selector.get_group()
                group_selector.base_checks_for_group(loan=loan, ajo_user=ajo_user)
                savingstype = SavingsType.AJOSEPO
            except ValueError as err:
                return value_error_response(error=err)

        boosta_tenure = serializer.validated_data.get("boosta_tenure")
        boosta_xtype = serializer.validated_data.get("boosta_xtype", None)

        if not boosta_xtype and boosta_tenure:
            savingstype = SavingsType.BOOSTA
        elif boosta_xtype == "BOOSTA_2X":
            savingstype = SavingsType.BOOSTA_2X
        elif boosta_xtype == "BOOSTA_2X_MINI":
            savingstype = SavingsType.BOOSTA_2X_MINI

        # prepare data
        ajo_savings_plan_data = dict(serializer.validated_data)
        # ajo_savings_plan_data.pop("ajo_type")
        ajo_savings_plan_data.update(
            {
                "user": user,
                "ajo_user": ajo_user,
                "commission_amount": serializer.validated_data["periodic_amount"],
                "lock": lock,
                "loan": loan,
                "savings_type": savingstype,
                "group": group,
                "must_belong_to_group": must_be_group,
                "business_suite": business_suite,
            }
        )

        # try to create the Ajo Savings plan
        try:
            ajo_saving = AjoSavingsService.create_ajo_savings(
                data=ajo_savings_plan_data
            )
        except ValueError as err:
            return value_error_response(error=err)

        plan_serializer = AjoSavingsInformationSerializer(ajo_saving)

        return Response(
            {
                "status": True,
                "message": "plan created successfully",
                "data": plan_serializer.data,
            },
            status=status.HTTP_201_CREATED,
        )

    @swagger_auto_schema(
        responses={
            200: AjoSavingsPlanTypesSerializer,
        }
    )
    def get(self, request, *args, **kwargs):
        """
        returns the plan types that a user or ajo user can utilize when creating a plan
        """
        return Response(
            {
                "status": True,
                "data": AjoSavingsPlanTypesSerializer(
                    self.get_queryset(), many=True
                ).data,
            }
        )


class V2CreateAjoSavingsPlanAPIView(GenericAPIView):
    serializer_class = GetPlansSerializer
    permission_classes = (
        permissions.IsAuthenticated,
        AllowSavingsPermission,
        CanCreatePlanPermission,
        IsBlackListed,
    )

    @swagger_auto_schema(
        request_body=GetPlansSerializer,
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="phone number of ajo user",
            ),
            openapi.Parameter(
                "plan_name",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="name of plan of ajo savings",
            ),
            openapi.Parameter(
                "ajo_type",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="ajo agent type. Has to be AGENT or PERSONAL",
            ),
            openapi.Parameter(
                "lock",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                required=False,
                description="select if plan should be locked or not",
            ),
            openapi.Parameter(
                name="loan",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                required=False,
                description="is this a loan savings or not",
            ),
            openapi.Parameter(
                name="savingstype",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="bnpl/snpl",
            ),
        ],
    )
    def post(self, request, *args, **kwargs):
        """
        create a plan for an ajo user or for personal user
        """
        # obtain the user
        user = request.user

        # obtain the phone number from the query params
        plan_name = request.query_params.get("plan_name")
        phone_number = request.query_params.get("phone_number", None)
        business_suite = request.query_params.get("business_suite")
        business_suite = json.loads(business_suite) if business_suite else False

        ajo_type = request.query_params.get("ajo_type")
        if ajo_type:
            ajo_type = ajo_type.upper()

        lock = request.query_params.get("lock")
        lock = json.loads(lock) if lock else None
        if not lock:
            lock = False

        loan = request.query_params.get("loan")
        loan = json.loads(loan) if loan else None
        if loan is None:
            loan = False

        savingstype: str | None = request.query_params.get("savingstype")
        if savingstype:
            savingstype = savingstype.upper()
            if savingstype not in SavingsType:
                return error_response(
                    message=f"choose from the following: {list(SavingsType.__members__.keys())}"
                )

        if loan and savingstype:
            return error_response(
                message="an ajo savings plan cannot be loan and buy/save now pay later"
            )

        # check for the required query parameters
        if not plan_name or not ajo_type:
            return Response(
                {
                    "error": "690",
                    "status": False,
                    "message": "ajo_type and plan_name are required as query parameters in this request",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # validate the data
        serializer = self.serializer_class(data=request.data)

        # attempt to validate the data
        try:
            serializer.is_valid(raise_exception=True)

        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        duration_in_days = serializer.validated_data.get("duration_in_days")
        frequency = serializer.validated_data.get("frequency")
        target = serializer.validated_data.get("target")

        if not target:
            return value_error_response(error="target must be passed")
        if duration_in_days < 31:
            return value_error_response(
                error="duration in days must be greater than 30"
            )

        # default values
        ajo_user = None

        # AGENT Type that onboards users
        if (not ajo_type == AjoType.AGENT) and phone_number:
            return Response(
                {
                    "error": "697",
                    "status": False,
                    "message": "this user type is not allowed to create an ajo savings plan for an onboarded ajo user",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        if ajo_type == AjoType.AGENT:
            if not phone_number or not plan_name:
                return Response(
                    {
                        "error": "690",
                        "status": False,
                        "message": "phone_number of ajo user and plan_name is required as a query parameter in this request",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # checkout the ajo user
            try:
                ajo_user = AjoUserSelector(
                    phone_number=phone_number,
                    user=user,
                ).get_ajo_user()
            except ValueError as err:
                return value_error_response(err, "1012")

            if not ajo_user.onboarding_verified:
                return Response(
                    {
                        "error": "1012",
                        "status": False,
                        "message": "this ajo user is not verified, please properly onboard this user",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        elif ajo_type == AjoType.PERSONAL:
            pass

        # prepare data
        try:
            get_plans = AjoSavingsPlanType.calculate_weekly_monthly_target_plan(
                frequency=frequency,
                duration_in_days=duration_in_days,
                target=target,
            )
        except Exception as err:
            response = {"status": "error", "message": f"{err}"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        # check if the next person must belong to a group
        agent_selector = AjoAgentSelector(user=user)
        must_be_group = agent_selector.is_next_loan_savings_group()

        group_id = serializer.validated_data.pop("group", None)
        group = None

        if must_be_group and not group_id:
            return error_response(message="the next savings must be group")

        if group_id:
            try:
                group_selector = AjoSepoSelector(group_id=group_id, user=user)
                group = group_selector.get_group()
                group_selector.base_checks_for_group(loan=loan, ajo_user=ajo_user)
                savingstype = SavingsType.AJOSEPO
            except ValueError as err:
                return value_error_response(error=err)

        boosta_tenure = serializer.validated_data.get("boosta_tenure")
        boosta_xtype = serializer.validated_data.get("boosta_xtype", None)

        if not boosta_xtype and boosta_tenure:
            savingstype = SavingsType.BOOSTA
        elif boosta_xtype == "BOOSTA_2X":
            savingstype = SavingsType.BOOSTA_2X
        elif boosta_xtype == "BOOSTA_2X_MINI":
            savingstype = SavingsType.BOOSTA_2X_MINI

        ajo_savings_plan_data = {
            "name": plan_name,
            "duration": duration_in_days,
            "frequency": frequency,
            "plan_data": get_plans,
            "user": user,
            "ajo_user": ajo_user,
            "commission_amount": get_plans["commission"],
            "periodic_amount": get_plans["contribution"],
            "lock": lock,
            "loan": loan,
            "savings_type": savingstype,
            "group": group,
            "must_belong_to_group": must_be_group,
            "boosta_tenure": boosta_tenure,
            "business_suite": business_suite,
        }

        # try to create the Ajo Savings plan
        try:
            ajo_saving = AjoSavingsService.create_ajo_savings(
                data=ajo_savings_plan_data
            )
        except ValueError as err:
            return value_error_response(error=err)

        plan_serializer = AjoSavingsInformationSerializer(ajo_saving)

        return Response(
            {
                "status": True,
                "message": "plan created successfully",
                "data": plan_serializer.data,
            },
            status=status.HTTP_201_CREATED,
        )


class UserPinAPIView(GenericAPIView):
    serializer_class = AjoUserPinSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        # obtain the user
        user = request.user

        # validate the data
        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        # obtain the phone number and pin
        phone_number = serializer.validated_data.get("phone_number")
        pin = serializer.validated_data.get("pin")

        # try to match it
        try:
            check = AjoUserSelector(phone_number=phone_number, user=user).match_pin(
                pin=pin
            )
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        return Response(
            {
                "match": check,
            },
            status=status.HTTP_200_OK,
        )


class AgentLedgerAPIView(GenericAPIView):
    serializer_class = AgentLedgerSerializer
    permission_classes = (permissions.IsAuthenticated,)
    pagination_class = CustomPagination

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "date",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="insert date in the format 'YYYY-MM-DD' e.g. '2023-05-15'",
            ),
        ],
        responses={200: TransactionSummarySerializer},
    )
    def get(self, request, *args, **kwargs):
        # obtain the date and user
        date: str | None = request.query_params.get("date")
        user = request.user

        agent_methods = AjoAgentSelector(user=user)
        data = {
            "total_cash_collection": agent_methods.get_total_cash_saved(
                complete_date=date
            ),
            "total_cash_withdrawn": agent_methods.get_total_withdrawal(
                complete_date=date
            ),
        }
        transactions = agent_methods.get_transaction_history_summary_for_agent(
            complete_date=date, plans_only=True
        )

        try:
            paginated_transactions = self.paginate_queryset(transactions)
        except NotFound:
            return pagination_page_not_found_response()

        serializer = self.serializer_class(data)
        transactions_serializer = GeneralAjoTransactionHistorySerializer(
            paginated_transactions, many=True
        )

        response_data = serializer.data
        response_data["transactions"] = transactions_serializer.data

        return self.get_paginated_response(response_data)


class GetVirtualAccountDetailsAPIView(GenericAPIView):
    serializer_class = VirtualAccountDetailsSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request, *args, **kwargs):
        # obtain the user
        user = request.user

        # instantiate the class
        bank_account_selector = BankAccountSelector(user=user)

        # get the account
        try:
            account_details_instance = (
                bank_account_selector.get_agent_account_instance()
            )
        except ValueError as err:
            if "virtual account" in str(err):
                # call the endpoint to call for an account
                # create_agent_vfd_virtual_wallet.delay(user_id=user.id)
                account_details = BankAccountService.create_agent_virtual_account(
                    user=user,
                    form_type=AccountFormType.AGENT,
                )

                if account_details.get("status") is True:
                    serializer = self.serializer_class(account_details.get("data"))
                    return Response(
                        {
                            "status": True,
                            "data": serializer.data,
                        },
                        status=status.HTTP_200_OK,
                    )

                # return Response(
                #     {
                #         "status": False,
                #         "error": "901",
                #         "message": "please make a request to this endpoint again to obtain your account details",
                #     },
                #     status=status.HTTP_400_BAD_REQUEST,
                # )

            return Response(
                {
                    "status": False,
                    "error": "985",
                    "message": "there was a problem, please contact customer care",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.serializer_class(account_details_instance)
        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )


#  Card Authorization
class CardDebitGatewayView(APIView):
    permission_classes = (VFDWebhookWhitelist,)

    def post(self, request):
        # You will need to save every payload

        request_payload = request.data

        # Get IP ADDRESS
        ip_addr = request.META.get("HTTP_X_REAL_IP")

        # You might have a better way of handling this. I see you have some Webhook and IpAddress Models. Dont wanna mess with them.

        if (
            ip_addr != settings.CARD_PURCHASE_IP
            and settings.ENVIRONMENT == "production"
        ):
            response = {
                "statusCode": 400,
                "responseCode": "99",
                "data": {"message": "Incorrect IP."},
            }

            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        environment = request_payload.get("environment")
        request_type = request_payload.get("type")

        if request_type == "card.balance":
            card_id = (
                request_payload.get("data", {"object": {"_id": "null"}})
                .get("object")
                .get("_id")
            )

        elif request_type == "authorization.request":
            if (
                ConstantTable.get_constant_table_instance().withdraw_from_wallet_regulator
                == False
                or environment != "production"
            ):
                response = {
                    "statusCode": 400,
                    "responseCode": "99",
                    "data": {
                        "message": f"Withdrawal Unavailable. Environment {environment}"
                    },
                }

                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            card_id = (
                request_payload.get("data", {"object": {"card": {"_id": "null"}}})
                .get("object")
                .get("card")
                .get("_id")
            )

        elif request_type == "transaction.created":
            card_id = (
                request_payload.get("data", {"object": {"card": "null"}})
                .get("object")
                .get("card")
            )

        elif request_type == "transaction.refund":
            card_id = (
                request_payload.get("data", {"object": {"card": "null"}})
                .get("object")
                .get("card")
            )

        try:
            customer = CardRequests.objects.get(
                card_id=card_id, is_frozen=False, ajo_user__isnull=False
            )
        except:
            response = {
                "statusCode": 400,
                "responseCode": "99",
                "data": {"message": "Card Does Not Exist or Card is not Active"},
            }

            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        ajo_user = customer.ajo_user
        user = customer.user

        ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)

        # obtain the Ajo User wallet
        wallet_instance = ajo_user_selector.get_spending_wallet()
        wallet_type = wallet_instance.wallet_type

        user_balance = wallet_instance.available_balance

        if request_type == "card.balance":
            response = {
                "statusCode": 200,
                "responseCode": "00",
                "data": {
                    "balance": user_balance,
                },
            }
            return Response(response, status=status.HTTP_200_OK)

        elif request_type == "authorization.request":
            unique_reference = request_payload.get("data").get("object").get("_id")
            trans_channel = (
                request_payload.get("data")
                .get("object")
                .get("transactionMetadata")
                .get("channel")
            )
            trans_meta_type = (
                request_payload.get("data")
                .get("object")
                .get("transactionMetadata")
                .get("type")
            )
            transaction_amount = (
                request_payload.get("data")
                .get("object")
                .get("pendingRequest")
                .get("amount")
            )
            provider_fee = request_payload.get("data").get("object").get("fee")
            liberty_commission = (
                ConstantTable.get_constant_table_instance().cards_liberty_comm
            )
            total_fee = provider_fee + liberty_commission
            total_amount = transaction_amount + liberty_commission
            amount_without_comm = total_amount - total_fee
            transfer_type = "CARD_PURCHASE"
            narration = f"{trans_channel}_{trans_meta_type}"

            if not check_if_agent_can_pay(
                agent_wallet=wallet_instance,
                amount=total_amount,
            ):
                return Response(
                    {
                        "error": "624",
                        "status": False,
                        "message": "the agent should top up his wallet",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            ### create a withdrawal transaction instance####
            transaction_instance = (
                TransactionService.create_ajo_user_withdrawal_transaction(
                    user=user,
                    ajo_user=ajo_user,
                    amount=total_amount,
                    wallet_type=wallet_type,
                    transaction_description=transfer_type,
                    unique_reference=unique_reference,
                )
            )

            # instantiate the transaction instance service
            transaction_service = TransactionService(
                transaction_instance=transaction_instance
            )

            debit_wallet_and_update_transaction(
                amount=total_amount,
                wallet=wallet_instance,
                transaction_instance=transaction_instance,
            )

            response = {"statusCode": 200, "responseCode": "00", "data": None}
            return Response(response, status=status.HTTP_200_OK)

        elif request_type == "transaction.refund":
            # Please handle this

            # process_refund_trans_task.apply_async(
            #     queue="resolvecardpur",
            #     kwargs={
            #         "request_payload": request_payload,
            #         "raw_data_id": raw_sudo_card.id
            #     }
            # )

            response = {
                "statusCode": 200,
                "responseCode": "00",
                "data": {"metadata": "Now in Queue"},
            }

            return Response(response, status=status.HTTP_200_OK)

        else:
            response = {
                "statusCode": 400,
                "responseCode": "99",
                "data": {"message": "Bad Data"},
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)


class AgencyBankingAgentInfoAPIView(GenericAPIView):
    serializer_class = AgencyBankingAgentInfoSerializer
    permission_classes = (HookAuthorizationPermission,)

    def post(self, request, *args, **kwargs):
        # attempt to validate the serializer
        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        # obtain the user id
        user_id = serializer.validated_data.get("user_id")

        # try to obtain the user on my server
        try:
            user = get_user_model().objects.get(customer_user_id=user_id)
        except get_user_model().DoesNotExist:
            return Response(
                {
                    "status": False,
                    "error": "404",
                    "message": "agent with this ID does not exist on our server",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        ajo_agent_selector = AjoAgentSelector(user=user)

        return Response(
            {
                "status": True,
                "data": {
                    "total_ajo_users": ajo_agent_selector.get_total_ajo_users_count(),
                },
            },
            status=status.HTTP_200_OK,
        )


# ROTATIONGROUP
class CreateRotationGroup(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request):
        serializer = AgentCreateRotationGroupSerializer(
            data=request.data,
            context={"user": request.user, "request": request, "create_group": True},
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        name = data.get("name")
        # no_of_collectors = data.get("no_of_collectors")
        existing_group = RotationGroup.objects.filter(
            user=request.user, name=name
        ).first()

        if existing_group:
            response_data = {
                "status": "Error",
                "message": f"A group with the name '{name}' already exists for this user. Please select another group name",
            }
            return Response(response_data, status=status.HTTP_400_BAD_REQUEST)

        create_group_result = RotationGroup.create_group(user=request.user, **data)
        WalletSystem().create_rotation_group_wallet(
            user=request.user, rotation_group_id=create_group_result.id
        )
        response_data = {
            "status": "Success",
            "contribution_amount": create_group_result.contribution_amount,
            "collection_amount": create_group_result.collection_amount,
            "agent_commission": create_group_result.agent_commission,
            "duration": (
                f"{create_group_result.duration} months"
                if create_group_result.frequency == "MONTHLY"
                else (
                    f"{create_group_result.duration} weeks"
                    if create_group_result.frequency == "WEEKLY"
                    else (
                        f"{create_group_result.duration} days"
                        if create_group_result.frequency == "DAILY"
                        else f"{create_group_result.duration} {create_group_result.frequency.lower()}"
                    )
                )
            ),
        }
        return Response(response_data, status=status.HTTP_200_OK)


class RotationBreakdown(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request):
        serializer = RotationbreakdownSerializer(
            data=request.data,
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        amount = data.get("amount")
        number_of_participants = data.get("number_of_participants")
        collection_type = data.get("collection_type")
        rosca_type = data.get("rosca_type")
        no_of_collectors = data.get("no_of_collectors")
        frequency = data.get("frequency")
        duration = data.get("duration")
        starting_date = data.get("starting_date")

        duration = round(duration / no_of_collectors)

        end_date = calculate_group_details_end_date(
            frequency=frequency, duration=duration, starting_date=starting_date
        )
        if rosca_type == "AGENT_ROSCA":
            constant = ConstantTable.get_constant_table_instance()
            percentage = constant.rosca_commision
            admin_fee = (percentage / 100) * amount
            agent_com = admin_fee * 0.60

            if no_of_collectors > 1:
                if collection_type == "ONE_OFF_DEDUCTION":
                    fees = admin_fee / number_of_participants
                    collection_amount = round(amount / no_of_collectors - fees)
                    contribution_amount = round(amount / number_of_participants)

                if collection_type == "RECURING_DEDUCTION":
                    collection_amount = round(amount / no_of_collectors)
                    recuring_amount = (
                        admin_fee / number_of_participants / number_of_participants
                    )
                    contribution_amount = (
                        amount / number_of_participants + recuring_amount
                    )

                durations = round(collection_amount / contribution_amount)
                response = {
                    "commission": admin_fee,
                    "agent_commission": agent_com,
                    "collection_amount": collection_amount,
                    "contribution_amount": contribution_amount,
                    "durations": durations,
                    "end_date": end_date,
                }
                return Response(response, status=status.HTTP_200_OK)
            else:
                if collection_type == "ONE_OFF_DEDUCTION":
                    collection_amount = round(
                        amount - admin_fee / number_of_participants
                    )
                    contribution_amount = round(amount / number_of_participants)

                if collection_type == "RECURING_DEDUCTION":
                    collection_amount = amount
                    recuring_amount = (
                        admin_fee / number_of_participants / number_of_participants
                    )
                    contribution_amount = round(
                        collection_amount / number_of_participants + recuring_amount
                    )

                response = {
                    "commission": admin_fee,
                    "agent_commission": agent_com,
                    "collection_amount": collection_amount,
                    "contribution_amount": contribution_amount,
                    "duration": None,
                    "end_date": end_date,
                }
                return Response(response, status=status.HTTP_200_OK)


class ListFilterGroupStatus(generics.ListAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
    )
    pagination_class = CustomPagination
    serializer_class = AgentCreateRotationGroupSerializer

    filterset_fields = ("id", "status")
    search_fields = ()

    def get_queryset(self):
        user = self.request.user
        pending_groups = RotationGroup.objects.filter(
            user=user, is_deleted=False
        ).order_by("-id")

        return pending_groups


class GroupDetails(APIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = AgentCreateRotationGroupSerializer

    def get(self, request):
        user = self.request.user
        group_id = request.query_params.get("group_id")
        try:
            group = RotationGroup.objects.get(user=user, id=group_id)
        except RotationGroup.DoesNotExist:
            return Response(
                {"message": f"group with {group_id} does not exist"},
                status=status.HTTP_404_NOT_FOUND,
            )
        member_names = RotationGroupMember.objects.filter(group=group)

        member_details = []
        for member in member_names:
            member_details.append(
                {
                    "member_id": member.id,
                    "full_name": member.ajo_user_member.fullname,
                    "position": member.position,
                    "phone_number": member.ajo_user_member.phone_number,
                }
            )

        response = {
            "id": group.id,
            "name": group.name,
            "amount": group.amount,
            "duration": group.duration,
            "frequency": group.frequency,
            "rosca_type": group.rosca_type,
            "starting_date": group.starting_date,
            "no_of_collectors": group.no_of_collectors,
            "collection_type": group.collection_type,
            "total_amount_saved": group.total_amount_saved,
            "progress_amount": group.progress_amount,
            "number_of_participants": group.number_of_participants,
            "commision": group.commision,
            "member_details": member_details,
        }

        return Response(response, status=status.HTTP_200_OK)


class AddGroupMembers(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request):
        user: CustomUser = request.user
        access_token: str = request.headers.get("Authorization", "").partition(" ")[-1]
        group_id = request.query_params.get("group_id")
        serializer = AddGroupMemberSerializer(data=request.data, many=True)

        if not group_id:
            response = {"message": "Request parameter group_id is required"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        if serializer.is_valid():
            members = []

            member_data = serializer.validated_data[0].get(
                "members"
            )  # Extract the list of members

            for member in member_data:
                position = member.get("position")
                phone_number = member.get("phone_number")

                try:
                    ajo_user = AjoUser.objects.get(
                        user=user, phone_number__startswith=phone_number
                    )

                except AjoUser.DoesNotExist:
                    response = {
                        "status": "error",
                        "message": f"User with phone number {phone_number} does not exist or not registered under {user}",
                    }
                    return Response(response, status=status.HTTP_404_NOT_FOUND)

                try:
                    group = RotationGroup.objects.get(id=group_id)
                except RotationGroup.DoesNotExist:
                    response = {
                        "status": "error",
                        "message": f"Group with group_id {group_id} does not exist",
                    }
                    return Response(response, status=status.HTTP_404_NOT_FOUND)

                if RotationGroupMember.objects.filter(
                    group=group, ajo_user_member=ajo_user
                ).exists():
                    response = {
                        "status": "error",
                        "message": "User already exists in this group",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                if position > group.number_of_participants:
                    response = {
                        "status": "error",
                        "message": "Position cannot be greater than the number of participants",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                if RotationGroupMember.position_in_group(group, position) is not None:
                    response = {
                        "status": "error",
                        "message": f"Position {position} in group {group.name} is already taken",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                if group.is_active is True:
                    response = {
                        "status": "error",
                        "message": "You cannot add members to a running group",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                agent_number = obtain_phone_number(
                    access_token=access_token, user_id=user.customer_user_id
                )
                phone_number = "234" + phone_number[1:]
                if phone_number == agent_number:
                    response = {
                        "status": "error",
                        "message": "Agent cannot be part of the rotation group",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                member = RotationGroupMember(
                    user=request.user,
                    ajo_user_member=ajo_user,
                    group=group,
                    position=position,
                )
                member.save()
                group.ajo_members.add(member)
                members.append(member)

            for member in members:
                RoscaPaymentBreakDown().create_rosca_payment_break_down(
                    user=request.user,
                    ajo_user=member.ajo_user_member,
                    group=group,
                    start_date=group.starting_date,
                    end_date=group.end_date,
                )
                RoscaPaymentBreakDown().update_collection_dates(
                    group=group, member_instance=member
                )

                # WalletSystem().create_rosca_wallets_for_ajo_user(user=user, onboarded_user=ajo_user)

            response = {"status": "success", "message": "Members added successfully"}
            return Response(response, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AgentOnboardedUserList(generics.ListAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
    )
    # pagination_class = CustomPagination
    serializer_class = AjoUserListSerializer

    filterset_fields = ("id", "phone_number", "first_name")
    search_fields = ("phone_number", "first_name")

    def get_queryset(self):
        user = self.request.user
        agent_onboarded_users = AjoUser.objects.filter(user=user)

        return agent_onboarded_users


class DueRotationGroupPaymentBreakdownList(generics.ListAPIView):
    pagination_class = CustomPagination
    serializer_class = RoscaPaymentBreakDownSerializer

    filterset_fields = ("id", "ajo_user")
    search_fields = ()

    def get_queryset(self):
        user = self.request.user
        group_id = self.request.GET.get("group_id")
        try:
            group = RotationGroup.objects.get(
                id=group_id, rosca_type=RoscaType.AGENT_ROSCA
            )
        except RotationGroup.DoesNotExist:
            raise ValueError(
                f"a rotation group with this ID, {group_id}, does not exist"
            )
        today = date.today()
        due_contribution = RoscaPaymentBreakDown.objects.filter(
            user=user, group=group, paid=False, rosca_due_date__lte=today
        )
        return due_contribution

    def list(self, request, *args, **kwargs):
        try:
            queryset = self.filter_queryset(self.get_queryset())
        except ValueError as err:
            return value_error_response(error=err)
        paginator = self.pagination_class()
        paginated_queryset = paginator.paginate_queryset(queryset, request)

        result_dict = {}
        for obj in paginated_queryset:
            phone_number = (
                obj.ajo_user.phone_number
            )  # Assuming ajo_user is a related field

            # Create a list for the phone number if it doesn't exist in the dictionary
            if phone_number not in result_dict:
                result_dict[phone_number] = []

            # Append the object to the list for the corresponding phone number
            result_dict[phone_number].append(
                {
                    "id": obj.id,
                    "frequency": obj.frequency,
                    "due_amount": obj.due_amount,
                    "paid_amount": obj.paid_amount,
                    "paid": obj.paid,
                    "day_count": obj.day_count,
                    "rosca_due_date": obj.rosca_due_date,
                    "group": obj.group.id,
                    "group_duration": obj.group.duration,
                }
            )

        response = {
            "status": True,
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "phone_numbers": list(result_dict.keys()),
            "results": result_dict,
        }

        return Response(response, status=status.HTTP_200_OK)


class AllRotationGroupPaymentBreakdownList(generics.ListAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
    )
    pagination_class = CustomPagination
    serializer_class = RoscaPaymentBreakDownSerializer

    filterset_fields = ("id", "ajo_user")
    search_fields = ()

    def get_queryset(self):
        user = self.request.user
        group_id = self.request.GET.get("group_id")
        group = RotationGroup.objects.get(id=group_id)
        contribution = RoscaPaymentBreakDown.objects.filter(user=user, group=group)

        return contribution


class AgentLogContribution(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request):
        user: CustomUser = request.user
        serializer = AgentLogContributionSerializer(data=request.data)

        response_list = []
        agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()
        serializer.is_valid(raise_exception=True)

        access_token = request.headers.get("Authorization", "").partition(" ")[-1]
        agent_transaction_pin = serializer.validated_data.get("agent_transaction_pin")
        ids = serializer.validated_data.get("ids")

        validate_ids = RoscaPaymentBreakDown().contribution_check(ids=ids)
        if validate_ids.get("status") == False:
            response = {
                "status": "error",
                "message": "contribution is already recorded or does not exist",
                "paid": validate_ids.get("paid"),
                "not_found": validate_ids.get("not_found"),
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        # check the agent's ajo pin
        verify_agent_pin = verify_transaction_pin(
            transaction_pin=agent_transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )
        for id in ids:
            try:
                contribution = RoscaPaymentBreakDown.objects.get(id=id)
            except RoscaPaymentBreakDown.DoesNotExist:
                response_list.append(
                    {
                        "status": "error",
                        "message": f"Contribution with ID {id} does not exist",
                    }
                )

        contributions = RoscaPaymentBreakDown.objects.filter(id__in=ids, paid=False)
        contribution_count = contributions.count()
        due = contributions.filter(rosca_due_date__lte=timezone.now().date())
        total_amount_contributed = (
            contributions.aggregate(sum_amount=Sum("due_amount"))["sum_amount"] or 0
        )
        total_due_contributed = (
            due.aggregate(sum_amount=Sum("due_amount"))["sum_amount"] or 0
        )

        due_instance = due.first()

        if not check_if_agent_can_pay(
            agent_wallet=agent_wallet, amount=total_amount_contributed
        ):
            return Response(
                {
                    "status": False,
                    "error": "624",
                    "message": "the agent should top up wallet",
                },
                status.HTTP_400_BAD_REQUEST,
            )

        if verify_agent_pin == True:
            pay_for_agent_ajo_user_contribution(
                user=user,
                amount=total_amount_contributed,
                request_data=request.data,
                agent_wallet=agent_wallet,
                due_instance=due_instance,
                total_due_contributed=total_due_contributed,
                count=contribution_count,
            )
            for contrib in contributions:
                ajo_rosca_contribution_sms(
                    ajo_user=contrib.ajo_user,
                    phone_number=contrib.ajo_user.phone_number,
                    name=contrib.ajo_user.first_name,
                    group=contrib.group.name,
                    amount=contrib.due_amount,
                )
            contributions.update(
                paid_amount=F("due_amount"), paid=True, date_paid=timezone.now()
            )

            rotation_group = RotationGroup.objects.get(pk=contribution.group.id)
            rotation_group.total_amount_saved += total_amount_contributed
            rotation_group.balance_before = (
                rotation_group.total_amount_saved - total_amount_contributed
            )
            rotation_group.save()

            response_list.append({"status": "success", "message": "Savings Successful"})
        else:
            return Response(
                {"status": "False", "error": "651", "message": "incorrect pin"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response(response_list, status=status.HTTP_200_OK)


class MembersCollectionList(generics.ListAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
    )
    pagination_class = CustomPagination
    serializer_class = RotationGroupMemberSerializer

    filterset_fields = ()
    search_fields = ()

    def get_queryset(self):
        user = self.request.user
        group_id = self.request.GET.get("group_id")
        group = RotationGroup.objects.get(id=group_id)
        members_collection = RotationGroupMember.objects.filter(
            user=user,
            group=group,
        )

        return members_collection


class EditDeleteRotationGroup(APIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = AgentEditDeleteRotationGroupSerializer

    def put(self, request):
        user = request.user
        group_id = request.query_params.get("group_id")
        member_id = request.query_params.get("member_id")
        updated_data = request.data

        try:
            group = RotationGroup.objects.get(pk=group_id, user=user)
        except RotationGroup.DoesNotExist:
            return Response(
                {
                    "status": "Error",
                    "message": "Group not found or user does not have permission to edit",
                },
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = self.serializer_class(group, data=updated_data, partial=True)

        # if group.ajo_members is not None:
        #     return Response({"status": "Error", "message": ""}, status=status.HTTP_400_BAD_REQUEST)

        if serializer.is_valid():
            serializer.save()

            if member_id:
                try:
                    member_to_remove = RotationGroupMember.objects.get(
                        pk=member_id, group=group
                    )
                except RotationGroupMember.DoesNotExist:
                    return Response(
                        {"status": "Error", "message": "Member not found in this group"}
                    )
                group.ajo_members.remove(member_to_remove)

                member_to_remove.delete()
                phone_number = member_to_remove.ajo_user_member.phone_number
                ajo_user = AjoUser.objects.filter(
                    phone_number__startswith=phone_number
                ).last()
                rosca_breakdown = RoscaPaymentBreakDown.objects.filter(
                    group=group, ajo_user=ajo_user
                )
                for rosca_breakdown_instance in rosca_breakdown:
                    rosca_breakdown_instance.delete()

            return Response(serializer.data, status=status.HTTP_200_OK)

        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request):
        user = request.user
        group_id = request.query_params.get("group_id")

        try:
            group = RotationGroup.objects.get(
                pk=group_id,
                user=user,
                status=RoscaGroupStatus.PENDING,
            )
        except RotationGroup.DoesNotExist:
            return Response(
                {
                    "status": False,
                    "error": "404",
                    "message": "Group not found",
                },
            )

        group.delete()

        return Response(
            {"status": "Success", "message": "Group deleted successfully"},
            status=status.HTTP_204_NO_CONTENT,
        )


class AgentDueGroups(generics.ListAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
    )
    pagination_class = CustomPagination
    serializer_class = AgentCreateRotationGroupSerializer

    search_fields = ()

    def get_queryset(self):
        user = self.request.user
        group = RotationGroup.objects.filter(
            user=user,
            status=RoscaGroupStatus.RUNNING,
            rosca_type=RoscaType.AGENT_ROSCA,
        )
        return group


class AgentStartGroup(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request):
        user = request.user
        group_id = request.query_params.get("group_id")

        if not group_id:
            response = {"message": "Request parameter group_id is required"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        try:
            group = RotationGroup.objects.get(pk=group_id, user=user)
        except RotationGroup.DoesNotExist:
            return Response(
                {"status": "Error", "message": "Group not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        if group.status == RoscaGroupStatus.RUNNING:
            response = {
                "status": "error",
                "message": f"Group {group.name} is currently running",
            }
            return Response(data=response, status=status.HTTP_400_BAD_REQUEST)

        if group.status == RoscaGroupStatus.PENDING:
            members_count = group.ajo_members.all().count()

            if members_count != group.number_of_participants:
                response = {
                    "status": "error",
                    "message": f"{group.name} cannot start until group contributors are complete",
                }
                return Response(data=response, status=status.HTTP_400_BAD_REQUEST)

            if members_count == group.number_of_participants:
                group.status = RoscaGroupStatus.RUNNING
                group.is_active = True
                group.save()
                response = {
                    "status": "success",
                    "message": f"{group.name} started successfully",
                }
                return Response(data=response, status=status.HTTP_200_OK)


################################################## AGENT ROSCA ########################################################


class GetPlanTypesAPIView(GenericAPIView):
    serializer_class = GetPlansSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            duration_in_days = serializer.validated_data["duration_in_days"]
            frequency = serializer.validated_data["frequency"]
            target = serializer.validated_data.get("target")

            if not target:
                try:
                    get_plans = (
                        AjoSavingsPlanType.calculate_daily_weekly_one_month_plan(
                            frequency,
                            duration_in_days,
                        )
                    )
                except Exception as err:
                    response = {
                        "status": "error",
                        "message": f"{err}",
                    }
                    return Response(
                        response,
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            else:
                try:
                    get_plans = AjoSavingsPlanType.calculate_weekly_monthly_target_plan(
                        frequency,
                        duration_in_days,
                        target=target,
                    )
                    # get_plans = AjoSavingsPlanType.get_contribution_amount(frequency, duration_in_days, target)
                except Exception as err:
                    response = {
                        "status": "error",
                        "message": f"{err}",
                    }
                    return Response(
                        response,
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            response = {
                "status": "success",
                "data": get_plans,
            }
            return Response(
                response,
                status=status.HTTP_200_OK,
            )

        return Response(
            serializer.errors,
            status=status.HTTP_400_BAD_REQUEST,
        )


class GetSpendingDigitalAccountNumbersAPIView(GenericAPIView):
    """
    This is view is concerned with getting the account numbers for
    the wallets of the ajo user
    """

    serializer_class = GetSpendingDigitalSerializer
    permission_classes = (permissions.IsAuthenticated,)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of the ajo user",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        # obtain the phone number of the ajo user
        phone_number = request.query_params.get("phone_number")
        # obtain the user
        user = request.user

        # call the ajo user selector
        ajo_user_selector = AjoUserSelector(phone_number=phone_number, user=user)

        # obtain the ajo user
        try:
            ajo_user = ajo_user_selector.get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err)

        # call the bank account class
        bank_selector = BankAccountSelector(ajo_user=ajo_user)

        accounts_data = {}

        # try to get the spending and digital accounts
        try:
            accounts_data["spending_account"] = (
                bank_selector.get_ajo_user_account_details(
                    form_type=AccountFormType.AJO_SPENDING
                )
            )
            accounts_data["digital_account"] = (
                bank_selector.get_ajo_user_account_details(
                    form_type=AccountFormType.AJO_DIGITAL,
                )
            )

        except ValueError as err:
            if "spending" or "digital" in str(err):
                # check if the user has a bvn
                if ajo_user.bvn:
                    set_dob = AjoUserService.set_dob_from_bvn(ajo_user=ajo_user)
                    if not set_dob.get("status"):
                        return Response(
                            {
                                "status": False,
                                "error": "452",
                                "message": "cannot update date of birth from BVN, please try again or contact customer care",
                            },
                            status.HTTP_400_BAD_REQUEST,
                        )

                # refresh the ajo user info from database
                ajo_user.refresh_from_db()

                # define the data
                data = {
                    "phone_number": ajo_user.phone_number,
                    "first_name": ajo_user.first_name,
                    "last_name": ajo_user.last_name,
                    "dob": ajo_user.dob,
                    "address": f"{ajo_user.address}, {ajo_user.lga}, {ajo_user.state}.",
                    "gender": ajo_user.gender,
                    "bvn": ajo_user.bvn,
                }

                # create spending account
                # OLD BankAccountService.create_account_for_ajo_user
                spending_account_details = BankAccountService.create_wema_account(
                    ajo_user=ajo_user,
                    acct_form_type=AccountFormType.AJO_SPENDING,
                    # bvn_present=True if ajo_user.bvn else False,
                    # data=data,
                )

                # create digital account
                digital_account_details = BankAccountService.create_wema_account(
                    ajo_user=ajo_user,
                    acct_form_type=AccountFormType.AJO_DIGITAL,
                    # bvn_present=True if ajo_user.bvn else False,
                    # data=data,
                )

                if spending_account_details.get(
                    "status"
                ) and digital_account_details.get("status"):
                    accounts_data["spending_account"] = spending_account_details.get(
                        "data"
                    )
                    accounts_data["digital_account"] = digital_account_details.get(
                        "data"
                    )

                elif not spending_account_details.get("status"):
                    return Response(
                        {
                            "status": False,
                            "error": "400",
                            "message": "ajo spending wallet could not be created, please try again or contact customer care",
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                elif not digital_account_details.get("status"):
                    return Response(
                        {
                            "status": False,
                            "error": "400",
                            "message": "ajo digital wallet could not be created, please try again or contact customer care",
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                else:
                    return Response(
                        {
                            "status": False,
                            "error": "507",
                            "message": "an error occurred in the system, please contact customer care",
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

        serializer = self.serializer_class(accounts_data)

        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )


class PrintTransactionsAPIVIew(GenericAPIView):
    serializer_class = AjoSavingsInformationSerializer
    permission_classes = (permissions.IsAuthenticated,)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                name="from",
                in_=openapi.IN_QUERY,
                description="the start date for the date range, format: YYYY-MM-DD",
                required=True,
                type=openapi.TYPE_STRING,
            ),
            openapi.Parameter(
                name="to",
                in_=openapi.IN_QUERY,
                description="the end date for the date range, format: YYYY-MM-DD",
                required=True,
                type=openapi.TYPE_STRING,
            ),
            openapi.Parameter(
                name="id",
                in_=openapi.IN_QUERY,
                description="the ID of the ajo savings plan",
                required=True,
                type=openapi.TYPE_NUMBER,
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        start_date = request.query_params.get("from")
        end_date = request.query_params.get("to")
        id = request.query_params.get("id")

        if not (start_date or end_date or id):
            return Response(
                {
                    "status": False,
                    "error": "404",
                    "message": "ensure 'from', 'to' and 'id' are present as query parameters",
                },
                status.HTTP_422_UNPROCESSABLE_ENTITY,
            )

        if not (validate_date_format(start_date) or validate_date_format(end_date)):
            return Response(
                {
                    "status": False,
                    "error": "422",
                    "message": "ensure the 'from' and 'to' follow the YYYY-MM-DD format",
                },
                status.HTTP_422_UNPROCESSABLE_ENTITY,
            )

        try:
            start_date = datetime.strptime(start_date, "%Y-%m-%d")
            end_date = datetime.strptime(end_date, "%Y-%m-%d")
        except ValueError as err:
            return value_error_response(
                error="invalid date format, use YYYY-MM-DD", code="422"
            )

        if start_date > end_date:
            return Response(
                {
                    "status": False,
                    "error": "602",
                    "message": "'from' should always be a date before 'to'",
                },
                status.HTTP_400_BAD_REQUEST,
            )

        ajo_savings_selector = AjoSavingsSelector(id=id, user=request.user)

        try:
            transactions = ajo_savings_selector.get_ajo_savings_transaction_history_within_date_range(
                start_date=start_date,
                end_date=end_date,
            )
        except ValueError as err:
            return value_error_response(error=err, code="404")

        transaction_serializer = TransactionHistoryForAjoUserSerializer(
            transactions, many=True
        )

        return Response(
            {"status": True, "data": transaction_serializer.data},
            status.HTTP_200_OK,
        )


class ChangeAjoUserInformationAPIView(GenericAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_classes = {
        "POST": ProfileChangeRequestSerializer,
    }

    def get_serializer_class(self) -> serializers.Serializer:
        return self.serializer_classes.get(self.request.method)

    @swagger_auto_schema(
        request_body=ProfileChangeRequestSerializer,
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
        ],
    )
    def post(self, request, *args, **kwargs):
        """
        Change the first_name, last_name, phone_number, alias and
        address of the ajo user whose phone number is passed as a query parameter.
        """
        phone_number = request.query_params.get("phone_number")

        try:
            ajo_user = AjoUserSelector(
                phone_number=phone_number, user=request.user
            ).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        serializer = self.get_serializer_class()(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        profile_selector = ProfileChangeSelector(ajo_user=ajo_user)

        if not profile_selector.can_user_edit_profile():
            return Response(
                {
                    "status": False,
                    "error": "403",
                    "message": "you do not have any valid successful request to make this change",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            ajo_user = AjoUserService(ajo_user=ajo_user).update_ajo_user_fields(
                dict(serializer.validated_data)
            )
            active_request = profile_selector.get_latest_change_request()
            active_request.is_active = False
            active_request.save()

        except IntegrityError as err:
            return Response(
                {
                    "status": False,
                    "error": "901",
                    "message": "this phone number already exists in our system, ensure this number is correct and belongs to you.",
                    "error_message": str(err),
                },
                status.HTTP_403_FORBIDDEN,
            )

        response_serializer = AjoUserChangeSerializer(ajo_user)

        return Response(
            data={
                "status": True,
                "message": "details updated successfully",
                "data": response_serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """
        This shows the status of an active request
        """
        phone_number = request.query_params.get("phone_number")

        try:
            ajo_user = AjoUserSelector(
                phone_number=phone_number, user=request.user
            ).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err, code="1012")

        profile_selector = ProfileChangeSelector(ajo_user=ajo_user)

        active_request = profile_selector.get_latest_change_request()

        if not active_request:
            return Response(
                {
                    "status": False,
                    "error": "404",
                    "message": "there is no active change request",
                },
                status=status.HTTP_404_NOT_FOUND,
            )

        return Response(
            {
                "status": True,
                "data": {
                    "request": {
                        "status": active_request.status,
                    }
                },
            },
            status.HTTP_200_OK,
        )


class AjoNextOfKinAPIView(generics.GenericAPIView):
    serializer_class = AjoNextOfKinSerializer
    permission_classes = (permissions.IsAuthenticated,)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
        ],
    )
    def post(self, request, *args, **kwargs):
        """
        Set the next of kin for an ajo user
        """
        user = request.user
        phone_number = request.query_params.get("phone_number")
        if not phone_number:
            return Response(
                {
                    "error": "404",
                    "status": False,
                    "message": "phone_number required as a query parameter",
                },
                status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.serializer_class(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        data = dict(serializer.validated_data)

        try:
            ajo_user = AjoUserSelector(
                phone_number=phone_number,
                user=user,
            ).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err)

        next_of_kin = NextOfKinService(
            ajo_user=ajo_user
        ).create_next_of_kin_for_ajo_user(data)

        response_serializer = self.serializer_class(next_of_kin)
        ajo_user.onboarding_stage = OnboardingStage.IMAGE_UPLOAD
        ajo_user.save()
        return Response(
            {
                "status": True,
                "data": response_serializer.data,
            },
            status.HTTP_201_CREATED,
        )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "phone_number",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="phone number of ajo user",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        user = request.user
        phone_number = request.query_params.get("phone_number")
        if not phone_number:
            return Response(
                {
                    "error": "404",
                    "status": False,
                    "message": "phone_number required as a query parameter",
                },
                status.HTTP_400_BAD_REQUEST,
            )

        try:
            ajo_user = AjoUserSelector(
                phone_number=phone_number,
                user=user,
            ).get_ajo_user()
        except ValueError as err:
            return value_error_response(error=err)

        try:
            nok = ajo_user.next_of_kin
        except ObjectDoesNotExist as err:
            return Response(
                {
                    "error": "404",
                    "status": False,
                    "message": "no next of kin set for this ajo user",
                },
                status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.serializer_class(nok)

        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status.HTTP_200_OK,
        )


class ProductToPurchaseAPIView(generics.GenericAPIView):
    serializer_class = ProductInformationSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        """
        Post the information of a product, tie it to a savings plan
        """
        serializer = self.serializer_class(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        user = request.user
        savings_id = serializer.validated_data.pop("savings_id")

        try:
            savings_plan = AjoSavingsSelector(
                id=savings_id, user=user
            ).get_ajo_saving_plan()
        except ValueError as err:
            return value_error_response(error=err)

        valid_savings_types = [SavingsType.BNPL, SavingsType.SNPL]
        if savings_plan.savings_type not in valid_savings_types:
            return error_response(
                message="this type of savings does not require a product item",
            )

        if savings_plan.product_info:
            return error_response(
                message="this savings is already tied to a product item"
            )

        with django_transaction.atomic():
            try:
                product = ProductInformationService.create_or_update_product_info(
                    data=serializer.validated_data
                )
            except ValueError as err:
                return value_error_response(error=err)

            if savings_plan.savings_type == SavingsType.BNPL:
                const = ConstantTable.get_constant_table_instance()
                bnpl_interest = const.staff_loans_config.get("bnpl_interest", 20) / 100
                expected_amount = (
                    bnpl_interest * product.selling_price
                ) + product.selling_price
                savings_plan.expected_amount = expected_amount

            savings_plan.product_info = product
            savings_plan.save()

        return success_response(
            message=f"item has been mapped to savings plan successfully"
        )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                name="savings_id",
                in_=openapi.IN_QUERY,
                description="savings plan ID",
                required=True,
                type=openapi.TYPE_STRING,
            )
        ]
    )
    def get(self, request, *args, **kwargs):
        """
        Get the product information tied to savings plan
        """
        savings_id = request.query_params.get("savings_id")
        if not savings_id:
            return error_response(
                message="please include 'savings_id' in query parameters"
            )

        try:
            savings = AjoSavingsSelector(
                id=savings_id, user=request.user
            ).get_ajo_saving_plan()
        except ValueError as err:
            return value_error_response(error=err)

        if savings.savings_type is None:
            return error_response("this type of savings does not have an item")

        serializer = self.serializer_class(savings.product_info)

        return success_response(data=serializer.data, message="item retrieved")


class AgentAjoSepoStatusAPIView(GenericAPIView):
    serializer_class = AgentAjoSepoStatusSerializer

    def get(self, request, *args, **kwargs):
        """
        Get the status of the Ajo Agent for the next type of loan savings to create
        """
        agent = request.user
        agent_selector = AjoAgentSelector(user=agent)
        data = {}
        data["next_savings_type"] = agent_selector.determine_next_savings_type()
        data["individual_savings_count"] = (
            agent_selector.get_available_individual_savings()
        )
        serializer = self.serializer_class(data)
        return success_response(data=serializer.data, message="data retrieved")


class AjoSepoAPIView(GenericAPIView):
    serializer_classes = {"POST": CreateAjoSepoSerializer, "GET": AjoSepoSerializer}
    pagination_class = CustomPagination

    def get_serializer_class(self) -> CreateAjoSepoSerializer | AjoSepoSerializer:
        return self.serializer_classes.get(self.request.method)

    def post(self, request, *args, **kwargs):
        """
        Create an AjoSepo Group
        """
        serializer = self.get_serializer_class()(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
            user = request.user
            phone_number = serializer.validated_data.pop("leader")
            ajo_user = AjoUserSelector(
                phone_number=phone_number, user=user
            ).get_ajo_user()
        except (serializers.ValidationError, ValueError) as err:
            if isinstance(err, ValueError):
                return value_error_response(error=err)
            return serializer_validation_error_response(error=err)

        data = dict(serializer.validated_data)
        data.update(
            {
                "leader": ajo_user,
                "user": user,
            }
        )

        try:
            ajosepo = AjoSepoService.create_ajosepo_group(data=data)
        except ValueError as err:
            return value_error_response(error=err)

        serializer = AjoSepoSerializer(ajosepo)
        return success_response(
            data=serializer.data, message="ajosepo group created successfully"
        )

    def get(self, request, *args, **kwargs):
        """get all the groups that belong to an agent"""
        agent_selector = AjoAgentSelector(user=request.user)
        try:
            paginated_queryset = self.paginate_queryset(
                agent_selector.get_ajosepo_groups()
            )
        except NotFound:
            return pagination_page_not_found_response()

        serializer = self.get_serializer_class()(paginated_queryset, many=True)
        return self.get_paginated_response(data=serializer.data)


class JoinLeaveAjoSepoAPIView(GenericAPIView):
    serializer_classes = {"POST": JoinAjoSepoGroupSerializer}
    pagination_class = CustomPagination

    def get_serializer_class(self) -> JoinAjoSepoGroupSerializer:
        return self.serializer_classes.get(self.request.method)

    def post(self, request, *args, **kwargs):
        """
        Join an AjoSepo group through an existing loan savings
        """
        serializer = self.get_serializer_class()(data=request.data)
        try:

            serializer.is_valid(raise_exception=True)
            group_id = serializer.validated_data.pop("group_id")
            savings_id = serializer.validated_data.pop("savings_id")

            user = request.user
            group_selector = AjoSepoSelector(group_id=group_id, user=user)
            group = group_selector.get_group()
            savings = AjoSavingsSelector(id=savings_id, user=user).get_ajo_saving_plan()

            group_selector.all_savings_check_to_join_group(ajo_savings=savings)
            AjoSepoService(group=group).add_savings_to_group(savings=savings)

        except (serializers.ValidationError, ValueError) as err:

            if isinstance(err, ValueError):
                return value_error_response(error=err)
            return serializer_validation_error_response(error=err)

        return success_response(message="savings added to group successfully")

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "savings_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="the ID (int) of the savings to be removed",
            ),
            openapi.Parameter(
                "group_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="the group_id of the group",
            ),
        ],
    )
    def delete(self, request, *args, **kwargs):
        """
        Remove a savings from an AjoSepo group
        """
        savings_id = request.query_params.get("savings_id")
        if not savings_id:
            return error_response(
                message="please include 'savings_id' in query parameters"
            )

        group_id = request.query_params.get("group_id")
        if not group_id:
            return error_response(
                message="please include 'group_id' in query parameters"
            )

        try:
            user = request.user
            savings = AjoSavingsSelector(id=savings_id, user=user).get_ajo_saving_plan()
            group = AjoSepoSelector(group_id=group_id, user=user).get_group()

            AjoSepoService(group=group).remove_savings_from_group(savings=savings)
        except ValueError as err:
            return value_error_response(error=err)
        return success_response(message="savings removed from group successfully")


class AjoSepoGroupInfoAPIView(GenericAPIView):
    serializer_class = AjoSepoDetailsSerializer

    def get(self, request, group_id: str, *args, **kwargs):
        """
        Get all the details of an AjoSepo group
        """

        try:
            user = request.user
            group_selector = AjoSepoSelector(group_id=group_id, user=user)
            group = group_selector.get_group()
            group.joined_members = group.savings
            group.joined_members_count = group.savings.count()
        except ValueError as err:
            return value_error_response(error=err)

        serializer = self.serializer_class(group)
        return success_response(data=serializer.data, message="data retrieved")


class UpdateAjoUserImageView(APIView):
    serializer_class = UpdateAjoUserImageSerializer

    def post(self, request):
        admin_emails = ActionPermission.get_customer_service_emails()
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        admin_email = serializer.validated_data["admin_email"]

        if admin_email not in admin_emails:
            return Response(
                {
                    "status": False,
                    "message": "You do not have permission to perform this action.",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        try:

            ajo_user_id = serializer.validated_data["ajo_user_id"]
            image = serializer.validated_data["image"]

            try:
                ajo_user = AjoUser.objects.get(id=ajo_user_id)
            except AjoUser.DoesNotExist:
                return Response(
                    {"status": False, "message": "AjoUser not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            ajo_user.image = image
            ajo_user.save()

            return Response(
                {"status": True, "message": "AjoUser image updated successfully"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": False, "message": str(e)}, status=status.HTTP_400_BAD_REQUEST
            )


@staff_member_required
def view_logs(
    request,
):
    """View logs dynamically based on the file name with color highlighting."""
    logs_dir = os.path.join(settings.BASE_DIR, "logs")  # Fixed logs directory

    file_name = request.GET.get("file_name")
    if not file_name:

        file_name = f"errors_{datetime.now().strftime('%Y-%m-%d')}.log"

    log_file = os.path.join(logs_dir, file_name)

    if not os.path.exists(log_file):
        return HttpResponse(
            "<h2 style='color: red; text-align: center;'>Log file not found</h2>",
            status=404,
        )

    with open(log_file, "r") as file:
        log_content = file.read()

    # Apply color highlighting
    def colorize_logs(log_text):
        log_text = re.sub(
            r"(ERROR)",
            r"<span style='color: red; font-weight: bold;'>\1</span>",
            log_text,
        )
        log_text = re.sub(
            r"(WARNING)",
            r"<span style='color: yellow; font-weight: bold;'>\1</span>",
            log_text,
        )
        log_text = re.sub(
            r"(INFO)",
            r"<span style='color: limegreen; font-weight: bold;'>\1</span>",
            log_text,
        )
        return log_text

    log_content = colorize_logs(log_content)

    # HTML template with Dark Mode and wide layout
    html_template = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Logs - {file_name}</title>
        <style>
            body {{
                background-color: #1e1e1e;
                color: #c7c7c7;
                font-family: Arial, sans-serif;
                padding: 20px;
            }}
            .container {{
                max-width: 90%;
                margin: auto;
                background: #2e2e2e;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            }}
            pre {{
                background: #333;
                color: #e6e6e6;
                padding: 15px;
                overflow-x: auto;
                border-radius: 5px;
                font-size: 14px;
                white-space: pre-wrap;
            }}
            h1 {{
                text-align: center;
                color: #fff;
            }}
            .back-link {{
                display: block;
                text-align: center;
                margin-top: 20px;
                color: #00aaff;
                text-decoration: none;
            }}
            .back-link:hover {{
                text-decoration: underline;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>📜 Logs - {file_name}</h1>
            <pre>{log_content}</pre>
            <a href="/admin/" class="back-link">🔙 Back to Admin</a>
        </div>
    </body>
    </html>
    """

    return HttpResponse(html_template)


class GetSavingsHealthPlansAPIView(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):
        """
        Get the savings health plan details from the ConstantTable

        The policy_fee returned for each plan is the sum of the original policy_fee and activation_fee
        """
        try:
            const = ConstantTable.get_constant_table_instance()
            health_plans = const.savings_health_plan_details

            if not health_plans:
                return success_response(data={}, message="No health plans found")

            try:
                health_plans_data = json.loads(health_plans)

                # Modify the policy_fee to be the sum of policy_fee and activation_fee
                for plan_name, plan_details in health_plans_data.items():
                    if (
                        "policy_fee" in plan_details
                        and "activation_fee" in plan_details
                    ):
                        # Store the original policy_fee for reference if needed
                        original_policy_fee = plan_details["policy_fee"]

                        # Set policy_fee to be the sum of policy_fee and activation_fee
                        plan_details["policy_fee"] = (
                            original_policy_fee + plan_details["activation_fee"]
                        )

            except json.JSONDecodeError:
                return error_response(message="Invalid health plan data format")

            return success_response(
                data=health_plans_data, message="Health plans retrieved successfully"
            )
        except Exception as e:
            return error_response(message=f"Error retrieving health plans: {str(e)}")


class GetCashConnectAdminAccountDetailsAPIView(APIView):

    def get(self, request):
        request_type = request.GET.get("request_type")
        verification_type = request.GET.get("verification_type")

        if request_type == "BORROWERINFO_COUNT":
            if not verification_type:

                borrower_info_queryset = BorrowerInfo.objects.filter(
                    is_verified=True,
                ).distinct("borrower")
            else:
                borrower_info_queryset = BorrowerInfo.objects.filter(
                    is_verified=True, verification_type=verification_type
                ).distinct("borrower")

            return Response(
                {
                    "status": True,
                    "message": "success",
                    "count": borrower_info_queryset.count(),
                },
                status=status.HTTP_200_OK,
            )

        request_get_account_details = (
            CoreBankingManager.get_cash_connect_company_account_details()
        )

        return Response(
            {"status": True, "message": "success", "data": request_get_account_details},
            status=status.HTTP_200_OK,
        )

    def post(self, request):
        verification_type = request.GET.get("verification_type")
        wallet_form_type = request.GET.get("wallet_form_type", None)
        existing_users = (
            BankAccountDetails.objects.filter(
                form_type__in=["AJO_SPENDING", "LOAN_REPAYMENT"],
                account_provider=AccountProvider.CASH_CONNECT,
            )
            .values("ajo_user")  # Group by user
            .annotate(user_count=Count("ajo_user"))  # Count occurrences
            .filter(user_count__gte=2)  # Keep only users appearing 2 times or More
            .values_list("ajo_user", flat=True)  # Extract only user IDs
        )
        # existing_users = BankAccountDetails.objects.filter(
        #     account_provider=AccountProvider.CASH_CONNECT
        # ).values_list("ajo_user", flat=True)

        borrower_info_queryset = BorrowerInfo.objects.filter(
            Q(is_verified=True)
            & ~Q(
                borrower__in=existing_users,
            )
        ).distinct("borrower")

        if verification_type:
            borrower_info_queryset = borrower_info_queryset.filter(
                verification_type=verification_type
            )

        result = []
        for _binfo in borrower_info_queryset:
            _binfo.update_dob_with_verification_response()
            request_create_cash_connect_acct_details = (
                BankAccountService.create_cash_connect_account_details(
                    borrower_info=_binfo, wallet_form_type=wallet_form_type
                )
            )
            result.append(request_create_cash_connect_acct_details)

        return Response(
            {
                "status": True,
                "count": borrower_info_queryset.count(),
                "message": "success",
                "data": result,
            },
            status=status.HTTP_200_OK,
        )


class UpdateAccountProvider(APIView):

    def get(self, request):
        bank_name = request.GET.get("bank_name", "").upper()
        bank_details_queryset = BankAccountDetails.objects.filter(
            bank_name__icontains=bank_name,
            account_provider__isnull=True,
            is_active=True,
        )
        if bank_name == "WEMA":
            bank_details_queryset.update(account_provider=AccountProvider.WEMA)
        elif bank_name == "VFD":
            bank_details_queryset.update(account_provider=AccountProvider.VFD)

        return Response(
            {
                "status": True,
                "message": "success",
                "count": bank_details_queryset.count(),
                "data": None,
            },
            status=status.HTTP_200_OK,
        )


class CreateRecoveryAccountView(APIView):

    def post(self, request):
        # verification_type = request.GET.get("verification_type")
        account_provider = request.GET.get("account_provider", "WEMA")

        existing_users = (
            BankAccountDetails.objects.filter(
                form_type__in=["LOAN_RECOVERY"],
                account_provider=account_provider,
            )
            .values("ajo_user")  # Group by user
            .annotate(user_count=Count("ajo_user"))  # Count occurrences
            .filter(user_count__gte=2)  # Keep only users appearing 2 times or More
            .values_list("ajo_user", flat=True)  # Extract only user IDs
        )

        loan_queryset = (
            AjoLoan.objects.filter(
                ~Q(borrower__in=existing_users),
            )
            .order_by("borrower", "id")
            .distinct("borrower")
        )

        success = 0
        failed = 0

        for loan_instance in loan_queryset:
            ajo_user = loan_instance.borrower
            if account_provider == "WEMA":
                request_result = BankAccountService.create_wema_account(
                    ajo_user=ajo_user,
                    acct_form_type=AccountFormType.LOAN_RECOVERY,
                )
                if request_result.get("status", True):
                    success += 1
                else:
                    failed += 1
            else:
                request_create_cash_connect_acct_details = (
                    BankAccountService.create_cash_connect_account_details(
                        ajo_user=ajo_user
                    )
                )
                success += 1

        return Response(
            {
                "status": True,
                "count": loan_queryset.count(),
                "message": "success",
                "data": {"success": success, "failed": failed},
            },
            status=status.HTTP_200_OK,
        )
