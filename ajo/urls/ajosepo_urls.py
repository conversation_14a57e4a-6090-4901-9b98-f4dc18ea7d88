from django.urls import path

from ajo.views.ajo_views import (
    AgentAjoSepoStatusAPIView,
    AjoSepoAPIView,
    AjoSepoGroupInfoAPIView,
    JoinLeaveAjoSepoAPIView,
)
from loans.views import (
    CreateGroupLoan,
    DisburseGroupLoan,
    GetRunningGroupLoan,
    GroupLoanRepaymentAPIView,
    PendingGroupInfo,
)

ajosepo = [
    path("", AjoSepoAPIView.as_view(), name="ajosepo"),
    path("agent/status/", AgentAjoSepoStatusAPIView.as_view(), name="agent_ajosepo_status"),
    path("manage-members/", JoinLeaveAjoSepoAPIView.as_view(), name="join_leave"),
    path("<str:group_id>/", AjoSepoGroupInfoAPIView.as_view(), name="ajosepo_group_info"),
]

group_loan = [
    path("pending-group/", PendingGroupInfo.as_view(), name="pending_group"),
    path("create-loan/", CreateGroupLoan.as_view(), name="create_loan_instance"),
    path("view-running-loans/", GetRunningGroupLoan.as_view(), name="view_loan_running_loans"),
    path("disbursement/", DisburseGroupLoan.as_view(), name="view_loan_running_loans"),
    path("repayment/", GroupLoanRepaymentAPIView.as_view(), name="trigger_repayment"),
]


urlpatterns = [
    *group_loan,
    *ajosepo,
]
