from django.urls import path

from ajo.views.ajo_views import (
    AddGroupMembers,
    AgencyBankingAgentInfoAPIView,
    AgentAjoSepoStatusAPIView,
    AgentDueGroups,
    AgentLedgerAPIView,
    AgentLogContribution,
    AgentOnboardedUserList,
    AgentStartGroup,
    AgentWalletAPIView,
    AjoNextOfKinAPIView,
    AjoSavingDetailAPIView,
    AjoUsersBasedOnCardsAPIView,
    AjoUserTransactionHistory,
    AllCustomersGroupsListAPIView,
    AllRotationGroupPaymentBreakdownList,
    AssignCardAPIView,
    CardDebitGatewayView,
    CardRequestByAgentAPIView,
    ChangeAjoUserInformationAPIView,
    ConfirmCardAssignDetailsAPIView,
    CreateAjoSavingsPlanAPIView,
    CreateRecoveryAccountView,
    CreateRotationGroup,
    DueRotationGroupPaymentBreakdownList,
    EditDeleteRotationGroup,
    ExistingAjoUserBalancesAPIView,
    ExistingUserSavingsListAPIView,
    FillPersonalDetailsAPIView,
    GetCashConnectAdminAccountDetailsAPIView,
    GetPlanTypesAPIView,
    GetSavingsHealthPlansAPIView,
    GetSpendingDigitalAccountNumbersAPIView,
    GetVirtualAccountDetailsAPIView,
    GroupDetails,
    ImageCapturingAPIView,
    ListFilterGroupStatus,
    MembersCollectionList,
    OnboardAjoUserWithPhoneNumberAPIView,
    OTPAPIView,
    PrintTransactionsAPIVIew,
    ProductToPurchaseAPIView,
    RotationBreakdown,
    SavedWithdrawnSummaryAPIView,
    StatesLGAAPIView,
    TradeSearchAPIView,
    TradesListAPIView,
    UpdateAccountProvider,
    UpdateAjoUserImageView,
    UserPinAPIView,
    V2CreateAjoSavingsPlanAPIView,
    VerifyOTPAPIView,
    view_logs,
)

from ..views.callback_views import (
    DojahWebhookView,
    LibertyPayVfdFundingCallback,
    SendtoAjoFundingViaAgencyBankingWebhookView,
    USSDCallbackAPIView,
    WemaCallbackAPIView,
)
from ..views.loan_views import (
    CreateGetLoanDetailsAPIView,
    LoanEligibilityAPIView,
    LoanHistoryAPIView,
    LoanWalletBalanceWithdrawAPIView,
    RequestLoanAPIView,
)
from ..views.payment_views import (
    CardUserInformationAPIView,
    CashoutFromSpendingWalletAPIView,
    CashoutRequestAPIView,
    CheckAjoUserWithdrawalAccountDetailsAPIView,
    CommissionsAPIView,
    CreateWemaWalletAPIView,
    GetExternalAccountWithdrawalBalanceAPIView,
    GetSetDeleteVerifiedWithdrawalAccountDetailsAPIView,
    PayForAjoPlanAPIView,
    PayForDojahVerificationRequestAPIView,
    PayForLoanAPIView,
    PrefundingRequestAPIView,
    PrefundingWalletAPIView,
    SettleDueBalanceFromAgentWalletAPIView,
    TransferCommissionsToAgentWalletAPIView,
    TriggerCashoutFromSpendingWithOTPAPIView,
    TriggerCashoutWithOTPAPIView,
    WithdrawToAgencyWalletAPIView,
    WithdrawToExternalAccountAPIView,
)
from ..views.personal_ajo_views import (
    CreateGetEditDeleteRotationGroupAPIView,
    FetchAjoSavingsAPIView,
    FetchRotationGroupsAPIView,
    GenerateRotationGroupDetailsAPIView,
    GroupMembersAPIView,
    JoinLeaveRotationGroupAPIView,
    PersonalAjoSavingsDetailAPIView,
    PersonalUserChecksAPIView,
    RotationGroupMemberDueInfoAPIView,
    RotationGroupTransactionHistoryAPIView,
    SavingsSummaryAPIView,
    StartRotationGroupAPIView,
    TransactionHistoryAPIView,
)


rosca = [
    path(
        "agent/rosca/create_rotation_group/",
        CreateRotationGroup.as_view(),
        name="create_group",
    ),
    path(
        "agent/rosca/rotation_breakdown/",
        RotationBreakdown.as_view(),
        name="rotation_breakdown",
    ),
    path(
        "agent/rosca/get_group_status/",
        ListFilterGroupStatus.as_view(),
        name="get_group_status",
    ),
    path(
        "agent/rosca/add_group_member/",
        AddGroupMembers.as_view(),
        name="add_group_member",
    ),
    path(
        "agent/rosca/onboarded_users/",
        AgentOnboardedUserList.as_view(),
        name="onboarded_users",
    ),
    path(
        "agent/rosca/due_collections/",
        DueRotationGroupPaymentBreakdownList.as_view(),
        name="due_collections",
    ),
    path(
        "agent/rosca/all_collections/",
        AllRotationGroupPaymentBreakdownList.as_view(),
        name="all_collections",
    ),
    path("agent/rosca/contribute/", AgentLogContribution.as_view(), name="contribute"),
    path(
        "agent/rosca/members_collection/",
        MembersCollectionList.as_view(),
        name="members_collection",
    ),
    path(
        "agent/rosca/delete_group/",
        EditDeleteRotationGroup.as_view(),
        name="delete_group",
    ),
    path(
        "agent/rosca/agent_due_group/", AgentDueGroups.as_view(), name="agent_due_group"
    ),
    path("agent/rosca/group_details/", GroupDetails.as_view(), name="group_details"),
    path(
        "agent/rosca/members_collection/",
        MembersCollectionList.as_view(),
        name="members_collection",
    ),
    path(
        "agent/rosca/delete_group/",
        EditDeleteRotationGroup.as_view(),
        name="delete_group",
    ),
    path(
        "agent/rosca/start_group/",
        AgentStartGroup.as_view(),
        name="start_group",
    ),
]

personal = [
    path(
        "personal/ajo-savings/",
        FetchAjoSavingsAPIView.as_view(),
        name="ajo_savings,",
    ),
    path(
        "personal/ajo-plan-details/",
        PersonalAjoSavingsDetailAPIView.as_view(),
        name="personal_ajo_plan_details",
    ),
    path(
        "personal/savings-summary/",
        SavingsSummaryAPIView.as_view(),
        name="savings_summary",
    ),
    path(
        "personal/transaction-history/",
        TransactionHistoryAPIView.as_view(),
        name="transaction_history",
    ),
    # groups
    path(
        "personal/groups/generate-group-details/",
        GenerateRotationGroupDetailsAPIView.as_view(),
        name="generate_group_details",
    ),
    path(
        "personal/groups/rotation-group/",
        CreateGetEditDeleteRotationGroupAPIView.as_view(),
        name="create_rotation_group",
    ),
    path(
        "personal/groups/join-leave-group/",
        JoinLeaveRotationGroupAPIView.as_view(),
        name="join_rotation_group",
    ),
    path(
        "personal/groups/group-members/",
        GroupMembersAPIView.as_view(),
        name="rotation_group_members",
    ),
    path(
        "personal/groups/member/",
        RotationGroupMemberDueInfoAPIView.as_view(),
        name="rotation_group_member_info",
    ),
    path(
        "personal/groups/fetch-groups/",
        FetchRotationGroupsAPIView.as_view(),
        name="fetch_rotation_groups",
    ),
    path(
        "personal/groups/start-rotation-group/",
        StartRotationGroupAPIView.as_view(),
        name="start_rotation_group",
    ),
    path(
        "personal/user/checks/",
        PersonalUserChecksAPIView.as_view(),
        name="personal_user_checks",
    ),
    path(
        "personal/groups/transaction-history/",
        RotationGroupTransactionHistoryAPIView.as_view(),
        name="rotation_group_transaction_history",
    ),
]

callbacks = [
    path("ussd/", USSDCallbackAPIView.as_view(), name="ussd_callback"),
    path("vfd/", LibertyPayVfdFundingCallback.as_view(), name="vfd_callback"),
    path("wema/", WemaCallbackAPIView.as_view(), name="wema_callback"),
    path(
        "fund_from_liberty/",
        SendtoAjoFundingViaAgencyBankingWebhookView.as_view(),
        name="liberty_callback",
    ),
    path("dojah/", DojahWebhookView.as_view(), name="dojah_callback"),
]

admin_calls = [
    path(
        "api/updateImage/",
        UpdateAjoUserImageView.as_view(),
        name="update-image-manually",
    ),
    path("ajo-savings/logs/", view_logs, name="view_logs"),
    path(
        "core-banking/get-cash-connect-details/",
        GetCashConnectAdminAccountDetailsAPIView.as_view(),
        name="get_cash_connet_details",
    ),
    path(
        "acct/ajo-update-provider/",
        UpdateAccountProvider.as_view(),
        name="update_acct_provider",
    ),
    path(
        "savings-health-plans/",
        GetSavingsHealthPlansAPIView.as_view(),
        name="get_savings_health_plans",
    ),
    path(
        "core-banking/create-account/",
        CreateRecoveryAccountView.as_view(),
        name="core-banking-create-account",
    ),
]

urlpatterns = [
    ###PERSONAL####
    *personal,
    ####AGENTS#######
    *rosca,
    *admin_calls,
    ####AJOSEPO#####
    *callbacks,
    ####AJOSEPO#####
    # OTPS
    path("otp/send-otp/", OTPAPIView.as_view(), name="send_otp"),
    path("otp/verify-otp", VerifyOTPAPIView.as_view(), name="verify_otp"),
    # Onboarding
    path(
        "agent/onboarding/onboard-ajo-user/",
        OnboardAjoUserWithPhoneNumberAPIView.as_view(),
        name="onboard_ajo_user",
    ),
    path(
        "agent/onboarding/states-lga/",
        StatesLGAAPIView.as_view(),
        name="states_and_lga",
    ),
    path(
        "agent/onboarding/trades-list/", TradesListAPIView.as_view(), name="trades_list"
    ),
    path(
        "agent/onboarding/trades-search/",
        TradeSearchAPIView.as_view(),
        name="trades_search",
    ),
    path(
        "agent/onboarding/fill-personal-details/",
        FillPersonalDetailsAPIView.as_view(),
        name="fill_personal_details",
    ),
    path(
        "agent/onboarding/upload-image/",
        ImageCapturingAPIView.as_view(),
        name="image_capturing",
    ),
    path(
        "agent/onboarding/confirm-card-details/",
        ConfirmCardAssignDetailsAPIView.as_view(),
        name="confirm_card_details",
    ),
    path(
        "agent/onboarding/assign-card-get-cards/",
        AssignCardAPIView.as_view(),
        name="assign_card_get_cards",
    ),
    path(
        "agent/onboarding/next-of-kin/",
        AjoNextOfKinAPIView.as_view(),
        name="ajo_user_next_of_kin",
    ),
    # AGENTS
    # filtering
    # path(
    #     "agent/boards/request-virtual-account/",
    #     RequestForVirtualAccountAPIView.as_view(),
    #     name="request_for_virtual_account",
    # ),
    path(
        "agent/boards/get-virtual-account-details/",
        GetVirtualAccountDetailsAPIView.as_view(),
        name="get_virtual_account_details",
    ),
    path(
        "agent/boards/ajo-users/",
        AjoUsersBasedOnCardsAPIView.as_view(),
        name="ajo_users",
    ),
    path(
        "agent/boards/all-customers-groups/",
        AllCustomersGroupsListAPIView.as_view(),
        name="all_customers_group_list",
    ),
    # Summary
    path(
        "agent/boards/summary/",
        SavedWithdrawnSummaryAPIView.as_view(),
        name="saved_withdrawn_summary",
    ),
    # Agent's wallet
    path("agent/boards/wallet/", AgentWalletAPIView.as_view(), name="agent_wallet"),
    # Ledger
    path("agent/boards/ledger/", AgentLedgerAPIView.as_view(), name="agent_ledger"),
    # Existing users urls
    path(
        "agent/existing-user/user-savings-info/",
        ExistingUserSavingsListAPIView.as_view(),
        name="existing_user_savings_list",
    ),
    path(
        "agent/existing-user/transaction-history/",
        AjoUserTransactionHistory.as_view(),
        name="existing_user_transaction_history",
    ),
    path(
        "agent/existing-user/balance-and-savings/",
        ExistingAjoUserBalancesAPIView.as_view(),
        name="ajo_user_total_balances",
    ),
    path(
        "agent/existing-user/account-details/",
        GetSpendingDigitalAccountNumbersAPIView.as_view(),
        name="ajo_spending_digital_account_details",
    ),
    path(
        "agent/existing-user/update-profile/",
        ChangeAjoUserInformationAPIView.as_view(),
        name="change_ajo_user_information",
    ),
    # requests
    path(
        "agent/cardrequests/request/",
        CardRequestByAgentAPIView.as_view(),
        name="request_card_by_agent",
    ),
    #####BOTH########
    path(
        "ajo-savings/create-plan/",
        CreateAjoSavingsPlanAPIView.as_view(),
        name="create_ajo_savings_plan",
    ),
    path(
        "ajo-savings/create-plan-v2/",
        V2CreateAjoSavingsPlanAPIView.as_view(),
        name="create_ajo_savings_plan_v2",
    ),
    path(
        "ajo-savings/plan-details/",
        AjoSavingDetailAPIView.as_view(),
        name="ajo_savings_plan_details",
    ),
    path(
        "ajo-savings/print-transactions/",
        PrintTransactionsAPIVIew.as_view(),
        name="print_history_within_range",
    ),
    path(
        "ajo-savings/create_wema_wallet/",
        CreateWemaWalletAPIView.as_view(),
        name="create_wemma_wallet",
    ),
    #####PERSONAL USERS###########
    # groups
    path(
        "personal/groups/rotation-group/",
        CreateGetEditDeleteRotationGroupAPIView.as_view(),
        name="create_rotation_group",
    ),
    path(
        "personal/groups/join-leave-group/",
        JoinLeaveRotationGroupAPIView.as_view(),
        name="join_rotation_group",
    ),
    #######USER PIN#########
    path("agent/verify-user-pin/", UserPinAPIView.as_view(), name="verify_user_pin"),
    ########PAYMENT#########
    path(
        "agent/payment/card-user-info/",
        CardUserInformationAPIView.as_view(),
        name="card_ajo_user_information",
    ),
    path(
        "agent/payment/pay-for-dojah-verification/",
        PayForDojahVerificationRequestAPIView.as_view(),
        name="pay_for_dojah_verification",
    ),
    path(
        "agent/payment/pay-for-ajo-plan/",
        PayForAjoPlanAPIView.as_view(),
        name="pay_for_ajo_plan",
    ),
    path(
        "agent/payment/commissions/", CommissionsAPIView.as_view(), name="commissions"
    ),
    path(
        "agent/payment/transfer-commissions/",
        TransferCommissionsToAgentWalletAPIView.as_view(),
        name="transfer_commissions",
    ),
    path(
        "agent/payment/request-cashout/",
        CashoutRequestAPIView.as_view(),
        name="cashout_request",
    ),
    path(
        "agent/payment/trigger-cashout/",
        TriggerCashoutWithOTPAPIView.as_view(),
        name="trigger_cashout_with_otp",
    ),
    path(
        "agent/payment/request-cashout-from-spend/",
        CashoutFromSpendingWalletAPIView.as_view(),
        name="cashout_from_spending_wallet",
    ),
    path(
        "agent/payment/trigger-cashout-from-spend/",
        TriggerCashoutFromSpendingWithOTPAPIView.as_view(),
        name="trigger_cashout_from_spending_wallet",
    ),
    path(
        "agent/payment/attempt-setting-withdrawal-account/",
        CheckAjoUserWithdrawalAccountDetailsAPIView.as_view(),
        name="check_ajo_user_withdrawal_account",
    ),
    path(
        "agent/payment/withdrawal-account/",
        GetSetDeleteVerifiedWithdrawalAccountDetailsAPIView.as_view(),
        name="set_withdrawal_account",
    ),
    path(
        "agent/payment/withdrawal-account/auth/",
        GetExternalAccountWithdrawalBalanceAPIView.as_view(),
        name="get_balance_of_wallets",
    ),
    path(
        "agent/payment/withdrawal-account/withdraw/",
        WithdrawToExternalAccountAPIView.as_view(),
        name="withdrawal_to_external_account",
    ),
    path("agent/payment/pay-loan/", PayForLoanAPIView.as_view(), name="pay_for_loan"),
    path(
        "agent/payment/withdraw-to-agency-wallet/",
        WithdrawToAgencyWalletAPIView.as_view(),
        name="withdraw_to_agency_wallet",
    ),
    ### PREFUNDING
    path(
        "agent/payment/prefunding/request/",
        PrefundingRequestAPIView.as_view(),
        name="prefunding_request",
    ),
    path(
        "agent/payment/prefunding/wallet/",
        PrefundingWalletAPIView.as_view(),
        name="prefunding_wallet",
    ),
    path(
        "agent/payment/prefunding/settle-debt/",
        SettleDueBalanceFromAgentWalletAPIView.as_view(),
        name="settle_debt_from_agent_wallet",
    ),
    #####LOANS#########
    path(
        "agent/loans/check-eligibilty/",
        LoanEligibilityAPIView.as_view(),
        name="check_loans",
    ),
    path("agent/loans/request/", RequestLoanAPIView.as_view(), name="request_loan"),
    path(
        "agent/loans/create-get-loan-details/",
        CreateGetLoanDetailsAPIView.as_view(),
        name="create_get_loan_details",
    ),
    path("agent/loans/history/", LoanHistoryAPIView.as_view(), name="loan_history"),
    path(
        "agent/loans/wallet-balance-withdraw",
        LoanWalletBalanceWithdrawAPIView.as_view(),
        name="loan_balance_withdraw",
    ),
    path(
        "agent/card_purchase/hook/",
        CardDebitGatewayView.as_view(),
        name="card_purchase",
    ),
    # agency banking
    path(
        "agent_info/",
        AgencyBankingAgentInfoAPIView.as_view(),
        name="agent_info_for_agency_banking",
    ),
    path(
        "get-ajo-plans-types/v2/", GetPlanTypesAPIView.as_view(), name="GET_AJO_PLANS"
    ),
    # agency banking
    path(
        "agent_info/",
        AgencyBankingAgentInfoAPIView.as_view(),
        name="agent_info_for_agency_banking",
    ),
    path(
        "ajo-savings/product-info/",
        ProductToPurchaseAPIView.as_view(),
        name="product_to_purchase_via_savings",
    ),
]
