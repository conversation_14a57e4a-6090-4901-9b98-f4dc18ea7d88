import json
import math
import uuid
from typing import Any, Dict, List, Optional, Union

from celery import shared_task
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.db import DatabaseError
from django.db.models import Q
from django.db import transaction as django_transaction
from django.db.models.query import QuerySet
from django.utils import timezone

from accounts.agency_banking import AgencyBankingClass, agent_login, loan_agent_login
from accounts.model_choices import UserType
from accounts.models import ConstantTable, CustomUser
from accounts.services import InterestsPaidTableMethods
from admin_dashboard.tasks import User
from chestlock.models import Onlending
from chestlock.selectors import OnlendingSelector
from payment.model_choices import (
    CommissionType,
    DisbursementProviderType,
    PlanType,
    Status,
    TransactionDestination,
    TransactionFormType,
    TransactionTypeCreditOrDebitChoices,
    WalletTypes,
)
from payment.models import DebitCreditRecordOnAccount, Transaction, WalletSystem
from payment.payment_actions import debit_wallet
from payment.services import CommissionService, TransactionService

from .model_choices import SavingsFrequency, SavingsType, TransactionDescriptionType
from .models import AjoSaving, AjoUser, Loan, RotationGroup, RotationGroupMember
from .prefunding import actions
from .selectors import (
    AjoAgentSelector,
    AjoCommissionsSelector,
    AjoSavingsSelector,
    AjoUserSelector,
    PersonalAjoSavingsSelector,
    PersonalSelector,
    PrefundingSelector,
    RotationGroupMemberSelector,
    RotationGroupSelector,
    get_ajo_rosca_user_spend_wallet,
)
from .services import (
    AjoSavingsService,
    AjoUserService,
    LoanService,
    PrefundingService,
    RotationGroupMemberService,
)
from .utils.otp_utils import ajo_rosca_collection_sms, send_out_sms_for_ajo_payment


def transaction_description(transaction_type: TransactionDescriptionType, amount: str):
    if transaction_type == TransactionDescriptionType.TRANSFER_FOR_AJO:

        def transfer_transaction_description(
            phone_number: str, ajo_saving_plan_name: str
        ):
            description = f"transferred {amount} to ajo user, {phone_number}, for {ajo_saving_plan_name} ajo savings plan."
            return description

        return transfer_transaction_description

    if transaction_type == TransactionDescriptionType.AJO_SAVING_DEPOSIT:

        def saving_deposit_description(ajo_saving_plan_name: str):
            description = f"{amount} was deposited into your ajo savings plan, {ajo_saving_plan_name}."
            return description

        return saving_deposit_description

    if transaction_type == TransactionDescriptionType.DEBIT_FOR_COMMISSION:

        def debit_for_commission_description(ajo_saving_plan_name: str):
            description = f"{amount} was debited from ajo plan, {ajo_saving_plan_name}, and taken as commission"
            return description

        return debit_for_commission_description

    if transaction_type == TransactionDescriptionType.FUND_AGENT_FROM_COMMISSION:

        def fund_agent_from_commission_description():
            description = f"{amount} was funded into your wallet from your commissions."
            return description

        return fund_agent_from_commission_description

    if transaction_type == TransactionDescriptionType.DEBIT_COMMISSION_AGENT_BALANCE:

        def debit_agent_commission_balance_description():
            description = f"{amount} was debited from your commissions balance"
            return description

        return debit_agent_commission_balance_description

    if transaction_type == TransactionDescriptionType.CASHOUT_BY_USSD:

        def cashout_by_ussd_description(ajo_savings_plan_name: str):
            return f"{amount} was cashed out from your ajo savings plan, {ajo_savings_plan_name}"

        # def cashout_by_ussd_description():
        #     return f"{amount} was cashed out from your spending wallet"

        return cashout_by_ussd_description

    if transaction_type == TransactionDescriptionType.TRANSFER_FOR_LOAN:

        def loan_transfer_transaction_description(
            phone_number: str, loan_quotation_id: str
        ):
            description = f"transferred {amount} to ajo user, {phone_number}, for loan with id, {loan_quotation_id}."
            return description

        return loan_transfer_transaction_description

    if transaction_type == TransactionDescriptionType.LOAN_DEPOSIT:

        def saving_deposit_description(loan_quotation_id: str):
            description = f"{amount} was deposited into your loan plan with id, {loan_quotation_id}."
            return description

        return saving_deposit_description

    if transaction_type == TransactionDescriptionType.CREATE_LOAN:

        def create_loan_descripiton():
            description = f"{amount} was placed in your loan wallet"
            return description

        return create_loan_descripiton

    if transaction_type == TransactionDescriptionType.CASHOUT_TO_AGENT_WALLET:

        def create_cashout_to_agent_wallet(ajo_savings_plan_name: str):
            return f"{amount} was cashed out from ajo savings plan, {ajo_savings_plan_name}"

        return create_cashout_to_agent_wallet
        # def cashout_to_agent_wallet(ajo_user_alias: str):
        #     return f"{amount} was cashed out from {ajo_user_alias} spending wallet"

        # return cashout_to_agent_wallet

    if transaction_type == TransactionDescriptionType.DEBIT_LOAN_WALLET:

        def debit_loan_wallet():
            description = f"{amount} was debited from your loan wallet"
            return description

        return debit_loan_wallet

    if transaction_type == TransactionDescriptionType.CREDIT_AGENT_WALLET_FROM_LOAN:

        def credit_agent_from_loan(ajo_user_phone_number: str):
            description = f"{amount} was just credited to you from ajo user, {ajo_user_phone_number}, loan wallet"
            return description

        return credit_agent_from_loan

    if transaction_type == TransactionDescriptionType.WITHDRAW_TO_AGENCY_WALLET:

        def withdraw_to_agent_wallet() -> str:
            description = f"{amount} was just withdrawn to agency wallet"
            return description

        return withdraw_to_agent_wallet

    if transaction_type == TransactionDescriptionType.REVERSE_WITHDRAWAL:

        def reverse_withdrawal():
            description = f"{amount} was reversed to your ajo agent wallet due to failed transaction"
            return description

        return reverse_withdrawal

    if transaction_type == TransactionDescriptionType.FUND_AGENT_FROM_VIRTUAL_ACCOUNT:

        def fund_agent_from_account(account_number: str):
            description = (
                f"{amount} was just funded from your virtual account, {account_number}"
            )
            return description

        return fund_agent_from_account

    if (
        transaction_type
        == TransactionDescriptionType.FUND_AGENT_FROM_LIBERTYPAY_TO_AJO_WALLET
    ):

        def fund_agent_from_lpay_to_ajo_wallet():
            description = f"{amount} was funded into your wallet from Liberty Pay."
            return description

        return fund_agent_from_lpay_to_ajo_wallet

    if transaction_type == TransactionDescriptionType.DEBIT_FOR_PLAN_MATURITY:

        def debit_savings_wallet(ajo_savings_plan_name: str):
            return f"{amount} was debited from your savings wallet as {ajo_savings_plan_name} has matured"

        return debit_savings_wallet

    if transaction_type == TransactionDescriptionType.CASHOUT_TO_AGENT_FROM_SPEND:

        def cashout_from_spending_wallet_to_agent():
            return f"{amount} was cashed out from your spending wallet"

        return cashout_from_spending_wallet_to_agent

    if transaction_type == TransactionDescriptionType.DEBIT_FOR_AJOLOAN_ESCROW:

        def move_fund_from_savings_to_escrow(ajo_savings_plan_name: str):
            return f"{amount} was moved out from your savings plan - {ajo_savings_plan_name} to your escrow wallet"

        return move_fund_from_savings_to_escrow

    if (
        transaction_type
        == TransactionDescriptionType.PROSPER_LOAN_DEPOSIT_FROM_LIBERTYPAY_TO_PROSPER_INV_WALLET
    ):

        def deposit_to_prosper_loan():
            description = f"{amount} was just deposited for prosper loan wallet"
            return description

        return deposit_to_prosper_loan

    if transaction_type == TransactionDescriptionType.TRANSFER_FROM_AGENT_TO_AGENT:

        def transfer_from_agent_to_agent(to_email: str):
            return (
                f"{amount} was just transferred to {to_email} for inter-agent reasons."
            )

        return transfer_from_agent_to_agent


def check_if_agent_can_pay(agent_wallet: WalletSystem, amount: float) -> bool:
    """
    Checks if the agent can pay a certain amount

    If the agent cannot:
    i. pay 3 times the amount if amount is less than 1000,
    ii. 2 times the amount if the amount is less than 3000, and,
    iii. the amount if the amount is greater than 3000
    with the amount in the available balance without
    finishing the money in the agent wallet, it won't go through.

    Args:
        agent_wallet (WalletSystem): the agent's wallet
        amount (float): the amount to be paid

    Returns:
        bool: True or False to signify that the agent can pay
    """
    # if amount < 1000:
    #     check = (agent_wallet.available_balance - (amount * 3)) > 0
    # elif amount < 3000:
    #     check = (agent_wallet.available_balance - (amount * 2)) > 0
    # else:
    #     check = (agent_wallet.available_balance - amount) > 0

    return (agent_wallet.available_balance - amount) >= 0


@django_transaction.atomic()
def debit_agent(
    amount: float,
    user: AbstractUser,
    wallet: WalletSystem,
    payload: dict,
    transaction_description: str,
) -> None:
    #

    # create the transaction instance
    transfer_debit_transaction: Transaction = (
        TransactionService.create_transfer_to_ajo_plan_deposit_transaction(
            user=user,
            amount=amount,
            wallet_type=WalletTypes.AJO_AGENT,
            plan_type=PlanType.AJO,
            transaction_description=transaction_description,
        )
    )

    # debit the agent's wallet
    debit = WalletSystem.deduct_balance(
        wallet=wallet,
        amount=amount,
        transaction_instance=transfer_debit_transaction,
    )

    prefunding_selector = PrefundingSelector(user=user)

    # update the fields
    transfer_debit_transaction.status = Status.SUCCESS
    transfer_debit_transaction.transaction_date_completed = timezone.localtime()
    transfer_debit_transaction.payload = payload
    transfer_debit_transaction.wallet_balance_before = debit.get("balance_before")
    transfer_debit_transaction.wallet_balance_after = debit.get("balance_after")
    transfer_debit_transaction.credit_balance = (
        prefunding_selector.available_balance_in_prefunding_wallet()
    )
    transfer_debit_transaction.due_balance = (
        prefunding_selector.how_much_prefunding_has_been_used_against_hold_balance()
    )

    transfer_debit_transaction.save()


@django_transaction.atomic()
def fund_ajo_user(
    user: AbstractUser,
    amount: float,
    quotation_id: str,
    wallet: WalletSystem,
    ajo_user: AjoUser,
    ajo_savings: AjoSaving,
    payload: dict | None = None,
) -> None:
    # obtain the transaction description
    transaction_description_func = transaction_description(
        TransactionDescriptionType.AJO_SAVING_DEPOSIT,
        amount=amount,
    )
    description = transaction_description_func(
        ajo_saving_plan_name=ajo_savings.name,
    )

    # create deposit transaction for the ajo user
    deposit_transaction = TransactionService.create_deposit_by_wallet_transaction(
        user=user,
        amount=amount,
        wallet_type=WalletTypes.AJO_USER,
        quotation_id=quotation_id,
        plan_type=PlanType.AJO,
        ajo_user=ajo_user,
    )

    # fund the ajo user's wallet
    credit = WalletSystem.fund_balance(
        wallet=wallet,
        amount=amount,
        transaction_instance=deposit_transaction,
        onboarded_user=ajo_user,
    )

    # update the fields
    deposit_transaction.status = Status.SUCCESS
    deposit_transaction.description = description
    deposit_transaction.transaction_date_completed = timezone.localtime()
    deposit_transaction.payload = payload
    deposit_transaction.wallet_balance_before = credit.get("balance_before")
    deposit_transaction.wallet_balance_after = credit.get("balance_after")
    deposit_transaction.plan_balance_before = ajo_savings.plan_balance_before
    deposit_transaction.plan_balance_after = ajo_savings.plan_balance_after
    deposit_transaction.save()

    return deposit_transaction


def fund_ajo_savings_position(
    user: AbstractUser,
    ajo_user: AjoUser,
    ajo_savings: AjoSaving,
    amount: float,
    request_data: dict | None = None,
) -> Transaction:
    # call the Ajo Savings Service, AjoUserService
    ajo_savings_service = AjoSavingsService(ajo_savings=ajo_savings)

    # fund the ajo savings
    ajo_savings = ajo_savings_service.fund_plan(amount=amount)

    # fund the ajo user's position
    deposit_transaction = fund_ajo_user(
        user=user,
        amount=amount,
        quotation_id=ajo_savings.quotation_id,
        wallet=AjoUserSelector(ajo_user=ajo_user).get_ajo_user_wallet(),
        ajo_user=ajo_user,
        ajo_savings=ajo_savings,
        payload=request_data,
    )

    # update ajo user fields
    AjoUserService(ajo_user=ajo_user).increment_total_saved(amount=amount)

    if not ajo_savings.is_activated:
        ajo_savings.is_activated = True
        ajo_savings.save()

    # calculate the days paid
    ajo_savings.calculate_frequency_paid(save=True)

    return deposit_transaction


def terminate_plan(ajo_plan: AjoSaving) -> str:
    """
    Terminate an ajo plan by moving money to spend wallet and updating fields

    Args:
        ajo_plan (AjoSaving): the Ajo Plan to be terminated

    Raises:
        ValueError: error occurred while moving money

    Returns:
        str: plan no longer active and money moved to spend
    """
    try:
        move_mature_funds_to_spending_for_ajo_user(ajo_plan=ajo_plan)

        return "plan is no longer active and money moved to spend wallet"

    except Exception as err:
        raise ValueError(f"something went wrong: {str(err)}")


@django_transaction.atomic
def pay_for_ajo_plan(
    amount: float,
    ajo_user: AjoUser,
    ajo_savings: AjoSaving,
    user: AbstractUser,
    request_data: dict,
) -> Transaction | str:
    # obtain the agent's wallet
    agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()

    # instantiate the ajo user methods and service classes
    ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)

    # ajo_user_service = AjoUserService(ajo_user=ajo_user)
    # obtain the ajo user's wallet
    # ajo_user_wallet = ajo_user_selector.get_ajo_user_wallet()

    # instantiate the ajo savings methods class
    # ajo_savings_service = AjoSavingsService(ajo_savings=ajo_savings)

    if ajo_savings.amount_saved >= ajo_savings.expected_amount and (
        not ajo_savings.lock
    ):
        if (not ajo_savings.loan) or (ajo_savings.savings_type is not None):
            return terminate_plan(ajo_plan=ajo_savings)

        else:
            ajo_savings.is_active = False
            ajo_savings.save()
            return "this ajo plan is no longer active"

    # if ajo_savings.amount_saved >= ajo_savings.expected_amount:
    #     try:
    #         move_mature_funds_to_spending_for_ajo_user(ajo_plan=ajo_savings)
    #         return "plan has been deactivated and money moved to spend wallet"
    #     except Exception as err:
    #         raise ValueError(f"something went wrong: {str(err)}")

    ####transaction description for debit agent
    # obtain the transaction description
    transaction_description_func = transaction_description(
        TransactionDescriptionType.TRANSFER_FOR_AJO,
        amount=amount,
    )
    debit_agent_description = transaction_description_func(
        phone_number=ajo_user.phone_number,
        ajo_saving_plan_name=ajo_savings.name,
    )

    # debit the agent's position
    debit_agent(
        amount=amount,
        user=user,
        wallet=agent_wallet,
        payload=request_data,
        transaction_description=debit_agent_description,
    )

    deposit_transaction = fund_ajo_savings_position(
        user=user,
        ajo_user=ajo_user,
        ajo_savings=ajo_savings,
        amount=amount,
        request_data=request_data,
    )
    # # fund the ajo savings
    # ajo_savings = ajo_savings_service.fund_plan(amount=amount)

    # # fund the ajo user's position
    # deposit_transaction = fund_ajo_user(
    #     user=user,
    #     amount=amount,
    #     quotation_id=ajo_savings.quotation_id,
    #     wallet=ajo_user_wallet,
    #     ajo_user=ajo_user,
    #     ajo_savings=ajo_savings,
    #     payload=request_data,
    # )

    # # update ajo user fields
    # ajo_user_service.increment_total_saved(amount=amount)

    # if not ajo_savings.is_activated:
    #     ajo_savings.is_activated = True
    #     ajo_savings.save()

    # # calculate the days paid
    # ajo_savings.calculate_frequency_paid(save=True)

    return deposit_transaction


@django_transaction.atomic
def pay_for_agent_ajo_user_contribution(
    user: AbstractUser,
    amount: float,
    request_data: dict,
    agent_wallet: WalletSystem,
    due_instance: RotationGroupMember,
    total_due_contributed,
    count,
) -> Transaction:
    # create transaction objects

    trans_obj = TransactionService.dynamic_deduction_from_wallet_transaction(
        user=user,
        amount=amount,
        wallet_type=agent_wallet.wallet_type,
        request_data=request_data,
        transaction_form_type=TransactionFormType.AGENT_ROSCA_DEDUCTION,
    )
    # deduct agent wallet
    deduct_agent_wallet = WalletSystem().deduct_balance(
        wallet=agent_wallet,
        amount=amount,
        transaction_instance=trans_obj,
        unique_reference=None,
        onboarded_user=None,
    )
    # update transaction
    trans_obj.status = Status.SUCCESS
    trans_obj.transaction_date_completed = timezone.now()
    trans_obj.wallet_balance_before = deduct_agent_wallet["balance_before"]
    trans_obj.wallet_balance_after = deduct_agent_wallet["balance_after"]
    trans_obj.save()

    agent_comms_wallet = AjoCommissionsSelector(user=user).get_commissions_wallet()
    commission_total = due_instance.group.fees * count
    company_user = get_user_model().objects.filter(email="<EMAIL>").last()

    total_due_contributed = total_due_contributed - commission_total

    split_agent_rosca_commissions(
        shared_amount=commission_total,
        wallet=agent_comms_wallet,
        company_user=company_user,
        transaction_description="AGENT_ROSCA_COMMISIONS",
        quotation_id=f"RTG-COM-CREDIT_{uuid.uuid4()}",
    )

    if due_instance:
        if due_instance.group.no_of_collectors <= 1:
            due_member_instence = due_instance.group.ajo_members.filter(
                collection_date=due_instance.rosca_due_date,
                position=due_instance.day_count,
            ).last()
            ajo_user = due_member_instence.ajo_user_member
            ajo_user_wallet = get_ajo_rosca_user_spend_wallet(ajo_user=ajo_user)

            deposit_transaction = (
                TransactionService.create_deposit_by_wallet_transaction(
                    user=user,
                    amount=total_due_contributed,
                    wallet_type=ajo_user_wallet.wallet_type,
                    quotation_id=f"RTG-CREDIT_{uuid.uuid4()}",
                    plan_type=PlanType.ROTATIONGROUP,
                    ajo_user=ajo_user,
                )
            )
            credit_wallet = WalletSystem.fund_balance(
                wallet=ajo_user_wallet,
                amount=total_due_contributed,
                transaction_instance=deposit_transaction,
                onboarded_user=ajo_user,
            )
            deposit_transaction.status = Status.SUCCESS
            deposit_transaction.transaction_date_completed = timezone.now()
            deposit_transaction.wallet_balance_before = credit_wallet["balance_before"]
            deposit_transaction.wallet_balance_after = credit_wallet["balance_after"]
            deposit_transaction.save()
            ajo_rosca_collection_sms(
                ajo_user=ajo_user,
                phone_number=ajo_user.phone_number,
                name=ajo_user.first_name,
                group=due_instance.group.name,
                amount=total_due_contributed,
            )
            due_member_instence.total_amount_collected += total_due_contributed
            due_member_instence.save()

            if (
                due_member_instence.total_amount_collected
                >= due_member_instence.group.collection_amount
            ):
                due_member_instence.collected = True
                due_member_instence.save()

                # Charge platform fee
                constant = ConstantTable.get_constant_table_instance()
                platform_fee = constant.rosca_platform_fee

                trans_obj = TransactionService.dynamic_deduction_from_wallet_transaction(
                    user=user,
                    amount=platform_fee,
                    wallet_type=ajo_user_wallet.wallet_type,
                    request_data=request_data,
                    transaction_form_type=TransactionFormType.AGENT_ROSCA_PLATFORM_FEE,
                )
                credit_wallet = WalletSystem.deduct_balance(
                    wallet=ajo_user_wallet,
                    amount=platform_fee,
                    transaction_instance=deposit_transaction,
                    onboarded_user=ajo_user,
                )
                trans_obj.status = Status.SUCCESS
                trans_obj.transaction_date_completed = timezone.now()
                trans_obj.wallet_balance_before = credit_wallet["balance_before"]
                trans_obj.wallet_balance_after = credit_wallet["balance_after"]
                trans_obj.save()
                fund_company_commissions_wallet(
                    company_user=company_user,
                    amount=platform_fee,
                    transaction_description="Platform Fee",
                    transaction_form_type=TransactionFormType.AGENT_ROSCA_PLATFORM_FEE,
                )

        # multiple collectors
        if due_instance.group.no_of_collectors > 1:
            due_member_instence = due_instance.group.ajo_members.filter(
                collection_date=due_instance.rosca_due_date
            )
            for member in due_member_instence:
                ajo_user = member.ajo_user_member

                ajo_user_wallet = get_ajo_rosca_user_spend_wallet(ajo_user=ajo_user)

                # DIVIDE THE TOTAL CONTRUBUTED BY THE NUMBER OF COLLECTORS
                shared_amount = (
                    total_due_contributed / due_instance.group.no_of_collectors
                )

                deposit_transaction = (
                    TransactionService.create_deposit_by_wallet_transaction(
                        user=user,
                        amount=shared_amount,
                        wallet_type=ajo_user_wallet.wallet_type,
                        quotation_id=f"RTG-CREDIT_{uuid.uuid4()}",
                        plan_type=PlanType.ROTATIONGROUP,
                        ajo_user=ajo_user,
                    )
                )
                credit_wallet = WalletSystem.fund_balance(
                    wallet=ajo_user_wallet,
                    amount=shared_amount,
                    transaction_instance=deposit_transaction,
                    onboarded_user=ajo_user,
                )
                deposit_transaction.status = Status.SUCCESS
                deposit_transaction.transaction_date_completed = timezone.now()
                deposit_transaction.wallet_balance_before = credit_wallet[
                    "balance_before"
                ]
                deposit_transaction.wallet_balance_after = credit_wallet[
                    "balance_after"
                ]
                deposit_transaction.save()
                ajo_rosca_collection_sms(
                    ajo_user=ajo_user,
                    phone_number=ajo_user.phone_number,
                    name=ajo_user.first_name,
                    group=due_instance.group.name,
                    amount=shared_amount,
                )

                member.total_amount_collected += shared_amount
                member.save()

                if member.total_amount_collected >= member.group.collection_amount:
                    member.collected = True
                    member.save()
                    # charge platform fee
                    constant = ConstantTable.get_constant_table_instance()
                    platform_fee = constant.rosca_platform_fee

                    trans_obj = TransactionService.dynamic_deduction_from_wallet_transaction(
                        user=user,
                        amount=platform_fee,
                        wallet_type=ajo_user_wallet.wallet_type,
                        request_data=request_data,
                        transaction_form_type=TransactionFormType.AGENT_ROSCA_PLATFORM_FEE,
                    )
                    credit_wallet = WalletSystem.deduct_balance(
                        wallet=ajo_user_wallet,
                        amount=platform_fee,
                        transaction_instance=deposit_transaction,
                        onboarded_user=ajo_user,
                    )
                    trans_obj.status = Status.SUCCESS
                    trans_obj.transaction_date_completed = timezone.now()
                    trans_obj.wallet_balance_before = credit_wallet["balance_before"]
                    trans_obj.wallet_balance_after = credit_wallet["balance_after"]
                    trans_obj.save()

                    fund_company_commissions_wallet(
                        company_user=company_user,
                        amount=platform_fee,
                        transaction_description="Platform Fee",
                        transaction_form_type=TransactionFormType.AGENT_ROSCA_PLATFORM_FEE,
                    )

        return {"status": True, "credit": credit_wallet, "charge": deduct_agent_wallet}


@django_transaction.atomic
def debit_ajo_plan(
    ajo_savings: AjoSaving,
    amount: float,
    wallet: WalletSystem,
    transaction_instance: Transaction,
) -> None:
    # firstly deduct the ajo user wallet and create the debit_credit_info instance

    ajo_user = ajo_savings.ajo_user
    # deduct = WalletSystem.deduct_balance(
    #     wallet=wallet,
    #     amount=amount,
    #     transaction_instance=transaction_instance,
    #     onboarded_user=ajo_user,
    # )
    deduct = debit_wallet(
        wallet=wallet,
        amount=amount,
        transaction_instance=transaction_instance,
        onboarded_user=ajo_user,
    )

    # set the transaction wallet, plan balance before and plan_instance balance before
    transaction_instance.wallet_balance_before = deduct.get("balance_before")
    transaction_instance.plan_balance_before = ajo_savings.amount_saved
    ajo_savings.plan_balance_before = ajo_savings.amount_saved

    # decrease the amount saved
    ajo_savings.amount_saved -= amount

    # set the balance after as done above for balance before
    transaction_instance.wallet_balance_after = deduct.get("balance_after")
    transaction_instance.plan_balance_after = (
        transaction_instance.plan_balance_before - amount
    )
    ajo_savings.plan_balance_after = ajo_savings.plan_balance_before - amount

    # save the changes
    ajo_savings.save()
    ajo_savings.refresh_from_db()
    # set some other fields
    transaction_instance.transaction_date_completed = timezone.localtime()
    transaction_instance.save()

    # calculate days paid
    ajo_savings.calculate_frequency_paid(save=True)


@django_transaction.atomic
def debit_wallet_and_update_transaction(
    amount: float,
    wallet: WalletSystem,
    transaction_instance: Transaction,
) -> Dict[str, Any]:
    # firstly deduct the ajo user wallet and create the debit_credit_info instance
    ajo_user = transaction_instance.onboarded_user

    deduct = debit_wallet(
        wallet=wallet,
        amount=amount,
        transaction_instance=transaction_instance,
        onboarded_user=ajo_user,
    )
    # deduct = WalletSystem.deduct_balance(
    #     wallet=wallet,
    #     amount=amount,
    #     transaction_instance=transaction_instance,
    #     onboarded_user=ajo_user,
    # )

    # update the fields
    transaction_instance.status = Status.SUCCESS
    transaction_instance.transaction_date_completed = timezone.localtime()
    transaction_instance.wallet_balance_before = deduct.get("balance_before")
    transaction_instance.wallet_balance_after = deduct.get("balance_after")
    transaction_instance.save()

    return deduct


@django_transaction.atomic
def debit_wallet_and_fill_transaction(
    amount: float,
    wallet: WalletSystem,
    transaction_instance: Transaction,
    unique_reference: str | None = None,
) -> None:
    """
    This debits a wallet and fills only the necessary fields in the transaction table

    Args:
        amount (float): the amount debited
        wallet (WalletSystem): the wallet to be debited
        transaction_instance (Transaction): the transaction instance
    """
    deduct = WalletSystem.deduct_balance(
        wallet=wallet,
        amount=amount,
        transaction_instance=transaction_instance,
        onboarded_user=transaction_instance.onboarded_user,
        unique_reference=unique_reference,
    )

    # fill the fields
    transaction_instance.transaction_date_completed = timezone.localtime()
    transaction_instance.wallet_balance_before = deduct.get("balance_before")
    transaction_instance.wallet_balance_after = deduct.get("balance_after")
    transaction_instance.save()

    return deduct


@django_transaction.atomic
def update_transaction_status(
    wallet: WalletSystem,
    transaction_instance: Transaction,
    success=True,
) -> None:
    """
    This function tries to update the status of any transaction instance
    """
    # update the fields
    transaction_instance.status = Status.SUCCESS if success is True else Status.FAILED
    transaction_instance.transaction_date_completed = timezone.localtime()
    transaction_instance.wallet_balance_before = wallet.available_balance
    transaction_instance.wallet_balance_after = wallet.available_balance
    transaction_instance.save()

    return transaction_instance


def calculate_commissions(amount: float) -> Dict[str, float]:
    """
    Calculates the amount for the company and the agent

    Args:
        amount (float): the amount to be shared

    Returns:
        Dict[str, float]: A dictionary containing the "company_share" and "agent_share"
    """
    # call the constants table
    constants = ConstantTable.get_constant_table_instance()

    # obtain the commissions for the company
    company_percentage = constants.commissions_rate

    # calculate the amount for the company
    company_share = round((company_percentage / 100) * amount, 2)

    # calculate the amount for the agent
    agent_share = round(amount - company_share, 2)

    return {
        "company_share": company_share,
        "agent_share": agent_share,
    }


def calculate_rosca_commissions(amount: float) -> Dict[str, float]:
    """
    Calculates the amount for the company and the agent

    Args:
        amount (float): the amount to be shared

    Returns:
        Dict[str, float]: A dictionary containing the "company_share" and "agent_share"
    """
    # call the constants table
    constants = ConstantTable.get_constant_table_instance()

    # obtain the commissions for the company
    agent_percentage = constants.agent_rosca_commision_rate

    # calculate the amount for the agent
    agent_share = round((agent_percentage / 100) * amount, 2)

    # calculate the amount for the company
    company_share = round(amount - agent_share, 2)

    return {
        "company_share": company_share,
        "agent_share": agent_share,
    }


def fund_wallet_and_update_transaction(
    wallet: WalletSystem,
    amount: float,
    transaction_instance: Transaction,
    unique_reference: str | None = None,
    ajo_user: AjoUser | None = None,
) -> dict:
    """
    Increments the ajo agent's commissions balance of the ajo commissions wallet

    Args:
        wallet (WalletSystem): the ajo agent's wallet
        amount (float): the amount of money
        transaction_instance (Transaction): the transaction instance
        unique_reference (str): the unique reference. Defaults to None.
        ajo_user (AjoUser): the ajo user the transaction belongs to. Defaults to None.

    Returns:
        Dict
    """
    # firstly increment the ajo user's wallet and create the debit credit info
    fund = WalletSystem.fund_balance(
        wallet=wallet,
        amount=amount,
        transaction_instance=transaction_instance,
        unique_reference=unique_reference,
        onboarded_user=ajo_user,
    )

    # update the transaction fields
    transaction_instance.status = Status.SUCCESS
    transaction_instance.transaction_date_completed = timezone.localtime()
    transaction_instance.wallet_balance_before = fund.get("balance_before")
    transaction_instance.wallet_balance_after = fund.get("balance_after")
    transaction_instance.save()

    return fund


def fund_company_commissions_wallet(
    amount: float,
    company_user: AbstractUser,
    transaction_description: str,
    quotation_id: str | None = None,
    ajo_savings: AjoSaving | None = None,
    transaction_form_type: TransactionFormType | None = None,
):
    # obtain the company's commission wallet
    company_commissions_wallet = AjoCommissionsSelector(
        user=company_user
    ).get_commissions_wallet()

    # create a transaction instance
    increment_transaction = (
        TransactionService.create_agent_commissions_increment_transaction(
            user=company_user,
            amount=amount,
            quotation_id=ajo_savings.quotation_id if ajo_savings else quotation_id,
            transaction_description=transaction_description,
            transaction_form_type=(
                transaction_form_type
                if transaction_form_type
                else TransactionFormType.COMMISSION
            ),
        )
    )

    # increment the commissions balance of the company's wallet
    fund_wallet_and_update_transaction(
        wallet=company_commissions_wallet,
        amount=amount,
        transaction_instance=increment_transaction,
    )


@django_transaction.atomic
def deduct_and_allocate_commissions(
    user: AbstractUser,
    ajo_savings: AjoSaving,
    ajo_user_wallet: WalletSystem,
    amount: float,
) -> None:
    ajo_user = ajo_savings.ajo_user

    # obtain the transaction description
    transaction_description_func = transaction_description(
        TransactionDescriptionType.DEBIT_FOR_COMMISSION,
        amount=amount,
    )
    description = transaction_description_func(
        ajo_saving_plan_name=ajo_savings.name,
    )

    # create a Transaction instance
    debit_transaction = TransactionService.create_ajo_commissions_transaction(
        user=user,
        amount=amount,
        quotation_id=ajo_savings.quotation_id,
        ajo_user=ajo_user,
        transaction_description=description,
    )

    # call a function that debits the Ajo savings plan
    debit_ajo_plan(
        ajo_savings=ajo_savings,
        amount=amount,
        wallet=ajo_user_wallet,
        transaction_instance=debit_transaction,
    )

    # obtain the commissions service charge
    commissions_service_charge = (
        ConstantTable.get_constant_table_instance().commissions_service_charge
    )

    # collect the service charge
    amount = amount - commissions_service_charge

    # create a transaction instance for the service charge
    service_charge_transaction = TransactionService.service_charge_transaction(
        user=user,
        amount=commissions_service_charge,
        ajo_user=ajo_user,
        quotation_id=ajo_savings.quotation_id,
        transaction_description=f"{commissions_service_charge} was collected as a service charge for commission from ajo plan, {ajo_savings.name}",
    )

    # if user.user_type != UserType.STAFF_AGENT:

    # call a function that does the calculation and returns how much for company and agent
    shares = calculate_commissions(amount=amount)

    # designate company share
    company_share = shares.get("company_share")

    agent_description_text = (
        f"{shares.get('agent_share')} commission earned, from {ajo_savings.name}"
    )
    agent_description_text += (
        f" {ajo_savings.frequency.lower()} savings, {ajo_user.phone_number[1:]}"
    )

    agent_commission = CommissionService.create_commission_instance(
        user=user,
        commission_type=CommissionType.AGENT,
        amount=shares.get("agent_share"),
        description=agent_description_text,
        quotation_id=ajo_savings.quotation_id,
        total_amount_taken_as_commission=amount,
        plan_type=PlanType.AJO,
    )

    # call the commissions selector
    commissions_selector = AjoCommissionsSelector(user=user)

    # obtain the commissions wallet
    commissions_wallet = commissions_selector.get_commissions_wallet()

    # create the transaction instance
    increment_transaction = (
        TransactionService.create_agent_commissions_increment_transaction(
            user=user,
            amount=shares.get("agent_share"),
            quotation_id=ajo_savings.quotation_id,
            transaction_description=agent_description_text,
        )
    )

    # increment the commissions balance of the agent's wallet
    fund_wallet_and_update_transaction(
        wallet=commissions_wallet,
        amount=shares.get("agent_share"),
        transaction_instance=increment_transaction,
    )

    # else:
    #     company_share = amount

    # try to obtain the company's user account
    company_user = get_user_model().objects.filter(email="<EMAIL>").last()

    # company description text
    com_description_text = f"{company_share} commission earned, from {ajo_savings.name}"
    com_description_text += f" {ajo_savings.frequency.lower()} savings, {ajo_savings.ajo_user.phone_number[1:]}"

    # call functions to create the Commission instances for both company and agent
    company_commission = CommissionService.create_commission_instance(
        user=company_user,
        commission_type=CommissionType.COMPANY,
        amount=company_share,
        description=com_description_text,
        quotation_id=ajo_savings.quotation_id,
        total_amount_taken_as_commission=amount,
        plan_type=PlanType.AJO,
    )

    # call the function for fund the company commission wallet
    if company_user:
        fund_company_commissions_wallet(
            amount=company_share,
            company_user=company_user,
            ajo_savings=ajo_savings,
            transaction_description=com_description_text,
        )

    # indicate that commission has been taken
    ajo_savings.commission_paid = True
    ajo_savings.save()


@shared_task
@django_transaction.atomic
def pay_commissions(ajo_savings_id: int, user_id: int):
    """
    This task handles the collection of commissions from the
    ajo savings plan

    Args:
        ajo_savings_id (int): the ID of the ajo plan.
        user_id (int): the ID of the user.

    Raises:
        Exception: any error caught while attempting flow.
    """
    try:
        # retrieve user
        user = get_user_model().objects.get(id=user_id)
        # call the ajo savings selector class
        ajo_saving_selector = AjoSavingsSelector(
            id=ajo_savings_id,
            user=user,
        )
        # retrieve the ajo plan
        ajo_savings = ajo_saving_selector.get_ajo_saving_plan()

        # obtain the ajo user
        ajo_user = ajo_savings.ajo_user

        # call the selector class
        ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)

        # obtain the wallet
        ajo_user_wallet = ajo_user_selector.get_ajo_user_wallet()

        # declare the amount
        amount = (
            ajo_savings.periodic_amount
            if ajo_savings.plan_type
            else ajo_savings.commission_amount
        )

        # check if amount saved is greater than periodic amount
        if not ajo_savings.amount_saved >= amount:
            return {
                "status": "failed",
                "message": "not enough to take out commissions",
            }

        # ajo_savings.refresh_from_db()
        # check if the commission has been paid already
        if ajo_savings.commission_paid:
            return {
                "status": "failed",
                "message": "commissions has already been paid for this plan",
            }

        if ajo_savings.loan:
            return {
                "status": "failed",
                "message": "this is a loan savings, hence no commissions to deduct",
            }

        if ajo_savings.savings_type:
            return {
                "status": "failed",
                "message": "this is either a BNPL/SNPL savings, hence no commissions to deduct",
            }

        # filter transaction to check if commission transaction exists
        if Transaction.objects.filter(
            user=user,
            transaction_form_type=TransactionFormType.COMMISSION,
            quotation_id=ajo_savings.quotation_id,
            onboarded_user=ajo_savings.ajo_user,
            transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
        ).exists():
            return {
                "status": "failed",
                "message": "commission has been taken out already",
            }

        deduct_and_allocate_commissions(
            user=user,
            ajo_savings=ajo_savings,
            ajo_user_wallet=ajo_user_wallet,
            amount=amount,
        )

        return {
            "status": "success",
            "message": f"commission taken out from {ajo_savings.name} and distributed successfully",
        }

        # exit out of the function.

    except Exception as err:
        # handle all exceptions
        print(str(err))


@shared_task
@django_transaction.atomic
def collect_loan_plan_commissions(ajo_savings_id: int, user_id: int):
    """
    This task handles the collection of commissions from the
    ajo loan savings plan

    Args:
        ajo_savings_id (int): the ID of the ajo plan.
        user_id (int): the ID of the user.

    Raises:
        Exception: any error caught while attempting flow.
    """
    from loans.helpers.repayments import (
        eligibility_more_than_3_and_no_loan,
        paid_processing_fee_no_crc_user,
    )

    # retrieve user
    user = get_user_model().objects.get(id=user_id)

    # obtain the savings plan
    ajo_savings = AjoSavingsSelector(
        id=ajo_savings_id,
        user=user,
    ).get_ajo_saving_plan()

    ajo_user = ajo_savings.ajo_user
    ajo_user_wallet = AjoUserSelector(ajo_user=ajo_user).get_ajo_user_wallet()

    if eligibility_more_than_3_and_no_loan(ajo_user=ajo_user):
        amount = round((ajo_savings.expected_amount * (5 / 100)), 2)
    elif paid_processing_fee_no_crc_user(ajo_user=ajo_user):
        raise ValueError("commission has been taken out already")
    else:
        amount = (
            ajo_savings.periodic_amount
            if ajo_savings.plan_type
            else ajo_savings.commission_amount
        )
    print(amount)
    # filter transaction to check if commission transaction exists
    if Transaction.objects.filter(
        user=user,
        transaction_form_type=TransactionFormType.COMMISSION,
        quotation_id=ajo_savings.quotation_id,
        onboarded_user=ajo_savings.ajo_user,
        transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
    ).exists():
        raise ValueError("commission has been taken out already")
        # return {
        #     "status": "failed",
        #     "message": "commission has been taken out already",
        # }

    # check if amount saved is greater than periodic amount
    if not ajo_savings.amount_saved >= amount:
        raise ValueError("not enough to take out commissions")
        # return {
        #     "status": "failed",
        #     "message": "not enough to take out commissions",
        # }

    deduct_and_allocate_commissions(
        user=user,
        ajo_savings=ajo_savings,
        ajo_user_wallet=ajo_user_wallet,
        amount=amount,
    )

    return {
        "status": "success",
        "message": f"commission taken out from {ajo_savings.name} and distributed successfully",
        "amount": amount,
    }


@django_transaction.atomic
def fund_agent_wallet(
    transaction: Transaction,
    wallet: WalletSystem,
    amount: float,
    unique_reference: str | None = None,
) -> Transaction:
    # fund the agent's wallet
    fund = WalletSystem.fund_balance(
        wallet=wallet,
        amount=amount,
        transaction_instance=transaction,
        unique_reference=unique_reference,
        onboarded_user=transaction.onboarded_user,
    )

    # update the fields
    transaction.status = Status.SUCCESS
    transaction.transaction_date_completed = timezone.localtime()
    transaction.wallet_balance_before = fund.get("balance_before")
    transaction.wallet_balance_after = fund.get("balance_after")
    transaction.save()

    return transaction


@django_transaction.atomic
def debit_agent_commission_wallet_balance(
    transaction: Transaction,
    wallet: WalletSystem,
    amount: float,
) -> Transaction:
    deduct = WalletSystem.deduct_balance(
        wallet=wallet,
        amount=amount,
        transaction_instance=transaction,
    )

    # update the fields
    transaction.status = Status.SUCCESS
    transaction.transaction_date_completed = timezone.localtime()
    transaction.wallet_balance_before = deduct.get("balance_before")
    transaction.wallet_balance_after = deduct.get("balance_after")
    transaction.save()

    return deduct


@django_transaction.atomic
def fund_agent_from_commissions(user: AbstractUser):
    # call the commissions selector class
    commissions_selector = AjoCommissionsSelector(user=user)

    # call the agent selector class
    agent_selector = AjoAgentSelector(user=user)

    # obtain the agent's wallet
    agent_wallet = agent_selector.get_agent_ajo_wallet()

    # call all the amount and queryset to be withdrawn from
    # commissions_amount = commissions_selector.get_total_amount_of_commissions_not_withdrawn()
    # commissions_amount = agent_wallet.commission_balance

    available_balance = commissions_selector.get_commissions_available_balance()

    if user.email == settings.AGENCY_BANKING_USEREMAIL:
        commissions_amount = float(math.floor(available_balance))
    else:
        commissions_amount = round(available_balance, 2)

    not_withdrawn_commissions_qs = (
        commissions_selector.get_list_of_not_withdrawn_commissions()
    )

    # obtain the description
    debit_transaction_func = transaction_description(
        transaction_type=TransactionDescriptionType.DEBIT_COMMISSION_AGENT_BALANCE,
        amount=commissions_amount,
    )

    debit_description = debit_transaction_func()

    # create a transaction instance for the debiting of the commission balance of the wallet
    debit_transaction_instance = TransactionService.create_debit_commission_balance_from_agent_wallet_transaction(
        user=user,
        amount=commissions_amount,
        transaction_description=debit_description,
    )

    # debit the commissions balance of the wallet
    debit_agent_commission_wallet_balance(
        transaction=debit_transaction_instance,
        wallet=commissions_selector.get_commissions_wallet(),
        amount=commissions_amount,
    )

    # obtain the transaction description
    transaction_description_func = transaction_description(
        transaction_type=TransactionDescriptionType.FUND_AGENT_FROM_COMMISSION,
        amount=commissions_amount,
    )
    description = transaction_description_func()

    # create the transaction instance
    transaction_instance = (
        TransactionService.create_commissions_to_agent_wallet_transaction(
            user=user,
            amount=commissions_amount,
            transaction_description=description,
        )
    )

    # call the fund agent wallet function
    transaction_instance = fund_agent_wallet(
        transaction=transaction_instance,
        wallet=agent_wallet,
        amount=commissions_amount,
    )

    # update the not withdrawn commissions queryset to withdrawn True
    not_withdrawn_commissions_qs.update(withdrawn=True)


@django_transaction.atomic
def cashout_from_ajo_plan(ajo_savings_id: int, amount: float, user_id: int):
    # obtain the user
    user = get_user_model().objects.get(id=user_id)

    # call the ajo savings selector
    ajo_savings_selector = AjoSavingsSelector(id=ajo_savings_id, user=user)

    # obtain the ajo savings plan
    ajo_savings = ajo_savings_selector.get_ajo_saving_plan()

    # obtain the ajo user
    ajo_user = ajo_savings.ajo_user

    # call the Ajo User Selector class
    ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)

    # obtain the ajo user's wallet
    ajo_user_wallet = ajo_user_selector.get_ajo_user_wallet()

    # obtain the transaction description
    transaction_description_func = transaction_description(
        TransactionDescriptionType.CASHOUT_BY_USSD,
        amount=amount,
    )
    description = transaction_description_func(ajo_savings_plan_name=ajo_savings.name)

    # create a Transaction instance
    debit_transaction = TransactionService.create_ajo_cashout_by_ussd(
        user=user,
        ajo_user=ajo_user,
        amount=amount,
        transaction_description=description,
        quotation_id=ajo_savings.quotation_id,
    )

    # call the function to debit the Ajo Savings plan
    debit_ajo_plan(
        ajo_savings=ajo_savings,
        amount=amount,
        wallet=ajo_user_wallet,
        transaction_instance=debit_transaction,
    )

    # call function to increment ajo user model fields
    AjoUserService(ajo_user=ajo_user).increment_total_withdrawn_in_ajo_user(
        amount=amount
    )

    ###fund the agent's wallet
    # obtain the agent's wallet
    agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()

    # obtain the transaction description
    cashout_transaction_des_func = transaction_description(
        transaction_type=TransactionDescriptionType.CASHOUT_TO_AGENT_WALLET,
        amount=amount,
    )
    cashout_description = cashout_transaction_des_func(
        ajo_savings_plan_name=ajo_savings.name
    )

    # create the transaction
    cashout_transaction_instance = (
        TransactionService.create_cashout_to_agent_wallet_transaction(
            user=user, amount=amount, transaction_description=cashout_description
        )
    )

    # call fund wallet
    fund_agent_wallet(
        transaction=cashout_transaction_instance,
        wallet=agent_wallet,
        amount=amount,
    )


@django_transaction.atomic
def cashout_from_spending_wallet(
    amount: float,
    phone_number: str,
    user: AbstractUser,
):
    # call the Ajo User Selector class
    ajo_user_selector = AjoUserSelector(user=user, phone_number=phone_number)

    # obtain the ajo user
    ajo_user = ajo_user_selector.get_ajo_user()

    # obtain the ajo user's spending wallet
    ajo_user_spending_wallet = ajo_user_selector.get_spending_wallet()

    # obtain the transaction description
    debit_transaction_description = transaction_description(
        TransactionDescriptionType.CASHOUT_TO_AGENT_FROM_SPEND,
        amount=amount,
    )()

    # create a Transaction instance
    debit_transaction = TransactionService.create_ajo_cashout_by_ussd(
        user=user,
        ajo_user=ajo_user,
        amount=amount,
        transaction_description=debit_transaction_description,
        wallet_type=WalletTypes.AJO_SPENDING,
    )

    # debit the spending wallet
    debit_wallet_and_update_transaction(
        amount=amount,
        wallet=ajo_user_spending_wallet,
        transaction_instance=debit_transaction,
    )

    # fund the agent's wallet

    agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()

    # create the transaction
    cashout_transaction_instance = TransactionService.create_cashout_to_agent_wallet_transaction(
        user=user,
        amount=amount,
        transaction_description=f"{amount} was cashed out from {ajo_user.phone_number} spending wallet",
    )

    # call fund wallet
    fund_agent_wallet(
        transaction=cashout_transaction_instance,
        wallet=agent_wallet,
        amount=amount,
    )


def create_loan_transaction(
    user: AbstractUser,
    ajo_user: AjoUser,
    quotation_id: str,
    amount: float,
) -> Transaction:
    trans_des_func = transaction_description(
        transaction_type=TransactionDescriptionType.CREATE_LOAN, amount=amount
    )
    description = trans_des_func()

    return TransactionService.create_loan_transaction(
        user=user,
        amount=amount,
        ajo_user=ajo_user,
        quotation_id=quotation_id,
        description=description,
    )


def check_if_amount_higher_than_remaining_loan(
    loan: Loan, amount: float
) -> Dict[str, Any]:
    """
    Checks if the amount to be paid is higher than the remaining amount
    to complete the loan.

    It returns a dictionary:
    {
        "status": (bool) if False, you can go ahead to pay, else, raise error
        "remaining_amount": how much remaining to complete the loan plan
    }

    Args:
        loan (Loan): the Loan instance in consideration
        amount (float): how much is to be paid into plan

    Returns:
        Dict[str, Any]: The Dictionary described above
    """
    info = {
        "status": False,
        "remaining_amount": loan.repayment_amount - loan.amount_paid or 0,
    }
    if (amount + loan.amount_paid) <= loan.repayment_amount:
        return info
    else:
        info["status"] = True
        return info


@django_transaction.atomic
def pay_for_loan(
    amount: float,
    ajo_user: AjoUser,
    loan: Loan,
    user: AbstractUser,
    request_data: dict,
) -> None:
    # obtain the agent wallet
    agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()
    # # obtain the loan wallet
    # ajo_user_loan_wallet = LoanSelector(ajo_user=ajo_user).get_ajo_user_loan_wallet()

    # instantiate the loan service class
    loan_service = LoanService(loan=loan)

    ###transaction description for debit agent
    # obtain the transaction description
    transaction_description_func = transaction_description(
        transaction_type=TransactionDescriptionType.TRANSFER_FOR_LOAN,
        amount=amount,
    )
    debit_agent_description = transaction_description_func(
        phone_number=ajo_user.phone_number,
        loan_quotation_id=str(loan.quotation_id),
    )

    # debit the agent's position
    debit_agent(
        amount=amount,
        user=user,
        wallet=agent_wallet,
        payload=request_data,
        transaction_description=debit_agent_description,
    )

    # fund the loan
    loan = loan_service.fund_loan(amount=amount)

    ###create a loan transaction
    # obtain the description first
    dep_description_func = transaction_description(
        transaction_type=TransactionDescriptionType.LOAN_DEPOSIT,
        amount=amount,
    )
    dep_description = dep_description_func(loan_quotation_id=loan.quotation_id)

    deposit_transaction = TransactionService.create_loan_deposit_by_wallet_transaction(
        user=user,
        amount=amount,
        ajo_user=ajo_user,
        quotation_id=loan.quotation_id,
        description=dep_description,
    )

    # update the fields
    deposit_transaction.transaction_date_completed = timezone.localtime()
    deposit_transaction.plan_balance_before = loan.plan_balance_before
    deposit_transaction.plan_balance_after = loan.plan_balance_after
    deposit_transaction.save()

    if not loan.is_activated:
        loan.is_activated = True
        loan.save()


@django_transaction.atomic
def move_loan_to_agent_wallet(
    user: AbstractUser,
    amount: float,
    ajo_user: AjoUser,
    loan_wallet: WalletSystem,
) -> None:
    # description
    debit_transaction_func = transaction_description(
        transaction_type=TransactionDescriptionType.DEBIT_LOAN_WALLET,
        amount=amount,
    )
    debit_description = debit_transaction_func()

    # create debit loan wallet transaction
    debit_transaction = TransactionService.create_loan_wallet_debit_transaction(
        user=user,
        amount=amount,
        ajo_user=ajo_user,
        description=debit_description,
    )

    # debit the loan wallet
    debit_info = WalletSystem.deduct_balance(
        wallet=loan_wallet,
        amount=amount,
        transaction_instance=debit_transaction,
    )

    # update debit transaction info
    debit_transaction.transaction_date_completed = timezone.localtime()
    debit_transaction.wallet_balance_before = debit_info.get("balance_before")
    debit_transaction.wallet_balance_after = debit_info.get("balance_after")
    debit_transaction.save()

    # obtain the agent's wallet
    agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()

    # obtain the transaction description
    credit_trans_desc_func = transaction_description(
        transaction_type=TransactionDescriptionType.CREDIT_AGENT_WALLET_FROM_LOAN,
        amount=amount,
    )
    credit_description = credit_trans_desc_func(
        ajo_user_phone_number=ajo_user.phone_number
    )

    # create credit transaction
    credit_transaction = TransactionService.create_loan_to_agent_wallet_transaction(
        user=user,
        amount=amount,
        description=credit_description,
    )
    print(3)
    # fund agent's wallet
    fund_agent_wallet(
        transaction=credit_transaction,
        wallet=agent_wallet,
        amount=amount,
    )

    # exit out of function


@django_transaction.atomic
def withdraw_from_ajo_agent_wallet_to_agency_wallet(
    amount: float, transaction_instance: Transaction, agent_wallet: WalletSystem
):
    # transaction description
    debit_trans_des_func = transaction_description(
        transaction_type=TransactionDescriptionType.WITHDRAW_TO_AGENCY_WALLET,
        amount=amount,
    )
    description = debit_trans_des_func()

    # debit the user's position
    debit = WalletSystem.deduct_balance(
        wallet=agent_wallet,
        amount=amount,
        transaction_instance=transaction_instance,
    )

    # place description in the transaction instance and update other fields
    transaction_instance.status = Status.SUCCESS
    transaction_instance.description = description
    transaction_instance.wallet_balance_before = debit.get("balance_before")
    transaction_instance.wallet_balance_after = debit.get("balance_after")
    transaction_instance.save()


@django_transaction.atomic
def dynamic_withdraw_from_ajo_agent_wallet(
    amount: float,
    transaction_instance: Transaction,
    agent_wallet: WalletSystem,
    transaction_type=TransactionDescriptionType.PAYMENT_FOR_CARD_REQUEST,
):
    # transaction description
    debit_trans_des_func = transaction_description(
        transaction_type=transaction_type,
        amount=amount,
    )
    description = debit_trans_des_func

    # debit the user's position
    debit = WalletSystem.deduct_balance(
        wallet=agent_wallet,
        amount=amount,
        transaction_instance=transaction_instance,
    )

    # place description in the transaction instance and update other fields
    transaction_instance.status = Status.SUCCESS
    transaction_instance.description = description
    transaction_instance.wallet_balance_before = debit.get("balance_before")
    transaction_instance.wallet_balance_after = debit.get("balance_after")
    transaction_instance.save()


@shared_task
@django_transaction.atomic
def reverse_withdrawal(
    amount: float,
    user_id: int,
    failed_transaction_id: str,
    wallet_type: WalletTypes,
    request_data: dict,
) -> Dict[str, str]:
    # obtain the user
    user = get_user_model().objects.get(id=user_id)

    plan_types = {
        WalletTypes.AJO_AGENT: PlanType.AJO,
        WalletTypes.ROSCA_PERSONAL: PlanType.ROTATIONGROUP,
    }

    if wallet_type in [
        WalletTypes.AJO_AGENT,
        WalletTypes.AJO_COMMISSION,
    ]:
        if wallet_type == WalletTypes.AJO_AGENT:
            # obtain the agent's wallet
            wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()
        else:
            # obtain the commission's wallet
            wallet = AjoCommissionsSelector(user=user).get_commissions_wallet()

        # transaction description
        credit_trans_des_func = transaction_description(
            transaction_type=TransactionDescriptionType.REVERSE_WITHDRAWAL,
            amount=amount,
        )
        description = credit_trans_des_func()

    elif wallet_type == WalletTypes.ROSCA_PERSONAL:
        # obtain the rotation wallet
        wallet = RotationGroupMemberSelector.get_rotation_group_wallet(user=user)

        # transaction description
        description = (
            f"{amount} was reversed to your rotation wallet due to failed transaction"
        )

    try:
        failed_transaction = Transaction.objects.get(
            transaction_id=failed_transaction_id
        )
    except Transaction.DoesNotExist as err:
        return {
            "status": "failed",
            "message": "failed transaction not obtained succesfully",
        }

    # create reversal transaction
    transaction = TransactionService.create_reversal_of_funds_transaction(
        user=user,
        amount=amount,
        wallet_type=wallet_type,
        request_data=request_data,
        quotation_id=None,
        plan_type=plan_types.get(wallet_type),
        description=description,
        unique_reference=f"{failed_transaction_id}_reversal",
        transfer_provider=failed_transaction.transfer_provider,
    )

    # fund the wallet
    fund_agent_wallet(
        transaction=transaction,
        wallet=wallet,
        amount=amount,
    )

    return {
        "status": "success",
        "message": f"{user.email} had {amount} reversed to {wallet_type} wallet due to failed transaction",
    }


@shared_task
def send_sms_alert_for_ajo_payment(ajo_savings_id: int, amount: float):
    """
    This task handles the SMS alert for ajo payment

    Args:
        ajo_savings_id (int): _description_
        user_id (int): _description_

    Raises:
        Exception: _description_
    """
    try:
        ajo_savings = AjoSaving.objects.get(id=ajo_savings_id)
        ajo_user = ajo_savings.ajo_user
        phone_number = ajo_user.phone_number
        first_name = ajo_user.first_name
        balance = ajo_savings.amount_saved

        send_out_sms_for_ajo_payment(phone_number, first_name, amount, balance)
    except:
        pass

    return


def fund_spending_wallet():
    """
    funds
    """
    pass


@django_transaction.atomic
def move_mature_funds_to_spending_for_ajo_user(ajo_plan: AjoSaving) -> Dict[str, Any]:
    amount = ajo_plan.amount_saved

    # create a transaction to debit the savings(AJO_USER) wallet
    debit_transaction_description_func = transaction_description(
        transaction_type=TransactionDescriptionType.DEBIT_FOR_PLAN_MATURITY,
        amount=amount,
    )
    debit_description = debit_transaction_description_func(
        ajo_savings_plan_name=ajo_plan.name
    )

    # create a Transaction instance
    debit_transaction = TransactionService.create_ajo_commissions_transaction(
        user=ajo_plan.user,
        amount=amount,
        quotation_id=ajo_plan.quotation_id,
        ajo_user=ajo_plan.ajo_user,
        transaction_description=debit_description,
        transaction_form_type=TransactionFormType.TRANSFER,
        wallet_type=WalletTypes.AJO_USER,
    )

    # call the ajo user selector class
    ajo_user_selector = AjoUserSelector(ajo_user=ajo_plan.ajo_user)

    # obtain the ajo user's wallet
    ajo_user_savings_wallet = ajo_user_selector.get_ajo_user_wallet()

    # debit the ajo user's wallet
    debit_ajo_plan(
        ajo_savings=ajo_plan,
        amount=amount,
        wallet=ajo_user_savings_wallet,
        transaction_instance=debit_transaction,
    )

    # create fund transaction
    credit_transaction = TransactionService.create_transfer_to_from_wallet_transaction(
        user=ajo_plan.user,
        amount=amount,
        transaction_description=f"{amount} was deposited to your spending wallet from {ajo_plan.name}",
        transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
        ajo_user=ajo_plan.ajo_user,
        wallet_type=WalletTypes.AJO_SPENDING,
        transaction_form_type=TransactionFormType.WALLET_DEPOSIT,
    )

    # obtain the ajo user's spending wallet
    ajo_user_spending_wallet = ajo_user_selector.get_spending_wallet()

    # increment the spending wallet's balance
    fund_wallet_and_update_transaction(
        wallet=ajo_user_spending_wallet,
        amount=amount,
        transaction_instance=credit_transaction,
        ajo_user=ajo_plan.ajo_user,
    )

    # update the ajo plan's fields
    ajo_plan.close_plan()

    return {
        "debit_transaction": debit_transaction,
        "credit_transaction": credit_transaction,
    }


@django_transaction.atomic
def debit_for_loan_disbursement(
    savings: AjoSaving, ajo_user_selector, actual_escrow_value, boosta_debit=False
):
    """
    Handles the transaction to debit the appropriate wallet (savings or spending)
    for the final loan disbursement. The function first attempts to debit the savings
    wallet of the Ajo user. If the savings wallet does not have sufficient funds, it
    then attempts to debit the spending wallet. If neither wallet has enough balance,
    the transaction is aborted.

    Parameters:
    -----------
    savings : AjoSaving
        The savings instance associated with the Ajo user.

    ajo_user_selector : AjoUserSelector
        An instance of AjoUserSelector to interact with the user's wallets.

    actual_escrow_value : float
        The amount that needs to be debited for the escrow, which is part of
        the loan disbursement process.

    Returns:
    --------
    bool
        Returns True if the transaction was successful, otherwise False.
    """

    ajo_user = savings.ajo_user
    agent_user = ajo_user.user

    # Obtain the Ajo user's wallet and balance
    ajo_user_wallet = ajo_user_selector.get_ajo_user_wallet()
    available_balance = ajo_user_wallet.available_balance

    # If savings wallet balance is insufficient, attempt to debit the spending wallet
    ajo_user_spend_wallet = ajo_user_selector.get_spending_wallet()

    transaction_form_type = TransactionFormType.AJO_LOAN_ESCROW_HOLDING
    if not boosta_debit:
        debit_description = f"Transferred {actual_escrow_value} to Ajo user's Escrow wallet for loan savings plan."
    else:
        debit_description = f"Transferred {actual_escrow_value} to BOOSTA commisions for loan savings plan."

    # Attempt to debit the Ajo user's savings wallet
    if available_balance >= actual_escrow_value:

        debit_transaction = TransactionService.create_ajo_commissions_transaction(
            user=savings.user,
            amount=actual_escrow_value,
            quotation_id=savings.quotation_id,
            ajo_user=savings.ajo_user,
            transaction_description=debit_description,
            transaction_form_type=transaction_form_type,
            wallet_type=ajo_user_wallet.wallet_type,
        )

        # Debit the savings wallet
        debit_ajo_plan(
            ajo_savings=savings,
            amount=actual_escrow_value,
            wallet=ajo_user_wallet,
            transaction_instance=debit_transaction,
        )
        return True

    elif ajo_user_spend_wallet.available_balance >= actual_escrow_value:
        debit_transaction = (
            TransactionService.dynamic_deduction_from_wallet_transaction(
                user=agent_user,
                amount=actual_escrow_value,
                wallet_type=ajo_user_spend_wallet.wallet_type,
                request_data=None,
                quotation_id=savings.quotation_id,
                description=debit_description,
                ajo_user=ajo_user,
                transaction_form_type=transaction_form_type,
            )
        )

        # Deduct from the spending wallet
        deduct_agent_wallet = WalletSystem.deduct_balance(
            wallet=ajo_user_spend_wallet,
            amount=actual_escrow_value,
            transaction_instance=debit_transaction,
            onboarded_user=ajo_user,
        )

        # Update the transaction status
        debit_transaction.status = Status.SUCCESS
        debit_transaction.transaction_date_completed = timezone.now()
        debit_transaction.wallet_balance_before = deduct_agent_wallet["balance_before"]
        debit_transaction.wallet_balance_after = deduct_agent_wallet["balance_after"]
        debit_transaction.save()
        return True

    else:

        # If neither wallet has sufficient balance, abort the transaction
        return False


@django_transaction.atomic
def transfer_savings_to_boosta_commission(loan, loan_type):
    # from loans.models import AjoLoan

    # loan_qs = AjoLoan.objects.filter(id=loan.id)
    eligibility_instance = loan.eligibility
    savings = eligibility_instance.saving
    amount_applied = loan.amount
    eligibility_multiplier = eligibility_instance.multiplier

    savings_loan_types = ["BOOSTA", "BOOSTA_2X", "BOOSTA_2X_MINI", "CREDIT_HEALTH"]
    if savings.savings_type in savings_loan_types:
        # actual_escrow_value = eligibility_instance.amount_saved
        actual_escrow_value = savings.amount_saved
    else:
        actual_escrow_value = amount_applied / eligibility_multiplier

    ajo_user = loan.borrower
    credit_description = (
        f"{actual_escrow_value} commission, from BOOSTA loans {ajo_user.phone_number}"
    )
    transaction_form_type = TransactionFormType.COMMISSION
    quotation_id = str(loan.unique_loan_ref)
    xtype_boosta = ["BOOSTA_2X", "BOOSTA_2X_MINI", "CREDIT_HEALTH"]
    wallet_type = (
        WalletTypes.BOOSTA_2x_COMMISSION
        if loan_type in xtype_boosta
        else WalletTypes.BOOSTA_COMMISSION
    )
    commission_transaction_exist = Transaction.objects.filter(
        wallet_type=wallet_type,
        transaction_form_type=transaction_form_type,
        quotation_id=quotation_id,
    ).exists()

    ajo_user_selector = AjoUserSelector(ajo_user=savings.ajo_user)

    wallet_debited = debit_for_loan_disbursement(
        ajo_user_selector=ajo_user_selector,
        savings=savings,
        actual_escrow_value=actual_escrow_value,
        boosta_debit=True,
    )

    if wallet_debited is True and not commission_transaction_exist:

        boosta_commission_wallet = AjoAgentSelector(
            user=loan.agent
        ).get_agent_ajo_wallet(wallet_type=wallet_type)

        credit_transaction = TransactionService.create_deposit_by_wallet_transaction(
            user=loan.agent,
            amount=actual_escrow_value,
            wallet_type=wallet_type,
            transaction_form_type=transaction_form_type,
            unique_reference=quotation_id,
            description=credit_description,
            status=Status.PENDING,
            quotation_id=quotation_id,
            ajo_user=ajo_user,
            plan_type=PlanType.AJO,
        )

        fund_agent_wallet(
            transaction=credit_transaction,
            wallet=boosta_commission_wallet,
            amount=actual_escrow_value,
            unique_reference=quotation_id,
        )

    if loan_type in xtype_boosta:
        transfer_insurance_to_escrow(loan=loan, savings=savings)
    savings.close_plan()
    return wallet_debited


@django_transaction.atomic
def transfer_insurance_to_escrow(loan, savings):

    from loans.models import AjoLoan

    loan_instance = AjoLoan.objects.get(id=loan.id)

    ajo_user_selector = AjoUserSelector(ajo_user=loan_instance.borrower)

    ajo_user_escrow_wallet = ajo_user_selector.get_any_ajo_user_wallet(
        wallet_type=WalletTypes.AJO_LOAN_ESCROW
    )
    loan_instance.update_loan_summary()

    loan_calc = AjoLoan.compute_interest_with_duration(
        principal=loan_instance.amount,
        duration=loan_instance.actual_duaration,
        loan_type=loan_instance.loan_type,
        ajouser=loan_instance.borrower,
        start_date=loan_instance.start_date,
    )

    boosta_insurance = loan_calc.get("boosta_insurance", 0)
    loan_status = ["COMPLETED", "DEFAULTED", "OPEN", "APPROVED"]
    to_exclude_types = ["BNPL", "BOOSTA"]
    all_time_loan = AjoLoan.objects.filter(
        Q(status__in=loan_status) & ~Q(loan_type__in=to_exclude_types),
        borrower=loan_instance.borrower,
    )
    escrow_transfers = Transaction.objects.filter(
        onboarded_user=loan.borrower,
        transaction_form_type="AJO_LOAN_ESCROW_HOLDING",
        transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
    )

    existing_escrow_transfer = escrow_transfers.filter(
        quotation_id=str(loan_instance.unique_loan_ref)
    ).exists()

    savings.close_plan()

    if (
        all_time_loan.count() > escrow_transfers.count()
        and boosta_insurance > 0
        and not existing_escrow_transfer
    ):

        return loan_instance.fund_escrow_wallet(
            borrower=loan_instance.borrower,
            wallet=ajo_user_escrow_wallet,
            amount=boosta_insurance,
            transaction_description=f"{boosta_insurance} was deposited to escrow wallet from Boosta(10%) insurance fee off loan amount {loan_instance.amount}.",
            transaction_form_type=TransactionFormType.AJO_LOAN_ESCROW_HOLDING,
            quotation_id=str(loan_instance.unique_loan_ref),
        )

    else:
        return {
            "message": f"boosta_insurance: {boosta_insurance}",
            "others": f"existing escrow status: {existing_escrow_transfer}",
        }


@django_transaction.atomic
def move_savings_funds_to_escrow_for_ajo_user(loan) -> bool:
    from loans.models import AjoLoan

    loan_qs = AjoLoan.objects.filter(id=loan.id)
    eligibility_instance = loan.eligibility
    savings = eligibility_instance.saving
    amount_applied = loan.amount
    eligibility_multiplier = eligibility_instance.multiplier
    actual_escrow_value = amount_applied / eligibility_multiplier

    ajo_user_selector = AjoUserSelector(ajo_user=savings.ajo_user)

    wallet_debited = debit_for_loan_disbursement(
        ajo_user_selector=ajo_user_selector,
        savings=savings,
        actual_escrow_value=actual_escrow_value,
    )

    if wallet_debited:
        # create fund transaction
        credit_transaction = TransactionService.create_transfer_to_from_wallet_transaction(
            user=savings.user,
            amount=actual_escrow_value,
            wallet_type=WalletTypes.AJO_LOAN_ESCROW,
            transaction_description=f"{actual_escrow_value} was deposited to escrow wallet from your {savings.name}",
            transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
            transaction_form_type=TransactionFormType.AJO_LOAN_ESCROW_HOLDING,
            ajo_user=savings.ajo_user,
        )

        ajo_user_escrow_wallet = ajo_user_selector.get_any_ajo_user_wallet(
            wallet_type=WalletTypes.AJO_LOAN_ESCROW
        )
        # increment the escrow wallet's balance
        fund_wallet_and_update_transaction(
            wallet=ajo_user_escrow_wallet,
            amount=actual_escrow_value,
            transaction_instance=credit_transaction,
            ajo_user=savings.ajo_user,
        )
        # update the ajo plan's fields
        savings.close_plan()
        try:
            savings_user_access_token = agent_login().get("access")
            savings_user = User.objects.get(email=settings.AGENCY_BANKING_USEREMAIL)
            escrow_account_user_email = settings.ESCROW_USER_EMAIL
            escrow_acct_user = User.objects.get(email=escrow_account_user_email)
            escrow_user_phone_no = escrow_acct_user.user_phone

            send_to_escrow_acct = (
                AgencyBankingClass.send_money_from_liberty_to_user_through_pay_buddy(
                    phone_number=escrow_user_phone_no,
                    amount=actual_escrow_value,
                    transaction_reference=str(savings.quotation_id),
                    access_token=savings_user_access_token,
                )
            )
            AjoSaving.objects.filter(id=savings.id).update(
                escrow_meta_response=send_to_escrow_acct
            )

            if send_to_escrow_acct.get("status") == True:
                send_to_escrow_data = send_to_escrow_acct.get("data")

                if send_to_escrow_data is not None:
                    message = send_to_escrow_data.get("message")
                    if message is not None and message == "success":

                        AjoSaving.objects.filter(id=savings.id).update(
                            escrow_settlement_status=True
                        )
                        savings_obj = AjoSaving.objects.filter(id=savings.id).last()
                        savings_obj.refresh_from_db()
                        savings_obj.escrow_settlement_status = True
                        savings.escrow_meta_response = send_to_escrow_acct
                        savings_obj.save()

                        transaction_record: Transaction = (
                            TransactionService.create_transfer_to_from_wallet_transaction(
                                user=savings_user,
                                amount=actual_escrow_value,
                                wallet_type=WalletTypes.AJO_AGENT,
                                transaction_description="escrow settlement",
                                transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
                                transaction_form_type=TransactionFormType.ESCROW_TO_ESCROW_ACCOUNT,
                                transaction_destination=TransactionDestination.BANK_ACCOUNT,
                            )
                        )
                        transaction_record.payload = json.dumps(send_to_escrow_acct)
                        transaction_record.save()
        except Exception as err:
            return wallet_debited

    return wallet_debited


@django_transaction.atomic
def bnpl_disbursement_settlement(loan, ajo_user):
    from loans.models import AjoLoan

    dibursement_wallet = AjoUserSelector(
        ajo_user=ajo_user
    ).get_loan_disbursement_wallet()
    available_balance = dibursement_wallet.available_balance
    unique_reference = str(uuid.uuid4())
    transaction_form_type = TransactionFormType.BNPL_SETTLEMENT_DEBIT
    debit_transaction = TransactionService.dynamic_deduction_from_wallet_transaction(
        user=ajo_user.user,
        amount=available_balance,
        wallet_type=dibursement_wallet.wallet_type,
        ajo_user=ajo_user,
        description="charged to fund spend due to system error on loan disbursement",
        unique_reference=unique_reference,
        transaction_form_type=transaction_form_type,
    )
    deduct_agent_wallet = WalletSystem().deduct_balance(
        wallet=dibursement_wallet,
        amount=available_balance,
        transaction_instance=debit_transaction,
        unique_reference=unique_reference,
        onboarded_user=ajo_user,
    )
    debit_transaction.status = Status.SUCCESS
    debit_transaction.transaction_date_completed = timezone.now()
    debit_transaction.wallet_balance_before = deduct_agent_wallet["balance_before"]
    debit_transaction.wallet_balance_after = deduct_agent_wallet["balance_after"]
    debit_transaction.save()

    savings_access_token = agent_login().get("access")
    savings_user = User.objects.get(email=settings.AGENCY_BANKING_USEREMAIL)
    bnpl_account_user_email = settings.BNPL_USER_EMAIL
    bnpl_user = CustomUser.objects.filter(email=bnpl_account_user_email).last()

    bnpl_phone_no = AgencyBankingClass().get_user_info(
        access_token=savings_access_token,
        user_id=bnpl_user.customer_user_id,
    )
    bnpl_phone_number = bnpl_phone_no.get("data").get("phone_number")
    send_to_bnpl_acct = (
        AgencyBankingClass.send_money_from_liberty_to_user_through_pay_buddy(
            phone_number=bnpl_phone_number,
            amount=available_balance,
            transaction_reference=str(loan.unique_loan_ref),
            access_token=savings_access_token,
        )
    )
    if send_to_bnpl_acct.get("data").get("message") == "success":
        transaction_record: Transaction = (
            TransactionService.create_transfer_to_from_wallet_transaction(
                user=savings_user,
                amount=available_balance,
                wallet_type=WalletTypes.LOAN_DISBURSEMENT,
                transaction_description="bnpl settlement",
                transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
                transaction_form_type=TransactionFormType.BNPL_TO_BNPL_ACCOUNT,
                transaction_destination=TransactionDestination.BANK_ACCOUNT,
            )
        )
        transaction_record.payload = json.dumps(send_to_bnpl_acct)
        transaction_record.save()

        loan_summary = AjoLoan.calculate_interest(
            principal_amount=loan.amount,
            loan_tenure=loan.tenor,
            savings_id=loan.eligibility.saving.id,
        )
        try:
            loan_instance = AjoLoan.objects.get(id=loan.id)

            # status="PENDING_PRODUCT_ASSIGNMENT"
            loan_instance.status = "OPEN"
            loan_instance.verification_stage = "PRODUCT_ASSIGNMENT"
            loan_instance.date_disbursed = timezone.now()
            loan_instance.is_disbursed = True
            loan_instance.start_date = loan_summary.get("start_date")
            loan_instance.end_date = loan_summary.get("end_date")
            loan_instance.tenor_in_days = loan_summary.get("tenor_in_days")
            loan_instance.daily_repayment_amount = loan_summary.get(
                "daily_repayment_amount"
            )
            loan_instance.save(
                update_fields=[
                    "status",
                    "verification_stage",
                    "date_disbursed",
                    "is_disbursed",
                    "start_date",
                    "end_date",
                    "tenor_in_days",
                    "daily_repayment_amount",
                ]
            )

            AjoLoan.split_bnpl_commission(loan=loan_instance)
        except AjoLoan.DoesNotExist:
            # Handle the case where the loan instance does not exist
            response = {"status": False, "error": "Loan instance not found."}
            return response

        response = {
            "status": True,
        }
        return response

    else:
        response = {
            "status": False,
        }
        return response


@django_transaction.atomic
def move_savings_funds_to_disbursement(savings: AjoSaving, loan) -> bool:
    from loans.models import AjoLoan, ProductAssignment

    # call the ajo user selector class
    ajo_user_selector = AjoUserSelector(ajo_user=savings.ajo_user)
    wallet = ajo_user_selector.get_ajo_user_wallet()
    transaction_form_type = TransactionFormType.BNPL_LOAN_DISBURSEMENT
    loan_summary = AjoLoan.calculate_interest(
        principal_amount=loan.amount,
        loan_tenure=loan.tenor,
        savings_id=loan.eligibility.saving.id,
    )
    AjoLoan.objects.filter(id=loan.id).update(
        status="OPEN_TO_SUPERVISOR",
        verification_stage="SUPERVISOR_DISBURSEMENT",
        date_disbursed=timezone.now(),
        is_disbursed=True,
        start_date=loan_summary.get("start_date"),
        end_date=loan_summary.get("end_date"),
        tenor_in_days=loan_summary.get("tenor_in_days"),
        daily_repayment_amount=loan_summary.get("daily_repayment_amount"),
    )

    try:
        plan_balance_after = savings.plan_balance_after
        exposure_amount = loan.amount
        loan_access_token = loan_agent_login().get("access")
        savings_access_token = agent_login().get("access")

        savings_acct_user = (
            get_user_model()
            .objects.filter(email=f"{settings.AGENCY_BANKING_USEREMAIL}")
            .last()
        )
        # saving_phone_number = savings_acct_user.user_phone
        print(savings_acct_user, "\n\n")

        savings_phone_no = AgencyBankingClass().get_user_info(
            access_token=savings_access_token,
            user_id=savings_acct_user.customer_user_id,
        )
        print(savings_phone_no, "\n\n")

        phone_number = savings_phone_no.get("data").get("phone_number")

        send_loan_buddy_to_savings = (
            AgencyBankingClass.send_money_from_loans_to_savings_through_pay_buddy(
                phone_number=phone_number,
                amount=exposure_amount,
                transaction_reference=loan.loan_ref,
                access_token=loan_access_token,
            )
        )
        # print(send_loan_buddy_to_savings, "<---------------")

        ajo_user_disbursement_wallet = ajo_user_selector.get_loan_disbursement_wallet()
        # send_loan_buddy_to_savings = {
        #     "data":{
        #         "message":"success"
        #     }
        # }
        # print("Fake Response",send_loan_buddy_to_savings)
        # print(send_loan_buddy_to_savings)
        if send_loan_buddy_to_savings.get("data").get("message") == "success":

            credit_transaction = TransactionService.create_transfer_to_from_wallet_transaction(
                user=savings.user,
                amount=exposure_amount,
                wallet_type=ajo_user_disbursement_wallet.wallet_type,
                transaction_description=f"{plan_balance_after} was deposited to disbursement wallet from your {savings.name}",
                transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
                transaction_form_type=transaction_form_type,
                ajo_user=savings.ajo_user,
            )
            fund_wallet_and_update_transaction(
                wallet=ajo_user_disbursement_wallet,
                amount=exposure_amount,
                transaction_instance=credit_transaction,
                ajo_user=savings.ajo_user,
            )

            const = ConstantTable.get_constant_table_instance()

            bnpl_interest = const.staff_loans_config.get("bnpl_interest", 20) / 100

            product_info = savings.product_info

            ajo_user = savings.ajo_user
            product_info = savings.product_info
            # get stock price

            selling_price = product_info.selling_price

            # print(bnpl_interest, "\n\n")
            bnpl_deposit_percentage = (
                const.staff_loans_config.get("bnpl_deposit_percentage", 40) / 100
            )
            # expected savings
            bnpl_price = (selling_price * bnpl_interest) + selling_price

            forty_percent_bnpl_amount = bnpl_price * bnpl_deposit_percentage

            debit_transaction = TransactionService.create_ajo_commissions_transaction(
                user=savings.user,
                amount=forty_percent_bnpl_amount,
                quotation_id=savings.quotation_id,
                ajo_user=savings.ajo_user,
                transaction_description=f"transferred {savings.amount_saved} to ajo user, {savings.ajo_user.phone_number}, for {savings.name} ajo savings plan.",
                transaction_form_type=transaction_form_type,
                wallet_type=wallet.wallet_type,
            )
            # debit the ajo user's wallet
            debit_ajo_plan(
                ajo_savings=savings,
                amount=forty_percent_bnpl_amount,
                wallet=wallet,
                transaction_instance=debit_transaction,
            )

            credit_transaction = TransactionService.create_transfer_to_from_wallet_transaction(
                user=savings.user,
                amount=forty_percent_bnpl_amount,
                wallet_type=ajo_user_disbursement_wallet.wallet_type,
                transaction_description=f"{forty_percent_bnpl_amount} was deposited to disbursement wallet from your {savings.name}",
                transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
                transaction_form_type=transaction_form_type,
                ajo_user=savings.ajo_user,
            )
            fund_wallet_and_update_transaction(
                wallet=ajo_user_disbursement_wallet,
                amount=forty_percent_bnpl_amount,
                transaction_instance=credit_transaction,
                ajo_user=savings.ajo_user,
            )

            savings.close_plan()
            # create product assignment model
            product_info = savings.product_info
            if product_info:
                prdasgn_instance, created = ProductAssignment.objects.get_or_create(
                    borrower_loan=loan, defaults={"item_name": product_info.item}
                )
            # split commission

            response = {
                "status": True,
                "message": "Success",
            }
            return response
        else:
            response = {
                "status": False,
                "message": "Failed",
            }
            return response
    except Exception as err:
        # print(err)
        response = {
            "status": False,
            "message": "Failed",
        }
        return response


@django_transaction.atomic
def dynamic_funding_of_ajo_plans_from_wallet(
    ajo_plans: QuerySet[AjoSaving],
    ajo_user: AjoUser,
    wallet: WalletSystem,
) -> Dict[str, Any]:
    # check if the ajo_plans query is empty
    if not ajo_plans:
        return {
            "status": False,
            "message": "no ajo plans to fund",
        }

    wallet.refresh_from_db()
    if wallet.available_balance <= 0:
        return {
            "status": False,
            "message": "insufficient funds to initiate funding of plans",
        }

    ajo_plans_funded: List[str] = []

    for ajo_plan in ajo_plans:
        ajo_user = ajo_plan.ajo_user
        periodic_amount = ajo_plan.periodic_amount

        if periodic_amount <= 0:
            continue

        if not wallet.available_balance >= periodic_amount:
            continue

        if ajo_plan.amount_saved >= ajo_plan.expected_amount:
            continue

        # generate a description
        debit_description_func = transaction_description(
            transaction_type=TransactionDescriptionType.TRANSFER_FOR_AJO,
            amount=periodic_amount,
        )
        description = debit_description_func(
            phone_number=ajo_user.phone_number,
            ajo_saving_plan_name=ajo_plan.name,
        )

        # create a transaction instance
        debit_transaction = (
            TransactionService.create_transfer_to_ajo_plan_deposit_transaction(
                user=ajo_plan.user,
                ajo_user=ajo_plan.ajo_user,
                amount=periodic_amount,
                transaction_description=description,
                wallet_type=wallet.wallet_type,
                plan_type=PlanType.AJO,
            )
        )

        # debit the wallet
        debit_wallet_and_update_transaction(
            amount=periodic_amount,
            wallet=wallet,
            transaction_instance=debit_transaction,
        )

        # fund the savings position
        deposit_transaction = fund_ajo_savings_position(
            user=ajo_user.user,
            ajo_user=ajo_user,
            ajo_savings=ajo_plan,
            amount=periodic_amount,
        )

        if not ajo_plan.commission_paid:
            try:
                pay_commissions(ajo_savings_id=ajo_plan.id, user_id=ajo_plan.user.id)
            except Exception as err:
                pass

        # append the plan that got funded
        ajo_plans_funded.append(ajo_plan.name)

        # refresh the wallet's balance
        wallet.refresh_from_db()

    return {
        "status": True,
        "message": f"funded ajo plans: {', '.join(ajo_plans_funded)}",
        "data": ajo_plans_funded,
    }


def fund_personal_ajo_savings_position(
    ajo_savings: AjoSaving,
    amount: float,
    transaction: Transaction,
    debit_credit_info: Dict[str, Any],
) -> Transaction:
    ajo_savings = AjoSavingsService(ajo_savings=ajo_savings).fund_plan(amount=amount)

    transaction.wallet_balance_before = debit_credit_info.get("balance_before")
    transaction.wallet_balance_after = debit_credit_info.get("balance_after")
    transaction.plan_balance_before = ajo_savings.plan_balance_before
    transaction.plan_balance_after = ajo_savings.plan_balance_after

    transaction.save()

    if not ajo_savings.is_activated:
        ajo_savings.is_activated = True
        ajo_savings.save()

    ajo_savings.calculate_frequency_paid(save=True)

    ajo_savings.refresh_from_db()
    if ajo_savings.amount_saved >= ajo_savings.expected_amount and (
        not ajo_savings.lock
    ):
        if ajo_savings.commission_paid:
            ajo_savings.is_active = False
            ajo_savings.save()

    return transaction


def pay_company_commission(
    amount: float,
    full_amount: float,
    savings_name: str,
    savings_frequency: str,
    quotation_id: str,
    plan_type: PlanType,
    phone_number: str | None = None,
) -> None:
    # company description text
    com_description_text = f"{amount} commission earned, from {savings_name}"
    com_description_text += f" {savings_frequency} savings"

    if phone_number:
        com_description_text += f", {phone_number}"

    # try to obtain the company's user account
    company_user = get_user_model().objects.filter(email="<EMAIL>").last()

    # call functions to create the Commission instances for both company and agent
    company_commission = CommissionService.create_commission_instance(
        user=company_user,
        commission_type=CommissionType.COMPANY,
        amount=amount,
        description=com_description_text,
        quotation_id=quotation_id,
        total_amount_taken_as_commission=full_amount,
        plan_type=plan_type,
    )

    # call the function for fund the company commission wallet
    if company_user:
        fund_company_commissions_wallet(
            amount=amount,
            company_user=company_user,
            quotation_id=quotation_id,
            transaction_description=com_description_text,
        )


class RotationGroupPay:
    @staticmethod
    def is_today_a_payment_day(rotation_group: RotationGroup) -> bool:
        """
        Checks if today is a payment day for the group

        Args:
            rotation_group (RotationGroup): the rotation group

        Returns:
            bool: True/False
        """
        group_selector = RotationGroupSelector(group_id=rotation_group.group_id)

        next_payment_day = group_selector.calculate_next_payment_day()

        if not next_payment_day:
            return False

        return timezone.localtime().date() == next_payment_day

    @staticmethod
    def rotation_group_checks_before_payment(rotation_group: RotationGroup) -> float:
        """
        Performs various checks on the rotation group before a payment is made

        Args:
            rotation_group (RotationGroup): the rotation group

        Raises:
            ValueError: this rotation group is inactive
            ValueError: today is not a payment day for this group

        Returns:
            float: the amount to be paid
        """
        if not rotation_group.is_active:
            raise ValueError("this rotation group is inactive")

        rotation_group_selector = RotationGroupSelector(
            group_id=rotation_group.group_id
        )

        return rotation_group.contribution_amount

    @staticmethod
    def collecting_rotation_group_member_checks_before_payment(
        rotation_group: RotationGroup,
        user: AbstractUser,
    ) -> Dict[str, Any]:
        # check if user is a member of RotationGroup
        paying_member_qs: QuerySet[RotationGroupMember] = rotation_group.members.filter(
            user=user
        )
        if not paying_member_qs.exists():
            raise ValueError("this user is not a member of this group")

        paying_member = paying_member_qs.first()

        rotation_group_selector = RotationGroupSelector(
            group_id=rotation_group.group_id
        )

        # check if the paying member has unpaid positions
        unpaid_positions = (
            RotationGroupMemberSelector.get_unpaid_positions_below_next_payment(
                group=rotation_group,
                member=paying_member,
            )
        )
        paid_positions = RotationGroupMemberSelector.paid_positions(
            member=paying_member
        )

        # check if today is not a payment day
        if not RotationGroupPay.is_today_a_payment_day(rotation_group=rotation_group):
            if not unpaid_positions:
                raise ValueError("today is not a payment day for this group")

            collecting_member_position = unpaid_positions[0]

        # if today is a payment day
        else:
            # get the frequency number being paid for
            frequency_number = RotationGroupSelector.find_key_by_date(
                date_to_find=rotation_group_selector.calculate_next_payment_day(),
                date_dict=rotation_group_selector.generate_day_intervals(),
            )

            # if no frequency number (no next payment day)
            if frequency_number is None:
                frequency_number = rotation_group.number_of_participants

            # if the user has paid for that day already
            if (frequency_number in paid_positions) or (
                paying_member.frequency_paid >= frequency_number
            ):
                # check if there is any unpaid position
                if len(unpaid_positions) > 0:
                    collecting_member_position = unpaid_positions[0]

                # if not, user has paid for that day already
                else:
                    raise ValueError(
                        "this user has already paid for this collection date"
                    )

            # if not, obtain the collecting member's position
            else:
                collecting_member_position = (
                    rotation_group_selector.get_collecting_member_position()
                )

        if not collecting_member_position:
            raise ValueError("could not obtain the collecting member's position")

        # obtain the member whose turn it is
        member: RotationGroupMember = rotation_group.members.filter(
            position=collecting_member_position
        ).first()
        if not member:
            raise ValueError("there is no user occupying this position")

        # if paying_member.position == member.position:
        #     raise ValueError("Don't worry, there's no need to pay yourself, the system will handle it.")

        # obtain the wallet of whose turn it is
        collection_wallet = RotationGroupMemberSelector.get_rotation_group_wallet(
            user=member.user
        )

        return {
            "wallet": collection_wallet,
            "member": member,
            "position": collecting_member_position,
        }

    @staticmethod
    def share_rotation_group_fees(
        amount: float,
        plan: AjoSaving | RotationGroup,
        plan_type: PlanType,
    ) -> None:
        # split the fees
        shares = calculate_commissions(amount=amount)

        # settle the commissions for company in company commissions wallet
        pay_company_commission(
            amount=shares.get("company_share"),
            full_amount=amount,
            savings_name=plan.name,
            savings_frequency=plan.frequency,
            quotation_id=plan.quotation_id,
            plan_type=plan_type,
        )

        # settle the commissions for admin in the admin ROSCA_PERSONAL wallet
        RotationGroupPay.pay_rosca_admin_commission(
            amount=shares.get("agent_share"),
            full_amount=amount,
            savings_name=plan.name,
            savings_frequency=plan.frequency,
            quotation_id=plan.quotation_id,
            user=plan.user,
        )

    @staticmethod
    def pay_rosca_admin_commission(
        amount: float,
        full_amount: float,
        savings_name: str,
        savings_frequency: str,
        quotation_id: str,
        user: AbstractUser,
    ) -> None:
        description_text = f"{amount} commission earned, from {savings_name}"
        description_text += f" {savings_frequency} savings"

        # call functions to create the Commission instances for both company and agent
        admin_commission = CommissionService.create_commission_instance(
            user=user,
            commission_type=CommissionType.ADMIN,
            amount=amount,
            description=description_text,
            quotation_id=quotation_id,
            total_amount_taken_as_commission=full_amount,
            plan_type=PlanType.ROTATIONGROUP,
        )

        rosca_wallet = RotationGroupMemberSelector.get_rotation_group_wallet(user=user)

        increment_transaction = (
            TransactionService.create_agent_commissions_increment_transaction(
                user=user,
                amount=amount,
                quotation_id=quotation_id,
                transaction_description=description_text,
                wallet_type=WalletTypes.ROSCA_PERSONAL,
                plan_type=PlanType.ROTATIONGROUP,
            )
        )

        fund_wallet_and_update_transaction(
            wallet=rosca_wallet,
            amount=amount,
            transaction_instance=increment_transaction,
        )

    @staticmethod
    @django_transaction.atomic
    def pay_rotation_group_contribution(
        rotation_group: RotationGroup,
        user: AbstractUser,
        transaction: Transaction,
        contribution_amount: float,
        debit_credit_info: Dict[str, Any],
    ) -> Transaction:
        if contribution_amount != rotation_group.contribution_amount:
            raise ValueError(
                "contribution amount paid does not match the group's contribution amount"
            )

        # update transaction
        transaction.wallet_balance_before = debit_credit_info.get("balance_before")
        transaction.wallet_balance_after = debit_credit_info.get("balance_after")

        transaction.save()

        # obtain collector information again from database
        collector_info = (
            RotationGroupPay.collecting_rotation_group_member_checks_before_payment(
                rotation_group=rotation_group,
                user=user,
            )
        )
        collector: RotationGroupMember = collector_info.get("member")
        collector_rosca_wallet: WalletSystem = collector_info.get("wallet")
        collector_position: int = collector_info.get("position")

        # update rotation group member who paid fields
        member_who_paid: RotationGroupMember = rotation_group.members.filter(
            user=user
        ).first()
        member_who_paid.frequency_paid += 1
        member_who_paid.total_amount_contributed += contribution_amount
        RotationGroupMemberService.update_paid_positions(
            member=member_who_paid,
            paid_position=collector_position,
        )
        member_who_paid.save()

        # remove the fees
        amount = rotation_group.amount
        fees = round(contribution_amount - amount, 2)

        # create a debit transaction to remove fees
        debit_transaction = TransactionService.create_ajo_commissions_transaction(
            user=collector.user,
            amount=fees,
            quotation_id=rotation_group.quotation_id,
            transaction_description=f"{fees} was debited as fees for rotation group, {rotation_group.name}.",
            plan_type=PlanType.ROTATIONGROUP,
            wallet_type=WalletTypes.ROSCA_PERSONAL,
        )

        debit_wallet_and_update_transaction(
            amount=fees,
            wallet=collector_rosca_wallet,
            transaction_instance=debit_transaction,
        )

        # pay the fees
        RotationGroupPay.share_rotation_group_fees(
            amount=fees,
            plan=rotation_group,
            plan_type=PlanType.ROTATIONGROUP,
        )

        # update collector fields
        collector.refresh_from_db()
        collector.total_amount_collected += amount

        if collector.total_amount_collected >= (rotation_group.collection_amount / 2):
            if not collector.collected:
                collector.collected = True
        #         RotationGroupMemberService.update_paid_positions(
        #             member=collector,
        #             paid_position=collector_position,
        #         )
        #         collector.frequency_paid += 1
        #         collector.total_amount_contributed += contribution_amount

        collector.save()

        # activate the rotation group if not activated
        if not rotation_group.is_activated:
            rotation_group.is_activated = True
            rotation_group.save()


@django_transaction.atomic
def resolve_pending_funding_agent_from_liberty_transaction(
    transaction_instance: Transaction,
) -> None:
    """
    Resolves a Pending transaction that is meant to be `SUCCESSFUL`
    by paying it into the agent's wallet.

    Args:
        transaction_instance (Transaction): the pending transaction instance

    Raises:
        ValueError: this transaction has been handled already
        ValueError: encountered an error
    """
    if transaction_instance.status != Status.PENDING:
        raise ValueError("this transaction has been handled already")

    user = transaction_instance.user
    amount = transaction_instance.amount

    if transaction_instance.transaction_form_type in [
        TransactionFormType.FUND_FROM_LIBERTY_PAY,
        TransactionFormType.BANK_DEPOSIT,
    ]:
        # check if the user has any prefunding being owed
        prefunding_selector = PrefundingSelector(user=transaction_instance.user)
        prefunding_owed = (
            prefunding_selector.how_much_prefunding_has_been_used_against_hold_balance()
        )

        if prefunding_owed > 0:
            prefunding_repayment_amount = min(amount, prefunding_owed)
            amount -= prefunding_repayment_amount
            amount = max(amount, 0)

            try:
                actions.PrefundingActions(user=user).remit_money_to_repay_prefunding(
                    amount=prefunding_repayment_amount,
                    unique_reference=f"{transaction_instance.unique_reference}_remit",
                )

                prefunding_instance = (
                    prefunding_selector.get_the_latest_prefunding_instance()
                )
                if prefunding_instance:
                    PrefundingService(
                        prefunding=prefunding_instance
                    ).payback_prefunding(amount=prefunding_repayment_amount)

            except Exception as err:
                raise ValueError(f"encountered an error: {str(err)}")

    if amount > 0:
        agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet(
            wallet_type=transaction_instance.wallet_type
        )

        if (
            transaction_instance.transaction_form_type
            == TransactionFormType.LIB_PROSPER_LOAN_DEPOSIT
        ):
            transaction_type = (
                TransactionDescriptionType.PROSPER_LOAN_DEPOSIT_FROM_LIBERTYPAY_TO_PROSPER_INV_WALLET
            )
            transaction_form_type = TransactionFormType.PROSPER_LOAN_DEPOSIT
        else:
            transaction_type = (
                TransactionDescriptionType.FUND_AGENT_FROM_LIBERTYPAY_TO_AJO_WALLET
            )
            transaction_form_type = TransactionFormType.WALLET_DEPOSIT

        fund_transaction_description_function = transaction_description(
            transaction_type=transaction_type,
            amount=amount,
        )
        description = fund_transaction_description_function()

        unique_reference = f"{transaction_instance.unique_reference}_deposit"
        try:
            credit_transaction = Transaction.objects.get(
                user=user, unique_reference=unique_reference
            )
        except Transaction.DoesNotExist as err:
            credit_transaction = (
                TransactionService.create_deposit_by_virtual_account_in_ajo_transaction(
                    user=user,
                    amount=amount,
                    wallet_type=transaction_instance.wallet_type,
                    transaction_form_type=transaction_form_type,
                    unique_reference=f"{transaction_instance.unique_reference}_deposit",
                    description=description,
                    status=Status.PENDING,
                )
            )

        fund_agent_wallet(
            transaction=credit_transaction,
            wallet=agent_wallet,
            amount=amount,
            unique_reference=transaction_instance.unique_reference,
        )

        # update the transaction
        transaction_instance.status = Status.SUCCESS
        transaction_instance.save()

        # if transaction_instance.transaction_form_type == "PROSPER_LOAN_DEPOSIT":
        #     from loans.models import ProsperAgent

        #     ProsperAgent.create_instance(user, amount, unique_reference=transaction_instance.unique_reference)


@shared_task
@django_transaction.atomic
def reverse_personal_ajo_savings_debit(
    user_id: int,
    amount: float,
    failed_transaction_id: str,
    plan_type: PlanType,
    request_data: dict,
    plan_id: int | None = None,
    quotation_id: str | None = None,
) -> Dict[str, Any]:
    # obtain user id
    user = get_user_model().objects.get(id=user_id)

    # obtain the plan instance
    if plan_id:
        plan_instance: AjoSaving = PersonalAjoSavingsSelector(
            id=plan_id, user=user
        ).get_ajo_savings_plan()
    else:
        plan_instance = AjoSaving.objects.get(quotation_id=quotation_id)

    # create reversal transaction
    transaction = TransactionService.create_reversal_of_funds_transaction(
        user=user,
        amount=amount,
        wallet_type=WalletTypes.AJO_PERSONAL,
        request_data=request_data,
        quotation_id=plan_instance.quotation_id,
        plan_type=plan_type,
        description=f"{amount} was reversed to your plan due to failed transaction",
        unique_reference=f"{failed_transaction_id}_reversal",
    )

    # retrieve wallet instance
    wallet = PersonalSelector(user=user).get_savings_wallet()

    # fund the wallet
    fund = WalletSystem.fund_balance(
        wallet=wallet,
        amount=amount,
        transaction_instance=transaction,
    )

    # set the transaction wallet, plan balance before and plan_instance balance before
    transaction.wallet_balance_before = fund.get("balance_before")
    transaction.plan_balance_before = plan_instance.amount_saved
    plan_instance.plan_balance_before = plan_instance.amount_saved

    # decrease the amount saved
    plan_instance.amount_saved += amount

    # set the balance after as done above
    transaction.wallet_balance_after = fund.get("balance_after")
    transaction.plan_balance_after = transaction.plan_balance_before + amount
    plan_instance.plan_balance_after = plan_instance.plan_balance_before + amount

    # save the changes
    plan_instance.save()
    transaction.save()

    plan_instance.refresh_from_db()
    plan_instance.calculate_frequency_paid(save=True)

    return {
        "status": "success",
        "message": f"ajo plan {plan_id} had {amount} reversed due to failed transaction",
    }


def split_agent_rosca_commissions(
    shared_amount: float,
    wallet: WalletSystem,
    company_user: AbstractUser,
    transaction_description: str,
    quotation_id: str | None = None,
):
    company_commissions_wallet = AjoCommissionsSelector(
        user=company_user
    ).get_commissions_wallet()

    calculate_commissions = calculate_rosca_commissions(amount=shared_amount)
    agent_commissions_amount = calculate_commissions.get("agent_share")
    company_commissions_amount = calculate_commissions.get("company_share")

    deposit_transaction_instance = (
        TransactionService.create_deposit_by_wallet_transaction(
            user=wallet.user,
            amount=agent_commissions_amount,
            wallet_type=wallet.wallet_type,
            quotation_id=quotation_id,
            plan_type=PlanType.ROTATIONGROUP,
            description=transaction_description,
        )
    )
    # firstly increment the ajo user's wallet and create the debit credit info
    fund_agent = WalletSystem.fund_balance(
        wallet=wallet,
        amount=agent_commissions_amount,
        transaction_instance=deposit_transaction_instance,
        unique_reference=f"ref-{uuid.uuid4()}",
    )

    # update the transaction fields
    deposit_transaction_instance.status = Status.SUCCESS
    deposit_transaction_instance.transaction_date_completed = timezone.localtime()
    deposit_transaction_instance.wallet_balance_before = fund_agent.get(
        "balance_before"
    )
    deposit_transaction_instance.wallet_balance_after = fund_agent.get("balance_after")
    deposit_transaction_instance.save()

    # create a transaction instance
    increment_transaction = (
        TransactionService.create_agent_commissions_increment_transaction(
            user=company_user,
            amount=company_commissions_amount,
            quotation_id=quotation_id,
            transaction_description=transaction_description,
        )
    )

    # increment the commissions balance of the company's wallet
    fund_wallet_and_update_transaction(
        wallet=company_commissions_wallet,
        amount=company_commissions_amount,
        transaction_instance=increment_transaction,
    )


@django_transaction.atomic
def debit_wallet_for_withdrawal(
    amount: float,
    transaction_instance: Transaction,
    wallet: WalletSystem,
    transaction_description: str,
):
    # debit the user's position
    debit = WalletSystem.deduct_balance(
        wallet=wallet,
        amount=amount,
        transaction_instance=transaction_instance,
    )

    # place description in the transaction instance and update other fields
    transaction_instance.status = Status.SUCCESS
    transaction_instance.description = transaction_description
    transaction_instance.wallet_balance_before = debit.get("balance_before")
    transaction_instance.wallet_balance_after = debit.get("balance_after")
    transaction_instance.save()


@shared_task
@django_transaction.atomic
def pay_personal_ajo_commissions(ajo_savings_id: int, user_id: int):
    """
    This task handles the collection of commissions from a personal
    ajo savings plan.

    Args:
        ajo_savings_id (int): the ID of the ajo plan.
        user_id (int): the ID of the user.

    Raises:
        Exception: any error encountered while attempting flow.
    """
    try:
        # Obtain necessary information

        user = get_user_model().objects.get(id=user_id)

        personal_ajo_selector = PersonalAjoSavingsSelector(id=ajo_savings_id, user=user)

        ajo_savings = personal_ajo_selector.get_ajo_savings_plan()

        personal_selector = PersonalSelector(user=user)

        savings_wallet = personal_selector.get_savings_wallet()

        amount = (
            ajo_savings.periodic_amount
            if ajo_savings.plan_type
            else ajo_savings.commission_amount
        )

        # PERFORM CHECKS

        if ajo_savings.commission_paid:
            return {
                "status": "failed",
                "message": "commissions has already been paid for this plan",
            }

        if not ajo_savings.amount_saved >= amount:
            return {
                "status": "failed",
                "message": "not enough to take out commissions",
            }

        if Transaction.objects.filter(
            user=user,
            transaction_form_type=TransactionFormType.COMMISSION,
            quotation_id=ajo_savings.quotation_id,
            onboarded_user__isnull=True,
            transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
        ).exists():
            return {
                "status": "failed",
                "message": "commission has been taken out already",
            }

        # CONTINUE FLOW

        ## debit ajo plan
        debit_description = transaction_description(
            TransactionDescriptionType.DEBIT_FOR_COMMISSION,
            amount=amount,
        )(ajo_saving_plan_name=ajo_savings.name)

        debit_transaction = TransactionService.create_ajo_commissions_transaction(
            user=user,
            amount=amount,
            quotation_id=ajo_savings.quotation_id,
            wallet_type=WalletTypes.AJO_PERSONAL,
            transaction_description=debit_description,
        )

        debit_ajo_plan(
            ajo_savings=ajo_savings,
            amount=amount,
            wallet=savings_wallet,
            transaction_instance=debit_transaction,
        )

        ## service charge
        commissions_service_charge = (
            ConstantTable.get_constant_table_instance().commissions_service_charge
        )

        amount = amount - commissions_service_charge

        service_charge_transaction = TransactionService.service_charge_transaction(
            user=user,
            amount=commissions_service_charge,
            ajo_user=None,
            quotation_id=ajo_savings.quotation_id,
            transaction_description=f"{commissions_service_charge} was collected as a service charge for commission from ajo plan, {ajo_savings.name}",
        )

        ## call function to create commission instance
        # try to obtain the company's user account
        company_user = (
            get_user_model().objects.filter(email="<EMAIL>").last()
        )

        # company description text
        company_description = f"{amount} commission earned, from {ajo_savings.name}"
        company_description += (
            f" {ajo_savings.frequency.lower()} savings, {ajo_savings.user.email}"
        )

        company_commission = CommissionService.create_commission_instance(
            user=company_user,
            commission_type=CommissionType.COMPANY,
            amount=amount,
            description=company_description,
            quotation_id=ajo_savings.quotation_id,
            total_amount_taken_as_commission=amount,
            plan_type=PlanType.AJO,
        )

        if company_user:
            fund_company_commissions_wallet(
                amount=amount,
                company_user=company_user,
                ajo_savings=ajo_savings,
                transaction_description=company_description,
            )

        ## commission taken
        ajo_savings.commission_paid = True
        ajo_savings.save()

        return {
            "status": "success",
            "message": f"commission taken out from {ajo_savings.name} and distributed successfully",
        }

    except Exception as err:
        pass


def create_commission_fund_company(
    amount: float,
    description: str,
    quotation_id: str | None,
    total_taken_as_commission: float,
    plan_type: PlanType,
    transaction_form_type: TransactionFormType | None = None,
):
    # Fund company
    company_user = get_user_model().objects.filter(email="<EMAIL>").last()

    company_commission = CommissionService.create_commission_instance(
        user=company_user,
        commission_type=CommissionType.COMPANY,
        amount=amount,
        description=description,
        quotation_id=quotation_id,
        total_amount_taken_as_commission=total_taken_as_commission,
        plan_type=plan_type,
    )

    if company_user:
        fund_company_commissions_wallet(
            amount=amount,
            company_user=company_user,
            quotation_id=quotation_id,
            transaction_description=description,
            transaction_form_type=transaction_form_type,
        )


@django_transaction.atomic
def debit_ajo_user_wallet_for_dojah_charge(
    ajo_user: AjoUser, amount: float, ajo_user_wallet: WalletSystem
) -> None:
    """
    Debit the ajo spending wallet of the ajo user who wants to utilize dojah verification

    Args:
        ajo_user (AjoUser): the ajo user
        amount (float): the amount to be debited

    Raises:
        ValueError: insufficient funds in the spending wallet
    """
    wallet_type = ajo_user_wallet.wallet_type

    if ajo_user_wallet.available_balance < amount:
        raise ValueError("insufficient funds in the wallet")

    debit_description = (
        f"{amount} was debited from your wallet as a verification charge"
    )

    debit_transaction = TransactionService.create_ajo_cashout_by_ussd(
        user=ajo_user.user,
        ajo_user=ajo_user,
        amount=amount,
        transaction_description=debit_description,
        transaction_form_type=TransactionFormType.DOJAH_SERVICE_CHARGE,
        wallet_type=getattr(WalletTypes, wallet_type),
        transaction_destination=None,
    )

    debit_wallet_and_update_transaction(
        amount=amount,
        wallet=ajo_user_wallet,
        transaction_instance=debit_transaction,
    )

    # fund company
    create_commission_fund_company(
        amount=amount,
        description=f"{amount} was taken as dojah service charge from ajo user, {ajo_user.phone_number}, for verification.",
        quotation_id=None,
        total_taken_as_commission=amount,
        plan_type=PlanType.AJO,
        transaction_form_type=TransactionFormType.DOJAH_SERVICE_CHARGE,
    )


@django_transaction.atomic
def perform_reversal_for_failed_external_transfers(
    transaction: Transaction,
) -> Dict[str, Any]:
    """
    Function to do reversals for failed external transfers

    Args:
        transaction (Transaction): the failed external transfer transaction instance

    Returns:
        Dict[str, Any]: the result
    """
    response = {
        "status": False,
        "message": "",
    }

    if transaction.transaction_form_type not in [
        TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT,
        TransactionFormType.DISBURSE_TO_EXTERNAL_ACCOUNT,
        TransactionFormType.FUND_MONNIFY_ACCOUNT,
    ]:
        response["message"] = "this is not a transfer transaction"
        return response

    # update the pending external transfer transaction
    transaction.status = Status.FAILED
    transaction.save()

    reversal_unique_ref = (
        f"{transaction.unique_reference}_reversal"
        if transaction.unique_reference
        else f"{transaction.transaction_id}_reversal"
    )
    # if transaction.unique_reference:
    #     reversal_unique_ref = f"{transaction.unique_reference}_reversal"

    if not DebitCreditRecordOnAccount.objects.filter(
        transaction_id=transaction.transaction_id,
        entry=TransactionTypeCreditOrDebitChoices.DEBIT,
    ).exists():
        response["message"] = "no debit was made for this transfer transaction"
        return response

    if reversal_unique_ref:
        if DebitCreditRecordOnAccount.objects.filter(
            unique_reference__contains=reversal_unique_ref,
            entry=TransactionTypeCreditOrDebitChoices.CREDIT,
        ).exists():
            response["message"] = "this transfer transaction has been reversed already"
            return response
    else:
        if Transaction.objects.filter(
            transaction_form_type=TransactionFormType.REVERSAL_EXTERNAL_TRANSFER,
            transaction_source_id=transaction.transaction_id,
            unique_reference=reversal_unique_ref,
        ).exists():
            response["message"] = "this transfer transaction has been reversed already"
            return response

    # get users
    user = transaction.user
    ajo_user = transaction.onboarded_user

    if (
        transaction.transaction_form_type
        == TransactionFormType.DISBURSE_TO_EXTERNAL_ACCOUNT
    ):
        from loans.models import AjoLoan, DisbursementStatus, LoanStatus

        loan_instance = AjoLoan.objects.filter(
            loan_ref=transaction.quotation_id,
            agent=transaction.user,
            borrower=transaction.onboarded_user,
        ).last()

        if not loan_instance:
            response["message"] = (
                f"there is no loan instance with {transaction.quotation_id}"
            )
            return response

        loan_instance.status = LoanStatus.OPEN_TO_SUPERVISOR
        loan_instance.supervisor_disbursement_status = DisbursementStatus.FAILED
        loan_instance.save()

        # Get user disbursement wallet
        ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
        wallet = ajo_user_selector.get_loan_disbursement_wallet()
        amount = transaction.amount
        plan_type = PlanType.LOAN
        quotation_id = loan_instance.loan_ref

        # Notify Supervisor of failed disbursement
        try:
            from loans.tasks import failed_disbursements_supervisor_notification

            failed_disbursements_supervisor_notification(
                email=loan_instance.checked_by
                and loan_instance.checked_by.email
                or "<EMAIL>",
                borrower_phone_number=loan_instance.borrower.phone_number,
                amount=loan_instance.amount,
                full_name=f"{loan_instance.borrower.first_name} {loan_instance.borrower.last_name}",
                title="Failed Disbursement Notification",
                subject="Failed Disbursement Notification",
            )
        except:
            pass

        # if transaction.transfer_provider == DisbursementProviderType.VFD_PROVIDER:
        #     vfd_transfer_charge = ConstantTable.get_constant_table_instance().vfd_transfer_charge
        #     amount -= vfd_transfer_charge

    else:

        if transaction.wallet_type in [
            WalletTypes.ONLENDING,
            WalletTypes.ONLENDING_MAIN,
        ]:
            plan_type = PlanType.ONLENDING
            wallet = OnlendingSelector(
                user=user, ajo_user=ajo_user
            ).get_main_onlending_wallet()
        else:
            plan_type = PlanType.AJO
            wallet = AjoUserSelector(ajo_user=ajo_user).get_spending_wallet()

        amount = transaction.amount
        quotation_id = None

    # create a new transaction for reversal
    reversal_transaction = TransactionService.create_reversal_of_funds_transaction(
        user=transaction.user,
        ajo_user=ajo_user,
        amount=amount,
        wallet_type=wallet.wallet_type,
        request_data=transaction.request_data,
        quotation_id=quotation_id,
        plan_type=plan_type,
        description=f"REVERSAL- {transaction.description}",
        transaction_form_type=TransactionFormType.REVERSAL_EXTERNAL_TRANSFER,
        unique_reference=reversal_unique_ref,
        transaction_source_id=transaction.transaction_id,
        transfer_provider=transaction.transfer_provider,
    )

    fund_wallet_and_update_transaction(
        wallet=wallet,
        amount=transaction.amount,
        transaction_instance=reversal_transaction,
        unique_reference=reversal_unique_ref,
        ajo_user=ajo_user,
    )

    response["status"] = True
    response["message"] = "reversal processed sucessfully"
    return response


@django_transaction.atomic
def move_money_from_wallet_to_wallet(
    amount: float,
    from_wallet: WalletSystem,
    from_wallet_description: str,
    to_wallet: WalletSystem,
    to_wallet_description: str,
) -> None:
    """
    Debit one wallet and fund another

    Args:
        amount (float): the amount to be debited and funded
        from_wallet (WalletSystem): wallet to be debited
        from_wallet_description (str): transaction description
        to_wallet (WalletSystem): wallet to be funded
        to_wallet_description (str): transaction description

    Raises:
        ValueError: insufficient funds to transfer
    """
    if not from_wallet.available_balance >= amount:
        raise ValueError("insufficient funds to transfer")

    debit_transaction = TransactionService.create_transfer_to_from_wallet_transaction(
        user=from_wallet.user,
        amount=amount,
        wallet_type=from_wallet.wallet_type,
        transaction_description=from_wallet_description,
        transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
        ajo_user=from_wallet.onboarded_user,
    )

    debit_wallet_and_update_transaction(
        amount=amount,
        wallet=from_wallet,
        transaction_instance=debit_transaction,
    )

    fund_transaction = TransactionService.create_transfer_to_from_wallet_transaction(
        user=to_wallet.user,
        amount=amount,
        wallet_type=to_wallet.wallet_type,
        transaction_description=to_wallet_description,
        transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
        transaction_destination=None,
        ajo_user=to_wallet.onboarded_user,
    )

    fund_wallet_and_update_transaction(
        wallet=to_wallet,
        amount=amount,
        transaction_instance=fund_transaction,
        ajo_user=to_wallet.onboarded_user,
    )


@django_transaction.atomic
def move_money_from_ajo_user_digital_to_spend(
    ajo_user: AjoUser,
    amount: float,
) -> None:
    ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)

    digital_wallet = ajo_user_selector.get_digital_wallet()
    spend_wallet = ajo_user_selector.get_spending_wallet()

    if not digital_wallet.available_balance >= amount:
        raise ValueError("insufficient funds to transfer")

    move_money_from_wallet_to_wallet(
        amount=amount,
        from_wallet=digital_wallet,
        from_wallet_description=f"{amount} has been transferred to spend wallet",
        to_wallet=spend_wallet,
        to_wallet_description=f"{amount} has been credited from digital wallet",
    )


@django_transaction.atomic
def debit_wallet_through_transfer(
    from_wallet: WalletSystem,
    amount: float,
    description: str,
    plan_type: Optional[PlanType],
) -> Dict[str, Any]:
    """
    Create a debit transaction and debit the wallet

    Args:
        from_wallet (WalletSystem): the wallet to be deducted
        amount (float): the amount to deduct
        description (str): the transaction description
        plan_type (PlanType): the plan type for this transaction.

    Raises:
        ValueError: insufficient funds to transfer

    Returns:
        Dict[str, Any]: the deduction details and transaction
    """

    debit_transaction = TransactionService.create_transfer_to_from_wallet_transaction(
        user=from_wallet.user,
        amount=amount,
        wallet_type=from_wallet.wallet_type,
        transaction_description=description,
        transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
        ajo_user=from_wallet.onboarded_user,
        plan_type=plan_type,
    )

    if not from_wallet.available_balance >= amount:
        debit_transaction.status = Status.FAILED
        debit_transaction.failure_reason = "Insufficient Funds"
        debit_transaction.save()
        return {"status": False, "message": "Insufficient Funds"}
        # raise ValueError("insufficient funds to transfer")

    deduction = debit_wallet_and_update_transaction(
        amount=amount,
        wallet=from_wallet,
        transaction_instance=debit_transaction,
    )

    return {
        "status": True,
        "transaction": debit_transaction,
        "deduction": deduction,
    }


def fund_ajo_savings_position_without_funding_wallet(
    user: AbstractUser,
    ajo_user: AjoUser,
    ajo_savings: AjoSaving,
    amount: float,
    unique_reference: str | None = None,
    description: str | None = None,
) -> Transaction:
    """
    #### NOTE: This function is meant to fixing the discrepancy with the funded wallet and not funded plans

    Args:
        user (AbstractUser): the user instance
        ajo_user (AjoUser): the ajo user instance
        ajo_savings (AjoSaving): the ajo savings instance
        amount (float): the amount
        request_data (dict | None, optional): the request data. Defaults to None.
        description (str | None, optional): the description for the transaction. Defaults to None.

    Returns:
        Transaction: The deposit transaction
    """

    ajo_savings_service = AjoSavingsService(ajo_savings=ajo_savings)
    ajo_savings = ajo_savings_service.fund_plan(amount=amount)

    deposit_transaction = TransactionService.create_deposit_by_wallet_transaction(
        user=user,
        amount=amount,
        wallet_type=WalletTypes.AJO_USER,
        quotation_id=ajo_savings.quotation_id,
        plan_type=PlanType.AJO,
        ajo_user=ajo_user,
    )
    # update the fields
    deposit_transaction.unique_reference = unique_reference
    deposit_transaction.status = Status.SUCCESS
    deposit_transaction.description = description
    deposit_transaction.transaction_date_completed = timezone.localtime()
    deposit_transaction.plan_balance_before = ajo_savings.plan_balance_before
    deposit_transaction.plan_balance_after = ajo_savings.plan_balance_after
    deposit_transaction.save()

    # update ajo user fields
    AjoUserService(ajo_user=ajo_user).increment_total_saved(amount=amount)
    ajo_savings.is_active = True
    ajo_savings.is_activated = True
    ajo_savings.withdrawn = False
    ajo_savings.save()

    # calculate the days paid
    ajo_savings.refresh_from_db()
    ajo_savings.calculate_frequency_paid(save=True)
    return deposit_transaction


def charge_escrow_fund_spend_on_double_charge_loan_disbursement(
    ajo_user: AjoUser,
    quotation_id: str,
    escrow_wallet: WalletSystem,
    amount: float,
    unique_reference: str | None = None,
) -> Transaction:
    user = ajo_user.user
    transaction_form_type = TransactionFormType.CHARGE_ESCROW_WALLET
    debit_transaction = TransactionService.dynamic_deduction_from_wallet_transaction(
        user=user,
        amount=amount,
        wallet_type=escrow_wallet.wallet_type,
        quotation_id=quotation_id,
        # request_data="",
        ajo_user=ajo_user,
        description="charged to fund spend due to system error on loan disbursement",
        unique_reference=unique_reference,
        transaction_form_type=transaction_form_type,
    )

    debit_wallet_and_update_transaction(
        amount=amount,
        wallet=escrow_wallet,
        transaction_instance=debit_transaction,
    )

    # fund spend wallet
    ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
    ajo_user_spend_wallet = ajo_user_selector.get_spending_wallet()

    credit_transaction = TransactionService.create_transfer_to_from_wallet_transaction(
        user=user,
        amount=amount,
        transaction_description=f"{amount} was deposited to your spend wallet from your escrow wallet: reason-> double charge on spend",
        transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
        ajo_user=ajo_user,
        wallet_type=WalletTypes.AJO_SPENDING,
        transaction_form_type=TransactionFormType.REFUND_SPEND_DOUBLE_LOAN_CHARGE,
    )

    # increment the spending wallet's balance
    fund_wallet_and_update_transaction(
        wallet=ajo_user_spend_wallet,
        amount=amount,
        transaction_instance=credit_transaction,
        ajo_user=ajo_user,
    )
    # credit_transaction.unique_reference = unique_reference
    credit_transaction.quotation_id = quotation_id
    credit_transaction.save()


def charge_disbursement_Wallet_and_move_funds_to_spend(
    loan_instance,
    ajo_user: AjoUser,
    quotation_id: str,
    disbursement_wallet: WalletSystem,
    amount: float,
    unique_reference: str | None = None,
) -> Transaction:
    from loans.enums import LoanStatus

    user = ajo_user.user
    transaction_form_type = TransactionFormType.CHARGE_DISBURSEMENT_WALLET
    debit_transaction = TransactionService.dynamic_deduction_from_wallet_transaction(
        user=user,
        amount=amount,
        wallet_type=disbursement_wallet.wallet_type,
        quotation_id=quotation_id,
        # request_data="",
        ajo_user=ajo_user,
        description="manually charge disbursement wallet and move funds to spend for withdrawal",
        unique_reference=unique_reference,
        transaction_form_type=transaction_form_type,
    )

    debit_wallet_and_update_transaction(
        amount=amount,
        wallet=disbursement_wallet,
        transaction_instance=debit_transaction,
    )

    # fund spend wallet
    ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
    ajo_user_spend_wallet = ajo_user_selector.get_spending_wallet()

    credit_transaction = TransactionService.create_transfer_to_from_wallet_transaction(
        user=user,
        amount=amount,
        transaction_description=f"{amount} was deposited to your spend wallet from your disbursement wallet",
        transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
        ajo_user=ajo_user,
        wallet_type=WalletTypes.AJO_SPENDING,
        transaction_form_type=TransactionFormType.DISBURSEMENT_TO_SPEND,
    )

    # increment the spending wallet's balance
    fund_wallet_and_update_transaction(
        wallet=ajo_user_spend_wallet,
        amount=amount,
        transaction_instance=credit_transaction,
        ajo_user=ajo_user,
    )
    # credit_transaction.unique_reference = unique_reference
    credit_transaction.quotation_id = quotation_id
    credit_transaction.save()
    # update loan status to open
    loan_instance.status = LoanStatus.OPEN
    loan_instance.save()


class PlanPayments:

    @classmethod
    @django_transaction.atomic
    def increment_positions(
        cls, plan: Union[Onlending], fund_transaction: Transaction, amount: float
    ):
        plan.plan_balance_before = plan.amount_saved
        fund_transaction.plan_balance_before = plan.amount_saved

        plan.amount_saved += amount

        plan.plan_balance_after = plan.plan_balance_before + amount
        fund_transaction.plan_balance_after = plan.plan_balance_before + amount

        plan.save()
        fund_transaction.save()

    @classmethod
    @django_transaction.atomic
    def decrement_positions(
        cls, plan: Union[Onlending], debit_transaction: Transaction, amount: float
    ):
        debit_transaction.plan_balance_before = plan.amount_saved
        plan.plan_balance_before = plan.amount_saved

        # decrease the amount saved
        plan.amount_saved -= amount

        # set the balance after as done above for balance before
        debit_transaction.plan_balance_after = (
            debit_transaction.plan_balance_before - amount
        )
        plan.plan_balance_after = plan.plan_balance_before - amount

        plan.save()
        debit_transaction.save()

    @classmethod
    @django_transaction.atomic
    def fund_onlending_plan(
        cls,
        from_wallet: WalletSystem,
        amount: float,
        onlending_plan: Onlending,
        onlending_wallet: WalletSystem,
    ) -> None:
        """
        Fund an onlending plan from any wallet
        """
        debit_wallet_through_transfer(
            from_wallet=from_wallet,
            amount=amount,
            description=f"{amount} transferred for funding onlending plan {onlending_plan.name}.",
            plan_type=PlanType.ONLENDING,
        )

        fund_transaction = TransactionService.create_deposit_by_wallet_transaction(
            user=from_wallet.user,
            amount=amount,
            quotation_id=onlending_plan.quotation_id,
            ajo_user=onlending_wallet.onboarded_user,
            unique_reference=None,
            description=f"{amount} was funded into your onlending plan, {onlending_plan.name}",
            wallet_type=onlending_wallet.wallet_type,
            plan_type=PlanType.ONLENDING,
        )

        fund_wallet_and_update_transaction(
            wallet=onlending_wallet,
            amount=amount,
            transaction_instance=fund_transaction,
            ajo_user=onlending_wallet.onboarded_user,
        )

        cls.increment_positions(
            plan=onlending_plan,
            fund_transaction=fund_transaction,
            amount=amount,
        )

        onlending_plan.refresh_from_db()
        onlending_plan.is_activated = True
        onlending_plan.is_active = True

        onlending_plan.save()

    @classmethod
    @shared_task
    @django_transaction.atomic
    def pay_upfront_interest_into_wallet(
        cls,
        quotation_id: str,
        plan_type: PlanType,
        wallet: WalletSystem,
    ) -> Dict[str, str]:
        plan_instance = None

        if plan_type == PlanType.ONLENDING:
            try:
                plan_instance = Onlending.objects.get(quotation_id=quotation_id)
            except Onlending.DoesNotExist as err:
                return {
                    "status": "failed",
                    "message": "could not obtain onlending plan",
                }

        if not plan_instance:
            return {
                "status": "failed",
                "message": "could not obtain plan instance",
            }

        if plan_instance.interest_paid:
            raise ValueError("interest has been paid already")

        amount = plan_instance.total_interest_earned

        interest_transaction = TransactionService.create_upfront_interest_to_wallet_transaction(
            user=plan_instance.user,
            amount=plan_instance.total_interest_earned,
            quotation_id=quotation_id,
            plan_type=plan_type,
            ajo_user=plan_instance.ajo_user,
            description=f"{amount} paid as interest for '{plan_instance.name}' {plan_type.lower()} plan",
            wallet_type=wallet.wallet_type,
        )

        fund = fund_wallet_and_update_transaction(
            wallet=wallet,
            amount=amount,
            transaction_instance=interest_transaction,
            ajo_user=plan_instance.ajo_user,
        )

        InterestsPaidTableMethods.create_upfront_interest_instance(
            user=plan_instance.user,
            plan_type=plan_type,
            total_interest_paid=plan_instance.total_interest_earned,
            plan_quotation_id=plan_instance.quotation_id,
        )

        plan_instance.interest_paid = True
        plan_instance.save()

    @classmethod
    @shared_task
    @django_transaction.atomic
    def pay_interest_into_plan(
        cls,
        plan_instance: Union[Onlending],
        wallet: WalletSystem,
    ) -> None:
        if plan_instance.interest_paid:
            raise ValueError("interest has been paid already")

        amount = plan_instance.total_interest_earned

        if isinstance(plan_instance, Onlending):
            plan_type = PlanType.ONLENDING

        interest_transaction = TransactionService.create_upfront_interest_to_wallet_transaction(
            user=plan_instance.user,
            amount=amount,
            quotation_id=plan_instance.quotation_id,
            plan_type=plan_type,
            ajo_user=plan_instance.ajo_user,
            description=f"{amount} paid as interest for '{plan_instance.name}' {plan_type.lower()} plan",
            wallet_type=wallet.wallet_type,
            transaction_form_type=TransactionFormType.INTEREST_DEPOSIT,
        )

        fund_wallet_and_update_transaction(
            wallet=wallet,
            amount=amount,
            transaction_instance=interest_transaction,
            ajo_user=plan_instance.ajo_user,
        )

        InterestsPaidTableMethods.create_upfront_interest_instance(
            user=plan_instance.user,
            plan_type=plan_type,
            total_interest_paid=amount,
            plan_quotation_id=plan_instance.quotation_id,
        )

        cls.increment_positions(
            plan=plan_instance,
            fund_transaction=interest_transaction,
            amount=amount,
        )

        plan_instance.refresh_from_db()
        plan_instance.interest_paid = True
        plan_instance.save()

    @classmethod
    @django_transaction.atomic
    def debit_savings_plan(
        cls,
        savings: Union[Onlending],
        amount: float,
        wallet: WalletSystem,
        transaction_instance: Transaction,
    ) -> None:
        # firstly deduct the ajo user wallet and create the debit_credit_info instance

        ajo_user = savings.ajo_user

        deduct = debit_wallet(
            wallet=wallet,
            amount=amount,
            transaction_instance=transaction_instance,
            onboarded_user=ajo_user,
        )

        # set the transaction wallet, plan balance before and plan_instance balance before
        transaction_instance.wallet_balance_before = deduct.get("balance_before")
        transaction_instance.plan_balance_before = savings.amount_saved
        savings.plan_balance_before = savings.amount_saved

        # decrease the amount saved
        savings.amount_saved -= amount

        # set the balance after as done above for balance before
        transaction_instance.wallet_balance_after = deduct.get("balance_after")
        transaction_instance.plan_balance_after = (
            transaction_instance.plan_balance_before - amount
        )
        savings.plan_balance_after = savings.plan_balance_before - amount

        # save the changes
        savings.save()
        savings.refresh_from_db()

        # set some other fields
        transaction_instance.transaction_date_completed = timezone.localtime()
        transaction_instance.save()

        if isinstance(savings, Onlending):
            savings.is_active = False
            savings.withdrawn = True
            savings.save()

    @classmethod
    @shared_task
    @django_transaction.atomic
    def reverse_onlending_plan_debit(
        cls,
        user_id: int,
        amount: float,
        failed_transaction_id: str,
        request_data: dict,
        plan_type: PlanType = PlanType.ONLENDING,
        plan_id: int | None = None,
        quotation_id: str | None = None,
    ) -> Dict[str, Any]:
        # obtain user id
        user = get_user_model().objects.get(id=user_id)

        # obtain the plan instance
        if plan_id:
            plan_instance: Onlending = OnlendingSelector.get_onlending_plan_by_id(
                id=plan_id, user=user
            )
        else:
            plan_instance = Onlending.objects.get(quotation_id=quotation_id)

        # create reversal transaction
        transaction = TransactionService.create_reversal_of_funds_transaction(
            user=user,
            amount=amount,
            wallet_type=WalletTypes.ONLENDING,
            request_data=request_data,
            quotation_id=plan_instance.quotation_id,
            plan_type=plan_type,
            description=f"{amount} was reversed to your plan due to failed transaction",
            unique_reference=f"{failed_transaction_id}_reversal",
        )

        # retrieve wallet instance
        if plan_instance.ajo_user:
            wallet = OnlendingSelector(
                user=user, ajo_user=plan_instance.ajo_user
            ).get_onlending_wallet()
        else:
            wallet = OnlendingSelector(user=user).get_onlending_wallet()

        # fund the wallet
        fund = WalletSystem.fund_balance(
            wallet=wallet,
            amount=amount,
            transaction_instance=transaction,
        )

        # set the transaction wallet, plan balance before and plan_instance balance before
        transaction.wallet_balance_before = fund.get("balance_before")
        transaction.plan_balance_before = plan_instance.amount_saved
        plan_instance.plan_balance_before = plan_instance.amount_saved

        # decrease the amount saved
        plan_instance.amount_saved += amount

        # set the balance after as done above
        transaction.wallet_balance_after = fund.get("balance_after")
        transaction.plan_balance_after = transaction.plan_balance_before + amount
        plan_instance.plan_balance_after = plan_instance.plan_balance_before + amount

        # save the changes
        plan_instance.save()
        transaction.save()

        return {
            "status": "success",
            "message": f"onlending plan {plan_id} had {amount} reversed due to failed transaction",
        }

    @classmethod
    @django_transaction.atomic
    def rollover_operation(
        cls,
        old_onlending_plan: Onlending,
        new_onlending_plan: Onlending,
        amount: float,
        request_data: Dict[str, Any],
    ) -> None:
        """
        Rollover the money from old onlending plan to new
        onlending plan if it belongs to the same user

        Args:
            old_onlending_plan (Onlending): Old onlending
            new_onlending_plan (Onlending): Rollover onlending plan
        """

        debit_transaction = TransactionService.create_transfer_to_external_account_transaction(
            user=old_onlending_plan.user,
            amount=amount,
            description=f"{amount} was debited from your plan to rollover into your new plan.",
            ajo_user=old_onlending_plan.ajo_user,
            wallet_type=WalletTypes.ONLENDING,
            request_data=request_data,
            quotation_id=old_onlending_plan.quotation_id,
            plan_type=PlanType.ONLENDING,
            transaction_form_type=TransactionFormType.ROLLOVER,
            status=Status.SUCCESS,
            transaction_source=None,
            transaction_destination=TransactionDestination.PLAN,
        )

        cls.decrement_positions(
            plan=old_onlending_plan,
            debit_transaction=debit_transaction,
            amount=amount,
        )

        old_onlending_plan.refresh_from_db()

        fund_transaction = TransactionService.create_deposit_by_wallet_transaction(
            user=new_onlending_plan.user,
            amount=amount,
            quotation_id=new_onlending_plan.quotation_id,
            plan_type=PlanType.ONLENDING,
            wallet_type=WalletTypes.ONLENDING,
            ajo_user=new_onlending_plan.ajo_user,
            unique_reference=None,
            transaction_form_type=TransactionFormType.ROLLOVER,
            status=Status.SUCCESS,
            description=f"{amount} was funded into plan from '{old_onlending_plan.name}' onlending plan",
        )

        cls.increment_positions(
            plan=new_onlending_plan,
            fund_transaction=fund_transaction,
            amount=amount,
        )

        new_onlending_plan.refresh_from_db()
        new_onlending_plan.is_activated = True
        new_onlending_plan.is_active = True
        new_onlending_plan.save()

    @classmethod
    @django_transaction.atomic
    def debit_wallet_and_pay_interest(
        cls,
        plan: Union[Onlending],
        from_wallet: WalletSystem,
        to_wallet: WalletSystem,
    ) -> Dict[str, str]:
        """
        Pay the interest from the wallet into another wallet

        Args:
            plan (Union[Onlending]): the plan instance
            from_wallet (WalletSystem): the wallet being debited
            to_wallet (WalletSystem): the wallet being funded

        Returns:
            Dict[str, str]: The dictionary response
        """
        amount = plan.total_interest_earned

        debit_transaction = TransactionService.dynamic_deduction_from_wallet_transaction(
            user=plan.user,
            amount=amount,
            wallet_type=from_wallet.wallet_type,
            request_data=None,
            description=f"your interest, {amount}, has been debited from your onlending plan",
            ajo_user=plan.ajo_user,
        )

        deduct = debit_wallet_and_update_transaction(
            wallet=from_wallet,
            amount=amount,
            transaction_instance=debit_transaction,
        )

        cls.decrement_positions(
            plan=plan,
            debit_transaction=debit_transaction,
            amount=amount,
        )

        debit_transaction.wallet_balance_before = deduct.get("balance_before")
        debit_transaction.wallet_balance_after = deduct.get("balance_after")
        debit_transaction.transaction_date_completed = timezone.localtime()
        debit_transaction.save()

        interest_transaction = (
            TransactionService.create_upfront_interest_to_wallet_transaction(
                user=plan.user,
                amount=plan.total_interest_earned,
                quotation_id=plan.quotation_id,
                plan_type=PlanType.ONLENDING,
                ajo_user=plan.ajo_user,
                description=f"{amount} paid as interest for '{plan.name}' plan",
                wallet_type=to_wallet.wallet_type,
                transaction_form_type=TransactionFormType.WALLET_DEPOSIT,
            )
        )

        fund = fund_wallet_and_update_transaction(
            wallet=to_wallet,
            amount=amount,
            transaction_instance=interest_transaction,
            ajo_user=plan.ajo_user,
        )
