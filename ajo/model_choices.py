from django.db import models
from django.utils.translation import gettext_lazy as _


class Gender(models.TextChoices):
    MALE = "MALE", _("MALE")
    FEMALE = "FEMALE", _("FEMALE")
    OTHER = "OTHER", _("OTHER")


class OTPType(models.TextChoices):
    SMS = "SMS", _("SMS")
    VOICE = "VOICE", _("VOICE")
    WHATSAPP = "WHATSAPP", _("WHATSAPP")
    USSD = "USSD", _("USSD")


class VerificationStages(models.TextChoices):
    ONBOARDING = "ONBOARDING", _("ONBOARDING")
    CARD_ISSUING = "CARD_ISSUING", _("CARD_ISSUING")
    LOAN_REQUEST = "LOAN_REQUEST", _("LOAN_REQUEST")


class SavingsFrequency(models.TextChoices):
    DAILY = "DAILY", _("DAILY")
    WEEKLY = "WEEKLY", _("WEEKLY")
    MONTHLY = "MONTHLY", _("MONTHLY")


class AjoPlanTypes(models.TextChoices):
    STARTER = "STARTER", _("STARTER")
    LILY = "LILY", _("LILY")
    BUD = "BUD", _("BUD")
    SPRING = "SPRING", _("SPRING")
    BLOOM = "BLOOM", _("BLOOM")
    FLOURISH = "FLOURISH", _("FLOURISH")


class AjoType(models.TextChoices):
    AGENT = "AGENT", _("AGENT")
    PERSONAL = "PERSONAL", _("PERSONAL")


class RoscaType(models.TextChoices):
    PERSONAL_ROSCA = "PERSONAL_ROSCA", _("PERSONAL_ROSCA")
    AGENT_ROSCA = "AGENT_ROSCA", _("AGENT_ROSCA")


class RoscaCollectionType(models.TextChoices):
    ONE_OFF_DEDUCTION = "ONE_OFF_DEDUCTION", _("ONE_OFF_DEDUCTION")
    RECURING_DEDUCTION = "RECURING_DEDUCTION", _("RECURING_DEDUCTION")


class RoscaGroupStatus(models.TextChoices):
    PENDING = "PENDING", _("PENDING")
    RUNNING = "RUNNING", _("RUNNING")
    CLOSED = "CLOSED", _("CLOSED")


class AccountFormType(models.TextChoices):
    AGENT = "AGENT", _("AGENT")
    AJO_DIGITAL = "AJO_DIGITAL", _("AJO_DIGITAL")
    AJO_SPENDING = "AJO_SPENDING", _("AJO_SPENDING")
    LOAN_REPAYMENT = "LOAN_REPAYMENT", _("LOAN_REPAYMENT")
    # AJO_LOAN_REPAYMENT = "AJO_LOAN_REPAYMENT", _("AJO_LOAN_REPAYMENT")
    ONLENDING_MAIN = "ONLENDING_MAIN", _("ONLENDING_MAIN")
    LOAN_RECOVERY = "LOAN_RECOVERY", _("LOAN_RECOVERY")


class TransactionDescriptionType(models.TextChoices):
    TRANSFER_FOR_AJO = "TRANSFER_FOR_AJO", _("TRANSFER_FOR_AJO")
    TRANSFER_FOR_LOAN = "TRANSFER_FOR_LOAN", _("TRANSFER_FOR_LOAN")
    AJO_SAVING_DEPOSIT = "AJO_SAVING_DEPOSIT", _("AJO_SAVING_DEPOSIT")
    LOAN_DEPOSIT = "LOAN_DEPOSIT", _("LOAN_DEPOSIT")
    DEBIT_FOR_COMMISSION = "DEBIT_FOR_COMMISSION", _("DEBIT_FOR_COMMISSION")
    FUND_AGENT_FROM_COMMISSION = "FUND_AGENT_FROM_COMMISSION", _(
        "FUND_AGENT_FROM_COMMISSION"
    )
    DEBIT_COMMISSION_AGENT_BALANCE = "DEBIT_COMMISSION_AGENT_BALANCE", _(
        "DEBIT_COMMISSION_AGENT_BALANCE"
    )
    CASHOUT_BY_USSD = "CASHOUT_BY_USSD", _("CASHOUT_BY_USSD")
    CREATE_LOAN = "CREATE_LOAN", _("CREATE_LOAN")
    CASHOUT_TO_AGENT_WALLET = "CASHOUT_TO_AGENT_WALLET", _("CASHOUT_TO_AGENT_WALLET")
    DEBIT_LOAN_WALLET = "DEBIT_LOAN_WALLET", _("DEBIT_LOAN_WALLET")
    CREDIT_AGENT_WALLET_FROM_LOAN = "CREDIT_AGENT_WALLET_FROM_LOAN", _(
        "CREDIT_AGENT_WALLET_FROM_LOAN"
    )
    WITHDRAW_TO_AGENCY_WALLET = "WITHDRAW_TO_AGENCY_WALLET", _(
        "WITHDRAW_TO_AGENCY_WALLET"
    )
    REVERSE_WITHDRAWAL = "REVERSE_WITHDRAWAL", _("REVERSE_WITHDRAWAL")
    FUND_AGENT_FROM_VIRTUAL_ACCOUNT = "FUND_AGENT_FROM_VIRTUAL_ACCOUNT", _(
        "FUND_AGENT_FROM_VIRTUAL_ACCOUNT"
    )
    FUND_AGENT_FROM_LIBERTYPAY_TO_AJO_WALLET = (
        "FUND_AGENT_FROM_LIBERTYPAY_TO_AJO_WALLET",
        _("FUND_AGENT_FROM_LIBERTYPAY_TO_AJO_WALLET"),
    )
    PAYMENT_FOR_CARD_REQUEST = "PAYMENT_FOR_CARD_REQUEST", _("PAYMENT_FOR_CARD_REQUEST")
    DEBIT_FOR_PLAN_MATURITY = "DEBIT_FOR_PLAN_MATURITY", _("DEBIT_FOR_PLAN_MATURITY")
    CASHOUT_TO_AGENT_FROM_SPEND = "CASHOUT_TO_AGENT_FROM_SPEND", _(
        "CASHOUT_TO_AGENT_FROM_SPEND"
    )
    PROSPER_LOAN_DEPOSIT_FROM_LIBERTYPAY_TO_PROSPER_INV_WALLET = (
        "PROSPER_LOAN_DEPOSIT_FROM_LIBERTYPAY_TO_PROSPER_INV_WALLET",
        _("PROSPER_LOAN_DEPOSIT_FROM_LIBERTYPAY_TO_PROSPER_INV_WALLET"),
    )
    DEBIT_FOR_AJOLOAN_ESCROW = "DEBIT_FOR_AJOLOAN_ESCROW", _("DEBIT_FOR_AJOLOAN_ESCROW")
    DEBIIT_FOR_CREDITBUREAU_CHECK = "DEBIIT_FOR_CREDITBUREAU_CHECK", _(
        "DEBIIT_FOR_CREDITBUREAU_CHECK"
    )
    AJO_LOAN_REPAYMENT_INFLOW = "AJO_LOAN_REPAYMENT_INFLOW", _(
        "AJO_LOAN_REPAYMENT_INFLOW"
    )
    TRANSFER_FROM_AGENT_TO_AGENT = "TRANSFER_FROM_AGENT_TO_AGENT", _(
        "TRANSFER_FROM_AGENT_TO_AGENT"
    )
    BNPL_DEBIT = "DEBIT_FOR_BNPL", _("DEBIT_FOR_BNPL")


class AjoAgentStatus(models.TextChoices):
    ACTIVE = "ACTIVE", _("ACTIVE")
    INACTIVE = "INACTIVE", _("INACTIVE")
    PARTIALLY_INACTIVE = "PARTIALLY_INACTIVE", _("PARTIALLY_INACTIVE")
    SUPER_PERFORMER = "SUPER_PERFORMER", _("SUPER_PERFORMER")
    UNDER_PERFORMING = "UNDER_PERFORMING", _("UNDER_PERFORMING")
    PERFORMING = "PERFORMING", _("PERFORMING")


class ProfileChangeStatus(models.TextChoices):
    PROCESSING = "PROCESSING", _("PROCESSING")
    SUCCESSFUL = "SUCCESSFUL", _("SUCCESSFUL")
    FAILED = "FAILED", _("FAILED")


class AccountProvider(models.TextChoices):
    VFD = "VFD", _("VFD")
    WEMA = "WEMA", _("WEMA")
    CASH_CONNECT = "CASH_CONNECT", _("CASH_CONNECT")


class SavingsType(models.TextChoices):
    """
    Savings Type

    - Buy Now Pay Later
    - Save Now Pay Later
    """

    AJO = "AJO", _("AJO")
    BNPL = "BNPL", _("BNPL")
    SNPL = "SNPL", _("SNPL")
    AJOSEPO = "AJOSEPO", _("AJOSEPO")
    BOOSTA = "BOOSTA", _("BOOSTA")
    BOOSTA_2X = "BOOSTA_2X", _("BOOSTA_2X")
    BOOSTA_2X_MINI = "BOOSTA_2X_MINI", _("BOOSTA_2X_MINI")
    CREDIT_HEALTH = "CREDIT_HEALTH", _("CREDIT_HEALTH")
    MERCHANT = "MERCHANT", _("MERCHANT")


class GroupStatus(models.TextChoices):
    RUNNING = "RUNNING", _("RUNNING")
    PENDING = "PENDING", _("PENDING")
    CLOSED = "CLOSED", _("CLOSED")


class OnboardingStage(models.TextChoices):
    OTP = "OTP", _("OTP")
    PERSONAL_DETAILS = "PERSONAL_DETAILS", _("PERSONAL_DETAILS")
    NEXT_OF_KIN = "NEXT_OF_KIN", _("NEXT_OF_KIN")
    IMAGE_UPLOAD = "IMAGE_UPLOAD", _("IMAGE_UPLOAD")
    ADDRESS_VERIFICATION = "ADDRESS_VERIFICATION", _("ADDRESS_VERIFICATION")
    ADDRESS_VERIFIED = "ADDRESS_VERIFIED", _("ADDRESS_VERIFIED")


class OnboardingSource(models.TextChoices):
    WEB = "WEB", _("WEB")
    MOBILE_APP = "MOBILE_APP", _("MOBILE_APP")
    USSD = "USSD", _("USSD")
    AGENT_REFERRAL = "AGENT_REFERRAL", _("AGENT_REFERRAL")
    LIBERTY_LIFE = "LIBERTY_LIFE", _("LIBERTY_LIFE")
    CREDIT_HEALTH = "CREDIT_HEALTH", _("CREDIT_HEALTH")


class CategoryNames(models.TextChoices):
    REDMI = "REDMI", _("REDMI")
    GRAINS = "GRAINS", _("GRAINS")
    SAMSUNG = "SAMSUNG", _("SAMSUNG")
    SEWING_MACHINE = "SEWING_MACHINE", _("SEWING MACHINE")
    SAMSUNG_2 = "SAMSUNG_2", _("SAMSUNG")
    FAN = "FAN", _("FAN")
    BUSINESS_SOLUTIONS = "BUSINESS_SOLUTIONS", _("BUSINESS SOLUTIONS")
    FREEZER = "FREEZER", _("FREEZER")
    GREEN = "GREEN", _("GREEN")
    TOASTING_MACHINE = "TOASTING_MACHINE", _("TOASTING MACHINE")
    GENERATORS = "GENERATORS", _("GENERATORS")
    ELECTRONICS = "ELECTRONICS", _("ELECTRONICS")
    REFRIGERATOR = "REFRIGERATOR", _("REFRIGERATOR")
    ACCESSORIES = "ACCESSORIES", _("ACCESSORIES")
    GAS_CYLINDER = "GAS_CYLINDER", _("GAS CYLINDER")
    POS_TERMINAL = "POS_TERMINAL", _("POS TERMINAL")
    MICROWAVE = "MICROWAVE", _("MICROWAVE")
    AIRCONDITION = "AIRCONDITION", _("AIRCONDITION")
    CASSEROLES = "CASSEROLES", _("CASSEROLES")
    BLENDER = "BLENDER", _("BLENDER")
    INFINIX = "INFINIX", _("INFINIX")
    ITEL = "ITEL", _("ITEL")
    TELEVISION = "TELEVISION", _("TELEVISION")
    WASHING_MACHINE = "WASHING_MACHINE", _("WASHING MACHINE")
    AIRFRYER = "AIRFRYER", _("AIRFRYER")
    HEALTH = "HEALTH", _("HEALTH")
    NOKIA = "NOKIA", _("NOKIA")
    GAS_COOKER = "GAS_COOKER", _("GAS COOKER")
    WASHINGMACHINE = "WASHINGMACHINE", _("WASHINGMACHINE")
    TECNO = "TECNO", _("TECNO")

