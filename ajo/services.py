import csv
import json
import os
import re
import uuid
from typing import Any, Dict

from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.core.exceptions import ValidationError
from django.core.files.uploadedfile import UploadedFile
from django.db import IntegrityError
from django.db import transaction as django_transaction
from django.utils import timezone
from django.utils.timezone import datetime, timedelta
from django.db import IntegrityError
from django.db.models import Q

from accounts.agency_banking import AgencyBankingClass
from accounts.models import (
    FailuresLog,
    IDVerificationDumps,
    IDVerificationSources,
    UserRecurrentChargeTable,
)
from loans.helpers.core_banking import CoreBankingManager
from payment.model_choices import PaymentMethod, PlanType, WalletTypes
from payment.models import Status, Transaction, TransactionFormType, WalletSystem
from payment.utils import Utility

from . import payment_actions
from .model_choices import (
    AccountFormType,
    AccountProvider,
    ProfileChangeStatus,
    RoscaGroupStatus,
    RoscaType,
    SavingsFrequency,
)
from .models import (
    AccountCreationsCheck,
    AjoSaving,
    AjoSepo,
    AjoUser,
    AjoUserWithdrawalAccount,
    BankAccountDetails,
    Card,
    Loan,
    Prefunding,
    ProductInformation,
    ProfileChangeRequest,
    RotationGroup,
    RotationGroupMember,
)
from .selectors import (
    AjoSavingsSelector,
    AjoUserSelector,
    BankAccountSelector,
    ProfileChangeSelector,
    RotationGroupMemberSelector,
    RotationGroupSelector,
)
from .third_party import LibertyBVN
from .utils.personal_utils import PersonalAjoUtils


def verify_onboarding_and_return(verify: bool, ajo_user: AjoUser) -> bool:
    """
    Takes the response from the verify_otp util and uses it to verify an ajo user
    if the user is already verified, it raises a ValueError

    Args:
        verify (bool): the boolean from the verify_otp util
        ajo_user (AjoUser): the ajo user the verification process is happening for

    Raises:
        ValueError: "this user is already verified"

    Returns:
        bool: True if user is successfully verified, else False
    """
    # if true
    # check if the user is already verified
    if ajo_user.onboarding_verified:
        return True
        # raise ValueError("this user is already verified")

    if verify:
        ajo_user.onboarding_verified = True
        ajo_user.save()
        return True

    else:
        return False


def verify_card_issuing_and_return(verify: bool, ajo_user: AjoUser) -> bool:
    # if a card has been issued already
    if ajo_user.card_issued:
        raise ValueError("a card has been issued to this user already")

    if verify:
        ajo_user.card_issued = True
        ajo_user.save()
        return True

    else:
        return False


class AjoUserService:
    def __init__(self, ajo_user: AjoUser) -> None:
        self.ajo_user = ajo_user

    def increment_total_saved(self, amount: float) -> AjoUser:
        """
        This increments the total money saved field

        Args:
            amount (float): the amount to increase it by

        Raises:
            ValueError: "amount should be more than 0"

        Returns:
            AjoUser: the updated ajo user instance
        """
        if amount <= 0:
            raise ValueError("amount should be more than 0")
        self.ajo_user.total_money_saved += amount
        self.ajo_user.save()
        return self.ajo_user

    def increment_total_withdrawn_in_ajo_user(self, amount: float) -> None:
        """
        Increase the total_amount_withdrawn and total_withdrawals for the Ajo user

        Raises:
            ValueError: "amount should be more than 0"

        Args:
            amount (float): amount to increment field by
        """
        if amount <= 0:
            raise ValueError("amount should be more than 0")
        self.ajo_user.total_money_withdrawn += amount
        self.ajo_user.total_withdrawals += 1
        self.ajo_user.save()
        return self.ajo_user

    @staticmethod
    def create_ajo_user_under_agent(
        user: AbstractUser,
        phone_number: str,
    ) -> AjoUser:
        """
        Creates an ajo user

        Args:
            user (AbstractUser): the user that wishes to onboard the ajo user
            phone_number (str): the phone number of the ajo user

        Raises:
            ValueError: "this ajo user already exists for this agent"

        Returns:
            AjoUser: the ajo user onboarded
        """
        try:
            ajo_user = AjoUser.objects.create(user=user, phone_number=phone_number)
            return ajo_user
        except IntegrityError:
            raise ValueError("this ajo user already exists for this agent")

    def save_image_for_ajo_user(self, image: UploadedFile) -> Dict[str, bool | str]:
        """
        This saves the image of the ajo user

        Args:
            ajo_user (AjoUser): Ajo user instance
            image (UploadedFile): the image to be uploaded for this user

        Returns:
            Dict: True/False based on if successful and a string of the URL of the image
        """
        ajo_user = self.ajo_user
        try:
            if ajo_user.image:
                ajo_user.image.delete()
            ajo_user.image = image
            ajo_user.save()

            return {"status": True, "image": ajo_user.image.url}
        except:
            return {"status": False, "image": None}

    def save_utility_bill_for_ajo_user(
        self, image: UploadedFile
    ) -> Dict[str, bool | str]:
        """
        This saves the utility bill image of the ajo user

        Args:
            ajo_user (AjoUser): Ajo user instance
            image (UploadedFile): the image to be uploaded for this user

        Returns:
            Dict: True/False based on if successful and a string of the URL of the image
        """
        ajo_user = self.ajo_user
        try:
            if ajo_user.verification_utility_bill:
                ajo_user.verification_utility_bill.delete()
            ajo_user.verification_utility_bill = image
            ajo_user.save()

            return {"status": True, "image": ajo_user.verification_utility_bill.url}
        except:
            return {"status": False, "image": None}

    def update_ajo_user_fields(self, validated_data: Dict[str, Any]) -> AjoUser:
        """
        Updates the Ajo User with the provided validated data
        To be used for "Fill Personal User details"

        Args:
            validated_data (Dict[str, Any]): A dictionary of fields to change and their respective values

        Returns:
            AjoUser: The updated Ajo user instance
        """
        for key, value in validated_data.items():
            setattr(self.ajo_user, key, value)

        # attempt to save the data
        self.ajo_user.save()

        return self.ajo_user

    @classmethod
    def verify_bvn_nin(cls, ajo_user: AjoUser) -> Dict[str, Any]:
        from loans.models import (
            IdentityRequestSource,
            VerificationType,
            YouVerifyRequest,
        )

        def verify_with_youverify(id_value, id_type, ajo_user):
            """Helper function to verify using YouVerify."""
            youverify = YouVerifyRequest.get_create_identity_result(
                id=id_value,
                id_type=id_type,
                source=IdentityRequestSource.EXTERNAL,
                ajo_user=ajo_user,
            )
            if youverify and youverify.is_valid:
                return youverify.response.get("dateOfBirth")
            return None

        data = {"status": False, "DOB": None, "verified": False}
        date_of_birth = None

        if ajo_user.bvn:
            # Attempt LibertyBVN verification
            bvn_verification = LibertyBVN.verify_bvn(bvn=ajo_user.bvn)
            if bvn_verification.get("status"):
                date_of_birth = (
                    bvn_verification.get("data", {})
                    .get("data", {})
                    .get("data", {})
                    .get("dateOfBirth")
                )
                IDVerificationDumps.objects.create(
                    ajo_user=ajo_user,
                    source=IDVerificationSources.LIBERTYBVN,
                    dump=json.dumps(bvn_verification),
                )
            else:
                # Fallback to YouVerify for BVN
                date_of_birth = verify_with_youverify(
                    id_value=ajo_user.bvn,
                    id_type=VerificationType.BVN,
                    ajo_user=ajo_user,
                )

        elif ajo_user.nin:
            # Verify using YouVerify for NIN
            date_of_birth = verify_with_youverify(
                id_value=ajo_user.nin,
                id_type=VerificationType.NIN,
                ajo_user=ajo_user,
            )

        if date_of_birth:
            data.update({"DOB": date_of_birth, "status": True, "verified": True})

        return data

    @classmethod
    def set_dob_from_bvn(cls, ajo_user: AjoUser):
        data = {"status": None, "message": ""}

        if ajo_user.dob:
            data["status"] = True
            data["message"] = "DOB already present"
            return data

        data = cls.verify_bvn_nin(ajo_user=ajo_user)

        if data.get("status"):
            if ajo_user.bvn:
                if not ajo_user.bvn_verified:
                    ajo_user.bvn_verified = True

            ajo_user.dob = data.get("DOB")
            ajo_user.save()
            data["status"] = True
            data["message"] = "DOB set"

        else:
            data["status"] = False
            data["message"] = "could not obtain date of birth"

        return data


class AjoSavingsService:
    def __init__(self, ajo_savings: AjoSaving) -> None:
        self.ajo_savings = ajo_savings

    def fund_plan(self, amount: float) -> AjoSaving:
        """
        This increases all the fields that hold balances
        in the ajo savings plan

        Args:
            amount (float): the amount funded

        Returns:
            AjoSaving: the updated ajo savings instance
        """
        # assign
        ajo_savings = self.ajo_savings

        # perform changes
        ajo_savings.plan_balance_before = ajo_savings.amount_saved
        ajo_savings.amount_saved += amount
        ajo_savings.plan_balance_after = ajo_savings.plan_balance_before + amount

        # save changes
        ajo_savings.save()

        self.ajo_savings = ajo_savings
        return self.ajo_savings

    def deduct_plan(self, amount: float) -> AjoSaving:
        """
        This reduces all the fields that hold balances by
        the aamount in the ajo savings plan

        Args:
            amount (float): the amount deducted

        Returns:
            AjoSaving: the updated ajo savings instance
        """
        # assign
        ajo_savings = self.ajo_savings

        # perform calculations
        ajo_savings.plan_balance_before = ajo_savings.amount_saved
        ajo_savings.amount_saved -= amount
        ajo_savings.plan_balance_after = ajo_savings.plan_balance_before - amount

        # save changes
        ajo_savings.save()

        self.ajo_savings = ajo_savings
        return self.ajo_savings

    @staticmethod
    def create_ajo_savings(data: dict) -> AjoSaving:
        """
        Create an Ajo Savings plan.

        The data dictionary should have the following:
        user, ajo_user(optional), plan_type(AjoSavingsPlanType), name,
        frequency, periodic_amount, duration.

        Args:
            data (dict): a dictionary of information to save

        Raises:
            ValueError:"an ajo savings plan with this name already exists"

        Returns:
            AjoSaving: the created instance of Ajo Savings
        """

        try:
            # Calculate the future date by adding the duration
            # frequency = data.get("frequency")
            # if frequency == SavingsFrequency.DAILY:
            #     last_day = calendar.monthrange(today.year, today.month)[1]
            #     maturity_date = date(today.year, today.month, last_day)
            # else:

            today = timezone.localdate()
            duration: int = data.get("duration")
            if not duration:
                raise ValueError("could not obtain a duration while creating this plan")

            maturity_date = today + timezone.timedelta(days=duration)

            data["maturity_date"] = maturity_date

            # set commissions to paid if loan savings
            if data.get("loan"):
                data["commission_paid"] = True

            # set commissions to paid if savings_type
            if data.get("savings_type"):
                data["commission_paid"] = True

            # create ajo plan
            data.pop("boosta_xtype", None)
            ajo_saving = AjoSaving.objects.create(**data)
        except IntegrityError:
            raise ValueError("an ajo savings plan with this name already exists")

        return ajo_saving

        # today = timezone.localdate()
        # duration: int = data.get("duration")
        # if not duration:
        #     raise ValueError("could not obtain a duration while creating this plan")

        # maturity_date = today + timezone.timedelta(days=duration)

        # data["maturity_date"] = maturity_date

        # # set commissions to paid if loan savings
        # if data.get("loan"):
        #     data["commission_paid"] = True

        # # set commissions to paid if savings_type
        # if data.get("savings_type"):
        #     data["commission_paid"] = True

        # # create ajo plan
        # ajo_saving = AjoSaving.objects.create(**data)

        # return ajo_saving


class CardService:
    def __init__(self, card: Card) -> None:
        self.card = card

    def update_card_fields(self, data: Dict[str, Any]) -> Card:
        """
        Updates the card with the provided valid data
        To be used for "Assign Card to Ajo User"

        Args:
            data (Dict[str, Any]): A dictionary of fields to change and their respective values

        Returns:
            Card: The updated Card instance
        """
        for key, value in data.items():
            setattr(self.card, key, value)

        # attempt to save the data
        self.card.save()

        return self.card


def create_card_for_agent(card_no: str, expiry_date):
    pass


class RotationGroupService:
    @classmethod
    def create_rotation_group(cls, data: dict) -> RotationGroup:
        """
        create a rotation group with a set of keys in a dictionary:
        user, name, amount, number_of_participants, frequency, duration, starting_date,
        end_date, admin_fee, auto_debit, fees, collection_amount, contribution_amount,
        rosca_type.

        Args:
            data (dict): validated_data as a dictionary

        Raises:
            ValueError: it can raise a ValueError necessary to be caught

        Returns:
            RotationGroup: A RotationGroup object
        """
        rotation_group = RotationGroup(**data)

        try:
            rotation_group.full_clean()
        except ValidationError as err:
            raise ValueError(err.error_dict)

        try:
            rotation_group.save()
        except IntegrityError:
            # handles the unique constraint error
            raise ValueError("a rotation group with this name already exists")

        return rotation_group

    @classmethod
    def update_rotation_group(
        cls, rotation_group_instance: RotationGroup, data: dict
    ) -> RotationGroup:
        """
        Updates a Rotation Group instance with the provided data.

        Args:
            rotation_group_instance (RotationGroup): The Rotation Group instance to update.
            daata (dict): The data containing the fields to update.

        Returns:
            RotationGroup: The updated RotationGroup instance.
        """
        members_count = rotation_group_instance.members.count()
        number_of_participants = data.get("number_of_participants", None)

        # perform validation
        if number_of_participants:
            if members_count > number_of_participants:
                raise ValueError(
                    "number of members greater than edited number of participants"
                )

        # attempt to save values
        for key, value in data.items():
            setattr(rotation_group_instance, key, value)

        rotation_group_instance.save()

        if rotation_group_instance.members.count() > 0:
            for member in rotation_group_instance.members.all():
                RotationGroupMemberService.update_rotation_group_member(
                    group=rotation_group_instance,
                    member=member,
                )

        return rotation_group_instance

    @classmethod
    def delete_rotation_group(
        cls,
        rotation_group: RotationGroup,
    ) -> bool:
        """
        Deletes a Rotation Group based on conditions.

        Args:
            rotation_group (RotationGroup): the rotation group to be deleted.

        Raises:
            ValueError: cannot delete rotation group because it has started.

        Returns:
            bool: True or False.
        """

        # if rotation_group.has_started:
        #     raise ValueError("cannot delete rotation group because it has started")

        for rotation_group_member in rotation_group.members.all():
            RotationGroupMemberService.remove_member(
                group_id=rotation_group.group_id,
                user=rotation_group_member.user,
            )

        rotation_group.delete()

        return True

    @classmethod
    def update_rotation_group_from_starting_date(
        cls,
        rotation_group: RotationGroup,
        starting_date: datetime | None = None,
    ) -> RotationGroup:
        if not starting_date:
            starting_date = timezone.localdate()

        generated_data = cls.clean_up_data_for_rotation_group(
            target_amount=rotation_group.collection_amount,
            participants=rotation_group.number_of_participants,
            frequency=rotation_group.frequency,
            starting_date=starting_date,
            auto_debit=rotation_group.auto_debit,
            user=rotation_group.user,
            name=rotation_group.name,
        )

        updated_rotation_group = cls.update_rotation_group(
            rotation_group_instance=rotation_group,
            data=generated_data,
        )

        for member in rotation_group.members.all():
            RotationGroupMemberService.update_rotation_group_member(
                group=rotation_group,
                member=member,
            )

        return updated_rotation_group

    @classmethod
    def clean_up_data_for_rotation_group(
        cls,
        target_amount: float,
        participants: int,
        frequency: SavingsFrequency,
        starting_date: datetime,
        auto_debit: bool,
        user: AbstractUser,
        name: str,
    ) -> Dict[str, Any]:
        generated_data = PersonalAjoUtils.generate_plan_information(
            target=target_amount,
            number_of_participants=participants,
            frequency=frequency,
            starting_date=starting_date,
        )

        generated_data["admin_fee"] = generated_data.get("agent_fee")
        generated_data["user"] = user
        generated_data["rosca_type"] = RoscaType.PERSONAL_ROSCA
        generated_data["name"] = name
        generated_data["auto_debit"] = auto_debit

        keys_to_remove = ["agent_fee", "company_fee", "converted_duration"]
        [generated_data.pop(key, None) for key in keys_to_remove]

        return generated_data

    @classmethod
    def start_rotation_group(
        cls,
        rotation_group: RotationGroup,
    ) -> RotationGroup:
        rotation_group.is_active = True
        rotation_group.status = RoscaGroupStatus.RUNNING
        rotation_group.save()


class LoanService:
    def __init__(self, loan: Loan) -> None:
        self.loan = loan

    def fund_loan(self, amount: float) -> Loan:
        """
        This pays back some or all of the loan taken

        Args:
            amount (float): the amount paid

        Returns:
            Loan: the updated loan instance
        """

        loan = self.loan

        # perform calculations
        loan.plan_balance_before = loan.repayment_amount - loan.amount_paid
        loan.amount_paid += amount
        loan.plan_balance_after = loan.plan_balance_before - amount

        # save changes
        loan.save()

        self.loan = loan.refresh_from_db()
        return loan

    @staticmethod
    def create_loan_plan(data: dict) -> Loan:
        """
        create a loan model instance

        Args:
            data (dict): neccessary Loan fields as a dictionary

        Raises:
            ValueError: it can raise a ValueError necessary to be caught

        Returns:
            Loan: the created Loan model instance
        """

        try:
            loan = Loan.objects.create(**data)

        except IntegrityError as err:
            return ValueError("there was a problem creating this loan")

        return loan

    @staticmethod
    def place_loan_in_wallet(
        loan_wallet: WalletSystem, amount: float, transaction: Transaction
    ) -> None:
        """
        Adds the loaned amount to the loan wallet balance

        Args:
            wallet (WalletSystem): the loan wallet
            amount (float): the amount loaned

        Returns:
            WalletSystem: the updated wallet
        """
        WalletSystem.fund_balance(
            wallet=loan_wallet, amount=amount, transaction_instance=transaction
        )
        return loan_wallet


class BankAccountService:
    @staticmethod
    def create_agent_virtual_account(
        user: AbstractUser,
        form_type: AccountFormType,
        new: bool = False,
    ) -> Dict[str, Any]:
        """
        Create a bank account for a user on the system

        Args:
            user (AbstractUser): the agent/user
            form_type (AccountFormType): the form type of the bank account
            new (bool, optional): a new bank account. Use this if it is a form_type != AJO_AGENT. Defaults to False.

        Returns:
            Dict[str, Any]: A status, message and data (BankAccountDetails) object.
        """
        agent_account_details = BankAccountDetails.objects.filter(
            user=user,
            ajo_user__isnull=True,
            form_type=form_type,
            is_active=True,
        )

        # agent_vfd_account_details = BankAccountDetails.objects.filter(user=user, bank_name__icontains="VFD").last()
        if agent_account_details.exists():
            return {
                "status": True,
                "message": "agent already has an account",
                "data": agent_account_details,
            }

        initial_payload, create_vfd_account_response = (
            AgencyBankingClass.create_agent_vfd_wallet(
                user=user,
                new=new,
            )
        )

        AccountCreationsCheck.objects.create(
            user=user,
            initial_payload=initial_payload,
            dump=create_vfd_account_response,
        )

        if not create_vfd_account_response.get("data", {}).get("status") == "success":
            return {
                "status": False,
                "message": "Agent VFD wallet not created",
            }

        nuban = (
            create_vfd_account_response.get("data").get("data").get("account_number")
        )
        acct_name = (
            create_vfd_account_response.get("data").get("data").get("account_name")
        )
        bank_name = create_vfd_account_response.get("data").get("data").get("bank_name")

        if BankAccountSelector.check_for_account_number(account_number=nuban):
            return {
                "status": False,
                "message": "this account number is already assigned to a form type on our system",
            }

        agent_account_details = BankAccountDetails.objects.create(
            user=user,
            account_number=nuban,
            account_name=acct_name,
            bank_name=bank_name,
            account_type=create_vfd_account_response.get("data")
            .get("data")
            .get("account_type"),
            bank_code=create_vfd_account_response.get("data")
            .get("data")
            .get("bank_code"),
            payload=create_vfd_account_response,
            form_type=form_type,
            consent=True,
            account_provider=AccountProvider.VFD,
            initial_payload=initial_payload,
        )

        return {
            "status": True,
            "message": "account details created successfully",
            "data": agent_account_details,
        }

    @classmethod
    def create_account_for_ajo_user(
        cls,
        ajo_user: AjoUser,
        acct_form_type: AccountFormType,
        bvn_present: bool,
        data: Dict[str, str],
    ) -> Dict[str, Any]:
        if not acct_form_type in [
            AccountFormType.AJO_SPENDING,
            AccountFormType.AJO_DIGITAL,
            AccountFormType.ONLENDING_MAIN,
        ]:
            return {
                "status": False,
                "message": "'acct_form_type' is not a valid AccountFormType",
            }

        accounts_query = BankAccountDetails.objects.filter(
            ajo_user=ajo_user,
            form_type=acct_form_type,
        )

        account_details = accounts_query.filter(is_active=True).last()

        if acct_form_type == AccountFormType.AJO_SPENDING:
            bank_type_choice = "SPEND"
            unique_ref_choice = "SPD"
            bank_type = "spending"
        elif acct_form_type == AccountFormType.ONLENDING_MAIN:
            bank_type_choice = "ONLENDING"
            unique_ref_choice = "ONL"
            bank_type = "onlending"
        else:
            bank_type_choice = "AJO"
            unique_ref_choice = "AJO"
            bank_type = "digital"

        if account_details:
            return {
                "status": True,
                "message": f"ajo user already has an account for {bank_type}",
                "data": account_details,
            }

        # NOTE: Create only wema for now
        # if bvn_present:
        #     (
        #         initial_payload,
        #         create_account_response,
        #     ) = AgencyBankingClass.create_cooperate_personal_bank_account_for_ajo_user(
        #         user=ajo_user.user,
        #         phone_number=data.get("phone_number"),
        #         bank_type=bank_type_choice,
        #         unique_ref_app=unique_ref_choice,
        #         first_name=data.get("first_name"),
        #         last_name=f"{data.get('last_name')} {bank_type_choice}",
        #         dob=data.get("dob"),
        #         address=data.get("address"),
        #         gender=data.get("gender"),
        #         bvn=data.get("bvn"),
        #     )

        # else:
        #     (
        #         initial_payload,
        #         create_account_response,
        #     ) = AgencyBankingClass.create_cooperate_personal_bank_account_for_ajo_user(
        #         user=ajo_user.user,
        #         phone_number=data.get("phone_number"),
        #         bank_type=bank_type_choice,
        #         unique_ref_app=unique_ref_choice,
        #         corporate_id=settings.LIBERTY_CORPORATE_ID,
        #     )

        # AccountCreationsCheck.objects.create(
        #     user=ajo_user.user,
        #     initial_payload=initial_payload,
        #     dump=create_account_response,
        # )

        # if create_account_response.get("status"):
        #     account_number = create_account_response.get("data").get("data").get("account_number")
        #     if BankAccountSelector.check_for_account_number(account_number=account_number):
        #         return {
        #             "status": False,
        #             "message": "this account number is already assigned to a form type on our system",
        #         }

        #     account_details_instance = BankAccountDetails.objects.create(
        #         user=ajo_user.user,
        #         ajo_user=ajo_user,
        #         account_name=create_account_response.get("data").get("data").get("account_name"),
        #         account_number=create_account_response.get("data").get("data").get("account_number"),
        #         bank_name=create_account_response.get("data").get("data").get("bank_name"),
        #         account_type=create_account_response.get("data").get("data").get("account_type"),
        #         bank_code=create_account_response.get("data").get("data").get("bank_code"),
        #         initial_payload=initial_payload,
        #         payload=json.dumps(create_account_response),
        #         form_type=getattr(AccountFormType, acct_form_type.upper()),
        #         consent=False,
        #     )

        #     return {
        #         "status": True,
        #         "message": "Account details created successfully",
        #         "data": account_details_instance,
        #     }
        # else:
        create_wema_funding_account_response = cls.create_wema_account(
            ajo_user=ajo_user,
            acct_form_type=acct_form_type,
            new_account=True,
            # previous_created_count=accounts_query.count(),
        )
        return create_wema_funding_account_response
        # return {
        #     "status": False,
        #     "message": "could not create account number for ajo user, contact support",
        # }

    @staticmethod
    def create_wema_account(
        ajo_user: AjoUser,
        acct_form_type: AccountFormType,
        new_account=False,
        retry_count=0,
    ) -> Dict[str, Any]:
        import random

        ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
        if acct_form_type in ["LOAN_RECOVERY"]:
            # wallet_type = "AJO_LOAN_REPAYMENT"
            wallet_type = "LOAN_RECOVERY"
        else:
            wallet_type = acct_form_type

        account_provider = AccountProvider.WEMA
        user_wallet = ajo_user_selector.get_any_ajo_user_wallet(wallet_type=wallet_type)

        filters = Q(ajo_user=ajo_user)  # Direct relationship

        if ajo_user.bvn:  # Ensure BVN is not null/empty
            filters |= Q(ajo_user__bvn=ajo_user.bvn) & ~Q(ajo_user__bvn="")

        if ajo_user.nin:  # Ensure NIN is not null/empty
            filters |= Q(ajo_user__nin=ajo_user.nin) & ~Q(ajo_user__nin="")

        if ajo_user.phone_number:  # Ensure phone_number is not null/empty
            filters |= Q(ajo_user__phone_number=ajo_user.phone_number) & ~Q(
                ajo_user__phone_number=""
            )

        wema_acct_details = BankAccountDetails.objects.filter(
            filters, account_provider=account_provider
        )

        lite_user = ajo_user.lite_user

        if wema_acct_details.exists():
            add_number = wema_acct_details.count()
            # add_number = random.randint(0, 1000)
            phone_number = f"{ajo_user.phone}-{add_number + 1}"
            email = f"{lite_user.email}-{add_number + 1}" if lite_user else ""
            bvn = f"{ajo_user.bvn}-{add_number + 1}" if ajo_user.bvn else ""
        else:
            phone_number = f"{ajo_user.phone}-1"
            email = f"{lite_user.email}-1" if lite_user else ""
            bvn = f"{ajo_user.bvn}-1" if ajo_user.bvn else ""

        wema_account_query_set = BankAccountDetails.objects.filter(
            ajo_user=ajo_user,
            form_type=acct_form_type,
            is_active=True,
            account_provider__icontains=account_provider,
        )
        if wema_account_query_set.exists():
            return {
                "status": True,
                "message": "Account Exist",
                "data": wema_account_query_set.last(),
            }
        else:
            request_payload = {
                "first_name": ajo_user.first_name,
                "last_name": ajo_user.last_name,
                "middle_name": ajo_user.alias,
                "email": email,
                "phone": phone_number,
                "bvn": bvn,
                "date_of_birth": str(ajo_user.dob),
            }
            try:
                create_wema_virtual_wallet = (
                    CoreBankingManager().create_multiple_account(**request_payload)
                )

            except ValueError as err:
                create_wema_virtual_wallet = {
                    "status_code": 500,
                    "message": str(err),
                }

            status_code = create_wema_virtual_wallet.get("status_code")

            if isinstance(create_wema_virtual_wallet, dict):
                create_wema_virtual_wallet["request_payload"] = request_payload

            success_status_code = [200, 201]
            if status_code in success_status_code:
                data = create_wema_virtual_wallet.get("data").get("account_details")
                account_number = data.get("account_number")
                acct_name = data.get("first_name") + " " + data.get("last_name")
                bank_code = data.get("bank_code")
                bank_name = data.get("bank_name")

                try:
                    # create bank account details on success response
                    account_details_instance = BankAccountDetails.objects.create(
                        user=ajo_user.user,
                        ajo_user=ajo_user,
                        user_wallet=user_wallet,
                        account_number=account_number,
                        account_name=acct_name,
                        bank_code=bank_code,
                        bank_name=bank_name,
                        account_provider=AccountProvider.WEMA,
                        account_type=acct_form_type,
                        consent=True,
                        form_type=acct_form_type,
                        payload=create_wema_virtual_wallet,
                        initial_payload=request_payload,
                    )

                    return {
                        "status": True,
                        "message": "Account details created successfully",
                        "data": account_details_instance,
                    }
                except IntegrityError as err:
                    create_wema_virtual_wallet["db_error"] = str(err)
                    FailuresLog.objects.create(
                        user=ajo_user.user,
                        ajo_user=ajo_user,
                        dump=create_wema_virtual_wallet,
                    )
                    return {
                        "status": False,
                        "message": "Bank account details not created",
                    }

            else:
                FailuresLog.objects.create(
                    user=ajo_user.user,
                    ajo_user=ajo_user,
                    dump=create_wema_virtual_wallet,
                )
                return {
                    "status": False,
                    "message": "Bank account details not created",
                }

    @classmethod
    def create_cash_connect_account_details(
        cls,
        ajo_user: AjoUser = None,
        borrower_info=None,
        wallet_form_type: AccountFormType = None,
    ) -> Dict[str, Any]:

        if not borrower_info:
            ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
            if not ajo_user.bvn:
                id_number = ajo_user.nin
                id_type = "NIN"
            else:
                id_number = ajo_user.bvn
                id_type = "BVN"
            dob = str(ajo_user.dob)

        else:
            ajo_user = borrower_info.borrower
            ajo_user_selector = AjoUserSelector(ajo_user=borrower_info.borrower)
            id_number = borrower_info.verification_number
            id_type = borrower_info.verification_type
            dob = str(borrower_info.date_of_birth)

        account_form_types = [
            wallet_form_type,
            AccountFormType.LOAN_RECOVERY,
            AccountFormType.AJO_SPENDING,
            AccountFormType.LOAN_REPAYMENT,
        ]
        result_list = []

        for acct_form_type in account_form_types:
            if not acct_form_type:
                continue

            cash_connect_acct_details = BankAccountDetails.objects.filter(
                ajo_user=ajo_user,
                form_type=acct_form_type,
                account_provider=AccountProvider.CASH_CONNECT,
            )

            user_wallet = ajo_user_selector.get_any_ajo_user_wallet(
                wallet_type=acct_form_type
            )
            if cash_connect_acct_details.exists():
                result = cash_connect_acct_details.values(
                    "account_number",
                    "account_name",
                    "bank_code",
                    "bank_name",
                    "account_type",
                    "account_provider",
                )[0]
                result_list.append(result)
                continue
            #### request create account from core banking

            request_create_account = (
                CoreBankingManager().create_virtual_account_cash_connect(
                    first_name=ajo_user.first_name,
                    last_name=ajo_user.last_name,
                    phone_number=ajo_user.phone,
                    id_number=id_number,
                    id_type=id_type,
                    dob=dob,
                )
            )
            ###################################################

            if request_create_account["status"] == "success":
                actual_response = request_create_account["response"]
                if actual_response.get("status") is True:

                    data = actual_response.get("data")
                    account_name = data.get("account_name")
                    account_number = data.get("account_number")
                    account_id = data.get("account_id")
                    bank_code = data.get("bank_code")
                    bank_name = data.get("bank_name")

                    try:
                        account_details_instance = BankAccountDetails.objects.create(
                            user=ajo_user.user,
                            ajo_user=ajo_user,
                            user_wallet=user_wallet,
                            account_number=account_number,
                            account_name=account_name,
                            bank_code=bank_code,
                            bank_name=bank_name,
                            account_type=acct_form_type,
                            consent=True,
                            account_provider=AccountProvider.CASH_CONNECT,
                            form_type=acct_form_type,
                            payload=request_create_account,
                            id_type=id_type,
                            initial_payload=request_create_account,
                        )

                        result = BankAccountDetails.objects.filter(
                            id=account_details_instance.id
                        ).values(
                            "account_number",
                            "account_name",
                            "bank_code",
                            "bank_name",
                            "account_type",
                            "account_provider",
                        )[
                            0
                        ]
                        result_list.append(result)
                        continue
                    except (Exception, IntegrityError) as err:
                        exception_error = {"exception_error": str(err)}
                        request_create_account["exception_error"] = exception_error
                        FailuresLog.objects.create(
                            user=ajo_user.user,
                            ajo_user=ajo_user,
                            dump=request_create_account,
                        )
                        result_list.append(request_create_account)
                        continue
                else:
                    FailuresLog.objects.create(
                        user=ajo_user.user,
                        ajo_user=ajo_user,
                        dump=request_create_account,
                    )
                    result_list.append(request_create_account)
                    continue
            else:

                FailuresLog.objects.create(
                    user=ajo_user.user, ajo_user=ajo_user, dump=request_create_account
                )
                result_list.append(request_create_account)
                continue

        return result_list


class PrefundingService:
    def __init__(self, prefunding: Prefunding) -> None:
        self.prefunding = prefunding

    @staticmethod
    def create_prefunding(data: dict) -> Prefunding:
        """
        This creates a prefunding instance

        Args:
            data (dict): the dictionary should contain the following keys: user, prefunding_amount, balance_left(optional)

        Returns:
            Prefunding: the created Prefunding model instance
        """

        try:
            prefunding = Prefunding.objects.create(**data)

        except IntegrityError as err:
            return ValueError("there was a problem creating this prefunding")

        return prefunding

    def payback_prefunding(self, amount: float) -> Prefunding:
        """
        Update the prefunding instance by paying back

        Args:
            amount (float): the amount to payback

        Returns:
            Prefunding: the updated prefunding instance
        """

        prefunding = self.prefunding

        # check if it has been paid back completely
        #####CHECKS######
        if prefunding.completed:
            raise ValueError("this prefunding has been completely been paid for")

        if prefunding.amount_paid >= prefunding.prefunding_amount:
            if not prefunding.completed:
                # set the metadata fields of the prefunding
                prefunding.completed = True
                prefunding.completed_at = timezone.localtime()
                prefunding.save()

            raise ValueError("this prefunding has been completely paid for")

        # perform calculations
        # Update the amount_paid
        prefunding.amount_paid += amount
        # Calculate the balance_left, ensuring it doesn't become negative
        prefunding.balance_left = max(
            0, prefunding.prefunding_amount - prefunding.amount_paid
        )

        # check if the payment is complete
        if (
            prefunding.amount_paid >= prefunding.prefunding_amount
            or prefunding.balance_left <= 0
        ):
            prefunding.completed = True
            prefunding.completed_at = timezone.localtime()

        # save the changes
        prefunding.save()

        self.prefunding = prefunding.refresh_from_db()

        return prefunding


class AjoUserWithdrawalAccountService:
    @staticmethod
    def set_withdrawal_account(data: dict) -> AjoUserWithdrawalAccount:
        """
        This creates or retrieves an AjoUserWithdrawalAccount

        Args:
            data (dict): the dictionary should contain the following keys:
                -   account_number
                -   account_name
                -   bank_code
                -   bank_name
                -   ajo_user

        Returns:
            AjoUserWithdrawalAccount: the created or retrieved model instance
        """

        withdrawal_account, created = AjoUserWithdrawalAccount.objects.get_or_create(
            ajo_user=data["ajo_user"], defaults=data
        )

        return withdrawal_account


class RotationGroupMemberService:
    @classmethod
    def join_group(
        cls,
        user: AbstractUser,
        group_id: str,
        position: int,
        payment_method: PaymentMethod | None = None,
        masked_pan: str | None = None,
        auth_code: str | None = None,
    ) -> None:
        group_selector = RotationGroupSelector(group_id=group_id)

        try:
            group = group_selector.get_rotation_group()
        except ValueError as err:
            raise ValueError(f"group with group_id {group_id} does not exist")

        # check if the person is already a member of this group
        if RotationGroupMember.objects.filter(group=group, user=user).exists():
            raise ValueError("this ajo user is already a member of this group")

        # check if the group has reached its maximum number of participants
        if group.number_of_participants == group.members.count():
            raise ValueError(
                "this group has reached its maximum number of participants"
            )

        # check if the given position is already taken
        if RotationGroupMemberSelector.position_in_group(group, position):
            raise ValueError(
                f"position {position} in group {group.group_id} is already taken"
            )

        # Check if the group has already started
        if group_selector.has_started():
            raise ValueError("this group has commenced, hence this user cannnot join.")

        # TODO: Uncomment the code below for production
        # check if the creator of the group wants to join the group
        # if user == group.user:
        #     raise ValueError("you can not become a member of a group you created")

        # check if the positon chosen is available
        if position not in group_selector.available_postions():
            raise ValueError("this position is not available for this group")

        # check if the given position is valid for this group
        if position not in range(1, group.number_of_participants + 1):
            raise ValueError(
                f"invalid position {position} for group with {group.number_of_participants} participants"
            )

        if (group.auto_debit) and (not payment_method):
            raise ValueError(f"please choose a payment method for joining the group")

        member = cls.create_rotation_group_member(
            group=group,
            user=user,
            position=position,
            payment_method=payment_method,
            masked_pan=masked_pan,
            auth_code=auth_code,
        )

    @classmethod
    def remove_member(
        cls,
        group_id: str,
        user: AbstractUser,
    ) -> str:
        group_selector = RotationGroupSelector(group_id=group_id)

        try:
            group = group_selector.get_rotation_group()
        except ValueError as err:
            raise ValueError(f"group with group_id {group_id} does not exist")

        if group_selector.has_started():
            raise ValueError(
                "this user cannot leave this group because it has commenced"
            )

        try:
            member = RotationGroupMember.objects.get(group=group, user=user)
            position = member.position
            member.delete()

        except RotationGroupMember.DoesNotExist:
            raise ValueError(f"this user, {user.email}, is not part of this group")

        if group.auto_debit:
            member_quotation_id = cls.set_member_quotation_id(
                quotation_id=group.quotation_id,
                position=position,
            )

            if Utility.recurrent_charging_instance_exists(
                user=user,
                plan_type=PlanType.ROTATIONGROUP,
                plan_quotation_id=member_quotation_id,
            ):
                Utility.retrieve_recurrent_charging_instance(
                    user=user,
                    plan_type=PlanType.ROTATIONGROUP,
                    plan_quotation_id=member_quotation_id,
                ).delete()

        return "member removed from group successfully"

    @classmethod
    def retrieve_rotation_group_member_instance(
        cls,
        user,
        rotation_group: RotationGroup,
    ) -> "RotationGroupMember":
        try:
            return RotationGroupMember.objects.get(user=user, group=rotation_group)
        except cls.DoesNotExist:
            return None

    @classmethod
    def create_rotation_group_member(
        cls,
        group: RotationGroup,
        user: AbstractUser,
        position: int,
        payment_method: PaymentMethod | None = None,
        masked_pan: str | None = None,
        auth_code: str | None = None,
    ) -> RotationGroupMember:
        positions_dates = RotationGroupSelector(
            group_id=group.group_id
        ).generate_day_intervals()

        collection_date = positions_dates.get(position)

        member = RotationGroupMember.objects.create(
            user=user,
            group=group,
            position=position,
            collection_date=collection_date,
        )

        if group.auto_debit:
            member_quotation_id = cls.set_member_quotation_id(
                quotation_id=group.quotation_id,
                position=position,
            )

            if not Utility.recurrent_charging_instance_exists(
                user=user,
                plan_type=PlanType.ROTATIONGROUP,
                plan_quotation_id=member_quotation_id,
            ):
                cls.set_auto_debit(
                    user=user,
                    group=group,
                    quotation_id=member_quotation_id,
                    payment_method=payment_method,
                    masked_pan=masked_pan,
                    auth_code=auth_code,
                )

        return member

    @classmethod
    def set_member_quotation_id(cls, quotation_id: str, position: int) -> str:
        return f"{quotation_id}_{position}"

    @classmethod
    def get_quotation_id_from_member(cls, member_quotation_id: str) -> str:
        # return member_quotation_id[:-2]
        # new implementation
        # Use regular expression to match the pattern before the underscore and numbers
        pattern = r"^(.*?)_\d+$"
        match = re.match(pattern, member_quotation_id)
        if match:
            return match.group(1)  # Return the part before the underscore
        else:
            return member_quotation_id  # Return original string if pattern not found

    @classmethod
    def update_rotation_group_member(
        cls,
        group: RotationGroup,
        member: RotationGroupMember,
        payment_method: PaymentMethod | None = None,
        masked_pan: str | None = None,
        auth_code: str | None = None,
        hour: str | None = None,
    ) -> RotationGroupMember:
        rotation_group_selector = RotationGroupSelector(group_id=group.group_id)
        position_dates = rotation_group_selector.generate_day_intervals()

        member.collection_date = position_dates.get(member.position)

        user = member.user

        member_quotation_id = cls.set_member_quotation_id(
            quotation_id=group.quotation_id,
            position=member.position,
        )

        if group.auto_debit:
            if not Utility.recurrent_charging_instance_exists(
                user=user,
                plan_type=PlanType.ROTATIONGROUP,
                plan_quotation_id=member_quotation_id,
            ):
                cls.set_auto_debit(
                    user=user,
                    group=group,
                    quotation_id=member_quotation_id,
                    payment_method=payment_method,
                    masked_pan=masked_pan,
                    auth_code=auth_code,
                )

            # retrieve the instance
            else:
                auto_debit_instance = UserRecurrentChargeTable.objects.get(
                    user=user,
                    plan_type=PlanType.ROTATIONGROUP,
                    plan_quotation_id=member_quotation_id,
                )

                data = {}

                if payment_method:
                    if not payment_method == auto_debit_instance.payment_method:
                        if payment_method == PaymentMethod.DEBIT_CARD:
                            if not masked_pan and auth_code:
                                raise ValueError(
                                    "insert masked pan and auth code as arguments"
                                )
                            data["masked_pan"] = masked_pan
                            data["auth_code"] = auth_code
                            data["payment_method"] = payment_method

                        else:
                            data["payment_method"] = payment_method

                else:
                    if not auto_debit_instance.payment_method:
                        data["payment_method"] = PaymentMethod.WALLET

                next_date = rotation_group_selector.calculate_next_payment_day()
                if next_date != auto_debit_instance.next_run_time.date():
                    if next_date:
                        data["next_run_time"] = datetime.combine(
                            next_date,
                            datetime.min.time(),
                        ) + timedelta(hours=cls.get_next_hour())

                    else:
                        # group has ended based on starting date
                        next_date = None

                data["next_charge_amount"] = (
                    rotation_group_selector.get_rotation_group().contribution_amount
                )

                cls.update_auto_debit(
                    auto_debit_instance=auto_debit_instance,
                    data=data,
                )
        else:
            if Utility.recurrent_charging_instance_exists(
                user=user,
                plan_type=PlanType.ROTATIONGROUP,
                plan_quotation_id=member_quotation_id,
            ):
                Utility.retrieve_recurrent_charging_instance(
                    user=user,
                    plan_type=PlanType.ROTATIONGROUP,
                    plan_quotation_id=member_quotation_id,
                ).delete()

        member.save()

        return member

    @classmethod
    def get_next_hour(cls) -> int:
        current_time = timezone.localtime()
        current_hour = current_time.hour
        current_minute = current_time.minute

        if current_minute >= 45:
            next_hour = current_hour + 2
        else:
            next_hour = current_hour + 1

        return next_hour

    @classmethod
    def set_auto_debit(
        cls,
        user: AbstractUser,
        group: RotationGroup,
        quotation_id: str,
        payment_method: PaymentMethod | None = None,
        masked_pan: str | None = None,
        auth_code: str | None = None,
    ) -> None:
        next_date = RotationGroupSelector(
            group_id=group.group_id
        ).calculate_next_payment_day()
        if next_date:
            next_run_time = datetime.combine(
                next_date, datetime.min.time()
            ) + timedelta(hours=cls.get_next_hour())
        else:
            next_run_time = None

        Utility.create_recurrent_charging_for_plan(
            user=user,
            plan_type=PlanType.ROTATIONGROUP,
            plan_instance=group,
            payment_method=payment_method if payment_method else PaymentMethod.WALLET,
            next_run_time=next_run_time,
            hour=12,
            quotation_id=quotation_id,
            masked_pan=masked_pan,
            auth_code=auth_code,
        )

    @classmethod
    def update_auto_debit(
        cls, auto_debit_instance: UserRecurrentChargeTable, data: Dict[str, Any]
    ) -> None:
        """
        Update the auto debit instance fields with necessary information.

        The data dictionary can contain the following keys:
            1. hour(int)
            2. payment_method(PaymentMethod)
            3. next_run_time(datetime)
            4. masked_pan(str)
            5. auth_code(str)


        Args:
            auto_debit_instance (UserRecurrentChargeTable): the auto debit table instance
            data (Dict[str, Any]): a dictionary of fields you wish to modify
        """
        for key, value in data.items():
            setattr(auto_debit_instance, key, value)

        auto_debit_instance.save()

    @classmethod
    def set_member_collection_date(
        cls,
        member: RotationGroupMember,
    ) -> RotationGroupMember:
        """
        Set the collection date for the member of a rotation group

        Args:
            member (RotationGroupMember): the member instance.

        Returns:
            RotationGroupMember: the updated member instance.
        """
        positions_dates = RotationGroupSelector(
            group_id=member.group.group_id
        ).generate_day_intervals()

        member.collection_date = positions_dates.get(member.position)

        member.save()

        return member

    @classmethod
    def update_paid_positions(
        cls,
        member: RotationGroupMember,
        paid_position: int,
    ) -> None:
        """
        This is a method to update the paid_positions field.

        #### NOTE:
        This does not save the the instance, so ensure to call `.save()`

        Args:
            paid_position (int): the paid position.
            member (RotationGroupMember): the rotation group member.
        """
        if not member.paid_positions:
            member.paid_positions = str(paid_position)
        else:
            member.paid_positions += f",{paid_position}"

        # if not self.paid_positions:
        #     # If paid_positions is empty, set it to the paid_position.
        #     self.paid_positions = str(paid_position)
        # else:
        #     # Split the paid_positions string into a list of integers.
        #     positions = [int(pos) for pos in self.paid_positions.split(",") if pos]

        #     # Check if paid_position is not already in the list of positions.
        #     if paid_position not in positions:
        #         # Append paid_position to the list of positions and update the paid_positions field.
        #         positions.append(paid_position)
        #         self.paid_positions = ",".join(map(str, positions))


class ProfileChangeService:
    @classmethod
    @django_transaction.atomic
    def create_paid_change_request(cls, ajo_user: AjoUser) -> ProfileChangeRequest:
        """
        Create a paid change request with a session_id

        Args:
            ajo_user (AjoUser): the ajo user that this is being created for.

        Returns:
            ProfileChangeRequest: the created empty paid request
        """
        profile_selector = ProfileChangeSelector(ajo_user=ajo_user)

        if profile_selector.get_all_unused_paid_requests().exists():
            raise ValueError("you have unused paid requests")

        new_request = ProfileChangeRequest.objects.create(
            session_id=str(uuid.uuid4()),
            ajo_user=ajo_user,
            status=None,
            payload={},
        )

        return new_request

    @classmethod
    def close_failed_requests(
        cls,
        ajo_user: AjoUser,
    ) -> None:
        active_profile_requests = ProfileChangeRequest.objects.filter(
            ajo_user=ajo_user, is_active=True
        )

        if active_profile_requests.exists():
            active_failed_profile_requests = active_profile_requests.filter(
                status=ProfileChangeStatus.FAILED
            )
            active_failed_profile_requests.update(is_active=False)

    @classmethod
    def update_profile_change_request(
        cls,
        session_id: str,
        ajo_user: AjoUser,
        data: dict,
        failure_reason: str | None = None,
    ) -> ProfileChangeRequest:
        """
        Update the fields of an existing profile change request

        Args:
            session_id (str): the session ID of the change request
            ajo_user (AjoUser): the ajo user that it belongs to.

        Returns:
            ProfileChangeRequest: the updated instance.
        """
        try:
            change_request = ProfileChangeRequest.objects.get(
                session_id=session_id,
                ajo_user=ajo_user,
            )
        except ProfileChangeRequest.DoesNotExist as err:
            return ValueError("this change request does not exist")

        change_request.status = ProfileChangeStatus.PROCESSING
        change_request.payload = data

        if failure_reason:
            change_request.status = ProfileChangeStatus.FAILED
            change_request.failure_reason = failure_reason

        change_request.save()

    @classmethod
    @django_transaction.atomic
    def create_ajo_profile_change_request(
        cls,
        ajo_user: AjoUser,
        data: dict,
        failure_reason: str | None = None,
    ) -> Dict[str, str]:
        """
        Create a Profile Request if there is no active request

        Args:
            ajo_user (AjoUser): the ajo user
            data (dict): the data of the request

        Returns:
            Dict[str, str]: the information about the profile request
        """
        active_profile_requests = ProfileChangeRequest.objects.filter(
            ajo_user=ajo_user, is_active=True
        )

        if active_profile_requests.exists():
            for profile_request in active_profile_requests:
                if not profile_request.status == ProfileChangeStatus.FAILED:
                    ProfileChangeRequest.objects.create(
                        ajo_user=ajo_user,
                        payload=json.dumps(data),
                        failure_reason="there is an active profile change request already",
                        status=ProfileChangeStatus.FAILED,
                        is_active=False,
                    )

                    return {
                        "status": False,
                        "message": "there is an active profile change request",
                    }

                else:
                    profile_request.is_active = False
                    profile_request.save()

        if failure_reason:
            ProfileChangeRequest.objects.create(
                ajo_user=ajo_user,
                payload=json.dumps(data),
                failure_reason=failure_reason,
                status=ProfileChangeStatus.FAILED,
                is_active=False,
            )

            return {
                "status": False,
                "message": failure_reason,
            }

        profile_change = ProfileChangeRequest.objects.create(
            ajo_user=ajo_user,
            payload=json.dumps(data),
        )

        return {
            "status": True,
            "message": "change profile request created successfully",
            "data": profile_change,
        }


def add_phone_number_to_investigate_to_csv(
    phone_number: str,
    csv_file_path: str | None = None,
) -> None:
    if not csv_file_path:
        csv_file_path = "ajo/management/commands/phone_numbers_to_investigate.csv"

    # check if the file exists, if not, create it
    if not os.path.isfile(csv_file_path):
        with open(csv_file_path, "w", newline="") as file:
            writer = csv.writer(file)
            # write the header if the file is newly created
            writer.writerow(["Phone Number"])

    with open(csv_file_path, "a", newline="") as file:
        writer = csv.writer(file)
        writer.writerow([phone_number])


@django_transaction.atomic
def remit_not_withdrawn_closed_plans_to_spend(ajo_user: AjoUser):
    ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)

    # check if the amount in active plans is present in wallet first:
    ajo_user_wallet = ajo_user_selector.get_ajo_user_wallet()
    # print("ajo_user_wallet_balance: ", ajo_user_wallet.available_balance)

    # define a default failed text
    failed_text = f"opertation cancelled for {ajo_user.phone_number}:"

    # print(
    #     "aggregate_in_active_savings", AjoSavingsSelector.aggregate_amount_saved_in_active_ajo_plans(ajo_user=ajo_user)
    # )
    # print(
    #     "aggregate_in_not_withdrawn_closed",
    #     AjoSavingsSelector.aggregate_amount_saved_in_closed_not_withdrawn_plans(ajo_user=ajo_user),
    # )
    # if there is only money for active savings
    if (
        not ajo_user_wallet.available_balance
        >= AjoSavingsSelector.aggregate_amount_saved_in_active_ajo_plans(
            ajo_user=ajo_user
        )
    ):
        raise ValueError(
            f"{failed_text} only aggregate active savings money or less is present in the wallet"
        )

    # obtain not-withdrawn closed plans
    not_withdrawn_closed_plans = ajo_user_selector.get_ajo_user_ajo_savings(
        is_active=False
    ).filter(withdrawn=False)

    # amount in not withdrawn closed plans
    not_withdrawn_closed_plans_sum = (
        AjoSavingsSelector.aggregate_amount_saved_in_closed_not_withdrawn_plans(
            ajo_user=ajo_user
        )
    )

    # if there is no money to cashout in closed plans
    if not_withdrawn_closed_plans_sum <= 0:
        raise ValueError(
            f"{failed_text} no money to cashout from not withdrawn closed plans"
        )

    # if the closed plans balance does not reach the amount aggregated, add phone number to plans to investigate
    if (
        ajo_user_wallet.available_balance
        - AjoSavingsSelector.aggregate_amount_saved_in_active_ajo_plans(
            ajo_user=ajo_user
        )
    ) >= not_withdrawn_closed_plans_sum:
        add_phone_number_to_investigate_to_csv(phone_number=f"{ajo_user.phone_number}")

    # loop through closed not withdrawn plan

    plans_altered = []

    for ajo_plan in not_withdrawn_closed_plans:
        ajo_user_wallet.refresh_from_db()

        closed_plan_balance = (
            ajo_user_wallet.available_balance
            - AjoSavingsSelector.aggregate_amount_saved_in_active_ajo_plans(
                ajo_user=ajo_user
            )
        )

        if not closed_plan_balance > 0:
            break

        if ajo_plan.amount_saved > closed_plan_balance:
            continue

        if ajo_plan.amount_saved <= 0:
            continue

        payment_actions.move_mature_funds_to_spending_for_ajo_user(ajo_plan=ajo_plan)
        plans_altered.append(ajo_plan.id)

    return f"operation successful for {ajo_user.phone_number}: list of plans withdrawn {plans_altered}"


class BankDepositService:

    @classmethod
    def create_fund_transaction(
        cls,
        user: AbstractUser,
        amount: float,
        wallet_type: WalletTypes,
        transaction_form_type: str,
        unique_reference: str,
        description: str,
        ajo_user: AjoUser = None,
    ) -> Transaction:
        if wallet_type == WalletTypes.ONLENDING_MAIN:
            plan_type = PlanType.ONLENDING
        else:
            plan_type = PlanType.AJO
        fund_transaction = payment_actions.TransactionService.create_deposit_by_virtual_account_in_ajo_transaction(
            user=user,
            amount=amount,
            wallet_type=wallet_type,
            transaction_form_type=transaction_form_type,
            unique_reference=unique_reference,
            description=description,
            status=Status.PENDING,
            ajo_user=ajo_user,
            plan_type=plan_type,
        )

        return fund_transaction

    @classmethod
    @django_transaction.atomic
    def settle_transaction_in_wallet(
        cls,
        user: AbstractUser,
        amount: float,
        wallet: WalletSystem,
        transaction_form_type: TransactionFormType,
        deposit_name: str,
        liberty_reference: str,
        description: str,
        ajo_user: AjoUser = None,
    ) -> None:
        unique_reference = f"{liberty_reference}_{deposit_name}_deposit"

        transaction = cls.create_fund_transaction(
            user=user,
            amount=amount,
            wallet_type=wallet.wallet_type,
            transaction_form_type=transaction_form_type,
            unique_reference=unique_reference,
            description=description,
            ajo_user=ajo_user,
        )

        payment_actions.fund_wallet_and_update_transaction(
            wallet=wallet,
            amount=amount,
            transaction_instance=transaction,
            unique_reference=unique_reference,
            ajo_user=ajo_user,
        )


class ProductInformationService:

    @classmethod
    def create_or_update_product_info(cls, data: Dict[str, Any]) -> ProductInformation:
        """
        The data dictionary should have the following keys:
            - category
            - item
            - item_id
            - stock_price
            - selling_price

        Args:
            data (Dict[str, Any]): the data to be utilized

        Returns:
            ProductInformation: Created/Updated Product Info
        """

        item_id: str = data.get("item_id")

        try:
            # Try to update the product if it exists, otherwise create a new one
            product, created = ProductInformation.objects.update_or_create(
                item_id=item_id, defaults=data
            )

            return product

        except IntegrityError:
            raise ValueError("An error occurred while processing the product data.")


class AjoSepoService:
    MIN_MEMBERS = 6
    MAX_MEMBERS = 12

    def __init__(self, group: AjoSepo) -> None:
        self.group = group

    def add_savings_to_group(self, savings: AjoSaving) -> AjoSaving:
        """
        Add savings to a group

        Args:
            savings (AjoSaving): the savings to add

        Raises:
            ValueError: this savings is already in a group

        Returns:
            AjoSaving: Updated savings group
        """
        if savings.group:
            raise ValueError("this savings is already in a group")

        savings.group = self.group
        savings.save()
        savings.refresh_from_db()
        return savings

    def remove_savings_from_group(self, savings: AjoSaving) -> AjoSaving:
        """
        Remove Savings from a group

        Args:
            savings (AjoSaving): the savings to remove

        Raises:
            ValueError: this savings does not belong to this group

        Returns:
            AjoSaving: the updated savings
        """
        if not savings.group == self.group:
            raise ValueError("this savings does not belong to this group")

        savings.group = None
        savings.save()
        savings.refresh_from_db()
        return savings

    @classmethod
    def create_ajosepo_group(cls, data: dict) -> AjoSepo:
        """
        Create an AjoSepo group.

        The data dictionary should have the following:
        user, leader(optional), participants, name,
        meeting_days, loan_tenure.

        Args:
            data (dict): a dictionary of information to save

        Raises:
            ValueError:"an ajo savings plan with this name already exists"

        Returns:
            AjoSepo: the created instance of AjoSepo
        """
        try:
            participants: int = data.get("participants")
            if participants < cls.MIN_MEMBERS:
                raise ValueError(
                    f"number of participants is less than {cls.MIN_MEMBERS}"
                )
            data["min_members"] = participants

            # create ajosepo group
            ajosepo = AjoSepo.objects.create(**data)
        except IntegrityError:
            raise ValueError("an error occurred when creating this group")

        return ajosepo
