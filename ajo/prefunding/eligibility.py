from django.contrib.auth.models import AbstractUser

from ..selectors import PrefundingSelector


def prefunding_eligibility(user: AbstractUser) -> bool:
    """
    This function checks if an agent is eligible for prefunding

    Args:
        user (settings.AUTH_USER_MODEL): the user that the eligibility should be checked

    Returns:
        bool: True/False if the user is eligible
    """

    # call the prefunding selector
    prefunding_selector = PrefundingSelector(user=user)

    # obtain the prefunding wallet
    prefunding_wallet = prefunding_selector.get_prefunding_wallet()

    # check if there is any outstanding balance
    if prefunding_selector.check_if_there_is_outstanding_in_prefunding_wallet():
        return False

    # check if there is a previous prefunding instance
    if prefunding_selector.check_if_there_is_any_active_prefunding_instance():
        return False

    return True
