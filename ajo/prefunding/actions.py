from typing import Any, Dict, List

from django.contrib.auth.models import AbstractUser
from django.db import transaction as django_transaction
from django.utils import timezone

from accounts.models import ConstantTable
from payment.model_choices import (
    Status,
    TransactionFormType,
    TransactionSource,
    TransactionTypeCreditOrDebitChoices,
    WalletTypes,
)
from payment.models import WalletSystem
from payment.services import TransactionService

from .. import payment_actions
from ..model_choices import SavingsFrequency
from ..models import AjoSaving, Prefunding
from ..selectors import AjoAgentSelector, AjoUserSelector, PrefundingSelector
from ..services import PrefundingService


class PrefundingActions:
    def __init__(self, user: AbstractUser) -> None:
        self.user = user

    def __get_prefunding_wallet(self) -> WalletSystem:
        """
        This is a private method to obtain the prefunding wallet

        Returns:
            WalletSystem: the prefunding wallet
        """
        return WalletSystem.get_or_create_wallet(
            user=self.user,
            wallet_type=WalletTypes.AJO_PREFUNDING,
        )

    def deposit_prefunding_constant_into_wallet(self, prefunding_amount: float | None = None):
        """
        This deposits the prefunding amount into the prefunding
        wallet

        Raises:
            ValueError: encountered an error: {error message}
        """
        try:
            if prefunding_amount:
                prefunding_constant_amount = prefunding_amount
            else:
                # obtain the constants table
                constants_table = ConstantTable.get_constant_table_instance()

                # obtain the prefunding constant
                prefunding_constant_amount = constants_table.prefunding_amount

            # obtain the prefunding wallet
            prefunding_wallet = self.__get_prefunding_wallet()

            with django_transaction.atomic():
                # create a transaction instance
                prefunding_transaction = TransactionService.prefunding_deposit_transaction(
                    user=self.user,
                    amount=prefunding_constant_amount,
                    transaction_description=f"{prefunding_constant_amount} was just deposited into your prefunding wallet",
                )

                # call the function to fund the prefunding account
                fund = WalletSystem.fund_prefunding_balances(
                    wallet=prefunding_wallet,
                    amount=prefunding_constant_amount,
                    transaction_instance=prefunding_transaction,
                )

                # prefunding
                prefunding_selector = PrefundingSelector(user=self.user)

                # update the transaction fields

                prefunding_transaction.status = Status.SUCCESS
                prefunding_transaction.transaction_date_completed = timezone.localtime()
                prefunding_transaction.wallet_balance_before = fund.get("balance_before")
                prefunding_transaction.wallet_balance_after = fund.get("balance_after")
                prefunding_transaction.plan_balance_before = fund.get("hold_balance_before")
                prefunding_transaction.plan_balance_after = fund.get("hold_balance_after")
                prefunding_transaction.credit_balance = prefunding_selector.available_balance_in_prefunding_wallet()
                prefunding_transaction.due_balance = (
                    prefunding_selector.how_much_prefunding_has_been_used_against_hold_balance()
                )
                # save the changes
                prefunding_transaction.save()

        except Exception as err:
            raise ValueError(f"encountered an error: {str(err)}")

    def deduct_prefunding_wallet_and_fund_ajo_plan_position(
        self,
        amount: float,
        ajo_savings: AjoSaving,
        request_data: dict,
    ) -> None:
        from ..payment_actions import fund_ajo_savings_position

        """
        Deduct the prefunding wallet to pay for an ajo savings plan

        Args:
            amount (float): the amount to be deducted from the prefunding wallet
        """

        # obtain the prefunding wallet
        prefunding_wallet = self.__get_prefunding_wallet()

        with django_transaction.atomic():
            ####Handle the debiting of the prefunding wallet

            # create the transaction instance
            deduction_transaction = TransactionService.deduct_prefunding_wallet_transaction(
                user=self.user,
                amount=amount,
                transaction_description=f"{amount} was deducted from your prefunding wallet to pay for {ajo_savings.name} ajo plan.",
            )

            # deduct the amount from wallet
            deduct = WalletSystem.deduct_balance(
                wallet=prefunding_wallet,
                amount=amount,
                transaction_instance=deduction_transaction,
            )

            prefunding_selector = PrefundingSelector(user=self.user)

            # update the fields
            deduction_transaction.status = Status.SUCCESS
            deduction_transaction.transaction_date_completed = timezone.localtime()
            deduction_transaction.wallet_balance_before = deduct.get("balance_before")
            deduction_transaction.wallet_balance_after = deduct.get("balance_after")
            deduction_transaction.credit_balance = prefunding_selector.available_balance_in_prefunding_wallet()
            deduction_transaction.due_balance = (
                prefunding_selector.how_much_prefunding_has_been_used_against_hold_balance()
            )
            deduction_transaction.save()

            ###Handle the funding of the ajo savings plan
            fund_ajo_savings_position(
                user=self.user,
                ajo_user=ajo_savings.ajo_user,
                ajo_savings=ajo_savings,
                amount=amount,
                request_data=request_data,
            )

        return deduction_transaction

    def remit_money_to_repay_prefunding(
        self,
        amount: float,
        unique_reference: str | None = None,
        transaction_source: TransactionSource = TransactionSource.BANK_ACCOUNT,
    ) -> Dict[str, float]:
        """
        this method is for paying back the hold balance in the prefunding wallet

        Args:
            amount (float): the amount passed to pay back
            unique_reference(str): the unique_reference for the payment

        Returns:
            Dict[str, float]: {
            "total_amount": the amount passed to use to pay back,
            "hold_balance": this holds how much is left to be paid back from prefunding wallet,
            "amount_left_after_repayment": the amount left from total_amount after paying back hold_balance,
            "remaining_amount_to_payback": the amount to payback after using the total amount to pay part of the hold_balance,
        }
        """
        # obtain the prefunding wallet
        prefunding_wallet = self.__get_prefunding_wallet()

        # check how much is left in the prefunding wallet
        hold_balance = prefunding_wallet.hold_balance
        print("hold_balance_in_func", hold_balance)

        # define the dictionary of data to return
        balances = {
            "total_amount": amount,
            "hold_balance": hold_balance,
            "amount_left_after_repayment": 0,
            "remaining_amount_to_payback": 0,
        }

        if amount > hold_balance:
            remaining_amount = amount - hold_balance
            balances["amount_left_after_repayment"] = remaining_amount
            repayment_amount = hold_balance

        if hold_balance > amount:
            remaining_amount = hold_balance - amount
            balances["remaining_amount_to_payback"] = remaining_amount
            repayment_amount = amount

        if hold_balance == amount:
            repayment_amount = hold_balance

        # create a transaction instance to indicate that money is being
        # paid back
        try:
            with django_transaction.atomic():
                payback_transaction = TransactionService.payback_prefunding_transaction(
                    user=self.user,
                    amount=repayment_amount,
                    transaction_description=f"{repayment_amount} was remitted to pay back for prefunding",
                    unique_reference=unique_reference,
                    transaction_form_type=TransactionFormType.PREFUNDING_REPAYMENT,
                    transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
                    transaction_source=transaction_source,
                )

                # deduct the amount from the hold balance
                deduct = WalletSystem.deduct_prefunding_hold_balance(
                    wallet=prefunding_wallet,
                    amount=repayment_amount,
                    transaction_instance=payback_transaction,
                    unique_reference=unique_reference,
                )

                prefunding_selector = PrefundingSelector(user=self.user)

                # update the transaction fields
                payback_transaction.status = Status.SUCCESS
                payback_transaction.transaction_date_completed = timezone.localtime()
                payback_transaction.plan_balance_before = deduct.get("hold_balance_before")
                payback_transaction.plan_balance_after = deduct.get("hold_balance_after")
                payback_transaction.credit_balance = prefunding_selector.available_balance_in_prefunding_wallet()
                payback_transaction.due_balance = (
                    prefunding_selector.how_much_prefunding_has_been_used_against_hold_balance()
                )
                # save the changes
                payback_transaction.save()

        except Exception as err:
            payback_transaction = None
            # raise ValueError(f"{str(err)}")

        return payback_transaction

    @django_transaction.atomic
    def settle_prefunding_outstanding_from_agent_wallet(self) -> None:
        from ..payment_actions import check_if_agent_can_pay, debit_agent

        """
        This ensures that the prefunding debt or due balance is cleared with money from the
        agent's wallet.

        Args:
            user (settings.AUTH_USER_MODEL): the user with the prefunding debt.

        Raises:
            ValueError: there is no outstanding to pay.
            ValueError: this agent does not have enough funds in agent wallet to settle debt.
        """
        prefunding_selector = PrefundingSelector(user=self.user)

        outstanding = prefunding_selector.how_much_prefunding_has_been_used_against_hold_balance()

        if outstanding <= 0.0:
            raise ValueError("there is no outstanding to pay")

        agent_wallet = AjoAgentSelector(user=self.user).get_agent_ajo_wallet()

        if not check_if_agent_can_pay(agent_wallet=agent_wallet, amount=outstanding):
            raise ValueError("this agent does not have enough funds in agent wallet to settle debt")

        debit_agent(
            amount=outstanding,
            user=self.user,
            wallet=agent_wallet,
            payload=None,
            transaction_description=f"{outstanding} was taken out to pay for prefunding due balance",
        )

        self.remit_money_to_repay_prefunding(amount=outstanding, transaction_source=TransactionSource.WALLET)

        prefunding_instance = prefunding_selector.get_the_latest_prefunding_instance()

        PrefundingService(prefunding=prefunding_instance).payback_prefunding(
            amount=outstanding,
        )


def check_if_prefunding_wallet_can_be_used(ajo_savings: AjoSaving) -> bool:
    """
    This checks if the prefunding wallet can be used for saving for an ajo savings plan.
    The checks are:
        1. The ajo plan must be a new plan
        2. The ajo plan must be a monthly plan

    Args:
        ajo_savings (AjoSaving): The Ajo savings plan to check

    Returns:
        bool: True/False if the prefunding can be used for the plan.
    """

    # if (not ajo_savings.is_activated) and (ajo_savings.amount_saved <= 0):
    #     return True

    # else:
    #     return False

    if (not ajo_savings.is_activated) and (ajo_savings.amount_saved <= 0):
        if ajo_savings.frequency == SavingsFrequency.DAILY:
            return True

    return False


def check_if_initial_prefunding_can_be_given_to_agent(user: AbstractUser) -> bool:
    """
    This happens in steps:
        1. Is this the first prefunding the agent is getting?
        2. No outstanding exists in the prefunding wallet?

    if the answers to questions above is Yes, then initial prefunding can be
    given to the agent, else, No.

    Args:
        user (settings.AUTH_USER_MODEL): the user being checked

    Returns:
        bool: True if you can give else False
    """

    if Prefunding.objects.filter(user=user).exists():
        return False

    if PrefundingSelector(user=user).check_if_there_is_outstanding_in_prefunding_wallet():
        return False

    return True
