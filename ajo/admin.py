import ast
import json
from typing import Dict

from django.conf import settings
from django.contrib import admin
from django.db import IntegrityError
from django.db.models import Sum
from django.db.models.query import QuerySet
from django.utils.html import format_html
from import_export import resources
from django.contrib.auth import get_user_model

from import_export.admin import ImportExportModelAdmin
from django.db import IntegrityError
from accounts.agency_banking import AgencyBankingClass, bnpl_agent_login
from accounts.models import ActionPermission, FailuresLog

from loans.helpers.apis.agency_banking import LibertyPayMgr
from loans.helpers.core_banking import CoreBankingManager
from loans.models import LoanEligibility
from loans.services import DiscrepancyFixes
from payment.admin import payment_actions
from payment.model_choices import (
    DisbursementProviderType,
    PlanType,
    Status,
    TransactionFormType,
    TransactionTypeCreditOrDebitChoices,
    WalletTypes,
)
from payment.models import Transaction, WalletSystem
from payment.services import TransactionService
from payment.utils import is_past_month_year
from savings.admin import set_readonly_fields

from . import payment_actions
from .models import *
from .selectors import AjoAgentSelector, AjoUserSelector, BankAccountSelector
from .services import AjoUserWithdrawalAccountService, BankAccountService
from .utils.general_utils import obtain_the_current_site

####################################################################################
# RESOURCES
User = get_user_model()


class AjoUserResource(resources.ModelResource):
    class Meta:
        model = AjoUser


class CardResource(resources.ModelResource):
    class Meta:
        model = Card


class RotationGroupResource(resources.ModelResource):
    class Meta:
        model = RotationGroup


class RotationGroupMemberResource(resources.ModelResource):
    class Meta:
        model = RotationGroupMember


class CardRequestsResource(resources.ModelResource):
    class Meta:
        model = CardRequests


class AjoSavingResource(resources.ModelResource):
    class Meta:
        model = AjoSaving


class AjoSavingsPlanTypeResource(resources.ModelResource):
    class Meta:
        model = AjoSavingsPlanType


class LoanResource(resources.ModelResource):
    class Meta:
        model = Loan


class BankAccountDetailsResource(resources.ModelResource):
    class Meta:
        model = BankAccountDetails


class RawFundingDataResource(resources.ModelResource):
    class Meta:
        model = RawFundingData


class PrefundingResource(resources.ModelResource):
    class Meta:
        model = Prefunding


class AccountCreationsCheckResource(resources.ModelResource):
    class Meta:
        model = AccountCreationsCheck


class AjoTradeCategoryResource(resources.ModelResource):
    class Meta:
        model = AjoTradeCategory


class AjoTradeSubCategoryResource(resources.ModelResource):
    class Meta:
        model = AjoTradeSubCategory


class RoscaPaymentBreakDownResource(resources.ModelResource):
    class Meta:
        model = RoscaPaymentBreakDown


class BranchResource(resources.ModelResource):
    class Meta:
        model = Branch


class RoscaPaymentBreakDownResource(resources.ModelResource):
    class Meta:
        model = RoscaPaymentBreakDown


class BranchResource(resources.ModelResource):
    class Meta:
        model = Branch


class AjoUserWithdrawalAccountResource(resources.ModelResource):
    class Meta:
        model = AjoUserWithdrawalAccount


class ProfileChangeRequestResource(resources.ModelResource):
    class Meta:
        model = ProfileChangeRequest


class SupervisorResource(resources.ModelResource):
    class Meta:
        model = Supervisor


class ProductInformationResource(resources.ModelResource):

    class Meta:
        model = ProductInformation


class AgencyDumpsResource(resources.ModelResource):

    class Meta:
        model = AgencyDumps


class AjoSepoResource(resources.ModelResource):

    class Meta:
        model = AjoSepo


class BusinessSuiteResource(resources.ModelResource):

    class Meta:
        model = BusinessSuite


####################################################################################
# RESOURCE ADMINS


class AjoUserResourceAdmin(ImportExportModelAdmin):
    resource_class = AjoUserResource
    search_fields = [
        "user__email",
        "phone_number",
        "first_name",
        "last_name",
        "alias",
        "bvn",
    ]
    readonly_fields = set_readonly_fields(
        "first_name",
        "last_name",
        "alias",
        "gender",
        "marital_status",
        "state",
        "lga",
        "address",
        "trade",
        "trade_location",
        "bvn",
        "nin",
    )

    list_filter = (
        "onboarding_verified",
        "need_card",
        "card_assigned",
        "card_issued",
        "bvn_verified",
        "agent_bvn_used",
        "onboarding_complete",
        "charge_verification",
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def move_money_from_digital_to_spend(self, request, queryset: QuerySet[AjoUser]):
        response: Dict[str, str] = {}

        for query_instance in queryset:
            response_key = f"{query_instance.phone_number}-{query_instance.first_name}"
            try:
                digital_wallet_balance = AjoUserSelector(
                    ajo_user=query_instance
                ).get_digital_wallet_balance()
                payment_actions.move_money_from_ajo_user_digital_to_spend(
                    ajo_user=query_instance,
                    amount=digital_wallet_balance,
                )
                response[response_key] = "money transferred successfully."

            except Exception as err:
                response[response_key] = str(err)

        self.message_user(request, str(response))

    def create_wema_spend_account_details(self, request, queryset: QuerySet[AjoUser]):
        message = ""

        for query_instance in queryset:
            ajo_user_selector = AjoUserSelector(ajo_user=query_instance)

            wema_acct_details = BankAccountDetails.objects.filter(
                ajo_user=query_instance, account_provider=AccountProvider.WEMA
            )

            if wema_acct_details.exists():
                add_number = wema_acct_details.count()
                phone_number = f"{query_instance.phone}-{add_number + 1}"
                bvn = (
                    f"{query_instance.bvn}-{add_number + 1}"
                    if query_instance.bvn
                    else ""
                )
            else:
                phone_number = f"{query_instance.phone}-1"
                bvn = f"{query_instance.bvn}-1" if query_instance.bvn else ""

            wema_spend_wallet = wema_acct_details.filter(
                form_type=AccountFormType.AJO_SPENDING
            )

            if not wema_spend_wallet.exists():

                request_payload = {
                    "first_name": query_instance.first_name,
                    "last_name": query_instance.last_name,
                    "middle_name": query_instance.alias,
                    "email": "",
                    "phone": phone_number,
                    "bvn": bvn,
                    "date_of_birth": str(query_instance.dob),
                }

                create_virtual_spend_wallet = CoreBankingManager().create_wema_wallet(
                    **request_payload
                )

                print(create_virtual_spend_wallet)

                status_code = create_virtual_spend_wallet.get("status_code")
                response = create_virtual_spend_wallet.get("response")
                success_code = [200, 201]
                if status_code in success_code:
                    data = response.get("data").get("account_details")
                    account_number = data.get("account_number")
                    acct_name = data.get("first_name") + " " + data.get("last_name")
                    bank_code = data.get("bank_code")
                    bank_name = data.get("bank_name")

                    # create bank account details on success response
                    BankAccountDetails.objects.create(
                        user=query_instance.user,
                        ajo_user=query_instance,
                        account_number=account_number,
                        account_name=acct_name,
                        bank_code=bank_code,
                        bank_name=bank_name,
                        account_type="Spend account",
                        consent=True,
                        account_provider=AccountProvider.WEMA,
                        form_type=AccountFormType.AJO_SPENDING,
                        payload=create_virtual_spend_wallet,
                        initial_payload=request_payload,
                    )
                    ajo_user_spend_wallet = ajo_user_selector.get_any_ajo_user_wallet(
                        wallet_type=WalletTypes.AJO_SPENDING
                    )
                    ajo_user_spend_wallet.wallet_number = account_number
                    ajo_user_spend_wallet.save()
                    message = "success"
                else:

                    FailuresLog.objects.create(
                        user=query_instance.user,
                        ajo_user=query_instance,
                        dump=create_virtual_spend_wallet,
                    )
                    message = f"unable to create account. status code: ({status_code})"
            else:
                message = "Wema spend account details exist"
        self.message_user(request, str(message))

    def create_wema_repayment_account_details(
        self, request, queryset: QuerySet[AjoUser]
    ):
        message = ""

        for query_instance in queryset:

            get_repayment_wallet = WalletSystem.objects.filter(
                user=query_instance.user,
                onboarded_user=query_instance,
                wallet_number__isnull=False,
                wallet_type=WalletTypes.AJO_LOAN_REPAYMENT,
            ).exists()
            ajouser = query_instance
            if not get_repayment_wallet:

                wema_acct_details = BankAccountDetails.objects.filter(
                    ajo_user=ajouser, account_provider=AccountProvider.WEMA
                )
                if wema_acct_details.exists():
                    add_number = wema_acct_details.count()
                    phone_number = f"{ajouser.phone}-{add_number + 1}"
                    bvn = f"{ajouser.bvn}-{add_number + 1}" if ajouser.bvn else ""
                else:
                    phone_number = f"{ajouser.phone}-1"
                    bvn = f"{ajouser.bvn}-1" if ajouser.bvn else ""

                repayment_account = BankAccountDetails.objects.filter(
                    ajo_user=ajouser, form_type=AccountFormType.LOAN_REPAYMENT
                )
                if not repayment_account.exists():

                    ajo_user_selector = AjoUserSelector(ajo_user=query_instance)

                    ajo_user_repayment_wallet = (
                        ajo_user_selector.get_any_ajo_user_wallet(
                            wallet_type=WalletTypes.AJO_LOAN_REPAYMENT
                        )
                    )

                    request_payload = {
                        "first_name": query_instance.first_name,
                        "last_name": query_instance.last_name,
                        "middle_name": query_instance.alias,
                        "email": "",
                        "phone": phone_number,
                        "bvn": bvn,
                        "date_of_birth": str(query_instance.dob),
                    }
                    try:
                        create_virtual_repayment_wallet = (
                            CoreBankingManager().create_multiple_account(
                                **request_payload
                            )
                        )
                        # create_virtual_repayment_wallet = (
                        #     CoreBankingManager().create_wema_wallet(**request_payload)
                        # )

                    except ValueError as err:
                        create_virtual_repayment_wallet = {
                            "status_code": 500,
                            "message": str(err),
                        }

                    status_code = create_virtual_repayment_wallet.get("status_code")

                    if isinstance(create_virtual_repayment_wallet, dict):
                        create_virtual_repayment_wallet["request_payload"] = (
                            request_payload
                        )

                    success_status_code = [200, 201]
                    if status_code in success_status_code:
                        data = create_virtual_repayment_wallet.get("data").get(
                            "account_details"
                        )
                        account_number = data.get("account_number")
                        acct_name = data.get("first_name") + " " + data.get("last_name")
                        bank_code = data.get("bank_code")
                        bank_name = data.get("bank_name")

                        try:
                            # create bank account details on success response
                            BankAccountDetails.objects.create(
                                user=query_instance.user,
                                ajo_user=query_instance,
                                account_number=account_number,
                                account_name=acct_name,
                                bank_code=bank_code,
                                bank_name=bank_name,
                                account_provider=AccountProvider.WEMA,
                                account_type="Repayment",
                                consent=True,
                                form_type=AccountFormType.LOAN_REPAYMENT,
                                payload=create_virtual_repayment_wallet,
                                initial_payload=request_payload,
                            )

                            ajo_user_repayment_wallet.wallet_number = account_number
                            ajo_user_repayment_wallet.save()
                            message = "SUCCESS"
                        except IntegrityError as e:
                            FailuresLog.objects.create(
                                user=query_instance.user,
                                ajo_user=query_instance,
                                dump=create_virtual_repayment_wallet,
                            )
                            message = str(e)

                    else:
                        FailuresLog.objects.create(
                            user=query_instance.user,
                            ajo_user=query_instance,
                            dump=create_virtual_repayment_wallet,
                        )
                        message = "FAILED"
                else:
                    message = "WALLET/ACCOUNT EXIST"

            else:
                message = "WALLET/ACCOUNT EXIST"

            self.message_user(request, str(message))

    def create_wema_recovery_account_details(
        self, request, queryset: QuerySet[AjoUser]
    ):
        message = ""

        for query_instance in queryset:

            message = BankAccountService.create_wema_account(
                ajo_user=query_instance,
                acct_form_type=AccountFormType.LOAN_RECOVERY,
            )

            self.message_user(request, str(message))

    def update_ajo_user_address_with_address_log(
        self, request, queryset: QuerySet[AjoUser]
    ):
        message = "invalid action"

        for query_instance in queryset:
            message = query_instance.update_address_with_address_verification_log()

        self.message_user(request, str(message))

    #########################################################

    def create_cash_connect_wallet(self, request, queryset: QuerySet[AjoUser]):
        message = "invalid action"

        for instance in queryset:
            message = BankAccountService.create_cash_connect_account_details(
                ajo_user=instance
            )

        self.message_user(request, str(message))

    def get_create_user_account_from_libertypay(
        self, request, queryset: QuerySet[AjoUser]
    ):
        message = "invalid action"

        for instance in queryset:
            agent = instance.user
            agency_banking_handler = LibertyPayMgr(config=settings)
            get_user_info = agency_banking_handler.get_agency_banking_users(
                id=agent.customer_user_id
            )
            user_info = get_user_info.get("response")[0]

            vfd_bankcodes = ["090110", "999999"]
            account_number = user_info.get("account_number")
            bank_code = user_info.get("bank_code")
            first_name = user_info.get("first_name")
            last_name = user_info.get("last_name")
            bank_code = "090110" if bank_code == "999999" else bank_code
            AjoUserWithdrawalAccountService.set_withdrawal_account(
                data={
                    "account_number": account_number,
                    "account_name": f"{first_name} {last_name}",
                    "user_id": agent.id,
                    "bank_code": bank_code,
                    "bank_name": (
                        "VFD Microfinance Bank" if bank_code in vfd_bankcodes else ""
                    ),
                    "ajo_user": instance,
                }
            )
            message = get_user_info

        self.message_user(request, str(message))

    move_money_from_digital_to_spend.short_description = (
        "AJO: transfer digital wallet balance to spend wallet"
    )
    move_money_from_digital_to_spend.allow_tags = True

    create_wema_spend_account_details.short_description = (
        "ACCT DETAILS: Create Wema Spend Account Details"
    )
    create_wema_spend_account_details.allow_tags = True

    create_wema_repayment_account_details.short_description = (
        "ACCT DETAILS: Create Wema Repayment Account Details"
    )
    create_wema_repayment_account_details.allow_tags = True

    update_ajo_user_address_with_address_log.short_description = (
        "Address: Update Verified Address With Address Log"
    )
    update_ajo_user_address_with_address_log.allow_tags = True

    create_cash_connect_wallet.short_description = (
        "Cash Connect: Create Repayment wallet"
    )
    create_cash_connect_wallet.allow_tags = True

    get_create_user_account_from_libertypay.short_description = (
        "Get or create: Account from agency banking"
    )
    get_create_user_account_from_libertypay.allow_tags = True

    create_wema_recovery_account_details.short_description = (
        "Create Wema Recovery Account/wallet"
    )
    create_wema_recovery_account_details.allow_tags = True

    actions = [
        move_money_from_digital_to_spend,
        create_wema_spend_account_details,
        create_wema_repayment_account_details,
        update_ajo_user_address_with_address_log,
        create_cash_connect_wallet,
        get_create_user_account_from_libertypay,
        create_wema_recovery_account_details,
    ]


class CardResourceAdmin(ImportExportModelAdmin):
    resource_class = CardResource
    search_fields = [
        "user__email",
        "card_number",
        "bvn",
        "phone_number",
    ]
    readonly_fields = set_readonly_fields(
        "card_number",
        "expiry_date",
        "cvv",
        "card_pin",
        "card_type",
        "full_name",
        "bvn",
        "phone_number",
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RotationGroupResourceAdmin(ImportExportModelAdmin):
    resource_class = RotationGroupResource
    search_fields = [
        "user__email",
        "group_id",
        "name",
        "frequency",
        "quotation_id",
    ]
    readonly_fields = set_readonly_fields(
        "amount",
        "number_of_participants",
        "duration",
        "admin_fee",
        "collection_amount",
        "total_amount_saved",
        "balance_before",
        "balance_after",
    )
    list_filter = [
        "status",
        "rosca_type",
        "collection_type",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RotationGroupMemberResourceAdmin(ImportExportModelAdmin):
    resource_class = RotationGroupMemberResource
    search_fields = [
        "user__email",
        "group__group_id",
        "group__quotation_id",
        "position",
    ]
    readonly_fields = set_readonly_fields(
        "position",
        "total_amount_contributed",
        "balance_before",
        "balance_after",
    )
    list_filter = [
        "withdrawn",
        "collected",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CardRequestsResourceAdmin(ImportExportModelAdmin):
    def retry_request(self, request, queryset):
        from accounts.agency_banking import AgencyBankingClass

        send_card_request_data = []

        for query in queryset:
            if query.ajo_user:
                pass
            else:
                try:
                    send_request_for_card = AgencyBankingClass.agent_card_request(
                        reference=str(query.reference)
                    )

                    send_card_request_data.append(send_request_for_card)

                except Exception as err:
                    send_card_request_data.append(
                        {
                            "error": "603",
                            "status": False,
                            "reference": query.reference,
                            "message": str(err),
                        }
                    )

        self.message_user(request, f"{send_card_request_data}")

    autocomplete_fields = ["user"]
    resource_class = CardRequestsResource
    search_fields = [
        "user__email",
        "state",
        "lga",
        "address_type",
    ]
    readonly_fields = set_readonly_fields(
        "amount",
        "quantity",
        "card_id",
    )

    actions = [retry_request]

    retry_request.short_description = "AJO: Retry Failed Card Requests"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AjoSavingResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["plan_type"]
    resource_class = AjoSavingResource
    search_fields = [
        "user__email",
        "ajo_user__phone_number",
        "name",
        "quotation_id",
        "plan_type__name",
        "is_active",
        "completed",
        "completed_at",
        "maturity_date",
        "group__group_id",
    ]
    list_filter = [
        "is_activated",
        "completed",
        "savings_type",
        "loan",
        "is_active",
        "is_loan_repayment",
        "savings_type",
        "must_belong_to_group",
        "completed_at",
        ("created_at", admin.DateFieldListFilter),
    ]

    date_hierarchy = "created_at"
    # readonly_fields = set_readonly_fields(
    #     "periodic_amount",
    #     "amount_saved",
    #     "frequency_paid",
    #     "expected_amount",
    #     "plan_balance_before",
    #     "plan_balance_after",
    #     "maturity_date",
    # )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def cashout_ajo_plan_to_agent(self, request, queryset: QuerySet[AjoSaving]):
        response: Dict[str, str] = {}
        for query_instance in queryset:
            response_key = query_instance.user.email
            amount = query_instance.amount_saved

            if query_instance.frequency == SavingsFrequency.DAILY:
                if query_instance.lock and is_past_month_year(
                    target_month=query_instance.created_at.month,
                    target_year=query_instance.created_at.year,
                ):
                    response[response_key] = f"{query_instance.id} is a locked plan"
                    continue
            else:
                if query_instance.lock and (
                    query_instance.maturity_date > timezone.localdate()
                ):
                    response[response_key] = f"{query_instance.id} is a locked plan"
                    continue

            if query_instance.withdrawn:
                response[response_key] = f"{query_instance.id} is a withdrawn plan"
                continue

            if amount <= 0:
                response[response_key] = f"{query_instance.id} has no money to withdraw"
                continue

            if (
                amount
                > AjoUserSelector(
                    ajo_user=query_instance.ajo_user
                ).get_ajo_user_wallet_balance()
            ):
                response[response_key] = (
                    f"{query_instance.id} does not have enough funds in wallet"
                )
                continue

            try:
                payment_actions.cashout_from_ajo_plan(
                    ajo_savings_id=query_instance.id,
                    amount=amount,
                    user_id=query_instance.user.id,
                )

                query_instance.is_active = False
                query_instance.withdrawn = True
                query_instance.save()

                response[response_key] = (
                    f"{query_instance.id} has been cashed out successfully"
                )

            except Exception as err:
                response[response_key] = (
                    f"{query_instance.id} encountered an error: {str(err)}"
                )
                continue

        self.message_user(request, str(response))

    def create_repayment_record_for_savings(
        self, request, queryset: QuerySet[AjoSaving]
    ):
        response: Dict[str, str] = {}
        from loans.models import AjoLoanRepayment
        from payment.model_choices import TransactionFormType
        from payment.models import Transaction

        for query_instance in queryset:
            ajo_user = query_instance.ajo_user
            response_key = query_instance.user.email
            quotation_id = query_instance.quotation_id
            if query_instance.loan is False:
                response[response_key] = f"{query_instance.id} is not a loan savings."
                continue

            if not quotation_id:
                response[response_key] = (
                    f"{query_instance.id} does not have a quotation ID"
                )
                continue

            transaction_qs_with_quotation_id = Transaction.objects.filter(
                quotation_id=quotation_id,
                transaction_form_type=TransactionFormType.WALLET_DEPOSIT,
                unique_reference__isnull=True,
            )

            if not transaction_qs_with_quotation_id.exists():
                response[response_key] = (
                    f"{query_instance.id} does not have a transaction record"
                )
                continue

            # total amount saved
            total_amount_saved = (
                transaction_qs_with_quotation_id.aggregate(total_paid=Sum("amount"))[
                    "total_paid"
                ]
                or 0
            )

            # check if savings holds the wallet
            # ajouser wallet
            ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
            ajo_user_savings_wallet = ajo_user_selector.get_ajo_user_wallet()
            savings_avialable_balance = ajo_user_savings_wallet.available_balance

            if total_amount_saved > savings_avialable_balance:
                response[response_key] = (
                    f"total amount saved from savings {query_instance.id} is greater than saving balance {savings_avialable_balance}"
                )
                continue

            try:
                process_savings_loan_repayment = (
                    AjoLoanRepayment.process_savings_loan_repayment(
                        wallet_instance=ajo_user_savings_wallet,
                        is_loan_repayment_savings_instance=query_instance,
                        amount_to_deduct=total_amount_saved,
                    )
                )
                processed_message = process_savings_loan_repayment.get("message")

                response[response_key] = f"{processed_message}"
            except Exception as err:
                response[response_key] = f"error encountered: {err}"
                continue
        self.message_user(request, str(response))

    def repay_commissions_for_plan_into_spend(
        self, request, queryset: QuerySet[AjoSaving]
    ):
        response = {}
        for query_instance in queryset:
            response_key = query_instance.ajo_user.phone_number
            amount = query_instance.commission_amount

            if query_instance.is_active or not query_instance.is_activated:
                response[response_key] = (
                    "error: this plan is either active or not activated"
                )
                continue

            try:
                DiscrepancyFixes.refund_commissions_taken_from_plan_into_spend(
                    plan=query_instance
                )
                response[response_key] = (
                    f"commissions has been refunded into spend wallet"
                )

            except Exception as err:
                response[response_key] = f"{query_instance.id} encountered error: {err}"
                continue

        self.message_user(request, str(response))

    def liquidate_savings_to_spend(self, request, queryset: QuerySet[AjoSaving]):
        response = {}
        for query_instance in queryset:
            response_key = query_instance.ajo_user.phone_number

            try:
                response[response_key] = DiscrepancyFixes.liquidate_ajo_savings(
                    plan=query_instance
                )
            except Exception as err:
                response[response_key] = f"Hit Exception: {err}"
                continue

        self.message_user(request, str(response))

    def get_savings_eligibility_status(self, request, queryset: QuerySet[AjoSaving]):
        message = "Invalid action"
        for query_instance in queryset:
            boosta_savings = ["BOOSTA", "BOOSTA_2X", "BOOSTA_2X_MINI"]
            if query_instance.savings_type == SavingsType.BNPL:
                message = LoanEligibility.bnpl_eligibility(savings_id=query_instance.id)
            elif query_instance.savings_type in boosta_savings:
                message = LoanEligibility.boosta_eligibility(
                    savings_id=query_instance.id
                )
            else:
                check_result = LoanEligibility.get_staff_agent_loan_eligibility_5x_rule(
                    savings=query_instance
                )
                main_check_result = check_result.get("result", [])

                if main_check_result:
                    eligibility_data = main_check_result[0]
                    is_eligible = eligibility_data.get("is_eligible")
                    result = eligibility_data.get("result")
                    amount = result.get("amount")
                    message = f"eligible: {is_eligible} - amount: {amount}"

                else:
                    message = f"eligible: {False} - amount: 0.00"

        self.message_user(request, str(message))

    def update_ajo_savings_type(self, request, queryset: QuerySet[AjoSaving]):
        response: Dict[str, str] = {}
        for instance in queryset:
            ajosaving_qs = AjoSaving.objects.filter(savings_type=SavingsType.AJO)
            queryset_count = ajosaving_qs.count()
            ajosaving_qs.update(savings_type=None)
            response = f"count: {queryset_count}"

        self.message_user(request, str(response))

    def set_group_to_null(self, request, queryset: QuerySet[AjoSaving]):
        message = "Invalid action"
        for instance in queryset:
            if settings.ENVIRONMENT == "development":
                instance.group = None
                instance.savings_type = None
                instance.frequency = SavingsFrequency.DAILY
                instance.save()
                message = "success"
            else:
                message = f"cannot perform action on {settings.ENVIRONMENT}"
                break

        self.message_user(request, str(message))

    def get_bnpl_refundable_amt(self, request, queryset: QuerySet[AjoSaving]):
        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:

            for instance in queryset:
                const = ConstantTable.get_constant_table_instance()
                # pprint(const.staff_loans_config)
                # admin_cap_amount = const.max_loan_amount
                # profit_margin = const.staff_loans_config.get("profit_margin", 15) / 100
                bnpl_interest = const.staff_loans_config.get("bnpl_interest", 20) / 100
                bnpl_deposit_percentage = (
                    const.staff_loans_config.get("bnpl_deposit_percentage", 40) / 100
                )

                product_info = instance.product_info

                ajo_user = instance.ajo_user
                product_info = instance.product_info
                # get stock price
                purchase_price = product_info.stock_price
                selling_price = product_info.selling_price

                # print(bnpl_interest, "\n\n")

                # expected instance
                bnpl_price = (selling_price * bnpl_interest) + selling_price
                forty_percent_bnpl_amount = bnpl_price * bnpl_deposit_percentage
                quotation_id = instance.quotation_id
                # get credit Transactions
                plan_transaction = Transaction.objects.filter(
                    wallet_type=WalletTypes.AJO_USER,
                    quotation_id=quotation_id,
                )
                total_funding = (
                    plan_transaction.filter(
                        transaction_form_type=TransactionFormType.WALLET_DEPOSIT,
                        transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
                    ).aggregate(total_amount=Sum("amount"))["total_amount"]
                    or 0
                )

                loan_charge_fee = (
                    plan_transaction.filter(
                        transaction_form_type=TransactionFormType.AJO_LOAN_CHARGE_FEE,
                        transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
                    ).aggregate(total_amount=Sum("amount"))["total_amount"]
                    or 0
                )

                refundable = total_funding - (
                    loan_charge_fee + forty_percent_bnpl_amount
                )
                message = f"Amount Refundable {refundable}"

        self.message_user(request, message)

    def bnp_refund_excess_savings(self, request, queryset: QuerySet[AjoSaving]):
        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:

            for instance in queryset:
                const = ConstantTable.get_constant_table_instance()
                # pprint(const.staff_loans_config)
                # admin_cap_amount = const.max_loan_amount
                # profit_margin = const.staff_loans_config.get("profit_margin", 15) / 100
                bnpl_interest = const.staff_loans_config.get("bnpl_interest", 20) / 100
                bnpl_deposit_percentage = (
                    const.staff_loans_config.get("bnpl_deposit_percentage", 40) / 100
                )

                product_info = instance.product_info

                ajo_user = instance.ajo_user
                product_info = instance.product_info
                # get stock price
                purchase_price = product_info.stock_price
                selling_price = product_info.selling_price

                # print(bnpl_interest, "\n\n")

                # expected instance
                bnpl_price = (selling_price * bnpl_interest) + selling_price
                forty_percent_bnpl_amount = bnpl_price * bnpl_deposit_percentage
                quotation_id = instance.quotation_id
                # get credit Transactions
                plan_transaction = Transaction.objects.filter(
                    wallet_type=WalletTypes.AJO_USER,
                    quotation_id=quotation_id,
                )
                total_funding = (
                    plan_transaction.filter(
                        transaction_form_type=TransactionFormType.WALLET_DEPOSIT,
                        transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
                    ).aggregate(total_amount=Sum("amount"))["total_amount"]
                    or 0
                )

                loan_charge_fee = (
                    plan_transaction.filter(
                        transaction_form_type=TransactionFormType.AJO_LOAN_CHARGE_FEE,
                        transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
                    ).aggregate(total_amount=Sum("amount"))["total_amount"]
                    or 0
                )

                refundable = total_funding - (
                    loan_charge_fee + forty_percent_bnpl_amount
                )
                user = ajo_user.user
                agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()
                unique_reference = f"bnpl-excessdpst-rfnd-{quotation_id}"

                refund_transaction_queryset = Transaction.objects.filter(
                    unique_reference=unique_reference,
                    transaction_form_type=TransactionFormType.WALLET_DEPOSIT,
                    transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
                )
                if refund_transaction_queryset.exists():
                    message = "Refund already exists."
                    continue
                # print(refundable, "\n\n")
                # print(forty_percent_bnpl_amount, "\n\n", total_funding, loan_charge_fee)
                if refundable > 0:
                    savings_user_email = settings.AGENCY_BANKING_USEREMAIL
                    savings_user = User.objects.filter(email=savings_user_email).last()

                    if not savings_user:
                        message = f"Invalid admin user email {savings_user_email}"
                    savings_phone_number = savings_user.user_phone

                    bnpl_agent_token = bnpl_agent_login(token_not_valid=True).get(
                        "access"
                    )

                    if not isinstance(bnpl_agent_token, str):
                        message = "BNPL Agent Login Failed"
                        continue

                    description = (
                        f"Rvsl of excess saved amt for BNPL item - Amt: N{refundable}"
                    )

                    transfer_transaction = TransactionService.create_internal_transfer_between_accounts(
                        user=ajo_user.user,
                        amount=refundable,
                        transaction_description=description,
                        transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
                        transaction_form_type=TransactionFormType.TRANSFER,
                        status=Status.PENDING,
                        ajo_user=ajo_user,
                        transfer_provider=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
                    )

                    reference = str(transfer_transaction.transaction_id)

                    request_data = {
                        "from_wallet_type": "COLLECTION",
                        "to_wallet_type": "COLLECTION",
                        "data": [
                            {
                                "buddy_phone_number": savings_phone_number,
                                "amount": refundable,
                                "narration": description,
                                "is_beneficiary": "False",
                                "save_beneficiary": "False",
                                "remove_beneficiary": "False",
                                "is_recurring": "False",
                                "custom_reference": reference,
                            }
                        ],
                        "transaction_pin": "",
                    }
                    transaction_pin = settings.BNPL_USER_PIN
                    send_money_to_liberty_nem_account = AgencyBankingClass.send_money_from_an_account_in_liberty_to_user_through_pay_buddy(
                        transaction_pin=transaction_pin,
                        phone_number=savings_phone_number,
                        amount=refundable,
                        transaction_reference=reference,
                        access_token=bnpl_agent_token,
                        narration=description,
                    )

                    if (
                        send_money_to_liberty_nem_account.get("data", {}).get("message")
                        == "success"
                    ):
                        transfer_status = Status.SUCCESS
                        transfer_transaction.status = transfer_status

                    else:

                        transfer_status = Status.FAILED

                        transfer_transaction.status = transfer_status

                    transfer_transaction.request_data = request_data
                    transfer_transaction.payload = send_money_to_liberty_nem_account
                    transfer_transaction.save(
                        update_fields=["request_data", "payload", "status"]
                    )
                    if transfer_status == Status.SUCCESS:

                        credit_transaction = TransactionService.create_deposit_by_wallet_transaction(
                            user=user,
                            amount=refundable,
                            wallet_type=agent_wallet.wallet_type,
                            transaction_form_type=TransactionFormType.WALLET_DEPOSIT,
                            transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
                            unique_reference=unique_reference,
                            description="Refund for excess deposit (BNPL)",
                            status=Status.PENDING,
                            quotation_id=quotation_id,
                            ajo_user=ajo_user,
                            plan_type=PlanType.AJO,
                        )

                        payment_actions.fund_agent_wallet(
                            transaction=credit_transaction,
                            wallet=agent_wallet,
                            amount=refundable,
                            unique_reference=unique_reference,
                        )
                    message = str(transfer_status)
                else:
                    message = f"Amount {refundable}"
                    continue

        self.message_user(request, message)

    cashout_ajo_plan_to_agent.short_description = (
        "AJO: Cashout ajo plan to agent wallet"
    )
    cashout_ajo_plan_to_agent.allow_tags = True

    create_repayment_record_for_savings.short_description = (
        "LOANS: Create loan repayment"
    )
    create_repayment_record_for_savings.allow_tags = True

    repay_commissions_for_plan_into_spend.short_description = (
        "LOANS: Repay commissions for plan to spend wallet"
    )
    repay_commissions_for_plan_into_spend.allow_tags = True

    liquidate_savings_to_spend.short_description = (
        "LOANS: Liquidate Ajo Savings (created instead of loan savings)"
    )
    liquidate_savings_to_spend.allow_tags = True

    get_savings_eligibility_status.short_description = (
        "LOANS: Create/get AjoLoan Eligibility Status"
    )
    get_savings_eligibility_status.allow_tags = True

    update_ajo_savings_type.short_description = "Update ajosavings type to None"
    update_ajo_savings_type.allow_tags = True

    set_group_to_null.short_description = "set group to null"
    set_group_to_null.allow_tags = True

    bnp_refund_excess_savings.short_description = "BNPL: Refund excess savings"
    bnp_refund_excess_savings.allow_tags = True

    get_bnpl_refundable_amt.short_description = "Get BNPL Refundable Amount"
    get_bnpl_refundable_amt.allow_tags = True

    actions = [
        cashout_ajo_plan_to_agent,
        create_repayment_record_for_savings,
        repay_commissions_for_plan_into_spend,
        liquidate_savings_to_spend,
        get_savings_eligibility_status,
        set_group_to_null,
        bnp_refund_excess_savings,
        get_bnpl_refundable_amt,
    ]


class AjoSavingsPlanTypeResourceAdmin(ImportExportModelAdmin):
    resource_class = AjoSavingsPlanTypeResource
    search_fields = [
        "name",
        "lower_limit",
        "upper_limit",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LoanResourceAdmin(ImportExportModelAdmin):
    resource_class = LoanResource
    search_fields = [
        "user__email",
        "ajo_user__phone_number",
        "loaned_amount",
        "interest",
        "repayment_amount",
        "quotation_id",
        "periodic_amount",
        "is_activated",
        "is_active",
        "completed",
        "completed_at",
    ]
    readonly_fields = set_readonly_fields(
        "loaned_amount",
        "interest",
        "repayment_amount",
        "periodic_amount",
        "amount_paid",
        "plan_balance_before",
        "plan_balance_after",
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class BankAccountDetailsResourceAdmin(ImportExportModelAdmin):
    resource_class = BankAccountDetailsResource
    search_fields = [
        "user__email",
        "account_number",
        "account_name",
        "bank_name",
        "bank_code",
        "is_active",
        "ajo_user__phone_number",
    ]
    list_filter = (
        "form_type",
        "is_active",
        "account_type",
        "consent",
    )
    # readonly_fields = set_readonly_fields(
    #     "account_number",
    #     "account_name",
    #     "account_type",
    #     "bank_name",
    #     "bank_code",
    # )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def toggle_status(self, request, queryset: QuerySet[BankAccountDetails]):
        data = {}
        for instance in queryset:
            instance.is_active = not instance.is_active
            instance.save(update_fields=["is_active"])
            instance.refresh_from_db()
            # try:
            #     instance.save()
            # except IntegrityError:
            #     accounts = BankAccountDetails.objects.filter(
            #         account_number=instance.account_number
            #     )
            #     count = 1
            #     while accounts.exists():
            #         account_number = f"{instance.account_number}-{count}"
            #         accounts = BankAccountDetails.objects.filter(
            #             account_number=account_number
            #         )
            #         count += 1

            # instance.account_number = account_number
            # instance.save()

            data[str(instance)] = f"status toggled to {instance.is_active}"

        self.message_user(request, message=str(data))

    def update_repayment_account(self, request, queryset: QuerySet[BankAccountDetails]):
        acct_no = "**********"
        for instance in queryset:
            if (
                instance.account_number != acct_no
                and instance.form_type != AccountFormType.LOAN_REPAYMENT
            ):
                self.message_user(
                    request, str("This Bank Account Instance does not meet requirement")
                )
                return

            ajo_user = instance.ajo_user

            with django_transaction.atomic():
                instance.delete()
                previously_created_count = BankAccountDetails.objects.filter(
                    ajo_user=ajo_user
                ).count()
                if ajo_user.bvn and ajo_user.bvn_verified:
                    new_account = False
                else:
                    new_account = True

                account = BankAccountService.create_wema_account(
                    ajo_user=ajo_user,
                    acct_form_type=AccountFormType.LOAN_REPAYMENT,
                    new_account=new_account,
                    previous_created_count=previously_created_count,
                )

            if account.get("status"):
                self.message_user(request, f"New account created for user successfully")

            else:
                self.message_user(
                    request, f"Account replacemnet failed: {account.get('message')}"
                )

                # create_wallet_acct = CoreBankingManager().create_account(
                #     first_name=ajo_user.first_name,
                #     last_name=ajo_user.last_name,
                #     middle_name=ajo_user.alias,
                #     email="",
                #     phone=ajo_user.phone_number,
                #     bvn=ajo_user.bvn,
                #     date_of_birth=str(ajo_user.dob),
                # )
                # status_code = create_wallet_acct.get("status_code")
                # if status_code == 200:
                #     data = create_wallet_acct.get("data").get("account_details")
                #     account_number = data.get("account_number")
                #     acct_name = data.get("first_name") + " " + data.get("last_name")
                #     bank_code = data.get("bank_code")
                #     bank_name = data.get("bank_name")
                #     bvn = data.get("bvn")

                #     instance.account_number = account_number
                #     instance.account_name = acct_name
                #     instance.payload = create_wallet_acct

                #     instance.bank_code = bank_code
                #     instance.bank_name = bank_name
                #     instance.bvn = bvn
                #     instance.save()

    update_repayment_account.short_description = "Resolve Duplicate Repayment Accounts"
    update_repayment_account.allow_tags = True

    toggle_status.short_description = "toggle status"
    toggle_status.allow_tags = True

    actions = [
        update_repayment_account,
        toggle_status,
    ]


class RawFundingDataResourceAdmin(ImportExportModelAdmin):
    resource_class = RawFundingDataResource
    search_fields = [
        "reference",
        "recipient_account_number",
        "source",
    ]
    list_filter = (
        "source",
        "authorized",
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def confirm_transaction_status_with_verification_ref(
        self, request, queryset: QuerySet[RawFundingData]
    ):
        message = "Invalid action"
        for instance in queryset:
            message = CoreBankingManager.get_verification_result(
                reference=f"{instance.reference}"
            )
        self.message_user(request, message=message)

    def confirm_transaction_status_with_verification_ref(
        self, request, queryset: QuerySet[RawFundingData]
    ):
        message = "Invalid action"
        for instance in queryset:
            message = CoreBankingManager.get_verification_result(
                reference=f"{instance.reference}"
            )
        self.message_user(request, message=message)

    def confirm_transaction_status_with_verification_session_id(
        self, request, queryset: QuerySet[RawFundingData]
    ):
        from helper_methods import parse_to_dict

        message = "Invalid action"

        for instance in queryset:
            try:
                payload = parse_to_dict(data=instance.payload)
                session_id = payload.get("session_id")
                if not session_id:
                    message = (
                        "No recipient account number found. Skipping this instance."
                    )
                    continue
                message = CoreBankingManager.get_verification_result(
                    reference=session_id
                )

            except ValueError as e:
                message = f"An error occurred while updating recipient account number: {str(e)}"
        self.message_user(request, message=message)

    def update_recipient_no(self, request, queryset: QuerySet[RawFundingData]):

        from helper_methods import parse_to_dict

        message = "Invalid action"
        for instance in queryset:
            try:
                payload = parse_to_dict(data=instance.payload)
                recipient_account_number = payload.get("recipient_account_number")
                if not recipient_account_number:
                    message = (
                        "No recipient account number found. Skipping this instance."
                    )
                    continue
                instance.recipient_account_number = recipient_account_number
                instance.save(update_fields=["recipient_account_number"])
                message = "success"
            except ValueError as e:
                message = f"An error occurred while updating recipient account number: {str(e)}"

        self.message_user(request, message=message)

    def retry_notification(self, request, queryset: QuerySet[RawFundingData]):
        from ajo.views.callback_views import perform_bank_account_funding
        from helper_methods import parse_to_dict

        message = "Invalid action"

        def verify_transaction(instance, reference):
            """Helper function to fetch and validate a transaction verification result."""
            result = CoreBankingManager.get_verification_result(reference=reference)

            # Update the instance with the result
            instance.result = str(result)
            instance.save(update_fields=["result"])

            if result.get("status_code") == 200 and result.get("status") == "success":
                return True, result.get("response")

            return False, result

        for instance in queryset:
            payload = parse_to_dict(data=instance.payload)
            transaction_reference = payload.get("reference")
            session_id = payload.get("session_id")
            recipient_account_number = payload.get("recipient_account_number")
            amount = payload.get("amount")

            # Ensure recipient account exists
            account_instance = BankAccountSelector.check_for_account_number(
                account_number=recipient_account_number,
                bank_name="Wema",
            )
            if not account_instance:
                message = f"Account number {recipient_account_number} not found. Skipping this transaction."
                continue

            # Verify using transaction session id
            verification = verify_transaction(instance=instance, reference=session_id)
            is_valid_response = verification[0]
            verification_response = verification[1]

            if not is_valid_response:
                message = f"MSG: {verification}"
                continue

            if verification_response:
                # Validate transaction details
                if (
                    verification_response.get("status") == "success"
                    and verification_response.get("data", {}).get(
                        "recipient_account_number"
                    )
                    == recipient_account_number
                    and verification_response.get("data", {}).get("amount") == amount
                    and verification_response.get("data", {}).get("reference")
                    == transaction_reference
                ):
                    # Extract additional details for funding
                    extra_data = {
                        "session_id": session_id,
                        "company": payload.get("company"),
                        "paid_at": payload.get("paid_at"),
                        "payer_account_name": payload.get("payer_account_name"),
                        "payer_account_number": payload.get("payer_account_number"),
                        "payer_bank_code": payload.get("payer_bank_code"),
                        "narration": payload.get("narration"),
                    }

                    # Perform funding
                    response = perform_bank_account_funding(
                        unique_reference=transaction_reference,
                        account_instance=account_instance,
                        amount=amount,
                        extra_data=extra_data,
                        raw_funding=instance,
                        user=account_instance.user,
                    )
                    message = str(response)
                else:
                    message = f"Transaction data mismatch: Reference {transaction_reference} does not match the expected values."
            else:
                message = f"Funding verification failed: Unable to verify transaction {transaction_reference}."

        self.message_user(request, message=message)

    def retry_vfd_notification(self, request, queryset: QuerySet[RawFundingData]):
        from ajo.views.callback_views import perform_bank_account_funding
        from helper_methods import parse_to_dict

        message = "Invalid action"

        def _verify_transaction(instance, reference):
            """Helper function to fetch and validate a transaction verification result."""
            result = AgencyBankingClass.verify_agency_banking_for_vfd_funding(
                liberty_reference=reference
            )

            # Update the instance with the result
            instance.result = str(result)
            instance.save(update_fields=["result"])

            if (
                result.get("status") is True
                and result.get("data", {}).get("status") == "SUCCESSFUL"
            ):
                return True, result
            else:
                return False, result

        for instance in queryset:

            payload = parse_to_dict(data=instance.payload)
            recipient_account_number = payload.get("account_number")

            liberty_reference = instance.reference
            verification = _verify_transaction(
                instance=instance, reference=liberty_reference
            )
            is_valid_response = verification[0]
            res = verification[1]

            if not is_valid_response:
                message = f"MSG: {verification}"
                continue

            # Ensure recipient account exists
            account_instance = BankAccountSelector.check_for_account_number(
                account_number=recipient_account_number,
            )
            if not account_instance:
                message = f"Account number {recipient_account_number} not found. Skipping this transaction."
                continue

            amount = round(float(payload.get("amount")), 2)

            calculated_amount = res.get("data", {}).get("amount") - res.get(
                "data", {}
            ).get("liberty_commission", 0)

            if (
                res.get("data", {}).get("status") == "SUCCESSFUL"
                and res.get("data", {}).get("transaction_type")
                in ["FUND_COLLECTION_ACCOUNT", "FUND_BANK_TRANSFER"]
                and res.get("data", {}).get("liberty_reference") == liberty_reference
                and calculated_amount == amount
            ):

                response = perform_bank_account_funding(
                    unique_reference=liberty_reference,
                    account_instance=account_instance,
                    amount=amount,
                    raw_funding=instance,
                    user=account_instance.user,
                )

                instance.funding_response = response
                instance.save()

                message = str(response)

            else:
                message = f"Transaction data mismatch: Reference {liberty_reference} does not match the expected values."

        self.message_user(request, message=message)

    confirm_transaction_status_with_verification_ref.short_description = (
        "Confirm transaction status with ref"
    )
    confirm_transaction_status_with_verification_ref.allow_tags = True

    confirm_transaction_status_with_verification_session_id.short_description = (
        "Confirm transaction status with session id"
    )
    confirm_transaction_status_with_verification_session_id.allow_tags = True

    retry_notification.short_description = "Wema: Retry notification"
    retry_notification.allow_tags = True

    retry_vfd_notification.short_description = "VFD: Retry notification"
    retry_vfd_notification.allow_tags = True

    update_recipient_no.short_description = "Wema: update recipient No"
    update_recipient_no.allow_tags = True

    actions = [
        confirm_transaction_status_with_verification_ref,
        confirm_transaction_status_with_verification_session_id,
        retry_notification,
        update_recipient_no,
        retry_vfd_notification,
    ]


class PrefundingResourceAdmin(ImportExportModelAdmin):
    resource_class = PrefundingResource
    search_fields = [
        "user__email",
        "prefunding_amount",
        "completed",
        "completed_at",
    ]
    readonly_fields = set_readonly_fields(
        "prefunding_amount",
        "amount_paid",
        "balance_left",
        "completed",
        "completed_at",
    )
    list_filter = ("completed",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AccountCreationsCheckResourceAdmin(ImportExportModelAdmin):
    resource_class = AccountCreationsCheckResource
    search_fields = [
        "user__email",
        "dump",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AjoTradeCategoryResourceAdmin(ImportExportModelAdmin):
    resource_class = AjoTradeCategoryResource
    search_fields = ["name"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AjoTradeSubCategoryResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["category"]
    resource_class = AjoTradeSubCategoryResource
    search_fields = [
        "name",
        "category__name",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AjoUserWithdrawalAccountResourceAdmin(ImportExportModelAdmin):
    resource_class = AjoUserWithdrawalAccountResource
    search_fields = [
        "ajo_user__phone_number",
        "account_name",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RoscaPaymentBreakDownAdmin(ImportExportModelAdmin):
    resource_class = RoscaPaymentBreakDownResource
    search_fields = [
        "user__email",
        "group",
        "ajo_user",
        "due_amount",
        "paid_amount",
        "paid",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class BranchResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["supervisor"]
    resource_class = BranchResource
    search_fields = [
        "name",
        "supervisor__email",
    ]
    list_filter = [
        "date_created",
        "supervisor__email",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ProfileChangeRequestResourceAdmin(ImportExportModelAdmin):
    search_fields = [
        "ajo_user__phone_number",
        "ajo_user__user__email",
        "status",
    ]
    list_filter = [
        "status",
        "is_active",
    ]
    fields = (
        "ajo_user",
        "status",
        "is_active",
        "payload",
        "ajo_user_image_url",
        "payload_image_url",
    )
    readonly_fields = (
        "ajo_user_image_url",
        "payload_image_url",
    )

    def get_form(self, request, obj=None, **kwargs):
        # Store the request in an instance variable
        self.request = request
        return super().get_form(request, obj, **kwargs)

    def ajo_user_image_url(self, obj: ProfileChangeRequest):
        image_url = obj.ajo_user.image
        if not image_url:
            return ""

        current_site = obtain_the_current_site(request=self.request)

        clickable_url = f"{current_site}/media/{image_url}"

        return format_html(
            '<a href="{}" target="_blank">{}</a>', clickable_url, clickable_url
        )

    ajo_user_image_url.short_description = "The Ajo User's Uploaded Image"

    def payload_image_url(self, obj: ProfileChangeRequest):
        if not obj.payload:
            return ""

        try:
            payload = json.loads(obj.payload)
        except json.JSONDecodeError as err:
            payload = ast.literal_eval(obj.payload)

        new_image_url = payload.get("selfieUrl", "")
        return format_html(
            '<a href="{}" target="_blank">{}</a>', new_image_url, new_image_url
        )

    payload_image_url.short_description = "Uploaded Selfie URL for verification"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SupervisorResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["supervisor"]
    resource_class = SupervisorResource
    search_fields = [
        "branch__name",
        "supervisor__email",
    ]
    list_filter = [
        "date_created",
        "supervisor__email",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PerformAgentSwitchResource(resources.ModelResource):
    class Meta:
        model = PerformAgentSwitch
        # fields = ['old_agent', 'new_agent']

    # def before_import_row(self, row, **kwargs):

    #     model_name = "AjoUser"
    #     self._meta.model = GetModel.get_model(model_name=model_name, app_name="ajo")

    #     try:
    #         old_object_id = row['old_agent']
    #         new_object_id = row['new_agent']

    #         old_object = self._meta.model.objects.get(id=old_object_id)
    #         new_object = self._meta.model.objects.get(id=new_object_id)
    #     except self._meta.model.DoesNotExist:
    #         raise ValueError(f"Object with ID {old_object_id} or {new_object_id} does not exist for model {model_name}")

    #     with django_transaction.atomic():

    #         new_log = AgentSwitchLog(old_agent=old_agent, new_agent=new_agent, content_object=obj)
    #         new_log.save()
    #         old_object.user = new_object
    #         old_object.save()


class PerformAgentSwitchResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["ajo_user", "old_agent", "new_agent"]
    resource_class = PerformAgentSwitchResource
    search_fields = [
        "ajo_user__first_name",
        "ajo_user__last_name",
        "ajo_user__phone_number",
        "old_agent__email",
        "new_agent__email",
    ]
    list_filter = [
        "created_at",
    ]
    readonly_fields = ["old_agent"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def get_readonly_fields(self, request, obj=None):
        if obj and obj.new_agent:
            all_field_names = [field.name for field in self.model._meta.get_fields()]
            return all_field_names
        else:
            return super().get_readonly_fields(request, obj)

    @django_transaction.atomic
    def save_model(self, request, obj: PerformAgentSwitch, form, change):
        from loans.helpers.helpers import send_dynamic_email
        from loans.models import AjoLoan, AjoLoanRepayment, LoanEligibility
        from payment.models import Transaction, WalletSystem

        from .utils.otp_utils import send_agent_switch_sms

        old_agent = obj.ajo_user.user
        ajo_instance = obj.ajo_user
        new_agent = obj.new_agent

        model = ajo_instance._meta.model
        content_type = ContentType.objects.get_for_model(model)

        new_log = AgentSwitchLog(
            old_agent=old_agent,
            new_agent=new_agent,
            content_type=content_type,
            object_id=ajo_instance.id,
        )
        new_log.save()

        obj.old_agent = old_agent

        ajo_instance.user = new_agent
        ajo_instance.save()

        for savings in AjoSaving.objects.filter(ajo_user=ajo_instance):
            savings.user = new_agent
            savings.save()

            for eligibility in LoanEligibility.objects.filter(saving=savings):
                eligibility.agent = new_agent
                eligibility.save()

        for open_loan in AjoLoan.get_open_loans(ajo_user=ajo_instance):
            open_loan.agent = new_agent
            open_loan.save()

            for repayment in AjoLoanRepayment.objects.filter(ajo_loan=open_loan):
                repayment.agent = new_agent
                repayment.save()

                for transaction in Transaction.objects.filter(
                    unique_reference=repayment.repayment_ref
                ):
                    transaction.user = new_agent
                    transaction.save()

        for bank in BankAccountDetails.objects.filter(ajo_user=ajo_instance):
            bank.user = new_agent
            bank.save()

        for withdrawal in AjoUserWithdrawalAccount.objects.filter(
            ajo_user=ajo_instance
        ):
            withdrawal.user = new_agent
            withdrawal.save()

        for wallet in WalletSystem.objects.filter(onboarded_user=ajo_instance):
            wallet.user = new_agent
            wallet.save()

        # Notify New Agent and Customer
        # if old_agent is not None and old_agent != new_agent:
        new_agent_email = new_agent.email
        borrower_name = ajo_instance.fullname
        borrower_phone = ajo_instance.phone_number

        borrower_context = {
            "agent_email": new_agent_email,
            "borrower_name": borrower_name,
            "borrower_phone": borrower_phone,
        }

        # Send email notification to agent
        try:
            send_dynamic_email(
                email_subject="Customers Switch Notification",
                email=new_agent_email,
                template_dir=os.path.join(
                    settings.BASE_DIR, "templates/loans/agent_switch_notifcation.html"
                ),
                extra_data=borrower_context,
            )
        except Exception:
            pass

        # Send sms notification to user
        try:
            send_agent_switch_sms(
                ajo_user=ajo_instance,
                phone_number=borrower_phone,
                user=new_agent,
            )
        except Exception:
            pass

        super().save_model(request, obj, form, change)

    @django_transaction.atomic
    def reassign_missed_information_to_new_agent(
        self, request, queryset: QuerySet[PerformAgentSwitch]
    ):
        from loans.models import AjoLoan, AjoLoanRepayment, Transaction

        response: Dict[str, str] = {}
        for query_instance in queryset:
            new_agent = query_instance.new_agent
            ajo_user = query_instance.ajo_user
            response_key = query_instance.new_agent.email

            try:
                for open_loan in AjoLoan.get_open_loans(ajo_user=ajo_user):

                    if open_loan.agent != new_agent:
                        open_loan.agent = new_agent
                        open_loan.save()

                    for repayment in AjoLoanRepayment.objects.filter(
                        ajo_loan=open_loan
                    ):
                        if repayment.agent != new_agent:
                            repayment.agent = new_agent
                            repayment.save()

                        for transaction in Transaction.objects.filter(
                            unique_reference=repayment.repayment_ref
                        ):
                            if transaction.user != new_agent:
                                transaction.user = new_agent
                                transaction.save()

                response[response_key] = (
                    f"reassigned open loans, open loans repayments and open loan repayment transactions to new agent"
                )

            except Exception as err:
                response[response_key] = f"an error occurred during reassignment: {err}"
                continue

        self.message_user(request, str(response))

    reassign_missed_information_to_new_agent.short_description = (
        "SWITCH: Reassign missed info to the new agent"
    )
    reassign_missed_information_to_new_agent.allow_tags = True

    actions = [
        reassign_missed_information_to_new_agent,
    ]


class ProductInformationResourceAdmin(ImportExportModelAdmin):
    # autocomplete_fields = ["savings"]
    search_fields = ["item_id", "item", "category"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AgencyDumpsResourceAdmin(ImportExportModelAdmin):
    # autocomplete_fields = ["savings"]
    resource_class = AgencyDumpsResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AjoSepoResourceAdmin(ImportExportModelAdmin):
    resource_class = AjoSepoResource
    search_fields = [
        "user__email",
        "name",
        "group_id",
        "leader__phone_number",
        "savings__quotation_id",
    ]
    list_filter = [
        "is_activated",
        "status",
        "loan_tenure",
        "repayment_type",
        "is_active",
        ("created_at", admin.DateFieldListFilter),
    ]

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class BusinessSuiteResourceAdmin(ImportExportModelAdmin):
    resource_class = BusinessSuiteResource
    search_fields = [
        "savings_id",
    ]
    # list_filter = [

    # ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


####################################################################################
# REGISTER MODELS

admin.site.register(AjoUser, AjoUserResourceAdmin)
admin.site.register(Card, CardResourceAdmin)
admin.site.register(RotationGroup, RotationGroupResourceAdmin)
admin.site.register(RotationGroupMember, RotationGroupMemberResourceAdmin)
admin.site.register(CardRequests, CardRequestsResourceAdmin)
admin.site.register(AjoSaving, AjoSavingResourceAdmin)
admin.site.register(AjoSavingsPlanType, AjoSavingsPlanTypeResourceAdmin)
admin.site.register(AgencyDumps, AgencyDumpsResourceAdmin)
# admin.site.register(Loan, LoanResourceAdmin)
admin.site.register(BankAccountDetails, BankAccountDetailsResourceAdmin)
admin.site.register(RawFundingData, RawFundingDataResourceAdmin)
admin.site.register(Prefunding, PrefundingResourceAdmin)
admin.site.register(AccountCreationsCheck, AccountCreationsCheckResourceAdmin)
admin.site.register(AjoTradeCategory, AjoTradeCategoryResourceAdmin)
admin.site.register(AjoTradeSubCategory, AjoTradeSubCategoryResourceAdmin)
admin.site.register(AjoUserWithdrawalAccount, AjoUserWithdrawalAccountResourceAdmin)
admin.site.register(RoscaPaymentBreakDown, RoscaPaymentBreakDownAdmin)
admin.site.register(Branch, BranchResourceAdmin)
admin.site.register(ProfileChangeRequest, ProfileChangeRequestResourceAdmin)
admin.site.register(Supervisor, SupervisorResourceAdmin)
admin.site.register(PerformAgentSwitch, PerformAgentSwitchResourceAdmin)
admin.site.register(ProductInformation, ProductInformationResourceAdmin)
admin.site.register(AjoSepo, AjoSepoResourceAdmin)
admin.site.register(BusinessSuite, BusinessSuiteResourceAdmin)
