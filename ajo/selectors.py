from datetime import datetime
from typing import Any, Dict, List, Optional

from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.core.cache import cache
from django.db.models import Count, Q, Sum
from django.db.models.functions import Coalesce
from django.db.models.query import QuerySet
from django.utils import timezone

from accounts.models import ConstantTable
from payment.model_choices import (
    PlanType,
    Status,
    TransactionFormType,
    TransactionTypeCreditOrDebitChoices,
    WalletTypes,
)
from payment.models import Commission, Transaction, WalletSystem
from payment.utils import generate_default_code, is_past_month_year

from .model_choices import (
    AccountFormType,
    ProfileChangeStatus,
    RoscaGroupStatus,
    SavingsFrequency,
    SavingsType,
)
from .models import (
    AjoSaving,
    AjoSepo,
    AjoUser,
    BankAccountDetails,
    Card,
    CardRequests,
    Loan,
    Prefunding,
    ProfileChangeRequest,
    RotationGroup,
    RotationGroupMember,
)
from .utils.personal_utils import PersonalAjoUtils


class AjoUserSelector:
    def __init__(
        self,
        phone_number: str | None = None,
        user: AbstractUser = None,
        ajo_user: AjoUser | None = None,
    ) -> None:
        """
        Either instantiate the class with:
            1. phone_number and user
        OR:
            2. ajo_user

        Args:
            phone_number (str | None): the phone number of the ajo user. Defaults to None
            user (AbstractUser | None): the user that onboarded the ajo user. Defaults to None
            ajo_user (AjoUser | None): the ajo user instance. Defaults to None

        """
        # perform checks to ensure valid combination of arguments
        type_error = "invalid combination of arguments. Please provide either onboarded_user or phone_number and user"
        if ajo_user:
            if phone_number or user:
                raise TypeError(type_error)

            if ajo_user.provider == "ajo":
                self.user = ajo_user.user
                self.ajo_user = ajo_user
            elif ajo_user.provider == "lite":
                self.user = ajo_user.lite_user
                self.ajo_user = ajo_user

        elif phone_number and user:
            if ajo_user:
                raise TypeError(type_error)

            self.user = user
            try:
                self.ajo_user = (
                    AjoUser.objects.select_related("user")
                    .filter(phone_number__startswith=phone_number, user=user)
                    .latest("id")
                )
            except AjoUser.DoesNotExist:
                self.ajo_user = None

        else:
            raise TypeError(type_error)

    def get_ajo_user(self) -> AjoUser:
        """
        Retrieves an ajo user

        Raises:
            ValueError: "ajo user was not found"

        Returns:
            AjoUser: the ajo user instance
        """

        if self.ajo_user:
            return self.ajo_user
        else:
            raise ValueError("ajo user was not found")

    def get_ajo_user_ajo_savings(
        self, is_active: Optional[bool] = None
    ) -> QuerySet[AjoSaving]:
        """
        Get a queryset of AjoSavings belonging to an ajo user

        Raises:
            ValueError: ajo user was not found

        Returns:
            List[AjoSaving]: A QuerySet of AjoSavings
        """
        # call the get_ajo_user function
        ajo_user = self.get_ajo_user()

        # get the AjoSavings
        ajo_savings = AjoSaving.objects.select_related("plan_type").filter(
            user=self.user, ajo_user=ajo_user
        )

        # exclude loan repayments
        ajo_savings = ajo_savings.exclude(is_loan_repayment=True)

        if is_active is not None:
            ajo_savings = ajo_savings.filter(is_active=is_active)

        ajo_savings = ajo_savings.order_by("-id")
        return ajo_savings

    def get_number_of_active_ajo_user_savings(
        self,
    ) -> int:
        """
        Get the total number of active savings an Ajo User has

        Raises:
            ValueError: "ajo user was not found"

        Returns:
            int: the number of active savings that the ajo user has
        """
        return self.get_ajo_user_ajo_savings(is_active=True).count()

    def get_amount_saved_in_active_savings(self) -> float:
        """
        Get the amount saved in active savings

        Returns:
            float: amount saved
        """
        active_plans = self.get_ajo_user_ajo_savings(is_active=True)
        total_amount_saved = (
            active_plans.aggregate(total=Sum("amount_saved"))["total"] or 0
        )
        return total_amount_saved

    def get_ajo_user_wallet(self) -> WalletSystem:
        """
        This retrieves the ajo user's wallet instance

        Returns:
            WalletSystem: The wallet of the ajo user
        """
        user = self.user
        ajo_user = self.get_ajo_user()

        wallet = WalletSystem.get_or_create_wallet(
            user=user,
            wallet_type=WalletTypes.AJO_USER,
            ajo_user=ajo_user,
        )

        return wallet

    def get_ajo_user_wallet_balance(self) -> float:
        """
        Returns how much an ajo user has in his wallet

        Returns:
            float: the amount of money in the available balance
        """
        return self.get_ajo_user_wallet().available_balance

    # TODO: this method is redundant
    def get_total_amount_ajo_user_has_saved(self) -> float:
        """
        Obtains the amount in an Ajo User's wallet

        Returns:
            float: the current amount
        """
        # call the get_ajo_user function
        ajo_user = self.get_ajo_user()

        # get the wallet
        wallet: WalletSystem = WalletSystem.get_or_create_wallet(
            user=self.user,
            wallet_type=WalletTypes.AJO_USER,
            ajo_user=ajo_user,
        )
        return wallet.available_balance

    def get_ajo_user_transaction_history(self) -> List[Transaction]:
        """
        Get the queryset of Transaction objects belonging to an ajo user

        Raises:
            ValueError: ajo user was not found

        Returns:
            List[Transaction]: Transaction QuerySet
        """
        ajo_user = self.get_ajo_user()
        user = self.user

        # get the Transaction
        return Transaction.objects.filter(user=user, onboarded_user=ajo_user).order_by(
            "-id"
        ).exclude(transaction_form_type=TransactionFormType.HEALTH_PLAN_DEDUCTION)

    def match_pin(self, pin: str) -> bool:
        ajo_user = self.get_ajo_user()
        return pin == ajo_user.pin

    def get_any_ajo_user_wallet(self, wallet_type: WalletTypes) -> WalletSystem:
        """
        This retrieves the ajo user's spending wallet instance

        Returns:
            WalletSystem: the spending wallet of the ajo user
        """
        user = self.user
        ajo_user = self.get_ajo_user()

        wallet = WalletSystem.get_or_create_wallet(
            user=user,
            wallet_type=wallet_type,
            ajo_user=ajo_user,
        )

        return wallet

    def get_spending_wallet(self, account_number=None) -> WalletSystem:
        """
        This retrieves the ajo user's spending wallet instance

        Returns:
            WalletSystem: the spending wallet of the ajo user
        """
        user = self.user
        ajo_user = self.get_ajo_user()

        wallet = WalletSystem.get_or_create_wallet(
            user=user,
            wallet_type=WalletTypes.AJO_SPENDING,
            ajo_user=ajo_user,
            wallet_number=account_number,
        )

        return wallet

    # def get_bnpl_wallet(self) -> WalletSystem:
    #     """
    #     This retrieves the ajo user's spending wallet instance

    #     Returns:
    #         WalletSystem: the spending wallet of the ajo user
    #     """
    #     user = self.user
    #     ajo_user = self.get_ajo_user()

    #     wallet = WalletSystem.get_or_create_wallet(
    #         user=user,
    #         wallet_type=WalletTypes.BNPL,
    #         ajo_user=ajo_user,
    #     )

    #     return wallet

    def get_loan_disbursement_wallet(self) -> WalletSystem:
        """
        This retrieves or create the ajo user's loan_disbursement wallet instance

        Returns:
            WalletSystem: the disburssment wallet of the ajo user
        """
        user = self.user
        ajo_user = self.get_ajo_user()

        wallet = WalletSystem.get_or_create_wallet(
            user=user,
            wallet_type=WalletTypes.LOAN_DISBURSEMENT,
            ajo_user=ajo_user,
        )
        return wallet

    def get_spending_wallet_balance(self) -> float:
        """
        Returns how much an ajo user has in his spending wallet

        Returns:
            float: the amount of money in the spending wallet available balance
        """
        return self.get_spending_wallet().available_balance

    def get_loan_escrow_wallet_balance(self) -> float:
        """
        Returns how much an ajo user has in his loan escrow wallet

        Returns:
            float: the amount of money in the loan escrow wallet available balance
        """
        return self.get_any_ajo_user_wallet(
            wallet_type=WalletTypes.AJO_LOAN_ESCROW
        ).available_balance

    def get_loan_disbursement_wallet_balance(self) -> float:
        """
        Returns how much an ajo user has in his loan disbursement wallet

        Returns:
            float: the amount of money in the loan escrow wallet available balance
        """
        return self.get_any_ajo_user_wallet(
            wallet_type=WalletTypes.LOAN_DISBURSEMENT
        ).available_balance

    def get_digital_wallet(self) -> WalletSystem:
        """
        This retrieves the ajo user's digital wallet
        Returns:
            WalletSystem: the digital wallet of the ajo user
        """
        return WalletSystem.get_or_create_wallet(
            user=self.user,
            wallet_type=WalletTypes.AJO_DIGITAL,
            ajo_user=self.get_ajo_user(),
        )

    def get_digital_wallet_balance(self) -> float:
        """
        Returns how much an ajo user has in his digital wallet
        Returns:
            float: the amount of money in the digital wallet available balance
        """
        return self.get_digital_wallet().available_balance

    def generate_and_set_spending_wallet_number(self) -> str:
        spending_wallet = self.get_spending_wallet()

        if not spending_wallet.wallet_number:
            number = generate_default_code()
            while WalletSystem.objects.filter(
                wallet_type=WalletTypes.AJO_SPENDING,
                wallet_number=number,
            ).exists():
                number = generate_default_code()

            spending_wallet.wallet_number = number
            spending_wallet.save()

        return spending_wallet.wallet_number

    def has_active_ajo_plan(self) -> bool:
        """
        Checks if the Ajo User has any active ajo plan

        Returns:
            bool: True or False
        """

        return AjoSaving.objects.filter(
            user=self.user,
            ajo_user=self.ajo_user,
            is_active=True,
        ).exists()

    def get_rosca_ajo_user_wallet(self) -> WalletSystem:
        """
        This retrieves the ajo user's wallet instance

        Args:
            ajo_user (AjoUser): The AjoUser for which to retrieve the wallet.

        Returns:
            WalletSystem: The wallet of the AjoUser.
        """
        user = self.user

        wallet = WalletSystem.get_or_create_wallet(
            user=user,
            wallet_type=WalletTypes.ROSCA_AJO_USER,
            ajo_user=self.ajo_user,
        )

        return wallet

    def get_ajo_escrow_wallet(self) -> WalletSystem:
        """
        This retrieves the ajo user's loan escrow wallet instance

        Returns:
            WalletSystem: the escrow wallet of the ajo user
        """
        user = self.user
        ajo_user = self.get_ajo_user()

        wallet = WalletSystem.get_or_create_wallet(
            user=user,
            wallet_type=WalletTypes.AJO_LOAN_ESCROW,
            ajo_user=ajo_user,
        )

        return wallet


def get_ajo_rosca_user_spend_wallet(ajo_user) -> WalletSystem:
    """
    This retrieves the ajo user's wallet instance

    Args:
        ajo_user (AjoUser): The AjoUser for which to retrieve the wallet.

    Returns:
        WalletSystem: The wallet of the AjoUser.
    """
    user = ajo_user.user

    wallet = WalletSystem.get_or_create_wallet(
        user=user,
        wallet_type=WalletTypes.AJO_SPENDING,
        ajo_user=ajo_user,
    )

    return wallet


def get_ajo_rosca_group_wallet(user, group_id) -> WalletSystem:
    """
    This retrieves the ajo user's wallet instance

    Args:
        ajo_user (AjoUser): The AjoUser for which to retrieve the wallet.

    Returns:
        WalletSystem: The wallet of the AjoUser.
    """
    wallet = WalletSystem.get_or_create_wallet(
        user=user,
        wallet_type=WalletTypes.ROSCA_GROUP_ESCROW,
        rotation_group_id=group_id,
    )

    return wallet


class AjoAgentSelector:

    def __init__(self, user: AbstractUser):
        self.user = user
        self.ajo_users_qs = AjoUser.objects.filter(user=user)

    def get_agent_ajo_wallet(
        self, wallet_type: WalletTypes = WalletTypes.AJO_AGENT
    ) -> WalletSystem:
        """
        Retrieves the Agent's Ajo Wallet instance

        Returns:
            WalletSystem: the Ajo wallet of the agent
        """
        wallet: WalletSystem = WalletSystem.get_or_create_wallet(
            user=self.user,
            wallet_type=wallet_type,
        )
        return wallet

    def get_agent_ajo_wallet_balance(self) -> float:
        """
        Retrieves the available balance an agent has in the wallet

        Returns:
            float: How much present.
        """
        return self.get_agent_ajo_wallet().available_balance

    def get_total_collection_for_the_day(self) -> float:
        """
        Retrieve how much the agent has collected today

        Returns:
            float: the amount collected
        """
        # identify what date it is today
        today = timezone.localtime().today()
        # the user model
        user = self.user

        # Query for the information
        savings_query = Transaction.objects.filter(
            user=user,
            onboarded_user__isnull=False,
            transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
            status=Status.SUCCESS,
            transaction_date_completed__date=today,
        )
        total_saving = savings_query.aggregate(total=Sum("amount"))["total"]
        total_cash_saved = total_saving or 0
        return total_cash_saved

    def get_summary_for_agent(self, month: str | None = None):
        user = self.user
        ajo_users = AjoUser.objects.filter(user=user)

        if month:
            # Get the month information
            # month_start = timezone.make_aware(datetime.strptime(month, "%Y-%m"))
            # month_end = (
            #     month_start.replace(day=1, month=month_start.month + 1)
            #     if month_start.month < 12
            #     else month_start.replace(day=1, month=1, year=month_start.year + 1)
            # )

            user_count = self.get_total_ajo_users_count(month=month)
            total_cash_saved = self.get_total_cash_saved(month=month)
            total_cash_withdrawn = self.get_total_withdrawal(month=month)
            withdrawal_count = self.get_total_withdrawal_count(month=month)

        else:
            user_count: int = self.get_total_ajo_users_count()
            total_cash_saved: float = self.get_total_cash_saved()
            total_cash_withdrawn: float = self.get_total_withdrawal()
            withdrawal_count: int = self.get_total_withdrawal_count()

        return {
            "total_savers": user_count,
            "total_cash_collection": total_cash_saved,
            "total_withdrawals": withdrawal_count,
            "total_cash_withdrawn": total_cash_withdrawn,
        }

    def get_total_ajo_users_count(
        self, month: str | None = None, complete_date: str | None = None
    ) -> List[AjoUser]:
        """
        Get the Ajo users belonging to an Ajo agent and apply certain fields

        Args:
            month (str | None, optional): Year-Month e.g. "2023-05". Defaults to None.
            complete_date (str | None, optional): Year-Month-Day e.g. "2023-05-12". Defaults to None.

        Returns:
            List[AjoUser]: Queryset of Ajo users that fit the filtering.
        """
        ajo_user_qs = self.ajo_users_qs

        if month:
            # Get the month information
            month_start = timezone.make_aware(datetime.strptime(month, "%Y-%m"))
            month_end = (
                month_start.replace(day=1, month=month_start.month + 1)
                if month_start.month < 12
                else month_start.replace(day=1, month=1, year=month_start.year + 1)
            )

            #### Filter AjoSaving by month and count unique active ajo users
            ajo_user_count = AjoUser.objects.filter(
                created_at__gte=month_start,
                created_at__lt=month_end,
            ).count()

        elif complete_date:
            date_to_filter = timezone.make_aware(
                datetime.strptime(complete_date, "%Y-%m-%d")
            ).date()
            #### Filter AjoUser model by date and count ajo users
            ajo_user_count = ajo_user_qs.filter(created_at__date=date_to_filter)
        else:
            ajo_user_count = ajo_user_qs.count()

        return ajo_user_count

    def get_total_cash_saved(
        self, month: str | None = None, complete_date: str | None = None
    ) -> float:
        """
        Get the total amount saved for ajo users belonging to a user
        based on certain filters as well

        Args:
            month (str | None, optional):  Year-Month e.g. "2023-05". Defaults to None.
            complete_date (str | None, optional): Year-Month-Day e.g. "2023-05-12". Defaults to None.

        Returns:
            float: the total amount saved
        """
        user = self.user
        transaction_qs = Transaction.objects.filter(
            user=user,
            onboarded_user__isnull=False,
            transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
            status=Status.SUCCESS,
            plan_type=PlanType.AJO,
        )
        ajo_users_qs = self.ajo_users_qs

        if month:
            month_start = timezone.make_aware(datetime.strptime(month, "%Y-%m"))
            savings_query = transaction_qs.filter(
                transaction_date_completed__year=month_start.year,
                transaction_date_completed__month=month_start.month,
            )
            total_saving = savings_query.aggregate(total=Sum("amount"))["total"]
            total_cash_saved = total_saving or 0

        elif complete_date:
            date_to_filter = timezone.make_aware(
                datetime.strptime(complete_date, "%Y-%m-%d")
            ).date()
            savings_query = transaction_qs.filter(
                transaction_date_completed__date=date_to_filter
            )
            total_saving = savings_query.aggregate(total=Sum("amount"))["total"]
            total_cash_saved = total_saving or 0

        else:
            total_saving = ajo_users_qs.aggregate(total_saved=Sum("total_money_saved"))[
                "total_saved"
            ]
            total_cash_saved = total_saving or 0

        return total_cash_saved

    def get_total_withdrawal(
        self,
        query: bool = False,
        month: str | None = None,
        complete_date: str | None = None,
    ) -> float | QuerySet[Transaction]:
        user = self.user
        transaction_qs = Transaction.objects.filter(
            user=user,
            onboarded_user__isnull=False,
            transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
            status=Status.SUCCESS,
            plan_type=PlanType.AJO,
        )
        ajo_user_qs = self.ajo_users_qs

        if month:
            month_start = timezone.make_aware(datetime.strptime(month, "%Y-%m"))
            withdrawal_query = transaction_qs.filter(
                transaction_date_completed__year=month_start.year,
                transaction_date_completed__month=month_start.month,
            )
            total_withdrawal = withdrawal_query.aggregate(total=Sum("amount"))["total"]
            total_cash_withdrawn = total_withdrawal or 0

        elif complete_date:
            date_to_filter = timezone.make_aware(
                datetime.strptime(complete_date, "%Y-%m-%d")
            ).date()
            withdrawal_query = transaction_qs.filter(
                transaction_date_completed__date=date_to_filter
            )
            total_withdrawal = withdrawal_query.aggregate(total=Sum("amount"))["total"]
            total_cash_withdrawn = total_withdrawal or 0

        else:
            total_withdrawal = ajo_user_qs.aggregate(
                total_withdrawn=Sum("total_money_withdrawn")
            )["total_withdrawn"]
            total_cash_withdrawn = total_withdrawal or 0

        if query:
            return withdrawal_query
        else:
            return total_cash_withdrawn

    def get_total_withdrawal_count(
        self,
        month: str | None = None,
        complete_date: str | None = None,
    ) -> int:
        ajo_user_qs = self.ajo_users_qs

        if month:
            withdrawal_qs = self.get_total_withdrawal(query=True, month=month)
            withdrawal_count = withdrawal_qs.count()

        elif complete_date:
            withdrawal_qs = self.get_total_withdrawal(
                query=True, complete_date=complete_date
            )
            withdrawal_count = withdrawal_qs.count()

        else:
            withdrawal_count = ajo_user_qs.aggregate(
                no_of_withdrawals=Sum("total_withdrawals")
            )["no_of_withdrawals"]

        return withdrawal_count

    def get_transaction_history_summary_for_agent(
        self,
        month: str | None = None,
        complete_date: str | None = None,
        plans_only: bool = False,
    ) -> QuerySet[Transaction]:

        transaction_qs = Transaction.objects.filter(
            user=self.user,
            plan_type__in=[PlanType.AJO, PlanType.ROTATIONGROUP],
        ).select_related("onboarded_user")

        if month:
            month_start = timezone.make_aware(datetime.strptime(month, "%Y-%m"))
            transactions = transaction_qs.filter(
                date_created__year=month_start.year,
                date_created__month=month_start.month,
            )
        elif complete_date:
            date_to_filter = timezone.make_aware(
                datetime.strptime(complete_date, "%Y-%m-%d")
            ).date()
            transactions = transaction_qs.filter(date_created__date=date_to_filter)
        else:
            transactions = transaction_qs

        if plans_only:
            transactions = transactions.filter(quotation_id__isnull=False)

        return transactions.order_by("-id")

    def get_quotas(self) -> None:
        """
        set class attributes for AjoSepo methods
        """
        constants = ConstantTable.get_constant_table_instance()
        self.INDIVIDUAL = "INDIVIDUAL"
        self.GROUP = "GROUP"
        self.INDIVIDUAL_NUMBER = constants.individual_quota
        self.GROUP_NUMBER = constants.group_quota

    def get_last_set_of_loan_savings(self) -> QuerySet[AjoSaving]:
        savings = AjoSaving.objects.filter(
            user=self.user,
            loan=True,
            ajo_user__isnull=False,
            is_loan_repayment=False,
        ).order_by("-id")

        return savings

    def determine_next_savings_type(self) -> str:
        """
        Gets if the next savings type for an agent is
        INDIVIDUAL or GROUP

        Returns:
            str: "INDIVIDUAL" | "GROUP"
        """
        last_set_of_loan_savings = self.get_last_set_of_loan_savings()
        last_set_of_loan_savings = last_set_of_loan_savings.filter(
            created_at__gte=timezone.make_aware(timezone.datetime(2024, 8, 14))
        )

        savings_count = last_set_of_loan_savings.count()

        self.get_quotas()
        individual_number = self.INDIVIDUAL_NUMBER
        group_number = self.GROUP_NUMBER
        total_quota_number = individual_number + group_number

        if savings_count < individual_number:
            return self.INDIVIDUAL

        # Fetch only the relevant records or all if less than required
        relevant_savings = list(
            last_set_of_loan_savings[: min(savings_count, total_quota_number)]
        )

        individual_count = sum(
            1
            for savings in relevant_savings
            if savings.group is None and not savings.must_belong_to_group
        )

        if individual_count >= individual_number:
            return self.GROUP
        else:
            return self.INDIVIDUAL

        # # Check the last individual slice savings instances
        # if savings_count >= total_quota_number:
        #     total_quota_slice = last_set_of_loan_savings[:total_quota_number]
        #     individual_count = 0
        #     for savings in total_quota_slice:
        #         if savings.group is None and not savings.must_belong_to_group:
        #             individual_count += 1

        #     if individual_count > individual_number:
        #         return self.GROUP
        #     else:
        #         return self.INDIVIDUAL

        # last_set = last_set_of_loan_savings[:individual_number]
        # if all(savings.group is None and not savings.must_belong_to_group for savings in last_set):
        #     return self.GROUP

        # if savings_count >= group_number:
        #     last_group_set = last_set_of_loan_savings[:group_number]
        #     if all(savings.group is not None and savings.must_belong_to_group for savings in last_group_set):
        #         return self.INDIVIDUAL

        # for savings in last_set_of_loan_savings:
        #     if savings.group is None and not savings.must_belong_to_group:
        #         individual_count += 1

        #     if individual_count > individual_number:
        #         return self.GROUP
        #     else:
        #         return self.INDIVIDUAL

        # # Default
        # return self.GROUP

    def get_available_individual_savings(self) -> int:
        last_set_of_loan_savings = self.get_last_set_of_loan_savings()
        last_set_of_loan_savings = last_set_of_loan_savings.filter(
            created_at__gte=timezone.make_aware(timezone.datetime(2024, 8, 14))
        )

        if self.determine_next_savings_type() == self.GROUP:
            return 0

        savings_count = last_set_of_loan_savings.count()
        group_number = self.GROUP_NUMBER
        total_quota_number = self.INDIVIDUAL_NUMBER + group_number

        relevant_savings = list(
            last_set_of_loan_savings[: min(savings_count, total_quota_number)]
        )

        individual_count = sum(
            1
            for savings in relevant_savings
            if savings.group is None and not savings.must_belong_to_group
        )

        return max(self.INDIVIDUAL_NUMBER - individual_count, 0)

        # if savings_count < group_number:
        #     individual_savings = last_set_of_loan_savings.filter(group__isnull=True, must_belong_to_group=False)
        #     individual_savings_count = individual_savings.count()
        #     return max(self.INDIVIDUAL_NUMBER - individual_savings_count, 0)

        # else:
        #     last_set = last_set_of_loan_savings[:group_number]
        #     individual_count = 0
        #     for savings in last_set:
        #         if not savings.group and not savings.must_belong_to_group:
        #             individual_count += 1
        #     return max(self.INDIVIDUAL_NUMBER - individual_count, 0)

    def is_next_loan_savings_group(self) -> bool:
        """
        Checks if the next savings is going to be a group savings

        Returns:
            bool: True/False
        """
        if self.determine_next_savings_type() == self.GROUP:
            return True
        return False

    def get_ajosepo_groups(self) -> QuerySet[AjoSepo]:
        """
        Get all the AjoSepo groups that belong to a user

        Returns:
            QuerySet[AjoSepo]: the queryset of ajosepo groups
        """
        groups = AjoSepo.objects.filter(user=self.user)
        return groups

    def get_number_of_ajosepo_groups(self) -> int:
        """
        Get the number of ajosepo groups an agent has

        Returns:
            int: the number of ajosepo groups
        """
        return self.get_ajosepo_groups().count()


class AjoSavingsSelector:
    def __init__(
        self,
        id: int | None = None,
        user: AbstractUser | None = None,
        ajo_savings: AjoSaving | None = None,
    ) -> None:
        """
        Initiliaze the class

        Args:
            id (int): the id of the ajo savings plan
            user (AbstractUser): the user to whom the plan is related
            ajo_savings(AjoSaving): the ajo savings instance
        """
        type_error = "invalid combination of arguments. Please provide either ajo savings object OR id and user"
        if ajo_savings:
            if id or user:
                raise TypeError(type_error)

            self.ajo_saving_plan = ajo_savings

        elif id and user:

            try:
                self.ajo_saving_plan = AjoSaving.objects.get(
                    id=id,
                    user=user,
                    ajo_user__isnull=False,
                )
            except AjoSaving.DoesNotExist:
                self.ajo_saving_plan = None

        else:
            raise TypeError(type_error)

        self.user = user if user else ajo_savings.user

    def get_ajo_saving_plan(self) -> AjoSaving:
        """
        Retrieve the ajo savings plan

        Raises:
            ValueError: "ajo plan does not exist"

        Returns:
            AjoSaving: the ajo savings plan instance
        """

        if self.ajo_saving_plan:
            return self.ajo_saving_plan
        else:
            raise ValueError("ajo plan does not exist for this user")

    def get_ajo_saving_transaction_history(self) -> QuerySet[Transaction]:
        """
        Retrieves a Transaction Queryset that belongs to an ajo savings plan

        Returns:
            QuerySet[Transaction]: a queryset of Transaction instances
        """
        self.get_ajo_saving_plan()
        return Transaction.objects.filter(
            Q(quotation_id=self.ajo_saving_plan.quotation_id)
            & Q(wallet_type=WalletTypes.AJO_USER)
            & ~Q(transaction_form_type="COMMISSION")
        ).order_by("-id")

    def get_amount_saved_in_plan(self) -> float:
        """
        Retrieves the amount of money saved in an
        ajo saving plan

        Returns:
            float: the amount saved in the plan
        """
        return self.get_ajo_saving_plan().amount_saved

    def get_ajo_savings_transaction_history_within_date_range(
        self,
        start_date: datetime,
        end_date: datetime,
    ) -> QuerySet[Transaction]:
        """
        Retrieves a Transaction Queryset that belongs to an ajo savings plan

        Returns:
            Queryset[Transaction]: a queryset of transactions.
        """
        self.get_ajo_saving_plan()

        start_date = timezone.make_aware(start_date)
        end_date = timezone.make_aware(end_date)
        # to make the end date inclusive
        end_date += timezone.timedelta(days=1)

        transactions = Transaction.objects.filter(
            Q(quotation_id=self.ajo_saving_plan.quotation_id)
            & Q(wallet_type=WalletTypes.AJO_USER)
            & Q(date_created__range=[start_date, end_date])
        ).order_by("-id")

        return transactions

    def milestone_reached_for_snpl(self) -> bool:
        self.get_ajo_saving_plan()
        ajo_savings = self.ajo_saving_plan
        if not ajo_savings.savings_type == SavingsType.SNPL:
            raise ValueError("this savings is not SNPL")

        constants = ConstantTable.get_constant_table_instance()
        milestone_percentage = constants.snpl_savings_milestone
        milestone_percentage = milestone_percentage / 100

        milestone = ajo_savings.expected_amount * milestone_percentage
        if ajo_savings.amount_saved >= milestone:
            return True

        return False

    def check_conditions_for_group_withdrawal(self) -> bool:
        if (
            self.ajo_saving_plan.savings_type == SavingsType.AJOSEPO
            or self.ajo_saving_plan.must_belong_to_group
        ):
            if self.ajo_saving_plan.group is not None:
                raise ValueError("this savings plan is part of a group")

        return True

    def check_conditions_for_savings_type_withdrawal(self) -> bool:
        boosta_plan = ["BOOSTA", "BOOSTA_2X", "BOOSTA_2X_MINI"]
        if self.ajo_saving_plan.savings_type is not None:
            if self.ajo_saving_plan.savings_type == SavingsType.AJOSEPO:
                self.check_conditions_for_group_withdrawal()

            elif self.ajo_saving_plan.savings_type not in boosta_plan:
                raise ValueError(
                    "this plan cannot be withdrawn because of its savings type"
                )

    def check_for_locked_savings_withdrawal_conditions(self) -> bool:
        savings_plan = self.ajo_saving_plan
        ajo_savings_date_created = savings_plan.created_at

        if savings_plan.frequency == SavingsFrequency.DAILY:
            if savings_plan.lock and is_past_month_year(
                target_month=ajo_savings_date_created.month,
                target_year=ajo_savings_date_created.year,
            ):
                raise ValueError("this is a locked ajo plan")
        else:
            if savings_plan.lock and (
                savings_plan.maturity_date > timezone.localdate()
            ):
                raise ValueError("this is a locked ajo plan")

    def check_withdrawal_conditions_for_savings_plans(self):
        if self.ajo_saving_plan.withdrawn:
            raise ValueError("this plan has already been withdrawn")

        self.check_for_locked_savings_withdrawal_conditions()
        self.check_conditions_for_savings_type_withdrawal()

    @classmethod
    def aggregate_amount_saved_in_active_ajo_plans(cls, ajo_user: AjoUser) -> float:
        ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)

        active_savings = ajo_user_selector.get_ajo_user_ajo_savings(is_active=True)

        return round(
            active_savings.aggregate(total=Sum("amount_saved"))["total"] or 0, 2
        )

    @classmethod
    def get_closed_savings(cls, ajo_user: AjoUser) -> QuerySet[AjoSaving]:
        ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
        return ajo_user_selector.get_ajo_user_ajo_savings(is_active=False)

    @classmethod
    def aggregate_amount_saved_in_closed_not_withdrawn_plans(
        cls, ajo_user: AjoUser
    ) -> float:
        closed_savings: QuerySet[AjoSaving] = cls.get_closed_savings(ajo_user=ajo_user)
        not_withdrawn_closed_savings = closed_savings.filter(withdrawn=False)

        return round(
            not_withdrawn_closed_savings.aggregate(total=Sum("amount_saved"))["total"]
            or 0,
            2,
        )


class CardSelector:
    def __init__(self, card_number: str, user: AbstractUser) -> None:
        self.user = user
        try:
            self.card = Card.objects.get(card_number=card_number, user=user)
        except Card.DoesNotExist:
            self.card = None

    def get_card_instance(self) -> Card:
        """
        Returns the card instance from the DB at instantation

        Raises:
            ValueError: "card not found"

        Returns:
            Card: the instance of the card
        """
        if self.card:
            return self.card
        else:
            raise ValueError("card not found")

    def check_if_card_assigned(self) -> bool:
        if self.card.ajo_user is not None:
            return self.card.card_assigned or True
        else:
            return self.card.card_assigned or False


class RotationGroupSelector:
    def __init__(
        self,
        group_id: str | None = None,
        user: AbstractUser = None,
        id: int | None = None,
        quotation_id: str | None = None,
    ) -> None:
        """
        Args:
            group_id (str | None, optional): group id. Defaults to None.
            user (AbstractUser | None, optional): user instance. Defaults to None.
            id (int | None, optional): the id of the rotation group instance. Defaults to None.
            quotation_id (str | None, optional): the quotation id of the rotation group instance. Defaults to None.

        Raises:
            ValueError: please provide either 1. 'id' and 'user' or 2. 'group_id'
        """
        try:
            if id and user:
                # Retrieve rotation group using id and user
                rotation_group = RotationGroup.objects.prefetch_related("members").get(
                    id=id, user=user
                )
            elif group_id:
                # Retrieve rotation group using group id
                rotation_group = RotationGroup.objects.prefetch_related("members").get(
                    group_id=group_id
                )
            elif quotation_id:
                rotation_group = RotationGroup.objects.prefetch_related("members").get(
                    quotation_id=quotation_id
                )
            else:
                raise ValueError(
                    "please provide 1. 'id' and 'user' or 2. 'group_id' or 3. 'quotation_id'"
                )
        except RotationGroup.DoesNotExist:
            rotation_group = None

        self.rotation_group = rotation_group

    def get_rotation_group(self) -> RotationGroup:
        """
        Retrieve the rotation group

        Raises:
            ValueError: rotation group not found

        Returns:
            RotationGroup: rotation group instance
        """
        if not self.rotation_group:
            raise ValueError("rotation group not found")

        return self.rotation_group

    def available_postions(self) -> List[int]:
        """
        This returns a list of the positions that are vacant

        Returns:
            List[int]: the list of vacant positions available in the group
        """
        self.get_rotation_group()

        taken_positions = self.rotation_group.members.values_list("position", flat=True)
        return [
            position
            for position in range(1, self.rotation_group.number_of_participants + 1)
            if position not in taken_positions
        ]

    def validate_number_of_participants(self):
        if (
            self.rotation_group.number_of_participants
            < self.rotation_group.members.count()
        ):
            raise ValueError(
                "number of participants must be greater than or equal to the number of members"
            )

    def calculate_next_payment_day(self) -> datetime | None:
        """This calculates the next payment date

        Returns:
            datetime | None: the next payment day as datetime.date object
        """
        frequency = self.rotation_group.frequency
        starting_date = self.rotation_group.starting_date
        today = timezone.localtime().today()
        frequency_deltas = {
            SavingsFrequency.DAILY: relativedelta(days=1),
            SavingsFrequency.WEEKLY: relativedelta(weeks=1),
            SavingsFrequency.MONTHLY: relativedelta(
                months=1
            ),  # Using 30 days for approximate months
        }

        # check if the starting date is ahead
        if today.date() <= starting_date:
            next_payment_date = starting_date

        elif frequency in frequency_deltas:
            delta = frequency_deltas[frequency]
            next_payment_date = starting_date + delta

            while next_payment_date < today.date():
                if next_payment_date > (
                    starting_date + relativedelta(days=self.rotation_group.duration)
                ):
                    return None

                next_payment_date += delta

        else:
            next_payment_date = None

        return next_payment_date

    def generate_day_intervals(self) -> Dict[int, datetime]:
        """
        This returns a dictionary of all the collection dates according to the frequency,
        starting date and duration.

        Raises:
            ValueError: invalid frequency.

        Returns:
            Dict[int, datetime]: a dictionary with an integer as key and a datetime object as value.
        """
        start_date = self.rotation_group.starting_date
        duration = self.rotation_group.duration
        frequency = self.rotation_group.frequency
        day_intervals = {}

        current_date = start_date
        frequency_number = 1
        while current_date < (start_date + relativedelta(days=duration)):
            # day_intervals[current_date.strftime('%Y-%m-%d')] = None
            day_intervals[frequency_number] = current_date
            if frequency == SavingsFrequency.DAILY:
                current_date += relativedelta(days=1)
            elif frequency == SavingsFrequency.WEEKLY:
                current_date += relativedelta(weeks=1)
            elif frequency == SavingsFrequency.MONTHLY:
                current_date += relativedelta(months=1)
            else:
                raise ValueError("Invalid frequency")

            frequency_number += 1

        return day_intervals

    def get_total_collection_frequency(self) -> int:
        """
        Get the total frequency of the rotation savings.

        Raises:
            ValueError: Invalid Frequency.

        Returns:
            int: the total collection frequency.
        """
        start_date = self.rotation_group.starting_date
        duration = self.rotation_group.duration
        frequency = self.rotation_group.frequency

        current_date = start_date
        total_frequency = 0

        while current_date < (start_date + relativedelta(days=duration)):
            if frequency == SavingsFrequency.DAILY:
                current_date += relativedelta(days=1)
            elif frequency == SavingsFrequency.WEEKLY:
                current_date += relativedelta(weeks=1)
            elif frequency == SavingsFrequency.MONTHLY:
                current_date += relativedelta(months=1)
            else:
                raise ValueError("Invalid frequency")
            total_frequency += 1

        return total_frequency

    @staticmethod
    def find_key_by_date(
        date_to_find: timezone.datetime | None,
        date_dict: Dict[int, timezone.datetime],
    ) -> int | None:
        """
        This is a helper method that finds if a date is in a dictionary of integer as key
        and datetime as value AS key-value pairs, then returning the key.

        Args:
            date_to_find (datetime): the date you are looking for.
            date_dict (Dict[int, timezone.datetime]): the dictionary.

        Returns:
            int | None: returns the key or None.
        """
        if not date_to_find:
            return None

        for key, date in date_dict.items():
            if date == date_to_find:
                return key
        return None  # Return None if the date is not found in the dictionary

    frequency_translations = {
        SavingsFrequency.DAILY: "days",
        SavingsFrequency.WEEKLY: "weeks",
        SavingsFrequency.MONTHLY: "months",
    }

    def get_current_frequency(self) -> int:
        """
        Obtain the current day, week or month over the total period
        for a rotation group

        Returns:
            int: the current frequency that group is in.
        """

        # get information about group
        group = self.get_rotation_group()

        max_frequency = group.number_of_participants

        # get current date
        current_date = timezone.localdate()

        next_payment_date = self.calculate_next_payment_day()

        if not next_payment_date:
            return max_frequency

        else:
            frequency_for_next_payment_date = self.find_key_by_date(
                date_to_find=next_payment_date,
                date_dict=self.generate_day_intervals(),
            )

            if not frequency_for_next_payment_date:
                return max_frequency

            if current_date <= next_payment_date:
                return frequency_for_next_payment_date
            else:
                return frequency_for_next_payment_date + 1

    def is_user_a_member(self, user) -> bool:
        """
        Checks if a user is a member of a group

        Args:
            user (AbstractUser): an instance of the User model

        Returns:
            bool: True | False
        """
        return self.rotation_group.members.filter(user=user).exists()

    def get_filled_positions(self) -> List[int]:
        """
        Returns the filled positions of a rotation group

        Returns:
            List[int]: a list of filled positions
        """
        members = self.get_group_members().order_by("position")
        positions_filled = members.values_list("position", flat=True)
        return list(positions_filled)

    def get_group_information(self) -> Dict[str, Any]:
        """
        Returns a dictionary containing all the information of the group.

        Returns:
            Dict[str, Any]: the group information.
        """
        self.get_rotation_group()

        frequency = self.rotation_group.frequency
        starting_date = self.rotation_group.starting_date
        number_of_participants = self.rotation_group.number_of_participants
        determine_duration = PersonalAjoUtils.determine_duration(
            frequency=frequency,
            participants=number_of_participants,
            starting_date=starting_date,
        )
        next_charge_day = self.calculate_next_payment_day()
        if next_charge_day:
            next_charge_day = next_charge_day.strftime("%A")
        else:
            next_charge_day = "Ended"

        group_info = {
            "id": self.rotation_group.id,
            "group_id": self.rotation_group.group_id,
            "user": self.rotation_group.user.email,
            "name": self.rotation_group.name,
            "number_of_participants": number_of_participants,
            "contribution_amount": self.rotation_group.contribution_amount,
            "frequency": frequency,
            "charging_day": next_charge_day,
            "duration_in_days": self.rotation_group.duration,
            "converted_duration": determine_duration.get("converted_duration"),
            "starting_date": starting_date,
            "end_date": self.rotation_group.end_date,
            "fees": self.rotation_group.fees,
            "admin_commission": self.rotation_group.admin_fee,
            "collection_amount": self.rotation_group.collection_amount,
            "is_active": self.rotation_group.is_active,
            "status": self.rotation_group.status,
        }

        members = self.rotation_group.members.order_by("position")
        # positions_filled = [member.position for member in members]
        positions_filled = self.get_filled_positions()
        positions_vacant = self.available_postions()

        group_info["positions_filled"] = positions_filled
        group_info["positions_vacant"] = positions_vacant
        group_info["group_filled"] = members.count() == number_of_participants

        return group_info

    # def get_collecting_member_position(self) -> int | None:
    #     frequency = self.rotation_group.frequency
    #     members = self.rotation_group.members
    #     starting_date = self.rotation_group.starting_date

    #     if frequency == SavingsFrequency.DAILY:
    #         # For daily frequency, calculate the number of days passed and determine the collecting member
    #         days_passed = (timezone.localtime().date() - starting_date).days
    #         position = (days_passed % members.count()) + 1
    #         return position if position > 0 else None
    #     elif frequency == SavingsFrequency.WEEKLY:
    #         # For weekly frequency, calculate the number of weeks passed and determine the collecting member
    #         weeks_passed = (timezone.localtime().date() - starting_date).days // 7
    #         position = (weeks_passed % members.count()) + 1
    #         return position if position > 0 else None
    #     elif frequency == SavingsFrequency.MONTHLY:
    #         # For monthly frequency, calculate the number of months passed and determine the collecting member
    #         months_passed = relativedelta(timezone.localtime().date(), starting_date).months
    #         position = (months_passed % members.count()) + 1
    #         return position if position > 0 else None
    #     else:
    #         return None

    def get_collecting_member_position(self) -> int | None:
        """
        This returns the position of the person meant to collect next based
        on the day.

        Returns:
            int | None: either the position or None.
        """
        frequency = self.rotation_group.frequency
        participants = self.rotation_group.number_of_participants
        starting_date = self.rotation_group.starting_date
        next_payment_date = self.calculate_next_payment_day()

        if not next_payment_date:
            return None

        if frequency == SavingsFrequency.DAILY:
            # For daily frequency, calculate the number of days passed and determine the collecting member
            frequency_passed = (next_payment_date - starting_date).days
        elif frequency == SavingsFrequency.WEEKLY:
            # For weekly frequency, calculate the number of weeks passed and determine the collecting member
            frequency_passed = (next_payment_date - starting_date).days // 7
        elif frequency == SavingsFrequency.MONTHLY:
            # For monthly frequency, calculate the number of months passed and determine the collecting member
            frequency_passed = relativedelta(next_payment_date, starting_date).months
        else:
            return None

        # print("frequency: ", frequency)
        # print("frequency passed: ", frequency_passed)
        position = frequency_passed + 1
        # print("participants: ", participants)
        # print("position: ", position)
        return position if position > 0 else None

    @classmethod
    def get_user_rotation_groups(
        cls,
        user: AbstractUser,
    ) -> QuerySet[RotationGroup]:
        """
        Returns all the Rotation Groups a user has created

        Args:
            user (AbstractUser): _description_

        Returns:
            QuerySet[RotationGroup]: _description_
        """
        rotation_groups = RotationGroup.objects.filter(
            user=user,
        )
        return rotation_groups

    def get_group_members(self) -> QuerySet[RotationGroupMember]:
        """
        Retrieve a QuerySet of all the group members

        Returns:
            QuerySet[RotationGroupMember]: A QuerySet of all the group members
        """
        self.get_rotation_group()

        members: QuerySet[RotationGroupMember] = self.rotation_group.members.all()

        return members

    def get_number_of_group_members(self) -> int:
        """
        Retrieves the number of group members

        Returns:
            int: the number
        """
        return self.get_group_members().count()

    def can_group_start(self) -> bool:
        """
        Checks if group has the conditions to start

        Returns:
            bool: True/False
        """
        today = timezone.localtime().date()
        return (
            self.get_number_of_group_members()
            == self.rotation_group.number_of_participants
        ) and (self.rotation_group.starting_date <= today)

    def has_started(self) -> bool:
        """
        Checks if the rotation group has started

        Returns:
            bool: True/False
        """
        group = self.get_rotation_group()

        return group.status == RoscaGroupStatus.RUNNING

    def get_withdrawal_count(self) -> int:
        """
        Obtains the withdrawal count from members of the group

        Returns:
            int: the withdrawal count
        """
        group = self.get_rotation_group()

        withdrawal_count = group.members.filter(withdrawn=True).count()
        return withdrawal_count

    def get_transaction_history(self) -> QuerySet[Transaction]:
        """
        Retrieves a Transaction Queryset that belongs to an Rotation Group

        Returns:
            QuerySet[Transaction]: a Queryset of transaction instances
        """
        group = self.get_rotation_group()

        transactions = Transaction.objects.filter(
            Q(quotation_id=group.quotation_id)
            & Q(wallet_type=WalletTypes.ROSCA_PERSONAL)
            & Q(transaction_form_type=TransactionFormType.COMMISSION)
        ).order_by("-id")

        return transactions

    def have_all_members_completed_payments(self) -> bool:
        """
        Checks if none of the members is owing payment

        Returns:
            bool: True/False
        """
        members = self.get_group_members()

        for member in members:
            if len(RotationGroupMemberSelector.get_unpaid_positions(member)) > 0:
                return False

        return True

    def get_contribution_count(self) -> int:
        """
        Get the number of contributions for a particular frequency

        Returns:
            int: the contribution count value
        """
        if self.has_started():

            current_frequency = self.get_current_frequency()
            members = self.get_group_members()

            count = 0
            for member in members:
                if current_frequency in RotationGroupMemberSelector.paid_positions(
                    member
                ):
                    count += 1

        else:
            count = 0

        return count


class AjoCommissionsSelector:
    def __init__(self, user: AbstractUser) -> None:
        self.user = user
        self.commission_qs = Commission.objects.filter(
            user=user,
            plan_type=PlanType.AJO,
        )

    def get_commissions_wallet(self) -> WalletSystem:
        """
        Returns the commissions wallet of a user

        Returns:
            WalletSystem: the ajo commissions wallet
        """
        return WalletSystem.get_or_create_wallet(
            user=self.user,
            wallet_type=WalletTypes.AJO_COMMISSION,
        )

    def get_commissions_available_balance(self) -> float:
        """
        returns the available balance in the ajo commissions wallet

        Returns:
            float: the available balance in the ajo commissions wallet
        """
        return self.get_commissions_wallet().available_balance

    def get_list_of_commissions(self) -> List[Commission]:
        """
        A queryset of all the commissions belonging to a user

        Returns:
            List[Commission]: QuerySet of Commissions
        """
        return self.commission_qs

    def get_list_of_not_withdrawn_commissions(self) -> List[Commission]:
        """
        Retrieves the queryset of commissions that have not been withdrawn

        Returns:
            List[Commission]: Queryset of Not Withdrawn Commissions
        """
        return self.commission_qs.filter(withdrawn=False)

    def get_total_amount_of_commissions_not_withdrawn(self) -> float:
        """
        Returns the sum total of not withdrawn commissions

        Returns:
            float: the total
        """
        return round(
            self.get_list_of_not_withdrawn_commissions().aggregate(total=Sum("amount"))[
                "total"
            ]
            or 0,
            2,
        )


class LoanSelector:
    def __init__(self, ajo_user: AjoUser):
        self.ajo_user = ajo_user
        self.user = ajo_user.user
        self.loan_qs = Loan.objects.filter(ajo_user=ajo_user)

    def get_active_loan(self) -> Loan:
        """
        This obtains the active incomplete loan of an ajo user

        Raises:
            ValueError: no active loan at the moment

        Returns:
            Loan: the active loan model instance
        """
        try:
            active_loan = self.loan_qs.get(completed=False)
        except Loan.DoesNotExist:
            raise ValueError("no active loan at the moment")
        return active_loan

    def get_ajo_user_loan_wallet(self) -> WalletSystem:
        """
        This retrieves the ajo user's loan wallet

        Returns:
            WalletSystem: the Loan wallet of the ajo user
        """
        return WalletSystem.get_or_create_wallet(
            user=self.user,
            wallet_type=WalletTypes.AJO_LOAN,
            ajo_user=self.ajo_user,
        )

    def get_list_of_loans(self, descending_order: bool) -> List[Loan]:
        """
        This returns the QuerySet of loans belonging
        to an ajo user

        Returns:
            List[Loan]: the queryset of loans
        """
        if descending_order:
            return self.loan_qs.order_by("-id")
        return self.loan_qs


class BankAccountSelector:
    def __init__(
        self,
        user: AbstractUser | None = None,
        ajo_user: AjoUser | None = None,
    ):
        self.user = user
        self.ajo_user = ajo_user

    def get_agent_account_instance(self) -> BankAccountDetails:
        """
        This retrieves the latest account details for
        an ajo agent
        """
        # # define cache key
        # cache_key = f"vfd_account_{self.user.customer_user_id}"
        # # Try to retrieve the cached data
        # account_instance = cache.get(cache_key)

        # if not account_instance:
        #     get_multiplier = BankAccountDetails.objects.filter(user=self.user)
        #     if get_multiplier:
        #         account_details = get_multiplier.latest("updated_at")
        #     else:
        #         pass
        #     pass

        agent_bank_acct_qs = BankAccountDetails.objects.filter(
            user=self.user,
            ajo_user__isnull=True,
            form_type=AccountFormType.AGENT,
            is_active=True,
        )
        if not agent_bank_acct_qs:
            raise ValueError(
                "this user does not have a virtual account, please request for one."
            )

        return agent_bank_acct_qs.last()

    @classmethod
    def check_for_account_number(
        cls, account_number: str, bank_name: str = "VFD"
    ) -> Optional[BankAccountDetails]:
        # provider = [
        #     "VFD",
        #     "WEMA",
        #     "CASH_CONNECT",
        # ]
        return BankAccountDetails.objects.filter(
            account_number=account_number,
            # bank_name__icontains=bank_name,
            # account_provider__in=provider,
            is_active=True,
        ).first()

    def get_ajo_user_account_details(
        self, form_type: AccountFormType
    ) -> BankAccountDetails:
        if not self.ajo_user:
            raise ValueError("no ajo user")

        account_detail_qs = BankAccountDetails.objects.filter(
            ajo_user=self.ajo_user,
            form_type=form_type,
            is_active=True,
        )

        if not account_detail_qs.exists():
            error_string = (
                lambda x: f"this ajo user does not have {x} account, please request for one."
            )

            if form_type == AccountFormType.AJO_SPENDING:
                raise ValueError(error_string("a spending"))

            elif form_type == AccountFormType.AJO_DIGITAL:
                raise ValueError(error_string("a digital"))

            elif form_type == AccountFormType.ONLENDING_MAIN:
                raise ValueError(error_string("an onlending main"))

            elif form_type == AccountFormType.LOAN_REPAYMENT:
                raise ValueError(error_string("a loan repayment"))
            else:
                raise ValueError("not a valid account form type")

        return account_detail_qs.last()

    def get_user_account_details(
        self, form_type: AccountFormType
    ) -> BankAccountDetails:
        if form_type in [AccountFormType.AJO_SPENDING, AccountFormType.AJO_DIGITAL]:
            raise ValueError("user not meant to have this type of bank account")

        account_detail_qs = BankAccountDetails.objects.filter(
            user=self.user,
            ajo_user__isnull=True,
            form_type=form_type,
            is_active=True,
        )

        if not account_detail_qs.exists():
            if form_type == AccountFormType.ONLENDING_MAIN:
                raise ValueError(
                    "this user does not have an onlending main account, please request for one."
                )

            elif form_type == AccountFormType.AGENT:
                raise ValueError(
                    "this user does not have an ajo agent account, please request for one."
                )

            else:
                raise ValueError("not a valid account form type")

        return account_detail_qs.last()


class PrefundingSelector:
    def __init__(self, user: AbstractUser):
        self.user = user

    def get_prefunding_wallet(self) -> WalletSystem:
        """
        Obtain the prefunding wallet of an agent

        Returns:
            WalletSystem: the retrieved prefunding wallet
        """
        return WalletSystem.get_or_create_wallet(
            user=self.user,
            wallet_type=WalletTypes.AJO_PREFUNDING,
        )

    def check_if_there_is_outstanding_in_prefunding_wallet(self) -> bool:
        """
        This checks if there is an outstanding balance in the
        hold balance of the prefunding wallet

        Returns:
            bool: True or False
        """
        return self.get_prefunding_wallet().hold_balance > 0

    def available_balance_in_prefunding_wallet(self) -> float:
        """
        This returns how much is in the available balance
        of the prefunding wallet

        Returns:
            float: the amount
        """
        return self.get_prefunding_wallet().available_balance

    def can_available_balance_cover_payment(self, amount: float) -> bool:
        """
        This checks if the available balance can cover the
        payment about to be made

        Args:
            amount(float): the amount to be checked against

        Returns:
            bool: True/False
        """
        return self.available_balance_in_prefunding_wallet() >= amount

    def check_how_much_outstanding_left(self) -> float:
        """
        This checks how much outstanding is left in the hold
        balance of the prefunding wallet

        Returns:
            float: the amount remaining
        """
        return self.get_prefunding_wallet().hold_balance

    def retrieve_active_prefunding_instances(self) -> List[Prefunding]:
        """
        Checks if there is any active prefunding instance

        Returns:
            List[Prefunding]: Returns a Queryset of Active Prefunding instances
        """
        return Prefunding.objects.filter(
            Q(completed=False) | Q(balance_left__gt=0),
            user=self.user,
        )

    def check_if_there_is_any_active_prefunding_instance(self) -> bool:
        """
        checks if there is any active prefunding instance and returns a True if
        yes and False if none

        Returns:
            bool: True/False
        """
        return self.retrieve_active_prefunding_instances().exists()

    def get_the_latest_prefunding_instance(self) -> Prefunding:
        """
        Retrieves the latest prefunding instance

        Returns:
            Prefunding: the latest prefunding instance
        """
        return self.retrieve_active_prefunding_instances().last()

    def how_much_prefunding_has_been_used_against_hold_balance(self) -> float:
        """
        Checks how much prefunding has been used against how much is being owed in the
        hold balance. This amount signifies how much is really owed by the agent to
        the company

        Returns:
            float: the amount
        """
        available_balance = self.available_balance_in_prefunding_wallet()
        hold_balance = self.check_how_much_outstanding_left()

        if hold_balance > available_balance:
            return round(hold_balance - available_balance, 2)

        return 0.0


class PersonalSelector:
    def __init__(self, user: AbstractUser):
        """
        Instantiate the class with the user

        Args:
            user (AbstractBaseUser): the user.
        """
        self.user = user

    def get_user_ajo_savings(
        self, is_active: Optional[bool] = None
    ) -> QuerySet[AjoSaving]:
        """
        Get a queryset of AjoSaving belonging to a user

        Args:
            is_active(Optional[bool]): filter the ajo savings by True, False or None to get the ajo savings

        Returns:
            QuerySet[AjoSaving]: A queryset of ajo savings.
        """

        ajo_savings = AjoSaving.objects.filter(
            user=self.user,
            ajo_user__isnull=True,
        )

        if is_active is not None:
            ajo_savings = ajo_savings.filter(is_active=is_active)

        ajo_savings = ajo_savings.order_by("-id")

        return ajo_savings

    def get_number_of_active_ajo_savings(self) -> int:
        """
        This returns the number active ajo savings a user has

        Returns:
            int: the number of active savings
        """
        return self.get_user_ajo_savings(is_active=True).count()

    def get_savings_wallet(self) -> WalletSystem:
        """
        This retrieves the user's personal ajo wallet instance

        Returns:
            WalletSystem: the wallet of the user
        """
        wallet = WalletSystem.get_or_create_wallet(
            user=self.user,
            wallet_type=WalletTypes.AJO_PERSONAL,
        )

        return wallet

    def get_ajo_user_wallet_balance(self) -> float:
        """
        Returns how much an ajo user has in his savings wallet

        Returns:
            float: the amount of money in the available balance
        """
        return self.get_savings_wallet().available_balance

    def generate_and_set_wallet_number(
        self, wallet: Optional[WalletSystem] = None
    ) -> str:
        """
        Generates and sets a wallet number for any wallet instance.
        If no wallet instance is specified, it will perform the operation for the
        personal ajo savings wallet of the user.

        Args:
            wallet (Optional[WalletSystem], optional): a wallet you want this done for. Defaults to None.

        Returns:
            str: the wallet number.
        """
        if not wallet:
            wallet = self.get_savings_wallet()

        if not wallet.wallet_number:
            number = generate_default_code()
            while WalletSystem.objects.filter(
                wallet_number=number,
            ).exists():
                number = generate_default_code()

            wallet.wallet_number = number
            wallet.save()

        return wallet.wallet_number

    def get_rotation_groups_transactions(self) -> QuerySet[Transaction]:
        """
        Get all rotation group transactions that concerns the user

        Returns:
            QuerySet[Transaction]: queryset of transactions
        """

        transactions = Transaction.objects.filter(
            (
                (
                    Q(description__icontains=self.user.email)
                    & Q(wallet_type=WalletTypes.ROSCA_PERSONAL)
                )
                | (Q(wallet_type=WalletTypes.ROSCA_PERSONAL) & Q(user=self.user))
            )
            & ~Q(
                transaction_form_type=TransactionFormType.COMMISSION,
            )
        )
        return transactions

    def get_personal_ajo_transactions(self) -> QuerySet[Transaction]:
        """
        Get the personal ajo transaction history
        """

        transactions = Transaction.objects.filter(
            Q(user=self.user) & Q(wallet_type=WalletTypes.AJO_PERSONAL)
        )
        return transactions

    def get_personal_user_transaction_history(
        self,
        month: str | None = None,
    ) -> QuerySet[Transaction]:
        """
        Retrieves a Transaction queryset that belongs to the Personal Ajo User.

        Args:
            month (str | None): the month string which must be 'YYYY-MM'. Defaults to None.

        Returns:
            QuerySet[Transaction]: Queryset of Transactions.
        """

        rosca_transactions = self.get_rotation_groups_transactions()
        personal_ajo_transactions = self.get_personal_ajo_transactions()

        transactions = rosca_transactions | personal_ajo_transactions
        transactions = transactions.distinct()
        # transactions = Transaction.objects.filter(
        #     user=self.user,
        #     wallet_type=WalletTypes.AJO_PERSONAL,
        # )

        if month:
            month_start = timezone.datetime.strptime(month, "%Y-%m")
            transactions = transactions.filter(
                date_created__year=month_start.year,
                date_created__month=month_start.month,
            )

        return transactions.order_by("-id")


class PersonalAjoSavingsSelector:
    def __init__(self, id: int, user: AbstractUser) -> None:
        """
        Initialize the class.

        Args:
            id (int): the id of the ajo savings plan.
            user (AbstractUser): the user to who owns the plan.
        """

        self.user = user
        try:
            self.ajo_savings_plan = AjoSaving.objects.get(
                id=id,
                user=user,
                ajo_user__isnull=True,
            )
        except AjoSaving.DoesNotExist:
            self.ajo_savings_plan = None

    def get_ajo_savings_plan(self) -> AjoSaving:
        """
        Retrieve the ajo savings plan.

        Raises:
            ValueError: ajo plan does not exist.

        Returns:
            AjoSaving: the ajo savings plan instance.
        """

        if not self.ajo_savings_plan:
            raise ValueError("ajo plan does not exist")

        return self.ajo_savings_plan

    def get_ajo_savings_transaction_history(
        self,
        month: str | None = None,
    ) -> QuerySet[Transaction]:
        """
        Retrieves a Transaction queryset that belongs to an ajo savings plan.

        Args:
            month (str | None): the month string which must be 'YYYY-MM'. Defaults to None.

        Returns:
            QuerySet[Transaction]: Queryset of Transactions.
        """
        self.get_ajo_savings_plan()

        transactions = Transaction.objects.filter(
            Q(quotation_id=self.ajo_savings_plan.quotation_id)
            & Q(wallet_type=WalletTypes.AJO_PERSONAL)
        )

        if month:
            month_start = timezone.datetime.strptime(month, "%Y-%m")
            transactions = transactions.filter(
                date_created__year=month_start.year,
                date_created__month=month_start.month,
            )

        return transactions.order_by("-id")

    def get_amount_saved_in_plan(self) -> float:
        """
        Retrieves the amount of money saved in an ajo savings plan

        Returns:
            float: the amount saved in the plan
        """
        return self.get_ajo_savings_plan().amount_saved


class RotationGroupMemberSelector:
    def __init__(self, member: RotationGroupMember | None = None):
        self.member = member
        self.group = member.group

    @classmethod
    def position_in_group(
        cls, group: RotationGroup, position: int
    ) -> RotationGroupMember | None:
        """
        Returns the Member occupying a position in the group

        Args:
            group (RotationGroup): the Rotation Group instance
            position (int): the position you want to check

        Raises:
            ValueError: position does not exist in this group

        Returns:
            RotationGroupMember: the retrieved group member
        """
        if not position <= group.number_of_participants:
            raise ValueError(f"position {position} does not exist in this group")

        try:
            return RotationGroupMember.objects.get(group=group, position=position)
        except RotationGroupMember.DoesNotExist:
            return None

    @classmethod
    def get_rotation_group_wallet(cls, user: AbstractUser) -> WalletSystem:
        wallet = WalletSystem.get_or_create_wallet(
            user=user,
            wallet_type=WalletTypes.ROSCA_PERSONAL,
        )
        return wallet

    def ensure_there_is_member(self) -> None:
        """
        This is a method to ensure a member has been passed when
        instantiating the class

        Raises:
            ValueError: "please instantiate the class with a member instance"
        """
        if not self.member:
            raise ValueError("please instantiate the class with a member instance")

    def get_set_member_withdrawal_status(self) -> bool:
        """
        Attempts to get or set the withdrawal status of a member
        (if everyone in the group has paid to a member)

        Returns:
            bool: True/False
        """
        self.ensure_there_is_member()

        if not self.member.withdrawn:
            # check if the number of credit transactions for the group to the user
            # is equal to the number of participants
            credit_transactions_count = Transaction.objects.filter(
                user=self.member.user,
                wallet_type=WalletTypes.ROSCA_PERSONAL,
                quotation_id=self.group.quotation_id,
                transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
                status=Status.SUCCESS,
            ).count()

            if credit_transactions_count >= self.group.number_of_participants:
                self.member.withdrawn = True
                self.member.save()

        return self.member.withdrawn

    @classmethod
    def get_all_rotation_groups_as_member(
        cls, user: AbstractUser
    ) -> QuerySet[RotationGroup]:
        """
        Returns all the Rotation Groups a user is a member of.

        Args:
            user (AbstractUser): the user who is a member of groups

        Returns:
            QuerySet[RotationGroup]: the groups the member belongs to.
        """
        rotation_group_ids: QuerySet[int] = (
            RotationGroupMember.objects.select_related("group")
            .filter(
                user=user,
            )
            .values_list(
                "group",
                flat=True,
            )
        )

        rotation_groups: QuerySet[RotationGroup] = RotationGroup.objects.filter(
            id__in=rotation_group_ids
        )

        return rotation_groups

    @classmethod
    def paid_positions(cls, member: RotationGroupMember) -> List[int]:
        if member.paid_positions:
            return [int(pos) for pos in member.paid_positions.split(",") if pos]
        else:
            return []

    @classmethod
    def get_unpaid_positions(cls, member: RotationGroupMember) -> List[int]:
        """
        This returns the list of frequencies that a rotation group member has not paid.

        Args:
            member(RotationGroupMember): the rotation group member.

        Returns:
            List[int]: the list of unpaid frequencies.
        """
        if not member.paid_positions:
            return list(range(1, member.group.number_of_participants + 1))

        max_position = int(member.group.number_of_participants)
        all_positions = set(range(1, max_position + 1))
        paid_positions = set(map(int, member.paid_positions.split(",")))
        unpaid_positions = list(all_positions - paid_positions)
        return sorted(unpaid_positions)

    @classmethod
    def get_unpaid_positions_below_next_payment(
        cls,
        group: RotationGroup,
        member: RotationGroupMember,
    ) -> List[int]:
        group_selector = RotationGroupSelector(group_id=group.group_id)

        unpaid_positions = cls.get_unpaid_positions(member=member)

        next_payment_date = group_selector.calculate_next_payment_day()

        next_position = RotationGroupSelector.find_key_by_date(
            date_to_find=group_selector.calculate_next_payment_day(),
            date_dict=group_selector.generate_day_intervals(),
        )

        if not next_payment_date or not next_position:
            return unpaid_positions

        new_unpaid_positions = [
            position for position in unpaid_positions if position < next_position
        ]

        return new_unpaid_positions

    @classmethod
    def get_position_payment_status(
        cls,
        member: RotationGroupMember,
    ) -> Dict[int, bool | None]:
        """
        Returns a dictionary of the positions for payment
        and if the member has paid or not

        Args:
            member (RotationGroupMember): the member instance

        Returns:
            Dict[int, bool | None]: _description_
        """

        paid_positions = cls.paid_positions(member=member)
        unpaid_positions_below_next_payment = (
            cls.get_unpaid_positions_below_next_payment(
                group=member.group,
                member=member,
            )
        )
        unpaid_positions_above_next_payment = [
            x
            for x in cls.get_unpaid_positions(member=member)
            if x not in unpaid_positions_below_next_payment
        ]

        position_status = {x: True for x in paid_positions}
        position_status.update({x: False for x in unpaid_positions_below_next_payment})
        position_status.update({x: None for x in unpaid_positions_above_next_payment})
        position_status = dict(sorted(position_status.items()))

        return position_status


class ProfileChangeSelector:
    def __init__(
        self,
        ajo_user: AjoUser | None = None,
    ) -> None:
        """_summary_

        Args:
            user (get_user_model, optional): The user involved. Defaults to None.
            ajo_user (AjoUser | None, optional): the ajo user who wants to change details. Defaults to None.
        """
        if not ajo_user:
            raise TypeError("please pass the ajo_user as an argument")

        self.ajo_user = ajo_user

    def get_all_change_requests(self) -> QuerySet[ProfileChangeRequest]:
        """
        All the Profile Change Requests an ajo user has made.

        Returns:
            QuerySet[ProfileChangeRequest]: the queryset for all.
        """
        return ProfileChangeRequest.objects.filter(
            ajo_user=self.ajo_user,
        )

    def get_active_change_requests(self) -> QuerySet[ProfileChangeRequest]:
        """
        Active Change Requests

        Returns:
            QuerySet[ProfileChangeRequest]: the Queryset of active requests
        """
        return self.get_all_change_requests().filter(is_active=True)

    def get_failed_active_change_requests(self) -> QuerySet[ProfileChangeRequest]:
        """
        Active Failed Requests

        Returns:
            QuerySet[ProfileChangeRequest]: the QuerySet of Failed Active Requests
        """
        return self.get_active_change_requests().filter(
            status=ProfileChangeStatus.FAILED
        )

    def is_there_an_active_failed_request(self) -> bool:
        """
        Returns if there is an active failed request as a boolean

        Returns:
            bool: True/False
        """
        return self.get_failed_active_change_requests().exists()

    def get_latest_change_request(self) -> ProfileChangeRequest | None:
        """
        The latest active change request

        Returns:
            ProfileChangeRequest | None: the active instance
        """
        return self.get_active_change_requests().order_by("-created_at").first()

    def can_user_edit_profile(self) -> bool:
        """
        If there is an active change request and the change request status is successful
        the user can edit his profile.

        Returns:
            bool: True/False
        """
        change_request = self.get_latest_change_request()

        if change_request:
            if change_request.status == ProfileChangeStatus.SUCCESSFUL:
                return True

        return False

    def get_all_unused_paid_requests(self) -> QuerySet[ProfileChangeRequest]:
        """
        All the Profile Change Requests that have been paid for but not used

        Returns:
            QuerySet[ProfileChangeRequest]: The QuerySet of the description above.
        """
        return ProfileChangeRequest.objects.filter(
            ajo_user=self.ajo_user,
            session_id__isnull=False,
            status__isnull=True,
            is_active=True,
        )

    def get_latest_unused_paid_request(self) -> ProfileChangeRequest | None:
        """
        The latest unused paid change request

        Returns:
            ProfileChangeRequest: the unused paid request
        """
        return self.get_all_unused_paid_requests().order_by("-created_at").first()


class CardRequestSelector:
    def __init__(self, card_id: int) -> None:
        """
        Pass the 'card_id' to instantiate the class

        Args:
            card_id (int): the card ID.
        """
        self.card_id = card_id

    def get_card_request_instance(self) -> CardRequests:
        """
        Get the card request instance for the card ID

        Raises:
            ValueError: this card does not exist or is not active

        Returns:
            CardRequests: the CardRequests instance.
        """

        try:
            card_instance = CardRequests.objects.get(
                card_id=self.card_id,
                is_frozen=False,
                ajo_user__isnull=False,
            )

        except CardRequests.DoesNotExist as err:
            raise ValueError("this card does not exist or is not active")

        return card_instance

    def get_ajo_user_from_card_instance(self) -> AjoUser:
        """
        Get the ajo user from the card instance

        Returns:
            AjoUser: the ajo user that the card belongs to.
        """
        return self.get_card_request_instance().ajo_user


class AjoSepoSelector:
    INDIVIDUAL = "INDIVIDUAL"
    GROUP = "GROUP"

    def __init__(
        self,
        group: AjoSepo | None = None,
        group_id: str | None = None,
        user: AbstractUser | None = None,
    ) -> None:
        if group:
            self.group = group
        elif group_id and user:
            self.group_query = AjoSepo.objects.filter(
                group_id=group_id,
                user=user,
            )
            self.group = self.group_query.last()
        else:
            raise TypeError(
                "initialize class with 'group' OR with 'group_id' and 'user'"
            )

    def get_group(self) -> AjoSepo:
        if not self.group:
            raise ValueError("this group does not exist for this user")
        return self.group

    def can_group_be_activated(self) -> bool:
        """
        Checks if a group's is_activated and is_active fields can be turned on
        """
        savings_count = self.group.savings.count()
        if savings_count < self.group.min_members:
            return False
        return True

    def participants(self) -> QuerySet[AjoSaving]:
        """
        The participants

        Returns:
            QuerySet[AjoSaving]: the participants in a group
        """
        members = self.group.savings
        return members

    def number_of_participants(self) -> int:
        """
        The number of participants in a group

        Returns:
            int: the number
        """
        number = self.participants().count()
        return number

    def is_max_number_of_participants_reached(self) -> bool:
        """
        Checks if the group has reached it's max number of participants

        Returns:
            bool: True if it has, else False
        """

        if self.number_of_participants() > self.group.max_members:
            return True
        return False

    def does_this_ajo_user_exist_in_group(self, ajo_user: AjoUser) -> bool:
        """
        if an ajo user already exists in group

        Returns:
            bool: True/False
        """

        members = self.participants()
        return members.filter(ajo_user=ajo_user).exists()

    def base_checks_for_group(self, loan: bool, ajo_user: AjoUser) -> None:
        """
        Performs a base check for group savings

        Args:
            loan (bool): True/False
            ajo_user (AjoUser): Ajo User
        """
        if not loan:
            raise ValueError("this is not an ajo loan savings plan")

        if self.is_max_number_of_participants_reached():
            raise ValueError("this group has reached its maximum")

        if self.does_this_ajo_user_exist_in_group(ajo_user=ajo_user):
            raise ValueError("this ajo user belongs to this group already")

    def all_savings_check_to_join_group(self, ajo_savings: AjoSaving) -> None:
        """
        Performs all the checks to join a group and raises a ValueError
        if the all conditions are not met

        Args:
            ajo_savings (AjoSaving): the ajo savings instance

        Raises:
            ValueError: this is not an ajo loan savings plan
            ValueError: this group has reached its maximum
            ValueError: this ajo user belongs to this group already
        """
        self.base_checks_for_group(loan=ajo_savings.loan, ajo_user=ajo_savings.ajo_user)
