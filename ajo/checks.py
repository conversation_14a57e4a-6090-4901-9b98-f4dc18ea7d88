from typing import Any, Dict, List, Optional

from rest_framework import status
from rest_framework.response import Response

from accounts.responses import value_error_response
from payment.utils import is_past_month_year

from . import payment_actions
from .models import AjoSaving
from .selectors import AjoUserSelector


class AjoChecks:

    @classmethod
    def ajo_plan_checks_before_withdrawal(cls, ajo_plan: AjoSaving, amount: float):
        if ajo_plan.lock and is_past_month_year(
            target_month=ajo_plan.created_at.month,
            target_year=ajo_plan.created_at.year,
        ):
            return Response(
                {
                    "status": False,
                    "error": "871",
                    "message": "this is a locked ajo plan",
                },
                status.HTTP_400_BAD_REQUEST,
            )

        if ajo_plan.withdrawn:
            return Response(
                {
                    "status": False,
                    "error": "877",
                    "message": "this plan has already been withdrawn",
                },
                status.HTTP_403_FORBIDDEN,
            )

        if ajo_plan.amount_saved <= 0:
            return Response(
                {
                    "status": False,
                    "error": "874",
                    "message": "there is no money to withdraw in this plan",
                },
                status.HTTP_403_FORBIDDEN,
            )

        else:
            if (ajo_plan.amount_saved < amount) or (
                AjoUserSelector(ajo_user=ajo_plan.ajo_user).get_ajo_user_wallet_balance() < amount
            ):
                return Response(
                    {
                        "status": False,
                        "error": "879",
                        "message": "insufficient funds to withdraw this amount",
                    },
                    status.HTTP_403_FORBIDDEN,
                )

        loan_checks = cls.loan_plan_checks(ajo_plan=ajo_plan)
        if isinstance(loan_checks, Response):
            return Response

        return None

    @classmethod
    def loan_plan_checks(cls, ajo_plan: AjoSaving) -> Optional[Response]:
        # check if it is a loan plan
        if ajo_plan.loan:
            # try to collect commission
            try:
                payment_actions.collect_loan_plan_commissions(
                    ajo_savings_id=ajo_plan.id,
                    user_id=ajo_plan.user.id,
                )

                amount = max(amount - ajo_plan.commission_amount, 0)

                if amount == 0:
                    raise ValueError("insufficient funds to debit")

            except ValueError as err:
                if "commission has been taken out already" in str(err):
                    pass
                else:
                    return value_error_response(error=err)

            except Exception as err:
                return value_error_response(error=err)

        return None
