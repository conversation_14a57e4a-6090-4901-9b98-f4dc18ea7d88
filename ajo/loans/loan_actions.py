from typing import Any, Dict, List

from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.core.cache import cache
from django.utils import timezone

from accounts.models import ConstantTable

from ..model_choices import SavingsFrequency
from ..models import AjoUser
from .loan_eligibility import calculate_loan_eligibility


class LoanActions:
    def __init__(self, user: AbstractUser) -> None:
        self.user = user

    def get_cache_key(self, ajo_user_id: int) -> str:
        return f"loan_eligibility_{ajo_user_id}"

    def get_loan_request_cache_key(self, ajo_user_id: int) -> str:
        return f"loan_request_{ajo_user_id}"

    def get_loan_text_cache_key(self, ajo_user_id: int) -> str:
        return f"loan_text_{ajo_user_id}"

    def get_the_eligibility_of_an_ajo_user(self, ajo_user: AjoUser) -> float:
        cached_data = cache.get(self.get_cache_key(ajo_user_id=ajo_user.id))
        if cached_data:
            return cached_data.get("eligible_loan_amount")

        return calculate_loan_eligibility(ajo_user=ajo_user)

    def get_all_cached_data_for_user(self) -> List[Dict[str, Any]]:
        # obtain the ajo_user ids under a particular user
        ajo_user_ids = AjoUser.objects.filter(user=self.user).values_list("id", flat=True)
        all_cached_data = []

        for ajo_user_id in ajo_user_ids:
            cache_key = self.get_cache_key(ajo_user_id=ajo_user_id)
            cached_data = cache.get(cache_key)

            if cached_data:
                all_cached_data.append(cached_data)

        return all_cached_data

    def generate_loan_details(self, loan_interest: float, loan_amount: float, duration: int) -> Dict[str, Any]:
        loan_repayment_amount = round(loan_amount * (1 + (loan_interest / 100)), 2)
        periodic_amount = round(loan_repayment_amount / duration, 2)

        return {
            "loan_amount": loan_amount,
            "repayment_frequency": SavingsFrequency.DAILY,
            "duration": duration,
            "due_date": timezone.localdate() + timezone.timedelta(days=30),
            "loan_interest_rate": loan_interest,
            "loan_repayment_amount": loan_repayment_amount,
            "periodic_amount": periodic_amount,
        }

    def request_loan(self, ajo_user: AjoUser, loan_amount: float, duration: int = 30) -> None:
        # obtain the cache key
        cache_key = self.get_loan_request_cache_key(ajo_user_id=ajo_user.id)

        # define the data
        loan_interest = ConstantTable.get_constant_table_instance().ajo_loan_interest_rate

        cache_data = {
            "ajo_user": ajo_user,
            **self.generate_loan_details(loan_interest=loan_interest, loan_amount=loan_amount, duration=duration),
        }

        cache.set(cache_key, cache_data, timeout=60 * 30)

    def get_cached_loan_request(self, ajo_user: AjoUser) -> Dict[str, Any] | None:
        # obtain the cache key
        cache_key = self.get_loan_request_cache_key(ajo_user_id=ajo_user.id)

        # try to get the data
        cached_data = cache.get(cache_key, None)

        return cached_data
