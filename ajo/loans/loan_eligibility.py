"""
There is the Ajo savings model called AjoSaving
it has created_at(datetimefield), duration(in days)(integerfield), completed_at(datetimefield), completed(boolean fields) fields,
I want to check if the user has had 2 completed plans in the past,
if they have had two completed ajo savings in the past,
I want to check if the user completed these savings at most 5 days after the duration (at most 5 days after the duration + created_at), 
if this is the case, I want the algorithm to get the average of the last two plans and set that 2/3 of the 
average is the how much loan the person is eligible to. 
When this calculated, the eligible amount of loan, the IDs of the savings plans should be cached for 3 days.
"""
from celery import shared_task
from django.core.cache import cache
from django.db.models import Count, OuterRef, Subquery, Sum
from django.utils import timezone

from ..models import AjoSaving, <PERSON>jo<PERSON><PERSON>


def calculate_loan_eligibility(ajo_user: AjoUser) -> float:
    ###THIS IS THE FIRST ATTEMPT AT THE ALGORITHM
    # Get the user's completed AjoSaving records
    # completed_savings = AjoSaving.objects.filter(user_id=user_id, completed=True).order_by('-completed_at')[:2]
    # if len(completed_savings) < 2:
    #     return 0  # User doesn't have enough completed savings plans

    ###THIS IS THE SECOND ATTEMPT AT THE ALGORITHM

    # Get the last 3 AjoSaving plans
    last_3_plans = AjoSaving.objects.filter(ajo_user=ajo_user).order_by("-created_at")[:3]

    # check if the ajo user does not have up to 3 AjoSaving plans
    # This means that the user is not eligible for a loan
    if not len(last_3_plans) == 3:
        return 0

    # get the three latest plans
    latest_plan = last_3_plans[0]
    second_latest_plan = last_3_plans[1]
    third_latest_plan = last_3_plans[2]

    # check if the latest_plan and the second_latest_plan were completed
    if latest_plan.completed and second_latest_plan.completed:
        # put them in a list
        completed_savings = [latest_plan, second_latest_plan]
    # if the first condition does not stand, check if
    # the second latest and third latest are completed
    elif second_latest_plan.completed and third_latest_plan.completed:
        # put them in a list
        completed_savings = [second_latest_plan, third_latest_plan]
    else:
        # they are not eligible for a loan
        return 0

    # Check if each savings plan was completed at most 5 days after the duration
    for saving in completed_savings:
        max_completed_date = saving.created_at + timezone.timedelta(days=saving.duration + 5)
        if saving.completed_at > max_completed_date:
            return 0  # One of the plans was completed too late, hence not eligible for loan

    # Calculate the average of the last two AjoSaving amounts
    average_amount = (completed_savings[0].expected_amount + completed_savings[1].expected_amount) / 2

    # Calculate the eligible loan amount (2/3 of the average amount)
    eligible_loan_amount = round((2 / 3) * average_amount, 2)

    # Cache the eligible amount of loan and the IDs of the savings plans
    cache_key = f"loan_eligibility_{ajo_user.id}"
    cached_data = {
        # information about the loan
        "eligible_loan_amount": eligible_loan_amount,
        "savings_plan_ids": [saving.id for saving in completed_savings],
        # information about the ajo user
        "ajo_user": {
            "full_name": ajo_user.fullname,
            "phone_number": ajo_user.phone_number,
        },
    }
    cache.set(cache_key, cached_data, timeout=3 * 24 * 60 * 60)  # Cache for 3 days

    return eligible_loan_amount



