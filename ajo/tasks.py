import calendar
import json
import os
from datetime import date, timed<PERSON>ta
from io import String<PERSON>
from string import Template
from typing import Any, Dict, List

import requests
from celery import shared_task
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.core.management import call_command
from django.db import IntegrityError
from django.db import transaction as django_transaction
from django.db.models import F, Q
from django.utils import timezone
from rest_framework.response import Response

from accounts.agency_banking import AgencyBankingClass
from accounts.models import (
    ConstantTable,
    FailuresLog,
    IDVerificationDumps,
    IDVerificationSources,
)
from accounts.paybox import PayBox
from ajo.model_choices import RoscaGroupStatus, RoscaType
from chestlock.selectors import OnlendingSelector
from health_insurance.models import SaveForHealthInsurance, SaveForHealthLogs
from health_insurance.models_choices import SaveHealthTypes, SavingsType
from health_insurance.tasks import request_savings_plan_creation
from loans.helpers.core_banking import CoreBankingManager
from loans.models import BorrowerInfo
from payment.model_choices import (
    DisbursementProviderType,
    Status,
    TransactionFormType,
    TransactionTypeCreditOrDebitChoices,
    WalletTypes,
)
from payment.models import Transaction, WalletSystem
from payment.tasks import settle_transfer_transaction

from .loans.loan_eligibility import calculate_loan_eligibility
from .model_choices import AccountFormType, AccountProvider, SavingsFrequency
from .models import (
    AjoSaving,
    AjoUser,
    BankAccountDetails,
    ProductInformation,
    RawFundingData,
    RotationGroup,
)
from .payment_actions import (
    dynamic_funding_of_ajo_plans_from_wallet,
    move_mature_funds_to_spending_for_ajo_user,
    resolve_pending_funding_agent_from_liberty_transaction,
)
from .prefunding.actions import PrefundingActions
from .selectors import (
    AjoAgentSelector,
    AjoSavingsSelector,
    AjoUserSelector,
    BankAccountSelector,
    PrefundingSelector,
    RotationGroupSelector,
)
from .services import BankAccountService, BankDepositService, PrefundingService
from .third_party import IdentityPass, TextMessages
from .utils.general_utils import remove_ansi_escape_codes, send_closing_ajo_plan_sms
from .utils.otp_utils import send_out_sms_for_ajo_payment, send_snpl_milestone_sms


################ AJO USER TASKS############################
@shared_task
def send_user_info_text(ajo_user_id: int) -> bool:
    """
    Sends the user information to the user and returns if
    it was sent successfully or not

    Args:
        ajo_user_id (int): the id of the ajo user that the text will be sent to

    Returns:
        bool: True or False depending if the text was sent
    """
    # obtain the ajo user
    ajo_user = AjoUser.objects.get(id=ajo_user_id)

    # send the message
    send_message = TextMessages.send_user_information_text(
        phone_number=ajo_user.phone,
        user_pin=ajo_user.pin,
    )

    if send_message.get("status"):
        ajo_user.sent_user_info_text = True
        ajo_user.save()
        return True

    return False


def provide_exception_message(user_email: str, phone_number: str):
    """
    This is a closure to set a message for a user's ajo user

    Args:
        user_email (str): the user
        phone_number (str): the phone number of the ajo user
    """

    def exception_message(message: str) -> str:
        """
        This is the exception message that will return from the closure

        Args:
            message (str): the message to be passed

        Returns:
            str: the exception message
        """
        return f"-> for {user_email}'s ajo user, {phone_number}: {message}"

    return exception_message


@shared_task
def verify_ajo_user_bvn(
    phone_number: str,
    user_id: int,
    base_url: str,
) -> None:
    """
    This verifies the BVN of an ajo user
    Do note that this function(task) does not check if
    a user exists or an ajo user exists
    for it assumes that these checks have already occured in
    the endpoint/view

    Args:
        bvn (str): the BVN of the ajo user
        phone_number (str): the phone number of the ajo user
        user_id (int): the id (user.id) of the user making the request

    Returns:
        None
    """
    try:
        # obtain the ajo user
        user = get_user_model().objects.get(id=user_id)
        ajo_user = AjoUserSelector(phone_number=phone_number, user=user).get_ajo_user()

        # get the url of the image
        image_url = base_url + ajo_user.image.url

        # call the third party class
        response = IdentityPass.bvn_with_face_verification(
            bvn=ajo_user.bvn,
            face_image_url=image_url,
        )

        # call the closure to provide context for Exceptions
        exc_message_func = provide_exception_message(user.email, phone_number)

        # if the BVN is found
        if response.get("status"):
            # obtain the face data information
            face_data = response.get("face_data")

            # if the face data did not match
            if not face_data.get("status"):
                raise Exception(exc_message_func(response.get("message")))

            # face data matches
            # update the field
            with django_transaction.atomic():

                ajo_user.bvn_verified = True
                ajo_user.save()

                IDVerificationDumps.objects.create(
                    ajo_user=ajo_user,
                    source=IDVerificationSources.IDENTITYPASS,
                    dump=json.dumps(response),
                )

            print(exc_message_func("BVN verified successfully"))

        # The BVN is not Found or an error in using the class request/call
        else:
            if "error_message" in response.keys():
                raise Exception(exc_message_func(response.get("error_message")))
            elif "message" in response.keys():
                raise Exception(exc_message_func(response.get("message")))
            else:
                raise Exception(exc_message_func(str(response)))

    except Exception as err:
        # print the error encountered during verification
        print(str(err))
        return  # abort this particular task


######################AJO LOAN TASKS###############################
@shared_task()
def calculate_the_loan_eligibility_of_all_ajo_users():
    """
    This task calculates the loan eligibility of all ajo users
    registered in the database
    """
    ajo_users = AjoUser.objects.all()

    for ajo_user in ajo_users:
        eligible_loan_amount = calculate_loan_eligibility(ajo_user=ajo_user)
        if eligible_loan_amount > 0:
            # TODO: call task to send a message
            pass


@shared_task
def create_agent_vfd_virtual_wallet(user_id: int):
    """
    CREATE VFD VIRTUAL WALLET FOR AGENT
    """
    try:
        user = get_user_model().objects.get(id=user_id)
    except get_user_model().DoesNotExist as err:
        return {
            "status": "failed",
            "message": f"this user, with user_id {user_id}, does not exist",
        }

    create_bank_account = BankAccountService.create_agent_virtual_account(
        user=user,
        form_type=AccountFormType.AGENT,
    )

    if create_bank_account.get("status") is False:
        create_bank_account["status"] = "failed"
        return create_bank_account

    create_bank_account["status"] = "success"
    return create_bank_account


@shared_task
def send_user_info_to_all_ajo_users():
    """
    send ajo user information to all ajo users' phone numbers

    Returns:
        bool: if it was sent or not (True/False)
    """
    ajo_users = AjoUser.objects.filter(sent_user_info_text=False)

    for ajo_user in ajo_users:
        send_message = TextMessages.send_user_information_text(
            phone_number=ajo_user.phone,
            user_pin=ajo_user.pin,
        )

        if send_message.get("status"):
            ajo_user.sent_user_info_text = True
            ajo_user.save()
            return True

        return False


@shared_task
def deposit_initial_prefunding(user_id: int) -> None:
    """
    Creates an initial agent prefunding for a new agent

    Args:
        user_id (int): the id of the user to give the prefunding

    Returns:
        None
    """
    try:
        user = get_user_model().objects.get(id=user_id)
    except get_user_model().DoesNotExist as err:
        return {
            "status": "failed",
            "message": f"this user, with user_id {user_id}, does not exist",
        }

    # obtain the prefunding constant
    prefunding_constant_amount = (
        ConstantTable.get_constant_table_instance().prefunding_amount
    )

    try:
        PrefundingService.create_prefunding(
            {
                "user": user,
                "prefunding_amount": prefunding_constant_amount,
                "balance_left": prefunding_constant_amount,
            }
        )

        PrefundingActions(user=user).deposit_prefunding_constant_into_wallet(
            prefunding_amount=prefunding_constant_amount,
        )

    except ValueError as err:
        return {
            "status": "failed",
            "error_message": str(err),
        }


# @shared_task
# def handle_mature_ajo_plans() -> Dict[str, str]:
#     """
#     This checks for all the ajo plans that have maturity dates on and before
#     the current day that have not been withdrawn, then performs the operation to move
#     their money from their savings(AJO_USER) wallet to their spending(AJO_SPENDING) wallet
#     and then inactivate the plans.
#     """
#     ajo_plans_qs = AjoSaving.objects.filter(
#         maturity_date__lte=timezone.localdate(),
#         withdrawn=False,
#         ajo_user__isnull=False,
#         is_active=True,
#     )
#     print("Mature Ajo plans: ", ajo_plans_qs)

#     for ajo_plan in ajo_plans_qs:
#         ajo_user_wallet = AjoUserSelector(ajo_user=ajo_plan.ajo_user).get_ajo_user_wallet()

#         # Check if there is amount saved and if the wallet holds the amount saved or more.
#         if (ajo_plan.amount_saved > 0) and (ajo_user_wallet.available_balance >= ajo_plan.amount_saved):
#             try:
#                 move_mature_funds_to_spending_for_ajo_user(ajo_plan=ajo_plan)
#             except Exception as err:
#                 return {
#                     "status": "failed",
#                     "error_message": str(err),
#                 }

#         else:
#             # deactivate the plan
#             ajo_plan.is_active = False
#             ajo_plan.withdrawn = True
#             ajo_plan.save()

#     return {
#         "status": "success",
#         "message": "all ajo plans have been treated",
#     }


def close_plan(ajo_plan: AjoSaving) -> Dict[str, str]:
    """close an individual plan"""
    ajo_user_wallet = AjoUserSelector(ajo_user=ajo_plan.ajo_user).get_ajo_user_wallet()

    if (
        ajo_plan.amount_saved > 0
        and ajo_user_wallet.available_balance >= ajo_plan.amount_saved
    ):
        try:
            move_mature_funds_to_spending_for_ajo_user(ajo_plan=ajo_plan)
            return {
                "status": "success",
                "message": f"For {ajo_plan.name}: funds moved to spend wallet and plan closed successfully",
            }
        except Exception as err:
            return {
                "status": "failed",
                "message": f"For {ajo_plan.name}: {str(err)}",
            }
    else:
        ajo_plan.is_active = False
        ajo_plan.withdrawn = True
        ajo_plan.save()
        return {
            "status": "success",
            "message": f"For {ajo_plan.name}: plan closed successfully",
        }


def generate_unique_name(base_name):
    suffix = 1
    new_name = base_name
    while AjoSaving.objects.filter(name=new_name).exists():
        new_name = f"{base_name} REPAY {suffix}"
        suffix += 1
    return new_name


def close_personal_plan(ajo_plan: AjoSaving) -> Dict[str, str]:
    """close personal ajo plan

    Args:
        ajo_plan (AjoSaving): the ajo plan

    Returns:
        Dict[str, str]: information
    """

    ajo_plan.is_active = False
    ajo_plan.save()

    return {
        "status": "success",
        "message": f"For {ajo_plan.name}: plan closed successfully",
    }


@shared_task
def close_daily_plans() -> str:
    # Get the current date
    current_time = timezone.localtime()
    plans_altered = []

    # Daily plans except March 2024 plans
    daily_plans_at_maturity_date = AjoSaving.objects.filter(
        maturity_date__lt=current_time.date(),
        frequency=SavingsFrequency.DAILY,
        is_active=True,
        savings_type__isnull=True,
    ).exclude(
        created_at__month=3,
        created_at__year=2024,
    )

    for ajo_plan in daily_plans_at_maturity_date:
        if ajo_plan.ajo_user:
            # if not ajo_plan.loan:
            #     close_plan_response = close_plan(ajo_plan=ajo_plan)
            # else:
            #     ajo_plan.is_active = False
            #     ajo_plan.save()
            #     close_plan_response = f"For {ajo_plan.name}: plan closed successfully"

            close_plan_response = close_plan(ajo_plan=ajo_plan)
        else:
            close_plan_response = close_personal_plan(ajo_plan=ajo_plan)
        plans_altered.append(close_plan_response)

    plans_created_in_march_2024 = AjoSaving.objects.filter(
        created_at__month=3,
        created_at__year=2024,
        frequency=SavingsFrequency.DAILY,
        is_active=True,
    )
    for ajo_plan in plans_created_in_march_2024:
        created_at_plus_thirty = ajo_plan.created_at + timedelta(days=30)
        if created_at_plus_thirty < current_time:
            if ajo_plan.ajo_user:
                # if not ajo_plan.loan:
                #     close_plan_response = close_plan(ajo_plan=ajo_plan)
                # else:
                #     ajo_plan.is_active = False
                #     ajo_plan.save()
                #     close_plan_response = f"For {ajo_plan.name}: plan closed successfully"

                close_plan_response = close_plan(ajo_plan=ajo_plan)
            else:
                close_plan_response = close_personal_plan(ajo_plan=ajo_plan)
            plans_altered.append(close_plan_response)

    return {
        "status": "successful",
        "data": f"the following plans were affected: {plans_altered}",
    }


@shared_task
def close_weekly_monthly_plans() -> str:
    """
    Close Weekly and Monthly Ajo Plans that
    have passed maturity date

    Returns:
        str: the list of ajo plans closed
    """
    # get current date
    current_date = timezone.localtime()

    plans_closed = []

    # get all weekly and monthly ajo plans have passed maturity date
    mature_ajo_plans = AjoSaving.objects.filter(
        ~Q(frequency=SavingsFrequency.DAILY),
        maturity_date__lt=current_date.date(),
        is_active=True,
        savings_type__isnull=True,
    )

    for ajo_plan in mature_ajo_plans:
        if ajo_plan.ajo_user:
            close_plan_response = close_plan(ajo_plan=ajo_plan)
        else:
            # TODO: insert what to do for Personal Users
            close_plan_response = close_personal_plan(ajo_plan=ajo_plan)
        plans_closed.append(close_plan_response)

    return {
        "status": "successful",
        "data": f"results from operations: {plans_closed}",
    }


################### ROTATION GROUP TASKS###########################
@shared_task
def process_starting_date():
    """
    The aim of this task is to find groups that are beginning on the current day
    and then send an email to each Rotation Group member
    """
    today = timezone.localtime().date()
    rotation_groups = RotationGroup.objects.filter(
        starting_date=today, starting_date_email_sent=False
    )

    for rotation_group in rotation_groups:
        notify_members(rotation_group)
        rotation_group.starting_date_email_sent = True
        rotation_group.save()


def notify_members(rotation_group: RotationGroup) -> None:
    ###Implement the email or text notification logic here
    send_mail(
        subject="Rotation Group Starting Day",
        message="Today is the starting day for your Rotation Group!",
        from_email="<EMAIL>",
        recipient_list=[member.user.email for member in rotation_group.members.all()],
        fail_silently=False,
    )


def send_sms_alert_for_ajo_payment(ajo_savings_id: int, amount: float):
    """
    This task handles the SMS alert for ajo payment

    Args:
        ajo_savings_id (int): the ID of the ajo savings plan
        user_id (int): the user ID

    Raises:
        Exception: Any Exception experienced in the process
    """
    try:
        ajo_savings = AjoSaving.objects.select_related("ajo_user").get(
            id=ajo_savings_id
        )
        ajo_user = ajo_savings.ajo_user
        phone_number = ajo_user.phone_number
        first_name = ajo_user.first_name
        balance = ajo_savings.plan_balance_after

        send_out_sms_for_ajo_payment(
            ajo_user=ajo_user,
            phone_number=phone_number,
            first_name=first_name,
            amount=amount,
            balance=balance,
        )
    except:
        pass

    return


@shared_task
def update_agent_rosca_group():
    """
    Updates the groups that have reached their starting date and
    the number of contributors are completely added to the group.

    Returns:
        str: information of groups that were updated.
    """
    current_date = timezone.localtime().date()
    all_agent_rosca_groups = RotationGroup.objects.filter(
        starting_date=current_date,
        rosca_type=RoscaType.AGENT_ROSCA,
        status=RoscaGroupStatus.PENDING,
    )
    for group in all_agent_rosca_groups:
        group_total_count = group.ajo_members.all().count()
        if group.number_of_participants == group_total_count:
            group.is_active = True
            group.status = RoscaGroupStatus.RUNNING
            group.save()

    return "Group Updated"


# def update_starting_rosca_groups() -> str:
#     """
#     Updates the groups that have reached their starting date.

#     Returns:
#         str: information of groups that were updated.
#     """
#     current_date = timezone.localtime().date()
#     updated_group_ids = []

#     # Update groups that start on the current date
#     groups = RotationGroup.objects.filter(
#         starting_date=current_date,
#         status=RoscaGroupStatus.PENDING,
#         rosca_type=RoscaType.PERSONAL_ROSCA,
#     )
#     for group in groups:
#         group_selector = RotationGroupSelector(group_id=group.group_id)

#         if group_selector.can_group_start():
#             RotationGroupService.start_rotation_group(rotation_group=group)

#             updated_group_ids.append(group.group_id)

#     # groups.update(is_active=True, status=RoscaGroupStatus.RUNNING)
#     # updated_group_ids = list(groups.values_list("group_id", flat=True))

#     # Add any update specific to the rotation group type below

#     return f"Updated {len(updated_group_ids)} groups with IDs: {updated_group_ids}"


def update_ending_rosca_groups() -> str:
    """
    Updates the groups that have reached their end dates.

    #### NOTE:
    This closes groups two days later than their end date.

    Returns:
        str: information of groups that were updated.
    """
    # Calculate the date two days in the past
    current_date = timezone.now().date()
    target_date = current_date - timedelta(days=2)

    # Update the groups that need to be closed two days later
    groups = RotationGroup.objects.filter(
        is_active=True,
        status=RoscaGroupStatus.RUNNING,
        rosca_type=RoscaType.AGENT_ROSCA,
        end_date=target_date,
    )
    groups.update(is_active=False, status=RoscaGroupStatus.CLOSED)
    updated_group_ids = list(groups.values_list("group_id", flat=True))

    # Add any update specific to the rotation group type below

    return f"Updated {len(updated_group_ids)} groups with IDs: {updated_group_ids}"


@shared_task
def close_ending_personal_rotation_groups() -> str:
    """
    Closes groups that have reached their end date and met the condition to close

    Returns:
        str: the list of groups closed
    """

    current_date = timezone.localdate()

    closing_groups = RotationGroup.objects.filter(
        is_active=True,
        status=RoscaGroupStatus.RUNNING,
        rosca_type=RoscaType.PERSONAL_ROSCA,
        end_date__lte=current_date,
    )

    closed_groups: List[str] = []

    for group in closing_groups:
        if RotationGroupSelector(
            group_id=group.group_id
        ).have_all_members_completed_payments():
            group.status = RoscaGroupStatus.CLOSED
            group.is_active = False
            group.save()
            closed_groups.append(f"{group.name} {group.group_id}")

    return f"the following groups were closed: {closed_groups}"


@shared_task
def fund_ajo_plans_from_digital_wallet(ajo_user_id: int) -> Dict[str, str | List[str]]:
    """
    The first step is to get all the active ajo plans,
    Then it finds the ajo plan that has the smallest periodic amount,


    Args:
        ajo_user_id (int): the ID of the ajo user

    Returns:
        Dict[str, str | List[str]]: returned data.
    """

    try:
        ajo_user = AjoUser.objects.get(id=ajo_user_id)
    except AjoUser.DoesNotExist:
        return {
            "status": "failed",
            "message": f"could not retrieve any ajo user with id: {ajo_user_id}",
        }

    active_ajo_plans = AjoSaving.objects.filter(
        ajo_user=ajo_user,
        is_active=True,
        is_activated=True,
        periodic_amount__gt=0,
    )

    if not active_ajo_plans:
        return {
            "status": "failed",
            "message": "no active ajo plan at the moment",
        }

    # obtain the plan with the smallest periodic amount
    smallest_periodic_amount_plan = active_ajo_plans.order_by("periodic_amount").first()
    smallest_periodic_amount = smallest_periodic_amount_plan.periodic_amount

    # obtain the digital wallet
    digital_wallet = AjoUserSelector(ajo_user=ajo_user).get_digital_wallet()

    if (digital_wallet.available_balance <= 0) or (
        not digital_wallet.available_balance >= smallest_periodic_amount
    ):
        return {
            "status": "failed",
            "message": f"ajo user, {ajo_user.phone_number}, cannot fund any active ajo plan due to insufficient funds in digital wallet",
        }

    data_from_funding: List[str] = []

    # Initialize a counter to track iterations without funding any plan
    iterations_without_funding = 0
    MAX_ITERATIONS_WITHOUT_FUNDING = 2

    while (
        digital_wallet.available_balance >= smallest_periodic_amount
        and digital_wallet.available_balance > 0
    ):
        data = dynamic_funding_of_ajo_plans_from_wallet(
            ajo_plans=active_ajo_plans.order_by("frequency_paid"),
            ajo_user=ajo_user,
            wallet=digital_wallet,
        )

        data_from_funding.append(json.dumps(data))

        digital_wallet.refresh_from_db()

        if (
            digital_wallet.available_balance < smallest_periodic_amount
            or digital_wallet.available_balance <= 0
        ):
            break

        elif not data.get("data"):
            # Increment the counter if no plan was funded
            iterations_without_funding += 1
            # Check if the number of iterations without funding exceeds a threshold
            if iterations_without_funding >= MAX_ITERATIONS_WITHOUT_FUNDING:
                break

        else:
            # Reset the counter if a plan was funded
            iterations_without_funding = 0

    return {
        "status": "success",
        "data": data_from_funding,
    }


@shared_task
def ajo_users_with_digital_wallet_balance():
    """
    This is the task that will go through all the ajo users and check for who has money in their
    digital wallet and will trigger the funding of plans process

    #### UPDATED
    This task will now search for digital wallets with 500 or more in them (because our lowest plans start from 500)
    and trigger the ajo users who own the wallets to fund their different ajo plans
    """
    # ajo_users = AjoUser.objects.all()

    # for ajo_user in ajo_users:
    #     ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)

    #     if ajo_user_selector.get_digital_wallet_balance() > 0:
    #         fund_ajo_plans_from_digital_wallet.delay(ajo_user_id=ajo_user.id)

    digital_wallets_with_money = WalletSystem.objects.select_related(
        "onboarded_user"
    ).filter(
        onboarded_user__isnull=False,
        wallet_type=WalletTypes.AJO_DIGITAL,
        available_balance__gte=500.00,
    )

    ajo_users_ids = []

    for digital_wallet in digital_wallets_with_money:
        ajo_user = digital_wallet.onboarded_user
        fund_ajo_plans_from_digital_wallet.delay(ajo_user_id=ajo_user.id)
        ajo_users_ids.append(ajo_user.id)

    return {
        "status": "success",
        "message": f"triggered funding process for ajo users, with ids: {ajo_users_ids}.",
    }


@shared_task
def process_mature_personal_ajo_plans() -> Dict[str, str]:
    """
    This checks for all personal ajo plans that have reached their maturity dates
    """
    personal_ajo_plans = AjoSaving.objects.filter(
        maturity_date__lte=timezone.localdate(),
        is_active=True,
        ajo_user__isnull=False,
    )

    personal_ajo_plans.update(is_active=False)
    personal_ajo_names = list(personal_ajo_plans.values_list("name", flat=True))

    return {
        "status": "success",
        "message": f"personal ajo plans: {personal_ajo_names}",
    }


@shared_task
def settle_due_balance_from_agent_wallet() -> Dict[str, str]:
    # check for prefunding wallet with a hold balance
    prefunding_wallets_with_hold_balance = WalletSystem.objects.select_related(
        "user"
    ).filter(
        wallet_type=WalletTypes.AJO_PREFUNDING,
        hold_balance__gt=F("available_balance"),
    )

    response = {"message": "find the users affected as key-value pairs"}

    for prefunding_wallet in prefunding_wallets_with_hold_balance:
        ajo_agent_selector = AjoAgentSelector(user=prefunding_wallet.user)
        prefunding_selector = PrefundingSelector(user=prefunding_wallet.user)

        if not (
            ajo_agent_selector.get_agent_ajo_wallet_balance()
            >= prefunding_selector.how_much_prefunding_has_been_used_against_hold_balance()
        ):
            continue

        try:
            PrefundingActions(
                user=prefunding_wallet.user
            ).settle_prefunding_outstanding_from_agent_wallet()
            response[prefunding_wallet.user.email] = (
                "prefunding due balance settled successfully"
            )

        except ValueError as err:
            response[prefunding_wallet.user.email] = str(err)

    return response


@shared_task
def closing_ajo_plans_for_sms() -> str:
    """
    This checks if today is the second or third to the last day of the month,
    if it is, it goes into the database and queries all the ajo plans that were created
    this month. After it does that, it will send the sms to everyone that their plan will end
    on the last day of the month.
    """
    current_date = timezone.localtime()

    # check if today is the second to the last day of the month
    last_day_of_month = calendar.monthrange(current_date.year, current_date.month)[1]
    last_day_of_month_date = date(
        current_date.year, current_date.month, last_day_of_month
    )

    second_to_last_day_of_month = last_day_of_month_date - timedelta(days=1)
    third_to_last_day_of_month = last_day_of_month_date - timedelta(days=2)

    if current_date.date in [second_to_last_day_of_month, third_to_last_day_of_month]:
        ajo_plans_sent = []

        ajo_plans_created_this_month = AjoSaving.objects.filter(
            created_at__month=current_date.month,
            created_at__year=current_date.year,
            ajo_user__isnull=False,
            frequency=SavingsFrequency.DAILY,
            is_active=True,
        )

        for ajo_plan in ajo_plans_created_this_month:
            send_closing_ajo_plan_sms(
                ajo_savings_plan=ajo_plan,
                end_date=last_day_of_month_date,
            )

            ajo_plans_sent.append(ajo_plan.id)

        return f"messages were sent to ajo users with these plan IDs: {ajo_plans_sent}"

    return "it is not the second or third to the last day of the month yet"


@shared_task
def update_successful_transfers_and_reversals():
    """
    This task will find external transfers and update successful ones and perform reversals
    """

    ten_minutes_before = timezone.now() - timezone.timedelta(minutes=1)
    ten_days_ago = timezone.now() - timezone.timedelta(days=10)

    external_transactions = Transaction.objects.filter(
        transaction_form_type__in=[
            TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT,
            TransactionFormType.DISBURSE_TO_EXTERNAL_ACCOUNT,
            TransactionFormType.FUND_MONNIFY_ACCOUNT,
        ],
        # onboarded_user__isnull=False,
        status__in=[Status.PENDING, Status.IN_PROGRESS],
        date_created__lte=ten_minutes_before,
        date_created__gte=ten_days_ago,
    )

    settlement_info = {}
    for transaction in external_transactions:
        settlement = settle_transfer_transaction(transaction_id=transaction.id)
        settlement_info[transaction.id] = settlement

    transaction_list = list(external_transactions.values_list("id", flat=True))
    return {
        "status": "success",
        "message": f"settlement task ran on this list of transactions, {transaction_list}",
        "data": str(settlement_info),
    }


@shared_task
def run_moveclosedfunds_command(number: str | None = None, email: str | None = None):
    stdout = StringIO()
    result = call_command(
        "moveclosedfunds",
        number=number,
        email=email,
        stdout=stdout,
    )

    result = stdout.getvalue()
    stdout.close()

    result = remove_ansi_escape_codes(text_with_colors=result)
    return result


@shared_task
def sweep_to_repay_loan():
    pass


def obtain_phone_number(access_token: str, user_id: int) -> str | Exception:
    user_details = AgencyBankingClass.get_user_info(
        access_token=access_token, user_id=user_id
    )
    # print(user_details)
    if user_details.get("status") is True:
        phone_number = user_details.get("data").get("phone_number")
    else:
        raise Exception("error obtaining user details")
    return phone_number


@shared_task
def general_send_email(
    recipient: str,
    subject: str,
    template_dir: str,
    use_template=True,
    body=None,
    **substitute,
):
    if use_template:
        TEMPLATE_DIR = os.path.join("templates", f"{template_dir}")
        html_temp = os.path.abspath(TEMPLATE_DIR)

        with open(html_temp) as temp_file:
            template = temp_file.read()

        template = Template(template).safe_substitute(substitute)
    else:
        template = None
    try:
        requests.post(
            f"{settings.MAILGUN_URL}",
            auth=("api", f"{settings.MAILGUN_API_KEY}"),
            data={
                "from": "Liberty Pay <<EMAIL>>",
                "to": f"<{recipient}>",
                "subject": f"{subject}",
                "html": f"""{template}""" if use_template else None,
                "text": body if not use_template else None,
            },
        )

        return "EMAIL SENT"
    except Exception as e:
        print("failed to send email", e)
        return "EMAIL FAILED"


@shared_task
def settle_bank_deposit_transactions() -> Dict[int, str]:
    from .views.callback_views import perform_bank_account_funding

    pending_bank_transactions = Transaction.objects.filter(
        transaction_form_type=TransactionFormType.BANK_DEPOSIT,
        status=Status.PENDING,
        transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
    )
    results = {}

    for bank_transaction in pending_bank_transactions:
        reference = bank_transaction.unique_reference

        try:
            raw_funding = RawFundingData.objects.get(reference=reference)
        except RawFundingData.DoesNotExist:
            results[bank_transaction.id] = "transaction does not have raw funding data"
            continue

        try:
            request_data = eval(raw_funding.payload)
            if "AGENCY" in raw_funding.source:
                bank_name = "VFD"
                account_number = request_data.get("account_number")
            else:
                bank_name = "WEMA"
                account_number = request_data.get("recipient_account_number")
            account_instance = BankAccountSelector.check_for_account_number(
                account_number=account_number,
                bank_name=bank_name,
            )

        except Exception as err:
            results[bank_transaction.id] = f"encountered an error: {err}"
            continue

        if "AGENCY" in raw_funding.source:
            res = AgencyBankingClass.verify_agency_banking_for_vfd_funding(
                liberty_reference=reference,
            )

            if not res.get("status"):
                results[bank_transaction.id] = "unable to verify transaction"
                continue

            if (
                res.get("data", {}).get("status") == "SUCCESSFUL"
                and res.get("data", {}).get("transaction_type")
                in ["FUND_COLLECTION_ACCOUNT", "FUND_BANK_TRANSFER"]
                and res.get("data", {}).get("liberty_reference") == reference
                and res.get("data", {}).get("amount") == bank_transaction.amount
            ):

                response = perform_bank_account_funding(
                    user=bank_transaction.user,
                    unique_reference=reference,
                    account_instance=account_instance,
                    amount=bank_transaction.amount,
                    raw_funding=raw_funding,
                )

                if isinstance(response, Response):
                    results[bank_transaction.id] = response.data.get("message")
                    continue

                results[bank_transaction.id] = (
                    f"transaction settled in {bank_transaction.wallet_type} wallet successfully"
                )
                continue

            else:
                results[bank_transaction.id] = (
                    f"VFD transaction could not be verified succesfully, response: {res}"
                )
                continue

        else:
            res = CoreBankingManager.verify_transaction_reference(reference=reference)

            if (
                res.get("status") == "success"
                and res.get("data", {}).get("recipient_account_number")
                == account_number
                and res.get("data", {}).get("amount") == bank_transaction.amount
                and res.get("data", {}).get("reference") == reference
            ):
                extra_data = {
                    "session_id": request_data.get("session_id"),
                    "company": request_data.get("company"),
                    "paid_at": request_data.get("paid_at"),
                }

                response = perform_bank_account_funding(
                    unique_reference=reference,
                    account_instance=account_instance,
                    amount=bank_transaction.amount,
                    extra_data=extra_data,
                    raw_funding=raw_funding,
                    user=bank_transaction.user,
                )

                if isinstance(response, Response):
                    results[bank_transaction.id] = response.data.get("message")
                    continue

                results[bank_transaction.id] = (
                    f"transaction settled in {bank_transaction.wallet_type} wallet successfully"
                )
                continue

            else:
                results[bank_transaction.id] = (
                    f"Wema transaction could not be verified succesfully, response: {res}"
                )
                continue

    return results


def error_response(message: str) -> Dict[str, Any]:
    return {"status": "failed", "message": message}


@shared_task
def reduce_item_stock_on_paybox(savings_id: AjoSaving, access_token: str):

    try:
        savings = AjoSaving.objects.get(id=savings_id)
        if not savings.savings_type:
            raise ValueError("This is not a BNPL, SNPL type of savings")
        if not savings.product_info:
            raise ValueError("This savings does not have a product info tied to it")
    except (AjoSaving.DoesNotExist, ValueError) as err:
        if err is ValueError:
            return error_response(message=str(err))
        return error_response(
            message=f"ajo savings with ID, {savings_id}, does not exist"
        )

    product_info: ProductInformation = savings.product_info
    register = PayBox.register_sale(
        company=product_info.company_id,
        branch=product_info.branch_id,
        items=[
            {
                "item_id": product_info.item_id,
                "quantity": 1,
                "amount": product_info.selling_price,
            },
        ],
        access_token=access_token,
    )

    if register.get("status"):
        savings.stock_marked = True
        invoice_id = register.get("data", {}).get("data", {}).get("invoice_id")
        savings.invoice_id = invoice_id
        savings.save()
        return {
            "status": "success",
            "message": "sale registered and stock marked successfully",
        }

    return error_response(message=f"issue occurred, see payload --> {register}")


@shared_task
def notify_snpl_savings_milestone(savings_id: int) -> Dict[str, Any]:
    try:

        ajo_savings = AjoSaving.objects.get(id=savings_id)

        is_milestone_reached = AjoSavingsSelector(
            ajo_savings=ajo_savings
        ).milestone_reached_for_snpl()

    except (AjoSaving.DoesNotExist, ValueError) as err:
        if isinstance(err, ValueError):
            return error_response(message=str(err))
        return error_response(
            message=f"ajo savings with ID, {savings_id}, does not exist"
        )

    if not is_milestone_reached:
        return error_response(message=f"milestone not yet reached for plan")

    # what should be done when milestone has been reached
    send_snpl_milestone_sms(
        ajo_user=ajo_savings.ajo_user,
        phone_number=ajo_savings.ajo_user.phone_number,
    )


@shared_task
def create_cash_connect_account_handler(ajo_user_id=None, borrower_info_id=None):
    if not borrower_info_id:

        try:
            ajo_user = AjoUser.objects.get(id=ajo_user_id)
        except AjoUser.DoesNotExist as err:
            return str(err)

        request_create_cash_connect_acct_details = (
            BankAccountService.create_cash_connect_account_details(ajo_user=ajo_user)
        )

        return request_create_cash_connect_acct_details

    else:

        try:
            borrower_info = BorrowerInfo.objects.get(id=borrower_info_id)
            borrower_info.update_dob_with_verification_response()
        except BorrowerInfo.DoesNotExist as err:
            return str(err)

        request_create_cash_connect_acct_details = (
            BankAccountService.create_cash_connect_account_details(
                borrower_info=borrower_info
            )
        )

        return request_create_cash_connect_acct_details


@shared_task
def create_wema_account_handler(ajo_user_id):
    try:
        ajo_user = AjoUser.objects.get(id=ajo_user_id)
    except AjoUser.DoesNotExist as err:
        return f"user id: {ajo_user_id}, {err}"

    user_default_wallet = [
        AccountFormType.LOAN_RECOVERY,
        AccountFormType.AJO_SPENDING,
        AccountFormType.LOAN_REPAYMENT,
    ]
    result = []
    for acct_form_type in user_default_wallet:

        request_result = BankAccountService.create_wema_account(
            ajo_user=ajo_user, acct_form_type=acct_form_type
        )
        data = request_result.get("data")  # account instance
        if data:
            request_result["data"] = data.id

        result.append(request_result)
    return result

@shared_task
def save_for_health_insurance(
    ajo_savings_id: int,
    amount: float, 
    pre_amount_saved: float = None,
    post_amount_saved: float = None
):
    save_for_health_logs = SaveForHealthLogs()
    
    ajo_savings = AjoSaving.objects.filter(id=ajo_savings_id).first()
    if not ajo_savings:
        save_for_health_logs.failure_reason = "Ajo Savings not found"
        save_for_health_logs.save()
        return "Ajo Savins not found"
    
    product_info = ajo_savings.product_info
    health_plan_name = product_info.item
    
    health_plan_const = ConstantTable.get_constant_table_instance()
    try:
        health_plans_data = json.loads(health_plan_const.savings_health_plan_details)
        plan_details = health_plans_data.get(health_plan_name, {})
        activation_fee = plan_details.get("activation_fee", 3000)
        policy_fee = plan_details.get("policy_fee", 0)
        total_plan_fee = policy_fee
    except (json.JSONDecodeError, KeyError, AttributeError):
        save_for_health_logs.failure_reason = "Unable to get health plan details. Contact Admin"
        save_for_health_logs.save()
        return 
   
    if pre_amount_saved <= 0:
        if amount >= activation_fee:
            try:
                health_savings = SaveForHealthInsurance.create_savings_health_plan(
                    savings=ajo_savings,
                    plan_type=health_plan_name.upper(),
                )
            except IntegrityError as err:
                save_for_health_logs.failure_reason = str(err)
                save_for_health_logs.save()
                return

            health_savings.refresh_from_db()
            liberty_status, liberty_resp = health_savings.send_money_to_liberty_nem_account(savings=ajo_savings, amount=amount)
            if liberty_status is True:
                # Update this particular health saving instance(not ajo saving) to the policy and activation fee
                # (This is so that incase we need to change fee later, it does not affect ongoing plans)
                health_savings.activate_plan(activation_fee=activation_fee, policy_fee=policy_fee) # if the send money was success # if the send money was success # if the send money was success
                health_savings.calculate_total_amount()
                health_savings.update_saved_amount(amount)
                health_savings.calculate_balance_amount()
                
                request_savings_plan_creation(
                    policy_fee=policy_fee,
                    activation_fee=activation_fee,
                    payment_reference=liberty_resp,
                    plan_for="INDIVIDUAL",
                    plan_type=health_plan_name.upper(),
                    savings_type=SavingsType.AJO_SAVINGS,
                    request_type=SaveHealthTypes.ACTIVATION,
                    save_for_health_log=save_for_health_logs,
                    ajo_savings_id=ajo_savings_id,
                )
            else:
                save_for_health_logs.failure_reason = f"Unable to send money to liberty. Response: {liberty_resp}"
                save_for_health_logs.save()
                return
        else:
            save_for_health_logs.failure_reason = f"First payment for HEALTH must be equal to or above activation fee of {activation_fee}."
            save_for_health_logs.save()
            return 
    else:
        health_savings = SaveForHealthInsurance.objects.filter(savings=ajo_savings).first()
        if health_savings is not None:
            total_plan_fee = health_savings.total_amount
            if post_amount_saved >= total_plan_fee:
                liberty_status, liberty_resp = health_savings.send_money_to_liberty_nem_account(savings=ajo_savings, amount=amount)
                if liberty_status is True:
                    health_savings.complete_plan() 
                    health_savings.update_saved_amount(amount)
                    health_savings.balance_amount = 0
                    health_savings.save()
                    
                    request_savings_plan_creation(
                        policy_fee=policy_fee,
                        activation_fee=activation_fee,
                        payment_reference=liberty_resp,
                        plan_for="INDIVIDUAL",
                        plan_type=health_plan_name.upper(),
                        savings_type=SavingsType.AJO_SAVINGS,
                        request_type=SaveHealthTypes.FULL_PLAN,
                        save_for_health_log=save_for_health_logs,
                        ajo_savings_id=ajo_savings_id,
                    )
                else:
                    save_for_health_logs.failure_reason = f"Unable to send money to liberty. Response: {liberty_resp}"
                    save_for_health_logs.save()
                    return
            else:
                health_savings.update_saved_amount(amount)
                health_savings.calculate_balance_amount()
                # Ongoing payments, no action required.
                pass
