import re
from typing import Any, Dict, List

from django.conf import settings
from django.core.cache import cache

from .general_utils import generate_otp, verify_otp
from .otp_utils import generate_otp

# define the default service code
ussd_service_code: str = f"{settings.AFRICAS_TALKING_SERVICE_CODE}"

# define the code to generate code
ussd_code_to_generate_otp = f"{ussd_service_code}*22#"


def generate_full_ussd_code(user_id: int) -> str:
    """
    Generate the OTP for the user to dial

    Args:
        user_id (int): the id of the user

    Returns:
        str: the ussd code the ajo user should dial
    """
    return f"{ussd_service_code}*22*{user_id}#"


# def generate_ussd_otp(phone_number: str, user_id: int) -> str:
#     """
#     Generates the OTP and persists it in the cache using the otp and phone number
#     as cache_key

#     Args:
#         phone_number (str): the phone number of the Ajo user
#         user_id (str): the id of the user

#     Returns:
#         str: returns a 6 digit
#     """
#     # generate the OTP
#     otp: str = generate_otp(phone_number=phone_number, time=120)
#     # define the cache key
#     cache_key = f"{otp}-{phone_number}"
#     # create the cache data
#     cache_data = {
#         "otp": otp,
#         "phone_number": phone_number,
#         "user_id": user_id,
#     }
#     # persist the data in the redis cache
#     cache.set(key=cache_key, value=cache_data, timeout=120)

#     return otp


def generate_ussd_otp(phone_number: str) -> str:
    """
    Generates the OTP and persists it in the cache using the otp and phone number
    as cache_key

    Args:
        phone_number (str): the phone number of the Ajo user

    Returns:
        str: returns a 6 digit
    """
    phone_number = phone_number.split("---")[0]
    # generate the OTP
    otp: str = generate_otp(phone_number=phone_number, time=300)
    # define the cache key
    cache_key = f"{otp}-{phone_number}"
    # create the cache data
    cache_data = {
        "otp": otp,
        "phone_number": phone_number,
    }
    # persist the data in the redis cache
    cache.set(key=cache_key, value=cache_data, timeout=300)

    return otp


# def verify_ussd_otp(otp: str, phone_number: str, user_id: int) -> bool:
#     """
#     Verifies the OTP for the phone number through a series of checks

#     Args:
#         otp (str): the OTP the user supplied
#         phone_number (str): the phone number of the Ajo user
#         user_id (int): the id of the user

#     Returns:
#         bool: True or False
#     """
#     # define the cache_key
#     cache_key = f"{otp}-{phone_number}"

#     # obtain the data from the cache
#     data = cache.get(cache_key)

#     # check if the data present in the cache
#     if not data:
#         print("Invalid or Expired OTP")
#         return False

#     if user_id != int(data.get("user_id")):
#         print("This is not the Ajo User's Agent")
#         return False

#     return verify_otp(phone_number=phone_number, otp=otp, time=120)


def verify_ussd_otp(otp: str, phone_number: str) -> bool:
    """
    Verifies the OTP for the phone number through a series of checks

    Args:
        otp (str): the OTP the user supplied
        phone_number (str): the phone number of the Ajo user

    Returns:
        bool: True or False
    """
    phone_number = phone_number.split("---")[0]
    # define the cache_key
    cache_key = f"{otp}-{phone_number}"

    # obtain the data from the cache
    data = cache.get(cache_key)

    # check if the data present in the cache
    if not data:
        print("Invalid or Expired OTP")
        return False

    return verify_otp(phone_number=phone_number, otp=otp, time=300)


def check_generate_otp_ussd_pattern(text: str) -> bool:
    """
    Checks if the ussd text matches the following format
    '22*{any combination of 8 digits}' e.g.
    '22*31'
    '22*800'

    Args:
        text (str): the text the pattern should be matched against

    Returns:
        bool: True or False based on match
    """

    pattern = r"^22\*\d{8}$"
    if re.match(pattern=pattern, string=text):
        return True
    else:
        return False


def persist_cashout_request(amount: int, user_id: int, phone_number: str) -> None:
    """
    This persists the information about the cashout in the cache

    Args:
        amount (float): the amount to be withdrawn
        user_id (int): the user ID of the agent whose ajo user is requesting for cashout
        phone_number (str): the phone number of the ajo user
    """
    # define the cache key
    cache_key = f"cashout-{phone_number}-{amount}"

    cache.delete(key=cache_key)

    # create the cache data
    cache_data = {
        "amount": amount,
        "user_id": user_id,
        "phone_number": phone_number,
    }

    # persist the data in the redis cache
    cache.set(key=cache_key, value=cache_data, timeout=60 * 30)  # 30 minutes persistence

    return


# def persist_cashout_request(amount: float, user_id: int, ajo_saving_id: int, phone_number: str):
#     # define the cache key
#     cache_key = f"cashout-{ajo_saving_id}-{amount}"

#     # create the cache data
#     cache_data = {
#         "amount": amount,
#         "ajo_saving_id": ajo_saving_id,
#         "user_id": user_id,
#         "phone_number": phone_number,
#     }

#     # persist the data in the redis cache
#     cache.set(key=cache_key, value=cache_data, timeout=60 * 15)


def generate_full_cashout_ussd_code(amount: float) -> str:
    """
    Generates a cashout USSD code for the ajo user to dial

    Args:
        amount (float): the amount to be withdrawn

    Returns:
        str: the generated cashout code
    """
    return f"{ussd_service_code}*23*{amount}#"


# def generate_full_cashout_ussd_code(ajo_saving_id: int, amount: float) -> str:
#     """
#     Generates a cashout USSD code for the ajo user to dial

#     Args:
#         ajo_saving_id (int): the ajo savings plan ID
#         amount (float): the amount to be withdrawn

#     Returns:
#         str: the generated cashout code
#     """
#     return f"{ussd_service_code}*23*{ajo_saving_id}*{amount}#"


# def check_cashout_ussd_pattern(text: str) -> bool:
#     """
#     Checks if the ussd text matches the following format
#     '23*{any combination of digits}*{any combination of digits}*' e.g.
#     '23*14*50000'
#     '23*80*7000'

#     Args:
#         text (str): the text that the pattern should be matched against

#     Returns:
#         bool: True or False based on match
#     """

#     pattern = r"^23\*\d+\*\d+"
#     if re.match(pattern=pattern, string=text):
#         return True
#     else:
#         return False


def check_cashout_ussd_pattern(text: str) -> bool:
    """
    Checks if the ussd text matches the following format
    '23*{any combination of digits}*' e.g.
    '23*50000'
    '23*7000'

    Args:
        text (str): the text that the pattern should be matched against

    Returns:
        bool: True or False based on match
    """

    pattern = r"^23\*\d+"
    if re.match(pattern=pattern, string=text):
        return True
    else:
        return False


# def obtain_data_from_cashout_request(ajo_saving_id: int, amount: float) -> Dict[str, Any]:
#     # define the cache key
#     cache_key = f"cashout-{ajo_saving_id}-{amount}"

#     # obtain the data from the cache
#     data = cache.get(cache_key)

#     # define a default response
#     response = {
#         "status": False,
#         "data": None,
#     }

#     # check if the data exists in the cache
#     if not data:
#         return response

#     # update the dictionary data
#     response["status"] = True
#     response["data"] = data

#     # invalidate the cache data
#     cache.delete(cache_key)

#     # return the response
#     return response


def obtain_data_from_cashout_request(phone_number: str, amount: int) -> Dict[str, Any]:
    """
    This checks for if a cache key is present in the cache and return the
    appropriate information

    Args:
        phone_number (str): the phone number of ajo user
        amount (float): the amount the ajo user requested for

    Returns:
        Dict[str, Any]: A dictionary containing status and data keys
    """
    # define the cache key
    cache_key = f"cashout-{phone_number}-{amount}"

    # obtain the data from the cache
    data = cache.get(cache_key)

    # define a default response
    response = {
        "status": False,
        "data": None,
    }

    # check if the data exists in the cache
    if not data:
        cache.delete(cache_key)
        return response

    # update the dictionary data
    response["status"] = True
    response["data"] = data

    # invalidate the cache data
    cache.delete(cache_key)

    # return the response
    return response


# def generate_ussd_cashout_otp(
#     phone_number: str,
#     ajo_saving_id: int,
#     amount: int,
#     user_id: int,
# ) -> str:
#     # generate the OTP
#     otp: str = generate_otp(phone_number=phone_number, time=60 * 15)

#     # define the cache_key
#     cache_key = f"cashout-{otp}-{ajo_saving_id}"

#     # create the cache data
#     cache_data = {
#         "otp": otp,
#         "amount": amount,
#         "ajo_saving_id": ajo_saving_id,
#         "user_id": user_id,
#         "phone_number": phone_number,
#     }

#     # persist the data in the redis cache
#     cache.set(key=cache_key, value=cache_data, timeout=60 * 15)

#     return otp


def generate_ussd_cashout_otp(
    phone_number: str,
    amount: int,
    user_id: int,
) -> str:
    """
    Generate an OTP for an ajo user to give an agent based
    off a cashout request

    Args:
        phone_number (str): the phone number of the ajo user
        amount (int): the amount the ajo user requested for
        user_id (int): the user ID of the agent who the ajo user is under

    Returns:
        str: a six(6) digit OTP for the ajo user
    """
    # generate the OTP
    otp: str = generate_otp(phone_number=phone_number, time=60 * 15)

    # define the cache_key
    cache_key = f"cashout-{otp}-{phone_number}"

    # create the cache data
    cache_data = {
        "otp": otp,
        "amount": amount,
        "user_id": user_id,
        "phone_number": phone_number,
    }

    # persist the data in the redis cache
    cache.set(key=cache_key, value=cache_data, timeout=60 * 15)

    return otp


# def verify_ussd_cashout_otp_and_get_data(
#     otp: str,
#     ajo_saving_id: str,
#     user_id: int,
# ) -> Dict[str, Any]:
#     """
#     Checks for the data in the cache and returns True or raises a
#     ValueError

#     Args:
#         otp (str): the otp received from the USSD
#         ajo_saving_id (str): the id of the ajo savings plan
#         user_id (int): the user's ID

#     Raises:
#         ValueError: Invalid or Expired OTP
#         ValueError: This cashout request does not relate to this user

#     Returns:
#         dict: returns the data from the cache
#     """
#     # define the cache_key
#     cache_key = f"cashout-{otp}-{ajo_saving_id}"

#     # obtain the data from the cache
#     data = cache.get(cache_key)

#     # check if the data present in the cache
#     if not data:
#         raise ValueError("Invalid, Expired OTP or cashout already happened")

#     if user_id != int(data.get("user_id")):
#         raise ValueError("This cashout request does not relate to this user")

#     # invalidate the cache
#     cache.delete(cache_key)

#     return data


def verify_ussd_cashout_otp_and_get_data(
    otp: str,
    phone_number: str,
    user_id: int,
) -> Dict[str, Any]:
    """
    Checks for the data in the cache and returns True or raises a
    ValueError

    Args:
        otp (str): the otp received from the USSD
        phone_number (str): the phone number of the ajo user
        user_id (int): the user's ID

    Raises:
        ValueError: Invalid or Expired OTP
        ValueError: This cashout request does not relate to this user

    Returns:
        dict: returns the data from the cache
    """
    # define the cache_key
    cache_key = f"cashout-{otp}-{phone_number}"

    # obtain the data from the cache
    data = cache.get(cache_key)

    # check if the data present in the cache
    if not data:
        raise ValueError("Invalid, Expired OTP or cashout already happened")

    # check if the cashout is for the user under the agent who made request
    if user_id != int(data.get("user_id")):
        raise ValueError("This cashout request does not relate to this agent")

    # invalidate the cache
    if data:
        cache.delete(cache_key)

    return data


# def check_verification_ussd_pattern(text: str) -> bool:
#     """
#     Checks if the ussd text matches the following format
#     '{a number from 1 - 2}*{any combination of 6 digits}' e.g.
#     '2*890331'
#     '1*101223'

#     Args:
#         text (str): the text to be matched against

#     Returns:
#         bool: True if it matches else False
#     """
#     pattern = r"^[1-2]\*\d{6}$"
#     if re.match(pattern=pattern, string=text):
#         return True
#     else:
#         return False


# def generate_ussd_otp(user_id: int, phone_number: str, verification_stage: VerificationStages) -> str:
#     """
#     Generates an OTP, caches it with the {"otp":{generated_otp}} key
#     which contains a dictionary of the phone_number, verification_stage and OTP

#     Args:
#         phone_number (str): the phone number of the user
#         verification_stage (VerificationStages): What stage of verification is happening

#     Returns:
#         str: The generated OTP
#     """
#     otp = generate_otp(phone_number=phone_number, time=120)
#     cache_key = f"{otp}-{phone_number}"
#     cache_data = {
#         "user_id": user_id,
#         "phone_number": phone_number,
#         "verification_stage": verification_stage,
#         "otp": otp,
#     }

#     cache.set(key=cache_key, value=cache_data, timeout=120)
#     return otp


# def generate_decorated_ussd_otp(
#     user_id: int,
#     phone_number: str,
#     verification_stage: VerificationStages,
# ) -> str:
#     otp = generate_ussd_otp(user_id=user_id, phone_number=phone_number, verification_stage=verification_stage)
#     return f"{settings.AFRICAS_TALKING_SERVICE_CODE}*{verification_stage.value}*{otp}"


# def verify_ussd_otp(otp: str, phone_number: str, verification_choice: int | None = None) -> bool:
#     """
#     Verifies the OTP with the phone number

#     Args:
#         otp (str): the OTP
#         phone_number (str): the phone number to be verified
#         verification_choice(int | None): the choice of verification from the USSD OTP. Defaults to None

#     Returns:
#         bool: True if successful else False
#     """
#     # define the cache_key
#     cache_key = f"{otp}-{phone_number}"

#     # obtain the data from the cache
#     data = cache.get(cache_key)

#     # if the data is present in the cache
#     if data:
#         # obtain the verification_stage, user_id
#         verification_stage: VerificationStages = data.get("verification_stage")
#         user_id = data.get("user_id")

#         try:
#             # retrieve the user instance
#             user = UserModelMethods.get_user_instance(id=user_id)
#             # retrieve the ajo user instance
#             ajo_user = AjoUserSelector(phone_number=phone_number, user=user).get_ajo_user()
#         except ValueError as err:
#             print(str(err))
#             return False

#         # Check if the verification choice matches:
#         if verification_choice:
#             if not verification_stage.value == verification_choice:
#                 print("invalid verification choice")
#                 return False

#         if verification_stage == VerificationStages.ONBOARDING:
#             return verify_onboarding_and_return(
#                 verify=verify_otp(phone_number=phone_number, otp=otp),
#                 ajo_user=ajo_user,
#             )
#         elif verification_stage == VerificationStages.CARD_ISSUING:
#             return verify_card_issuing_and_return(
#                 verify=verify_otp(phone_number=phone_number, otp=otp),
#                 ajo_user=ajo_user,
#             )
#         else:
#             print("invalid verification stage choice")
#             return False
#     else:
#         print("Invalid OTP or expired")
#         return False
