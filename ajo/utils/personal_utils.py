from typing import Any, Dict, Optional

from dateutil.relativedelta import relativedelta
from django.utils import timezone
from django.utils.timezone import datetime

from accounts.models import ConstantTable
from payment.utils import two_decimal_places

from ..model_choices import SavingsFrequency


class PersonalAjoUtils:
    @staticmethod
    def generate_plan_information(
        target: float,
        number_of_participants: int,
        frequency: SavingsFrequency,
        starting_date: datetime,
    ) -> Dict[str, Any]:
        """
        This generates the information for a Rotation group plan

        Args:
            target (float): the amount that each member will collect
            number_of_participants (int): the number of people participating
            frequency (SavingsFrequency): the frequency
            starting_date (datetime): the starting date

        Returns:
            Dict[str, Any]: {
                "amount",
                "contribution_amount",
                "number_of_participants",
                "frequency",
                "starting_date",
                "end_date",
                "duration",
                "converted_duration",
                "collection_amount",
                "fees",
                "agent_fee",
                "company_fee",
            }
        """
        constants = ConstantTable.get_constant_table_instance()
        fees_percentage: float = constants.personal_rosca_commissions_rate

        # divide the percentage by 100 to get the real value
        fees_percentage_value = fees_percentage / 100

        # determine how much would be taken as fees
        fees = target * fees_percentage_value

        # determine the amount each participant would pay
        contribution_amount = two_decimal_places((target + fees) / number_of_participants)

        # determine amount
        amount = two_decimal_places(target / number_of_participants)

        # determine the duration
        duration_dict = PersonalAjoUtils.determine_duration(
            frequency=frequency,
            participants=number_of_participants,
            starting_date=starting_date,
        )

        # calculate agent fees
        agent_rate: float = 100 - constants.commissions_rate
        agent_rate = agent_rate / 100

        agent_fee = two_decimal_places(fees * agent_rate)

        # calculate company's fees
        company_rate = constants.commissions_rate / 100
        company_fee = two_decimal_places(fees * company_rate)

        # calculate collection fees
        collection_fee = two_decimal_places(contribution_amount - amount)

        return {
            "amount": amount,
            "contribution_amount": contribution_amount,
            "number_of_participants": number_of_participants,
            "frequency": frequency,
            **duration_dict,
            "collection_amount": target,
            "collection_fee": collection_fee,
            "fees": fees,
            "agent_fee": agent_fee,
            "company_fee": company_fee,
        }

    @staticmethod
    def determine_duration(
        frequency: SavingsFrequency,
        participants: int,
        starting_date: datetime,
    ) -> Dict[str, float]:
        """
        Based on the frequency and number of participants, it will determine the duration and end date

        Args:
            frequency (SavingsFrequency): the frequency
            participants (int): the number of people participating
            starting_date (datetime): the starting date

        Returns:
            Dict[str, float]: {
                "starting_date",
                "end_date",
                "duration",
                "converted_duration",
            }
        """
        number_of_times = participants - 1

        if frequency == SavingsFrequency.DAILY:
            end_date = starting_date + relativedelta(days=number_of_times)
            unit = "days"

        if frequency == SavingsFrequency.WEEKLY:
            end_date = starting_date + relativedelta(weeks=number_of_times)
            unit = "weeks"

        if frequency == SavingsFrequency.MONTHLY:
            end_date = starting_date + relativedelta(months=number_of_times)
            unit = "months"

        duration = (end_date - starting_date).days + 1

        return {
            "starting_date": starting_date,
            "end_date": end_date,
            "duration": duration,
            "converted_duration": f"{number_of_times + 1} {unit}",
        }
