import base64
import re
from datetime import date, datetime

import pyotp
from django.conf import settings
from django.contrib.sites.shortcuts import get_current_site

from ..models import AjoSaving
from ..third_party import TextMessages


def obtain_the_current_site(request) -> str:
    """
    uses the request for a view to get the current base url
    or domain where the server is running

    Args:
        request (Request): The request object from the view

    Returns:
        str: the domain or base url
    """
    current_site = get_current_site(request=request)
    base_url = f"{request.scheme}://{current_site.domain}"

    return base_url


def convert_string_to_base32_string(input: str) -> str:
    """
    This converts any string to the base32 format of itself

    Args:
        input (str): the string you wish to convert

    Returns:
        str: base32 format of the input string
    """
    # encoding the email string to bytes
    input_string_bytes = input.encode("UTF-8")

    # encoding the bytes to Base32
    input_base32_bytes = base64.b32encode(input_string_bytes)

    # decoding the base32 bytes to string
    input_base32 = input_base32_bytes.decode("UTF-8")

    return input_base32


def generate_otp(phone_number: str, time: int = 900) -> str:
    """
    This generates an OTP using the pyotp package and the user's phone number
    the OTP will be valid for 15 minutes

    Args:
        phone_number (str): phone number of the ajo user
        time (int, optional): how many seconds should the OTP be valid for. Defaults to 900.

    Returns:
        str: the OTP that was generated
    """
    phone_number = phone_number.split("---")[0]
    formatted_phone_number = convert_string_to_base32_string(input=phone_number)
    totp = pyotp.TOTP(s=formatted_phone_number, interval=time)
    otp = totp.now()
    return otp


def verify_otp(phone_number: str, otp: str, time: int = 900) -> bool:
    """
    This verifies the OTP against the user's phone number as long as it
    in the valid time window

    Args:
        phone_number (str): the user's phone number
        otp (str): the OTP of the user.

    Returns:
        bool: True/False
    """
    phone_number = phone_number.split("---")[0]
    formatted_phone_number = convert_string_to_base32_string(input=phone_number)
    totp = pyotp.TOTP(s=formatted_phone_number, interval=time)
    verify = totp.verify(otp)
    return verify


def format_phone_number(phone_number: str) -> str:
    """
    Formats a Nigerian phone number from +234 to 0
    e.g. "+2349055412902" would become 09055412902

    Args:
        phone_number (str): the phone number you intend to format

    Raises:
        TypeError: "The phone_number argument must be a string"
        ValueError: "Invalid phone number format"

    Returns:
        str: a formatted version of the number
    """
    phone_number = phone_number.split("---")[0]
    if not isinstance(phone_number, str):
        raise TypeError("The phone_number argument must be a string")
    # define the regex pattern to match the phone number
    pattern = r"\+234(\d{10})"

    # use re.sub to remove '+234' and add a leading '0'
    formatted_number = re.sub(pattern, r"0\1", phone_number)

    if formatted_number == phone_number:
        raise ValueError("Invalid phone number format")

    return formatted_number


def validate_date_format(
    date_string: str,
    month_year_only: bool = False,
) -> bool:
    """
    Validate a date string to ensure it follows 'YYYY-MM-DD'.
    If month_year_only argument is set to true, it validates only 'YYYY-MM'.

    Args:
        date_string (str): the date string to be checked
        month_year_only (bool, optional): if you want only month and year to be checked. Defaults to False.

    Returns:
        bool: True if it matches else False.
    """
    pattern = r"\d{4}-\d{2}-\d{2}$"

    if month_year_only:
        pattern = r"\d{4}-\d{2}$"

    if re.match(pattern=pattern, string=date_string):
        return True

    return False


def send_closing_ajo_plan_sms(ajo_savings_plan: AjoSaving, end_date: date) -> bool:
    try:
        amount = ajo_savings_plan.periodic_amount
        name = ajo_savings_plan.name
        phone_number = ajo_savings_plan.ajo_user.phone_number
        phone_number = phone_number.split("---")[0]
        formatted_end_date = end_date.strftime("%d/%m/%Y")

        placeholders = {
            "message": f"Your contribution of {amount}, with name, {name}, will end on {formatted_end_date}."
        }

        sms = TextMessages.dynamic_send_sms(
            ajo_user=ajo_savings_plan.ajo_user,
            phone_number=phone_number,
            template_id=settings.EMPTY_TEMPLATE_ID,
            placeholders=placeholders,
        )

    except:
        return False

    return sms.get("status")


def remove_ansi_escape_codes(text_with_colors):
    ansi_escape = re.compile(r"\x1b\[[0-9;]*[mGK]")

    # Remove ANSI escape codes using the regular expression
    plain_text = ansi_escape.sub("", text_with_colors)

    return plain_text


def alt_format_phone_no(phone_number: str):
    # Remove any non-digit characters from the input
    cleaned_number = "".join(filter(str.isdigit, phone_number))
    # Check if the cleaned number is in the specified formats
    if cleaned_number.startswith("234") and len(cleaned_number) == 13:
        return "0" + cleaned_number[3:]
    elif cleaned_number.startswith(("070", "080", "090")) and len(cleaned_number) == 11:
        return cleaned_number
    elif cleaned_number.startswith("+234") and len(cleaned_number) == 14:
        return "0" + cleaned_number[3:]
    else:
        raise TypeError("Invalid phone number format")
