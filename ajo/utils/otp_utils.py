from celery import shared_task
from django.conf import settings

from accounts.models import CustomUser
from payment.utils import format_currency

from ..model_choices import OTPType
from ..models import AjoSaving, AjoUser, RotationGroup
from ..third_party import OTP, TextMessages
from .general_utils import generate_otp, verify_otp


def send_out_sms_otp(phone_number: str) -> bool:
    """
    This sends an SMS OTP to a phone number, please you may need to ignore the boolean that will be returned
    it may show <PERSON>alse and the OTP will still be sent

    Args:
        phone_number (str): phone number where the OTP will be sent to

    Returns:
        bool: True or False is returned
    """
    sms = OTP.sms_voice_otp(otp_type=OTPType.SMS, phone_number=phone_number)
    print("I am SMS", sms)
    return sms.get("status")


def send_out_voice_otp(phone_number: str) -> bool:
    """
    This sends a VOICE OTP to a phone number, please you may need to ignore the boolean that will be returned
    it may show False and the OTP will still be sent

    Args:
        phone_number (str): phone number where the OTP will be sent to

    Returns:
        bool: True or False is returned
    """
    voice = OTP.sms_voice_otp(otp_type=OTPType.VOICE, phone_number=phone_number)
    return voice.get("status")


def send_out_whatsapp_otp(phone_number: str) -> bool:
    """
    This sends a Whatsapp OTP to a phone number and returns a True or False if it is successfully delivered

    Args:
        phone_number (str): phone number where the OTP will be sent to

    Returns:
        bool: True or False is returned
    """
    whatsapp = OTP.send_otp_through_whatsapp(
        phone_number=phone_number,
        otp=generate_otp(phone_number=str(phone_number)),
    )
    return whatsapp.get("status")


def verify_sms_voice_otp(otp: str, phone_number: str) -> bool:
    print(otp, "\n\n")
    """
    verifies either an SMS or VOICE OTP

    Args:
        otp (str): OTP sent to the user,
        phone_number (str): phone number of the user

    Returns:
        bool: True or False is returned which shows if it was verified
    """
    verify = OTP.verify_sms_voice_otp(otp=otp, phone_number=phone_number)
    # return verify.get("data").get("is_authenticated") and verify.get("data").get("verified")
    return verify.get("verified", False)


def verify_whatsapp_otp(otp: str, phone_number: str) -> bool:
    """
    verifies a whatsapp OTP

    Args:
        otp (str): OTP sent to the user,
        phone_number (str): phone number of the user

    Returns:
        bool: True or False is returned which shows if it was verified
    """
    return verify_otp(phone_number=phone_number, otp=otp)


def send_out_sms_for_ajo_payment(
    ajo_user: AjoUser, phone_number: str, first_name: str, amount: float, balance: float
) -> bool:
    """
    This sends an SMS OTP to an ajo user phone number for ajo payment sms alert

    Args:
        phone_number (str): phone number where the OTP will be sent to

    Returns:
        bool: True or False is returned
    """

    placeholders = {
        "first_name": first_name,
        "amount": format_currency(amount),
        "balance": format_currency(balance),
    }

    sms = TextMessages.dynamic_send_sms(
        ajo_user=ajo_user,
        phone_number=phone_number,
        template_id=settings.AJO_PAYMENT_TEMPLATE_ID,
        placeholders=placeholders,
    )
    print("I am SMS", sms)
    return sms.get("status")


def convert_num_to_currency(number):
    return "{:,.2f}".format(float(number))


def send_first_ajo_payment_sms(
    ajo_user: AjoUser,
    phone_number: str,
    amount: float,
    balance: float,
) -> bool:
    """
    This sends the first message when an ajo plan is activated with payment

    Args:
        ajo_user (AjoUser): the ajo user
        phone_number (str): the phone number
        amount (float): the amount
        balance (float): the balance

    Returns:
        bool: True or False
    """

    placeholders = {
        "message": f"Welcome, You have successfully activated your Ajo Plan\nAmt: {format_currency(amount)}\nBal: N{format_currency(balance)}"
    }

    sms = TextMessages.dynamic_send_sms(
        ajo_user=ajo_user,
        phone_number=phone_number,
        template_id=settings.EMPTY_TEMPLATE_ID,
        placeholders=placeholders,
    )

    return sms.get("status")


def send_first_sms_alert(ajo_savings_id: int, amount: float):
    """
    This sends the first sms alert for an ajo savings plan

    Args:
        ajo_savings_id (int): the ID of the ajo savings plan
        user_id (int): the user ID

    Raises:
        Exception: Any Exception experienced in the process
    """
    try:
        ajo_savings = AjoSaving.objects.select_related("ajo_user").get(id=ajo_savings_id)
        ajo_user = ajo_savings.ajo_user
        phone_number = ajo_user.phone_number
        balance_after_commission = ajo_savings.amount_saved - ajo_savings.periodic_amount
        balance = 0 if balance_after_commission < 0 else balance_after_commission

        send_first_ajo_payment_sms(
            ajo_user=ajo_user,
            phone_number=phone_number,
            amount=amount,
            balance=balance,
        )
    except:
        pass

    return


def ajo_rosca_contribution_sms(
    ajo_user: AjoUser, phone_number: str, name: str, group: RotationGroup, amount: float
) -> bool:
    """
    This sends This sends the rosca contribution payment

    Args:
        ajo_user (AjoUser): the ajo user
        phone_number (str): the phone number
        amount (float): the amount
        balance (float): the balance

    Returns:
        bool: True or False
    """

    placeholders = {
        "name": name,
        "group": (group),
        "amount": format_currency(amount),
    }

    sms = TextMessages.dynamic_send_sms(
        ajo_user=ajo_user,
        phone_number=phone_number,
        template_id=settings.AJO_ROSCA_CONTRIBUTION_TEMPLATE,
        placeholders=placeholders,
    )

    return sms.get("status")


def ajo_rosca_collection_sms(ajo_user: AjoUser, phone_number: str, name: str, group: str, amount: float) -> bool:
    """
    This sends the rosca collection payment

    Args:
        ajo_user (AjoUser): the ajo user
        phone_number (str): the phone number
        amount (float): the amount
        balance (float): the balance

    Returns:
        bool: True or False
    """

    placeholders = {
        "name": name,
        "group": (group),
        "amount": format_currency(amount),
    }

    sms = TextMessages.dynamic_send_sms(
        ajo_user=ajo_user,
        phone_number=phone_number,
        template_id=settings.AJO_ROSCA_COLLECTION_TEMPLATE,
        placeholders=placeholders,
    )

    return sms.get("status")


@shared_task
def send_agent_switch_sms(
    ajo_user: AjoUser,
    phone_number: str,
    user: CustomUser,
) -> bool:
    """
    This sends a message to inform the ajo user when their agent is changed.

    Args:
        ajo_user (AjoUser): the ajo user
        phone_number (str): the phone number

    Returns:
        bool: True or False
    """

    placeholders = {
        "message": f"Dear customer, Your new agent is;\nEmail: {user.email}\nPhone Number: {user.user_phone}"
    }

    sms = TextMessages.dynamic_send_sms(
        ajo_user=ajo_user,
        phone_number=phone_number,
        template_id=settings.EMPTY_TEMPLATE_ID,
        placeholders=placeholders,
    )

    return sms.get("status")


def send_snpl_milestone_sms(
    ajo_user: AjoUser,
    phone_number: str,
) -> bool:
    """
    This sends a message to inform the ajo user that they have hit their SNPL Milestone

    Args:
        ajo_user (AjoUser): the ajo user
        phone_number (str): the phone number

    Returns:
        bool: True or False
    """

    placeholders = {
        "message": f"Dear customer, Your 70Seeds/SNPL milestone has been reached. \n reach out to customer care to identify how to get your device."
    }

    sms = TextMessages.dynamic_send_sms(
        ajo_user=ajo_user,
        phone_number=phone_number,
        template_id=settings.EMPTY_TEMPLATE_ID,
        placeholders=placeholders,
    )

    return sms.get("status")
