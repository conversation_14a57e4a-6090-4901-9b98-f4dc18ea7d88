from django.core.validators import RegexValidator
from rest_framework import serializers

from payment.models import WalletSystem

from ..models import Ajo<PERSON>ser, Loan


class LoanEligibiltySerializer(serializers.Serializer):
    phone_number = serializers.CharField(
        max_length=11,
        validators=[
            RegexValidator(
                regex=r"^\d{11}$",
                message="phone number must be 11 digits",
                code="invalid_phone_number",
            )
        ],
    )


class EligibleAjoUserDetailsSerializer(serializers.Serializer):
    full_name = serializers.CharField()
    phone_number = serializers.CharField()


class ListOfEligibleAjoUsersLoansSerializer(serializers.Serializer):
    eligible_loan_amount = serializers.FloatField()
    ajo_user = EligibleAjoUserDetailsSerializer()


class RequestForLoanSerializer(serializers.Serializer):
    phone_number = serializers.CharField(
        max_length=11,
        validators=[
            RegexValidator(
                regex=r"^\d{11}$",
                message="phone number must be 11 digits",
                code="invalid_phone_number",
            )
        ],
    )
    loan_amount = serializers.FloatField()


class LoanRequestAjoUserDetailsSerializer(serializers.Serializer):
    first_name = serializers.CharField()
    alias = serializers.CharField()
    phone_number = serializers.CharField()


class GetLoanRequestDetails(serializers.Serializer):
    ajo_user = LoanRequestAjoUserDetailsSerializer()
    loan_amount = serializers.FloatField()
    repayment_frequency = serializers.CharField()
    duration = serializers.IntegerField()
    due_date = serializers.DateField()
    loan_interest_rate = serializers.FloatField()
    loan_repayment_amount = serializers.FloatField()
    periodic_amount = serializers.FloatField()


class CreateLoanPlanSerializer(serializers.Serializer):
    phone_number = serializers.CharField(
        max_length=11,
        validators=[
            RegexValidator(
                regex=r"^\d{11}$",
                message="phone number must be 11 digits",
                code="invalid_phone_number",
            )
        ],
    )


class LoanSerializer(serializers.ModelSerializer):
    class Meta:
        model = Loan
        fields = [
            "id",
            "loaned_amount",
            "interest",
            "repayment_amount",
            "frequency",
            "quotation_id",
            "periodic_amount",
            "amount_paid",
            "is_activated",
            "completed",
            "completed_at",
            "is_active",
        ]


class LoanBalanceSerializer(serializers.ModelSerializer):
    available_balance = serializers.FloatField()

    class Meta:
        model = WalletSystem
        fields = [
            "available_balance",
        ]


class LoanWithdrawSerializer(serializers.Serializer):
    phone_number = serializers.CharField(
        max_length=11,
        validators=[
            RegexValidator(
                regex=r"^\d{11}$",
                message="phone number must be 11 digits",
                code="invalid_phone_number",
            )
        ],
    )
    amount = serializers.FloatField()
