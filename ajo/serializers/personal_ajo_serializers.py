from typing import List

from django.contrib.auth import get_user_model
from rest_framework import serializers

from payment.model_choices import PaymentMethod
from payment.models import Transaction

from ..model_choices import SavingsFrequency
from ..models import RotationGroup, RotationGroupMember
from ..selectors import RotationGroup<PERSON>emberSelector, RotationGroupSelector
from .ajo_serializers import AjoSavingsInformationSerializer


class RotationGroupInformationSerializer(serializers.ModelSerializer):
    current_frequency = serializers.SerializerMethodField(
        help_text="this provides the current count in terms of frequency that the group is in"
    )
    withdrawal_count = serializers.SerializerMethodField()
    contribution_count = serializers.SerializerMethodField(
        help_text="the number of people that have paid for the current contribution day. Works for only running groups"
    )
    positions_filled = serializers.SerializerMethodField()
    positions_vacant = serializers.SerializerMethodField()

    def get_current_frequency(self, obj: RotationGroup) -> int:
        return RotationGroupSelector(group_id=obj.group_id).get_current_frequency()

    def get_withdrawal_count(self, obj: RotationGroup) -> int:
        return RotationGroupSelector(group_id=obj.group_id).get_withdrawal_count()

    def get_contribution_count(self, obj: RotationGroup) -> int:
        return RotationGroupSelector(group_id=obj.group_id).get_contribution_count()

    def get_positions_filled(self, obj: RotationGroup) -> List[int]:
        return RotationGroupSelector(group_id=obj.group_id).get_filled_positions()

    def get_positions_vacant(self, obj: RotationGroup) -> List[int]:
        return RotationGroupSelector(group_id=obj.group_id).available_postions()

    class Meta:
        model = RotationGroup
        fields = [
            "id",
            "group_id",
            "user",
            "name",
            "amount",
            "contribution_amount",
            "number_of_participants",
            "frequency",
            "duration",
            "starting_date",
            "end_date",
            "admin_fee",
            "auto_debit",
            "collection_amount",
            "quotation_id",
            "status",
            "is_active",
            "current_frequency",
            "withdrawal_count",
            "contribution_count",
            "positions_filled",
            "positions_vacant",
        ]


class CreateRotationGroupSerializer(serializers.Serializer):
    name = serializers.CharField()
    target_amount = serializers.FloatField()
    participants = serializers.IntegerField()
    frequency = serializers.CharField()
    starting_date = serializers.DateField()
    auto_debit = serializers.BooleanField(default=False)

    def validate(self, attrs):
        frequency = attrs.get("frequency", "")
        if frequency.upper() not in SavingsFrequency:
            raise serializers.ValidationError("please choose daily, weekly or monthly")
        else:
            attrs["frequency"] = getattr(SavingsFrequency, frequency.upper())
        return attrs


class EditRotationGroupSerializer(serializers.Serializer):
    target_amount = serializers.FloatField()
    participants = serializers.IntegerField()
    frequency = serializers.CharField()
    starting_date = serializers.DateField()
    auto_debit = serializers.BooleanField(default=False)

    def validate(self, attrs):
        frequency = attrs.get("frequency", "")
        if frequency.upper() not in SavingsFrequency:
            raise serializers.ValidationError("please choose daily, weekly or monthly")
        else:
            attrs["frequency"] = getattr(SavingsFrequency, frequency.upper())
        return attrs


class RotationGroupIDSerializer(serializers.Serializer):
    group_id = serializers.CharField()


class CheckRotationGroupSerializer(RotationGroupIDSerializer):
    pass


class JoinRotationGroupSerializer(serializers.Serializer):
    group_id = serializers.CharField()
    position = serializers.IntegerField()
    payment_method = serializers.CharField(required=False, allow_null=True)
    masked_pan = serializers.CharField(required=False, allow_null=True)

    def validate(self, attrs):
        payment_method = attrs.get("payment_method", "")
        masked_pan = attrs.get("masked_pan", "")

        if payment_method:
            if payment_method.upper() not in PaymentMethod:
                raise serializers.ValidationError("please choose 'WALLET' or 'DEBIT_CARD'")
            else:
                attrs["payment_method"] = getattr(PaymentMethod, payment_method.upper())

            if getattr(PaymentMethod, payment_method.upper()) == PaymentMethod.DEBIT_CARD:
                if not masked_pan:
                    raise serializers.ValidationError("please pass the masked_pan in the request body")
            else:
                attrs["masked_pan"] = None

        return attrs


class LeaveRotationGroupSerializer(RotationGroupIDSerializer):
    pass


class GroupSerializer(serializers.ModelSerializer):
    class Meta:
        model = RotationGroup
        fields = [
            "group_id",
            "name",
            "amount",
        ]


class PersonalAjoSavingsInformationSerializer(AjoSavingsInformationSerializer): ...


class SavingsSummarySerializer(serializers.Serializer):
    balance = serializers.FloatField()
    active_savings = serializers.IntegerField()
    wallet_number = serializers.CharField()
    rotation_balance = serializers.FloatField()


class PersonalTransactionHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        fields = [
            "id",
            "status",
            "amount",
            "date_created",
            "transaction_id",
            "description",
            "transaction_form_type",
            "transaction_date_completed",
            "transaction_type",
            "wallet_type",
            "wallet_balance_before",
            "wallet_balance_after",
        ]


class GenerateRotationGroupDetailsSerializer(serializers.Serializer):
    target = serializers.FloatField()
    number_of_participants = serializers.IntegerField()
    frequency = serializers.CharField()
    starting_date = serializers.DateField()

    def validate(self, attrs):
        frequency = attrs.get("frequency", "")
        if frequency.upper() not in SavingsFrequency:
            raise serializers.ValidationError("please choose daily, weekly or monthly")
        else:
            attrs["frequency"] = getattr(SavingsFrequency, frequency.upper())

        return super().validate(attrs)


class RotationGroupGeneratedDetailsSerializer(serializers.Serializer):
    contribution_amount = serializers.FloatField()
    number_of_participants = serializers.IntegerField()
    frequency = serializers.CharField()
    starting_date = serializers.DateField()
    end_date = serializers.DateField()
    duration = serializers.IntegerField()
    converted_duration = serializers.CharField()
    collection_amount = serializers.FloatField()
    collection_fee = serializers.FloatField()
    fees = serializers.FloatField()
    agent_fee = serializers.FloatField()
    company_fee = serializers.FloatField()


class FetchRotationGroupsSerializer(RotationGroupInformationSerializer):
    is_admin = serializers.SerializerMethodField()

    def get_is_admin(self, obj) -> bool:
        # Retrieve the user making the request
        user = self.context["request"].user

        # Check if the user is the admin
        is_admin = obj.user == user

        return is_admin

    class Meta(RotationGroupInformationSerializer.Meta):
        fields = RotationGroupInformationSerializer.Meta.fields + [
            "is_admin",
        ]


class UserInformationSerializer(serializers.ModelSerializer):
    class Meta:
        model = get_user_model()
        fields = [
            "email",
        ]


class RotationGroupMemberBasicInformationSerializer(serializers.ModelSerializer):
    user = UserInformationSerializer()

    class Meta:
        model = RotationGroupMember
        fields = [
            "group",
            "user",
            "position",
        ]


class RotationGroupMemberInformationSerializer(serializers.ModelSerializer):
    user = UserInformationSerializer()
    paid_positions = serializers.SerializerMethodField()
    total_frequency = serializers.SerializerMethodField()
    unpaid_positions = serializers.SerializerMethodField()
    withdrawn = serializers.SerializerMethodField()

    def get_paid_positions(self, obj: RotationGroupMember) -> List[int]:
        return RotationGroupMemberSelector.paid_positions(obj)

    def get_total_frequency(self, obj: RotationGroupMember) -> int:
        return RotationGroupSelector(group_id=obj.group.group_id).get_total_collection_frequency()

    def get_unpaid_positions(self, obj: RotationGroupMember) -> List[int]:
        return RotationGroupMemberSelector.get_unpaid_positions_below_next_payment(
            group=obj.group,
            member=obj,
        )

    def get_withdrawn(self, obj: RotationGroupMember) -> bool:
        return RotationGroupMemberSelector(member=obj).get_set_member_withdrawal_status()

    class Meta:
        model = RotationGroupMember
        fields = [
            "group",
            "user",
            "position",
            "total_amount_contributed",
            "total_amount_collected",
            "collected",
            "collection_date",
            "frequency_paid",
            "total_frequency",
            "paid_positions",
            "unpaid_positions",
            "withdrawn",
        ]


# class PositionStatusSerializer(serializers.Serializer):
#     position = serializers.IntegerField()
#     paid = serializers.BooleanField()


class RotationGroupMemberDueInformationSerializer(serializers.Serializer):
    member = RotationGroupMemberBasicInformationSerializer()
    position_payment_status = serializers.DictField(
        child=serializers.BooleanField(),
        allow_empty=False,
    )
    total_unpaid_positions = serializers.IntegerField()


class RemoveRotationGroupMemberSerializer(serializers.Serializer):
    id = serializers.IntegerField(help_text="the ID of the rotation group")
    position = serializers.IntegerField(help_text="the position of the member of the group to be removed")


class PersonalAjoSavingsFullDetailsSerializer(serializers.Serializer):
    ajo_savings = PersonalAjoSavingsInformationSerializer()
    transaction_history = PersonalTransactionHistorySerializer(many=True)


class StartRotationGroupSerializer(serializers.Serializer):
    id = serializers.IntegerField(help_text="the ID of the rotation group")


class PersonalUserChecksSerializer(serializers.Serializer):
    active_card = serializers.BooleanField()
    active_card_message = serializers.CharField()
    kyc_level = serializers.IntegerField()
    kyc_message = serializers.CharField()
