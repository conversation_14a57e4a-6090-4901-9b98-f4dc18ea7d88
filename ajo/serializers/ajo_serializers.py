import os
from datetime import date
from django.db.models import Q
from django.core.validators import RegexValidator
from rest_framework import serializers

from accounts.serializers import NextOfKinSerializer, UserSerializer
from loans.enums import RepaymentFrequency
from payment.models import Transaction

from ..model_choices import (
    AjoType,
    Gender,
    OTPType,
    SavingsFrequency,
    SavingsType,
    VerificationStages,
)
from ..models import (
    AjoSaving,
    AjoSavingsPlanType,
    AjoSepo,
    AjoTradeCategory,
    AjoTradeSubCategory,
    AjoUser,
    BankAccountDetails,
    Card,
    CardRequests,
    ProductInformation,
    RoscaPaymentBreakDown,
    RotationGroup,
    RotationGroupMember,
)
from .payment_serializers import PrefundingWalletSerializer


class ExistingAjoUserSerializer(serializers.ModelSerializer):
    image = serializers.FileField(use_url=True)
    has_transaction_pin = serializers.SerializerMethodField()

    class Meta:
        model = AjoUser
        fields = [
            "id",
            "first_name",
            "last_name",
            "alias",
            "image",
            "need_card",
            "card_assigned",
            "card_issued",
            "bvn_verified",
            "agent_bvn_used",
            "has_transaction_pin",
            "onboarding_stage",
            "onboarding_source",
            "address",
            "state",
            "lga",
            "gender",
        ]

    def to_representation(self, instance: AjoUser):
        representation = super().to_representation(instance)
        representation["phone_number"] = instance.phone
        return representation

    def get_has_transaction_pin(self, obj: AjoUser):
        if obj.lite_transaction_pin:
            return True
        return False


class MinimalAjoUserSerializer(serializers.ModelSerializer):

    class Meta:
        model = AjoUser
        fields = [
            "id",
            "first_name",
            "last_name",
            "phone_number",
        ]


class AjoUserTotalBalancesSerializer(serializers.Serializer):
    savings_wallet_balance = serializers.FloatField()
    total_active_savings = serializers.IntegerField()
    total_amount = serializers.FloatField()
    spending_wallet_balance = serializers.FloatField()
    wallet_number = serializers.FloatField()
    loan_escrow_wallet_balance = serializers.FloatField()
    loan_disbursement_wallet_balance = serializers.FloatField()


class AgentWalletSerializer(serializers.Serializer):
    available_balance = serializers.FloatField()
    prefunding_balances = PrefundingWalletSerializer()


class TransactionHistoryForAjoUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        fields = [
            "id",
            "status",
            "amount",
            "date_created",
            "transaction_id",
            "description",
            "transaction_form_type",
            "transaction_date_completed",
            "transaction_type",
        ]


class AjoUserForTransactionHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = AjoUser
        fields = ["first_name", "last_name", "phone_number"]


class GeneralAjoTransactionHistorySerializer(serializers.ModelSerializer):
    onboarded_user = AjoUserForTransactionHistorySerializer()

    class Meta:
        model = Transaction
        fields = [
            "id",
            "onboarded_user",
            "status",
            "amount",
            "transaction_id",
            "description",
            "date_created",
            "transaction_form_type",
            "transaction_date_completed",
            "transaction_type",
            "wallet_type",
            "wallet_balance_before",
            "wallet_balance_after",
            "credit_balance",
            "due_balance",
        ]


class AgentSummarySerializer(serializers.Serializer):
    today_collection = serializers.FloatField()
    total_cash_collection = serializers.FloatField()
    total_savers = serializers.IntegerField()
    total_cash_withdrawn = serializers.FloatField()
    total_withdrawals = serializers.IntegerField()
    ussd_otp_code = serializers.CharField()


class TransactionSummarySerializer(AgentSummarySerializer):
    transactions = GeneralAjoTransactionHistorySerializer(many=True)


class OnboardAjoUserWithPhoneNumberSerializer(serializers.ModelSerializer):
    phone_number = serializers.CharField(
        max_length=11,
        validators=[
            RegexValidator(
                regex=r"^\d{11}$",
                message="phone number must be 11 digits",
                code="invalid_phone_number",
            )
        ],
    )

    class Meta:
        model = AjoUser
        fields = ["phone_number"]

    def validate(self, attrs):
        user = self.context["user"]
        phone_number = attrs.get("phone_number")
        ajo_user = AjoUser.objects.filter(
            user=user, phone_number__startswith=phone_number
        ).last()
        if (
            ajo_user
            and ajo_user.onboarding_verified is True
            and ajo_user.onboarding_complete is True
        ):
            raise serializers.ValidationError(
                "The provided phone number is already associated with another user. Please use a different phone number."
            )
        ajo_user_exist_under_a_different_agent = AjoUser.objects.filter(
            phone_number__startswith=phone_number
        ).exclude(user=user)

        if ajo_user_exist_under_a_different_agent.exists():
            agent_email = ajo_user_exist_under_a_different_agent.first().user.email
            raise serializers.ValidationError(
                f"The provided phone number is already associated with another user under the agent with email: {agent_email}. Please use a different phone number."
            )

        attrs["ajo_user_instance"] = ajo_user
        return super().validate(attrs)


class TradeSearchSerializer(serializers.ModelSerializer):
    name = serializers.CharField()

    class Meta:
        model = AjoTradeSubCategory
        fields = ["name"]


class AjoTradesCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = AjoTradeCategory
        fields = ["name"]


class AjoTradesSubCategorySerializer(serializers.ModelSerializer):
    category = AjoTradesCategorySerializer()

    class Meta:
        model = AjoTradeSubCategory
        fields = ["category", "name"]


class FillPersonalDetailsSerializer(serializers.ModelSerializer):
    gender = serializers.CharField()
    alias = serializers.CharField(required=False, allow_null=True)
    bvn = serializers.CharField(required=False, allow_null=True)
    nin = serializers.CharField(required=False, allow_null=True)

    def validate(self, attrs):
        phone_number = self.context.get("phone_number")
        first_name = attrs.get("first_name")
        last_name = attrs.get("last_name")
        gender = attrs.get("gender", "")
        bvn = attrs.get("bvn")
        nin = attrs.get("nin")

        if not first_name.isalpha() and last_name.isalpha():
            raise serializers.ValidationError(
                "please ensure that first name and last name are only alphabets with no space or special characters"
            )

        if gender.upper() not in Gender:
            raise serializers.ValidationError("please choose male or female for gender")
        else:
            attrs["gender"] = getattr(Gender, gender.upper())

        if not nin:
            pass
        else:
            if AjoUser.objects.filter(
                Q(nin=nin) & ~Q(phone_number__startswith=phone_number)
            ).exists():
                raise serializers.ValidationError(
                    f"User with nin: {nin} already exist "
                )

        if not bvn:
            pass
        else:
            if AjoUser.objects.filter(
                Q(bvn=bvn) & ~Q(phone_number__startswith=phone_number)
            ).exists():
                raise serializers.ValidationError(
                    f"User with bvn: {bvn} already exist "
                )

        return attrs

    class Meta:
        model = AjoUser
        fields = [
            "first_name",
            "last_name",
            "alias",
            "gender",
            "marital_status",
            "state",
            "lga",
            "address",
            "trade",
            "trade_location",
            "bvn",
            "nin",
        ]


class SelectAlternativeOTPChoiceSerializer(serializers.Serializer):
    otp_type = serializers.CharField(required=True, min_length=3)
    phone_number = serializers.CharField(
        max_length=11,
        required=True,
        validators=[
            RegexValidator(
                regex=r"^\d{11}$",
                message="phone number must be 11 digits",
                code="invalid_phone_number",
            )
        ],
    )

    def validate(self, attrs):
        otp_type = attrs.get("otp_type", "")

        if otp_type.upper() not in OTPType:
            raise serializers.ValidationError(
                "please choose from 'SMS','VOICE','WHATSAPP' or 'USSD'"
            )
        else:
            attrs["otp_type"] = getattr(OTPType, otp_type.upper())

        return attrs


class VerifyOTPSerializer(serializers.Serializer):
    otp = serializers.CharField(min_length=5, required=True)
    otp_type = serializers.CharField(default=OTPType.SMS)
    verification_stage = serializers.CharField(default=VerificationStages.ONBOARDING)
    phone_number = serializers.CharField(
        max_length=11,
        required=True,
        validators=[
            RegexValidator(
                regex=r"^\d{11}$",
                message="phone number must be 11 digits",
                code="invalid_phone_number",
            )
        ],
    )

    def validate(self, attrs):
        otp = attrs.get("otp", "")
        otp_type = attrs.get("otp_type", "")
        verification_stage = attrs.get("verification_stage", "")

        if otp_type.upper() not in OTPType:
            raise serializers.ValidationError(
                "please choose from 'SMS','VOICE','WHATSAPP' or 'USSD'"
            )
        else:
            attrs["otp_type"] = getattr(OTPType, otp_type.upper())

        if not otp.isdigit():
            raise serializers.ValidationError("OTP field must be numbers only")

        if verification_stage.upper() not in VerificationStages:
            raise serializers.ValidationError(
                f"please choose from the list: {list(VerificationStages.__members__.keys())}"
            )
        else:
            attrs["verification_stage"] = getattr(
                VerificationStages, verification_stage.upper()
            )

        return attrs


class ImageCapturingSerializer(serializers.ModelSerializer):
    image = serializers.FileField(
        max_length=None, allow_empty_file=False, use_url=False
    )

    class Meta:
        model = AjoUser
        fields = ["image"]

    def validate_image(self, value):
        valid_extensions = [".jpg", ".jpeg", ".png", ".gif"]
        extension = os.path.splitext(value.name)[1].lower()
        if extension not in valid_extensions:
            raise serializers.ValidationError(
                "invalid image file. Only JPG, JPEG, PNG and GIF files are allowed"
            )
        return value


class AssignCardSerializer(serializers.Serializer):
    need_card = serializers.BooleanField()
    card_number = serializers.CharField()
    card_expiry_month = serializers.CharField()
    card_expiry_year = serializers.CharField()
    # card_reference = serializers.CharField()
    # full_name = serializers.CharField()
    # bvn = serializers.CharField(required=False)
    # agent_bvn_used = serializers.BooleanField(required=False)
    # expiry_date = serializers.DateField(required=False)
    # cvv = serializers.CharField(min_length=3, max_length=3, required=False)
    # phone_number = serializers.CharField(required=False)


class ConfirmCardDetailsSerializer(serializers.Serializer):
    bvn = serializers.CharField()
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    phone_number = serializers.CharField()
    agent_bvn_used = serializers.BooleanField()


class GetAgentCardsSerializer(serializers.ModelSerializer):
    class Meta:
        model = Card
        fields = ["card_number", "card_assigned"]


class AjoUsersBasedOnCardsSerializer(serializers.ModelSerializer):
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    phone_number = serializers.CharField()

    class Meta:
        model = AjoUser
        fields = [
            "first_name",
            "last_name",
            "phone_number",
            "alias",
        ]


class ExistingUserSavingsSerializer(serializers.Serializer):
    phone_number = serializers.CharField(
        max_length=11,
        required=True,
        validators=[
            RegexValidator(
                regex=r"^\d{11}$",
                message="phone number must be 11 digits",
                code="invalid_phone_number",
            )
        ],
    )
    pin = serializers.CharField(
        max_length=4,
        required=True,
        validators=[
            RegexValidator(
                regex=r"^\d{4}$",
                message="pin must be 4 digits",
                code="invalid pin",
            )
        ],
    )


# class RequestCardSerializer(serializers.ModelSerializer):
#     nearest_landmark = serializers.CharField(required=False, allow_null=True)

#     class Meta:
#         model = CardRequests
#         fields = [
#             "quantity",
#             "state",
#             "lga",
#             "nearest_landmark",
#             "street",
#             "address_type",
#         ]


class RequestCardSerializer(serializers.Serializer):
    # wallet_type = serializers.CharField()
    quantity = serializers.IntegerField()
    # amount = serializers.FloatField()
    agent_transaction_pin = serializers.CharField()
    state = serializers.CharField()
    lga = serializers.CharField()
    nearest_landmark = serializers.CharField()
    street = serializers.CharField()
    # address_type = serializers.CharField()

    def validate(self, attrs):
        # wallet_types = [WalletTypes.AJO_AGENT, WalletTypes.ROTATIONGROUP, WalletTypes.PERSONAL_AJO]
        # valid_wallet_types = [str(x) for x in wallet_types]
        # wallet_type = attrs.get("wallet_type")
        agent_transaction_pin = attrs.get("agent_transaction_pin")

        if not agent_transaction_pin.isdigit():
            raise serializers.ValidationError(
                "the transaction pin should be numeric only"
            )

        # if wallet_type.upper() not in valid_wallet_types:
        #     raise serializers.ValidationError(f"wallet type should be in this list: {valid_wallet_types}")
        # else:
        #     attrs["wallet_type"] = getattr(WalletTypes, wallet_type.upper())

        return attrs


class DeliveryAddressSerializer(serializers.ModelSerializer):
    count = serializers.IntegerField()

    class Meta:
        model = CardRequests
        fields = [
            "state",
            "lga",
            "nearest_landmark",
            "street",
            "address_type",
            "count",
        ]


class AjoSavingsPlanTypesSerializer(serializers.ModelSerializer):
    class Meta:
        model = AjoSavingsPlanType
        fields = [
            "name",
            "lower_limit",
            "upper_limit",
        ]


class MinimalAjoSepoSerializer(serializers.ModelSerializer):

    class Meta:
        model = AjoSepo
        fields = ["name", "group_id"]


class MinimalAjoSavingsSerializer(serializers.ModelSerializer):
    ajo_user = MinimalAjoUserSerializer()

    class Meta:
        model = AjoSaving
        fields = [
            "id",
            "name",
            "user",
            "ajo_user",
        ]


class AjoSavingsInformationSerializer(serializers.ModelSerializer):
    plan_type = AjoSavingsPlanTypesSerializer()
    group = MinimalAjoSepoSerializer()

    class Meta:
        model = AjoSaving
        fields = [
            "id",
            "name",
            "frequency",
            "periodic_amount",
            "duration",
            "plan_type",
            "lock",
            "amount_saved",
            "quotation_id",
            "frequency_paid",
            "expected_amount",
            "maturity_date",
            "is_activated",
            "is_active",
            "completed",
            "completed_at",
            "commission_paid",
            "loan",
            "is_loan_repayment",
            "savings_type",
            "group",
            "boosta_tenure",
        ]


class AjoSavingFullDetailsSerializer(serializers.Serializer):
    ajo_saving = AjoSavingsInformationSerializer()
    transaction_history = TransactionHistoryForAjoUserSerializer(many=True)


class CreateAjoSavingsPlanSerializer(serializers.ModelSerializer):
    duration = serializers.IntegerField(default=30)
    ajo_type = serializers.CharField()
    frequency = serializers.CharField()
    plan_type = serializers.CharField()
    group = serializers.CharField(
        required=False, allow_null=True, help_text="the group_id of the intended group"
    )
    boosta_tenure = serializers.IntegerField(
        required=False, allow_null=True, help_text="boosta tenure in days"
    )
    boosta_xtype = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )

    class Meta:
        model = AjoSaving
        fields = [
            "name",
            "frequency",
            "duration",
            "periodic_amount",
            "plan_type",
            "ajo_type",
            "group",
            "boosta_tenure",
            "boosta_xtype",
        ]

    def validate(self, attrs):
        periodic_amount = attrs.get("periodic_amount", "")
        frequency = attrs.get("frequency", "")
        plan_type = attrs.get("plan_type", "")
        ajo_type = attrs.get("ajo_type", "")
        savingstype: str | None = self.context.get("savingstype")

        if frequency.upper() not in SavingsFrequency:
            raise serializers.ValidationError("please choose daily, weekly or monthly")
        else:
            attrs["frequency"] = getattr(SavingsFrequency, frequency.upper())

        if plan_type.capitalize() not in AjoSavingsPlanType.objects.exclude(
            name="Starter"
        ).values_list("name", flat=True):
            raise serializers.ValidationError("please choose a valid ajo plan type")
        else:
            attrs["plan_type"] = ajo_savings_plan_type = AjoSavingsPlanType.objects.get(
                name=plan_type.capitalize(), frequency="DAILY"
            )

        if ajo_type.upper() not in AjoType:
            raise serializers.ValidationError(
                f"please choose a valid ajo type from the list: {list(AjoType.__members__.keys())}"
            )
        else:
            attrs["ajo_type"] = getattr(AjoType, ajo_type.upper())

        if savingstype is None:
            if (
                not ajo_savings_plan_type.lower_limit
                <= periodic_amount
                <= ajo_savings_plan_type.upper_limit
            ):
                raise serializers.ValidationError(
                    f"periodic_amount must be within {ajo_savings_plan_type.lower_limit} and {ajo_savings_plan_type.upper_limit}"
                )

        return attrs


class AjoUserPinSerializer(serializers.Serializer):
    phone_number = serializers.CharField(
        max_length=11,
        validators=[
            RegexValidator(
                regex=r"^\d{11}$",
                message="phone number must be 11 digits",
                code="invalid_phone_number",
            )
        ],
    )
    pin = serializers.CharField(max_length=4, min_length=4)

    def validate_pin(self, value):
        if not value.isdigit():
            raise serializers.ValidationError("pin must be 4 digits")

        return value


class AgentLedgerSerializer(serializers.Serializer):
    total_cash_collection = serializers.FloatField()
    total_cash_withdrawn = serializers.FloatField()


class VirtualAccountDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = BankAccountDetails
        fields = [
            "account_number",
            "bank_name",
            "account_name",
            "form_type",
        ]


class AddGroupMemberSerializer(serializers.Serializer):
    members = serializers.ListSerializer(child=serializers.DictField())

    def create(self, validated_data):
        members_data = validated_data.get("members")
        members = []

        for data in members_data:
            position = data.get("position")
            phone_number = data.get("phone_number")
            members.append({"position": position, "phone_number": phone_number})
            print(members)
        return members


class RotationbreakdownSerializer(serializers.Serializer):
    amount = serializers.FloatField(required=True)
    rosca_type = serializers.CharField(required=True)
    collection_type = serializers.CharField(required=True)
    number_of_participants = serializers.IntegerField(required=True)
    no_of_collectors = serializers.IntegerField(required=True)
    frequency = serializers.CharField(required=True)
    duration = serializers.IntegerField(required=True)
    starting_date = serializers.DateField(required=True)


class AgentLogContributionSerializer(serializers.Serializer):
    ids = serializers.ListField(child=serializers.IntegerField(), required=True)
    agent_transaction_pin = serializers.CharField(required=True)


class AgencyBankingAgentInfoSerializer(serializers.Serializer):
    """
    Serializer for AgencyBankingAgentInfo APIView in Ajo views
    """

    user_id = serializers.IntegerField()


class AgentCreateRotationGroupSerializer(serializers.ModelSerializer):
    no_of_collectors = serializers.IntegerField(required=True)

    class Meta:
        model = RotationGroup
        fields = [
            "id",
            "name",
            "amount",
            "duration",
            "frequency",
            "rosca_type",
            "starting_date",
            "no_of_collectors",
            "collection_type",
            "total_amount_saved",
            "progress_amount",
            "number_of_participants",
            "commision",
            "group_wallet_balance",
        ]

    def validate(self, attrs):
        current_date = date.today()
        start_date = attrs.get("starting_date")
        no_of_collectors = attrs.get("no_of_collectors")
        number_of_participants = attrs.get("number_of_participants")
        if start_date is None:
            raise serializers.ValidationError({"message": "starting_date is required"})

        if start_date < current_date:
            raise serializers.ValidationError(
                {"message": "start date cannot be less than current date"}
            )

        if no_of_collectors > 1 and number_of_participants % no_of_collectors != 0:
            raise serializers.ValidationError(
                {
                    "message": "The number of participants must tally with number of collecors"
                }
            )

        return attrs

    def to_representation(self, instance):
        today = date.today()
        representation = super().to_representation(instance)
        member_names = RotationGroupMember.objects.filter(group=instance)
        breadown = RoscaPaymentBreakDown.objects.filter(group=instance)

        member_details = []
        for member in member_names:
            member_details.append(
                {
                    "member_id": member.id,
                    "full_name": member.ajo_user_member.fullname,
                    "position": member.position,
                    "phone_number": member.ajo_user_member.phone_number,
                }
            )

        for object in breadown:
            if object.paid is False and object.rosca_due_date < today:
                representation["past_due"] = True
            else:
                representation["past_due"] = False
        representation["member_details"] = member_details

        return representation


class AgentEditDeleteRotationGroupSerializer(serializers.ModelSerializer):
    class Meta:
        model = RotationGroup
        fields = [
            "id",
            "name",
            "amount",
            "duration",
            "frequency",
            "rosca_type",
            "starting_date",
            "no_of_collectors",
            "collection_type",
            "total_amount_saved",
            "progress_amount",
            "number_of_participants",
            "commision",
        ]

    def validate(self, attrs):
        current_date = date.today()
        start_date = attrs.get("starting_date")
        if start_date is None:
            raise serializers.ValidationError({"message": "starting_date is required"})

        if start_date < current_date:
            raise serializers.ValidationError(
                {"message": "start date cannot be less than current date"}
            )

        return attrs


class GetPlansSerializer(serializers.Serializer):
    duration_in_days = serializers.IntegerField()
    frequency = serializers.ChoiceField(choices=SavingsFrequency.choices)
    target = serializers.FloatField(required=False)
    group = serializers.CharField(required=False, allow_null=True)
    boosta_tenure = serializers.IntegerField(
        required=False, allow_null=True, help_text="boosta tenure in days"
    )
    boosta_xtype = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    # plan_name = serializers.ChoiceField(choices=AjoPlanTypes.choices, allow_null=True)


class GetSpendingDigitalSerializer(serializers.Serializer):
    spending_account = VirtualAccountDetailsSerializer()
    digital_account = VirtualAccountDetailsSerializer()


class AjoUserListSerializer(serializers.ModelSerializer):
    class Meta:
        model = AjoUser
        fields = [
            "first_name",
            "last_name",
            "alias",
            "gender",
            "marital_status",
            "state",
            "lga",
            "address",
            "trade",
            "trade_location",
            "phone_number",
        ]


class RoscaPaymentBreakDownSerializer(serializers.ModelSerializer):
    class Meta:
        model = RoscaPaymentBreakDown
        fields = [
            "id",
            "frequency",
            "due_amount",
            "paid_amount",
            "paid",
            "day_count",
            "rosca_due_date",
            "group",
            "phone_number",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        group = representation["group"]

        if group:
            representation["group_duration"] = instance.group.duration

        return representation


class RotationGroupMemberSerializer(serializers.ModelSerializer):
    class Meta:
        model = RotationGroupMember
        fields = [
            "id",
            "position",
            "collected",
            "collection_date",
            "collection_date",
            "full_name",
            "phone_number",
            "group",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        group = representation["group"]
        group = instance.group

        representation["amount"] = group.amount
        representation["duration"] = group.duration

        if group.no_of_collectors > 1:
            representation["amount"] = group.collection_amount
        return representation


class ProfileChangeRequestSerializer(serializers.ModelSerializer):
    class Meta:
        model = AjoUser
        fields = [
            "first_name",
            "last_name",
            "phone_number",
            "address",
        ]


class AjoUserChangeSerializer(serializers.ModelSerializer):
    class Meta:
        model = AjoUser
        fields = [
            "first_name",
            "last_name",
            "phone_number",
            "alias",
            "gender",
            "marital_status",
            "state",
            "lga",
            "address",
            "trade",
            "trade_location",
            "bvn",
            "nin",
        ]


class AjoNextOfKinSerializer(NextOfKinSerializer):
    phone_number = serializers.CharField(
        max_length=11,
        required=True,
        validators=[
            RegexValidator(
                regex=r"^\d{11}$",
                message="phone number must be 11 digits",
                code="invalid_phone_number",
            )
        ],
    )

    class Meta(NextOfKinSerializer.Meta):
        fields = NextOfKinSerializer.Meta.fields + [
            "phone_number",
        ]


class ProductInformationSerializer(serializers.ModelSerializer):
    savings_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = ProductInformation
        fields = "__all__"


class AgentAjoSepoStatusSerializer(serializers.Serializer):
    next_savings_type = serializers.CharField()
    individual_savings_count = serializers.IntegerField()


class AjoSepoSerializer(serializers.ModelSerializer):

    class Meta:
        model = AjoSepo
        fields = "__all__"


class CreateAjoSepoSerializer(serializers.ModelSerializer):
    leader = serializers.CharField(
        max_length=12,
        validators=[
            RegexValidator(
                regex=r"^\d{11}$",
                message="phone number must be 11 digits",
                code="invalid_phone_number",
            )
        ],
        required=False,
        allow_null=True,
    )

    class Meta:
        model = AjoSepo
        fields = [
            "name",
            "participants",
            "leader",
            "meeting_days",
            "loan_tenure",
            "repayment_type",
        ]

    def validate(self, attrs):
        participants = attrs.get("participants")

        if not (6 <= participants <= 12):
            raise serializers.ValidationError("the participants must between 6 and 12")

        repayment_type = attrs.get("repayment_type")
        if not repayment_type.upper() in RepaymentFrequency:
            raise serializers.ValidationError(
                f"please choose from: {list(RepaymentFrequency.__members__.keys())}"
            )
        else:
            attrs["repayment_type"] = getattr(
                RepaymentFrequency, repayment_type.upper()
            )

        return attrs


class JoinAjoSepoGroupSerializer(serializers.Serializer):
    group_id = serializers.CharField()
    savings_id = serializers.IntegerField()


class AjoSepoDetailsSerializer(AjoSepoSerializer):
    joined_members = MinimalAjoSavingsSerializer(many=True)
    joined_members_count = serializers.IntegerField()
    user = UserSerializer()
    leader = MinimalAjoUserSerializer()

    class Meta(AjoSepoSerializer.Meta):
        fields = "__all__"


class UpdateAjoUserImageSerializer(serializers.Serializer):
    admin_email = serializers.CharField()
    ajo_user_id = serializers.CharField()
    image = serializers.FileField()
