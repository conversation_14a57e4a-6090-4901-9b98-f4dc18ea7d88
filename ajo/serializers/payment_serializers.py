from datetime import datetime

from django.core.validators import RegexValidator
from rest_framework import serializers

from payment.model_choices import WalletTypes
from payment.models import Commission

from ..models import Ajo<PERSON>ser, AjoUserWithdrawalAccount, ProfileChangeRequest


class PayForAjoPlanSerializer(serializers.Serializer):
    """
    This is for paying for Ajo users' plans from agent's
    wallet
    """

    plan_id = serializers.IntegerField()
    amount = serializers.FloatField()
    user_transaction_pin = serializers.CharField()
    agent_transaction_pin = serializers.CharField()

    def validate(self, attrs):
        amount = attrs.get("amount", "")
        agent_transaction_pin = attrs.get("agent_transaction_pin", "")
        user_transaction_pin = attrs.get("user_transaction_pin", "")

        if not (user_transaction_pin.isdigit() and agent_transaction_pin.isdigit()):
            raise serializers.ValidationError("the transaction pin should be numeric only")

        attrs["amount"] = round(amount, 2)
        return attrs


class PayForLoanSerializer(serializers.Serializer):
    """
    This is for paying for Ajo users' loans from agent's
    wallet
    """

    phone_number = serializers.CharField(
        max_length=11,
        validators=[
            RegexValidator(
                regex=r"^\d{11}$",
                message="phone number must be 11 digits",
                code="invalid_phone_number",
            )
        ],
    )
    amount = serializers.FloatField()
    user_transaction_pin = serializers.CharField()
    agent_transaction_pin = serializers.CharField()

    def validate(self, attrs):
        amount = attrs.get("amount", "")
        agent_transaction_pin = attrs.get("agent_transaction_pin", "")
        user_transaction_pin = attrs.get("user_transaction_pin", "")

        if not (user_transaction_pin.isdigit() and agent_transaction_pin.isdigit()):
            raise serializers.ValidationError("the transaction pin should be numeric only")

        attrs["amount"] = round(amount, 2)
        return attrs


class CommissionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = Commission
        fields = [
            "amount",
            "description",
            "created_at",
            "withdrawn",
        ]


class TotalCommissionsSerializer(serializers.Serializer):
    total_commission = serializers.FloatField()


class GetCommissionsSerializer(TotalCommissionsSerializer):
    # total_commission = serializers.FloatField()
    commissions = CommissionsSerializer(many=True)


class CashoutRequestSerializer(serializers.Serializer):
    amount = serializers.FloatField()
    plan_id = serializers.IntegerField()


class CashoutFromSpendingSerializer(serializers.Serializer):
    amount = serializers.FloatField()
    phone_number = serializers.CharField(
        max_length=11,
        validators=[
            RegexValidator(
                regex=r"^\d{11}$",
                message="phone number must be 11 digits",
                code="invalid_phone_number",
            )
        ],
        help_text="the phone number of ajo user",
    )


# class CashoutRequestSerializer(serializers.Serializer):
#     amount = serializers.FloatField()
#     phone_number = serializers.CharField(
#         max_length=11,
#         validators=[
#             RegexValidator(
#                 regex=r"^\d{11}$",
#                 message="phone number must be 11 digits",
#                 code="invalid_phone_number",
#             )
#         ],
#     )


class TriggerCashoutSerializer(serializers.Serializer):
    plan_id = serializers.IntegerField()
    # phone_number = serializers.CharField(
    #     max_length=11,
    #     validators=[
    #         RegexValidator(
    #             regex=r"^\d{11}$",
    #             message="phone number must be 11 digits",
    #             code="invalid_phone_number",
    #         )
    #     ],
    # )
    otp = serializers.CharField(max_length=6, min_length=6)
    agent_transaction_pin = serializers.CharField()

    def validate(self, attrs):
        otp: str = attrs.get("otp", "")
        agent_transaction_pin: str = attrs.get("agent_transaction_pin", "")

        if not otp.isdigit():
            raise serializers.ValidationError("the otp should be numeric only")

        if not agent_transaction_pin.isdigit():
            raise serializers.ValidationError("the transaction pin should be numeric only")

        return attrs


class TriggerCashoutFromSpendingSerializer(serializers.Serializer):
    phone_number = serializers.CharField(
        max_length=11,
        validators=[
            RegexValidator(
                regex=r"^\d{11}$",
                message="phone number must be 11 digits",
                code="invalid_phone_number",
            )
        ],
    )
    otp = serializers.CharField(max_length=6, min_length=6)
    agent_transaction_pin = serializers.CharField()

    def validate(self, attrs):
        otp: str = attrs.get("otp", "")
        agent_transaction_pin: str = attrs.get("agent_transaction_pin", "")

        if not otp.isdigit():
            raise serializers.ValidationError("the otp should be numeric only")

        if not agent_transaction_pin.isdigit():
            raise serializers.ValidationError("the transaction pin should be numeric only")

        return attrs


class WithdrawToAgencyWalletSerializer(serializers.Serializer):
    wallet_type = serializers.CharField()
    amount = serializers.FloatField()
    plan_id = serializers.IntegerField(required=False, allow_null=True)
    agent_transaction_pin = serializers.CharField()

    def validate(self, attrs):
        wallet_types = [
            WalletTypes.AJO_AGENT,
            WalletTypes.ROSCA_PERSONAL,
            WalletTypes.AJO_PERSONAL,
            WalletTypes.ONLENDING,
            WalletTypes.AJO_COMMISSION,
        ]
        valid_wallet_types = [str(x) for x in wallet_types]

        wallet_type = attrs.get("wallet_type")
        agent_transaction_pin = attrs.get("agent_transaction_pin")
        plan_id = attrs.get("plan_id")
        amount = attrs.get("amount")

        if not agent_transaction_pin.isdigit():
            raise serializers.ValidationError("the transaction pin should be numeric only")

        if wallet_type.upper() not in valid_wallet_types:
            raise serializers.ValidationError(f"wallet type should be in this list: {valid_wallet_types}")
        else:
            attrs["wallet_type"] = getattr(WalletTypes, wallet_type.upper())

        if wallet_type.upper() in [
            WalletTypes.AJO_AGENT,
            WalletTypes.ROSCA_PERSONAL,
            WalletTypes.AJO_COMMISSION,
        ]:
            if plan_id:
                raise serializers.ValidationError("there should not be a plan_id in the request")

        if amount <= 0:
            raise serializers.ValidationError("the amount to withdraw should be greater than 0")

        return attrs


class PrefundingWalletSerializer(serializers.Serializer):
    available_balance = serializers.FloatField()
    hold_balance = serializers.FloatField()
    due_balance = serializers.FloatField()


class CheckAccountDetailsSerializer(serializers.Serializer):
    """
    Account number, bank name and bank code for checking for the account name
    """

    account_number = serializers.CharField(
        max_length=10,
        validators=[
            RegexValidator(
                regex=r"^\d{10}$",
                message="account number must be 10 digits",
                code="invalid_account_number",
            )
        ],
    )
    bank_name = serializers.CharField()
    bank_code = serializers.CharField(
        max_length=9,
        validators=[
            RegexValidator(
                regex=r"^\d{6,9}$",
                message="bank code must be 6 to 9 digits",
                code="invalid_bank_code",
            )
        ],
    )


class SetVerifiedAccountDetailsSerializer(CheckAccountDetailsSerializer):
    """
    all the information to save in the database alongside an OTP to verify
    """

    account_name = serializers.CharField()
    otp = serializers.CharField(
        validators=[
            RegexValidator(
                regex=r"^\d{6}$",
                message="OTP must be 6 digits",
                code="invalid_otp_code",
            )
        ]
    )


class AjoUserWithdrawalAccountSerializer(serializers.ModelSerializer):
    class Meta:
        model = AjoUserWithdrawalAccount
        fields = [
            "account_number",
            "account_name",
            "bank_name",
        ]


class PayForDojahVerificationSerializer(serializers.Serializer):
    phone_number = serializers.CharField(
        max_length=11,
        validators=[
            RegexValidator(
                regex=r"^\d{11}$",
                message="phone number must be 11 digits",
                code="invalid_phone_number",
            )
        ],
    )
    verification_type = serializers.CharField(max_length=5)

    def validate(self, attrs):
        valid_verification_types = ["BVN", "NIN"]

        verification_type = attrs.get("verification_type")

        if verification_type.upper() not in valid_verification_types:
            raise serializers.ValidationError(f"verification_type should be in the list: {valid_verification_types}")
        else:
            attrs["verification_type"] = verification_type.upper()

        return attrs


class AjoUserProfileChangeSerializer(serializers.ModelSerializer):
    class Meta:
        model = AjoUser
        fields = ["phone_number"]


class ProfileChangeRequestDetailsSerializer(serializers.ModelSerializer):
    ajo_user = AjoUserProfileChangeSerializer()

    class Meta:
        model = ProfileChangeRequest
        fields = [
            "session_id",
            "ajo_user",
            "status",
        ]


class WithdrawalAccountAuthSerializer(serializers.Serializer):
    balance = serializers.FloatField()
    bank_name = serializers.CharField()
    account_number = serializers.CharField()
    account_name = serializers.CharField()


class WithdrawToExternalAccountSerializer(serializers.Serializer):
    phone_number = serializers.CharField(
        max_length=11,
        validators=[
            RegexValidator(
                regex=r"^\d{11}$",
                message="phone number must be 11 digits",
                code="invalid_phone_number",
            )
        ],
        required=False,
        allow_null=True,
    )
    plan_id = serializers.IntegerField(required=False, allow_null=True)
    wallet_type = serializers.CharField()
    amount = serializers.FloatField(required=False, allow_null=True)

    def validate(self, attrs):
        valid_wallet_types = [
            "SAVINGS",
            "SPENDING",
            "ONLENDING",
            "ONLENDING_MAIN",
        ]

        wallet_type = attrs.get("wallet_type")
        plan_id = attrs.get("plan_id")
        phone_number = attrs.get("phone_number")

        if wallet_type.upper() not in valid_wallet_types:
            raise serializers.ValidationError(f"wallet type should be in this list: {valid_wallet_types}")
        else:
            attrs["wallet_type"] = wallet_type = wallet_type.upper()

        if wallet_type in ["SPENDING", "ONLENDING_MAIN"]:
            if plan_id:
                raise serializers.ValidationError("there should not be a plan_id in the request")

        if wallet_type in ["SAVINGS", "SPENDING"]:
            if not phone_number:
                raise serializers.ValidationError("there should be a phone number in the request")

        return attrs


class AjoUserInfoForCardSerializer(AjoUserProfileChangeSerializer):
    class Meta(AjoUserProfileChangeSerializer.Meta):
        fields = AjoUserProfileChangeSerializer.Meta.fields + [
            "first_name",
            "last_name",
            "alias",
        ]


class CardUserInformationSerializer(serializers.Serializer):
    status = serializers.BooleanField()
    data = AjoUserInfoForCardSerializer()


class PayForOnlendingPlanSerializer(serializers.Serializer):
    plan_id = serializers.IntegerField()
    wallet_type = serializers.CharField()

    def validate(self, attrs):
        valid_wallet_types = [
            "AJO_AGENT",
            "AJO_SPENDING",
        ]

        wallet_type = attrs.get("wallet_type")

        if wallet_type.upper() not in valid_wallet_types:
            raise serializers.ValidationError(f"wallet type should be in this list: {valid_wallet_types}")
        else:
            attrs["wallet_type"] = getattr(WalletTypes, wallet_type.upper())

        return attrs
