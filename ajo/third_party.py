import json

import requests
from django.conf import settings

from accounts.services import SMSResponseService
from ajo.model_choices import OTPType
from ajo.models import AjoUser
from helper_methods import format_api_response


class OTP:
    whisper_url = settings.WHISPER_URL
    whisper_token = settings.WHISPER_TOKEN
    sms_voice_application_id = settings.SMS_VOICE_APPLICATION_ID
    sms_voice_message_id = settings.SMS_VOICE_MESSAGE_ID
    whatsapp_base_url = settings.WHATSAPP_BASE_URL
    whatsapp_token = settings.WHATSAPP_TOKEN

    @classmethod
    def get_phone_number(cls, phone_number):
        """
        Extracts and returns the corrected phone number from a string that may contain a separator.

        This method is designed to correct phone numbers that contain the '---' separator.
        It works by splitting the phone number string at the '---' and returning the first part,
        which represents the actual phone number before the separator.

        Args:
            phone_number (str): The input phone number, which may contain '---' as a separator.

        Returns:
            str: The corrected phone number (portion before '---').

        Example:
            >>> get_phone_number('1234567890---extra info')
            '1234567890'
        """
        return phone_number.split("---")[0]

    # @classmethod
    # def sms_voice_otp(cls, otp_type: OTPType, phone_number: str) -> dict:
    #     # print("BEFORE UPDATE", phone_number)
    #     phone_number = cls.get_phone_number(phone_number=phone_number)
    #     # print("AFTER UPDATE", phone_number)
    #     types = [OTPType.SMS, OTPType.VOICE]
    #     if otp_type.upper() not in types:
    #         return {"status": False, "error": "choose a valid type: SMS or VOICE"}

    #     url = f"{cls.whisper_url}/2fa/message/"

    #     headers = {
    #         "Authorization": f"Api_key {cls.whisper_token}",
    #         "Content-Type": "application/json",
    #         # "Cookie": "csrftoken=r9lRLhv4KGIx4MVvM8QdS0cIXJIKwkeHNfWqW5DS3egOHq1j7zn0IfandVIvc1ex; sessionid=422w2t36dknbcflysiwog97z2vv4n8ek",
    #     }

    #     payload = json.dumps(
    #         {
    #             "applicationId": str(cls.sms_voice_application_id),
    #             "messageId": str(cls.sms_voice_message_id),
    #             "message_type": (
    #                 "INTERNAL-TEXT" if otp_type.upper() == "SMS" else "VOICE"
    #             ),
    #             "to": str(phone_number),
    #             "placeholders": {},
    #         }
    #     )

    #     try:
    #         r = requests.post(url=url, headers=headers, data=payload)
    #         resp = r.json()
    #         response = {}
    #         if r.status_code == 200:
    #             response["status"] = True
    #         else:
    #             response["status"] = False

    #         response["data"] = resp
    #         return response

    #     except requests.exceptions.RequestException as err:
    #         return {
    #             "status": False,
    #             "message": "error occured, please try again",
    #             "error_message": str(err),
    #         }
    @classmethod
    def sms_voice_otp(cls, otp_type: OTPType, phone_number: str) -> dict:
        from ajo.utils.ussd_utils import generate_ussd_otp

        # print("BEFORE UPDATE", phone_number)
        phone_number = cls.get_phone_number(phone_number=phone_number)
        # print("AFTER UPDATE", phone_number)
        types = [OTPType.SMS, OTPType.VOICE]
        if otp_type.upper() not in types:
            return {"status": False, "error": "choose a valid type: SMS or VOICE"}

        url = f"{cls.whisper_url}/transactional/send"

        otp = generate_ussd_otp(phone_number=phone_number)
        print(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
        print(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
        print("USER PHONE NUMBER", phone_number)
        print("OTP", otp)
        print(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
        print(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
        headers = {
            "Authorization": f"Api_key {cls.whisper_token}",
            "Content-Type": "application/json",
            # "Cookie": "csrftoken=r9lRLhv4KGIx4MVvM8QdS0cIXJIKwkeHNfWqW5DS3egOHq1j7zn0IfandVIvc1ex; sessionid=422w2t36dknbcflysiwog97z2vv4n8ek",
        }

        payload = {
            "receiver": phone_number,
            "template": "e6f8f5b4-5461-46df-a3e7-d2797d2dd776",
            "place_holders": {"username": "customer", "otp": otp},
        }

        r = requests.post(url=url, headers=headers, json=payload)
        print(r.text)
        try:
            resp = r.json()
            response = {}
            if r.status_code in range(200, 209):
                response["status"] = True
                response["otp"] = otp
            else:
                response["status"] = False

            response["data"] = resp
            return response

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "error occured, please try again",
                "error_message": str(err),
            }

    # @classmethod
    # def verify_sms_voice_otp(cls, otp: str, phone_number: str):
    #     phone_number = cls.get_phone_number(phone_number=phone_number)
    #     url = f"{cls.whisper_url}/2fa/verify/"

    #     headers = {
    #         "Authorization": f"Api_key {cls.whisper_token}",
    #         "Content-Type": "application/json",
    #         # "Cookie": "csrftoken=3E71V7RLnBBtt6a6atm5cV9uQUWcwbD7S1EjaSbyHyWHEWc0Bv2QsyiQ0RzaLpUC; sessionid=422w2t36dknbcflysiwog97z2vv4n8ek",
    #     }

    #     payload = json.dumps(
    #         {
    #             "otp": str(otp),
    #             "receiver": str(phone_number),
    #         }
    #     )

    #     try:
    #         r = requests.post(
    #             url=url,
    #             headers=headers,
    #             data=payload,
    #         )

    #         resp = r.json()
    #         response = {}
    #         if r.status_code == 200:
    #             response["status"] = True
    #         else:
    #             response["status"] = False

    #         response["data"] = resp
    #         return response

    #     except requests.exceptions.RequestException as err:
    #         return {
    #             "status": False,
    #             "message": "error occured, please try again",
    #             "error": str(err),
    #         }

    @classmethod
    def verify_sms_voice_otp(cls, otp: str, phone_number: str):
        from ajo.utils.ussd_utils import verify_ussd_otp

        phone_number = cls.get_phone_number(phone_number=phone_number)
        is_verified = verify_ussd_otp(otp=otp, phone_number=phone_number)
        print("<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<")
        print("<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<")
        print("<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<")
        print("USER PHONE NUMBER ON VERIFY", phone_number)
        print("VERIFICATION STATUS", is_verified)
        print("<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<")
        print("<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<")
        print("<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<")

        result = {"verified": is_verified, "message": "success"}
        print(result)
        return result

    @classmethod
    def send_otp_through_whatsapp(cls, otp: str, phone_number: str) -> dict:
        phone_number = cls.get_phone_number(phone_number=phone_number)
        url = f"{cls.whatsapp_base_url}/app/api/v2/push"

        headers = {"Content-Type": "application/json"}

        payload = json.dumps(
            {
                "token": str(cls.whatsapp_token),
                "priority ": "0",
                "application": "10",
                "globalmessage": f"Ajo Savings: {otp} is your One Time code. Do not share your code.",
                "globalmedia": "",
                "data": [{"number": f"234{phone_number[1:]}", "message": ""}],
            }
        )

        try:
            r = requests.post(url=url, data=payload, headers=headers)
            res = r.json()
            return res

        # sample successful response
        # {'status': True, 'data': {'status': 100, 'push_id': '90363029', 'message': 'Success', 'data': [{'msg_id': '123653772', 'number': '2348055310207', 'credit': '0'}]}}

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "error occured, please try again",
                "error_message": str(err),
            }


class IdentityPass:
    """
    This class handles interactions with IdentityPass 3rd party API
    """

    base_url = settings.ID_PASS_BASE_URL
    api_key = settings.ID_PASS_API_KEY
    app_id = settings.ID_PASS_APP_ID

    @classmethod
    def bvn_with_face_verification(cls, bvn: str, face_image_url: str) -> dict:
        """
        Verifies if the BVN is valid, if it is, it will check if the face
        in the image link matches the face in the BVN

        Args:
            bvn (str): the BVN to be verified
            face_image_url (str): the URL to the image of the face to be verified

        Returns:
            dict: _description_
        """
        url = f"{cls.base_url}/api/v2/biometrics/merchant/data/verification/bvn_w_face"

        headers = {
            "Content-Type": "application/json",
            "x-api-key": cls.api_key,
            "app-id": cls.app_id,
        }

        payload = json.dumps(
            {
                "number": bvn,
                "image": face_image_url,
            }
        )

        try:
            r = requests.post(
                url=url,
                data=payload,
                headers=headers,
            )
            res = r.json()
            return res

        except requests.RequestException as err:
            return {
                "status": False,
                "error": "error occurred, please try again",
                "error_message": str(err),
            }


class LibertyBVN:
    """
    This class handles everything that has to do with BVN
    """

    base_url = settings.LIBERTY_BVN_URL
    token = settings.LIBERTY_BVN_TOKEN

    @classmethod
    def verify_bvn(cls, bvn: str) -> dict:
        """
        Verifies if a BVN is valid

        Args:
            bvn (str): the BVN of the individual

        Returns:
            dict: a dictionary of the data
        """
        url = f"{cls.base_url}/loan/bvn_details/"

        headers = {
            "Savings-Web-Token": cls.token,
            "Content-Type": "application/json",
        }

        payload = json.dumps(
            {
                "bvn": str(bvn),
            }
        )

        try:
            r = requests.post(
                url=url,
                data=payload,
                headers=headers,
                timeout=30,
            )

            res = r.json()

            response = {
                "status": False,
                "data": res,
            }

            if res.get("data", {}).get("success"):
                response["status"] = True

            return response

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "error": "error occured, please try again",
                "error_message": str(err),
            }


class TextMessages:
    whisper_url = settings.WHISPER_URL
    whisper_token = settings.WHISPER_TOKEN
    seeds_and_penny_whisper_token = settings.SEEDS_AND_PENNY_WHISPER_TOKEN

    @classmethod
    def send_user_information_text(cls, phone_number: str, user_pin: str):
        url = f"{cls.whisper_url}/api/send_transactional_message/"

        headers = {
            "Authorization": f"Api_key {cls.whisper_token}",
            "Content-Type": "application/json",
            # "Cookie": "csrftoken=IbyQGdSj6h62JSDYTT3Uzhxd73sjTz97WRI6qoeE95ajWRpGQaKCNK1gjZbA9vVJ; sessionid=rsud9p0swlrh9k95hww2euz5y2wfb2d0",
        }

        payload = json.dumps(
            {
                "receiver": str(phone_number),
                "template": "d00a4633-83bd-4bb5-a7c7-116b41c7b297",
                "place_holders": {
                    "username": str(phone_number),
                    "pin": str(user_pin),
                },
            }
        )

        response = {}
        try:
            r = requests.post(
                url=url,
                headers=headers,
                data=payload,
            )
            return r.json()

        except requests.exceptions.RequestException as err:
            response.update(
                {
                    "status": False,
                    "error_message": str(err),
                    "message": "check the error_message and try again",
                }
            )
            return response

    @classmethod
    def dynamic_send_sms(
        cls,
        phone_number: str,
        template_id: str,
        placeholders: dict,
        user=None,
        ajo_user: AjoUser | None = None,
    ):
        url = f"{cls.whisper_url}/api/send_transactional_message/"

        headers = {
            "Authorization": f"Api_key {cls.whisper_token}",
            "Content-Type": "application/json",
        }

        payload = json.dumps(
            {
                "receiver": f"{phone_number}",
                "template": f"{template_id}",
                "place_holders": placeholders,
            }
        )

        sms_res = SMSResponseService.create_sms_response_instance(
            user=ajo_user.user if ajo_user else user,
            ajo_user=ajo_user,
            payload=json.dumps(payload),
            message=json.dumps(placeholders),
            receiver=phone_number,
        )

        try:
            r = requests.post(
                url=url,
                headers=headers,
                data=payload,
                timeout=90,
            )

            res = r.json()
            SMSResponseService.update_after_request(
                sms_instance=sms_res,
                response_payload=json.dumps(res),
                sent=True,
            )
            return res

        except requests.RequestException as err:
            response = {
                "status": False,
                "error_message": str(err),
                "message": "check the error_message and try again",
            }
            SMSResponseService.update_after_request(
                response_payload=json.dumps(response)
            )
            return response

    @classmethod
    def send_sms(cls, user_phone, message):
        url = "https://whispersms.xyz/transactional/send"
        headers = {
            "Authorization": f"Api_key {cls.seeds_and_penny_whisper_token}",
            "Content-Type": "application/json",
        }

        payload = json.dumps(
            {
                "receiver": f"{user_phone}",
                "template": "4e08c548-54ba-453e-8cad-4876713a3f05",
                "place_holders": {"message": message},
            }
        )

        response = requests.request("POST", url, headers=headers, data=payload)

        return format_api_response(payload=payload, response=response, url=url)
