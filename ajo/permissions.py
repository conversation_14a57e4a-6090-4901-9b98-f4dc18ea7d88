from django.conf import settings
from django.contrib.auth.hashers import check_password

# from django.core.cache import cache
from rest_framework import permissions, status
from rest_framework.exceptions import PermissionDenied

from accounts.model_choices import UserType
from accounts.models import CustomUser
from accounts.services import UserService
from ajo.models import AjoUser
from payment.models import WebhookSettings


class CustomPermissionDenied(PermissionDenied):
    def __init__(self, detail=None, code=None):
        data = {"error": "1012", "status": "failed", "message": detail}
        super().__init__(detail=data, code=code)


# TODO: consider caching this for faster requests
# def get_ajo_user_checks(request, phone_number: str, cache_timeout=settings.DEFAULT_CACHE_TIMEOUT):
#     cache_key = f"ajo_user_checks:{request.user.id}:{phone_number}"
#     cached_checks = cache.get(cache_key)
#     if cached_checks is not None:
#         return cached_checks

#     checks = {}
#     try:
#         ajo_user = AjoUser.objects.filter(agent=request.user, phone_number=phone_number).last()
#         if not ajo_user:
#             checks["exists"] = False
#         else:
#             checks["verified"] = ajo_user.onboarding_verified
#             checks["exists"] = True
#     except AjoUser.DoesNotExist:
#         checks["exists"] = False

#     # Cache the result
#     cache.set(cache_key, checks, cache_timeout)
#     return checks


def get_ajo_user_checks(request, phone_number: str):
    ajo_user = (
        AjoUser.objects.filter(user=request.user, phone_number__startswith=phone_number)
        .exclude(phone_number__contains="---")
        .last()
    )
    if ajo_user:
        return {
            "ajo_user": ajo_user,
            "exists": True,
            "verified": ajo_user.onboarding_verified,
        }
    else:
        return {
            "ajo_user": None,
            "exists": False,
            "verified": False,
        }

class AjoUserUnderAgentPermission(permissions.BasePermission):
    def has_permission(self, request, view):
        phone_number = request.query_params.get("phone_number")
        if not phone_number:
            raise CustomPermissionDenied(
                detail="phone number of ajo user is required as a query parameter in this request"
            )
        if not get_ajo_user_checks(request=request, phone_number=phone_number).get(
            "exists"
        ):
            raise CustomPermissionDenied(
                detail="this ajo user does not exist under this agent"
            )
        return True


class AjoUserOnboardedPermission(AjoUserUnderAgentPermission):
    def has_permission(self, request, view):
        super().has_permission(request, view)
        phone_number = request.query_params.get("phone_number")
        if not get_ajo_user_checks(request, phone_number=phone_number).get("verified"):
            raise CustomPermissionDenied(
                detail="this ajo user is not verified, please properly onboard this user"
            )
        return True


class PersonalUserPermission(permissions.BasePermission):
    def has_permission(self, request, view):
        access_token = request.headers.get("Authorization", "").partition(" ")[-1]
        user = request.user
        try:
            user_type = UserService.get_user_type(user=user, access_token=access_token)
        except Exception as err:
            raise CustomPermissionDenied(
                detail="there was a problem verifying your user type"
            )
        if user_type is not UserType.PERSONAL:
            raise CustomPermissionDenied(
                detail="only 'PERSONAL' type users can perform this operation"
            )
        return True


class AgentUserPermission(permissions.BasePermission):
    def has_permission(self, request, view):
        access_token = request.headers.get("Authorization", "").partition(" ")[-1]
        user = request.user
        try:
            user_type = UserService.get_user_type(user=user, access_token=access_token)
        except Exception as err:
            raise CustomPermissionDenied(
                detail="there was a problem verifying your user type"
            )
        if user_type is not UserType.AGENT:
            raise CustomPermissionDenied(
                detail="only 'AGENT' type users can perform this operation"
            )
        return True


# to be considered later
# from django.core.cache import cache
# from django.conf import settings

# def get_ajo_user_checks(request, phone_number: str, cache_timeout=settings.DEFAULT_CACHE_TIMEOUT):
#     cache_key = f"ajo_user_checks:{request.user.id}:{phone_number}"
#     cached_checks = cache.get(cache_key)
#     if cached_checks is not None:
#         return cached_checks

#     checks = {}
#     try:
#         ajo_user = AjoUser.objects.filter(agent=request.user, phone_number=phone_number).last()
#         if not ajo_user:
#             checks["exists"] = False
#         else:
#             checks["verified"] = ajo_user.onboarding_verified
#             checks["exists"] = True
#     except AjoUser.DoesNotExist:
#         checks["exists"] = False

#     # Cache the result
#     cache.set(cache_key, checks, cache_timeout)
#     return checks


class VFDWebhookWhitelist(permissions.BasePermission):
    """
    Permission to check for VFD Whitelisted Hook for callback.
    """

    def has_permission(self, request, view):
        auth_header = request.headers.get("Authorization", "")

        token_type, _, credentials = auth_header.partition(" ")

        if (
            token_type != "Hook"
            or credentials != settings.AGENCY_BANKING_CALLBACK_TOKEN
        ):
            return False
        else:
            return True


class AjoUSSDWebhook(permissions.BasePermission):
    """
    Permission to check for Ajo WebHook for callback.
    """

    def has_permission(self, request, view):
        auth_header = request.headers.get("Authorization", "")

        token_type, _, credentials = auth_header.partition(" ")

        if token_type != "Hook" or credentials != settings.AJO_CALLBACK_TOKEN:
            return False
        else:
            return True


class CoreRepaymentWebhook(permissions.BasePermission):
    """
    Permission to check for Ajo WebHook for callback.
    """

    def has_permission(self, request, view):
        auth_header = request.headers.get("Authorization", "")

        token_type, _, credentials = auth_header.partition(" ")

        if token_type != "Hook" or credentials != settings.REPAYMENT_CALLBACK_TOKEN:
            return False
        else:
            return True


class HookCustomPermissionDenied(PermissionDenied):
    def __init__(self, detail=None, code=None):
        data = {"error": "845", "status": False, "message": detail}
        super().__init__(detail=data, code=code)


class HookAuthorizationPermission(permissions.BasePermission):
    def has_permission(self, request, view):
        settings = WebhookSettings.get_settings()

        # if no WebhookSettings instance is returned
        if settings is None:
            raise HookCustomPermissionDenied(
                detail="please set an authorization header for this endpoint in WebhookSettings"
            )

        # Perform the authorization header check
        header_value: str = request.headers.get("Authorization", None)
        # if header_value != f"Hook {settings.auth_header}":
        #     raise HookCustomPermissionDenied(detail="invalid authorization key")

        if not header_value:
            raise HookCustomPermissionDenied(detail="no Authorization header")

        if not header_value.startswith("Hook "):
            raise HookCustomPermissionDenied(detail="invalid authorization key pattern")

        key = header_value[5:]
        if not check_password(key, settings.auth_header):
            raise HookCustomPermissionDenied(detail="invalid authorization key")

        return True


class CustomAdminPermission(permissions.IsAuthenticated):
    def has_permission(self, request, view):
        if not super().has_permission(request, view):
            return False

        user = request.user
        allowed_admin_emails = settings.ADMIN_USER_EMAIL

        if user.email in allowed_admin_emails:
            admin_user_id = request.query_params.get("admin_user_id")
            if admin_user_id:
                try:
                    custom_user = CustomUser.objects.get(id=admin_user_id)
                    request.user = custom_user
                except CustomUser.DoesNotExist:
                    pass

        return True
