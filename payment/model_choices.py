from django.db import models
from django.utils.translation import gettext_lazy as _


class Status(models.TextChoices):
    SUCCESS = "SUCCESS", _("SUCCESS")
    FAILED = "FAILED", _("FAILED")
    PENDING = "PENDING", _("PENDING")
    IN_PROGRESS = "IN_PROGRESS", _("IN_PROGRESS")


class TransactionFormType(models.TextChoices):
    """
    Information about the Transaction in terms of
    DEPOSIT, WALLET_DEPOSIT, WITHDRAWAL etc
    """

    DEPOSIT = "DEPOSIT", _("DEPOSIT")
    WITHDRAWAL = "WITHDRAWAL", _("WITHDRAWAL")
    BANK_DEPOSIT = "BANK_DEPOSIT", _("BANK DEPOSIT")
    WALLET_DEPOSIT = "WALLET_DEPOSIT", _("WALLET DEPOSIT")
    IN_WALLET_DEPOSIT = "IN_WALLET_DEPOSIT", _("IN WALLET DEPOSIT")
    WALLET_AUTO_CHARGE_DEPOSIT = "WALLET_AUTO_CHARGE_DEPOSIT", _(
        "WALLET_AUTO_CHARGE_DEPOSIT"
    )
    INTEREST_DEPOSIT = "INTEREST_DEPOSIT", _(" INTEREST DEPOSIT")
    UPFRONT_INTEREST_TO_WALLET = "UPFRONT_INTEREST_TO_WALLET", _(
        "UPFRONT_INTEREST_TO_WALLET"
    )
    CARD_DEPOSIT = "CARD_DEPOSIT", _("CARD DEPOSIT")
    CARD_AUTO_CHARGE_DEPOSIT = "CARD_AUTO_CHARGE_DEPOSIT", _("CARD_AUTO_CHARGE_DEPOSIT")
    AJO_CARD_ASSIGNMENT = "AJO_CARD_ASSIGNMENT", _("AJO_CARD_ASSIGNMENT")
    WALLET_WITHDRAWAL = "WALLET_WITHDRAWAL", _("WALLET WITHDRAWAL")
    TRANSFER = "TRANSFER", _("TRANSFER")
    REVERSAL = "REVERSAL", _("REVERSAL")
    CARD_WITHRAWAL_CHARGE = "CARD_WITHRAWAL_CHARGE", _("CARD_WITHRAWAL_CHARGE")
    CARD_WITHRAWAL = "CARD_WITHRAWAL", _("CARD_WITHRAWAL")
    REVERSAL_EXTERNAL_TRANSFER = "REVERSAL_EXTERNAL_TRANSFER", _(
        "REVERSAL_EXTERNAL_TRANSFER"
    )
    COMMISSION = "COMMISSION", _("COMMISSION")
    CASHOUT = "CASHOUT", _("CASHOUT")
    PREFUNDING_DEPOSIT = "PREFUNDING_DEPOSIT", _("PREFUNDING_DEPOSIT")
    PREFUNDING_REPAYMENT = "PREFUNDING_REPAYMENT", _("PREFUNDING_REPAYMENT")
    SERVICE_CHARGE = "SERVICE_CHARGE", _("SERVICE_CHARGE")
    CARD_REQUEST = "CARD_REQUEST", _("CARD_REQUEST")
    FUND_FROM_LIBERTY_PAY = "FUND_FROM_LIBERTY_PAY", _("FUND_FROM_LIBERTY_PAY")
    AGENT_ROSCA_DEDUCTION = "AGENT_ROSCA_DEDUCTION", _("AGENT_ROSCA_DEDUCTION")
    DOJAH_SERVICE_CHARGE = "DOJAH_SERVICE_CHARGE", _("DOJAH_SERVICE_CHARGE")
    TRANSFER_TO_EXTERNAL_ACCOUNT = "TRANSFER_TO_EXTERNAL_ACCOUNT", _(
        "TRANSFER_TO_EXTERNAL_ACCOUNT"
    )
    AGENT_ROSCA_PLATFORM_FEE = "AGENT_ROSCA_PLATFORM_FEE", _("AGENT_ROSCA_PLATFORM_FEE")
    AJO_LOAN_CHARGE_FEE = "AJO_LOAN_CHARGE_FEE", _("AJO_LOAN_CHARGE_FEE")
    PROSPER_LOAN_DEPOSIT = "PROSPER_LOAN_DEPOSIT", _("PROSPER_LOAN_DEPOSIT")
    LIB_PROSPER_LOAN_DEPOSIT = "LIB_PROSPER_LOAN_DEPOSIT", _("LIB_PROSPER_LOAN_DEPOSIT")
    SAVING_MOVE_TO_LOAN = "SAVING_MOVE_TO_LOAN", _("SAVING_MOVE_TO_LOAN")
    BNPL_LOAN_DISBURSEMENT = "BNPL_LOAN_DISBURSEMENT", _("BNPL_LOAN_DISBURSEMENT")
    BNPL_SETTLEMENT_DEBIT = "BNPL_SETTLEMENT_DEBIT", _("BNPL_SETTLEMENT_DEBIT")
    AJO_LOAN_DISBURSEMENT = "LOAN_DISBURSEMENT", _("LOAN_DISBURSEMENT")
    AJO_LOAN_ESCROW_HOLDING = "AJO_LOAN_ESCROW_HOLDING", _("AJO_LOAN_ESCROW_HOLDING")
    AJO_LOAN_ESCROW_HOLDING_REFUND = "AJO_LOAN_ESCROW_HOLDING_REFUND", _(
        "AJO_LOAN_ESCROW_HOLDING_REFUND"
    )
    ESCROW_HOLDING_OFFSET_LOAN_REPAYMENT = "ESCROW_HOLDING_OFFSET_LOAN_REPAYMENT", _(
        "ESCROW_HOLDING_OFFSET_LOAN_REPAYMENT"
    )
    AJO_LOAN_REPAYMENT_TOPUP_DEPOSIT = "AJO_LOAN_REPAYMENT_TOPUP_DEPOSIT", _(
        "AJO_LOAN_REPAYMENT_TOPUP_DEPOSIT"
    )
    AJO_LOAN_REPAYMENT_TOPUP = "AJO_LOAN_REPAYMENT_TOPUP", _("AJO_LOAN_REPAYMENT_TOPUP")
    LOAN_REPAYMENT = "LOAN_REPAYMENT", _("LOAN_REPAYMENT")
    CHARGE_SPEND_ON_CLOSED_SAVINGS_ELIGIBILITY = (
        "CHARGE_SPEND_ON_CLOSED_SAVINGS_ELIGIBILITY",
        _("CHARGE_SPEND_ON_CLOSED_SAVINGS_ELIGIBILITY"),
    )
    MANUAL_CLEANUP_WALLET_DEDUCTION = "MANUAL_CLEANUP_WALLET_DEDUCTION", _(
        "MANUAL_CLEANUP_WALLET_DEDUCTION"
    )
    MANUAL_CLEANUP_WALLET_TOPUP = "MANUAL_CLEANUP_WALLET_TOPUP", _(
        "MANUAL_CLEANUP_WALLET_TOPUP"
    )
    ROLLOVER = "ROLLOVER", _("ROLLOVER")
    DISCREPANCY_DEBIT = (
        "DISCREPANCY_DEBIT",
        _("DISCREPANCY_DEBIT"),
    )
    CREDIT_BUREAU_CHECK = "CREDIT_BUREAU_CHECK", _("CREDIT_BUREAU_CHECK")
    CHARGE_ESCROW_WALLET = "CHARGE_ESCROW_WALLET", _("CHARGE_ESCROW_WALLET")
    CHARGE_DISBURSEMENT_WALLET = "CHARGE_DISBURSEMENT_WALLET", _(
        "CHARGE_DISBURSEMENT_WALLET"
    )
    REFUND_SPEND_DOUBLE_LOAN_CHARGE = "REFUND_SPEND_DOUBLE_LOAN_CHARGE", _(
        "REFUND_SPEND_DOUBLE_LOAN_CHARGE"
    )
    DISBURSEMENT_TO_SPEND = "DISBURSEMENT_TO_SPEND", _("DISBURSEMENT_TO_SPEND")
    DISBURSE_TO_EXTERNAL_ACCOUNT = (
        "DISBURSE_TO_EXTERNAL_ACCOUNT",
        "DISBURSE_TO_EXTERNAL_ACCOUNT",
    )
    REFUND_SPEND_LOAN_DECLINE = "REFUND_SPEND_LOAN_DECLINE", _(
        "REFUND_SPEND_LOAN_DECLINE"
    )
    REFUND_REPAYMENT = "REFUND_REPAYMENT", _("REFUND_REPAYMENT")
    SAVINGS_TO_LOANS_SWEEP = "SAVINGS_TO_LOANS_SWEEP", _("SAVINGS_TO_LOANS_SWEEP")
    ESCROW_TO_ESCROW_ACCOUNT = "ESCROW_TO_ESCROW_ACCOUNT", _("ESCROW_TO_ESCROW_ACCOUNT")
    COMMISSION_TO_COMMISION_ACCOUNT = "COMMISSION_TO_COMMISION_ACCOUNT", _(
        "COMMISSION_TO_COMMISION_ACCOUNT"
    )
    BNPL_TO_BNPL_ACCOUNT = "BNPL_TO_BNPL_ACCOUNT", _("BNPL_TO_BNPL_ACCOUNT")
    REPAYMENT_TO_REPAYMENT_ACCOUNT = "REPAYMENT_TO_REPAYMENT_ACCOUNT", _(
        "REPAYMENT_TO_REPAYMENT_ACCOUNT"
    )
    WALLET_DEDUCTION_TO_LOAN = "WALLET_DEDUCTION_TO_LOAN", _("WALLET_DEDUCTION_TO_LOAN")
    OUSTANDING_ESCROW_SETTLEMENT = "OUSTANDING_ESCROW_SETTLEMENT", _(
        "OUSTANDING_ESCROW_SETTLEMENT"
    )
    REFUND_LOAN_FEE = "REFUND_LOAN_FEE", _("REFUND_LOAN_FEE")
    BNPL_INITIAL_DEPOSIT = "BNPL_INITIAL_DEPOSIT", _("BNPL_INITIAL_DEPOSIT")
    RETURN_ESCROW_OVERAGE = "RETURN_ESCROW_OVERAGE", _("RETURN_ESCROW_OVERAGE")
    INTERNAL_TRANSFER_TO_LOANS = "INTERNAL_TRANSFER_TO_LOANS", _(
        "INTERNAL_TRANSFER_TO_LOANS"
    )
    FUND_MONNIFY_ACCOUNT = "FUND_MONNIFY_ACCOUNT", _("FUND_MONNIFY_ACCOUNT")
    HEALTH_INSURANCE = "HEALTH_INSURANCE", _("HEALTH_INSURANCE")
    SAVINGS_HEALTH_INSURANCE = "SAVINGS_HEALTH_INSURANCE", _("SAVINGS_HEALTH_INSURANCE")
    SAVINGS_HEALTH_INSURANCE_ACTIVATION = "SAVINGS_HEALTH_INSURANCE_ACTIVATION", _(
        "SAVINGS_HEALTH_INSURANCE_ACTIVATION"
    )
    LOAN_AMOUNT_CHECKER_FEE = "LOAN_AMOUNT_CHECKER_FEE", _("LOAN_AMOUNT_CHECKER_FEE")
    HEALTH_PLAN_DEDUCTION = "HEALTH_PLAN_DEDUCTION", _("HEALTH_PLAN_DEDUCTION")
    LOAN_RECOVERY = "LOAN_RECOVERY", _("LOAN_RECOVERY")


class TransactionTypeCreditOrDebitChoices(models.TextChoices):
    CREDIT = "CREDIT", _("CREDIT")
    DEBIT = "DEBIT", _("DEBIT")


class WalletTypes(models.TextChoices):
    CHESTLOCK = "CHESTLOCK", _("CHESTLOCK")
    QUICKSAVINGS = "QUICKSAVINGS", _("QUICKSAVINGS")
    HALAL = "HALAL", _("HALAL")
    AJO_AGENT = "AJO_AGENT", _("AJO_AGENT")
    AJO_USER = "AJO_USER", _("AJO_USER")
    AJO_SPENDING = "AJO_SPENDING", _("AJO_SPENDING")
    AJO_DIGITAL = "AJO_DIGITAL", _("AJO_DIGITAL")
    AJO_LOAN = "AJO_LOAN", _("AJO_LOAN")
    AJO_COMMISSION = "AJO_COMMISSION", _("AJO_COMMISSION")
    AJO_PREFUNDING = "AJO_PREFUNDING", _("AJO_PREFUNDING")
    ROTATIONGROUP = "ROTATIONGROUP", _("ROTATIONGROUP")
    AJO_PERSONAL = "AJO_PERSONAL", _("AJO_PERSONAL")
    ROSCA_PERSONAL = "ROSCA_PERSONAL", _("ROSCA_PERSONAL")
    ROSCA_AJO_USER = "ROSCA_AJO_USER", _("ROSCA_AJO_USER")
    ROSCA_GROUP_ESCROW = "ROSCA_GROUP_ESCROW", _("ROSCA_GROUP_ESCROW")
    PROSPER_LOAN_WALLET = "PROSPER_LOAN_WALLET", _("PROSPER_LOAN_WALLET")
    AJO_LOAN_ESCROW = "AJO_LOAN_ESCROW", _("AJO_LOAN_ESCROW")
    AJO_LOAN_REPAYMENT = "AJO_LOAN_REPAYMENT", _("AJO_LOAN_REPAYMENT")
    LOAN_DISBURSEMENT = "LOAN_DISBURSEMENT", _("LOAN_DISBURSEMENT")
    ONLENDING = "ONLENDING", _("ONLENDING")
    ONLENDING_MAIN = "ONLENDING_MAIN", _("ONLENDING_MAIN")
    ONLENDING_COMMISSION = "ONLENDING_COMMISSION", _("ONLENDING_COMMISSION")
    BNPL = "BNPL", _("BNPL")
    BNPL_COMMISSION = "BNPL_COMMISSION", _("BNPL_COMMISSION")
    BOOSTA_COMMISSION = "BOOSTA_COMMISSION", _("BOOSTA_COMMISSION")
    BOOSTA_2x_COMMISSION = "BOOSTA_2x_COMMISSION", _("BOOSTA_2x_COMMISSION")
    CARD_SETTLEMENT = "CARD_SETTLEMENT", _("CARD_SETTLEMENT")
    CHESTLOCK_INTEREST = "CHESTLOCK_INTEREST", _("CHESTLOCK_INTEREST")
    ONLENDING_INTEREST = "ONLENDING_INTEREST", _("ONLENDING_INTEREST")
    LOAN_RECOVERY = "LOAN_RECOVERY", _("LOAN_RECOVERY")


class TransactionSource(models.TextChoices):
    IN_WALLET = "IN_WALLET", _("IN WALLET")
    WALLET = "WALLET", _("WALLET")
    DEBIT_CARD = "DEBIT_CARD", _("DEBIT CARD")
    INTEREST = "INTEREST", _("INTEREST")
    COMMISSION = "COMMISSION", _("COMMISSION")
    BANK_ACCOUNT = "BANK_ACCOUNT", _("BANK_ACCOUNT")
    PREFUNDING = "PREFUNDING", _("PREFUNDING")
    LIBERTY_PAY = "LIBERTY_PAY", _("LIBERTY_PAY")
    COREBANKING = "COREBANKING", _("COREBANKING")
    MONNIFY = "MONNIFY", _("MONNIFY")


class TransactionDestination(models.TextChoices):
    WALLET = "WALLET", _("WALLET")
    BANK_ACCOUNT = "BANK_ACCOUNT", _("BANK ACCOUNT")
    CASH = "CASH", _("CASH")
    PLAN = "PLAN", _("PLAN")


class PlanType(models.TextChoices):
    CHESTLOCK = "CHESTLOCK", _("CHESTLOCK")
    HALAL = "HALAL", _("HALAL")
    QUICKSAVINGS = "QUICKSAVINGS", _("QUICKSAVINGS")
    ONLENDING = "ONLENDING", _("ONLENDING")
    AJO = "AJO", _("AJO")
    ROTATIONGROUP = "ROTATIONGROUP", _("ROTATIONGROUP")
    LOAN = "LOAN", _("LOAN")


class PaymentMethod(models.TextChoices):
    WALLET = "WALLET", _("WALLET")
    DEBIT_CARD = "DEBIT_CARD", _("DEBIT CARD")
    CONNECTED_BANK_ACCOUNT = "CONNECTED_BANK_ACCOUNT", _("CONNECTED BANK ACCOUNT")


class RecurrentSavingStatus(models.TextChoices):
    ACTIVE = "ACTIVE", _("ACTIVE")
    PAUSED = "PAUSED", _("PAUSED")
    NONE = "NONE", _("NONE")


class CommissionType(models.TextChoices):
    COMPANY = "COMPANY", _("COMPANY")
    AGENT = "AGENT", _("AGENT")
    ADMIN = "ADMIN", _("ADMIN")


class WalletBalanceType(models.TextChoices):
    AVAILABLE = "AVAILABLE", _("AVAILABLE")
    COMMISSION = "COMMISSION", _("COMMISSION")
    HOLD = "HOLD", _("HOLD")


class DisbursementProviderType(models.TextChoices):
    AGENCY_BANKING_PROVIDER = "AGENCY_BANKING_PROVIDER", _("AGENCY_BANKING_PROVIDER")
    MONNIFY_PROVIDER = "MONNIFY_PROVIDER", _("MONNIFY_PROVIDER")
    VFD_PROVIDER = "VFD_PROVIDER", _("VFD_PROVIDER")
    WEMA_PROVIDER = "WEMA_PROVIDER", _("WEMA_PROVIDER")
    PAYSTACK_PROVIDER = "PAYSTACK_PROVIDER", _("PAYSTACK_PROVIDER")
    CASH_CONNECT = "CASH_CONNECT", _("CASH_CONNECT")


class TransferRouteTypes(models.TextChoices):
    TRANSFER_FROM_MONNIFY_TO_ACCOUNT = "TRANSFER_FROM_MONNIFY_TO_ACCOUNT", _(
        "TRANSFER_FROM_MONNIFY_TO_ACCOUNT"
    )
    TRANSFER_FROM_REPAYMENT_TO_LOANS = "TRANSFER_FROM_REPAYMENT_TO_LOANS", _(
        "TRANSFER_FROM_REPAYMENT_TO_LOANS"
    )
