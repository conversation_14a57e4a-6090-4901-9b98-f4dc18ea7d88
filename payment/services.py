from typing import Any, Dict, Optional

from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.db import IntegrityError
from django.db.models import F
from django.utils import timezone
from django.utils.timezone import datetime

from accounts.models import ConstantTable
from ajo.models import AjoUser, RotationGroup, RotationGroupMember

from .model_choices import (
    CommissionType,
    DisbursementProviderType,
    PlanType,
    Status,
    TransactionDestination,
    TransactionFormType,
    TransactionSource,
    TransactionTypeCreditOrDebitChoices,
    WalletTypes,
)
from .models import Commission, Transaction, WithdrawalAccount


class TransactionService:
    def __init__(self, transaction_instance: Transaction) -> None:
        self.transaction = transaction_instance

    def set_failed_transaction(self, failure_reason: str, payload: dict) -> Transaction:
        """
        Make the transaction a failed transaction

        Args:
            failure_reason (str): the reason the transaction is failing
            payload (dict): the response returned to the user

        Returns:
            Transaction: the updated Transaction object
        """
        # set the new fields
        self.transaction.failure_reason = failure_reason
        self.transaction.status = Status.FAILED
        self.transaction.payload = payload
        # save the instance
        self.transaction.save()

        return self.transaction

    def set_successful_transaction(
        self,
        payload: Dict[str, Any] | None = None,
        unique_reference: str | None = None,
        transaction_date_completed: datetime = timezone.localtime(),
        masked_pan: str | None = None,
    ) -> Transaction:
        """
        Sets the transaction as a successful one.

        Args:
            payload (Dict[str, Any] | None, optional): the response made from the call. Defaults to None.
            unique_reference (str | None, optional): the unique reference from the call. Defaults to None.
            transaction_date_completed (datetime, optional): the time this transaction was completed. Defaults to timezone.localtime().

        Returns:
            Transaction: the completed Transaction
        """
        self.transaction.status = Status.SUCCESS
        self.transaction.transaction_date_completed = transaction_date_completed
        self.transaction.payload = payload
        self.transaction.masked_pan_of_card = masked_pan

        if unique_reference and not self.transaction.unique_reference:
            self.transaction.unique_reference = unique_reference

        self.transaction.save()

        return self.transaction

    @staticmethod
    def create_deposit_by_wallet_transaction(
        user,
        amount,
        wallet_type: WalletTypes,
        quotation_id,
        plan_type: PlanType | None = None,
        ajo_user: AjoUser | None = None,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.CREDIT,
        transaction_form_type: TransactionFormType = TransactionFormType.WALLET_DEPOSIT,
        status: Status = Status.PENDING,
        transaction_source: TransactionSource = TransactionSource.WALLET,
        description: str | None = None,
        unique_reference: Optional[str] = None,
        # transfer_provider: Optional[str] = None,
        transfer_provider=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
    ) -> Transaction:
        """
        Creates a Deposit Transaction By Wallet Instance

        Args:
            quotation_id (str): the quotation_id of the savings type eg chestlock -> LI-CHK-{{string_of_length_12}}

        Returns:
            model instance: A transaction instance created in the database
        """
        transaction = Transaction.objects.create(
            user=user,
            transaction_form_type=transaction_form_type,
            status=status,
            transaction_source=transaction_source,
            amount=amount,
            transaction_type=transaction_type,
            wallet_type=wallet_type,
            plan_type=plan_type,
            quotation_id=quotation_id,
            onboarded_user=ajo_user,
            description=description,
            unique_reference=unique_reference,
            transfer_provider=transfer_provider,
        )

        return transaction

    @staticmethod
    def create_interest_transaction(
        user,
        amount: float,
        wallet_type: WalletTypes,
        quotation_id: str,
        plan_type: PlanType,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.CREDIT,
        transaction_form_type: TransactionFormType = TransactionFormType.INTEREST_DEPOSIT,
        status: Status = Status.PENDING,
        transaction_source: TransactionSource = TransactionSource.INTEREST,
    ):
        """
        Creates a Deposit Transaction By Wallet Instance

        Args:
            quotation_id (str): the quotation_id of the savings type eg chestlock -> LI-CHK-{{string_of_length_12}}

        Returns:
            model instance: A transaction instance created in the database
        """
        transaction = Transaction.objects.create(
            user=user,
            transaction_form_type=transaction_form_type,
            status=status,
            transaction_source=transaction_source,
            amount=amount,
            transaction_type=transaction_type,
            wallet_type=wallet_type,
            plan_type=plan_type,
            quotation_id=quotation_id,
        )

        return transaction

    @staticmethod
    def create_deposit_by_card_transaction(
        user,
        amount: float,
        ###card information###
        # card_instance: Card,
        # transaction_date,
        # reference,
        ######################
        wallet_type: WalletTypes,
        quotation_id: str,
        plan_type: PlanType,
        unique_reference: str,
        description: str | None = None,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.CREDIT,
        transaction_form_type: TransactionFormType = TransactionFormType.CARD_DEPOSIT,
        status: Status = Status.PENDING,
        transaction_source: TransactionSource = TransactionSource.DEBIT_CARD,
    ):
        """
        Creates a Deposit Transaction By Card Instance

        Args:
            quotation_id (str): the quotation_id of the savings type eg chestlock -> LI-CHK-{{string_of_length_12}},

        Returns:
            str: A transaction instance created in the database
        """
        transaction = Transaction.objects.create(
            user=user,
            transaction_form_type=transaction_form_type,
            status=status,
            transaction_source=transaction_source,
            amount=amount,
            transaction_type=transaction_type,
            quotation_id=quotation_id,
            wallet_type=wallet_type,
            plan_type=plan_type,
            unique_reference=unique_reference,
            description=description,
            # # card information##
            # card_instance=card_instance,
            # transaction_date=transaction_date,
            # reference=reference,
        )

        return transaction

    @staticmethod
    def create_withdrawal_to_wallet_transaction(
        user,
        amount: float,
        wallet_type: WalletTypes,
        request_data: dict,
        quotation_id: str,
        plan_type: PlanType,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.DEBIT,
        transaction_form_type: TransactionFormType = TransactionFormType.WALLET_WITHDRAWAL,
        status: Status = Status.PENDING,
        transaction_destination: TransactionDestination = TransactionDestination.WALLET,
        description: Optional[str] = None,
        ajo_user: Optional[str] = None,
    ):
        """
        Create a Withdrawal Transaction By Wallet Instance

        Args:
            user (User): User Instance.
            amount (float): How much to be withdrawn.
            wallet_type (WalletTypes): what type of wallet to be withdrawn from
            quotation_id (str): quotation id of the plan
            plan_type (PlanType): the plan type, instance of PlanType
            transaction_type (TransactionTypeCreditOrDebitChoices, optional): what transaction type is taking place. Defaults to TransactionTypeCreditOrDebitChoices.DEBIT.
            transaction_form_type (TransactionFormType, optional): what form of transaction is taking place. Defaults to TransactionFormType.WALLET_WITHDRAWAL.
            status (Status, optional): status of the transaction. Defaults to Status.PENDING.
            transaction_destination (TransactionDestination, optional): where is the money going to. Defaults to TransactionDestination.WALLET.
            request_data(dict): the request made to the server through the withdraw endpoint.

        Returns:
            Transaction: the transaction object that was created.
        """
        transaction = Transaction.objects.create(
            user=user,
            transaction_form_type=transaction_form_type,
            status=status,
            transaction_destination=transaction_destination,
            amount=amount,
            transaction_type=transaction_type,
            wallet_type=wallet_type,
            plan_type=plan_type,
            quotation_id=quotation_id,
            request_data=request_data,
            description=description,
            onboarded_user=ajo_user,
        )

        return transaction

    @staticmethod
    def create_reversal_of_funds_transaction(
        user,
        amount: float,
        wallet_type: WalletTypes,
        request_data: dict,
        quotation_id: str,
        plan_type: PlanType,
        description: str | None = None,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.CREDIT,
        transaction_form_type: TransactionFormType = TransactionFormType.REVERSAL,
        status: Status = Status.SUCCESS,
        transaction_destination: TransactionSource = TransactionSource.WALLET,
        ajo_user: AjoUser | None = None,
        unique_reference: str | None = None,
        transaction_source_id: str | None = None,
        transfer_provider: str | None = None,
    ):
        """
        Create a Withdrawal Transaction By Wallet Instance

        Args:
            user (User): User Instance.
            amount (float): How much to be withdrawn.
            wallet_type (WalletTypes): what type of wallet to be withdrawn from
            quotation_id (str): quotation id of the plan
            plan_type (PlanType): the plan type, instance of PlanType
            transaction_type (TransactionTypeCreditOrDebitChoices, optional): what transaction type is taking place. Defaults to TransactionTypeCreditOrDebitChoices.DEBIT.
            transaction_form_type (TransactionFormType, optional): what form of transaction is taking place. Defaults to TransactionFormType.WALLET_WITHDRAWAL.
            status (Status, optional): status of the transaction. Defaults to Status.PENDING.
            transaction_destination (TransactionDestination, optional): where is the money going to. Defaults to TransactionDestination.WALLET.

        Returns:
            Transaction: the transaction object that was created
        """
        transaction = Transaction.objects.create(
            user=user,
            transaction_form_type=transaction_form_type,
            status=status,
            transaction_destination=transaction_destination,
            amount=amount,
            transaction_type=transaction_type,
            wallet_type=wallet_type,
            plan_type=plan_type,
            description=description,
            quotation_id=quotation_id,
            request_data=request_data,
            onboarded_user=ajo_user,
            unique_reference=unique_reference,
            transaction_source_id=transaction_source_id,
            transfer_provider=transfer_provider,
        )

        return transaction

    @staticmethod
    def create_rotation_group_deposit_transaction(
        user,
        amount: float,
        rotation_group_id: str,
        transaction_form_type: TransactionFormType,
        wallet_type: WalletTypes = WalletTypes.ROTATIONGROUP,
        plan_type: PlanType = PlanType.ROTATIONGROUP,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.CREDIT,
        status: Status = Status.PENDING,
        transaction_source: TransactionSource = TransactionSource.WALLET,
    ):
        transaction = Transaction.objects.create(
            user=user,
            amount=amount,
            wallet_type=wallet_type,
            rotation_group_id=rotation_group_id,
            transaction_form_type=transaction_form_type,
            transaction_source=transaction_source,
            plan_type=plan_type,
            transaction_type=transaction_type,
            status=status,
        )
        return transaction

    @staticmethod
    def create_upfront_interest_to_wallet_transaction(
        user: AbstractUser,
        amount: float,
        quotation_id: str,
        plan_type: PlanType,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.CREDIT,
        transaction_form_type: TransactionFormType = TransactionFormType.UPFRONT_INTEREST_TO_WALLET,
        status: Status = Status.PENDING,
        transaction_destination: TransactionDestination = TransactionDestination.WALLET,
        ajo_user: Optional[AjoUser] = None,
        description: Optional[str] = None,
        wallet_type: Optional[WalletTypes] = None,
    ) -> "Transaction":
        """
        Create an upfront interest to the user's Agency wallet instance or the any other wallet

        Args:
            user (User): the user instance
            amount (float): how much to be paid
            quotation_id (str): the quotation id of the plan
            plan_type (PlanType): the plan type, instance of PlanType
            transaction_type (TransactionTypeCreditOrDebitChoices, optional): _description_. Defaults to TransactionTypeCreditOrDebitChoices.CREDIT.
            transaction_form_type (TransactionFormType, optional): _description_. Defaults to TransactionFormType.UPFRONT_INTEREST_TO_WALLET.
            status (Status, optional): _description_. Defaults to Status.PENDING.
            transaction_destination (TransactionDestination, optional): _description_. Defaults to TransactionDestination.WALLET.
            ajo_user (AjoUser, optional): the AjoUser instance. Defaults to None.
            description (str, optional): the transaction description. Defaults to None.
            wallet_type (WalletTypes, optional): the wallet type. Defaults to None.

        Returns:
            Transaction: the transaction instance that has been created
        """
        transaction = Transaction.objects.create(
            user=user,
            amount=amount,
            quotation_id=quotation_id,
            plan_type=plan_type,
            transaction_type=transaction_type,
            transaction_form_type=transaction_form_type,
            status=status,
            transaction_destination=transaction_destination,
            onboarded_user=ajo_user,
            description=description,
            wallet_type=wallet_type,
        )

        return transaction

    @staticmethod
    def create_transfer_to_ajo_plan_deposit_transaction(
        user: AbstractUser,
        amount: float,
        wallet_type: WalletTypes,
        plan_type: PlanType,
        transaction_description: str,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.DEBIT,
        transaction_form_type: TransactionFormType = TransactionFormType.TRANSFER,
        status: Status = Status.PENDING,
        transaction_source: TransactionSource = TransactionSource.WALLET,
        transaction_destination: TransactionDestination = TransactionDestination.WALLET,
        ajo_user: AjoUser | None = None,
    ):
        """_summary_

        Args:
            user (AbstractUser): the user.
            amount (float): the amount involved
            wallet_type (WalletTypes): the wallet type.
            plan_type (PlanType): the plan type
            transaction_description (str): the transaction description
            transaction_type (TransactionTypeCreditOrDebitChoices, optional): the type of transaction. Defaults to TransactionTypeCreditOrDebitChoices.DEBIT.
            transaction_form_type (TransactionFormType, optional): the form type of the transaction. Defaults to TransactionFormType.TRANSFER.
            status (Status, optional): the status of the transaction upon creation. Defaults to Status.PENDING.
            transaction_source (TransactionSource, optional): the source of the transaction. Defaults to TransactionSource.WALLET.
            transaction_destination (TransactionDestination, optional): the destination of the transaction. Defaults to TransactionDestination.WALLET.
            ajo_user (AjoUser | None, optional): the ajo user involved in this transaction. Defaults to None.

        Returns:
            _type_: _description_
        """
        transaction = Transaction.objects.create(
            user=user,
            transaction_form_type=transaction_form_type,
            status=status,
            transaction_source=transaction_source,
            amount=amount,
            description=transaction_description,
            transaction_type=transaction_type,
            wallet_type=wallet_type,
            plan_type=plan_type,
            transaction_destination=transaction_destination,
            onboarded_user=ajo_user,
        )

        return transaction

    @staticmethod
    def create_transfer_to_from_wallet_transaction(
        user: AbstractUser,
        amount: float,
        wallet_type: WalletTypes,
        transaction_description: str,
        transaction_type: TransactionTypeCreditOrDebitChoices,
        transaction_form_type: TransactionFormType = TransactionFormType.TRANSFER,
        status: Status = Status.SUCCESS,
        plan_type: PlanType | None = None,
        transaction_source: TransactionSource = TransactionSource.WALLET,
        transaction_destination: TransactionDestination = TransactionDestination.WALLET,
        ajo_user: AjoUser | None = None,
        # transfer_provider: DisbursementProviderType | None = None
        transfer_provider=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
    ) -> Transaction:
        """ "
        Create a transaction for a wallet transfer either debit or credit

        Args:
            user (AbstractUser): the user.
            amount (float): the amount involved
            wallet_type (WalletTypes): the wallet type.
            plan_type (PlanType, optional): the plan type. Defaults to None.
            transaction_description (str): the transaction description
            transaction_type (TransactionTypeCreditOrDebitChoices, optional): the type of transaction.
            transaction_form_type (TransactionFormType, optional): the form type of the transaction. Defaults to TransactionFormType.TRANSFER.
            status (Status, optional): the status of the transaction upon creation. Defaults to Status.PENDING.
            transaction_source (TransactionSource, optional): the source of the transaction. Defaults to TransactionSource.WALLET.
            transaction_destination (TransactionDestination, optional): the destination of the transaction. Defaults to TransactionDestination.WALLET.
            ajo_user (AjoUser | None, optional): the ajo user involved in this transaction. Defaults to None.

        Returns:
            Transaction: the created Transaction instance
        """

        transaction = Transaction.objects.create(
            user=user,
            transaction_form_type=transaction_form_type,
            status=status,
            transaction_source=transaction_source,
            amount=amount,
            description=transaction_description,
            transaction_type=transaction_type,
            wallet_type=wallet_type,
            plan_type=plan_type,
            transaction_destination=transaction_destination,
            onboarded_user=ajo_user,
            transfer_provider=transfer_provider,
        )

        return transaction

    @staticmethod
    def create_ajo_commissions_transaction(
        user: AbstractUser,
        amount: float,
        quotation_id: str,
        transaction_description: str,
        ajo_user: AjoUser | None = None,
        transaction_form_type: TransactionFormType = TransactionFormType.COMMISSION,
        wallet_type: WalletTypes = WalletTypes.AJO_USER,
        plan_type: PlanType = PlanType.AJO,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.DEBIT,
        status: Status = Status.SUCCESS,
        transaction_source: TransactionSource = TransactionSource.WALLET,
    ) -> Transaction:
        """
        Creates a Commission Transaction Removed from an Ajo User's Saving's Wallet

        Args:
            user (AbstractUser): the user that the ajo user is under
            amount (float): how much is taken as commissions
            quotation_id (str): the quotation id of the ajo plan
            ajo_user (AjoUser): the ajo user
            transaction_description(str): the transaction description
            transaction_form_type (TransactionFormType, optional): The form of the transaction. Defaults to TransactionFormType.COMMISSION.
            wallet_type (WalletTypes, optional): the type of wallet that is concerned to this transaction. Defaults to WalletTypes.AJO_USER.
            plan_type (PlanType, optional): the plan type associated to this. Defaults to PlanType.AJO.
            transaction_type (TransactionTypeCreditOrDebitChoices, optional): The Type of the transaction. Defaults to TransactionTypeCreditOrDebitChoices.DEBIT.
            status (Status, optional): Status of the transaction, always successful because it is created when commission can be taken. Defaults to Status.SUCCESS.
            transaction_source (TransactionSource, optional): the place where this transaction is happening from. Defaults to TransactionSource.WALLET.

        Returns:
            Transaction: the created Transaction instance
        """
        transaction = Transaction.objects.create(
            user=user,
            amount=amount,
            onboarded_user=ajo_user,
            wallet_type=wallet_type,
            quotation_id=quotation_id,
            description=transaction_description,
            transaction_form_type=transaction_form_type,
            transaction_source=transaction_source,
            plan_type=plan_type,
            transaction_type=transaction_type,
            status=status,
            transfer_provider=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
        )
        return transaction

    @staticmethod
    def create_agent_commissions_increment_transaction(
        user: AbstractUser,
        amount: float,
        quotation_id: str | None,
        transaction_description: str,
        transaction_form_type: TransactionFormType = TransactionFormType.COMMISSION,
        wallet_type: WalletTypes = WalletTypes.AJO_COMMISSION,
        plan_type: PlanType = PlanType.AJO,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.CREDIT,
        status: Status = Status.SUCCESS,
        transaction_source: TransactionSource = TransactionSource.WALLET,
    ) -> Transaction:
        """
        Creates a transaction that indicates that the commission_balance of the agent's wallet has been incremented

        Args:
            user (AbstractUser): the user that the ajo user is under
            amount (float): how much is taken as commissions
            quotation_id (str): the quotation id of the ajo plan
            transaction_description(str): the transaction description
            transaction_form_type (TransactionFormType, optional): The form of the transaction. Defaults to TransactionFormType.COMMISSION.
            wallet_type (WalletTypes, optional): the type of wallet that is concerned to this transaction. Defaults to WalletTypes.AJO_USER.
            plan_type (PlanType, optional): the plan type associated to this. Defaults to PlanType.AJO.
            transaction_type (TransactionTypeCreditOrDebitChoices, optional): The Type of the transaction. Defaults to TransactionTypeCreditOrDebitChoices.DEBIT.
            status (Status, optional): Status of the transaction, always successful because it is created when commission can be taken. Defaults to Status.SUCCESS.
            transaction_source (TransactionSource, optional): the place where this transaction is happening from. Defaults to TransactionSource.WALLET.

        Returns:
            Transaction: the created Transaction instance
        """
        transaction = Transaction.objects.create(
            user=user,
            amount=amount,
            wallet_type=wallet_type,
            quotation_id=quotation_id,
            description=transaction_description,
            transaction_form_type=transaction_form_type,
            transaction_source=transaction_source,
            plan_type=plan_type,
            transaction_type=transaction_type,
            status=status,
            transfer_provider=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
        )
        return transaction

    @staticmethod
    def create_ajo_user_withdrawal_transaction(
        user: AbstractUser,
        amount: float,
        ajo_user: AjoUser,
        transaction_description: str,
        transaction_form_type: TransactionFormType = TransactionFormType.WITHDRAWAL,
        wallet_type: WalletTypes = WalletTypes.AJO_USER,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.DEBIT,
        status: Status = Status.SUCCESS,
        transaction_source: TransactionSource = TransactionSource.WALLET,
        unique_reference: str = None,
    ) -> Transaction:
        """
        Creates a Withdrawal Transaction initiated by an Ajo User's Wallet

        Returns:
            Transaction: the created Transaction instance
        """
        transaction = Transaction.objects.create(
            user=user,
            amount=amount,
            onboarded_user=ajo_user,
            wallet_type=wallet_type,
            description=transaction_description,
            transaction_form_type=transaction_form_type,
            transaction_source=transaction_source,
            transaction_type=transaction_type,
            status=status,
            unique_reference=unique_reference,
        )
        return transaction

    @staticmethod
    def create_commissions_to_agent_wallet_transaction(
        user: AbstractUser,
        amount: float,
        transaction_description: str,
        transaction_form_type: TransactionFormType = TransactionFormType.WALLET_DEPOSIT,
        wallet_type: WalletTypes = WalletTypes.AJO_AGENT,
        plan_type: PlanType = PlanType.AJO,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.CREDIT,
        status: Status = Status.PENDING,
        transaction_source: TransactionSource = TransactionSource.COMMISSION,
    ) -> Transaction:
        """
        Create a Commissions to Agent's Wallet Transaction instance

        Args:
            user (AbstractUser): the user associated with this transaction
            amount (float): the amount being transferred
            transaction_form_type (TransactionFormType, optional): the form type of transaction. Defaults to TransactionFormType.WALLET_DEPOSIT.
            wallet_type (WalletTypes, optional): the wallet type. Defaults to WalletTypes.AJO_AGENT.
            plan_type (PlanType, optional): the plan type. Defaults to PlanType.AJO.
            transaction_type (TransactionTypeCreditOrDebitChoices, optional): the transaction type. Defaults to TransactionTypeCreditOrDebitChoices.CREDIT.
            status (Status, optional): the status of the transaction. Defaults to Status.PENDING.
            transaction_source (TransactionSource, optional): the source of the transaction. Defaults to TransactionSource.COMMISSION.

        Returns:
            Transaction: the created Transaction instance
        """
        transaction = Transaction.objects.create(
            user=user,
            amount=amount,
            description=transaction_description,
            wallet_type=wallet_type,
            transaction_form_type=transaction_form_type,
            transaction_source=transaction_source,
            plan_type=plan_type,
            transaction_type=transaction_type,
            status=status,
            transfer_provider=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
        )

        return transaction

    @staticmethod
    def create_debit_commission_balance_from_agent_wallet_transaction(
        user: AbstractUser,
        amount: float,
        transaction_description: str,
        transaction_form_type: TransactionFormType = TransactionFormType.WALLET_WITHDRAWAL,
        wallet_type: WalletTypes = WalletTypes.AJO_COMMISSION,
        plan_type: PlanType = PlanType.AJO,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.DEBIT,
        status: Status = Status.SUCCESS,
        transaction_destination: TransactionDestination = TransactionDestination.WALLET,
    ) -> Transaction:
        """
        Create a debit of the commissions balance of Agent's Wallet Transaction instance

        Args:
            user (AbstractUser): the user associated with this transaction
            amount (float): the amount being transferred
            transaction_form_type (TransactionFormType, optional): the form type of transaction. Defaults to TransactionFormType.COMMISSION.
            wallet_type (WalletTypes, optional): the wallet type. Defaults to WalletTypes.AJO_AGENT.
            plan_type (PlanType, optional): the plan type. Defaults to PlanType.AJO.
            transaction_type (TransactionTypeCreditOrDebitChoices, optional): the transaction type. Defaults to TransactionTypeCreditOrDebitChoices.DEBIT.
            status (Status, optional): the status of the transaction. Defaults to Status.SUCCESS.
            transaction_source (TransactionSource, optional): the source of the transaction. Defaults to TransactionSource.COMMISSION.

        Returns:
            Transaction: the created Transaction instance
        """
        transaction = Transaction.objects.create(
            user=user,
            amount=amount,
            description=transaction_description,
            wallet_type=wallet_type,
            transaction_form_type=transaction_form_type,
            transaction_destination=transaction_destination,
            plan_type=plan_type,
            transaction_type=transaction_type,
            status=status,
            transfer_provider=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
        )

        return transaction

    @staticmethod
    def create_ajo_cashout_by_ussd(
        user: AbstractUser,
        amount: float,
        transaction_description: str,
        quotation_id: str | None = None,
        transaction_form_type: TransactionFormType = TransactionFormType.CASHOUT,
        wallet_type: WalletTypes = WalletTypes.AJO_USER,
        plan_type: PlanType = PlanType.AJO,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.DEBIT,
        status: Status = Status.SUCCESS,
        transaction_destination: TransactionDestination = TransactionDestination.CASH,
        transaction_source: TransactionSource = TransactionSource.WALLET,
        ajo_user=None,
    ) -> Transaction:
        """
        Create a cashout by USSD transaction instance

        Args:
            user (AbstractUser): the user associated with this transaction
            amount (float): the amount being transferred
            transaction_description(str): the transaction description
            transaction_form_type (TransactionFormType, optional): the form type of transaction. Defaults to TransactionFormType.CASHOUT.
            wallet_type (WalletTypes, optional): the wallet type. Defaults to WalletTypes.AJO_USER.
            plan_type (PlanType, optional): the plan type. Defaults to PlanType.AJO.
            transaction_type (TransactionTypeCreditOrDebitChoices, optional): the transaction type. Defaults to TransactionTypeCreditOrDebitChoices.DEBIT.
            status (Status, optional): the status of the transaction. Defaults to Status.SUCCESS.
            transaction_destination (TransactionDestination, optional): the destination of the transaction. Defaults to TransactionDestination.CASH.

        Returns:
            Transaction: the created Transaction instance
        """
        transaction = Transaction.objects.create(
            user=user,
            amount=amount,
            description=transaction_description,
            wallet_type=wallet_type,
            transaction_form_type=transaction_form_type,
            transaction_destination=transaction_destination,
            plan_type=plan_type,
            transaction_type=transaction_type,
            status=status,
            quotation_id=quotation_id,
            transaction_source=transaction_source,
            onboarded_user=ajo_user,
            transfer_provider=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
        )

        return transaction

    @staticmethod
    def create_loan_deposit_by_wallet_transaction(
        user: AbstractUser,
        amount: float,
        # wallet_type: WalletTypes,
        ajo_user: AjoUser,
        quotation_id: str,
        description: str,
        plan_type: PlanType = PlanType.LOAN,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.CREDIT,
        transaction_form_type: TransactionFormType = TransactionFormType.WALLET_DEPOSIT,
        status: Status = Status.SUCCESS,
        transaction_source: TransactionSource = TransactionSource.WALLET,
    ) -> Transaction:
        """
        Creates a loan deposit transaction (paying back for a loan taken)

        Args:
            user (AbstractUser): the user
            amount (float): how much was paid
            ajo_user (AjoUser): the ajo user
            quotation_id (str): the quotation id of the plan
            description(str): the transaction description
            plan_type (PlanType, optional): the plan type. Defaults to PlanType.LOAN.
            transaction_type (TransactionTypeCreditOrDebitChoices, optional): the transaction type. Defaults to TransactionTypeCreditOrDebitChoices.CREDIT.
            transaction_form_type (TransactionFormType, optional): the transaction form type. Defaults to TransactionFormType.WALLET_DEPOSIT.
            status (Status, optional): the status of the transaction. Defaults to Status.SUCCESS.
            transaction_source (TransactionSource, optional): the source of the transactuin. Defaults to TransactionSource.WALLET.

        Returns:
            Transaction: a created loan transaction
        """
        transaction = Transaction.objects.create(
            user=user,
            transaction_form_type=transaction_form_type,
            status=status,
            transaction_source=transaction_source,
            amount=amount,
            transaction_type=transaction_type,
            # wallet_type=wallet_type,
            plan_type=plan_type,
            quotation_id=quotation_id,
            description=description,
            onboarded_user=ajo_user,
            transfer_provider=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
        )

        return transaction

    @staticmethod
    def create_loan_transaction(
        user: AbstractUser,
        amount: float,
        ajo_user: AjoUser,
        quotation_id: str,
        description: str,
        wallet_type: WalletTypes = WalletTypes.AJO_LOAN,
        plan_type: PlanType = PlanType.LOAN,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.CREDIT,
        transaction_form_type: TransactionFormType = TransactionFormType.WALLET_DEPOSIT,
        status: Status = Status.SUCCESS,
        transaction_destination: TransactionDestination = TransactionDestination.WALLET,
    ) -> Transaction:
        """
        Creates a loan deposit transaction (paying back for a loan taken)

        Args:
            user (AbstractUser): the user
            amount (float): how much was paid
            ajo_user (AjoUser): the ajo user
            quotation_id (str): the quotation id of the plan
            description(str): the transaction description
            plan_type (PlanType, optional): the plan type. Defaults to PlanType.LOAN.
            transaction_type (TransactionTypeCreditOrDebitChoices, optional): the transaction type. Defaults to TransactionTypeCreditOrDebitChoices.CREDIT.
            transaction_form_type (TransactionFormType, optional): the transaction form type. Defaults to TransactionFormType.WALLET_DEPOSIT.
            status (Status, optional): the status of the transaction. Defaults to Status.SUCCESS.
            transaction_destination (TransactionDestination, optional): the source of the transactuin. Defaults to TransactionDestination.WALLET.

        Returns:
            Transaction: a created loan transaction
        """
        transaction = Transaction.objects.create(
            user=user,
            transaction_form_type=transaction_form_type,
            status=status,
            transaction_destination=transaction_destination,
            amount=amount,
            transaction_type=transaction_type,
            wallet_type=wallet_type,
            plan_type=plan_type,
            quotation_id=quotation_id,
            description=description,
            onboarded_user=ajo_user,
            transfer_provider=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
        )

        return transaction

    @staticmethod
    def create_cashout_to_agent_wallet_transaction(
        user: AbstractUser,
        amount: float,
        transaction_description: str,
        transaction_form_type: TransactionFormType = TransactionFormType.WALLET_DEPOSIT,
        wallet_type: WalletTypes = WalletTypes.AJO_AGENT,
        plan_type: PlanType = PlanType.AJO,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.CREDIT,
        status: Status = Status.SUCCESS,
        transaction_source: TransactionSource = TransactionSource.WALLET,
    ) -> Transaction:
        """
        Create a Cashout to Agent wallet transaction

        Args:
            user (AbstractUser): the user associated with this transaction
            amount (float): the amount being transferred
            transaction_form_type (TransactionFormType, optional): the form type of transaction. Defaults to TransactionFormType.WALLET_DEPOSIT.
            wallet_type (WalletTypes, optional): the wallet type. Defaults to WalletTypes.AJO_AGENT.
            plan_type (PlanType, optional): the plan type. Defaults to PlanType.AJO.
            transaction_type (TransactionTypeCreditOrDebitChoices, optional): the transaction type. Defaults to TransactionTypeCreditOrDebitChoices.CREDIT.
            status (Status, optional): the status of the transaction. Defaults to Status.PENDING.
            transaction_source (TransactionSource, optional): the source of the transaction. Defaults to TransactionSource.WALLET.

        Returns:
            Transaction: the created Transaction instance
        """
        transaction = Transaction.objects.create(
            user=user,
            amount=amount,
            description=transaction_description,
            wallet_type=wallet_type,
            transaction_form_type=transaction_form_type,
            transaction_source=transaction_source,
            plan_type=plan_type,
            transaction_type=transaction_type,
            status=status,
        )

        return transaction

    @staticmethod
    def create_loan_wallet_debit_transaction(
        user: AbstractUser,
        amount: float,
        ajo_user: AjoUser,
        description: str,
        wallet_type: WalletTypes = WalletTypes.AJO_LOAN,
        plan_type: PlanType = PlanType.LOAN,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.DEBIT,
        transaction_form_type: TransactionFormType = TransactionFormType.TRANSFER,
        status: Status = Status.SUCCESS,
        transaction_destination: TransactionDestination = TransactionDestination.WALLET,
    ) -> Transaction:
        """
        Creates a loan debit transaction

        Args:
            user (AbstractUser): the user
            amount (float): how much was paid
            ajo_user (AjoUser): the ajo user
            description(str): the transaction description
            plan_type (PlanType, optional): the plan type. Defaults to PlanType.LOAN.
            transaction_type (TransactionTypeCreditOrDebitChoices, optional): the transaction type. Defaults to TransactionTypeCreditOrDebitChoices.CREDIT.
            transaction_form_type (TransactionFormType, optional): the transaction form type. Defaults to TransactionFormType.WALLET_DEPOSIT.
            status (Status, optional): the status of the transaction. Defaults to Status.SUCCESS.
            transaction_destination (TransactionDestination, optional): the source of the transaction. Defaults to TransactionDestination.WALLET.

        Returns:
            Transaction: a created loan transaction
        """
        transaction = Transaction.objects.create(
            user=user,
            transaction_form_type=transaction_form_type,
            status=status,
            transaction_destination=transaction_destination,
            amount=amount,
            transaction_type=transaction_type,
            wallet_type=wallet_type,
            plan_type=plan_type,
            description=description,
            onboarded_user=ajo_user,
        )

        return transaction

    @staticmethod
    def create_loan_to_agent_wallet_transaction(
        user: AbstractUser,
        amount: float,
        description: str,
        wallet_type: WalletTypes = WalletTypes.AJO_AGENT,
        plan_type: PlanType = PlanType.LOAN,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.CREDIT,
        transaction_form_type: TransactionFormType = TransactionFormType.WALLET_DEPOSIT,
        status: Status = Status.SUCCESS,
        transaction_source: TransactionSource = TransactionSource.WALLET,
    ) -> Transaction:
        """
        Creates a loan to agent wallet transaction

        Args:
            user (AbstractUser): the user
            amount (float): how much was paid
            description(str): the transaction description
            plan_type (PlanType, optional): the plan type. Defaults to PlanType.LOAN.
            transaction_type (TransactionTypeCreditOrDebitChoices, optional): the transaction type. Defaults to TransactionTypeCreditOrDebitChoices.CREDIT.
            transaction_form_type (TransactionFormType, optional): the transaction form type. Defaults to TransactionFormType.WALLET_DEPOSIT.
            status (Status, optional): the status of the transaction. Defaults to Status.SUCCESS.
            transaction_source (TransactionSource, optional): the source of the transactuin. Defaults to TransactionSource.WALLET.

        Returns:
            Transaction: a created loan to agent wallet transaction
        """
        transaction = Transaction.objects.create(
            user=user,
            transaction_form_type=transaction_form_type,
            status=status,
            transaction_source=transaction_source,
            amount=amount,
            transaction_type=transaction_type,
            wallet_type=wallet_type,
            plan_type=plan_type,
            description=description,
        )

        return transaction

    @staticmethod
    def create_deposit_by_virtual_account_in_ajo_transaction(
        user: AbstractUser,
        amount: float,
        unique_reference: str,
        description: str,
        ajo_user: str | None = None,
        wallet_type: WalletTypes = WalletTypes.AJO_AGENT,
        plan_type: PlanType = PlanType.AJO,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.CREDIT,
        transaction_form_type: TransactionFormType = TransactionFormType.WALLET_DEPOSIT,
        status: Status = Status.SUCCESS,
        transaction_source: TransactionSource = TransactionSource.BANK_ACCOUNT,
        quotation_id: Optional[str] = None,
    ):
        """
        Creates a Deposit Transaction By Wallet Instance

        Args:
            quotation_id (str): the quotation_id of the savings type eg chestlock -> LI-CHK-{{string_of_length_12}}

        Returns:
            model instance: A transaction instance created in the database
        """
        transaction = Transaction.objects.create(
            user=user,
            onboarded_user=ajo_user,
            transaction_form_type=transaction_form_type,
            status=status,
            description=description,
            transaction_source=transaction_source,
            amount=amount,
            transaction_type=transaction_type,
            wallet_type=wallet_type,
            plan_type=plan_type,
            unique_reference=unique_reference,
            quotation_id=quotation_id,
        )

        return transaction

    @staticmethod
    def create_deposit_transaction(
        user: AbstractUser,
        amount: float,
        unique_reference: str,
        description: str,
        wallet_type: WalletTypes = WalletTypes.AJO_AGENT,
        plan_type: PlanType = PlanType.AJO,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.CREDIT,
        transaction_form_type: TransactionFormType = TransactionFormType.BANK_DEPOSIT,
        status: Status = Status.PENDING,
        transaction_source: TransactionSource = TransactionSource.BANK_ACCOUNT,
        ajo_user: AjoUser = None,
        transfer_provider=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
    ):
        """
        Creates a Deposit Transaction for funding wallets

        Returns:
            model instance: A transaction instance created in the database
        """
        transaction = Transaction.objects.create(
            user=user,
            transaction_form_type=transaction_form_type,
            status=status,
            description=description,
            transaction_source=transaction_source,
            amount=amount,
            wallet_type=wallet_type,
            transaction_type=transaction_type,
            plan_type=plan_type,
            unique_reference=unique_reference,
            onboarded_user=ajo_user,
            transfer_provider=transfer_provider,
        )

        return transaction

    @staticmethod
    def dynamic_deduction_from_wallet_transaction(
        user,
        amount: float,
        wallet_type: WalletTypes,
        request_data: dict = None,
        description: str | None = None,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.DEBIT,
        transaction_form_type: TransactionFormType = TransactionFormType.WALLET_WITHDRAWAL,
        status: Status = Status.PENDING,
        transaction_destination: TransactionDestination = TransactionDestination.WALLET,
        unique_reference: str | None = None,
        ajo_user: AjoUser | None = None,
        quotation_id: str | None = None,
        transaction_source: TransactionSource | None = None,
        transaction_source_id: str | None = None,
    ):
        """
        Create a Transaction For any Kind of Purchase By Wallet Instance

        Returns:
            Transaction: the transaction object that was created.
        """
        transaction = Transaction.objects.create(
            user=user,
            transaction_form_type=transaction_form_type,
            status=status,
            transaction_destination=transaction_destination,
            amount=amount,
            transaction_type=transaction_type,
            wallet_type=wallet_type,
            request_data=request_data,
            description=description,
            unique_reference=unique_reference,
            onboarded_user=ajo_user,
            quotation_id=quotation_id,
            transaction_source=transaction_source,
            transaction_source_id=transaction_source_id,
            transfer_provider=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
        )

        return transaction

    @staticmethod
    def prefunding_deposit_transaction(
        user: AbstractUser,
        amount: float,
        transaction_description: str,
        transaction_form_type: TransactionFormType = TransactionFormType.PREFUNDING_DEPOSIT,
        wallet_type: WalletTypes = WalletTypes.AJO_PREFUNDING,
        plan_type: PlanType = PlanType.AJO,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.CREDIT,
        status: Status = Status.PENDING,
        transaction_source: TransactionSource = TransactionSource.PREFUNDING,
    ) -> Transaction:
        """
        Create a prefunding transaction instance

        Args:
            user (AbstractUser): the user associated with this transaction.
            amount (float): the amount being transferred.
            transaction_description(str): the description of the transaction.
            transaction_form_type (TransactionFormType, optional): the form type of transaction. Defaults to TransactionFormType.PREFUNDING_DEPOSIT.
            wallet_type (WalletTypes, optional): the wallet type. Defaults to WalletTypes.AJO_AGENT.
            plan_type (PlanType, optional): the plan type. Defaults to PlanType.AJO.
            transaction_type (TransactionTypeCreditOrDebitChoices, optional): the transaction type. Defaults to TransactionTypeCreditOrDebitChoices.CREDIT.
            status (Status, optional): the status of the transaction. Defaults to Status.PENDING.
            transaction_source (TransactionSource, optional): the source of the transaction. Defaults to TransactionSource.PREFUNDING.

        Returns:
            Transaction: the created Transaction instance
        """
        transaction = Transaction.objects.create(
            user=user,
            amount=amount,
            description=transaction_description,
            wallet_type=wallet_type,
            transaction_form_type=transaction_form_type,
            transaction_source=transaction_source,
            plan_type=plan_type,
            transaction_type=transaction_type,
            status=status,
        )

        return transaction

    @staticmethod
    def deduct_prefunding_wallet_transaction(
        user: AbstractUser,
        amount: float,
        transaction_description: str,
        transaction_form_type: TransactionFormType = TransactionFormType.TRANSFER,
        wallet_type: WalletTypes = WalletTypes.AJO_PREFUNDING,
        plan_type: PlanType = PlanType.AJO,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.DEBIT,
        status: Status = Status.PENDING,
        transaction_source: TransactionSource = TransactionSource.WALLET,
    ) -> Transaction:
        """
        Create a prefunding deduction transaction

        Args:
            user (AbstractUser): the user associated with this transaction.
            amount (float): the amount being transferred.
            transaction_description(str): the description of the transaction.
            transaction_form_type (TransactionFormType, optional): the form type of transaction. Defaults to TransactionFormType.WALLET_WITHDRAWAL.
            wallet_type (WalletTypes, optional): the wallet type. Defaults to WalletTypes.AJO_AGENT.
            plan_type (PlanType, optional): the plan type. Defaults to PlanType.AJO.
            transaction_type (TransactionTypeCreditOrDebitChoices, optional): the transaction type. Defaults to TransactionTypeCreditOrDebitChoices.DEBIT.
            status (Status, optional): the status of the transaction. Defaults to Status.PENDING.
            transaction_source (TransactionSource, optional): the source of the transaction. Defaults to TransactionSource.WALLET.

        Returns:
            Transaction: the created Transaction instance
        """
        transaction = Transaction.objects.create(
            user=user,
            amount=amount,
            description=transaction_description,
            wallet_type=wallet_type,
            transaction_form_type=transaction_form_type,
            transaction_source=transaction_source,
            plan_type=plan_type,
            transaction_type=transaction_type,
            status=status,
        )

        return transaction

    @staticmethod
    def payback_prefunding_transaction(
        user: AbstractUser,
        amount: float,
        transaction_description: str,
        unique_reference: str,
        transaction_form_type: TransactionFormType = TransactionFormType.WALLET_DEPOSIT,
        wallet_type: WalletTypes = WalletTypes.AJO_PREFUNDING,
        plan_type: PlanType = PlanType.AJO,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.CREDIT,
        status: Status = Status.PENDING,
        transaction_source: TransactionSource = TransactionSource.BANK_ACCOUNT,
    ) -> Transaction:
        """
        Create a prefunding payback transaction

        Args:
            user (AbstractUser): the user associated with this transaction.
            amount (float): the amount being paid back.
            transaction_description(str): the description of the transaction.
            transaction_form_type (TransactionFormType, optional): the form type of transaction. Defaults to TransactionFormType.WALLET_DEPOSIT.
            wallet_type (WalletTypes, optional): the wallet type. Defaults to WalletTypes.AJO_PREFUNDING.
            plan_type (PlanType, optional): the plan type. Defaults to PlanType.AJO.
            transaction_type (TransactionTypeCreditOrDebitChoices, optional): the transaction type. Defaults to TransactionTypeCreditOrDebitChoices.CREDIT.
            status (Status, optional): the status of the transaction. Defaults to Status.PENDING.
            transaction_source (TransactionSource, optional): the source of the transaction. Defaults to TransactionSource.WALLET.

        Returns:
            Transaction: the created Transaction instance
        """
        transaction = Transaction.objects.create(
            user=user,
            amount=amount,
            description=transaction_description,
            wallet_type=wallet_type,
            transaction_form_type=transaction_form_type,
            transaction_source=transaction_source,
            plan_type=plan_type,
            transaction_type=transaction_type,
            status=status,
            unique_reference=unique_reference,
        )

        return transaction

    @staticmethod
    def service_charge_transaction(
        user: AbstractUser,
        amount: float,
        transaction_description: str,
        quotation_id: str,
        ajo_user: AjoUser | None = None,
        transaction_form_type: TransactionFormType = TransactionFormType.SERVICE_CHARGE,
        wallet_type: WalletTypes | None = None,
        plan_type: PlanType = PlanType.AJO,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.DEBIT,
        status: Status = Status.SUCCESS,
        transaction_source: TransactionSource = TransactionSource.COMMISSION,
        transaction_date_completed: str | None = timezone.localtime(),
    ) -> Transaction:
        """
        Create a transaction for service charge
        Args:
            user (AbstractUser): the user model for this transaction
            amount (float): the amount taken as service charge
            transaction_description (str): the transaction description
            quotation_id (str): the quotation id of the plan this service charge was taken
            ajo_user (AjoUser | None, optional): the ajo user. Defaults to None.
            transaction_form_type (TransactionFormType, optional): the transaction form type. Defaults to TransactionFormType.SERVICE_CHARGE.
            wallet_type (WalletTypes | None, optional): the wallet type. Defaults to None.
            plan_type (PlanType, optional): the plan type. Defaults to PlanType.AJO.
            transaction_type (TransactionTypeCreditOrDebitChoices, optional): the transaction type. Defaults to TransactionTypeCreditOrDebitChoices.DEBIT.
            status (Status, optional): the status of the transaction. Defaults to Status.SUCCESS.
            transaction_source (TransactionSource, optional): the source of the transaction. Defaults to TransactionSource.COMMISSION.
        Returns:
            Transaction: _description_
        """
        transaction = Transaction.objects.create(
            user=user,
            onboarded_user=ajo_user,
            amount=amount,
            quotation_id=quotation_id,
            description=transaction_description,
            wallet_type=wallet_type,
            transaction_form_type=transaction_form_type,
            transaction_source=transaction_source,
            plan_type=plan_type,
            transaction_type=transaction_type,
            status=status,
            transaction_date_completed=transaction_date_completed,
        )

        return transaction

    @staticmethod
    def create_transfer_to_external_account_transaction(
        user: AbstractUser,
        amount: float,
        description: str,
        wallet_type: WalletTypes,
        unique_reference: str | None = None,
        ajo_user: str | None = None,
        plan_type: PlanType = PlanType.AJO,
        transaction_type: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.DEBIT,
        transaction_form_type: TransactionFormType = TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT,
        status: Status = Status.PENDING,
        transaction_source: TransactionSource = TransactionSource.WALLET,
        transaction_destination: TransactionDestination = TransactionDestination.BANK_ACCOUNT,
        request_data: Dict[str, Any] | None = None,
        quotation_id: str | None = None,
        transfer_provider: str = DisbursementProviderType.AGENCY_BANKING_PROVIDER,
    ) -> Transaction:
        """
        Creates a transfer to an external account transaction instance

        Args:
            user (AbstractUser): the user
            amount (float): the amount
            unique_reference (str): the unique reference
            description (str): the transaction description
            wallet_type (WalletTypes): the wallet type
            ajo_user (str | None, optional): the ajo user. Defaults to None.
            plan_type (PlanType, optional): the plan type. Defaults to PlanType.AJO.
            transaction_type (TransactionTypeCreditOrDebitChoices, optional): the transaction type. Defaults to TransactionTypeCreditOrDebitChoices.DEBIT.
            transaction_form_type (TransactionFormType, optional): the transaction form type. Defaults to TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT.
            status (Status, optional): the status of the transaction. Defaults to Status.PENDING.
            transaction_source (TransactionSource, optional): the source of the transaction. Defaults to TransactionSource.WALLET.
            transaction_destination (TransactionDestination, optional): the destination of the transaction. Defaults to TransactionDestination.BANK_ACCOUNT.

        Returns:
            Transaction: the created Transaction instance.
        """

        transaction = Transaction.objects.create(
            user=user,
            onboarded_user=ajo_user,
            transaction_form_type=transaction_form_type,
            status=status,
            description=description,
            transaction_source=transaction_source,
            transaction_destination=transaction_destination,
            amount=amount,
            transaction_type=transaction_type,
            wallet_type=wallet_type,
            plan_type=plan_type,
            unique_reference=unique_reference,
            request_data=request_data,
            quotation_id=quotation_id,
            transfer_provider=transfer_provider,
        )

        return transaction

    @staticmethod
    def create_internal_transfer_between_accounts(
        user: AbstractUser,
        amount: float,
        transaction_description: str,
        transaction_type: TransactionTypeCreditOrDebitChoices,
        transaction_form_type: TransactionFormType = TransactionFormType.INTERNAL_TRANSFER_TO_LOANS,
        wallet_type: WalletTypes = None,
        status: Status = Status.PENDING,
        plan_type: PlanType | None = None,
        transaction_source: TransactionSource = TransactionSource.BANK_ACCOUNT,
        transaction_destination: TransactionDestination = TransactionDestination.BANK_ACCOUNT,
        ajo_user: AjoUser | None = None,
        quotation_id: str | None = None,
        transfer_provider: DisbursementProviderType = DisbursementProviderType.AGENCY_BANKING_PROVIDER,
    ) -> Transaction:
        """ "
        Create a transaction between internal accounts

        Args:
            user (AbstractUser): the user.
            amount (float): the amount involved
            wallet_type (WalletTypes): the wallet type.
            plan_type (PlanType, optional): the plan type. Defaults to None.
            transaction_description (str): the transaction description
            transaction_type (TransactionTypeCreditOrDebitChoices, optional): the type of transaction.
            transaction_form_type (TransactionFormType, optional): the form type of the transaction. Defaults to TransactionFormType.TRANSFER.
            status (Status, optional): the status of the transaction upon creation. Defaults to Status.PENDING.
            transaction_source (TransactionSource, optional): the source of the transaction. Defaults to TransactionSource.WALLET.
            transaction_destination (TransactionDestination, optional): the destination of the transaction. Defaults to TransactionDestination.WALLET.
            ajo_user (AjoUser | None, optional): the ajo user involved in this transaction. Defaults to None.

        Returns:
            Transaction: the created Transaction instance
        """

        transaction = Transaction.objects.create(
            user=user,
            transaction_form_type=transaction_form_type,
            status=status,
            transaction_source=transaction_source,
            amount=amount,
            description=transaction_description,
            transaction_type=transaction_type,
            wallet_type=wallet_type,
            plan_type=plan_type,
            transaction_destination=transaction_destination,
            onboarded_user=ajo_user,
            transfer_provider=transfer_provider,
            quotation_id=quotation_id
        )

        return transaction


class CommissionService:
    @staticmethod
    def create_commission_instance(
        user: AbstractUser,
        commission_type: CommissionType,
        amount: float,
        description: str,
        quotation_id: str,
        total_amount_taken_as_commission: float,
        plan_type: PlanType,
    ) -> Commission:
        """
        Creates a commission instance

        Args:
            user (AbstractUser): the user
            commission_type (CommissionType): the type of commission
            amount (float): the amount of commission
            description (str): the commissions description
            quotation_id (str): the quotation id that the commission was taken from
            total_amount_taken_as_commission (float): the total amount taken as commission before the split happened
            plan_type (PlanType): the plan type

        Returns:
            Commission: the created Commmission instance
        """
        commission = Commission.objects.create(
            user=user,
            commission_type=commission_type,
            amount=amount,
            description=description,
            quotation_id=quotation_id,
            total_amount_taken_as_commission=total_amount_taken_as_commission,
            plan_type=plan_type,
        )

        return commission


def increment_total_saved_in_ajo_user(ajo_user: AjoUser, amount: float) -> None:
    """
    Increase the total_amount_saved for the Ajo user

    Args:
        ajo_user (AjoUser): Ajo User instance
        amount (float): amount to increment the field by
    """
    if amount <= 0:
        raise ValueError("amount should be more than 0")
    ajo_user.total_money_saved += amount
    ajo_user.save()


def increment_total_withdrawn_in_ajo_user(ajo_user: AjoUser, amount: float) -> None:
    """
    Increase the total_amount_withdrawn for the Ajo user

    Args:
        ajo_user (AjoUser): ajo user instance
        amount (float): amount to increment field by
    """
    if amount <= 0:
        raise ValueError("amount should be more than 0")
    ajo_user.total_money_withdrawn += amount
    ajo_user.total_withdrawals += 1
    ajo_user.save()


def update_transaction_fields_after_credit_in_rotation_group(
    transaction: Transaction,
    debit_credit: dict,
    rotation_group: RotationGroup,
) -> None:
    """
    Increments the transaction fields after credit

    Args:
        transaction_instance (Transaction): The transaction object to be changed
        debit_credit (dict): the dictionary containing debit_credit information
        rotation_group (RotationGroup): the rotation group instance
    """
    # Set the balances before
    transaction.wallet_balance_before = debit_credit.get("balance_before")
    transaction.plan_balance_before = rotation_group.total_amount_saved

    # Set the balance after
    transaction.wallet_balance_after = debit_credit.get("balance_after")
    transaction.plan_balance_after = (
        transaction.plan_balance_before + transaction.amount
    )

    # Save the changes
    transaction.save()


def update_rotation_group_fields_after_deposit(
    rotation_group: RotationGroup,
    transaction: Transaction,
) -> None:
    """
    Increments the fields of the RotationGroup

    Args:
        rotation_group_instance (RotationGroup)
        transaction_obj (Transaction)
        debit_credit_obj (DebitCreditRecordOnAccount)
    """
    # Set the balance before
    rotation_group.balance_before = rotation_group.total_amount_saved

    # increment the total amount saved so far
    rotation_group.total_amount_saved += transaction.amount

    # Change the balance after
    rotation_group.balance_after = rotation_group.balance_before + transaction.amount

    # Save the changes
    rotation_group.save()


def update_rotation_group_member_fields_after_contribution(
    rotation_group_member: RotationGroupMember,
    transaction: Transaction,
) -> None:
    """
    Update the fields of the RotationGroupMember

    Args:
        rotation_group_member (RotationGroupMember)
        transaction (Transaction):
    """
    # Set the balance before
    rotation_group_member.balance_before = (
        rotation_group_member.total_amount_contributed
    )

    # Update the total amount contributed so far
    rotation_group_member.total_amount_contributed += transaction.amount

    # Set the balance after
    rotation_group_member.balance_after = (
        rotation_group_member.balance_before + transaction.amount
    )

    # Save the changes
    rotation_group_member.save()


class WithdrawalAccountService:
    @staticmethod
    def set_withdrawal_account(data: dict) -> WithdrawalAccount:
        """
        This creates a WithdrawalAccount

        Args:
            data (dict): the dictionary should contain the following keys:
                -   account_number
                -   account_name
                -   bank_code
                -   bank_name
                -   user

        Returns:
            WithdrawalAccount: the created model instance
        """

        try:
            withdrawal_account = WithdrawalAccount.objects.create(**data)

        except IntegrityError as err:
            raise ValueError("this user has an account set already.")

        return withdrawal_account
