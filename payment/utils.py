import json
import random
from datetime import datetime, timed<PERSON>ta
from typing import Union

import pytz
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.db import IntegrityError
from django.utils import timezone
from rest_framework import serializers

from accounts.models import UserRecurrentChargeTable
from ajo.models import AjoSaving, AjoUser, RotationGroup
from chestlock.models import ChestLock, Onlending
from halal.model_choices import Frequency
from halal.models import Halal
from quicksavings.models import QuickSavings

from .model_choices import PaymentMethod, PlanType, Status
from .models import DebitCard, Transaction
from .wrapper import PayStack


def is_past_month_year(target_month, target_year) -> bool:
    """
    Checks if the target month and year are in the past.
    If the target month, year are in the future (has not passed maturity_date),
    it will return True
    else it will return False

    Args:
        target_month (int): the month
        target_year (int): the year

    Returns:
        bool: True/False
    """
    current_time = timezone.localtime()

    # Get current month and year
    current_month = current_time.month
    current_year = current_time.year

    # Compare with target month and year
    if current_year < target_year or (current_year == target_year and current_month <= target_month):
        return True
    else:
        return False


def two_decimal_places(value: float) -> float:
    """
    Format a float value to 2 decimal places

    Args:
        value (float): the value to be formatted

    Returns:
        float: the formatted value
    """
    return round(value, 2)


def format_currency(amount: float) -> str:
    """
    It formats a float to string and adds a comma after every three digits
    e.g.

        500 -> "500.00"

        1000 -> "1,000.00"

        40000 -> "40,000.00"

        100,000 -> "100,000.00"

    Args:
        amount (float): takes in a float

    Returns:
        str: returns a string version of that float
    """
    if amount >= 1000:
        return "{:,.2f}".format(amount)
    else:
        return "{:.2f}".format(amount)


def convert_string_to_aware_datetime_object(date_string: str) -> datetime:
    """
    Converts a string to an aware datetime object

    Args:
        date_string (str): a string of the datetime information

    Returns:
        datetime: an aware datetime object
    """
    # convert the string to a datetime object
    datetime_object = datetime.strptime(date_string, "%Y-%m-%dT%H:%M:%S.%f")
    # make the datetime object timezone aware
    aware_datetime_object = timezone.make_aware(datetime_object)

    return aware_datetime_object


def generate_default_code() -> str:
    """
    Generates a random 12 digits string
    Returns:
        str: the generated 12 digits string
    """
    return "".join(str(random.randint(0, 9)) for _ in range(12))


class CustomValidationError(serializers.ValidationError):
    def __init__(self, detail=None, code=None):
        if isinstance(detail, dict):
            # Convert status value to boolean if it exists
            if "status" in detail:
                detail["status"] = bool(detail["status"])
        super().__init__(detail, code)


class Utility:
    @staticmethod
    def get_plan_instance(
        user: AbstractUser,
        plan_id: int | None = None,
        plan_type: str | None = None,
        quotation_id: str | None = None,
        ajo_user: AjoUser | None = None,
    ) -> Union[ChestLock, Halal, QuickSavings, Onlending]:
        if quotation_id:
            quotation_plan_type = quotation_id[3:6].upper()
            valid_types = [
                "CHK",
                "HAL",
                "QSV",
                "AJO",
                "ONL",
            ]
            if quotation_plan_type not in valid_types:
                raise serializers.ValidationError(
                    {
                        "error": "703",
                        "status": "failed",
                        "message": "error in obtaining a valid plan instance with the quotation id provided",
                    }
                )

            if quotation_plan_type == "CHK":
                chestlock_qs_instance = ChestLock.objects.filter(user=user, quotation_id=quotation_id)
                if chestlock_qs_instance:
                    return chestlock_qs_instance.first()
                else:
                    raise serializers.ValidationError(
                        {
                            "error": "703",
                            "status": "failed",
                            "message": "error obtaining chestlock instance, check quotation_id or user supplied",
                        }
                    )
            elif quotation_plan_type == "HAL":
                halal_qs_instance = Halal.objects.filter(user=user, quotation_id=quotation_id)
                if halal_qs_instance:
                    return halal_qs_instance.first()
                else:
                    raise serializers.ValidationError(
                        {
                            "error": "703",
                            "status": "failed",
                            "message": "error obtaining halal instance, check quotation_id or user being passed",
                        }
                    )
            elif quotation_plan_type == "QSV":
                quicksavings_qs_instance = QuickSavings.objects.filter(user=user, quotation_id=quotation_id)
                if quicksavings_qs_instance:
                    return quicksavings_qs_instance.first()
                else:
                    raise serializers.ValidationError(
                        {
                            "error": "703",
                            "status": "failed",
                            "message": "error obtaining quicksavings instance, check quotation_id or user being passed",
                        }
                    )

            elif quotation_plan_type == "AJO":
                ajo_qs_instance = AjoSaving.objects.filter(user=user, quotation_id=quotation_id)
                # ajo_qs_instance = AjoSaving.objects.filter(user=user, quotation_id=quotation_id, ajo_user=ajo_user)
                if ajo_qs_instance:
                    return ajo_qs_instance.first()
                else:
                    raise serializers.ValidationError(
                        {
                            "error": "703",
                            "status": "failed",
                            "message": "error obtaining ajo instance, check quotation_id or user or ajo_user being passed",
                        }
                    )

            elif quotation_plan_type == "ONL":
                onlending_qs = Onlending.objects.filter(
                    user=user,
                    quotation_id=quotation_id,
                )
                if onlending_qs:
                    return onlending_qs.first()
                else:
                    raise serializers.ValidationError(
                        {
                            "error": "703",
                            "status": "failed",
                            "message": "error obtaining onlending instance, check quotation_id or user supplied",
                        }
                    )

        if plan_type.upper() not in PlanType:
            raise serializers.ValidationError(
                {
                    "error": "703",
                    "status": "failed",
                    "message": "error in getting plan instance",
                }
            )

        if plan_type.upper() == PlanType.CHESTLOCK:
            chestlock_qs_instance = ChestLock.objects.filter(user=user, id=plan_id)
            if chestlock_qs_instance:
                return chestlock_qs_instance.first()
            else:
                raise serializers.ValidationError(
                    {
                        "error": "703",
                        "status": "failed",
                        "message": "error obtaining chestlock instance, check plan_id and/or plan type",
                    }
                )

        if plan_type.upper() == PlanType.HALAL:
            halal_qs_instance = Halal.objects.filter(user=user, id=plan_id)
            if halal_qs_instance:
                return halal_qs_instance.first()
            else:
                raise serializers.ValidationError(
                    {
                        "error": "703",
                        "status": "failed",
                        "message": "error obtaining halal instance, check plan_id and/or plan type",
                    }
                )

        if plan_type.upper() == PlanType.QUICKSAVINGS:
            quicksavings_qs_instance = QuickSavings.objects.filter(user=user, id=plan_id)
            if quicksavings_qs_instance:
                return quicksavings_qs_instance.first()
            else:
                raise serializers.ValidationError(
                    {
                        "error": "703",
                        "status": "failed",
                        "message": "error obtaining quicksavings instance, check plan_id and/or plan type",
                    }
                )

        if plan_type.upper() == PlanType.AJO:
            ajo_qs_instance = AjoSaving.objects.filter(user=user, id=plan_id)
            # ajo_qs_instance = AjoSaving.objects.filter(user=user, id=plan_id, ajo_user=ajo_user)
            if ajo_qs_instance:
                return ajo_qs_instance.first()
            else:
                raise serializers.ValidationError(
                    {
                        "error": "703",
                        "status": "failed",
                        "message": "error obtaining ajosaving instance, check plan_id and/or user/ajo_user",
                    }
                )

        if plan_type.upper() == PlanType.ONLENDING:
            onlending_qs = Onlending.objects.filter(user=user, id=plan_id)
            if onlending_qs:
                return onlending_qs.first()
            else:
                raise serializers.ValidationError(
                    {
                        "error": "703",
                        "status": "failed",
                        "message": "error obtaining onlending instance, check plan_id and/or plan type",
                    }
                )

    @staticmethod
    def set_auto_charge_table_next_charge_amount(auto_charge_table_instance, amount):
        # set last charge amount
        auto_charge_table_instance.last_charge_amount = amount

        # set the next charge amount
        try:
            plan_instance = Utility.get_plan_instance(
                user=auto_charge_table_instance.user,
                plan_id=auto_charge_table_instance.plan_id,
                plan_type=auto_charge_table_instance.plan_type,
            )
        except serializers.ValidationError as err:
            raise Exception("problem encountered in obtaining plan instance")

        target = float(plan_instance.target)
        amount_saved_so_far = float(plan_instance.amount_saved)
        periodic_amount = float(plan_instance.periodic_amount)

        remaining_amount = target - amount_saved_so_far

        if remaining_amount >= periodic_amount:
            auto_charge_table_instance.next_charge_amount = periodic_amount
        else:
            auto_charge_table_instance.next_charge_amount = remaining_amount

        auto_charge_table_instance.save()

        return True

    @staticmethod
    def set_next_run_time(interval, hour):
        now = timezone.localtime()

        if interval == Frequency.DAILY:
            next_run_time = now + timedelta(days=1)
            next_run_time_2 = datetime.combine(next_run_time, datetime.min.time()) + timedelta(hours=hour)

        elif interval == Frequency.WEEKLY:
            next_run_time = now + timedelta(days=7)
            next_run_time_2 = datetime.combine(next_run_time, datetime.min.time()) + timedelta(hours=hour)

        elif interval == Frequency.MONTHLY:
            next_run_time = now.replace(month=now.month + 1)

            ## Handle the case where the current month is December
            if next_run_time.month == 1:
                next_run_time = next_run_time.replace(year=next_run_time.year + 1)

            next_run_time_2 = datetime.combine(next_run_time, datetime.min.time()) + timedelta(hours=hour)

        elif interval == Frequency.JUST_ONCE:
            next_run_time_2 = None

        else:
            next_run_time_2 = None

        return next_run_time_2

    @staticmethod
    def get_plan_queryset(user: AbstractUser, plan_id: int, quotation_id: str):
        saving_types = [
            "CHK",
            "HAL",
            "QSV",
        ]

        quotation_savings_type = quotation_id[3:6]
        if quotation_savings_type in saving_types:
            if quotation_savings_type == "CHK":
                chk_plan_qs = ChestLock.objects.filter(user=user, id=plan_id)
                return chk_plan_qs

            elif quotation_savings_type == "HAL":
                hal_plan_qs = Halal.objects.filter(user=user, id=plan_id)
                return hal_plan_qs

            elif quotation_savings_type == "QSV":
                qsv_plan_qs = QuickSavings.objects.filter(user=user, id=plan_id)
                return qsv_plan_qs

            else:
                # the aim is to return an empty Queryset
                empty_plan_qs = ChestLock.objects.none() or QuickSavings.objects.none()
                return empty_plan_qs

    @staticmethod
    def create_recurrent_charging_for_plan(
        user,
        plan_type: PlanType,
        plan_instance: ChestLock | Halal | QuickSavings | RotationGroup | Onlending,
        payment_method: PaymentMethod,
        next_run_time: datetime | None = None,
        hour: int = 12,
        quotation_id: str | None = None,
        masked_pan: str | None = None,
        auth_code: str | None = None,
    ) -> None:
        """
        Creates a UserRecurrentChargeTable object

        Args:
            user (AbstractUser): the user that wants to create it.
            plan_type (PlanType): the plan type of the plan.
            plan_instance (ChestLock | Halal | QuickSavings): the plan object.
            payment_method (PaymentMethod): the payment method.
            next_run_time: (datetime | None): the next run time. Defaults to None.
            masked_pan (str | None): masked pan of the card. Defaults to None.
            auth_code (str | None): authorization code of the card. Defaults to None.

        Raises:
            Exception: An error occurred while creating an instance.
        """
        try:
            if plan_type == PlanType.ROTATIONGROUP:
                UserRecurrentChargeTable.objects.create(
                    user=user,
                    plan_type=plan_type,
                    plan_quotation_id=quotation_id if quotation_id else plan_instance.quotation_id,
                    plan_id=plan_instance.id,
                    plan_frequency=plan_instance.frequency,
                    payment_method=payment_method,
                    plan_hour=hour if hour else plan_instance.hour,
                    next_charge_amount=plan_instance.contribution_amount,
                    next_run_time=next_run_time,
                    masked_pan=masked_pan,
                    auth_code=auth_code,
                )
            else:
                UserRecurrentChargeTable.objects.create(
                    user=user,
                    plan_type=plan_type,
                    plan_id=plan_instance.id,
                    plan_quotation_id=plan_instance.quotation_id,
                    plan_frequency=plan_instance.frequency,
                    payment_method=payment_method,
                    plan_hour=plan_instance.hour,
                    next_charge_amount=plan_instance.periodic_amount,
                    next_run_time=timezone.make_aware(
                        Utility.set_next_run_time(
                            interval=plan_instance.frequency,
                            hour=plan_instance.hour,
                        )
                    ),
                    masked_pan=masked_pan,
                    auth_code=auth_code,
                )
        except IntegrityError as err:
            raise Exception(f"there was a problem creating the recurrent charge object: {str(err)}")

    @classmethod
    def retrieve_recurrent_charging_instance(
        cls,
        user: AbstractUser,
        plan_type: PlanType,
        plan_quotation_id: str,
    ) -> UserRecurrentChargeTable | None:
        try:
            instance = UserRecurrentChargeTable.objects.get(
                user=user,
                plan_type=plan_type,
                plan_quotation_id=plan_quotation_id,
            )
            return instance
        except UserRecurrentChargeTable.DoesNotExist:
            return None

    @classmethod
    def recurrent_charging_instance_exists(
        cls,
        user: AbstractUser,
        plan_type: str,
        plan_quotation_id: str,
    ) -> bool:
        return UserRecurrentChargeTable.objects.filter(
            user=user,
            plan_type=plan_type,
            plan_quotation_id=plan_quotation_id,
        ).exists()


def tokenize_card(user: object, response: dict) -> dict:
    print(response)
    status = response.get("status")
    data_event = response.get("data").get("event")
    data_message = response.get("data").get("transactionRef").get("message")
    transaction_message = response.get("transactionRef").get("message")
    transaction_status = response.get("transactionRef").get("status")

    if (status == "success" or data_event == "successful" or transaction_status == "success") and (
        data_message == "Approved",
        transaction_message == "Approved",
    ):
        transaction_reference = response.get("transactionRef").get("reference")

        paystack = PayStack()
        verify_transaction_response = paystack.verify_transactions(transaction_reference=transaction_reference)
        print(verify_transaction_response)

        if (
            verify_transaction_response.get("status") is True
            or verify_transaction_response.get("message") == "Verification successful"
        ):
            data = verify_transaction_response.get("data")
            data_status = data.get("status")
            gateway_response = data.get("gateway_response")

            if data_status == "success" or gateway_response == "Successful":
                # Convert the amount from kobo to naira
                # amount = data["amount"]/100

                card_auth = data.get("authorization")
                customer = data.get("customer")

                auth_code = card_auth["authorization_code"]

                try:
                    DebitCard.objects.get(
                        card_signature=card_auth.get("signature"),
                        card_last_four_digits=card_auth.get("last4"),
                    )

                    response = {
                        "status": False,
                        "message": "card already exists",
                        "card_result": None,
                    }

                    return response

                except DebitCard.DoesNotExist:
                    # TODO: Check the number of cards a user has here.
                    # if user.card.count() <= 1:

                    exp_month = card_auth.get("exp_month")
                    exp_year = card_auth.get("exp_year")

                    card = DebitCard.objects.create(
                        user=user,
                        bin=card_auth.get("bin"),
                        card_last_four_digits=card_auth.get("last4"),
                        exp_month=exp_month,
                        exp_year=exp_year,
                        channel=card_auth.get("channel"),
                        card_type=card_auth.get("card_type"),
                        bank_name=card_auth.get("bank"),
                        country_code=card_auth.get("country_code"),
                        brand=card_auth.get("brand"),
                        reusable=card_auth.get("reusable"),
                        card_signature=card_auth.get("signature"),
                        account_name=card_auth.get("account_name", None),
                        customer_id=customer.get("id"),
                        customer_code=customer.get("customer_code"),
                        customer_email=customer.get("email"),
                        authorization_code=auth_code,
                        payload=response,
                    )

                    response = {
                        "status": True,
                        "message": "successful",
                        "card_result": {
                            "id": card.id,
                            "last_four_digits": card.card_last_four_digits,
                            "exp_month": card.exp_month,
                            "exp_year": card.exp_year,
                            "card_type": card.card_type,
                            "brand": card.brand,
                            "reusable": card.reusable,
                        },
                    }

                    return response
        else:
            if (
                verify_transaction_response.get("status") is False
                or verify_transaction_response.get("message") == "Transaction reference not found"
            ):
                response = {
                    "status": False,
                    "message": "Transaction reference not found",
                }
                return response


def charge_card(
    user: object,
    card_id: int,
    amount: float,
    transaction_type: str,
    transaction_method: str,
    quotation_id: str,
) -> dict:
    """
    Charge the user's card
    """
    try:
        card_instance = DebitCard.objects.get(id=card_id)
        email = card_instance.customer_email
        paystack = PayStack()

        if card_instance.reusable is True:
            charge_card = paystack.charge_reusable_card(
                email=email,
                amount=amount,
                auth_code=card_instance.authorization_code,
            )

            status = charge_card.get("status")
            if status is True:
                data = charge_card.get("data")
                gateway_response = data.get("gateway_response")

                if gateway_response == "Approved":
                    transaction_date = data["transaction_date"]
                    reference = data["reference"]

                    transaction = Transaction.objects.create(
                        user=user,
                        card_instance=card_instance,
                        transaction_date=transaction_date,
                        reference=reference,
                        amount=amount,
                        transaction_type=transaction_type,
                        transaction_method=transaction_method,
                        status=Status.SUCCESS,
                        payload=charge_card,
                        gateway_response=gateway_response,
                        paid_at=data.get("paidAt", ""),
                        quotation_id=quotation_id,
                    )

                    # RawPaystackData.objects.create(
                    #     user=user,
                    #     data=charge_card,
                    # )

                    result = {
                        "status": True,
                        "message": "transaction was successful",
                        "results": {
                            "amount": transaction.amount,
                            "transaction_date": transaction_date,
                            "card_type": card_instance.card_type,
                        },
                    }

                    return result

                else:
                    result = {
                        "status": False,
                        "message": "",
                    }

                    return result
            else:
                result = {
                    "status": False,
                    "message": "card is not reusable",
                }

                return result

    except DebitCard.DoesNotExist:
        result = {
            "status": False,
            "message": "invalid card",
        }

        return result


# 1. remaining less than or equal to periodic amount
# 2.

def compare_transfer_out_with_wema_inflows(amount):
    """
    Compare the total amount transferred out of the Wema account
    with the total amount transferred into the Wema account
    """
    from django.db.models import Sum
    from payment.model_choices import DisbursementProviderType, TransactionFormType
    from loans.helpers.core_banking import CoreBankingManager

    total_transfer_out = Transaction.objects.filter(
        transfer_provider=DisbursementProviderType.WEMA_PROVIDER,
        transaction_form_type=TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT,
        status=Status.SUCCESS,
    ).aggregate(total=Sum("amount"))["total"] or 0.00

    # Check Corebanking for total inflows
    response = CoreBankingManager.check_account_details()
    total_inflows = response.get("data", {}).get("acount_details").get("total_wema_inflow", 0.00)

    # Confirm that total transfer amount after this transfer will not exceed total inflows into wema account
    transfer_amount = total_transfer_out + amount

    return transfer_amount <= total_inflows