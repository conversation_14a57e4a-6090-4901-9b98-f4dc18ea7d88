class CreditCard(BaseModel):
    user = models.ForeignKey(to=User, on_delete=models.PROTECT, null=True)
    paystack_customer_id = models.CharField(
        max_length=250, editable=False, null=True, blank=True
    )
    paystack_customer_code = models.CharField(
        max_length=250, editable=False, null=True, blank=True
    )
    card_email = models.CharField(max_length=250, null=True, blank=True)
    bin = models.CharField(max_length=250, null=True, blank=True)
    last_four_digits = models.CharField(max_length=250, null=True, blank=True)
    exp_month = models.CharField(max_length=250, null=True, blank=True)
    exp_year = models.CharField(max_length=250, null=True, blank=True)
    channel = models.CharField(max_length=250, null=True, blank=True)
    card_type = models.Char<PERSON>ield(max_length=250, null=True, blank=True)
    bank = models.Char<PERSON>ield(max_length=250, null=True, blank=True)
    country_code = models.Char<PERSON>ield(max_length=250, null=True, blank=True)
    brand = models.Char<PERSON>ield(max_length=250, null=True, blank=True)
    reusable = models.BooleanField(default=False)
    signature = models.CharField(
        max_length=250, editable=False, unique=True, null=True, blank=True
    )
    account_name = models.CharField(max_length=250, null=True, blank=True)
    authorization_code = models.TextField(editable=False, blank=True, null=True)
    payload = models.TextField()

    def __str__(self) -> str:
        return str(self.paystack_customer_id + self.signature)

    @classmethod
    def card_tokenization(cls, user, resp):

        status = resp.get("status")
        data_event = resp.get("data").get("event")
        data_message = resp.get("data").get("transactionRef").get("message")
        trnx_message = resp.get("transactionRef").get("message")
        trnx_status = resp.get("transactionRef").get("status")

        if (
            status == "success"
            or data_event == "successful"
            or trnx_status == "success"
        ) and (data_message == "Approved", trnx_message == "Approved"):

            transactionRef = resp.get("transactionRef").get("reference")

            paystack = PayStack()
            verify_trnx_resp = paystack.verify_transactions(
                transaction_ref=transactionRef
            )

            if (
                verify_trnx_resp.get("status") is True
                or verify_trnx_resp.get("message") == "Verification successful"
            ):
                data = verify_trnx_resp.get("data")
                data_status = data.get("status")
                gateway_response = data.get("gateway_response")

                if data_status == "success" or gateway_response == "Successful":

                    # convert amount from kobo to naira
                    # amount = data["amount"] / 100

                    card_auth = data.get("authorization")
                    customer = data.get("customer")

                    auth_code = card_auth["authorization_code"]

                    try:
                        cls.objects.get(
                            signature=card_auth.get("signature"),
                            last_four_digits=card_auth.get("last4"),
                        )

                        response = {
                            "status": False,
                            "message": "card already exist",
                            "card_result": None,
                        }
                        return response
                    except cls.DoesNotExist:
                        exp_month = card_auth.get("exp_month")
                        exp_year = card_auth.get("exp_year")

                        # current_time = timezone.now()

                        # card_date = f"{exp_year}-{exp_month}-1"
                        # _formated_date = datetime.strptime(card_date, "%Y-%m-%d").date()
                        # diff = _formated_date - current_time.date()

                        # days_to_expiration = diff.days

                        paystack_fernet_string = settings.PAYSTACK_BS64
                        fernet = Fernet(bytes(paystack_fernet_string, encoding="utf-8"))
                        encrypted_auth_code = ByteHelper.convert_byte_to_string(
                            fernet.encrypt(auth_code.encode())
                        )

                        # if days_to_expiration > 60:
                        card = cls.objects.create(
                            user=user,
                            bin=card_auth.get("bin"),
                            last_four_digits=card_auth.get("last4"),
                            exp_month=exp_month,
                            exp_year=exp_year,
                            channel=card_auth.get("channel"),
                            card_type=card_auth.get("card_type"),
                            bank=card_auth.get("bank"),
                            country_code=card_auth.get("country_code"),
                            brand=card_auth.get("brand"),
                            reusable=card_auth.get("reusable"),
                            signature=card_auth.get("signature"),
                            account_name=card_auth.get("account_name", None),
                            paystack_customer_id=customer.get("id"),
                            paystack_customer_code=customer.get("customer_code"),
                            card_email=customer.get("email"),
                            authorization_code=encrypted_auth_code,
                            payload=resp,
                        )
                        response = {
                            "status": True,
                            "message": "successful",
                            "card_result": {
                                "id": card.id,
                                "last_four_digits": card.last_four_digits,
                                "exp_month": card.exp_month,
                                "exp_year": card.exp_year,
                                "card_type": card.card_type,
                                "brand": card.brand,
                                "reusable": card.reusable,
                            },
                        }
                        return response

                        # else:
                        #     response = {
                        #         "status": False,
                        #         "message": "card declined",
                        #         "body": "card is less than 60 days to expiration",
                        #         "card_result": None,
                        #     }
                        #     return response

    @classmethod
    def top_up_via_card(cls, card_id, user, amount):

        response = cls.charge_credit_card(
            user=user,
            amount=amount,
            card_id=card_id,
            title="Fund Wallet",
            body=f"N{amount} was successfully added to your wallet",
            notification_data=None,
            transaction_type="Fund_Wallet",
            transaction_method="Card",
            notification_type="Fund_Wallet",
        )
        if response.get("status") is True:
            amount = response.get("results").get("amount")
            WalletSystem.fund_wallet(user=user, amount=Decimal(amount))
            return response
        else:
            return response

    @staticmethod
    def charge_credit_card(
        user,
        amount,
        card_id,
        title,
        body,
        notification_data,
        transaction_type,
        transaction_method,
        notification_type,
    ) -> dict:

        """charge user's card at every point the
        card is needed and the results are
        #status
        #message
        #results
        #amount
        #transaction_date
        #card_type
        if for any resaon result is false ; it wasn't successful

        Function also handles notifications with the necessary param
        """

        try:
            card_ins = CreditCard.objects.get(id=card_id)
            email = card_ins.card_email
            paystack = PayStack()

            if card_ins.reusable is True:
                paystack_fernet_string = settings.PAYSTACK_BS64
                fernet = Fernet(bytes(paystack_fernet_string, encoding="utf-8"))

                decrypted_auth_code = fernet.decrypt(
                    ByteHelper.convert_string_to_byte(card_ins.authorization_code)
                ).decode()

                charge_card = paystack.charge_reusable_card(
                    email=email, amount=str(amount), auth_code=decrypted_auth_code
                )

                status = charge_card.get("status")
                if status is True:
                    data = charge_card.get("data")
                    gateway_res = data.get("gateway_response")

                    if gateway_res == "Approved":
                        transaction_date = data["transaction_date"]
                        reference = data["reference"]

                        card_tranx = CreditCardTransaction.objects.create(
                            user=user,
                            card=card_ins,
                            amount=float(amount),
                            transaction_date=transaction_date,
                            reference=reference,
                            payload=charge_card,
                        )
                        Transaction.objects.create(
                            card_tranx=card_tranx,
                            user=user,
                            amount=amount,
                            transaction_type=transaction_type,
                            transaction_method=transaction_method,
                            status="SUCCESS",
                            payload=charge_card,
                        )
                        RawPayStack.objects.create(data=charge_card)

                        # notifications
                        token = user.firebase_key
                        Notification.create_in_app_notifications_along_firbase(
                            user_token=str(token),
                            title=title,
                            body=body,
                            user=user,
                            notification_type=notification_type,
                            case_value=None,
                            data=notification_data,
                        )

                        result = {
                            "status": True,
                            "message": "transaction was successful",
                            "results": {
                                "amount": card_tranx.amount,
                                "transaction_date": transaction_date,
                                "card_type": card_ins.card_type,
                            },
                        }

                        return result
                    else:
                        result = {
                            "status": False,
                            "message": "",
                        }
                        return result
                else:

                    result = {
                        "status": False,
                        "message": charge_card.get("message", None),
                    }
                    return result
            else:
                result = {
                    "status": False,
                    "message": "card is not reusable",
                }
                return result

        except CreditCard.DoesNotExist:
            result = {
                "status": False,
                "message": "invalid card",
            }
            return result




{'data': {'event': 'successful', 'transactionRef': {'reference': '', 'trans': '', 'status': 'success', 'message': 'Approved', 'transaction': '2157573336', 'trxref': '', 'redirecturl': 'https://ignite.originate.ng/paystack/callback/slug/libertyassured?trxref=T882099696265015&reference='}}, 'status': 'success', 'transactionRef': {'reference': '', 'trans': '2157573336', 'status': 'success', 'message': 'Approved', 'transaction': '2157573336', 'trxref': '', 'redirecturl': 'https://ignite.originate.ng/paystack/callback/slug/libertyassured?trxref=T882099696265015&reference=T882099696265015'}}

 {
   "event":"charge.success",
   "data":{
      "id":2730386359,
      "domain":"test",
      "status":"success",
      "reference":"SAVINGS-CR-070959a6-dc78-415e-85ab-b3bbc1e04dcb",
      "amount":100000,
      "message":"None",
      "gateway_response":"Successful",
      "paid_at":"2023-04-17T17:09:35.000Z",
      "created_at":"2023-04-17T17:05:44.000Z",
      "channel":"card",
      "currency":"NGN",
      "ip_address":"**************",
      "metadata":{
         "display_name":"Savings Credit",
         "variable_name":"Credit Transaction",
         "value":"<EMAIL> credited savings with 1000.0",
         "unique_name":"LIBERTYSAVINGS"
      },
      "fees_breakdown":"None",
      "log":"None",
      "fees":1500,
      "fees_split":"None",
      "authorization":{
         "authorization_code":"AUTH_vp31zu1s88",
         "bin":"408408",
         "last4":"4081",
         "exp_month":"12",
         "exp_year":"2030",
         "channel":"card",
         "card_type":"visa ",
         "bank":"TEST BANK",
         "country_code":"NG",
         "brand":"visa",
         "reusable":true,
         "signature":"SIG_KxhRL8S6MFLpx7lhDstk",
         "account_name":"None",
         "receiver_bank_account_number":"None",
         "receiver_bank":"None"
      },
      "customer":{
         "id":*********,
         "first_name":"None",
         "last_name":"None",
         "email":"<EMAIL>",
         "customer_code":"CUS_0ouagxcsyzt0k4z",
         "phone":"None",
         "metadata":"None",
         "risk_action":"default",
         "international_format_phone":"None"
      },
      "plan":{
         
      },
      "subaccount":{
         
      },
      "split":{
         
      },
      "order_id":"None",
      "paidAt":"2023-04-17T17:09:35.000Z",
      "requested_amount":100000,
      "pos_transaction_data":"None",
      "source":{
         "type":"api",
         "source":"merchant_api",
         "entry_point":"transaction_initialize",
         "identifier":"None"
      }
   }
}


#CHARGE AUTHORIZATION_CODE RESPONSE
{   
    'status': True,
    'message': 'Charge attempted',
    'data': {
        'amount': 200000, 
        'currency': 'NGN', 
        'transaction_date': '2023-04-22T11:56:20.000Z', 
        'status': 'success', 
        'reference': 'SAVINGS-CR-77187b79-8956-40a2-9b64-0a28123d88bb', 
        'domain': 'test', 
        'metadata': {
            'display_name': 'savings_credit', 
            'variable_name': 'Credit Transaction', 
            'value': '<EMAIL> credited savings with 20.0', 
            'unique_name': 'LIBERTYSAVINGS'
        }, 
        'gateway_response': 'Approved', 
        'message': None, 
        'channel': 'card', 
        'ip_address': None, 
        'log': None, 
        'fees': 3000, 
        'authorization': {
            'authorization_code': 'AUTH_vp31zu1s88', 
            'bin': '408408', 
            'last4': '4081', 
            'exp_month': '12', 
            'exp_year': '2030', 
            'channel': 'card', 
            'card_type': 'visa ', 
            'bank': 'TEST BANK', 
            'country_code': 'NG', 
            'brand': 'visa', 
            'reusable': True, 
            'signature': 'SIG_KxhRL8S6MFLpx7lhDstk', 
            'account_name': None
        }, 
        'customer': {
            'id': *********, 
            'first_name': None, 
            'last_name': None, 
            'email': '<EMAIL>', 
            'customer_code': 'CUS_0ouagxcsyzt0k4z', 
            'phone': None, 
            'metadata': None, 
            'risk_action': 'default', 
            'international_format_phone': None
        }, 
        'plan': None, 
        'id': **********
    }
}
{'status': True, 'data': {'status': True, 'message': 'Charge attempted', 'data': {'amount': 200000, 'currency': 'NGN', 'transaction_date': '2023-04-22T11:56:20.000Z', 'status': 'success', 'reference': 'SAVINGS-CR-77187b79-8956-40a2-9b64-0a28123d88bb', 'domain': 'test', 'metadata': {'display_name': 'savings_credit', 'variable_name': 'Credit Transaction', 'value': '<EMAIL> credited savings with 20.0', 'unique_name': 'LIBERTYSAVINGS'}, 'gateway_response': 'Approved', 'message': None, 'channel': 'card', 'ip_address': None, 'log': None, 'fees': 3000, 'authorization': {'authorization_code': 'AUTH_vp31zu1s88', 'bin': '408408', 'last4': '4081', 'exp_month': '12', 'exp_year': '2030', 'channel': 'card', 'card_type': 'visa ', 'bank': 'TEST BANK', 'country_code': 'NG', 'brand': 'visa', 'reusable': True, 'signature': 'SIG_KxhRL8S6MFLpx7lhDstk', 'account_name': None}, 'customer': {'id': *********, 'first_name': None, 'last_name': None, 'email': '<EMAIL>', 'customer_code': 'CUS_0ouagxcsyzt0k4z', 'phone': None, 'metadata': None, 'risk_action': 'default', 'international_format_phone': None}, 'plan': None, 'id': **********}}}



#PAYSTACK code
@staticmethod
    def create_transaction(user, data):

        card_details = data.get("data").get("authorization")
        logs = data.get("data").get("log")
        amount = (data.get("data").get("amount"))/100

        payment_reference = data.get("data").get("reference")
        device_provider = data.get("device_provider")
        # similar_payments = PayStackTransaction.objects.filter(reference = payment_reference)

        if isinstance(logs, dict):
            mobile = logs.get("mobile")
        else:
            mobile = True

        liberty_reference = Transaction.create_liberty_reference("LGLP-INW_PYSTCK")
        wallet_instance = WalletSystem.objects.filter(wallet_type="COLLECTION").last()
        if wallet_instance:
            wallet_id = wallet_instance.wallet_id
            wallet_type = wallet_instance.wallet_type
            wallet_balance = wallet_instance.available_balance
        else:
            wallet_id = None
            wallet_type = None
            wallet_balance = None
        

        charge_on_paystack = (amount/100) * 3


        if not Transaction.objects.filter(unique_reference = payment_reference).exists():

            paystack_transaction = PayStackTransaction.objects.create(
                user=user,
                amount=amount,
                reference=payment_reference,
                event=data.get(
                    "event"),
                paid_at=standard_str_to_dt(
                    data.get("data", {}).get("paid_at")),
                created_at=standard_str_to_dt(
                    data.get("data", {}).get("created_at")),
                channel=data.get(
                    "data", {}).get("channel"),
                currency=data.get(
                    "data", {}).get("currency"),
                raw_data=data.get(
                    "raw_data"),
                reason=data.get(
                    "data", {}).get("reason"),
                mobile=mobile,
                card_type=data.get("data", {}).get(
                    "authorization", {}).get("card_type"),
                bank=data.get("data", {}).get(
                    "authorization", {}).get("bank"),
                gateway_response=data.get(
                    "data", {}).get("gateway_response"),
                device_provider=device_provider,
                cash_balance = wallet_balance
            )



            user_balance_before = wallet_instance.available_balance if wallet_instance else 0.0

            user_balance_after = WalletSystem.get_balance_after(
                user = user,
                balance_before=user_balance_before,
                total_amount=amount - charge_on_paystack,
                is_credit=True
            )

            transaction_instance = Transaction.objects.create(
                user = user,
                wallet_id = wallet_id,
                wallet_type = wallet_type,
                transaction_type = "FUND_PAYSTACK",
                amount = amount,
                liberty_commission = charge_on_paystack,
                balance_before = user_balance_before,
                balance_after = user_balance_after,
                total_amount_received = amount,
                provider_status = paystack_transaction.event,
                liberty_reference = liberty_reference,
                unique_reference = payment_reference,
                transaction_mode = "FUND_PAYSTACK_ONLINE",
                payload = str(data)
            )


            if paystack_transaction.event == "charge.success" and "Approved" in paystack_transaction.gateway_response or paystack_transaction.gateway_response == "Payment successful" or "authorized" in paystack_transaction.gateway_response:

                verify_transaction = paystack_verify_funding_transaction(reference=payment_reference, amount=amount)


                if wallet_instance and verify_transaction:
                    # SETTLE MONEY

                    PayStackTransaction.settle_money_function(
                        user=user,
                        amount=amount,
                        wallet_type=wallet_type,
                        transaction_instance_id=transaction_instance.transaction_id,
                        charge_on_paystack=charge_on_paystack
                    )

                    transaction_instance.status = "SUCCESSFUL"
                    transaction_instance.save()

##########################################################################################
                    card_sender_name = card_details.get("account_name", "")

                    # SEND OUT APP NOTIFICATION
                    receiver_not_token=user.firebase_key
                    receiver_not_title="Payment Received"
                    receiver_not_body=f"You have recieved a CREDIT of N{amount} from {card_sender_name}"
                    receiver_not_data={"amount_sent": f"{amount}", "available_balance": f"{wallet_instance.available_balance}"}

                    send_out_notification = cloud_messaging.send_broadcast(
                        token=receiver_not_token,
                        title=receiver_not_title,
                        body=receiver_not_body,
                        data=receiver_not_data
                    )

                else:
                    pass

            else:
                pass

        else:
            transaction_instance = Transaction.objects.filter(unique_reference = payment_reference).last()
            paystack_transaction = PayStackTransaction.objects.filter(reference=payment_reference).last()
            debit_credit = DebitCreditRecordOnAccount.objects.filter(transaction_instance_id=transaction_instance.transaction_id).last()


            if transaction_instance.status in ["SUCCESSFUL", "FAILED"] or debit_credit is not None:
                pass
            else:
                if paystack_transaction.event == "charge.success" and "Approved" in paystack_transaction.gateway_response or paystack_transaction.gateway_response == "Payment successful" or "authorized" in paystack_transaction.gateway_response:

                    verify_transaction = paystack_verify_funding_transaction(reference=payment_reference, amount=amount)


                    if wallet_instance and verify_transaction:
                        # SETTLE MONEY

                        PayStackTransaction.settle_money_function(
                            user=user,
                            amount=amount,
                            wallet_type=wallet_type,
                            transaction_instance_id=transaction_instance.transaction_id,
                            charge_on_paystack=charge_on_paystack
                        )

                        transaction_instance.status = "SUCCESSFUL"
                        transaction_instance.save()

    ##########################################################################################
                        card_sender_name = card_details.get("account_name", "")

                        # SEND OUT APP NOTIFICATION
                        receiver_not_token=user.firebase_key
                        receiver_not_title="Payment Received"
                        receiver_not_body=f"You have recieved a CREDIT of N{amount} from {card_sender_name}"
                        receiver_not_data={"amount_sent": f"{amount}", "available_balance": f"{wallet_instance.available_balance}"}

                        send_out_notification = cloud_messaging.send_broadcast(
                            token=receiver_not_token,
                            title=receiver_not_title,
                            body=receiver_not_body,
                            data=receiver_not_data
                        )

                    else:
                        pass

                else:
                    pass



        return True





NEWWWWWWW
charge.success from webhook URL

{
    "event": "charge.success",
    "data": {
        "id": **********,
        "domain": "test",
        "status": "success",
        "reference": "SAVINGS-CR-52305325-be8b-4d0a-972d-7569cc872fe0",
        "amount": ********,
        "message": None,
        "gateway_response": "Successful",
        "paid_at": "2023-05-23T12:23:17.000Z",
        "created_at": "2023-05-23T12:22:46.000Z",
        "channel": "card",
        "currency": "NGN",
        "ip_address": "**************",
        "metadata": {
            "display_name": "savings_credit",
            "variable_name": "Credit Transaction",
            "value": "<EMAIL> credited savings with 210000.0",
            "unique_name": "LIBERTYSAVINGS",
        },
        "fees_breakdown": None,
        "log": None,
        "fees": 200000,
        "fees_split": None,
        "authorization": {
            "authorization_code": "AUTH_6em9ndco60",
            "bin": "408408",
            "last4": "4081",
            "exp_month": "12",
            "exp_year": "2030",
            "channel": "card",
            "card_type": "visa ",
            "bank": "TEST BANK",
            "country_code": "NG",
            "brand": "visa",
            "reusable": True,
            "signature": "SIG_KxhRL8S6MFLpx7lhDstk",
            "account_name": None,
            "receiver_bank_account_number": None,
            "receiver_bank": None,
        },
        "customer": {
            "id": *********,
            "first_name": None,
            "last_name": None,
            "email": "<EMAIL>",
            "customer_code": "CUS_0ouagxcsyzt0k4z",
            "phone": None,
            "metadata": None,
            "risk_action": "default",
            "international_format_phone": None,
        },
        "plan": {},
        "subaccount": {},
        "split": {},
        "order_id": None,
        "paidAt": "2023-05-23T12:23:17.000Z",
        "requested_amount": ********,
        "pos_transaction_data": None,
        "source": {
            "type": "api",
            "source": "merchant_api",
            "entry_point": "transaction_initialize",
            "identifier": None,
        },
    },
}


NEWWWWWWWWW NEWWWWWWWWW
Responses from Verify Transaction

FAILED TRANSACTION
{
    "status": True,
    "message": "Verification successful",
    "data": {
        "id": **********,
        "domain": "test",
        "status": "failed",
        "reference": "SAVINGS-CR-37abb2aa-70e3-4102-bbfd-33f1698198b9",
        "receipt_number": None,
        "amount": ********,
        "message": None,
        "gateway_response": "Declined",
        "paid_at": None,
        "created_at": "2023-05-30T17:12:27.000Z",
        "channel": "card",
        "currency": "NGN",
        "ip_address": "*************",
        "metadata": {
            "display_name": "savings_credit",
            "variable_name": "Credit Transaction",
            "value": "<EMAIL> credited savings with 210000.0",
            "unique_name": "LIBERTYSAVINGS",
        },
        "log": {
            "start_time": **********,
            "time_spent": 6,
            "attempts": 1,
            "errors": 1,
            "success": False,
            "mobile": False,
            "input": [],
            "history": [
                {"type": "action", "message": "Attempted to pay with card", "time": 5},
                {"type": "error", "message": "Error: Declined", "time": 6},
            ],
        },
        "fees": None,
        "fees_split": None,
        "authorization": {
            "authorization_code": "AUTH_knj5vyi1c2",
            "bin": "408408",
            "last4": "5408",
            "exp_month": "12",
            "exp_year": "2030",
            "channel": "card",
            "card_type": "visa ",
            "bank": "TEST BANK",
            "country_code": "NG",
            "brand": "visa",
            "reusable": True,
            "signature": "SIG_zlvnm1cbJOWljiqDXdU9",
            "account_name": None,
            "receiver_bank_account_number": None,
            "receiver_bank": None,
        },
        "customer": {
            "id": *********,
            "first_name": None,
            "last_name": None,
            "email": "<EMAIL>",
            "customer_code": "CUS_0ouagxcsyzt0k4z",
            "phone": None,
            "metadata": None,
            "risk_action": "default",
            "international_format_phone": None,
        },
        "plan": None,
        "split": {},
        "order_id": None,
        "paidAt": None,
        "createdAt": "2023-05-30T17:12:27.000Z",
        "requested_amount": ********,
        "pos_transaction_data": None,
        "source": None,
        "fees_breakdown": None,
        "transaction_date": "2023-05-30T17:12:27.000Z",
        "plan_object": {},
        "subaccount": {},
    },
}

SUCCESSFUL TRANSACTION
{
    "status": True,
    "message": "Verification successful",
    "data": {
        "id": **********,
        "domain": "test",
        "status": "success",
        "reference": "SAVINGS-CR-37abb2aa-70e3-4102-bbfd-33f1698198b9",
        "receipt_number": None,
        "amount": ********,
        "message": None,
        "gateway_response": "Successful",
        "paid_at": "2023-05-30T17:20:48.000Z",
        "created_at": "2023-05-30T17:12:27.000Z",
        "channel": "card",
        "currency": "NGN",
        "ip_address": "*************",
        "metadata": {
            "display_name": "savings_credit",
            "variable_name": "Credit Transaction",
            "value": "<EMAIL> credited savings with 210000.0",
            "unique_name": "LIBERTYSAVINGS",
        },
        "log": {
            "start_time": **********,
            "time_spent": 472,
            "attempts": 2,
            "errors": 1,
            "success": True,
            "mobile": False,
            "input": [],
            "history": [
                {"type": "action", "message": "Attempted to pay with card", "time": 5},
                {"type": "error", "message": "Error: Declined", "time": 6},
                {"type": "action", "message": "Attempted to pay with card", "time": 471},
                {"type": "success", "message": "Successfully paid with card", "time": 472},
            ],
        },
        "fees": 200000,
        "fees_split": None,
        "authorization": {
            "authorization_code": "AUTH_nwemy7mqak",
            "bin": "408408",
            "last4": "4081",
            "exp_month": "12",
            "exp_year": "2030",
            "channel": "card",
            "card_type": "visa ",
            "bank": "TEST BANK",
            "country_code": "NG",
            "brand": "visa",
            "reusable": True,
            "signature": "SIG_KxhRL8S6MFLpx7lhDstk",
            "account_name": None,
            "receiver_bank_account_number": None,
            "receiver_bank": None,
        },
        "customer": {
            "id": *********,
            "first_name": None,
            "last_name": None,
            "email": "<EMAIL>",
            "customer_code": "CUS_0ouagxcsyzt0k4z",
            "phone": None,
            "metadata": None,
            "risk_action": "default",
            "international_format_phone": None,
        },
        "plan": None,
        "split": {},
        "order_id": None,
        "paidAt": "2023-05-30T17:20:48.000Z",
        "createdAt": "2023-05-30T17:12:27.000Z",
        "requested_amount": ********,
        "pos_transaction_data": None,
        "source": None,
        "fees_breakdown": None,
        "transaction_date": "2023-05-30T17:12:27.000Z",
        "plan_object": {},
        "subaccount": {},
    },
}

DOJAH
{
  "metadata": {
    "deviceInfo": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:98.0) Gecko/******** Firefox/98.0",
    "ipInfo": {
      "status": "success",
      "country": "Nigeria",
      "countryCode": "NG",
      "region": "LA",
      "regionName": "Lagos",
      "city": "Lagos",
      "zip": "",
      "lat": 6.4474,
      "lon": 3.3903,
      "timezone": "Africa/Lagos",
      "isp": "MTN NIGERIA Communication limited",
      "org": "MTN Nigeria",
      "as": "AS29465 MTN NIGERIA Communication limited",
      "query": "102.89.33.XX"
    }
  },
  "data": {
    "index": {
      "data": {},
      "status": true,
      "message": "Successfully continued to the main checks."
    },
    "countries": {
      "data": {
        "country": "Nigeria"
      },
      "status": true,
      "message": "Successfully continued to the next step."
    },
    "phonenumber": {
      "data": {
        "mobile": "+"
      },
      "status": true,
      "message": "Successfully continued to the next step."
    },
    "user-data": {
      "status": true,
      "data": {
        "firstName": "JOHN",
        "lastName": "DOE",
        "dob": "1997-12-10"
      },
      "message": ""
    },
    "government-data": {
      "data": {
        "entity": {
          "nin": "12334567899",
          "firstname": "JOHN",
          "middlename": "DOJAH",
          "surname": "DOE",
          "maidenname": "",
          "telephoneno": "0809999999999",
          "state": "Imo",
          "place": "MBUTU",
          "profession": "STUDENT",
          "title": "mr",
          "height": "****",
          "email": "",
          "birthdate": "1997-12-10",
          "birthstate": "Lagos",
          "birthcountry": "nigeria",
          "centralID": "8051646",
          "documentno": "",
          "educationallevel": "",
          "employmentstatus": "",
          "othername": "",
          "pfirstname": "",
          "photo": "/9j/4AAQSkZJRgABAgAAAQABAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDB",
           "pmiddlename": "",
          "psurname": "",
          "nspokenlang": "IGBO",
          "ospokenlang": "ENGLISH",
          "religion": "christianity",
          "residence_Town": "OJO",
          "residence_lga": "Ojo",
          "residence_state": "Lagos",
          "residencestatus": "birth",
          "residence_AddressLine1": "CAMP 5 BLOCK 8 ROOM 7 NIGERIAN ARMY CANTONMENT",
          "residence_AddressLine2": "CAMP 5 BLOCK 8 ROOM 7 NIGERIAN ARMY CANTONMENT",
          "self_origin_lga": "Ojo",
          "self_origin_place": "",
          "self_origin_state": "Lagos",
          "signature": "/9j/4AAQSkZJRgABAQEAlgCWA",
          "nationality": "",
          "gender": "m",
          "trackingId": "S7Y0NYFM1000468",
          "customer": "365eb08f-6a1d-46bc-9aa0-XXXXXXXX"
        }
      },
      "message": "",
      "status": true
    }
  },
  "selfieUrl": "https://dojah-images.s3.amazonaws.com/633d5225b93aae0034ba243dface.jpeg",
  "verificationId": 29928,
  "verificationUrl": "https://app.dojah.io/verifications/bio-data/DJ-78BD664121",
  "referenceId": "DJ-78BD664121",
  "verificationType": "nin",
  "verificationValue": "12334567899",
  "userDetails": {
    "nin": "12334567899",
    "firstname": "JOHN",
    "middlename": "DOJAH",
    "surname": "DOE",
    "maidenname": "",
    "telephoneno": "08038296941",
    "state": "Imo",
    "place": "MBUTU",
    "profession": "STUDENT",
    "title": "mr",
    "height": "****",
    "email": "",
    "birthdate": "1992-12-30",
    "birthstate": "Lagos",
    "birthcountry": "nigeria",
    "centralID": "8051646",
    "documentno": "",
    "educationallevel": "",
    "employmentstatus": "",
    "othername": "",
    "pfirstname": "",
    "photo": "/9j/4AAQSkZJRgABAgAAAQaTnvQ",
    "pmiddlename": "",
    "psurname": "",
    "nspokenlang": "IGBO",
    "ospokenlang": "ENGLISH",
    "religion": "christianity",
    "residence_Town": "OJO",
    "residence_lga": "Ojo",
    "residence_state": "Lagos",
    "residencestatus": "birth",
    "residence_AddressLine1": "XXXXXXXXXXXX",
    "residence_AddressLine2": "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
    "self_origin_lga": "Ojo",
    "self_origin_place": "",
    "self_origin_state": "Lagos",
    "signature": "/9j/4AAQSkZJRgABAAAQUBAQEBAQEAAAAAAAAAAAEC,
    "gender": "m",
    "trackingId": "XXXXXXXXXXXXXX",
    "customer": "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
  },
  "idType": "nin",
  "value": "XXXXXXXXXXXXXXXX"
}

{
    metadata: {
        ipinfo: {
            status: "success",
            country: "Nigeria",
            regionName: "Lagos",
            city: "Lagos",
            district: "",
            zip: "",
            lat: 6.4474,
            lon: 3.3903,
            timezone: "Africa/Lagos",
            isp: "KKON Technologies Ltd",
            org: "Fiberone Broadband",
            as: "AS36920 KKON Technologies Ltd",
            mobile: false,
            proxy: false,
            hosting: true,
            query: "***********"
        },
        device_info: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    },
    data: {
        index: {
            data: {},
            message: "Successfully continued to the main checks.",
            status: true
        },
        countries: {
            data: {
                country: "Nigeria"
            },
            message: "Successfully continued to the next step.",
            status: true
        },
        email: {
            data: {
                email: "<EMAIL>"
            },
            status: true,
            message: "<EMAIL> validation Successful"
        },
        government-data: {
            data: {
                entity: {
                    title: "",
                    nationality: "",
                    bvn: "",
                    first_name: "",
                    middle_name: "",
                    last_name: "",
                    date_of_birth: "",
                    phone_number1: "",
                    phone_number2: "",
                    gender: "Male",
                    enrollment_bank: "",
                    enrollment_branch: "",
                    email: "",
                    lga_of_origin: "",
                    lga_of_residence: "",
                    marital_status: "",
                    nin: "",
                    name_on_card: "",
                    residential_address: "",
                    state_of_origin: "",
                    state_of_residence: "",
                    watch_listed: "",
                    level_of_account: "",
                    registration_date: "",
                    customer: "",
                    image_url: "https://dojah-kyc.s3-us-east-2.amazonaws.com/456645.jpg"
                },
                message: "",
                status: true
            }
        }
    },
    governmentData: {
        title: "",
        nationality: "",
        bvn: "",
        first_name: "",
        middle_name: "",
        last_name: "",
        date_of_birth: "",
        phone_number1: "",
        phone_number2: "",
        gender: "Male",
        enrollment_bank: "",
        enrollment_branch: "",
        email: "",
        lga_of_origin: "",
        lga_of_residence: "",
        marital_status: "",
        nin: "",
        name_on_card: "",
        residential_address: "",
        state_of_origin: "",
        state_of_residence: "",
        watch_listed: "",
        level_of_account: "",
        registration_date: "",
        customer: "",
        image_url: "https://dojah-kyc.s3-us-east-2.amazonaws.com/DANIEL_UGAH_1694177615393338.jpg"
    },
    userDetails: {
        title: "",
        nationality: "",
        bvn: "",
        first_name: "",
        middle_name: "",
        last_name: "",
        date_of_birth: "",
        phone_number1: "",
        phone_number2: "",
        gender: "Male",
        enrollment_bank: "",
        enrollment_branch: "",
        email: "",
        lga_of_origin: "",
        lga_of_residence: "",
        marital_status: "",
        nin: "",
        name_on_card: "",
        residential_address: "",
        state_of_origin: "",
        state_of_residence: "",
        watch_listed: "",
        level_of_account: "",
        registration_date: "",
        customer: "ba6e00e2-32f5-40cf-a67c-87c6ec358389",
        image_url: "https://dojah-kyc.s3-us-east-2.amazonaws.com/dgggfsdfdf.jpg"
    },
    idType: "BVN",
    value: "",
    message: "Successfully completed the verification.",
    referenceId: "DJ-3DF68028A1",
    verificationMode: "LIVENESS",
    verificationType: "BVN",
    verificationValue: "",
    verificationUrl: "https://app.dojah.io/verifications/bio-data/DJ-3DF68028A1",
    selfieUrl: "https://dojah-images.s3.amazonaws.com/65685463badbd5003fba2a80face.jpeg",
    status: true,
    aml: {
        status: false
    },
    verificationStatus: "Completed"
}