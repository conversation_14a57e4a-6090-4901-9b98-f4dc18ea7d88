import ast
from datetime import datetime
from typing import Dict

from django.contrib import admin, messages
from django.contrib.auth import get_user_model
from django.core.exceptions import PermissionDenied
from django.db.models import Q, Sum
from django.db.models.query import QuerySet
from django.conf import settings
from django.urls import reverse
from django.utils.html import format_html
from import_export import resources, fields
from import_export.admin import ImportExportModelAdmin

from accounts.agency_banking import AgencyBankingClass, agent_login, loan_agent_login
from accounts.models import ActionPermission
from admin_dashboard.tasks import User
from ajo.models import BankAccountDetails
from ajo.payment_actions import (
    fund_agent_wallet,
    fund_wallet_and_update_transaction,
    update_transaction_status,
)
from ajo.prefunding.actions import (
    AjoSaving,
    AjoUserSelector,
    PrefundingActions,
    payment_actions,
)
from ajo.selectors import AjoAgentSelector, AjoCommissionsSelector
from chestlock.models import ChestLock
from loans.enums import Loan<PERSON><PERSON>ge<PERSON>arration, LoanStatus, VerificationStage
from payment.checks import VerifyTransactionsOnAgency
from payment.services import TransactionService
from payment.tasks import settle_transfer_transaction
from savings.admin import set_readonly_fields

from .model_choices import PlanType, Status, TransactionFormType, WalletTypes
from .models import *
from .payment_actions import (
    Discrepancies,
    pay_withdrawal_and_handle_response,
    reverse_debit,
)
from .utils import Utility

########################################################################################################
# RESOURCES


class TransactionResource(resources.ModelResource):
    class Meta:
        model = Transaction


class DebitCreditRecordOnAccountResource(resources.ModelResource):
    class Meta:
        model = DebitCreditRecordOnAccount


class WalletSystemResource(resources.ModelResource):
    total_credit = fields.Field(column_name="Total Credit")
    total_debit = fields.Field(column_name="Total Debit")
    offset_amount = fields.Field(column_name="Offset Amount")

    class Meta:
        model = WalletSystem

    def dehydrate_total_credit(self, obj):
        return obj.total_credit

    def dehydrate_total_debit(self, obj):
        return obj.total_debit

    def dehydrate_offset_amount(self, obj):
        return obj.offset_amount


class WebhookSettingsResource(resources.ModelResource):
    class Meta:
        model = WebhookSettings


class IPAddressResource(resources.ModelResource):
    class Meta:
        model = IPAddress


class CommissionResource(resources.ModelResource):
    class Meta:
        model = Commission


class WithdrawalAccountResource(resources.ModelResource):
    class Meta:
        model = WithdrawalAccount


class RequestDumpResource(resources.ModelResource):
    class Meta:
        model = RequestDump


class ManualTransferResource(resources.ModelResource):
    class Meta:
        model = ManualTransfer


class WalletTransactionReconciliationResource(resources.ModelResource):
    class Meta:
        model = WalletTransactionReconciliation


class TempLogOnlendingPaymentsResource(resources.ModelResource):
    class Meta:
        model = TempLogOnlendingPayments


class MakeAdminTransferResource(resources.ModelResource):
    class Meta:
        model = MakeAdminTransfer


class PayWiseWaitListResource(resources.ModelResource):
    class Meta:
        model = PayWiseWaitList


###########################################################################################################
# RESOURCE ADMINS


class TransactionResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["user", "onboarded_user"]
    resource_class = TransactionResource
    search_fields = [
        "user__email",
        "onboarded_user__phone_number",
        "onboarded_user__alias",
        "transaction_id",
        "status",
        "transaction_form_type",
        "transaction_source",
        "transaction_destination",
        "transaction_type",
        "wallet_type",
        "unique_reference",
        "masked_pan_of_card",
        "quotation_id",
        "plan_type",
        "description",
    ]
    # readonly_fields = set_readonly_fields(
    #     "amount",
    #     "wallet_balance_before",
    #     "wallet_balance_after",
    #     "plan_balance_before",
    #     "plan_balance_after",
    # )

    list_filter = (
        "wallet_type",
        "plan_type",
        "status",
        "transaction_form_type",
        "transaction_type",
        ("date_created", admin.DateFieldListFilter),
        "transaction_source",
        "transaction_destination",
        "marked_for_excess_transfer",
    )

    date_hierarchy = "date_created"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def fund_plans_from_manual_cleanup(self, request, queryset: QuerySet[Transaction]):
        response: Dict[str, str] = {}

        for query_instance in queryset:
            response_key = query_instance.user.email

            if (
                query_instance.transaction_form_type
                != TransactionFormType.MANUAL_CLEANUP_WALLET_TOPUP
            ):
                response[response_key] = (
                    f"{query_instance.id} is not a manual clean up transaction"
                )
                continue

            if not query_instance.quotation_id:
                response[response_key] = (
                    f"{query_instance.id} does not have a quotation ID"
                )
                continue

            if Transaction.objects.filter(
                unique_reference=f"{query_instance.transaction_id}_resolution"
            ).exists():
                response[response_key] = (
                    f"{query_instance.id} has been resolved already"
                )
                continue

            try:
                amount = query_instance.amount
                ajo_savings = AjoSaving.objects.get(
                    quotation_id=query_instance.quotation_id
                )

                ajo_user = ajo_savings.ajo_user
                ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
                wallet = ajo_user_selector.get_ajo_user_wallet()

                if amount > wallet.available_balance:
                    raise Exception(f"{amount} is not available in the ajo user wallet")

                if (
                    amount + ajo_user_selector.get_amount_saved_in_active_savings()
                ) > wallet.available_balance:
                    raise Exception(f"{amount} would affect already active savings")

                payment_actions.fund_ajo_savings_position_without_funding_wallet(
                    user=ajo_savings.user,
                    ajo_user=ajo_savings.ajo_user,
                    ajo_savings=ajo_savings,
                    amount=query_instance.amount,
                    unique_reference=f"{query_instance.transaction_id}_resolution",
                    description="funding plan issue resolution",
                )

                response[response_key] = (
                    f"{query_instance.amount} funded into {ajo_savings.name} plan"
                )
            except Exception as err:
                response[response_key] = f"error encountered: {err}"
                continue

        self.message_user(request, str(response))

    def debit_transaction_due_to_discrepancy(
        self, request, queryset: QuerySet[Transaction]
    ):
        response: Dict[str, str] = {}

        for query_instance in queryset:
            response_key = query_instance.user.email

            valid_form_types = [
                TransactionFormType.WALLET_DEPOSIT,
                TransactionFormType.AJO_LOAN_DISBURSEMENT,
            ]
            if query_instance.transaction_form_type not in valid_form_types:
                response[response_key] = (
                    f"{query_instance.id} is not in the valid lists: {valid_form_types}"
                )
                continue

            if query_instance.unique_reference is not None:
                unique_reference = f"{query_instance.unique_reference}_discrepancy"
            else:
                unique_reference = f"{query_instance.transaction_id}_discrepancy"

            if Transaction.objects.filter(unique_reference=unique_reference).exists():
                response[response_key] = (
                    f"{query_instance.id} has already been deducted for a discrepancy"
                )
                continue

            try:
                wallet = WalletSystem.get_or_create_wallet(
                    user=query_instance.user,
                    ajo_user=query_instance.onboarded_user,
                    wallet_type=query_instance.wallet_type,
                )

                Discrepancies.debit_double_funding_transaction(
                    user=query_instance.user,
                    amount=query_instance.amount,
                    wallet=wallet,
                    unique_reference=(
                        query_instance.unique_reference
                        if query_instance.unique_reference
                        else query_instance.transaction_id
                    ),
                )

                response[response_key] = "debit successful"
            except Exception as err:
                response[response_key] = f"error encountered: {err}"

        self.message_user(request, str(response))

    def settle_funding_from_liberty_transaction(
        self, request, queryset: QuerySet[Transaction]
    ):
        response: Dict[str, str] = {}

        for query_instance in queryset:
            user_email = query_instance.user.email

            if query_instance.transaction_form_type not in [
                TransactionFormType.FUND_FROM_LIBERTY_PAY,
                TransactionFormType.BANK_DEPOSIT,
            ]:
                response[user_email] = "this is not valid transaction form type"
                continue

            if query_instance.status != Status.PENDING:
                response[user_email] = "this is not a pending transaction"
                continue

            if query_instance.wallet_type != WalletTypes.AJO_AGENT:
                response[user_email] = "this is not an ajo agent wallet settlement"
                continue

            try:
                payment_actions.resolve_pending_funding_agent_from_liberty_transaction(
                    transaction_instance=query_instance
                )
                response[user_email] = (
                    "transaction settled in the agent's wallet successfully"
                )

            except ValueError as err:
                response[user_email] = str(err)

        self.message_user(request, str(response))

    def reverse_failed_external_transfer(
        self, request, queryset: QuerySet[Transaction]
    ):
        response: Dict[str, str] = {}

        for query_instance in queryset:
            if (
                query_instance.transaction_form_type
                != TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT
            ):
                continue

            if query_instance.status != Status.PENDING:
                continue

            response_key = f"{query_instance.user.email}-{query_instance.onboarded_user.phone_number}"

            try:
                payment_actions.perform_reversal_for_failed_external_transfers(
                    transaction=query_instance
                )
                response[response_key] = (
                    "transaction reversed into the spend wallet successfully."
                )

            except Exception as err:
                response[response_key] = str(err)

        self.message_user(request, str(response))

    def reverse_failed_ajo_agent_personal_rosca_withdrawals(
        self, request, queryset: QuerySet[Transaction]
    ):
        response: Dict[str, str] = {}

        for query_instance in queryset:
            response_key = f"{query_instance.user.email}"

            if query_instance.status != Status.FAILED:
                response[response_key] = (
                    f"{query_instance.id} is not a failed transaction"
                )
                continue

            elif (
                query_instance.transaction_form_type
                != TransactionFormType.WALLET_WITHDRAWAL
            ):
                response[response_key] = (
                    f"{query_instance.id} is not a wallet withdrawal transaction"
                )
                continue

            elif Transaction.objects.filter(
                unique_reference=f"{query_instance.transaction_id}_reversal"
            ).exists():
                response[response_key] = (
                    f"{query_instance.id} has already been reversed"
                )
                continue

            elif query_instance.wallet_type not in [
                WalletTypes.AJO_AGENT,
                WalletTypes.ROSCA_PERSONAL,
            ]:
                response[response_key] = (
                    f"{query_instance.id} is not an agent or rosca personal withdrawal"
                )
                continue

            try:
                payment_actions.reverse_withdrawal(
                    amount=query_instance.amount,
                    user_id=query_instance.user.id,
                    failed_transaction_id=query_instance.transaction_id,
                    wallet_type=query_instance.wallet_type,
                    request_data=query_instance.request_data,
                )
                response[response_key] = (
                    f"{query_instance.id} transaction reversed into your wallet successfully"
                )

            except Exception as err:
                response[response_key] = str(err)

        self.message_user(request, str(response))

    def reverse_personal_ajo_withdrawal(self, request, queryset: QuerySet[Transaction]):
        response: Dict[str, str] = {}

        for query_instance in queryset:
            response_key = f"{query_instance.user.email}"

            if query_instance.status != Status.FAILED:
                response[response_key] = (
                    f"{query_instance.id} is not a failed transaction"
                )
                continue

            elif (
                query_instance.transaction_form_type
                != TransactionFormType.WALLET_WITHDRAWAL
            ):
                response[response_key] = (
                    f"{query_instance.id} is not a wallet withdrawal transaction"
                )
                continue

            elif Transaction.objects.filter(
                unique_reference=f"{query_instance.transaction_id}_reversal"
            ).exists():
                response[response_key] = (
                    f"{query_instance.id} has already been reversed"
                )
                continue

            elif query_instance.wallet_type != WalletTypes.AJO_PERSONAL:
                response[response_key] = (
                    f"{query_instance.id} is not a personal ajo withdrawal"
                )
                continue

            try:
                payment_actions.reverse_personal_ajo_savings_debit(
                    user_id=query_instance.user.id,
                    amount=query_instance.amount,
                    failed_transaction_id=query_instance.transaction_id,
                    plan_type=PlanType.AJO,
                    request_data=query_instance.request_data,
                    quotation_id=query_instance.quotation_id,
                )
                response[response_key] = (
                    f"{query_instance.id} transaction reversed into ajo plan successfully"
                )

            except Exception as err:
                response[response_key] = str(err)
                continue

        self.message_user(request, str(response))

    def reverse_savings(self, request, queryset: QuerySet[Transaction]):
        response: Dict[str, str] = {}

        for query_instance in queryset:
            response_key = f"{query_instance.user.email}"

            if (
                query_instance.transaction_form_type
                != TransactionFormType.WALLET_WITHDRAWAL
            ):
                response[response_key] = (
                    f"{query_instance.id} is not a wallet withdrawal transaction"
                )
                continue

            if query_instance.status == Status.SUCCESS:
                response[response_key] = (
                    f"{query_instance.id} is not a failed or pending transaction"
                )
                continue

            if query_instance.plan_type not in [
                PlanType.CHESTLOCK,
                PlanType.QUICKSAVINGS,
                PlanType.HALAL,
            ]:
                response[response_key] = f"{query_instance.id} not a valid plan type"
                continue

            if Transaction.objects.filter(
                unique_reference=f"{query_instance.transaction_id}_reversal",
            ).exists():
                response[response_key] = (
                    f"{query_instance.id} has already been reversed"
                )
                continue

            try:
                plan = Utility.get_plan_instance(
                    quotation_id=query_instance.quotation_id,
                    user=query_instance.user,
                )

                reverse_debit(
                    plan_id=plan.id,
                    user_id=query_instance.user.id,
                    amount=query_instance.amount,
                    plan_type=query_instance.plan_type,
                    request_data=query_instance.request_data,
                    transaction_reference=str(query_instance.transaction_id),
                )

                if query_instance.status == Status.PENDING:
                    query_instance.status = Status.FAILED
                    query_instance.save()

                response[response_key] = (
                    f"{query_instance.id} has been reversed successfully"
                )

            except Exception as err:
                response[response_key] = f"error encountered: {str(err)}"
                continue

        self.message_user(request, str(response))

    def refund_spend_on_double_charge_on_disbursement(
        self, request, queryset: QuerySet[Transaction]
    ):
        response: Dict[str, str] = {}

        for query_instance in queryset:
            response_key = query_instance.user.email

            if (
                query_instance.transaction_form_type
                != TransactionFormType.AJO_LOAN_ESCROW_HOLDING
            ):
                response[response_key] = (
                    f"{query_instance.id} is not a valid charge spend on close savings transaction"
                )
                continue

            if not query_instance.quotation_id:
                response[response_key] = (
                    f"{query_instance.id} does not have a quotation ID"
                )
                continue

            if Transaction.objects.filter(
                unique_reference=f"{query_instance.transaction_id}_resolution"
            ).exists():
                response[response_key] = (
                    f"{query_instance.id} has been resolved already"
                )
                continue

            try:

                amount = query_instance.amount
                ajo_savings = AjoSaving.objects.get(
                    quotation_id=query_instance.quotation_id
                )
                ajo_user = ajo_savings.ajo_user

                ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
                get_escrow_wallet = ajo_user_selector.get_ajo_escrow_wallet()

                _available_balance = get_escrow_wallet.available_balance
                if amount > _available_balance:
                    raise Exception(f"{amount} is not available in the escrow wallet")

                # after deduction balance after
                balance_after = _available_balance - amount
                if balance_after < amount:
                    raise Exception(
                        f"escrow holding must have at least {amount} after deduction"
                    )

                payment_actions.charge_escrow_fund_spend_on_double_charge_loan_disbursement(
                    ajo_user=ajo_savings.ajo_user,
                    quotation_id=ajo_savings.quotation_id,
                    amount=query_instance.amount,
                    escrow_wallet=get_escrow_wallet,
                    unique_reference=f"{query_instance.transaction_id}_resolution",
                )

                response[response_key] = (
                    f"{query_instance.amount} funded into spend on double spend wallet charge"
                )
            except Exception as err:
                response[response_key] = f"error encountered: {err}"
                continue
        self.message_user(request, str(response))

    def move_funds_from_disbursement_wallet_to_spend(
        self, request, queryset: QuerySet[Transaction]
    ):
        response: Dict[str, str] = {}
        from loans.enums import LoanStatus
        from loans.models import AjoLoan

        for query_instance in queryset:
            response_key = query_instance.user.email
            ajo_user = query_instance.onboarded_user

            loan_qs = AjoLoan.objects.filter(
                borrower=ajo_user, status=LoanStatus.OPEN_TO_SUPERVISOR
            )

            if not loan_qs.exists():
                response[response_key] = (
                    f"user does not have a loan that is opened to supervisor at the moment"
                )
                continue

            if (
                query_instance.transaction_form_type
                != TransactionFormType.AJO_LOAN_DISBURSEMENT
            ):
                response[response_key] = (
                    f"{query_instance.id} is not a valid loan disbursement transaction type."
                )
                continue

            if not query_instance.quotation_id:
                response[response_key] = (
                    f"{query_instance.id} does not have a quotation ID"
                )
                continue

            if not ajo_user:
                response[response_key] = f"{query_instance.id} does not have ajo user"
                continue

            if Transaction.objects.filter(
                unique_reference=f"{query_instance.transaction_id}_resolve to spend"
            ).exists():
                response[response_key] = (
                    f"{query_instance.id} has been resolved already"
                )
                continue

            try:

                amount = query_instance.amount

                ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
                loan_disbursement_wallet = (
                    ajo_user_selector.get_loan_disbursement_wallet()
                )

                _available_balance = loan_disbursement_wallet.available_balance
                if amount > _available_balance:
                    raise Exception(
                        f"{amount} is not available in the disbursement wallet"
                    )

                loan_instance = loan_qs.last()
                quotation_id = loan_instance.eligibility.saving.quotation_id
                payment_actions.charge_disbursement_Wallet_and_move_funds_to_spend(
                    ajo_user=ajo_user,
                    loan_instance=loan_instance,
                    quotation_id=quotation_id,
                    amount=query_instance.amount,
                    disbursement_wallet=loan_disbursement_wallet,
                    unique_reference=f"{query_instance.transaction_id}_resolve to spend",
                )

                response[response_key] = (
                    f"{query_instance.amount} funded into spend for withdrawal"
                )
            except Exception as err:
                response[response_key] = f"error encountered: {err}"
                continue
        self.message_user(request, str(response))

    def revert_bad_reversals(self, request, queryset: QuerySet[Transaction]):
        from datetime import datetime

        yesterday_9 = datetime(2023, 3, 3, 21)
        # date_created__gte = yesterday_9

        # all_trans = Transaction.objects.filter(
        #     transaction_form_type=TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT,
        #     status=Status.FAILED,
        # )

        for trans in queryset:
            if (
                trans.transaction_form_type
                != TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT
                and trans.status != Status.FAILED
            ):
                continue

            onboarded_user = trans.onboarded_user

            if not DebitCreditRecordOnAccount.objects.filter(
                transaction_id=trans.transaction_id
            ).first():
                trans_desc = trans.description
                reversal_desc = f"REVERSAL- {trans_desc}"
                get_reversal_trans = Transaction.objects.filter(
                    description=reversal_desc,
                    transaction_form_type=TransactionFormType.REVERSAL_EXTERNAL_TRANSFER,
                    status=Status.SUCCESS,
                    date_created__gte=yesterday_9,
                    onboarded_user=onboarded_user,
                )

                amount_to_debit = 0
                wallet_to_debit = None
                for rev_trans in get_reversal_trans:

                    rev_trans.status = Status.FAILED
                    rev_trans.save()

                    debit_cred_record = DebitCreditRecordOnAccount.objects.filter(
                        transaction_id=rev_trans.transaction_id
                    ).first()
                    if debit_cred_record:
                        debitable_amount = debit_cred_record.amount
                        amount_to_debit += debitable_amount

                        if wallet_to_debit is None:
                            wallet_to_debit = debit_cred_record.wallet

                        debit_cred_record.delete()

                if amount_to_debit > 0 and wallet_to_debit:
                    wallet_to_debit.available_balance -= amount_to_debit
                    wallet_to_debit.save()

                # get_reversal_trans = Transaction.objects.filter(description=reversal_desc, transaction_form_type=TransactionFormType.REVERSAL_EXTERNAL_TRANSFER, status=Status.SUCCESS, date_created__gte=yesterday_9).first()

                # if get_reversal_trans:
                #     get_reversal_trans.status = Status.FAILED
                #     get_reversal_trans.save()

                #     debit_cred_record = DebitCreditRecordOnAccount.objects.filter(transaction_id=get_reversal_trans.transaction_id).first()

                #     if debit_cred_record:
                #         debit_cred_record.delete()

                #         wallet = debit_cred_record.wallet
                #         wallet.available_balance -= get_reversal_trans.amount
                #         wallet.save()

        self.message_user(request, str("done"))

    def revert_bad_disbursement_reversals(self, request, queryset: QuerySet):
        for query in queryset:
            if query.transaction_form_type != TransactionFormType.REVERSAL:
                continue
            transaction_payload = query.payload
            payload_json = ast.literal_eval(transaction_payload)
            customer_reference = (
                payload_json.get("data").get("data").get("customer_reference")
            )

            # print(":::::::::::::::CUSTOMER REF::::::::::::::::::::::::::::")
            # print(customer_reference)
            # print("::::::::::::::::::CUSTOMER REF:::::::::::::::::::::::::")

            # Fetch actual disbursement transaction
            disbursement_wallet_debit = Transaction.objects.filter(
                transaction_form_type=TransactionFormType.DISBURSE_TO_EXTERNAL_ACCOUNT,
                wallet_type=WalletTypes.LOAN_DISBURSEMENT,
                transaction_id=customer_reference,
            ).last()

            if disbursement_wallet_debit is None:
                continue
            elif disbursement_wallet_debit.user is query.user:
                continue
            else:
                pass

            # print(":::::::::::::::disbursement_wallet_debit::::::::::::::::::::::::::::")
            # print(disbursement_wallet_debit)
            # print("::::::::::::::::::disbursement_wallet_debit:::::::::::::::::::::::::")

            wrong_ajo_user = query.onboarded_user
            wrong_ajo_user_selector = AjoUserSelector(ajo_user=wrong_ajo_user)
            wrong_ajo_user_disbursment_wallet = (
                wrong_ajo_user_selector.get_loan_disbursement_wallet()
            )

            # print(":::::::::::::::wrong_ajo_user_disbursment_wallet::::::::::::::::::::::::::::")
            # print(wrong_ajo_user_disbursment_wallet)
            # print("::::::::::::::::::wrong_ajo_user_disbursment_wallet:::::::::::::::::::::::::")

            # Deduct Amount From Wrongly funded disbursement Wallets
            debit_transaction = TransactionService.dynamic_deduction_from_wallet_transaction(
                user=wrong_ajo_user_disbursment_wallet.user,
                amount=query.amount,
                wallet_type=wrong_ajo_user_disbursment_wallet.wallet_type,
                quotation_id=query.quotation_id,
                ajo_user=wrong_ajo_user,
                description="Admin Manual Deduct Disbursement Wallet from bad reversal",
                transaction_form_type=TransactionFormType.MANUAL_CLEANUP_WALLET_DEDUCTION,
                status=Status.FAILED,
            )
            # print(":::::::::::debit_transaction:::::::::::::::::::::::")
            # print(debit_transaction)
            # print("::::::::::::::debit_transaction::::::::::::::::::::")

            deduct_wrong_disbursement_wallet = WalletSystem.deduct_balance(
                wallet=wrong_ajo_user_disbursment_wallet,
                amount=query.amount,
                transaction_instance=debit_transaction,
                onboarded_user=wrong_ajo_user,
            )
            # print(":::::::::::deduct_wrong_disbursement_wallet:::::::::::::::::::::::")
            # print(deduct_wrong_disbursement_wallet)
            # print("::::::::::::::deduct_wrong_disbursement_wallet::::::::::::::::::::")

            Transaction.objects.filter(id=debit_transaction.id).update(
                status=Status.SUCCESS
            )

            # Fund intended Disbursement Wallet
            intended_ajo_user = disbursement_wallet_debit.onboarded_user
            intended_ajo_user_selector = AjoUserSelector(ajo_user=intended_ajo_user)
            intended_ajo_user_disbursment_wallet = (
                intended_ajo_user_selector.get_loan_disbursement_wallet()
            )
            # print("::::::::intended_ajo_user_disbursment_wallet:::::::::::")
            # print(intended_ajo_user_disbursment_wallet)
            # print("::::::::intended_ajo_user_disbursment_wallet:::::::::::")

            credit_transaction = TransactionService.create_deposit_by_wallet_transaction(
                user=intended_ajo_user_disbursment_wallet.user,
                amount=disbursement_wallet_debit.amount,
                wallet_type=intended_ajo_user_disbursment_wallet.wallet_type,
                quotation_id=query.quotation_id,
                ajo_user=intended_ajo_user,
                description="Admin Manual Fund Disbursement Wallet from bad reversal",
                transaction_form_type=TransactionFormType.MANUAL_CLEANUP_WALLET_TOPUP,
                status=Status.FAILED,
                plan_type=PlanType.LOAN,
            )
            # print("::::::::credit_transaction:::::::::::")
            # print(credit_transaction)
            # print("::::::::credit_transaction:::::::::::")

            fund_intended_wallet = WalletSystem.fund_balance(
                wallet=intended_ajo_user_disbursment_wallet,
                amount=disbursement_wallet_debit.amount,
                transaction_instance=credit_transaction,
                onboarded_user=intended_ajo_user,
            )
            # print("::::::::fund_intended_wallet:::::::::::")
            # print(fund_intended_wallet)
            # print("::::::::fund_intended_wallet:::::::::::")
            Transaction.objects.filter(id=credit_transaction.id).update(
                status=Status.SUCCESS
            )
            # print("::::::::edn:::::::::::")
            # print("end")
            # print("::::::::end:::::::::::")
        self.message_user(request, "Wallets resolved successfully")

    def resolved_successful_agency_banking_transactions(
        self, request, queryset: QuerySet[Transaction]
    ):
        auth_emails = ["<EMAIL>"]
        # auth_emails = ["<EMAIL>"]
        resp = {}
        not_found_quotations = []

        if request.user.email not in auth_emails:
            self.message_user(request, "Unauthorized user email")
            return

        pending_chestlock_transactions = Transaction.objects.filter(
            transaction_form_type=TransactionFormType.WALLET_AUTO_CHARGE_DEPOSIT,
            wallet_type=WalletTypes.CHESTLOCK,
            status=Status.PENDING,
        )
        for transaction in pending_chestlock_transactions:
            reference = str(transaction.transaction_id)
            verification = VerifyTransactionsOnAgency.verify_bank_transfers_v2(
                reference=reference
            )

            resp[reference] = str(verification)

            if verification.get("transferred") is True:
                try:
                    chestlock_plan = ChestLock.objects.get(
                        quotation_id=transaction.quotation_id
                    )
                except ChestLock.DoesNotExist:
                    not_found_quotations.append(str(transaction.quotation_id))
                    continue

                wallet = WalletSystem.get_or_create_wallet(
                    user=chestlock_plan.user,
                    wallet_type=WalletTypes.CHESTLOCK,
                )
                debit_credit = WalletSystem.fund_balance(
                    wallet=wallet,
                    amount=transaction.amount,
                    transaction_instance=transaction,
                )

                transaction.wallet_balance_before = debit_credit.get("balance_before")
                transaction.wallet_balance_after = debit_credit.get("balance_after")
                transaction.plan_balance_before = chestlock_plan.amount_saved

                chestlock_plan.amount_saved += transaction.amount

                transaction.plan_balance_after = (
                    transaction.plan_balance_before + transaction.amount
                )
                transaction.status = Status.SUCCESS

                with django_transaction.atomic():
                    chestlock_plan.save()
                    transaction.save()

        self.message_user(
            request,
            f"Ran: {len(pending_chestlock_transactions)} With Responses: {resp}: Quotation: {not_found_quotations} | {len(not_found_quotations)}",
        )

    def refund_repayment_with_no_record(self, request, queryset: QuerySet[Transaction]):
        from loans.models import AjoLoanRepayment

        message = "Invalid selection"
        for instance in queryset:
            if instance.transaction_form_type != TransactionFormType.LOAN_REPAYMENT:
                message = f"{instance.id} is not a valid repayment record"
                continue
            # check if there's a repayment record
            if AjoLoanRepayment.objects.filter(
                repayment_ref=instance.unique_reference
            ).exists():
                message = f"{instance.id} repayment record already esixt"
                continue
            # reund to spend
            unique_reference = f"{instance.transaction_id}_repayment_refund"
            if Transaction.objects.filter(unique_reference=unique_reference).exists():
                message = f"Reversal on transaction {instance.id} already exist"
                continue

            user = instance.user
            agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()
            amount: float = instance.amount

            credit_transaction = TransactionService.create_deposit_by_wallet_transaction(
                user=user,
                amount=amount,
                wallet_type=agent_wallet.wallet_type,
                transaction_form_type=TransactionFormType.REFUND_REPAYMENT,
                unique_reference=unique_reference,
                description="Refund for repayment with no record created",
                status=Status.PENDING,
                quotation_id="",
                # ajo_user=ajo_user,
                plan_type=PlanType.AJO,
            )

            fund_agent_wallet(
                transaction=credit_transaction,
                wallet=agent_wallet,
                amount=amount,
                unique_reference=unique_reference,
            )
            message = "success"

        self.message_user(request, message)

    def loan_escrow_update_cleanup(self, request, queryset: QuerySet[Transaction]):
        from loans.helpers.repayments import process_loan_repayment_from_spend_wallet_v2
        from loans.models import AjoLoan

        for transaction in queryset:
            if (
                transaction.transaction_form_type
                == TransactionFormType.ESCROW_HOLDING_OFFSET_LOAN_REPAYMENT
            ):
                ajo_user = transaction.onboarded_user
                amount = transaction.amount
                loan = AjoLoan.objects.filter(borrower=ajo_user).last()
                process_loan = process_loan_repayment_from_spend_wallet_v2(
                    loan=loan, amount=amount
                )
                if process_loan.get("status") == True:
                    message = f"spending wallt depleted Successfully, {loan.id}"
                    self.message_user(request, message)
                else:
                    message = f"Repayment created succefully"
                    self.message_user(request, message)
            else:
                message = f"Transaction does not meet criteria"
                self.message_user(request, message)

    def refund_double_charge_ajo_loan_fee(
        self, request, queryset: QuerySet[Transaction]
    ):

        message = "Invalid action"
        for txn_instance in queryset:

            if (
                txn_instance.transaction_form_type
                != TransactionFormType.AJO_LOAN_CHARGE_FEE
            ):
                message = "Not a valid loan fee transaction"
                continue

            txn_qs = Transaction.objects.filter(
                quotation_id=txn_instance.quotation_id,
                transaction_form_type=TransactionFormType.AJO_LOAN_CHARGE_FEE,
            )
            total_amount = txn_qs.aggregate(sum_amount=Sum("amount"))["sum_amount"] or 0
            amount = txn_instance.amount
            total_refundable = total_amount - amount
            user = txn_instance.user
            agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()
            unique_reference = f"refund-{txn_instance.quotation_id}"

            refund_transaction_queryset = Transaction.objects.filter(
                transaction_form_type=TransactionFormType.REFUND_LOAN_FEE,
                quotation_id=txn_instance.quotation_id,
                onboarded_user=txn_instance.onboarded_user,
            )

            if refund_transaction_queryset.exists():
                sum_total_amount_refunded = (
                    refund_transaction_queryset.aggregate(sum_amount=Sum("amount"))[
                        "sum_amount"
                    ]
                    or 0
                )

                if sum_total_amount_refunded < total_refundable:
                    # get balance
                    balance_to_refund = total_refundable - sum_total_amount_refunded

                    credit_transaction = TransactionService.create_deposit_by_wallet_transaction(
                        user=user,
                        amount=balance_to_refund,
                        wallet_type=agent_wallet.wallet_type,
                        transaction_form_type=TransactionFormType.REFUND_LOAN_FEE,
                        # unique_reference=unique_reference,
                        description="Refund for double charge loan fee",
                        status=Status.PENDING,
                        quotation_id=txn_instance.quotation_id,
                        ajo_user=txn_instance.onboarded_user,
                        plan_type=PlanType.AJO,
                    )

                    fund_agent_wallet(
                        transaction=credit_transaction,
                        wallet=agent_wallet,
                        amount=balance_to_refund,
                        # unique_reference=unique_reference,
                    )
                    message = "success"
                else:
                    message = "Refund already exist."
                    continue
            else:

                qs_count = txn_qs.count()

                if qs_count >= 2:

                    credit_transaction = (
                        TransactionService.create_deposit_by_wallet_transaction(
                            user=user,
                            amount=total_refundable,
                            wallet_type=agent_wallet.wallet_type,
                            transaction_form_type=TransactionFormType.REFUND_LOAN_FEE,
                            unique_reference=unique_reference,
                            description="Refund for double charge loan fee",
                            status=Status.PENDING,
                            quotation_id=txn_instance.quotation_id,
                            ajo_user=txn_instance.onboarded_user,
                            plan_type=PlanType.AJO,
                        )
                    )

                    fund_agent_wallet(
                        transaction=credit_transaction,
                        wallet=agent_wallet,
                        amount=total_refundable,
                        unique_reference=unique_reference,
                    )
                    message = "success"
                else:
                    message = "There's only one occurence of the selected transaction"

        self.message_user(request, message)

    def resolve_reversed_monnify_transfers(
        self, request, queryset: QuerySet[Transaction]
    ):
        from accounts.helper_files.monify import Monnify
        from loans.models import AjoLoan, LoanStatus

        if request.user.email not in [
            "<EMAIL>",
            "<EMAIL>",
        ]:
            self.message_user(request, "You are not authorized to perform this action")
            return

        response: Dict[str, str] = {}

        for query_instance in queryset:
            if (
                query_instance.transaction_form_type
                in [
                    TransactionFormType.DISBURSE_TO_EXTERNAL_ACCOUNT,
                    TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT,
                ]
                and query_instance.transfer_provider
                == DisbursementProviderType.MONNIFY_PROVIDER
            ):
                # Check Monnify Transfer Status
                monnify_response = Monnify().get_single_transfer_status(
                    reference=str(query_instance.transaction_id),
                )
                if (
                    monnify_response.get("responseBody", {}).get("status") == "REVERSED"
                    or monnify_response.get("responseCode") == "D02"
                ):
                    pass
                else:
                    continue
            else:
                continue

            response_key = f"{query_instance.user.email}-{query_instance.onboarded_user.phone_number}"

            try:
                payment_actions.perform_reversal_for_failed_external_transfers(
                    transaction=query_instance
                )
                response[response_key] = (
                    "transaction reversed into the disbursement wallet successfully."
                )
                query_instance.verification_response = monnify_response
                query_instance.save()

            except Exception as err:
                response[response_key] = str(err)

            loan_instance = AjoLoan.objects.filter(
                loan_ref=query_instance.quotation_id,
                agent=query_instance.user,
                borrower=query_instance.onboarded_user,
            ).last()

            if loan_instance:
                loan_instance.status = LoanStatus.OPEN_TO_SUPERVISOR
                loan_instance.save()

        self.message_user(request, str(response))

    def complete_debit_transaction(self, request, queryset: QuerySet[Transaction]):
        response: Dict[str, str] = {}
        valid_transaction_types = [TransactionFormType.WALLET_WITHDRAWAL]

        for query_instance in queryset:
            response_key = query_instance.id
            if query_instance.transaction_form_type not in valid_transaction_types:
                response[response_key] = (
                    f"transaction form type not in list: {valid_transaction_types}"
                )
                continue

            if query_instance.onboarded_user:
                wallet = payment_actions.AjoUserSelector(
                    ajo_user=query_instance.onboarded_user
                ).get_any_ajo_user_wallet(wallet_type=query_instance.wallet_type)
            else:
                wallet = payment_actions.AjoAgentSelector(
                    user=query_instance.user
                ).get_agent_ajo_wallet(wallet_type=query_instance.wallet_type)
            try:
                payment_actions.debit_wallet_and_update_transaction(
                    amount=query_instance.amount,
                    wallet=wallet,
                    transaction_instance=query_instance,
                )
                response[response_key] = "transaction completed successfully."

            except Exception as err:
                response[response_key] = str(err)

        self.message_user(request, str(response))

    def resolve_external_transfer_transactions(
        self, request, queryset: QuerySet[Transaction]
    ):
        response: Dict[str, str] = {}

        for query_instance in queryset:
            if query_instance.transaction_form_type not in [
                TransactionFormType.DISBURSE_TO_EXTERNAL_ACCOUNT,
                TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT,
            ]:
                continue

            if query_instance.status != Status.PENDING:
                continue

            response_key = f"{query_instance.user.email}-{query_instance.onboarded_user.phone_number if query_instance.onboarded_user else ''}"

            try:
                settle = settle_transfer_transaction(transaction_id=query_instance.id)
                response[response_key] = settle

            except Exception as err:
                response[response_key] = str(err)

        self.message_user(request, str(response))

    def quick_savings_reversal(self, request, queryset: QuerySet[Transaction]):
        message = "Invalid action"

        for instance in queryset:

            if instance.wallet_type != WalletTypes.QUICKSAVINGS:
                message = f"wallet type: {instance.wallet_type} not equal to {WalletTypes.QUICKSAVINGS}"
                break
            user_wallet = WalletSystem.get_or_create_wallet(
                user=instance.user,
                wallet_type=WalletTypes.QUICKSAVINGS,
                ajo_user=None,
            )

            quotation_id = instance.quotation_id
            unique_reference = f"qck-rv{quotation_id}"

            reversal_txnquery_set = Transaction.objects.filter(
                unique_reference=unique_reference
            )
            if (
                instance.status == Status.FAILED
                and instance.transaction_form_type
                == TransactionFormType.WALLET_WITHDRAWAL
            ):
                if not reversal_txnquery_set.exists():
                    credit_description = f"{instance.amount} has being reversed to {WalletTypes.QUICKSAVINGS} wallet"
                    transaction_form_type = TransactionFormType.REVERSAL
                    credit_transaction = (
                        TransactionService.create_deposit_by_wallet_transaction(
                            user=instance.user,
                            amount=instance.amount,
                            wallet_type=WalletTypes.QUICKSAVINGS,
                            transaction_form_type=transaction_form_type,
                            unique_reference=unique_reference,
                            description=credit_description,
                            status=Status.PENDING,
                            quotation_id=quotation_id,
                            plan_type=PlanType.QUICKSAVINGS,
                        )
                    )

                    fund_agent_wallet(
                        transaction=credit_transaction,
                        wallet=user_wallet,
                        amount=instance.amount,
                    )
                    message = "success"
                else:
                    message = "reversal already exist"
            else:
                message = f"Txn status: {instance.status}"

        self.message_user(request, str(message))

    def credit_ajo_agent_wallet_from_transaction(
        self, request, queryset: QuerySet[Transaction]
    ):

        admin_emails = ActionPermission.get_admin_emails()

        if request.user.email not in admin_emails:
            self.message_user(
                request,
                "You do not have permission to perform this action.",
                level=messages.ERROR,
            )

        else:

            for query_instance in queryset:

                if Transaction.objects.filter(
                    unique_reference=query_instance.transaction_id
                ).exists():
                    self.message_user(
                        request,
                        "Reversal already exists",
                        level=messages.ERROR,
                    )
                    continue

                if query_instance.transaction_type != "DEBIT":
                    self.message_user(
                        request,
                        "Invalid transaction type",
                        level=messages.ERROR,
                    )
                    continue

                amount = query_instance.amount
                user = query_instance.user
                ajo_user = query_instance.onboarded_user

                # credit agent
                agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()
                credit_description = f"This refund reverses an earlier debit of {amount} from transaction {query_instance.transaction_id}"
                credit_transaction = (
                    TransactionService.create_deposit_by_wallet_transaction(
                        user=user,
                        amount=amount,
                        wallet_type=agent_wallet.wallet_type,
                        transaction_form_type=query_instance.transaction_form_type,
                        unique_reference=query_instance.transaction_id,
                        description=credit_description,
                        status=Status.PENDING,
                        quotation_id=None,
                        ajo_user=ajo_user,
                        plan_type=PlanType.AJO,
                    )
                )

                fund_agent_wallet(
                    transaction=credit_transaction,
                    wallet=agent_wallet,
                    amount=amount,
                    unique_reference=query_instance.transaction_id,
                )
                self.message_user(request, "success")

    def resolve_reversed_vfd_transfers(self, request, queryset: QuerySet[Transaction]):
        from accounts.liberty_ussd import LibertyUSSD

        if request.user.email not in [
            "<EMAIL>",
            "<EMAIL>",
        ]:
            self.message_user(request, "You are not authorized to perform this action")
            return

        response: Dict[str, str] = {}
        exception = "No exception"

        for query_instance in queryset:
            if (
                query_instance.transaction_form_type
                == TransactionFormType.DISBURSE_TO_EXTERNAL_ACCOUNT
                and query_instance.transfer_provider
                == DisbursementProviderType.VFD_PROVIDER
            ):
                # Check VFD Transfer Status
                vfd_response = LibertyUSSD.verify_vfd_overdraft_disbursement(
                    reference=str(query_instance.transaction_id)
                )

                try:
                    if vfd_response.get("status"):
                        v_response = vfd_response.get("data")
                        j_response = v_response.get("response")
                        transfer_status = j_response.get("trans_status")

                        if transfer_status == "NOT_FOUND":
                            pass
                        else:
                            continue
                    else:
                        continue
                except Exception as e:
                    exception = "An error occurred"
                    pass
            else:
                continue

            response_key = f"{query_instance.user.email}-{query_instance.onboarded_user.phone_number}-{exception}"

            try:
                payment_actions.perform_reversal_for_failed_external_transfers(
                    transaction=query_instance
                )
                response[response_key] = (
                    "transaction reversed into the disbursement wallet successfully."
                )
                query_instance.verification_response = vfd_response
                query_instance.save()

            except Exception as err:
                response[response_key] = str(err)

        self.message_user(request, str(response))

    def commission_refund(self, request, queryset: QuerySet[Transaction]):
        admin_emails = ActionPermission.get_admin_emails()

        if request.user.email not in admin_emails:
            self.message_user(
                request,
                "You do not have permission to perform this action.",
                level=messages.ERROR,
            )

        else:

            for instance in queryset:
                quotation_id = instance.quotation_id
                transaction_form_type = TransactionFormType.COMMISSION
                amount = instance.amount
                unique_reference = f"{amount}comm-debit{quotation_id}"

                credit_transaction_unique_reference = (
                    f"{amount}comm-refund{quotation_id}"
                )
                if Transaction.objects.filter(
                    Q(unique_reference=credit_transaction_unique_reference)
                    | Q(unique_reference=unique_reference)
                ).exists():
                    message = f"commission reversal already exist"
                    continue

                if instance.wallet_type != WalletTypes.AJO_COMMISSION:
                    message = f"invalid commission transaction wallet-type {instance.wallet_type}"
                    continue
                # get user
                user = instance.user

                savings_instance = AjoSaving.objects.get(quotation_id=quotation_id)
                ajo_user = savings_instance.ajo_user

                _commissions_wallet = AjoCommissionsSelector(
                    user=user
                ).get_commissions_wallet()

                if _commissions_wallet.available_balance >= amount:

                    description = f"This debit transaction was processed due to the initial commission debited from your account, linked with the credit transaction ID: {instance.transaction_id}. The total amount debited is {amount}"
                    # debit_agent wallet
                    trans_obj = TransactionService.dynamic_deduction_from_wallet_transaction(
                        user=user,
                        amount=amount,
                        wallet_type=_commissions_wallet.wallet_type,
                        request_data=None,
                        transaction_form_type=transaction_form_type,
                        unique_reference=unique_reference,
                        quotation_id=quotation_id,
                        ajo_user=ajo_user,
                        transaction_source="WALLET",
                        # transaction_source_id=transaction_source_id,
                        description=description,
                    )

                    deduct_agent_wallet = WalletSystem().deduct_balance(
                        wallet=_commissions_wallet,
                        amount=amount,
                        transaction_instance=trans_obj,
                        unique_reference=unique_reference,
                        onboarded_user=ajo_user,
                    )

                    trans_obj.status = Status.SUCCESS
                    trans_obj.transaction_date_completed = timezone.now()
                    trans_obj.wallet_balance_before = deduct_agent_wallet[
                        "balance_before"
                    ]
                    trans_obj.wallet_balance_after = deduct_agent_wallet[
                        "balance_after"
                    ]
                    trans_obj.save()

                    ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
                    ajo_user_spend_wallet = ajo_user_selector.get_spending_wallet()

                    credit_description = f"{amount} has been transferred to your Spend Wallet due to the previously debited commission."

                    credit_transaction = (
                        TransactionService.create_deposit_by_wallet_transaction(
                            user=user,
                            amount=amount,
                            wallet_type=ajo_user_spend_wallet.wallet_type,
                            transaction_form_type=transaction_form_type,
                            unique_reference=credit_transaction_unique_reference,
                            description=credit_description,
                            status=Status.PENDING,
                            quotation_id=quotation_id,
                            ajo_user=ajo_user,
                            plan_type=PlanType.AJO,
                        )
                    )

                    fund_agent_wallet(
                        transaction=credit_transaction,
                        wallet=ajo_user_spend_wallet,
                        amount=amount,
                        unique_reference=credit_transaction_unique_reference,
                    )
                    message = "success"
                else:
                    message = f"wallet balance {_commissions_wallet.available_balance} is less than transaction amount {amount}"
                    continue

            self.message_user(request, str(message))

    def resolve_reversed_paystack_transfers(
        self, request, queryset: QuerySet[Transaction]
    ):
        from accounts.helper_files.monify import Monnify
        from loans.models import AjoLoan, LoanStatus

        if request.user.email not in [
            "<EMAIL>",
            "<EMAIL>",
        ]:
            self.message_user(request, "You are not authorized to perform this action")
            return

        response: Dict[str, str] = {}

        for query_instance in queryset:
            if (
                query_instance.transaction_form_type
                == TransactionFormType.DISBURSE_TO_EXTERNAL_ACCOUNT
                and query_instance.transfer_provider
                == DisbursementProviderType.PAYSTACK_PROVIDER
            ):
                # Check Monnify Transfer Status
                from accounts.paystack import PaystackApi

                paystack = PaystackApi()

                response = paystack.verify_transaction_status(
                    reference=str(query_instance.transaction_id),
                )

                if response.get("data", {}).get("message") == "Transfer not found":
                    pass
                else:
                    continue
            else:
                continue

            response_key = f"{query_instance.user.email}-{query_instance.onboarded_user.phone_number}"

            try:
                payment_actions.perform_reversal_for_failed_external_transfers(
                    transaction=query_instance
                )
                response[response_key] = (
                    "transaction reversed into the disbursement wallet successfully."
                )
                query_instance.verification_response = response
                query_instance.save()

            except Exception as err:
                response[response_key] = str(err)

            loan_instance = AjoLoan.objects.filter(
                loan_ref=query_instance.quotation_id,
                agent=query_instance.user,
                borrower=query_instance.onboarded_user,
            ).last()

            if loan_instance:
                loan_instance.status = LoanStatus.OPEN_TO_SUPERVISOR
                loan_instance.save()

        self.message_user(request, str(response))

    def resolve_failed_health_insurance_fee(
        self, request, queryset: QuerySet[Transaction]
    ):
        admin_emails = ActionPermission.get_admin_emails()

        if request.user.email not in admin_emails:
            self.message_user(
                request,
                "You do not have permission to perform this action.",
                level=messages.ERROR,
            )

        else:

            for instance in queryset:

                transaction_form_type = instance.transaction_form_type
                description = instance.description

                if transaction_form_type == TransactionFormType.HEALTH_PLAN_DEDUCTION:
                    buddy_phone_number = settings.LIBERTY_LIFE_BUDDY_PHONE_NUMBER
                    description = (
                        f"Health Plan Deduction: ({instance.onboarded_user.phone})"
                    )

                # elif transaction_form_type == TransactionFormType.HEALTH_INSURANCE:

                elif transaction_form_type == TransactionFormType.TRANSFER:
                    if not "Activation Fee" in description:
                        message = (
                            f"Invalid health transaction description {description}"
                        )
                        continue
                    else:
                        buddy_phone_number = settings.NEM_BUDDY_PHONE_NUMBER
                else:
                    message = (
                        f"Invalid health transaction form type {transaction_form_type}"
                    )
                    continue

                if instance.status == Status.SUCCESS:
                    message = f"transaction already completed"
                    continue

                reference = str(instance.transaction_id)
                savings_access_token = agent_login(token_not_valid=False).get("access")
                request_data = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": buddy_phone_number,
                            "amount": instance.amount,
                            "narration": description,
                            "is_beneficiary": "False",
                            "save_beneficiary": "False",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "custom_reference": reference,
                        }
                    ],
                    "transaction_pin": "",
                }

                transaction_pin = settings.AGENCY_BANKING_TRANSACTION_PIN
                send_money_to_liberty_life_buddy_acct = AgencyBankingClass.send_money_from_an_account_in_liberty_to_user_through_pay_buddy(
                    transaction_pin=transaction_pin,
                    phone_number=buddy_phone_number,
                    amount=instance.amount,
                    transaction_reference=reference,
                    access_token=savings_access_token,
                    narration=description,
                )

                if (
                    send_money_to_liberty_life_buddy_acct.get("data", {}).get("message")
                    == "success"
                ):
                    instance.status = Status.SUCCESS
                    message = "Success"

                else:

                    instance.status = Status.FAILED
                    message = "Failed"

                instance.request_data = request_data
                instance.description = description
                instance.payload = send_money_to_liberty_life_buddy_acct

                instance.save(
                    update_fields=["request_data", "payload", "status", "description"]
                )

            self.message_user(request, str(message))

    def refund_prosper_loan_deposit(self, request, queryset: QuerySet[Transaction]):
        admin_emails = ActionPermission.get_admin_emails()

        if request.user.email not in admin_emails:
            self.message_user(
                request,
                "You do not have permission to perform this action.",
                level=messages.ERROR,
            )

        else:

            for instance in queryset:
                quotation_id = instance.unique_reference
                transaction_form_type = TransactionFormType.PROSPER_LOAN_DEPOSIT
                amount = instance.amount
                unique_reference = f"{amount}prosper-db{quotation_id}"

                credit_transaction_unique_reference = (
                    f"{amount}prosper-rfnd{quotation_id}"
                )
                wallet_type = instance.wallet_type
                if Transaction.objects.filter(
                    Q(unique_reference=credit_transaction_unique_reference)
                    | Q(unique_reference=unique_reference)
                ).exists():
                    message = f"Reversal already exist"
                    continue

                if wallet_type != WalletTypes.PROSPER_LOAN_WALLET:
                    message = f"invalid wallet-type {instance.wallet_type}"
                    continue
                if (
                    instance.transaction_form_type
                    != TransactionFormType.PROSPER_LOAN_DEPOSIT
                ):
                    message = f"invalid transaction form type {instance.wallet_type}"
                    continue
                # get user
                user = instance.user

                agent_selector = AjoAgentSelector(user=user)
                prosper_loan_wallet = agent_selector.get_agent_ajo_wallet(
                    wallet_type=wallet_type
                )

                if prosper_loan_wallet.available_balance >= amount:
                    description = f"Reversal of mistakenly deposited funds—amount {amount} debited to facilitate crediting agent wallet as per request"

                    # debit_agent wallet
                    trans_obj = TransactionService.dynamic_deduction_from_wallet_transaction(
                        user=user,
                        amount=amount,
                        wallet_type=prosper_loan_wallet.wallet_type,
                        request_data=None,
                        transaction_form_type=transaction_form_type,
                        unique_reference=unique_reference,
                        quotation_id=quotation_id,
                        transaction_source="WALLET",
                        # transaction_source_id=transaction_source_id,
                        description=description,
                    )

                    deduct_agent_wallet = WalletSystem().deduct_balance(
                        wallet=prosper_loan_wallet,
                        amount=amount,
                        transaction_instance=trans_obj,
                        unique_reference=unique_reference,
                    )

                    trans_obj.status = Status.SUCCESS
                    trans_obj.transaction_date_completed = timezone.now()
                    trans_obj.wallet_balance_before = deduct_agent_wallet[
                        "balance_before"
                    ]
                    trans_obj.wallet_balance_after = deduct_agent_wallet[
                        "balance_after"
                    ]
                    trans_obj.save(
                        update_fields=[
                            "status",
                            "transaction_date_completed",
                            "wallet_balance_before",
                            "wallet_balance_after",
                        ]
                    )

                    ajo_agent_wallet = agent_selector.get_agent_ajo_wallet()
                    credit_description = f"Funds credited {amount} in accordance with the reversal transaction—restoring balance from the previous debit action."

                    credit_transaction = (
                        TransactionService.create_deposit_by_wallet_transaction(
                            user=user,
                            amount=amount,
                            wallet_type=ajo_agent_wallet.wallet_type,
                            transaction_form_type=transaction_form_type,
                            unique_reference=credit_transaction_unique_reference,
                            description=credit_description,
                            status=Status.PENDING,
                            quotation_id=quotation_id,
                        )
                    )

                    fund_agent_wallet(
                        transaction=credit_transaction,
                        wallet=ajo_agent_wallet,
                        amount=amount,
                        unique_reference=credit_transaction_unique_reference,
                    )
                    message = "success"
                else:
                    message = f"wallet balance {prosper_loan_wallet.available_balance} is less than transaction amount {amount}"
                    continue

            self.message_user(request, str(message))

    def refund_loan_repayment_deposit(self, request, queryset: QuerySet[Transaction]):
        admin_emails = ActionPermission.get_admin_emails()

        if request.user.email not in admin_emails:
            self.message_user(
                request,
                "You do not have permission to perform this action.",
                level=messages.ERROR,
            )

        else:

            for instance in queryset:
                quotation_id = instance.unique_reference
                transaction_form_type = TransactionFormType.AJO_LOAN_REPAYMENT_TOPUP
                amount = instance.amount
                unique_reference = f"{amount}rpmtf-db{quotation_id}"

                credit_transaction_unique_reference = (
                    f"{amount}rpmtf-rfnd{quotation_id}"
                )
                wallet_type = instance.wallet_type
                if Transaction.objects.filter(
                    Q(unique_reference=credit_transaction_unique_reference)
                    | Q(unique_reference=unique_reference)
                ).exists():
                    message = f"Reversal already exist credit-ref: {credit_transaction_unique_reference} unique-ref: {unique_reference}"
                    continue

                if wallet_type != WalletTypes.AJO_LOAN_REPAYMENT:
                    message = f"invalid wallet-type {instance.wallet_type}"
                    continue
                if instance.transaction_form_type != transaction_form_type:
                    message = f"invalid transaction form type {instance.wallet_type}"
                    continue
                # get user
                user = instance.user
                ajo_user = instance.onboarded_user
                ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
                ajo_user_loan_repayment_wallet = (
                    ajo_user_selector.get_any_ajo_user_wallet(wallet_type=wallet_type)
                )

                if ajo_user_loan_repayment_wallet.available_balance >= amount:
                    description = f"Charge excess loan repayment transfer {amount}"

                    # debit_agent wallet
                    trans_obj = TransactionService.dynamic_deduction_from_wallet_transaction(
                        user=user,
                        amount=amount,
                        wallet_type=ajo_user_loan_repayment_wallet.wallet_type,
                        request_data=None,
                        transaction_form_type=transaction_form_type,
                        unique_reference=unique_reference,
                        quotation_id=quotation_id,
                        transaction_source="WALLET",
                        # transaction_source_id=transaction_source_id,
                        description=description,
                    )

                    deduct_agent_wallet = WalletSystem().deduct_balance(
                        wallet=ajo_user_loan_repayment_wallet,
                        amount=amount,
                        transaction_instance=trans_obj,
                        unique_reference=unique_reference,
                    )

                    trans_obj.status = Status.SUCCESS
                    trans_obj.transaction_date_completed = timezone.now()
                    trans_obj.wallet_balance_before = deduct_agent_wallet[
                        "balance_before"
                    ]
                    trans_obj.wallet_balance_after = deduct_agent_wallet[
                        "balance_after"
                    ]
                    trans_obj.save(
                        update_fields=[
                            "status",
                            "transaction_date_completed",
                            "wallet_balance_before",
                            "wallet_balance_after",
                        ]
                    )

                    ajo_user_spend = ajo_user_selector.get_spending_wallet()
                    credit_description = (
                        f"Funds credited {amount}.Excess loan repayment transfer"
                    )

                    credit_transaction = (
                        TransactionService.create_deposit_by_wallet_transaction(
                            user=user,
                            amount=amount,
                            wallet_type=ajo_user_spend.wallet_type,
                            transaction_form_type=transaction_form_type,
                            unique_reference=credit_transaction_unique_reference,
                            description=credit_description,
                            status=Status.PENDING,
                            quotation_id=quotation_id,
                        )
                    )

                    fund_agent_wallet(
                        transaction=credit_transaction,
                        wallet=ajo_user_spend,
                        amount=amount,
                        unique_reference=credit_transaction_unique_reference,
                    )
                    message = "success"
                else:
                    message = f"wallet balance {ajo_user_loan_repayment_wallet.available_balance} is less than transaction amount {amount}"
                    continue

            self.message_user(request, str(message))

    revert_bad_disbursement_reversals.short_description = (
        "LOANS: Resolve bad supervisor disbursement reversals"
    )
    revert_bad_disbursement_reversals.allow_tags = True

    fund_plans_from_manual_cleanup.short_description = (
        "LOANS: Fund ajo plans from manual topup transaction"
    )
    fund_plans_from_manual_cleanup.allow_tags = True

    debit_transaction_due_to_discrepancy.short_description = (
        "PAYMENT: Dynamically debit transaction/wallet due to discrepancy"
    )
    debit_transaction_due_to_discrepancy.allow_tags = True

    settle_funding_from_liberty_transaction.short_description = (
        "AJO: Settle pending funding transactions into agent wallet"
    )
    settle_funding_from_liberty_transaction.allow_tags = True

    reverse_failed_external_transfer.short_description = (
        "PAYMENT: Reverse failed external transactions"
    )
    reverse_failed_external_transfer.allow_tags = True

    reverse_failed_ajo_agent_personal_rosca_withdrawals.short_description = (
        "AJO: Reverse failed ajo agent, personal rosca wallet withdrawals"
    )
    reverse_failed_ajo_agent_personal_rosca_withdrawals.allow_tags = True

    reverse_personal_ajo_withdrawal.short_description = (
        "AJO: Reverse personal ajo withdrawal"
    )
    reverse_personal_ajo_withdrawal.allow_tags = True

    reverse_savings.short_description = "AJO: Reverse failed savings plan withdrawal"
    reverse_savings.allow_tags = True

    refund_spend_on_double_charge_on_disbursement.short_description = (
        "LOANS: Refund spend charge"
    )
    refund_spend_on_double_charge_on_disbursement.allow_tags = True

    move_funds_from_disbursement_wallet_to_spend.short_description = (
        "LOANS: Move disbursement funds to spend wallet"
    )
    move_funds_from_disbursement_wallet_to_spend.allow_tags = True

    revert_bad_reversals.short_description = "LOANS: Revert bad reversals"
    revert_bad_reversals.allow_tags = True

    refund_repayment_with_no_record.short_description = (
        "LOANS: Refund repayment with no record"
    )
    refund_repayment_with_no_record.allow_tags = True

    loan_escrow_update_cleanup.short_description = (
        "LOANS: loan escrow cleanup and deduct paid amount"
    )
    loan_escrow_update_cleanup.allow_tags = True

    refund_double_charge_ajo_loan_fee.short_description = (
        "LOANS: Refund ajo loan charge fee"
    )
    refund_double_charge_ajo_loan_fee.allow_tags = True

    resolve_reversed_monnify_transfers.short_description = (
        "Resolve reversed monnify transfers"
    )
    resolve_reversed_monnify_transfers.allow_tags = True

    complete_debit_transaction.short_description = (
        "PAYMENT: Complete Pending Debit Transaction"
    )
    complete_debit_transaction.allow_tags = True

    resolve_external_transfer_transactions.short_description = (
        "Resolve external transfers"
    )
    resolve_external_transfer_transactions.allow_tags = True

    quick_savings_reversal.short_description = "QUICKAVINGS: Reversal.."
    quick_savings_reversal.allow_tags = True

    credit_ajo_agent_wallet_from_transaction.short_description = (
        "Refund (Debit transactions only)"
    )
    credit_ajo_agent_wallet_from_transaction.allow_tags = True

    resolve_reversed_vfd_transfers.short_description = "Resolve reversed vfd transfers"
    resolve_reversed_vfd_transfers.allow_tags = True

    commission_refund.short_description = "COMM: Refund to Spend"
    commission_refund.allow_tags = True

    resolve_reversed_paystack_transfers.short_description = (
        "DISBURSEMENT: Resolve Reversed Paystack Transfer"
    )
    resolve_reversed_paystack_transfers.allow_tags = True

    resolved_successful_agency_banking_transactions.short_description = (
        "CHESTLOCK: Resolved Pending Chestlock Transactions"
    )
    resolve_failed_health_insurance_fee.short_description = (
        "HEALTH INSURANCE: Resolve failed health insurance fee"
    )
    resolve_failed_health_insurance_fee.allow_tags = True

    refund_prosper_loan_deposit.short_description = (
        "PROSPER: Refund prosper loan deposit"
    )
    refund_prosper_loan_deposit.allow_tags = True

    refund_loan_repayment_deposit.short_description = (
        "Loans: Refund Excess Repayment Deposit"
    )
    refund_loan_repayment_deposit.allow_tags = True

    actions = [
        fund_plans_from_manual_cleanup,
        debit_transaction_due_to_discrepancy,
        settle_funding_from_liberty_transaction,
        reverse_failed_external_transfer,
        reverse_failed_ajo_agent_personal_rosca_withdrawals,
        reverse_personal_ajo_withdrawal,
        commission_refund,
        reverse_savings,
        refund_spend_on_double_charge_on_disbursement,
        move_funds_from_disbursement_wallet_to_spend,
        revert_bad_reversals,
        revert_bad_disbursement_reversals,
        refund_repayment_with_no_record,
        loan_escrow_update_cleanup,
        refund_double_charge_ajo_loan_fee,
        resolve_reversed_monnify_transfers,
        complete_debit_transaction,
        resolve_external_transfer_transactions,
        quick_savings_reversal,
        credit_ajo_agent_wallet_from_transaction,
        resolve_reversed_vfd_transfers,
        resolve_reversed_paystack_transfers,
        resolved_successful_agency_banking_transactions,
        resolve_failed_health_insurance_fee,
        refund_prosper_loan_deposit,
        refund_loan_repayment_deposit,
    ]


class DebitCreditRecordOnAccountResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["user", "wallet", "onboarded_user"]
    resource_class = DebitCreditRecordOnAccountResource
    search_fields = [
        "user__email",
        "unique_reference",
        "transaction_id",
        "onboarded_user__phone_number",
    ]
    # readonly_fields = set_read
    # only_fields("amount", "balance_after", "balance_before")
    list_filter = (
        "entry",
        "balance_type",
        "wallet_type",
        ("date_created", admin.DateFieldListFilter),
    )

    date_hierarchy = "date_created"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WalletSystemResourceAdmin(ImportExportModelAdmin):
    resource_class = WalletSystemResource
    search_fields = [
        "user__email",
        "wallet_id",
        "available_balance",
        "onboarded_user__phone_number",
        "wallet_number",
    ]

    # readonly_fields = set_readonly_fields(
    #     "available_balance",
    # )

    list_filter = ("wallet_type", "date_created", "user__user_type")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields] + [
            "total_credit",
            "total_debit",
            "offset_amount",
        ]

    def get_actions(self, request):
        actions = super().get_actions(request)
        if not request.user.is_superuser:
            actions.pop("move_prosper_fund_balance_to_agent", None)

        return actions

    def settle_due_balance(self, request, queryset: QuerySet[WalletSystem]):
        response: Dict[str, str] = {}
        for query_instance in queryset:
            if query_instance.wallet_type != WalletTypes.AJO_PREFUNDING:
                continue

            user = query_instance.user

            try:
                PrefundingActions(
                    user=query_instance.user
                ).settle_prefunding_outstanding_from_agent_wallet()
                response[user.email] = "prefunding due balance settled successfully"

            except ValueError as err:
                response[user.email] = str(err)

        self.message_user(request, str(response))

    def move_prosper_fund_balance_to_agent(
        self, request, queryset: QuerySet[WalletSystem]
    ):
        from .model_choices import TransactionSource
        from .selectors import WalletSelector
        from .services import TransactionService

        # DEBUG = 10
        # INFO = 20
        # SUCCESS = 25
        # WARNING = 30
        # ERROR = 40

        if queryset.count() > 1:
            self.message_user(
                request, level=40, message="Must Pass One Instance at once"
            )
            return

        instance = queryset.last()

        deductable_wallet_type = WalletTypes.PROSPER_LOAN_WALLET

        if instance.wallet_type != deductable_wallet_type:
            self.message_user(
                request, level=40, message="Wallet must be Prosper Loan Wallet"
            )
            return

        if instance.available_balance <= 0:
            self.message_user(request, level=40, message="Insufficient balance")
            return

        agent_wallet = WalletSelector.get_or_create_savings_wallet(
            user=instance.user, wallet_type=WalletTypes.AJO_AGENT
        )

        user = instance.user
        amount = instance.available_balance

        with django_transaction.atomic():
            transaction_instance = TransactionService.create_deposit_transaction(
                user=user,
                amount=amount,
                unique_reference=None,
                wallet_type=agent_wallet.wallet_type,
                transaction_form_type=TransactionFormType.IN_WALLET_DEPOSIT,
                transaction_source=TransactionSource.IN_WALLET,
                description=f"{amount} has been deposited into your wallet fro m your {deductable_wallet_type}",
                status=Status.SUCCESS,
            )

            debit_wallet = WalletSystem.deduct_balance(
                wallet=instance,
                amount=amount,
                transaction_instance=transaction_instance,
            )

            credit_wallet = WalletSystem.fund_balance(
                wallet=agent_wallet,
                amount=amount,
                transaction_instance=transaction_instance,
            )

        self.message_user(request, level=25, message="Wallet Successfully credited")

    def debit_company_commission_wallet(
        self, request, queryset: QuerySet[WalletSystem]
    ):
        from .model_choices import TransactionSource
        from .selectors import WalletSelector
        from .services import TransactionService

        # DEBUG = 10
        # INFO = 20
        # SUCCESS = 25
        # WARNING = 30
        # ERROR = 40

        if queryset.count() > 1:
            self.message_user(
                request, level=40, message="Must Pass One Instance at once"
            )
            return

        e_value: str | None = request.GET.get("e", None)
        if not e_value:
            self.message_user(request, level=40, message="Value must be digits")
            return

        try:
            amount = round(float(e_value), 2)
        except ValueError as err:
            self.message_user(request, level=40, message="Value must be digits")
            return

        instance = queryset.last()

        deductable_wallet_type = WalletTypes.AJO_COMMISSION

        if instance.wallet_type != deductable_wallet_type:
            self.message_user(
                request, level=40, message="Wallet must be Ajo Commission Loan Wallet"
            )
            return

        if instance.available_balance <= 0:
            self.message_user(request, level=40, message="Insufficient balance")
            return

        user = instance.user
        wallet_amount = instance.available_balance

        if wallet_amount < amount:
            self.message_user(request, level=40, message="insufficient balance")

        # create transaction
        withdrawal_transaction = (
            TransactionService.create_withdrawal_to_wallet_transaction(
                user=user,
                amount=amount,
                wallet_type=instance.wallet_type,
                request_data=None,
                quotation_id=None,
                plan_type=PlanType.AJO,
                description="wallet deducted for company purposes",
            )
        )

        payment_actions.debit_wallet_and_update_transaction(
            amount=amount,
            wallet=instance,
            transaction_instance=withdrawal_transaction,
        )

        self.message_user(request, level=25, message="balance deducted successfully")
        return

    def debit_wallet_for_commission_purpose(
        self, request, queryset: QuerySet[WalletSystem]
    ):
        from accounts.models import ConstantTable

        from .services import TransactionService

        # DEBUG = 10
        # INFO = 20
        # SUCCESS = 25
        # WARNING = 30
        # ERROR = 40
        admin_emails = ActionPermission.get_admin_emails()

        if request.user.email not in admin_emails:
            self.message_user(
                request,
                "You do not have permission to perform this action.",
                level=messages.ERROR,
            )

        else:
            if queryset.count() > 1:
                self.message_user(
                    request, level=40, message="Must Pass One Instance at once"
                )
                return

            e_value: str | None = request.GET.get("e", None)
            if not e_value:
                self.message_user(request, level=40, message="Value must be digits")
                return

            try:
                amount = round(float(e_value), 2)
            except ValueError as err:
                self.message_user(request, level=40, message="Value must be digits")
                return

            instance = queryset.last()

            if instance.available_balance <= 0:
                self.message_user(request, level=40, message="Insufficient balance")
                return

            user = instance.user
            ajo_user = instance.onboarded_user
            if not ajo_user:
                self.message_user(
                    request, level=40, message="this is not an ajo user's wallet"
                )
                return

            wallet_amount = instance.available_balance

            if wallet_amount < amount:
                self.message_user(request, level=40, message="insufficient balance")

            # create transaction
            withdrawal_transaction = (
                TransactionService.create_withdrawal_to_wallet_transaction(
                    user=user,
                    amount=amount,
                    wallet_type=instance.wallet_type,
                    request_data=None,
                    quotation_id=None,
                    plan_type=PlanType.AJO,
                    ajo_user=instance.onboarded_user,
                    description="wallet deducted for commission",
                )
            )

            payment_actions.debit_wallet_and_fill_transaction(
                amount=amount,
                wallet=instance,
                transaction_instance=withdrawal_transaction,
            )

            # obtain the commissions service charge
            commissions_service_charge = (
                ConstantTable.get_constant_table_instance().commissions_service_charge
            )

            # collect the service charge
            amount = amount - commissions_service_charge

            # create a transaction instance for the service charge
            service_charge_transaction = TransactionService.service_charge_transaction(
                user=user,
                amount=commissions_service_charge,
                ajo_user=ajo_user,
                quotation_id=None,
                transaction_description=f"{commissions_service_charge} was collected as a service charge for commission for ajo plan",
            )

            # if user.user_type != UserType.STAFF_AGENT:

            # call a function that does the calculation and returns how much for company and agent
            shares = payment_actions.calculate_commissions(amount=amount)

            # designate company share
            company_share = shares.get("company_share")

            agent_description_text = f"{shares.get('agent_share')} commission earned, from ajo user, {ajo_user.phone_number[1:]}"

            agent_commission = (
                payment_actions.CommissionService.create_commission_instance(
                    user=user,
                    commission_type=CommissionType.AGENT,
                    amount=shares.get("agent_share"),
                    description=agent_description_text,
                    quotation_id=None,
                    total_amount_taken_as_commission=amount,
                    plan_type=PlanType.AJO,
                )
            )

            # call the commissions selector
            commissions_selector = payment_actions.AjoCommissionsSelector(user=user)

            # obtain the commissions wallet
            commissions_wallet = commissions_selector.get_commissions_wallet()

            # create the transaction instance
            increment_transaction = (
                TransactionService.create_agent_commissions_increment_transaction(
                    user=user,
                    amount=shares.get("agent_share"),
                    quotation_id=None,
                    transaction_description=agent_description_text,
                )
            )

            # increment the commissions balance of the agent's wallet
            payment_actions.fund_wallet_and_update_transaction(
                wallet=commissions_wallet,
                amount=shares.get("agent_share"),
                transaction_instance=increment_transaction,
            )

            # else:
            #     company_share = amount

            # try to obtain the company's user account
            company_user = (
                get_user_model().objects.filter(email="<EMAIL>").last()
            )

            # company description text
            com_description_text = f"{company_share} commission earned, from ajo user, {ajo_user.phone_number[1:]}"

            # call functions to create the Commission instances for both company and agent
            company_commission = (
                payment_actions.CommissionService.create_commission_instance(
                    user=company_user,
                    commission_type=CommissionType.COMPANY,
                    amount=company_share,
                    description=com_description_text,
                    quotation_id=None,
                    total_amount_taken_as_commission=amount,
                    plan_type=PlanType.AJO,
                )
            )

            # call the function for fund the company commission wallet
            if company_user:
                payment_actions.fund_company_commissions_wallet(
                    amount=company_share,
                    company_user=company_user,
                    quotation_id=None,
                    transaction_description=com_description_text,
                )

            self.message_user(
                request, level=25, message="balance deducted successfully"
            )
            return

    def wallets_deduction(self, request, queryset: QuerySet[WalletSystem]):

        admin_emails = ActionPermission.get_admin_emails()

        if request.user.email not in admin_emails:
            self.message_user(
                request,
                "You do not have permission to perform this action.",
                level=messages.ERROR,
            )

        else:
            # loan_access_token = loan_agent_login().get("access")
            savings_acct_user = (
                get_user_model()
                .objects.filter(email=settings.AGENCY_BANKING_USEREMAIL)
                .last()
            )
            for wallet in queryset:
                savings_acct_user = wallet.user
                total_amount = wallet.available_balance
                wallet_type = wallet.wallet_type
                ajo_user = wallet.onboarded_user

                trans_objs = TransactionService.dynamic_deduction_from_wallet_transaction(
                    user=savings_acct_user,
                    amount=total_amount,
                    wallet_type=wallet_type,
                    description="One Off Deduction",
                    ajo_user=ajo_user,
                    transaction_form_type=TransactionFormType.WALLET_DEDUCTION_TO_LOAN,
                )
                # loans_acct_user = get_user_model().objects.filter(email=f"{settings.LOAN_AGENCY_BANKING_USEREMAIL}").last()
                # loans_phone_no = AgencyBankingClass.get_user_info(
                # access_token=loan_access_token,
                # user_id=loans_acct_user.customer_user_id,
                # )
                # loan_phone_number = loans_phone_no.get("data").get("phone_number")
                # temporary fix
                deduct_agent_wallet = WalletSystem.deduct_balance(
                    wallet=wallet,
                    amount=total_amount,
                    transaction_instance=trans_objs,
                    unique_reference=None,
                    onboarded_user=ajo_user,
                )
                trans_objs.status = Status.SUCCESS
                trans_objs.transaction_date_completed = timezone.now()
                trans_objs.wallet_balance_before = deduct_agent_wallet["balance_before"]
                trans_objs.wallet_balance_after = deduct_agent_wallet["balance_after"]
                trans_objs.save()

                loan_user = User.objects.get(
                    email=settings.LOAN_AGENCY_BANKING_USEREMAIL
                )
                loan_phone_number = loan_user.user_phone
                daily_sweep_transfer = pay_withdrawal_and_handle_response(
                    transaction_id=trans_objs.id,
                    amount=total_amount,
                    phone_number=loan_phone_number,
                )
                if daily_sweep_transfer.get("status") == True:
                    self.message_user(
                        request, f"Wallet deduction Successful and settled"
                    )
                else:
                    self.message_user(
                        request, f"Wallet deduction Successful but unsettled"
                    )

    def settle_outsanting_escrow_balance(
        self, request, queryset: QuerySet[WalletSystem]
    ):
        from loans.models import AjoLoan

        for wallet in queryset:
            ajo_user = wallet.onboarded_user
            user = wallet.user
            amount = wallet.available_balance
            wallet_type = wallet.wallet_type

            has_open_loan = AjoLoan.objects.filter(
                borrower=ajo_user, status=LoanStatus.OPEN
            ).last()
            if has_open_loan:
                self.message_user(request, f"Ajo User has an open loan")
            else:
                trans_objs = TransactionService.dynamic_deduction_from_wallet_transaction(
                    user=user,
                    amount=amount,
                    wallet_type=wallet_type,
                    description="Outanding Escrow",
                    ajo_user=ajo_user,
                    transaction_form_type=TransactionFormType.OUSTANDING_ESCROW_SETTLEMENT,
                )
                deduct_agent_wallet = WalletSystem.deduct_balance(
                    wallet=wallet,
                    amount=amount,
                    transaction_instance=trans_objs,
                    unique_reference=None,
                    onboarded_user=ajo_user,
                )
                trans_objs.status = Status.SUCCESS
                trans_objs.transaction_date_completed = timezone.now()
                trans_objs.wallet_balance_before = deduct_agent_wallet["balance_before"]
                trans_objs.wallet_balance_after = deduct_agent_wallet["balance_after"]
                trans_objs.save()

                ajo_user_selector = AjoUserSelector(ajo_user=ajo_user)
                ajo_user_spend_wallet = ajo_user_selector.get_spending_wallet()

                deposit_transaction = (
                    TransactionService.create_deposit_by_wallet_transaction(
                        user=ajo_user_spend_wallet.user,
                        amount=amount,
                        wallet_type=ajo_user_spend_wallet.wallet_type,
                        quotation_id=uuid.uuid4(),
                        plan_type=PlanType.LOAN,
                        ajo_user=ajo_user,
                    )
                )
                credit_wallet = WalletSystem.fund_balance(
                    wallet=ajo_user_spend_wallet,
                    amount=amount,
                    transaction_instance=deposit_transaction,
                    onboarded_user=ajo_user,
                )
                deposit_transaction.status = Status.SUCCESS
                deposit_transaction.transaction_date_completed = timezone.now()
                deposit_transaction.wallet_balance_before = credit_wallet[
                    "balance_before"
                ]
                deposit_transaction.wallet_balance_after = credit_wallet[
                    "balance_after"
                ]
                deposit_transaction.transaction_form_type = (
                    TransactionFormType.OUSTANDING_ESCROW_SETTLEMENT
                )
                deposit_transaction.save()

                self.message_user(
                    request, f"Escrow balance successfully moved to spend"
                )

    def spending_sweep_action(self, request, queryset: QuerySet[WalletSystem]):
        phone = "08023073430"
        destination_phone = "08117849057"
        ajo_user = AjoUser.objects.get(phone_number=phone)

        for wallet in queryset:
            if wallet.onboarded_user == ajo_user:
                amount = wallet.available_balance

                trans_objs = TransactionService.dynamic_deduction_from_wallet_transaction(
                    user=wallet.user,
                    amount=wallet.available_balance,
                    wallet_type=wallet.wallet_type,
                    description="Outanding Escrow",
                    ajo_user=wallet.onboarded_user,
                    transaction_form_type=TransactionFormType.OUSTANDING_ESCROW_SETTLEMENT,
                )
                deduct_agent_wallet = WalletSystem.deduct_balance(
                    wallet=wallet,
                    amount=amount,
                    transaction_instance=trans_objs,
                    unique_reference=None,
                    onboarded_user=wallet.onboarded_user,
                )
                trans_objs.status = Status.SUCCESS
                trans_objs.transaction_date_completed = timezone.now()
                trans_objs.wallet_balance_before = deduct_agent_wallet["balance_before"]
                trans_objs.wallet_balance_after = deduct_agent_wallet["balance_after"]
                trans_objs.save()

                savings_user_access_token = agent_login().get("access")
                savings_user = User.objects.get(email=settings.AGENCY_BANKING_USEREMAIL)

                AgencyBankingClass.send_money_from_liberty_to_user_through_pay_buddy(
                    phone_number=destination_phone,
                    amount=amount,
                    transaction_reference=uuid.uuid4(),
                    access_token=savings_user_access_token,
                )
                self.message_user(
                    request, f"Escrow balance successfully moved to spend"
                )

    def cashout_ajouser_spend_to_agent(self, request, queryset: QuerySet[WalletSystem]):

        admin_emails = ActionPermission.get_admin_emails()

        if request.user.email not in admin_emails:
            self.message_user(
                request,
                "You do not have permission to perform this action.",
                level=messages.ERROR,
            )

        else:

            for query_instance in queryset:

                if query_instance.wallet_type != "AJO_SPENDING":
                    self.message_user(
                        request,
                        "Invalid Wallet type",
                        level=messages.ERROR,
                    )
                    continue

                balance = query_instance.available_balance
                user = query_instance.user
                ajo_user = query_instance.onboarded_user

                if balance > 0:
                    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                    unique_id = str(uuid.uuid4()).split("-")[0]
                    quotation_id = f"{timestamp}{unique_id}"

                    transaction_form_type = "CASHOUT"
                    description = f"{balance} was cashed out from your spending wallet to {user.email} wallet"
                    transaction_instance = (
                        TransactionService.dynamic_deduction_from_wallet_transaction(
                            user=user,
                            transaction_source=TransactionSource.WALLET,
                            description=description,
                            amount=balance,
                            wallet_type=query_instance.wallet_type,
                            request_data=None,
                            quotation_id=quotation_id,
                            ajo_user=ajo_user,
                            transaction_form_type=transaction_form_type,
                        )
                    )

                    deduct_agent_wallet = WalletSystem.deduct_balance(
                        wallet=query_instance,
                        amount=balance,
                        transaction_instance=transaction_instance,
                        unique_reference=None,
                        onboarded_user=ajo_user,
                    )
                    transaction_instance.status = Status.SUCCESS
                    transaction_instance.transaction_date_completed = timezone.now()
                    transaction_instance.wallet_balance_before = deduct_agent_wallet[
                        "balance_before"
                    ]
                    transaction_instance.wallet_balance_after = deduct_agent_wallet[
                        "balance_after"
                    ]
                    transaction_instance.save()

                    # credit agent
                    agent_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()
                    credit_description = f"{balance} was deposited to your ajo wallet from saver {ajo_user.phone_number}"
                    credit_transaction = (
                        TransactionService.create_deposit_by_wallet_transaction(
                            user=user,
                            amount=balance,
                            wallet_type=agent_wallet.wallet_type,
                            transaction_form_type=transaction_form_type,
                            unique_reference=None,
                            description=credit_description,
                            status=Status.PENDING,
                            quotation_id=quotation_id,
                            ajo_user=ajo_user,
                            plan_type=PlanType.AJO,
                        )
                    )

                    fund_agent_wallet(
                        transaction=credit_transaction,
                        wallet=agent_wallet,
                        amount=balance,
                        unique_reference=None,
                    )
                    self.message_user(request, "success")

                else:
                    self.message_user(
                        request,
                        "Insufficient Funds",
                        level=messages.ERROR,
                    )
                    continue

    def update_bank_account_with_wallet_type(
        self, request, queryset: QuerySet[WalletSystem]
    ):
        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:
            for instance in queryset:
                wallet_form_type = instance.wallet_type
                #### get bank account details
                user_bank_account_detail_queryset = BankAccountDetails.objects.filter(
                    ajo_user=instance.onboarded_user,
                    user=instance.onboarded_user.user,
                    form_type=wallet_form_type,
                    user_wallet__isnull=True,
                ).update(user_wallet=instance)

                message = "success"
        self.message_user(request, message)

    def toggle_is_active(self, request, queryset: QuerySet[WalletSystem]):
        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:
            for instance in queryset:
                instance.is_active = not instance.is_active
                instance.save(update_fields=["is_active"])
                instance.refresh_from_db()

                message = f"Instance status toggled. {instance.is_active}"
        self.message_user(request, message)

    def remove_wallet(self, request, queryset: QuerySet[WalletSystem]):
        message = "Invalid Action"
        admin_emails = ActionPermission.get_customer_service_emails()

        if request.user.email not in admin_emails:
            message = "You do not have permission to perform this action."
        else:
            for instance in queryset:

                wallet_count = WalletSystem.objects.filter(
                    user=instance.user,
                    onboarded_user=instance.onboarded_user,
                    wallet_type=instance.wallet_type,
                ).count()

                if instance.available_balance > 0:  # Check if the balance is zero
                    message = f"Instance has a balance of {instance.available_balance}, so it cannot be removed."
                    continue

                elif wallet_count > 1:
                    instance.delete()  # Delete the instance
                    message = "Instance with zero balance removed."

                else:

                    message = f"cannot remove wallet, user has only one occurence of this wallet type: {instance.wallet_type}"

        self.message_user(request, message)

    settle_due_balance.short_description = (
        "AJO: Settle prefunding due balance from agent wallet"
    )
    move_prosper_fund_balance_to_agent.short_description = (
        "LOANS: Move Prosper Loan Funds To Agent wallet"
    )
    debit_company_commission_wallet.short_description = (
        "PAYMENT: Debit Balance from Company Commissions wallet"
    )

    settle_due_balance.allow_tags = True
    debit_company_commission_wallet.allow_tags = True
    move_prosper_fund_balance_to_agent.allow_tags = True

    wallets_deduction.short_description = "Deduct spend to loans"
    wallets_deduction.allow_tags = True

    settle_outsanting_escrow_balance.short_description = (
        "settle outsanting escrow balance"
    )
    settle_outsanting_escrow_balance.allow_tags = True

    spending_sweep_action.short_description = "spending sweep action"
    spending_sweep_action.allow_tags = True

    debit_wallet_for_commission_purpose.short_description = (
        "Debit specific amount for Commission purposes"
    )
    debit_wallet_for_commission_purpose.allow_tags = True

    cashout_ajouser_spend_to_agent.short_description = "Charge spend to ajo agent"
    cashout_ajouser_spend_to_agent.allow_tags = True

    update_bank_account_with_wallet_type.short_description = (
        "update bank account detail with wallet"
    )
    update_bank_account_with_wallet_type.allow_tags = True

    toggle_is_active.short_description = "toggle status"
    toggle_is_active.allow_tags = True

    remove_wallet.short_description = (
        "remove wallet (remove wallet if multiple instance exist)"
    )
    remove_wallet.allow_tags = True

    actions = [
        settle_due_balance,
        move_prosper_fund_balance_to_agent,
        debit_company_commission_wallet,
        wallets_deduction,
        settle_outsanting_escrow_balance,
        spending_sweep_action,
        debit_wallet_for_commission_purpose,
        cashout_ajouser_spend_to_agent,
        update_bank_account_with_wallet_type,
        toggle_is_active,
        remove_wallet,
    ]


class WebhookSettingsResourceAdmin(ImportExportModelAdmin):
    resource_class = WebhookSettingsResource
    search_fields = ["auth_header"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class IPAddressResourceAdmin(ImportExportModelAdmin):
    resource_class = IPAddressResource
    search_fields = ["address", "webhook_settings__auth_header"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CommissionResourceAdmin(ImportExportModelAdmin):
    resource_class = CommissionResource
    search_fields = [
        "user__email",
        "commission_type",
        "amount",
        "reference",
        "quotation_id",
        "plan_type",
        "total_amount_taken_as_commission",
        "withdrawn",
    ]
    readonly_fields = set_readonly_fields(
        "amount",
        "commission_type",
        "total_amount_taken_as_commission",
    )

    list_filter = (
        ("created_at", admin.DateFieldListFilter),
        "commission_type",
    )

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RequestDumpResourceAdmin(ImportExportModelAdmin):
    resource_class = RequestDumpResource

    list_filter = (("created_at", admin.DateFieldListFilter),)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WithdrawalAccountResourceAdmin(ImportExportModelAdmin):
    resource_class = WithdrawalAccountResource
    search_fields = ["user__email", "account_number"]

    list_filter = (("created_at", admin.DateFieldListFilter),)

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ManualTransferResourceAdmin(ImportExportModelAdmin):
    resource_class = ManualTransferResource
    search_fields = ["address", "webhook_settings__auth_header"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WalletTransactionReconciliationResourceAdmin(ImportExportModelAdmin):
    resource_class = WalletTransactionReconciliationResource
    # search_fields = ["address", "webhook_settings__auth_header"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TempLogOnlendingPaymentsAdmin(ImportExportModelAdmin):
    resource_class = TempLogOnlendingPaymentsResource

    # Custom filter for the `date_created` field using DateTimeFieldListFilter
    list_filter = (("date_created", admin.DateFieldListFilter),)

    # Display the fields in the admin list page dynamically
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class MakeAdminTransferResourceAdmin(ImportExportModelAdmin):
    resource_class = MakeAdminTransferResource

    # Custom filter for the `date_created` field using DateTimeFieldListFilter
    list_filter = (("created_at", admin.DateFieldListFilter),)
    date_hierarchy = "created_at"
    readonly_fields = ["transfer_pin"]

    # Display the fields in the admin list page dynamically
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return ["transfer_pin"]
        return []


class PayWiseWaitListResourceAdmin(ImportExportModelAdmin):
    resource_class = PayWiseWaitListResource

    search_fields = [
        "full_name",
        "email",
        "phone_number",
        "company_name",
        "product_interest",
    ]

    # Custom filter for the `date_created` field using DateTimeFieldListFilter
    list_filter = (("created_at", admin.DateFieldListFilter),)
    date_hierarchy = "created_at"

    # Display the fields in the admin list page dynamically
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


# Register the admin class

###################################################################################################################
# REGISTER MODELS

admin.site.register(Transaction, TransactionResourceAdmin)
admin.site.register(DebitCreditRecordOnAccount, DebitCreditRecordOnAccountResourceAdmin)
admin.site.register(WalletSystem, WalletSystemResourceAdmin)
admin.site.register(WebhookSettings, WebhookSettingsResourceAdmin)
admin.site.register(IPAddress, IPAddressResourceAdmin)
admin.site.register(Commission, CommissionResourceAdmin)
admin.site.register(RequestDump, RequestDumpResourceAdmin)
admin.site.register(WithdrawalAccount, WithdrawalAccountResourceAdmin)
admin.site.register(ManualTransfer, ManualTransferResourceAdmin)
admin.site.register(
    WalletTransactionReconciliation, WalletTransactionReconciliationResourceAdmin
)
admin.site.register(TempLogOnlendingPayments, TempLogOnlendingPaymentsAdmin)
admin.site.register(MakeAdminTransfer, MakeAdminTransferResourceAdmin)
admin.site.register(PayWiseWaitList, PayWiseWaitListResourceAdmin)


# @admin.register(Transaction)
# class TransactionAdmin(admin.ModelAdmin):
#     def get_list_display(self, request):
#         return [field.name for field in self.model._meta.concrete_fields]


# # @admin.register(Card)
# # class CardAdmin(admin.ModelAdmin):
# #     def get_list_display(self, request):
# #         return [field.name for field in self.model._meta.concrete_fields]


# @admin.register(DebitCreditRecordOnAccount)
# class DebitCreditRecordOnAccountAdmin(admin.ModelAdmin):
#     def get_list_display(self, request):
#         return [field.name for field in self.model._meta.concrete_fields]


# @admin.register(WalletSystem)
# class WalletSystemAdmin(admin.ModelAdmin):
#     def get_list_display(self, request):
#         return [field.name for field in self.model._meta.concrete_fields]


# # @admin.register(RawPaystackData)
# # class RawPaystackDataAdmin(admin.ModelAdmin):
# #     def get_list_display(self, request):
# #         return [field.name for field in self.model._meta.concrete_fields]
