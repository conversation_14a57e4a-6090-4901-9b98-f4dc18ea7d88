import re

from django.core.validators import RegexValidator
from rest_framework import serializers

from accounts.models import ConstantTable

from .model_choices import PaymentMethod, PlanType, WalletTypes
from .models import DebitCard, PayWiseWaitList, WithdrawalAccount


class GetCardsSerializer(serializers.Serializer):
    masked_pan = serializers.CharField()
    exp_month = serializers.CharField()
    exp_year = serializers.CharField()
    brand = serializers.CharField()
    account_name = serializers.CharField()


class SavingsBalanceSummarySerializer(serializers.Serializer):
    wallets_balance = serializers.FloatField()
    total_savings = serializers.IntegerField()


class PayWithWalletSerializer(serializers.Serializer):
    plan_id = serializers.IntegerField()
    transaction_pin = serializers.CharField()
    plan_type = serializers.CharField()

    def validate(self, attrs):
        transaction_pin = attrs.get("transaction_pin", "")
        plan_type = attrs.get("plan_type", "")

        if not transaction_pin.isdigit():
            raise serializers.ValidationError("the transaction pin should be numeric only")

        if plan_type.upper() not in PlanType:
            raise serializers.ValidationError("the plan type should be CHESTLOCK, HALAL, QUICKSAVINGS or ONLENDING")
        else:
            attrs["plan_type"] = getattr(PlanType, plan_type.upper())

        return attrs


class PayWithCardSerializer(serializers.Serializer):
    plan_id = serializers.IntegerField()
    plan_type = serializers.CharField()
    transaction_pin = serializers.CharField()
    masked_pan = serializers.CharField()
    # auth_code is for test purposes
    auth_code = serializers.CharField(required=False)

    def validate(self, attrs):
        transaction_pin = attrs.get("transaction_pin", "")
        plan_type = attrs.get("plan_type", "")
        masked_pan = attrs.get("masked_pan", "")

        if not transaction_pin.isdigit():
            raise serializers.ValidationError("the transaction pin should be numeric only")

        if plan_type.upper() not in PlanType:
            raise serializers.ValidationError("the plan type should be CHESTLOCK, HALAL or QUICKSAVINGS")
        else:
            attrs["plan_type"] = getattr(PlanType, plan_type.upper())

        if not re.match(pattern=r"^[0-9*]+$", string=masked_pan):
            raise serializers.ValidationError(
                "the plan name should start with a letter and can have numbers and spaces"
            )
        return attrs


class WithdrawFromWalletSerializer(serializers.Serializer):
    amount = serializers.FloatField(required=False, allow_null=True)
    plan_id = serializers.IntegerField()
    transaction_pin = serializers.CharField()
    plan_type = serializers.CharField()

    def validate(self, attrs):
        amount = attrs.get("amount", "")
        transaction_pin = attrs.get("transaction_pin", "")
        plan_type = attrs.get("plan_type", "")

        # check if the amount is less than the Constant minimum withdrawal amount
        minimum_withdrawal_amount: float = ConstantTable.get_constant_table_instance().minimum_withdrawal_amount
        if amount:
            if amount < minimum_withdrawal_amount:
                raise serializers.ValidationError(f"amount must not be less than {minimum_withdrawal_amount} naira")

        if not transaction_pin.isdigit():
            raise serializers.ValidationError("the transaction pin should be numeric only")

        if plan_type.upper() in PlanType:
            attrs["plan_type"] = plan_type.upper()
            if plan_type.upper() == (PlanType.HALAL or PlanType.QUICKSAVINGS):
                if not amount:
                    raise serializers.ValidationError("there should be an amount value")
        else:
            raise serializers.ValidationError("the plan type should be CHESTLOCK, HALAL or QUICKSAVINGS")

        return attrs


class AddCardSerializer(serializers.Serializer):
    response = serializers.JSONField()


class CardDetailsSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()

    class Meta:
        model = DebitCard
        fields = [
            "user",
            "id",
            "bin",
            "card_last_four_digits",
            "exp_year",
            "exp_month",
            "card_type",
            "brand",
        ]


class PayWithNewCardSerializer(serializers.Serializer):
    plan_id = serializers.IntegerField()
    plan_type = serializers.CharField()

    def validate(self, attrs):
        plan_type = attrs.get("plan_type", "")

        if plan_type.upper() not in PlanType:
            raise serializers.ValidationError("the plan type should be CHESTLOCK, HALAL or QUICKSAVINGS")
        else:
            attrs["plan_type"] = getattr(PlanType, plan_type.upper())

        return attrs


class OneTimePaymentSerializer(serializers.Serializer):
    email = serializers.EmailField()
    amount = serializers.FloatField(default=0.0)

    def validate(self, attrs):
        amount = attrs.get("amount", "")
        if not amount:
            raise serializers.ValidationError("there should be an amount value")
        if amount < 1_000:
            raise serializers.ValidationError("minimum deposit amount is 1000")
        return super().validate(attrs)


class PauseSavingsPlanSerializer(serializers.Serializer):
    plan_id = serializers.IntegerField()
    plan_type = serializers.CharField()

    def validate(self, attrs):
        plan_type = attrs.get("plan_type", "")

        if plan_type.upper() not in PlanType:
            raise serializers.ValidationError("the plan type should be CHESTLOCK, HALAL or QUICKSAVINGS")
        else:
            attrs["plan_type"] = getattr(PlanType, plan_type.upper())
        return attrs


class ContinueSavingsPlanSerializer(serializers.Serializer):
    plan_id = serializers.IntegerField()
    plan_type = serializers.CharField()

    def validate(self, attrs):
        plan_type = attrs.get("plan_type", "")

        if plan_type.upper() not in PlanType:
            raise serializers.ValidationError("the plan type should be CHESTLOCK, HALAL or QUICKSAVINGS")
        else:
            attrs["plan_type"] = getattr(PlanType, plan_type.upper())

        return attrs


class DeleteSavingsPlanSerializer(serializers.Serializer):
    plan_id = serializers.IntegerField()
    plan_type = serializers.CharField()
    transaction_pin = serializers.CharField()

    def validate(self, attrs):
        plan_type = attrs.get("plan_type", "")
        transaction_pin = attrs.get("transaction_pin", "")

        if plan_type.upper() not in PlanType:
            raise serializers.ValidationError("the plan type should be CHESTLOCK, HALAL or QUICKSAVINGS")
        else:
            attrs["plan_type"] = getattr(PlanType, plan_type.upper())

        if not transaction_pin.isdigit():
            raise serializers.ValidationError("the transaction pin should be numeric only")

        return attrs


class PayWithAgencyWalletSerializerV2(serializers.Serializer):
    amount = serializers.FloatField(
        required=False,
        allow_null=True,
        help_text="this field will not work for certain plan types eg Rotation Groups",
    )
    plan_id = serializers.IntegerField()
    plan_type = serializers.CharField()
    transaction_pin = serializers.CharField()

    def validate(self, attrs):
        transaction_pin = attrs.get("transaction_pin", "")
        plan_type = attrs.get("plan_type", "")

        if not transaction_pin.isdigit():
            raise serializers.ValidationError("the transaction pin should be numeric only")

        if plan_type.upper() not in PlanType:
            raise serializers.ValidationError(f"please choose from the list: {list(PlanType.__members__.keys())}")
        else:
            attrs["plan_type"] = getattr(PlanType, plan_type.upper())

        return attrs


class PayWithCardSerializerV2(serializers.Serializer):
    amount = serializers.FloatField(required=False, allow_null=True)
    plan_id = serializers.IntegerField()
    plan_type = serializers.CharField()
    transaction_pin = serializers.CharField()
    masked_pan = serializers.CharField()
    # auth_code is for test purposes
    auth_code = serializers.CharField(required=False)

    def validate(self, attrs):
        transaction_pin = attrs.get("transaction_pin", "")
        plan_type = attrs.get("plan_type", "")

        if not transaction_pin.isdigit():
            raise serializers.ValidationError("the transaction pin should be numeric only")

        if plan_type.upper() not in PlanType:
            raise serializers.ValidationError(f"please choose from the list: {list(PlanType.__members__.keys())}")
        else:
            attrs["plan_type"] = getattr(PlanType, plan_type.upper())

        return attrs


# class SaveToAjoSavingsPlanSerializer(serializers.Serializer):
#     amount = serializers.FloatField()
#     plan_id = serializers.IntegerField()
#     ajo_user_transaction_pin = serializers.CharField(required=False, allow_null=True)
#     transaction_pin = serializers.CharField()

#     def validate(self, attrs):
#         transaction_pin = attrs.get("transaction_pin", "")
#         ajo_user_transaction_pin = attrs.get("ajo_user_transaction_pin", "")

#         if not transaction_pin.isdigit():
#             raise serializers.ValidationError("the transaction pin should be numeric only")

#         if ajo_user_transaction_pin:
#             if not ajo_user_transaction_pin.isdigit():
#                 raise serializers.ValidationError("the ajo user transaction pin should be numeric only")

#         return attrs


# class SaveToRotationSavingsGroupSerializer(serializers.Serializer):
#     payment_method = serializers.CharField()
#     amount = serializers.FloatField(required=False)
#     group_id = serializers.CharField()
#     transaction_pin = serializers.CharField()

#     def validate(self, attrs):
#         payment_method = attrs.get("payment_method", "")
#         transaction_pin = attrs.get("transaction_pin", "")

#         if payment_method.upper() not in PaymentMethod:
#             raise serializers.ValidationError("the payment method should be WALLET, DEBIT_CARD")
#         else:
#             attrs["payment_method"] = getattr(PaymentMethod, payment_method.upper())

#         if not transaction_pin.isdigit():
#             raise serializers.ValidationError("the transaction pin should be numeric only")

#         return attrs


class PayForOnlendingPlanSerializer(serializers.Serializer):
    plan_id = serializers.IntegerField()
    wallet_type = serializers.CharField()
    otp = serializers.CharField(required=False, allow_null=True)
    transaction_pin = serializers.CharField(required=False, allow_null=True)
    ajo_plan_id = serializers.IntegerField(required=False, allow_null=True)

    def validate(self, attrs):
        valid_wallet_types = [
            "AJO_AGENT",
            "AJO_SPENDING",
            "ONLENDING_MAIN",
            "AJO_USER",
        ]

        wallet_type = attrs.get("wallet_type")
        otp = attrs.get("otp", "")
        transaction_pin = attrs.get("transaction_pin", "")
        ajo_plan_id = attrs.get("ajo_plan_id", "")

        wallet_type = wallet_type.upper()
        if wallet_type not in valid_wallet_types:
            raise serializers.ValidationError(f"wallet type should be in this list: {valid_wallet_types}")
        else:
            attrs["wallet_type"] = getattr(WalletTypes, wallet_type.upper())

        if wallet_type in valid_wallet_types[1::2]:
            if not otp:
                raise serializers.ValidationError("'otp' field required in request data")

        if wallet_type == valid_wallet_types[0]:
            if not transaction_pin:
                raise serializers.ValidationError("'transaction_pin' required in request data")

        if wallet_type == valid_wallet_types[2]:
            error = serializers.ValidationError("please supply either an OTP or a transaction pin")

            if not otp and not transaction_pin:
                raise error

            if otp and transaction_pin:
                raise error

        if otp:
            if not otp.isdigit():
                raise serializers.ValidationError("OTP field must be numbers only")

        if transaction_pin:
            if not transaction_pin.isdigit():
                raise serializers.ValidationError("Transaction pin must be numbers only")

        if wallet_type == valid_wallet_types[3]:
            if not ajo_plan_id:
                raise serializers.ValidationError("'ajo_plan_id' required in request data")

        return attrs


class VerifyAccountDetailsSerializer(serializers.Serializer):
    """
    Account number, bank name and bank code for checking for the account name
    """

    account_number = serializers.CharField(
        max_length=10,
        validators=[
            RegexValidator(
                regex=r"^\d{10}$",
                message="account number must be 10 digits",
                code="invalid_account_number",
            )
        ],
    )
    bank_name = serializers.CharField()
    bank_code = serializers.CharField(
        max_length=9,
        validators=[
            RegexValidator(
                regex=r"^\d{6,9}$",
                message="bank code must be 6 to 9 digits",
                code="invalid_bank_code",
            )
        ],
    )


class VerifiedAccountDetailsSerializer(VerifyAccountDetailsSerializer):
    """
    all the information to save in the database alongside an OTP to verify
    """

    account_name = serializers.CharField()
    transaction_pin = serializers.CharField(
        validators=[
            RegexValidator(
                regex=r"^\d{4}$",
                message="transaction pin must be 4 digits",
                code="invalid_transaction_pin",
            )
        ]
    )


class WithdrawalAccountSerializer(serializers.ModelSerializer):
    class Meta:
        model = WithdrawalAccount
        fields = [
            "account_number",
            "account_name",
            "bank_name",
        ]


class TransactionIDSerializer(serializers.Serializer):
    transaction_id = serializers.CharField(required=True)


class ManualWalletDepletionSerializer(serializers.Serializer):
    amount = serializers.FloatField()
    reason = serializers.CharField(max_length=200)
    wallet_id = serializers.IntegerField()


class PayWiseWaitListSerializer(serializers.ModelSerializer):
    class Meta:
        model = PayWiseWaitList
        fields = "__all__"