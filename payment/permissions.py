from django.contrib.auth.hashers import check_password
from rest_framework import permissions
from rest_framework.exceptions import PermissionDenied

from .models import WebhookSettings


class WebHookCustomPermissionDenied(PermissionDenied):
    def __init__(self, detail=None, code=None):
        data = {"error": "845", "status": False, "message": detail}
        super().__init__(detail=data, code=code)


class WebhookAuthorizationPermission(permissions.BasePermission):
    def has_permission(self, request, view):
        settings = WebhookSettings.get_settings()

        # if no WebhookSettings instance is returned
        if settings is None:
            raise WebHookCustomPermissionDenied(
                detail="please set a list of IP addresses and authorization header for this endpoint"
            )

        # Perform the IP whitelist check
        remote_ip = request.META.get("REMOTE_ADDR")
        if remote_ip not in settings.ip_addresses.values_list("address", flat=True):
            raise WebHookCustomPermissionDenied(detail="IP not whitelisted")

        # Perform the authorization header check
        header_value = request.headers.get("Webhook-Header", None)
        # if header_value != settings.auth_header:
        if not check_password(header_value, settings.auth_header):
            raise WebHookCustomPermissionDenied(detail="invalid authorization key")

        return True
