from typing import Dict, List

from celery import shared_task
from django.conf import settings
from django.contrib.auth import get_user_model
from django.db import transaction as django_transaction
from django.utils import timezone
from django.db.models import Sum

from accounts.agency_banking import AgencyBankingClass
from accounts.helper_files.monify import Monnify
from accounts.liberty_ussd import LibertyUSSD
from accounts.models import ConstantTable, CustomUser
from ajo.models import AccountFormType, BankAccountDetails

from .admin import payment_actions
from .checks import VerifyTransactionsOnAgency
from .model_choices import (
    DisbursementProviderType,
    PaymentMethod,
    PlanType,
    Status,
    TransactionFormType,
)
from .models import Transaction
from .utils import Utility
from loans.enums import DisbursementStatus, LoanStatus, LoanType
from loans.models import AjoLoan, VfdProviderDisbursementLog


@shared_task
def mark_old_card_deposit_pending_transactions_as_failed():
    two_hours_ago = timezone.localtime() - timezone.timedelta(hours=2)

    # Find pending transactions older than 2 hours
    pending_transactions = Transaction.objects.filter(
        transaction_form_type=TransactionFormType.CARD_DEPOSIT,
        status=Status.PENDING,
        date_created__lte=two_hours_ago,
        payload__isnull=True,
    )

    print("pending_card_transactions: ", pending_transactions)

    # Update status to 'failed' for each pending transaction
    for transaction in pending_transactions:
        transaction.status = Status.FAILED
        transaction.save()


@shared_task(name="charge_user_wallet_task")
def charge_user_wallet_task(
    plan_id: int, plan_type: str, user_id: int, transaction_pin: str
):
    # obtain the information needed
    user = CustomUser.objects.get(customer_user_id=user_id)
    plan_instance = Utility.get_plan_instance(
        user=user, plan_id=plan_id, plan_type=plan_type
    )
    print(plan_instance)

    # regulator
    if ConstantTable.get_constant_table_instance().pay_from_wallet_regulator == False:
        raise Exception("pay wallet regulator inactive")

    if plan_instance.is_activated == False:
        raise Exception("activate the plan first")

    if plan_instance.payment_method != PaymentMethod.WALLET:
        raise Exception("the payment method is not wallet, contact customer care")

    if plan_instance.completed == True:
        plan_instance.task = None
        plan_instance.save()
        raise Exception("this plan is marked as complete and hence will not run again")

    if plan_instance.is_active == False:
        raise Exception(
            "plan is marked as inactive for some reason, please investigate"
        )

    # define the amount and remaining_amount
    amount = float(plan_instance.periodic_amount)
    remaining_amount = float(plan_instance.target) - float(plan_instance.amount_saved)
    # the below variable tracks if the remaining amount was charged to check if it's the last time
    remainder_became_amount = False

    if remaining_amount >= amount:
        pass
    else:
        amount = remaining_amount
        remainder_became_amount = True

    charge = AgencyBankingClass.charge_user_wallet(
        user=user,
        plan_type=plan_type,
        quotation_id=plan_instance.quotation_id,
        transaction_pin=transaction_pin,
        amount=amount,
        super_token=settings.AGENCY_BANKING_SUPER_TOKEN,
    )

    if (charge.get("status") == True) and ("debit_credit_info" in charge.keys()):
        # get debit_credit_info
        debit_credit = charge.get("debit_credit_info")

        # get the transactio instance
        transaction = charge.get("transaction")

        # set the transaction balance before
        transaction.balance_before = plan_instance.amount_saved

        # increase the amount saved
        plan_instance.amount_saved += transaction.amount

        # set the transaction balance after
        transaction.balance_after = transaction.balance_before + transaction.amount

        # set the plan balance before and plan balance after
        plan_instance.plan_balance_before = debit_credit.get("balance_before")
        plan_instance.plan_balance_after = debit_credit.get("balance_after")

        # save changes
        plan_instance.save()
        transaction.save()

        # This checks if the plan is complete
        if remainder_became_amount:
            # double check
            if (remaining_amount == transaction.amount) and (
                plan_instance.target == plan_instance.amount_saved
            ):
                # the plan is complete
                plan_instance.completed = True
                if plan_instance.task is not None:
                    plan_instance.task.delete()
                plan_instance.is_active = False

                # check if the plan is a halal plan and if the lock is true
                if plan_type.upper() == PlanType.HALAL:
                    if plan_instance.lock == True:
                        plan_instance.is_active = True

                plan_instance.save()

        # if remainder never became the amount but it's completed
        if (remaining_amount == transaction.amount) and (
            plan_instance.target == plan_instance.amount_saved
        ):
            # the plan is complete
            plan_instance.completed = True
            if plan_instance.task is not None:
                plan_instance.task.delete()
            plan_instance.is_active = False

            # check if the plan is a halal plan and if the lock is true
            if plan_type.upper() == PlanType.HALAL:
                if plan_instance.lock == True:
                    plan_instance.is_active = True

            plan_instance.save()

    # if the charge response is not successful

    # check for  insufficient balance failed transaction
    if (charge.get("status") == False) and ("insufficient" in charge.get("message")):
        print(charge.get("message"))
        raise Exception("insufficient balance")

    else:
        print(charge.get("message"))
        raise Exception("either user_id or error in amount")


@shared_task
@django_transaction.atomic
def settle_transfer_transaction(transaction_id: int) -> Dict[str, str]:
    """
    Confirms the status of a transfer to external bank account transaction
    and checks if the transfer was sucessful for now.

    Args:
        transaction_id (int): the ID of the transaction.
        user_id (int): the ID of the user.

    Returns:
        Dict[str, str]: the response of the task.
    """
    # try:
    #     user = get_user_model().objects.get(id=user_id)

    # except get_user_model().DoesNotExist:
    #     return {
    #         "status": "failed",
    #         "message": f"could not obtain user with id {user_id}",
    #     }

    try:
        transfer_transaction = Transaction.objects.get(
            id=transaction_id,
        )

    except Transaction.DoesNotExist:
        return {
            "status": "failed",
            "message": f"could not obtain transaction with id {transaction_id}",
        }

    if transfer_transaction.transaction_form_type not in [
        TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT,
        TransactionFormType.DISBURSE_TO_EXTERNAL_ACCOUNT,
        TransactionFormType.FUND_MONNIFY_ACCOUNT,
    ]:
        return {
            "status": "failed",
            "message": "this is not a transfer transaction",
        }

    # if not transfer_transaction.unique_reference:
    #     return {
    #         "status": "failed",
    #         "message": f"there is no unique reference to verify this transaction {transaction_id} {not transfer_transaction.unique_reference} {transfer_transaction.unique_reference}",
    #     }

    reference = transfer_transaction.unique_reference
    vfd_transfer = transfer_transaction.vfd_transfers.last()
    borrower_account_transfer = transfer_transaction.borrower_account_transfers.last()
    vfd_disbursement_log = VfdProviderDisbursementLog.objects.filter(
        transaction=transfer_transaction
    )

    if not reference:
        reference = transfer_transaction.transaction_id

    if (
        transfer_transaction.transfer_provider
        == DisbursementProviderType.MONNIFY_PROVIDER
    ):
        monnify_class = Monnify()

        verification = monnify_class.verify_monnify_disbursement(
            reference=transfer_transaction.transaction_id,
        )
    elif (
        transfer_transaction.transfer_provider == DisbursementProviderType.VFD_PROVIDER
    ):
        # txnId = transfer_transaction.unique_reference
        verification = LibertyUSSD.verify_vfd_overdraft_disbursement(
            reference=reference
        )
        VfdProviderDisbursementLog.objects.filter(
            transaction=transfer_transaction
        ).update(verification_response=verification)
    elif (
        transfer_transaction.transfer_provider
        == DisbursementProviderType.AGENCY_BANKING_PROVIDER
    ):
        verification = VerifyTransactionsOnAgency.verify_bank_transfers_v2(
            reference=reference,
        )
    elif (
        transfer_transaction.transfer_provider == DisbursementProviderType.WEMA_PROVIDER
    ):
        from loans.helpers.core_banking import CoreBankingManager

        verification = CoreBankingManager.verify_from_wema(
            ref=reference,
        )
    elif transfer_transaction.transfer_provider == DisbursementProviderType.PAYSTACK_PROVIDER:
        from accounts.paystack import PaystackApi
        paystack = PaystackApi()
        verification = paystack.verify_transaction_status(
            reference=reference,
        )
    elif transfer_transaction.transfer_provider == DisbursementProviderType.CASH_CONNECT:
        from loans.helpers.core_banking import CoreBankingManager

        verification = CoreBankingManager().verify_connect_transaction_status(
            reference=str(reference),
        )

    else:
        return {
            "status": False,
            "message": "Disbursement provider not found, customer care will confirm the case manually",
            "data": {},
        }

    print("$$$$after vev######", verification, "#################")

    # if transfer_transaction.transaction_form_type == TransactionFormType.FUND_MONNIFY_ACCOUNT:
    transfer_transaction.verification_response = verification
    transfer_transaction.save()

    # if the verification happened
    if verification.get("status"):

        if not transfer_transaction.unique_reference:
            if verification.get("data") and verification.get("data").get("escrow_id"):
                transfer_transaction.unique_reference = verification.get("data").get(
                    "escrow_id"
                )
                transfer_transaction.save()

        # if the transfer went through
        if verification.get("transferred"):
            transfer_transaction.status = Status.SUCCESS
            transfer_transaction.save()

            if vfd_transfer:
                vfd_transfer.vfd_transfer_status = Status.SUCCESS
                vfd_transfer.save()
                vfd_transfer.vfd_transfer_count += 1
                vfd_transfer.save()
            elif borrower_account_transfer:
                borrower_account_transfer.borrower_account_transfer_status = (
                    Status.SUCCESS
                )
                borrower_account_transfer.save()
            else:
                pass

            vfd_disbursement_log and vfd_disbursement_log.update(status=Status.SUCCESS)

            if (
                transfer_transaction.transaction_form_type
                == TransactionFormType.DISBURSE_TO_EXTERNAL_ACCOUNT
            ):
                transfer_transaction.verification_response = verification
                transfer_transaction.save()

                loan_instance = AjoLoan.objects.filter(
                    loan_ref=transfer_transaction.quotation_id,
                    agent=transfer_transaction.user,
                    borrower=transfer_transaction.onboarded_user,
                ).update(
                    status=LoanStatus.OPEN,
                    supervisor_disbursement_status=DisbursementStatus.SUCCESSFUL,
                )

                try:
                    from loans.tasks import failed_disbursements_supervisor_notification
                    if loan_instance.loan_type == LoanType.MERCHANT_OVERDRAFT:
                        merchants_overdraft_admin_emails = ConstantTable.objects.last().merchant_overdraft_admin_emails

                        for email in merchants_overdraft_admin_emails:
                            failed_disbursements_supervisor_notification(
                                email=email,
                                borrower_phone_number=loan_instance.borrower.phone_number,
                                amount=loan_instance.amount,
                                full_name=f"{loan_instance.borrower.first_name} {loan_instance.borrower.last_name}",
                                title="Merchant Loan Disbursement Notification",
                                subject="Merchant Loan Disbursement Notification",
                            )
                except:
                    pass

            return {
                "status": True,
                "message": "transfer confirmed successfully",
            }

        # if the transfer was reversed
        elif verification.get("reversed"):
            if (
                transfer_transaction.transfer_provider
                == DisbursementProviderType.VFD_PROVIDER
            ):
                AjoLoan.objects.filter(
                    loan_ref=transfer_transaction.quotation_id,
                    agent=transfer_transaction.user,
                    borrower=transfer_transaction.onboarded_user,
                ).update(
                    failed_to_disbursed_via_vfd=True,
                    failed_disbursement_payload=verification,
                    last_failed_disbursement_attempt=timezone.now(),
                )

            if vfd_transfer:
                vfd_transfer.vfd_transfer_status = Status.PENDING
                vfd_transfer.save()
            elif borrower_account_transfer:
                borrower_account_transfer.borrower_account_transfer_status = (
                    Status.PENDING
                )
                borrower_account_transfer.save()

            vfd_disbursement_log and vfd_disbursement_log.update(status=Status.FAILED)
            try:
                payment_actions.perform_reversal_for_failed_external_transfers(
                    transaction=transfer_transaction
                )
            except Exception as err:
                return {
                    "status": False,
                    "message": f"encountered error during reversal: {err}",
                }

            return {
                "status": True,
                "message": "transfer was reversed and settled into spend successfully",
            }

    return {
        "status": True,
        "message": "customer care will confirm every other case manually",
        "data": verification,
    }


@shared_task
def clear_duplicate_bank_account_records():
    from collections import defaultdict

    valid_form_types = [AccountFormType.AJO_DIGITAL, AccountFormType.AJO_SPENDING]

    bank_accounts = BankAccountDetails.objects.filter(form_type__in=valid_form_types)

    account_details: Dict[str, List[BankAccountDetails]] = defaultdict(list)
    for account in bank_accounts:
        key = account.account_number
        account_details[key].append(account)

    info = {}
    for key, accounts in account_details.items():
        if len(accounts) > 1:
            # Keep the first occurrence and delete the duplicates
            for duplicate_account in accounts[1:]:
                original_account = accounts[0]

                if (
                    duplicate_account.account_number != original_account.account_number
                    or duplicate_account.account_name != original_account.account_name
                    or duplicate_account.ajo_user != original_account.ajo_user
                    or duplicate_account.form_type != original_account.form_type
                ):
                    continue

                duplicate_account.delete()
                info[str(duplicate_account.ajo_user)] = (
                    f"deleted duplicate: {duplicate_account.account_number}-{duplicate_account.form_type}"
                )

    return info


@shared_task
def add_monnify_status_to_monnify_transactions():
    _2_months_ago = timezone.now() - timezone.timedelta(days=60)
    monnify_transactions_qs = Transaction.objects.filter(
        # transaction_form_type=TransactionFormType.DISBURSE_TO_EXTERNAL_ACCOUNT,
        # status=Status.FAILED,
        transfer_provider=DisbursementProviderType.MONNIFY_PROVIDER,
        date_created__date__gte=_2_months_ago.date(),
    )

    monnify_class = Monnify()
    for transfer_transaction in monnify_transactions_qs:
        verification = monnify_class.verify_monnify_disbursement(
            reference=transfer_transaction.transaction_id,
        )

        try:
            if verification:
                monnify_status = (
                    verification.get("response_payload", {})
                    .get("responseBody", {})
                    .get("status")
                )

                transfer_transaction.monnify_status = monnify_status
                transfer_transaction.save()
        except Exception as e:
            continue


@shared_task
def transfer_from_loans_to_savings():
    from accounts.models import CustomUser as User
    from loans.models import TransactionService
    from accounts.agency_banking import check_balances, loan_agent_login
    from payment.model_choices import (
        TransactionFormType,
        TransactionTypeCreditOrDebitChoices,
        Status,
    )

    amount = 500000
    loans_access_token = loan_agent_login(token_not_valid=True).get("access")
    transaction_pin = settings.LOAN_AGENCY_BANKING_TRANSACTION_PIN

    # Check repayment account balance
    available_balance = check_balances(
        login_purpose="LOAN", access_token=loans_access_token
    )

    if available_balance < amount:
        return "Insufficient balance in loans account; Balance is {available_balance}"

    # Get savings User
    savings_user_email = settings.AGENCY_BANKING_USEREMAIL
    savings_user = User.objects.filter(email=savings_user_email).last()
    savings_phone_number = savings_user.user_phone

    # Get Loans account Phone number
    loans_user_email = settings.LOAN_AGENCY_BANKING_USEREMAIL
    loans_user = User.objects.filter(email=loans_user_email).last()

    transfer_transaction = TransactionService.create_internal_transfer_between_accounts(
        user=loans_user,
        amount=amount,
        transaction_description="Transfer from loans account to savings account",
        transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
        transaction_form_type=TransactionFormType.INTERNAL_TRANSFER_TO_LOANS,
        status=Status.PENDING,
    )
    reference = str(transfer_transaction.transaction_id)
    narration = "Transfer from loans account to savings account"

    request_data = {
        "from_wallet_type": "COLLECTION",
        "to_wallet_type": "COLLECTION",
        "data": [
            {
                "buddy_phone_number": savings_phone_number,
                "amount": amount,
                "narration": narration,
                "is_beneficiary": "False",
                "save_beneficiary": "False",
                "remove_beneficiary": "False",
                "is_recurring": "False",
                "custom_reference": reference,
            }
        ],
        "transaction_pin": "",
    }

    send_money_to_loans = AgencyBankingClass.send_money_from_an_account_in_liberty_to_user_through_pay_buddy(
        transaction_pin=transaction_pin,
        phone_number=savings_phone_number,
        amount=amount,
        transaction_reference=reference,
        access_token=loans_access_token,
        narration=narration,
    )

    if send_money_to_loans.get("data").get("message") == "success":
        transfer_transaction.status = Status.SUCCESS
    else:
        transfer_transaction.status = Status.FAILED

    transfer_transaction.request_data = request_data
    transfer_transaction.payload = send_money_to_loans
    transfer_transaction.save()

    return send_money_to_loans


@shared_task
def reconcile_spend_wallet():
    from payment.models import WalletSystem
    from payment.model_choices import WalletTypes, TransactionTypeCreditOrDebitChoices

    spend_wallet_qs = WalletSystem.objects.filter()

    for wallet in spend_wallet_qs:
        available_balance = wallet.available_balance

        total_credit = Transaction.objects.filter(
            onboarded_user=wallet.onboarded_user,
            wallet_type=WalletTypes.AJO_SPENDING,
            transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT
        ).aggregate(total_amount=Sum("amount"))["total_amount"] or 0.00

        total_debit = Transaction.objects.filter(
            onboarded_user=wallet.onboarded_user,
            wallet_type=WalletTypes.AJO_SPENDING,
            transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT
        ).aggregate(total_amount=Sum("amount"))["total_amount"] or 0.00

        accurate_balance = total_credit - total_debit
        offset = accurate_balance - available_balance


@shared_task
def update_transactions_with_wallet_ids():
    from payment.models import Transaction, WalletSystem

    transactions = Transaction.objects.filter(wallet_id__isnull=True)
    for transaction in transactions:

        wallet = WalletSystem.objects.filter(
            user=transaction.user,
            onboarded_user=transaction.onboarded_user,
            wallet_type=transaction.wallet_type,
        ).last()

        if wallet:
            transaction.wallet_id = wallet.id
            transaction.save()


@shared_task
def reconcile_spend_wallet_transactions():
    from ajo.models import AjoUser
    from payment.models import WalletTransactionReconciliation

    ajo_users_qs = AjoUser.objects.filter()

    for ajo_user in ajo_users_qs:
        transactions_qs = Transaction.objects.filter(
            onboarded_user=ajo_user,
            )

        disburse_to_external_amount = transactions_qs.filter(
            transaction_form_type=TransactionFormType.DISBURSE_TO_EXTERNAL_ACCOUNT
        ).aggregate(total_amount=Sum("amount"))["total_amount"] or 0.00

        disburse_to_spend_amount = transactions_qs.filter(
            transaction_form_type=TransactionFormType.DISBURSEMENT_TO_SPEND
        ).aggregate(total_amount=Sum("amount"))["total_amount"] or 0.00

        transfer_to_external_amount = transactions_qs.filter(
            transaction_form_type=TransactionFormType.DISBURSEMENT_TO_SPEND
        ).aggregate(total_amount=Sum("amount"))["total_amount"] or 0.00

        reversal_amount = transactions_qs.filter(
            transaction_form_type=TransactionFormType.REVERSAL_EXTERNAL_TRANSFER
        ).aggregate(total_amount=Sum("amount"))["total_amount"] or 0.00

        difference_spend_disburse = transfer_to_external_amount - disburse_to_spend_amount
        difference_disburse_external = transfer_to_external_amount - disburse_to_external_amount

        inst = WalletTransactionReconciliation.objects.filter(ajo_user=ajo_user).last()

        if inst:
            inst.disburse_to_external_amount=disburse_to_external_amount
            inst.disburse_to_spend_amount=disburse_to_spend_amount
            inst.transfer_to_external_amount=transfer_to_external_amount
            inst.reversal_amount=reversal_amount
            inst.difference_spend_disburse=difference_spend_disburse
            inst.difference_disburse_external=difference_disburse_external
            inst.save()
        else:
            inst = WalletTransactionReconciliation.objects.create(
                ajo_user=ajo_user,
                disburse_to_external_amount=disburse_to_external_amount,
                disburse_to_spend_amount=disburse_to_spend_amount,
                transfer_to_external_amount=transfer_to_external_amount,
                reversal_amount=reversal_amount,
                difference_spend_disburse=difference_spend_disburse,
                difference_disburse_external=difference_disburse_external,
            )

        return inst