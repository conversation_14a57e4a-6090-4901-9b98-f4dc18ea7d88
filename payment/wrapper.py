import json
import uuid
from dataclasses import dataclass

import requests
from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.db import IntegrityError
from requests.structures import CaseInsensitiveDict

from accounts.models import RecurrentChargeLogs

from .model_choices import PlanType, Status, TransactionFormType, WalletTypes
from .models import WalletSystem
from .services import TransactionService


@dataclass
class PayStack:
    # if settings.ENVIRONMENT == "development":
    #     access_token = settings.PAYSTACK_BEARER_TEST

    # elif settings.ENVIRONMENT == "production":
    #     access_token = settings.PAYSTACK_BEARER_LIVE

    access_token = settings.PAYSTACK_AUTHORIZATION_TOKEN
    base_url = "https://api.paystack.co"

    @classmethod
    def initialize_transaction(cls, email: str, amount: float) -> dict:
        """_summary_

        Args:
            email (str): the email of the user
            amount (float): the amount to be paid, it should be in Naira

        Returns:
            dict: _description_
        """
        url = f"{cls.base_url}/transaction/initialize"

        headers = CaseInsensitiveDict()
        headers["Authorization"] = f"Bearer {cls.access_token}"
        headers["Content-Type"] = "application/json"

        # convert the amount to kobo
        amount = int(float(amount) * 100)

        data = json.dumps(
            {
                "email": email,
                "amount": amount,
                "reference": "SAVINGS-" + "CR-" + str(uuid.uuid4()),
                "channels": ["card"],
                "metadata": {
                    "display_name": "savings_credit",
                    "variable_name": "Credit Transaction",
                    "value": email + " credited savings with " + str(amount / 100),
                    "unique_name": "LIBERTYSAVINGS",
                },
            }
        )

        try:
            response = requests.post(
                url=url,
                headers=headers,
                data=data,
            )
            return response.json()

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "error_message": str(err),
            }

    @classmethod
    def verify_transaction(cls, transaction_reference: str):
        url = f"https://api.paystack.co/transaction/verify/{transaction_reference}"

        headers = CaseInsensitiveDict()
        headers["Authorization"] = f"Bearer {cls.access_token}"

        try:
            resp = requests.get(url, headers=headers)
            return resp.json()

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "error": str(err),
            }

    @staticmethod
    def charges_on_amount_per_trnx(amount):
        if str(amount) == "":
            return 0

        else:
            value = float(amount)
            if value < 2500:
                # 1.5 percent charge of amount
                percent_charge = (1.5 * value) / 100
                return percent_charge

            elif value >= 2500:
                # 1.5 percent charge + 100
                percent_charge = (1.5 * value) / 100
                fee = percent_charge + 100

                if fee > 2000:
                    return 2000
                else:
                    return fee

    @classmethod
    def charge_reusable_card(
        cls,
        email: str,
        amount: float,
        auth_code: str,
        unique_card_reference: str | None,
    ) -> dict:
        url = f"{cls.base_url}/transaction/charge_authorization"

        # deal with the money
        total_in_naira = float(amount)
        # TODO find out if the charges still work for the paystack
        # # charge_per_transaction = self.charges_on_amount_per_trnx(amount=value)
        # total_amount = charge_per_transaction + value
        total_in_kobo = round(total_in_naira * 100)  # Round to the nearest integer

        # check if there is a unique_card_reference
        if unique_card_reference:
            reference = unique_card_reference
        else:
            reference = "SAVINGS-" + "CR-" + str(uuid.uuid4())

        headers = CaseInsensitiveDict()
        headers["Authorization"] = f"Bearer {cls.access_token}"
        headers["Content-Type"] = "application/json"

        data = json.dumps(
            {
                "authorization_code": auth_code,
                "email": email,
                "amount": str(total_in_kobo),
                "reference": reference,
                "channels": ["card"],
                "metadata": {
                    "display_name": "savings_credit",
                    "variable_name": "Credit Transaction",
                    "value": email + " credited savings with " + str(total_in_kobo / 100),
                    "unique_name": "LIBERTYSAVINGS",
                },
            }
        )

        try:
            response = requests.post(url=url, headers=headers, data=data)
            res = response.json()
            return {
                "status": True,
                "data": res,
            }

        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "error_message": str(err),
            }


def create_transaction_and_charge_card(
    user: AbstractUser,
    amount: str,
    auth_code: str,
    quotation_id: str,
    plan_type: str,
    transaction_form_type: TransactionFormType = TransactionFormType.CARD_DEPOSIT,
) -> dict | Exception:
    """_summary_

    Args:
        email (str): _description_
        amount (str): _description_
        auth_code (str): _description_

    Returns:
        dict: _description_
    """

    # create transaction instance
    try:
        # generate a unique_reference
        reference = "SAVINGS-" + "CR-" + str(uuid.uuid4())

        transaction = TransactionService.create_deposit_by_card_transaction(
            user=user,
            amount=amount,
            quotation_id=quotation_id,
            plan_type=getattr(PlanType, plan_type.upper()),
            wallet_type=getattr(WalletTypes, plan_type.upper()),
            unique_reference=reference,
            transaction_form_type=transaction_form_type,
        )
    except IntegrityError as err:
        if "unique constraint" in str(err) and "unique_reference" in str(err).lower():
            # generate another unique_reference
            reference = "SAVINGS-" + "CR-" + str(uuid.uuid4())
            transaction = TransactionService.create_deposit_by_card_transaction(
                user=user,
                amount=amount,
                quotation_id=quotation_id,
                plan_type=getattr(PlanType, plan_type.upper()),
                wallet_type=getattr(WalletTypes, plan_type.upper()),
                unique_reference=reference,
                transaction_form_type=transaction_form_type,
            )
        else:
            # re-raise the original exception with a custom error message
            raise IntegrityError("Failed to save model due to integrity constraint violation") from err

    charge = PayStack.charge_reusable_card(
        email=user.email,
        amount=amount,
        auth_code=auth_code,
        unique_card_reference=reference,
    )

    RecurrentChargeLogs.objects.create(
        user=user,
        charge_type="DEBIT_CARD",
        charge_call_response=charge,
        plan_type=plan_type
    )


    # returns "status" and "data" which is the paystack response

    # ensure that the charge was attempted
    if charge.get("status") is True and charge.get("data").get("status") is True:
        # check if the charge was successful
        if charge.get("data").get("data").get("status") == "success":
            # Update the information of the transaction
            # change the status
            transaction.status = Status.SUCCESS

            # Collect the transaction completed date, payload
            transaction.transaction_date_completed = charge.get("data").get("data").get("transaction_date")
            transaction.payload = json.dumps(charge.get("data"))
            transaction.save()

            # Get the wallet of the user
            wallet = WalletSystem.get_or_create_wallet(user=user, wallet_type=getattr(WalletTypes, plan_type.upper()))

            # fund the wallet
            fund = WalletSystem.fund_balance(wallet=wallet, amount=amount, transaction_instance=transaction)

            return {
                "status": True,
                "transaction": transaction,
                "debit_credit_info": fund,
                "message": charge,
            }

        elif charge.get("data").get("data").get("status") == "failed":
            # insufficient balance
            if charge.get("data").get("data").get("gateway_response") == "Insufficient Balance":
                # set the reason the failed transaction
                transaction.failure_reason = charge.get("data").get("data").get("gateway_response")
                transaction.status = Status.FAILED
                transaction.payload = json.dumps(charge.get("data"))
                transaction.save()

                # return the insufficient balance in the response
                return {
                    "status": False,
                    "transaction": transaction,
                    "message": "insufficient balance, please fund your account",
                }

            # any other reason for the failed transaction
            else:
                # set the reason for the failed transaction
                transaction.failure_reason = charge.get("data").get("data").get("gateway_response")
                transaction.status = Status.FAILED
                transaction.payload = json.dumps(charge.get("data"))
                transaction.save()

                # return the response
                return {
                    "status": False,
                    "transaction": transaction,
                    "message": charge.get("data").get("data").get("gateway_response"),
                }

        else:
            # any other reason
            # setting it to failed because the webhook might say different
            # set the reason for the failed transaction
            transaction.failure_reason = charge.get("data").get("data").get("gateway_response")
            transaction.status = Status.FAILED
            transaction.payload = json.dumps(charge.get("data"))
            transaction.save()

            # return the response
            return {
                "status": False,
                "transaction": transaction,
                "message": charge.get("data").get("data").get("gateway_response"),
            }

    # in this case, it might have failed, awaiting response from the webhook if otherwise
    else:
        # setting the transaction to failed because the charging request either did not go through or encountered an error
        transaction.failure_reason = charge.get("error_message")
        transaction.status = Status.FAILED
        # No payload
        # transaction.payload = json.dumps(charge.get("data"))
        transaction.save()

        # return the response
        return {
            "status": False,
            "transaction": transaction,
            "message": "error while charging card, please try again in a while",
        }


# {
#     "event": "charge.success",
#     "data": {
#         "id": 2825214690,
#         "domain": "test",
#         "status": "success",
#         "reference": "SAVINGS-CR-52305325-be8b-4d0a-972d-7569cc872fe0",
#         "amount": ********,
#         "message": None,
#         "gateway_response": "Successful",
#         "paid_at": "2023-05-23T12:23:17.000Z",
#         "created_at": "2023-05-23T12:22:46.000Z",
#         "channel": "card",
#         "currency": "NGN",
#         "ip_address": "**************",
#         "metadata": {
#             "display_name": "savings_credit",
#             "variable_name": "Credit Transaction",
#             "value": "<EMAIL> credited savings with 210000.0",
#             "unique_name": "LIBERTYSAVINGS",
#         },
#         "fees_breakdown": None,
#         "log": None,
#         "fees": 200000,
#         "fees_split": None,
#         "authorization": {
#             "authorization_code": "AUTH_6em9ndco60",
#             "bin": "408408",
#             "last4": "4081",
#             "exp_month": "12",
#             "exp_year": "2030",
#             "channel": "card",
#             "card_type": "visa ",
#             "bank": "TEST BANK",
#             "country_code": "NG",
#             "brand": "visa",
#             "reusable": True,
#             "signature": "SIG_KxhRL8S6MFLpx7lhDstk",
#             "account_name": None,
#             "receiver_bank_account_number": None,
#             "receiver_bank": None,
#         },
#         "customer": {
#             "id": *********,
#             "first_name": None,
#             "last_name": None,
#             "email": "<EMAIL>",
#             "customer_code": "CUS_0ouagxcsyzt0k4z",
#             "phone": None,
#             "metadata": None,
#             "risk_action": "default",
#             "international_format_phone": None,
#         },
#         "plan": {},
#         "subaccount": {},
#         "split": {},
#         "order_id": None,
#         "paidAt": "2023-05-23T12:23:17.000Z",
#         "requested_amount": ********,
#         "pos_transaction_data": None,
#         "source": {
#             "type": "api",
#             "source": "merchant_api",
#             "entry_point": "transaction_initialize",
#             "identifier": None,
#         },
#     },
# }
