from django.urls import path

from .views import (
    CardsResponseAfterChargeAPIView,
    ContinueSavingsPlanAPIView,
    DeleteSavingsPlanAPIView,
    GetCardsAPIView,
    PauseSavingsPlanAPIView,
    PayForOnlendingPlanAPIView,
    PayWiseWaitListView,
    PaystackWebHookURLAPIView,
    SaveFromAgencyWalletAPIViewV2,
    SaveFromCardAPIView,
    SaveFromCardAPIViewV2,
    SaveFromNewCardAPIView,
    SaveFromWalletAPIView,
    SavingsBalanceSummary,
    SettlePendingTrasfersAPIView,
    VerifyMonnifyTransactionAPIView,
    VerifyWithdrawalAccountDetailsAPIView,
    WithdrawalAccountAPIView,
    WithdrawToWalletAPIView,
    AgencyBankCards,
    ManualWalletDepletionView,
)

urlpatterns = [
    path("get-cards/", GetCardsAPIView.as_view(), name="get_cards"),
    path("savings-balance-summary/", SavingsBalanceSummary.as_view(), name="savings_balance_summary"),
    path("save-from-wallet/", SaveFromWalletAPIView.as_view(), name="save_from_wallet"),
    path("save-from-card/", SaveFromCardAPIView.as_view(), name="save_from_card"),
    path("withdraw-to-wallet/", WithdrawToWalletAPIView.as_view(), name="withdraw_to_wallet"),
    path("save-from-new-card/", SaveFromNewCardAPIView.as_view(), name="save_from_new_card"),
    path("checkout-response/", CardsResponseAfterChargeAPIView.as_view(), name="checkout_response"),
    # URL to get the details of one card
    # path("carddetails/", RetrieveOneCardDetailsAPIView.as_view(), name="card-details"),
    # URL to initiate a one time payment
    # path("initiate-card-payment/", OneTimePaymentAPIView.as_view(), name="initiate_card_payment"),
    path("webhook/", PaystackWebHookURLAPIView.as_view(), name="paystack_webhook_url"),
    path("manage/pause-plan/", PauseSavingsPlanAPIView.as_view(), name="pause_a_savings_plan"),
    path("manage/continue-plan/", ContinueSavingsPlanAPIView.as_view(), name="continue_a_savings_plan"),
    path("manage/delete-plan/", DeleteSavingsPlanAPIView.as_view(), name="delete_a_savings_plan"),
    path("save-from-wallet-v2/", SaveFromAgencyWalletAPIViewV2.as_view(), name="save_from_wallet_v2"),
    path("save-from-card-v2/", SaveFromCardAPIViewV2.as_view(), name="save_from_card-v2"),
    path("pay-for-onlending-plan/", PayForOnlendingPlanAPIView.as_view(), name="pay-for-onlending-plan"),
    path("withdrawal-account/", WithdrawalAccountAPIView.as_view(), name="withdrawal-account"),
    path("withdrawal-account/verify/", VerifyWithdrawalAccountDetailsAPIView.as_view(), name="verify-account"),
    path("settle-pending-transactions/", SettlePendingTrasfersAPIView.as_view(), name="pending_transactions"),
    path(
        "monnify-transaction/verify/",
        VerifyMonnifyTransactionAPIView.as_view(),
        name="verify_monnify_transaction_status",
    ),
    path("agency-bank-cards/", AgencyBankCards.as_view(), name="agency_bank_cards"),
    path("manually-deplete-wallet/", ManualWalletDepletionView.as_view(), name="manual_wallet_depletion"),
    path("add_to_wait_list/", PayWiseWaitListView.as_view(), name="add_to_wait_list"),
]
