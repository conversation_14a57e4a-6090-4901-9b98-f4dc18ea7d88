import json
from abc import ABC, abstractmethod
from copy import deepcopy
from typing import Any, Dict

from celery import shared_task
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.db import transaction as django_transaction

from accounts.agency_banking import AgencyBankingClass
from accounts.services import InterestsPaidTableMethods
from savings.celery import app

from .checks import obtain_phone_number, verify_account
from .model_choices import PlanType, Status, WalletTypes
from .models import Transaction, WalletSystem
from .services import TransactionService
from .utils import Utility, convert_string_to_aware_datetime_object
from .wrapper import PayStack


@shared_task
def pay_upfront_interest(user_id: int, amount: float, quotation_id: str, plan_type: PlanType, access_token: str):
    # obtain the user model
    try:
        user = get_user_model().objects.get(id=user_id)
    except get_user_model().DoesNotExist:
        raise ValueError(f"could not retrieve this user with id {user_id} from the database")

    # create the transaction instance
    transaction = TransactionService.create_upfront_interest_to_wallet_transaction(
        user=user,
        amount=amount,
        quotation_id=quotation_id,
        plan_type=plan_type,
    )

    # call the function to obtain the phone number
    phone_number = obtain_phone_number(access_token=access_token, user_id=user.customer_user_id)

    # call the function to verify
    verification, verification_data = verify_account(access_token=access_token, phone_number=phone_number)

    if verification:
        # call the function that sends the money to the user's wallet
        send_interest_dict = AgencyBankingClass.send_money_from_liberty_to_user_through_pay_buddy(
            phone_number=phone_number,
            amount=amount,
            transaction_reference=str(transaction.transaction_id),
        )

        # get the data object in the send_interest_dict object
        data = send_interest_dict.get("data")
        print(data)

        # check if the status is True
        if send_interest_dict.get("status"):
            # Ensure that the money was successfully sent
            if "message" in data.keys() and data.get("message") == "success":
                # change the status
                transaction.status = Status.SUCCESS
                # collect the escrow_id and date completed
                transaction.transaction_date_completed = convert_string_to_aware_datetime_object(
                    data.get("date_completed")
                )
                transaction.unique_reference = data.get("data").get("escrow_id")

                # update the transaction balances information
                # note that these remain the same as their previous values
                # create Interest paid table

                # Firstly obtain the wallet
                wallet = WalletSystem.get_or_create_wallet(
                    user=user,
                    wallet_type=getattr(WalletTypes, plan_type.upper()),
                )

                # Obtain the plan instance
                plan_instance = Utility.get_plan_instance(quotation_id=quotation_id, user=user)

                # then go ahead to update the other fields
                # the transaction wallet balance before and after will be equal to the amount present
                transaction.wallet_balance_before = transaction.wallet_balance_after = wallet.available_balance

                # the transaction's plan balance before and after will be equal to the amount saved in the plan
                transaction.plan_balance_before = transaction.plan_balance_after = plan_instance.amount_saved

                # log the data received
                transaction.payload = json.dumps(data)
                # save the changes
                with django_transaction.atomic():
                    transaction.save()

                    InterestsPaidTableMethods.create_upfront_interest_instance(
                        user=user,
                        plan_type=PlanType.CHESTLOCK,
                        plan_quotation_id=plan_instance.quotation_id,
                        total_interest_paid=amount,
                    )

                return {
                    "message": "interest paid successfully",
                    "data": transaction.payload,
                }

        # check if the status is False
        else:
            # this is should indicate that the upfront payment was not successfully
            # change the status
            transaction.status = Status.FAILED
            # log the failure reason
            transaction.failure_reason = data.get("message")
            # log the data received
            transaction.payload = json.dumps(data)
            # save it
            transaction.save()

            return {
                "message": f"an error occurred",
                "data": transaction.payload,
            }

    else:
        return {"message": "the account to make the transfer to is not valid"}


class PaymentProcessor(ABC):
    @abstractmethod
    def pay(cls, amount: float, wallet: WalletSystem):
        pass


class UserAgencyBankingWalletPaymentProcessor(PaymentProcessor):
    @classmethod
    def charge_user_wallet(
        cls,
        user: AbstractUser,
        amount: float,
        transaction_pin: str,
        transaction_id: str,
        super_token: str | None = None,
    ):
        charge = AgencyBankingClass.charge_user_agency_banking_wallet(
            user=user,
            transaction_pin=transaction_pin,
            amount=amount,
            transaction_id=transaction_id,
            super_token=super_token,
        )
        return charge

    @classmethod
    def pay(
        cls,
        wallet: WalletSystem,
        amount: float,
        transaction_pin: str,
        transaction: Transaction,
        user: AbstractUser,
        super_token: str | None = None,
    ) -> Dict[str, Any]:
        charge = cls.charge_user_wallet(
            user=user,
            amount=amount,
            transaction_pin=transaction_pin,
            transaction_id=transaction.transaction_id,
            super_token=super_token,
        )

        response = {
            "status": None,
            "transaction": None,
            "debit_credit_info": None,
            "data": charge,
            "message": "",
        }

        charge_status = charge.get("status")

        if charge_status:
            try:
                with django_transaction.atomic():
                    response["debit_credit_info"] = cls.handle_wallet_funding(
                        wallet=wallet,
                        transaction=transaction,
                        amount=amount,
                    )
                    response["transaction"] = cls.handle_transaction_update(charge=charge, transaction=transaction)

                response["status"] = True
            except Exception as err:
                django_transaction.rollback()
                response["status"] = False
                response["message"] = str(err)

        else:
            response["transaction"] = cls.handle_transaction_update(charge=charge, transaction=transaction)
            response["status"] = False
            response["message"] = charge.get("message")

        return response

    @classmethod
    def handle_wallet_funding(
        cls,
        wallet: WalletSystem,
        transaction: Transaction,
        amount: float,
    ) -> Dict[str, Any]:
        fund = WalletSystem.fund_balance(
            wallet=wallet,
            amount=amount,
            transaction_instance=transaction,
        )
        return fund

    @classmethod
    def handle_transaction_update(
        cls,
        charge: Dict[str, Any],
        transaction: Transaction,
    ) -> Transaction:
        transaction_service = TransactionService(transaction)

        if charge.get("status"):
            data = charge.get("data")
            transaction = transaction_service.set_successful_transaction(
                payload=json.dumps(charge),
                unique_reference=data.get("data").get("escrow_id"),
                transaction_date_completed=convert_string_to_aware_datetime_object(data.get("date_completed")),
            )

        else:
            transaction = transaction_service.set_failed_transaction(
                failure_reason=charge.get("message"),
                payload=json.dumps(charge),
            )

        return transaction


class PaystackPaymentProcessor:
    paystack = PayStack()

    @classmethod
    def charge_reusable_card(
        cls,
        email: str,
        amount: float,
        auth_code: str,
        unique_card_reference: str | None,
    ) -> Dict[str, Any]:
        """
        This returns a True status if the charging was successful,
        every other thing is False.

        Args:
            email (str): the email of the person.
            amount (float): the amount to be charged in Naira.
            auth_code (str): _description_
            unique_card_reference(str | None): a unique reference to be used

        Returns:
            Dict[str, Any]: _description_
        """
        charge = cls.paystack.charge_reusable_card(
            email=email,
            amount=amount,
            auth_code=auth_code,
            unique_card_reference=unique_card_reference,
        )

        data = charge.get("data") if charge.get("data") else charge

        response = {
            "status": False,
            "data": data,
        }

        if data.get("status") and data.get("data").get("status") == "success":
            response["status"] = True

        return response

    @classmethod
    def initialize_transaction(
        cls,
        email: str,
        amount: float,
    ):
        """
        This creates a payment invoice

        Args:
            email (str): the email of the person making the payment.
            amount (float): the amount to be paid in Naira.
        """
        return cls.paystack.initialize_transaction(
            email=email,
            amount=amount,
        )

    @classmethod
    def verify_transaction(
        cls,
        reference: str,
    ) -> Dict[str, Any]:
        """
        If the transaction was successful, it will return a True,
        anything else will return a False.

        Returns:
            Dict[str, Any]: a dictionary containing the status and the data
        """
        verification = cls.paystack.verify_transaction(transaction_reference=reference)

        response = {
            "status": False,
            "data": verification,
        }

        if verification.get("data").get("status") == "success":
            response["status"] = True

        return response


class SavedAgencyBankingDebitCardPaymentProcessor(UserAgencyBankingWalletPaymentProcessor, PaystackPaymentProcessor):
    @classmethod
    def get_cards(
        cls,
        user_access_token: str,
    ) -> Dict[str, bool | Dict[str, str]]:
        """_summary_

        Args:
            user_access_token (str): _description_

        Returns:
            _type_: _description_
        """
        cards = AgencyBankingClass.get_user_cards(access_token=user_access_token)
        data = cards.get("data") if cards.get("data") else cards

        response = {
            "status": False,
            "data": data,
        }

        if cards.get("status") and cards.get("message") == "Cards Retrieved":
            response["status"] = True

        return response

    @classmethod
    def card_in_saved_debit_cards(
        cls,
        masked_pan: str,
        user_access_token: str,
    ) -> Dict[str, bool | str]:
        """
        This checks if the card is in the saved debit cards on Agency Banking and returns True or False
        in a status, as well as the card's auth_code

        Args:
            masked_pan (str): the masked pan of the card.
            user_access_token (str): the user's access token

        Raises:
            ValueError: there was an error obtaining the saved cards

        Returns:
            Dict[str, bool | str]: the dictionary of the status and the auth_code.
        """
        cards = cls.get_cards(user_access_token=user_access_token)

        if not cards.get("status"):
            raise ValueError("there was an error obtaining the cards")

        response = {
            "status": False,
            "auth_code": None,
        }

        for card in cards.get("data"):
            if masked_pan in card.values():
                response["auth_code"] = card.get("authorization_code")
                response["status"] = True

        return response

    @classmethod
    def pay(
        cls,
        amount: float,
        masked_pan: str,
        access_token: str,
        transaction: Transaction,
        user: AbstractUser,
        wallet: WalletSystem,
        auth_code: str | None = None,
    ) -> Dict[str, Any]:
        if not auth_code:
            card_info = cls.card_in_saved_debit_cards(
                masked_pan=masked_pan,
                user_access_token=access_token,
            )

            if not card_info.get("status"):
                raise ValueError("this card is not a saved debit card")

            auth_code = card_info.get("auth_code")

        charge_card = cls.charge_reusable_card(
            email=user.email,
            amount=amount,
            auth_code=auth_code,
            unique_card_reference=transaction.unique_reference,
        )

        response = {
            "status": None,
            "transaction": None,
            "debit_credit_info": None,
            "data": charge_card,
            "message": "",
        }

        charge_card_status = charge_card.get("status")

        if charge_card_status:
            try:
                with django_transaction.atomic():
                    response["debit_credit_info"] = cls.handle_wallet_funding(
                        wallet=wallet,
                        transaction=transaction,
                        amount=amount,
                    )
                    response["transaction"] = cls.handle_transaction_update(
                        charge=charge_card,
                        transaction=transaction,
                    )

                response["status"] = True
            except Exception as err:
                django_transaction.rollback()
                response["status"] = False
                response["message"] = str(err)
        else:
            response["transaction"] = cls.handle_transaction_update(charge=charge_card, transaction=transaction)
            response["status"] = False

        return response

    @classmethod
    def handle_transaction_update(
        cls,
        charge: Dict[str, Any],
        transaction: Transaction,
    ) -> Transaction:
        transaction_service = TransactionService(transaction_instance=transaction)

        presave_charge = deepcopy(charge)
        if presave_charge.get("data") is not None and presave_charge["data"].get("data") is not None:
            presave_charge["data"]["data"].pop("authorization", None)
        payload = json.dumps(presave_charge)

        if charge.get("status"):
            charge_data = charge.get("data")
            transaction = transaction_service.set_successful_transaction(
                payload=payload,
                transaction_date_completed=charge_data.get("data").get("transaction_date"),
                masked_pan=f"{charge_data.get('data').get('authorization').get('bin')}******{charge_data.get('data').get('authorization').get('last4')}",
            )

        else:
            transaction = transaction_service.set_failed_transaction(
                payload=payload,
                failure_reason=charge.get("data").get("gateway_response"),
            )

        return transaction


class NewDebitCardPaymentProcessor(UserAgencyBankingWalletPaymentProcessor, PaystackPaymentProcessor):
    @classmethod
    def pay(
        cls,
        transaction: Transaction,
        wallet: WalletSystem,
    ) -> Dict[str, Any]:
        if transaction.status != Status.PENDING:
            raise ValueError(f"this transaction has already been processed as {transaction.status.lower()}")

        verification = cls.verify_transaction(reference=transaction.unique_reference)

        response = {
            "status": None,
            "transaction": None,
            "debit_credit_info": None,
            "data": verification,
            "message": "",
        }

        if verification.get("status"):
            try:
                with django_transaction.atomic():
                    response["debit_credit_info"] = cls.handle_wallet_funding(
                        wallet=wallet,
                        transaction=transaction,
                        amount=transaction.amount,
                    )
                    response["transaction"] = cls.handle_transaction_update(
                        charge=verification,
                        transaction=transaction,
                    )

                response["status"] = True
            except Exception as err:
                django_transaction.rollback()
                response["status"] = False
                response["message"] = str(err)

        else:
            response["status"] = False
            response["transaction"] = cls.handle_transaction_update(charge=verification, transaction=transaction)

        return response

    @classmethod
    def handle_transaction_update(
        cls,
        charge: Dict[str, Any],
        transaction: Transaction,
    ) -> Transaction:
        transaction_service = TransactionService(transaction_instance=transaction)

        presave_charge = deepcopy(charge)
        if presave_charge.get("data") is not None:
            presave_charge["data"].pop("authorization", None)
        payload = json.dumps(presave_charge)

        charge_data = charge.get("data")

        if charge_data.get("status") == "success":
            transaction = transaction_service.set_successful_transaction(
                payload=payload,
                transaction_date_completed=charge_data.get("paid_at"),
            )

        elif "error" in charge_data.keys():
            pass

        elif charge_data.get("status") == "failed":
            log = charge_data.get("log")
            if log:
                if not log.get("history"):
                    failure_reason = str(log)
                else:
                    failure_reason = str(log.get("history"))
            else:
                failure_reason = "Declined"

            transaction = transaction_service.set_failed_transaction(
                failure_reason=failure_reason,
                payload=payload,
            )
