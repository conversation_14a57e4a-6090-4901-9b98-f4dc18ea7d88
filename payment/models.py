import base64
import json
import uuid
from typing import Dict

from cryptography.fernet import <PERSON><PERSON><PERSON>, InvalidToken
from django.conf import settings
from django.contrib.auth.hashers import check_password, make_password
from django.core.cache import cache
from django.core.validators import MinV<PERSON>ueValidator
from django.db import IntegrityError, models
from django.db import transaction as django_transaction
from django.db.models import F, Sum
from django.utils import timezone
from rest_framework.serializers import ValidationError
from django.utils.translation import gettext as _

from accounts.helpers import BaseModel, RoundedFloatField
from ajo.models import AjoUser

from .model_choices import (
    CommissionType,
    DisbursementProviderType,
    PlanType,
    Status,
    TransactionDestination,
    TransactionFormType,
    TransactionSource,
    TransactionTypeCreditOrDebitChoices,
    WalletBalanceType,
    WalletTypes,
    TransferRouteTypes,
)


class DebitCard(models.Model):
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="user_debit_card",
        null=True,
    )

    # Card details
    authorization_code = models.CharField(
        max_length=500, editable=False, blank=True, null=True
    )
    card_type = models.CharField(max_length=100)
    card_last_four_digits = models.CharField(max_length=100)
    exp_month = models.CharField(max_length=100)
    exp_year = models.CharField(max_length=100)
    bin = models.CharField(max_length=100)
    bank_name = models.CharField(max_length=500)
    channel = models.CharField(max_length=100)
    card_signature = models.CharField(max_length=500)
    reusable = models.BooleanField(default=False)
    country_code = models.CharField(max_length=100)
    account_name = models.CharField(max_length=150, null=True, blank=True)
    brand = models.CharField(max_length=250, null=True, blank=True)

    # Customer details
    customer_id = models.CharField(
        max_length=250, editable=False, null=True, blank=True
    )
    customer_code = models.CharField(
        max_length=250, editable=False, null=True, blank=True
    )
    customer_email = models.CharField(max_length=250, null=True, blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Raw data
    payload = models.TextField()

    class Meta:
        verbose_name = "Cards"
        verbose_name_plural = "Cards"


class Transaction(models.Model):
    #### THINGS ABOUT THE TRANSACTION ####
    date_created = models.DateTimeField(default=timezone.localtime)
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name="transaction",
        null=True,
        db_index=True,
    )
    transaction_form_type = models.CharField(
        max_length=100, null=True, blank=True, choices=TransactionFormType.choices
    )
    amount = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="amount should be >= 0"),
        ],
    )
    transaction_id = models.UUIDField(
        default=uuid.uuid4, editable=False, unique=True, db_index=True
    )
    status = models.CharField(
        max_length=100, null=True, blank=True, choices=Status.choices
    )
    marked_for_excess_transfer = models.BooleanField(
        default=False, blank=True, null=True
    )
    ##if it is a deposit, choose the source type##
    transaction_source = models.CharField(
        max_length=100, null=True, blank=True, choices=TransactionSource.choices
    )
    ##if is it a withdrawal, choose the destination##
    transaction_destination = models.CharField(
        max_length=100, null=True, blank=True, choices=TransactionDestination.choices
    )
    description = models.CharField(max_length=255, null=True, blank=True)

    #### WALLET TRANSACTION INFORMATION ####
    transaction_type = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        choices=TransactionTypeCreditOrDebitChoices.choices,
    )
    wallet_type = models.CharField(
        max_length=100, null=True, blank=True, choices=WalletTypes.choices
    )
    unique_reference = models.CharField(
        max_length=100, null=True, blank=True, unique=True, db_index=True
    )
    transaction_date_completed = models.DateTimeField(null=True, blank=True)
    failure_reason = models.TextField(null=True, blank=True)

    # # CARD TRANSACTION INFORMATION
    # card_instance = models.ForeignKey(to=Card, on_delete=models.PROTECT, null=True, blank=True)
    # transaction_date = models.DateTimeField(null=True, blank=True)
    # reference = models.CharField(max_length=200, null=True, blank=True)
    masked_pan_of_card = models.CharField(max_length=100, blank=True, null=True)

    ####balance numbers####
    wallet_balance_before = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="wallet balance before should be >= 0"
            ),
        ],
    )
    wallet_balance_after = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="wallet balance after should be >= 0"
            ),
        ],
    )
    plan_balance_before = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="plan balance before should be >= 0"
            ),
        ],
    )
    plan_balance_after = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="plan balance after should be >= 0"
            ),
        ],
    )

    ####plan instance####
    # Consider the contenttype thing here though
    # plan = models.ForeignKey()

    ####This is the unique id of the saving type in order to be able to retrieve the transaction history####
    # So always add the chestlock, halal, quicksavings quotation_id here
    plan_type = models.CharField(
        max_length=100, null=True, blank=True, choices=PlanType.choices
    )
    quotation_id = models.CharField(
        max_length=120, null=True, blank=True, db_index=True
    )

    # GROUP ID FOR ROTATIONGROUP SAVINGS
    rotation_group_id = models.CharField(
        max_length=100, null=True, blank=True, db_index=True
    )

    ###AJO CREDIT INFORMATION
    credit_balance = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="credit balance after should be >= 0"
            ),
        ],
    )

    due_balance = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(
                limit_value=0.0, message="due balance after should be >= 0"
            ),
        ],
    )

    # accomodate ajo user
    onboarded_user = models.ForeignKey(
        to=AjoUser,
        related_name="ajo_user_transactions",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        db_index=True,
    )

    transaction_source_id = models.CharField(max_length=150, null=True, blank=True)
    transfer_provider = models.CharField(
        max_length=100,
        choices=DisbursementProviderType.choices,
        default=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
        null=True,
        blank=True,
    )

    ####metadata####
    last_updated = models.DateTimeField(auto_now=True)
    email_sent = models.BooleanField(default=False)
    wallet_id = models.IntegerField(null=True, blank=True)

    # Raw data
    request_data = models.TextField(null=True, blank=True)
    payload = models.TextField(null=True, blank=True)
    paystack_webhook_payload = models.TextField(null=True, blank=True)
    verification_response = models.TextField(null=True, blank=True)
    monnify_status = models.CharField(max_length=100, null=True, blank=True)
    beneficiary_name = models.CharField(max_length=500, null=True, blank=True)
    beneficiary_account = models.CharField(max_length=20, null=True, blank=True)
    beneficiary_bank = models.CharField(max_length=500, null=True, blank=True)

    def save(self, *args, **kwargs):
        # self.amount = round(self.amount, 2)
        # self.wallet_balance_before = (
        #     round(self.wallet_balance_before, 2) if self.wallet_balance_before is not None else None
        # )
        # self.wallet_balance_after = (
        #     round(self.wallet_balance_after, 2) if self.wallet_balance_after is not None else None
        # )
        # self.plan_balance_before = round(self.plan_balance_before, 2) if self.plan_balance_before is not None else None
        # self.plan_balance_after = round(self.plan_balance_after, 2) if self.plan_balance_after is not None else None

        if self.transfer_provider is None:
            raise ValidationError(
                detail={"error": "transfer provider cannot be a null value"}
            )
        super(Transaction, self).save(*args, **kwargs)


class WalletSystem(models.Model):
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="wallets",
        on_delete=models.CASCADE,
        db_index=True,
    )
    onboarded_user = models.ForeignKey(
        to=AjoUser,
        related_name="ajo_user_wallet",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        db_index=True,
    )
    wallet_id = models.UUIDField(default=uuid.uuid4, editable=False)
    wallet_type = models.CharField(
        max_length=300, choices=WalletTypes.choices, db_index=True
    )
    wallet_number = models.CharField(max_length=100, null=True, blank=True, unique=True)
    available_balance = RoundedFloatField(default=0.00)
    hold_balance = RoundedFloatField(default=0.00)
    commission_balance = RoundedFloatField(default=0.00)
    # this is for a RotationGroup Saving
    rotation_group_id = models.CharField(max_length=100, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    repayment_id = models.CharField(max_length=100, null=True, blank=True, unique=True)
    transfer_suspended = models.BooleanField(default=False)

    def __str__(self) -> str:
        return f"{self.id}-{self.user if not self.onboarded_user else self.onboarded_user}-{self.wallet_type}"

    @property
    def total_credit(self):
        total_credit = (
            Transaction.objects.filter(
                onboarded_user=self.onboarded_user,
                transaction_type=TransactionTypeCreditOrDebitChoices.CREDIT,
                wallet_type=self.wallet_type,
            ).aggregate(total_amount=Sum("amount"))["total_amount"]
            or 0.00
        )

        return total_credit

    @property
    def total_debit(self):
        total_debit = (
            Transaction.objects.filter(
                onboarded_user=self.onboarded_user,
                transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
                wallet_type=self.wallet_type,
            ).aggregate(total_amount=Sum("amount"))["total_amount"]
            or 0.00
        )

        return total_debit

    @property
    def offset_amount(self):
        return self.total_credit - self.total_debit

    @classmethod
    @django_transaction.atomic
    def deduct_balance(
        cls,
        wallet: "WalletSystem",
        amount: float,
        transaction_instance: Transaction,
        unique_reference: str | None = None,
        onboarded_user: AjoUser | None = None,
    ) -> Dict[str, float | int]:
        """
        This deducts the available balance of a user's wallet and creates a DebitCreditRecord Info

        Args:
            wallet (WalletSystem): the wallet to be debited
            amount (float): the amount to debit
            transaction_instance (Transaction): the transaction associated to the debit

        Raises:
            Exception: "amount is less than zero"

        Returns:
            Dict[str, float|int]:
            {
                "debit_credit_record_id": debit_credit_record.id,
                "balance_before": balance_before,
                "balance_after": balance_after,
            }
        """
        if amount <= 0:
            raise Exception("amount is less than zero")

        new_amount = abs(amount)
        balance_before = wallet.available_balance

        wallet.available_balance = F("available_balance") - int(new_amount)
        wallet.save()

        wallet.refresh_from_db()

        balance_after = wallet.available_balance

        debit_credit_record = DebitCreditRecordOnAccount.objects.create(
            user=wallet.user,
            onboarded_user=onboarded_user,
            entry=TransactionTypeCreditOrDebitChoices.DEBIT,
            wallet=wallet,
            wallet_type=wallet.wallet_type,
            balance_before=balance_before,
            amount=amount,
            balance_after=balance_after,
            type_of_trans=transaction_instance.transaction_form_type,
            transaction_id=transaction_instance.transaction_id,
            unique_reference=unique_reference,
        )

        return {
            "debit_credit_record_id": debit_credit_record.id,
            "balance_before": balance_before,
            "balance_after": balance_after,
        }

    @classmethod
    @django_transaction.atomic
    def fund_balance(
        cls,
        wallet: "WalletSystem",
        amount: float,
        transaction_instance: Transaction,
        unique_reference: str | None = None,
        onboarded_user: AjoUser | None = None,
    ) -> dict:
        """
        Fund the available balance of the wallet

        Args:
            wallet (WalletSystem): the specific wallet to alter fields
            amount (float): the amount that the wallet should be altered by
            transaction_instance (Transaction): the transaction instance of the transaction that has taken place

        Raises:
            Exception: amount is less than zero

        Returns:
            Dict[str, float|int]:
            {
                "debit_credit_record_id": debit_credit_record.id,
                "balance_before": balance_before,
                "balance_after": balance_after,
            }
        """
        if amount <= 0:
            raise Exception("amount is less than zero")

        balance_before = wallet.available_balance

        wallet.available_balance = F("available_balance") + round(amount, 2)
        wallet.save()

        wallet.refresh_from_db()
        balance_after = wallet.available_balance

        debit_credit_record = DebitCreditRecordOnAccount.objects.create(
            user=wallet.user,
            onboarded_user=onboarded_user,
            entry=TransactionTypeCreditOrDebitChoices.CREDIT,
            wallet=wallet,
            wallet_type=wallet.wallet_type,
            balance_before=balance_before,
            amount=amount,
            balance_after=balance_after,
            type_of_trans=transaction_instance.transaction_form_type,
            transaction_id=transaction_instance.transaction_id,
            unique_reference=unique_reference,
        )

        return {
            "debit_credit_record_id": debit_credit_record.id,
            "balance_before": balance_before,
            "balance_after": balance_after,
        }

    @classmethod
    @django_transaction.atomic
    def fund_prefunding_balances(
        cls,
        wallet: "WalletSystem",
        amount: float,
        transaction_instance: Transaction,
        unique_reference: str | None = None,
    ) -> dict:
        """
        fund the prefunding wallet's available balance and hold balance

        Args:
            wallet (WalletSystem): the specific wallet to alter fields
            amount (float): the amount that the wallet should be altered by
            transaction_instance (Transaction): the transaction instance of the transaction that has taken place

        Raises:
            Exception: amount is less than zero

        Returns:
            Dict[str, float|int]:
            {
                "debit_credit_record_id": debit_credit_record.id,
                "balance_before": balance_before,
                "balance_after": balance_after,
                "hold_balance_before": hold_balance_before,
                "hold_balance_after": hold_balance_after,
            }
        """
        if amount <= 0:
            raise Exception("amount is less than zero")

        # change the balance before
        balance_before = wallet.available_balance
        hold_balance_before = wallet.hold_balance

        # change the available balance
        wallet.available_balance = F("available_balance") + round(amount, 2)
        wallet.hold_balance = F("hold_balance") + round(amount, 2)
        wallet.save()

        # change the balance after
        wallet.refresh_from_db()
        balance_after = wallet.available_balance
        hold_balance_after = wallet.hold_balance

        debit_credit_record = DebitCreditRecordOnAccount.objects.create(
            user=wallet.user,
            entry=TransactionTypeCreditOrDebitChoices.CREDIT,
            wallet=wallet,
            wallet_type=wallet.wallet_type,
            balance_before=balance_before,
            amount=amount,
            balance_after=balance_after,
            type_of_trans=transaction_instance.transaction_form_type,
            transaction_id=transaction_instance.transaction_id,
            hold_balance_before=hold_balance_before,
            hold_balance_after=hold_balance_after,
            unique_reference=unique_reference,
        )

        return {
            "debit_credit_record_id": debit_credit_record.id,
            "balance_before": balance_before,
            "balance_after": balance_after,
            "hold_balance_before": hold_balance_before,
            "hold_balance_after": hold_balance_after,
        }

    @classmethod
    @django_transaction.atomic
    def deduct_prefunding_hold_balance(
        cls,
        wallet: "WalletSystem",
        amount: float,
        transaction_instance: Transaction,
        unique_reference: str | None = None,
    ) -> dict:
        """
        deduct money from the prefunding hold balance

        Args:
            wallet (WalletSystem): the specific wallet to alter fields
            amount (float): the amount that the wallet should be altered by
            transaction_instance (Transaction): the transaction instance of the transaction that has taken place

        Raises:
            Exception: amount is less than zero

        Returns:
            Dict[str, float|int]:
            {
                "debit_credit_record_id": debit_credit_record.id,
                "hold_balance_before": hold_balance_before,
                "hold_balance_after": hold_balance_after,
            }
        """
        if amount <= 0:
            raise Exception("amount is less than zero")

        # change the balance before
        # balance_before = wallet.available_balance
        hold_balance_before = wallet.hold_balance

        # change the hold balance
        # wallet.available_balance = F("available_balance") + round(amount, 2)
        wallet.hold_balance = F("hold_balance") - round(amount, 2)
        wallet.save()

        # change the balance after
        wallet.refresh_from_db()
        # balance_after = wallet.available_balance
        hold_balance_after = wallet.hold_balance

        debit_credit_record = DebitCreditRecordOnAccount.objects.create(
            user=wallet.user,
            entry=TransactionTypeCreditOrDebitChoices.DEBIT,
            wallet=wallet,
            balance_type=WalletBalanceType.HOLD,
            wallet_type=wallet.wallet_type,
            hold_balance_before=hold_balance_before,
            amount=amount,
            hold_balance_after=hold_balance_after,
            type_of_trans=transaction_instance.transaction_form_type,
            transaction_id=transaction_instance.transaction_id,
            unique_reference=unique_reference,
        )

        return {
            "debit_credit_record_id": debit_credit_record.id,
            # "balance_before": balance_before,
            # "balance_after": balance_after,
            "hold_balance_before": hold_balance_before,
            "hold_balance_after": hold_balance_after,
        }

    @classmethod
    def create_wallets_for_user(cls, user):
        wallets = [
            WalletSystem(user=user, wallet_type=WalletTypes.CHESTLOCK),
            WalletSystem(user=user, wallet_type=WalletTypes.HALAL),
            WalletSystem(user=user, wallet_type=WalletTypes.QUICKSAVINGS),
        ]
        WalletSystem.objects.bulk_create(wallets)

    @classmethod
    def create_rosca_wallets_for_ajo_user(cls, user, onboarded_user):
        wallets = [
            WalletSystem(
                user=user,
                onboarded_user=onboarded_user,
                wallet_type=WalletTypes.ROSCA_AJO_USER,
            ),
        ]
        WalletSystem.objects.bulk_create(wallets)

    @classmethod
    def create_rotation_group_wallet(cls, user, rotation_group_id):
        wallets = [
            WalletSystem(
                user=user,
                rotation_group_id=rotation_group_id,
                wallet_type=WalletTypes.ROSCA_GROUP_ESCROW,
            ),
        ]
        WalletSystem.objects.bulk_create(wallets)

    @classmethod
    def create_ajo_loan_escrow_wallet(cls, user, onboarded_user):
        wallets = [
            WalletSystem(
                user=user,
                onboarded_user=onboarded_user,
                wallet_type=WalletTypes.AJO_LOAN_ESCROW,
            ),
        ]
        WalletSystem.objects.bulk_create(wallets)

    @classmethod
    def create_missing_wallets_for_user(cls, user):
        wallet_types = set(WalletTypes.values)  # Get all wallet types
        existing_wallets = {
            wallet.wallet_type: wallet for wallet in user.wallets.all()
        }  # Get existing wallets for user

        # Check if all wallets exist
        if len(existing_wallets) == len(wallet_types):
            return  # All wallets already exist for the user

        # Create missing wallets and enforce single wallet type
        wallets_to_create = []
        for wallet_type in wallet_types:
            if wallet_type not in existing_wallets:
                wallets_to_create.append(
                    WalletSystem(user=user, wallet_type=wallet_type)
                )
            elif (
                existing_wallets[wallet_type].pk != None
            ):  # If existing wallet is not saved, ignore it
                existing_wallet = existing_wallets[wallet_type]
                try:
                    existing_wallet.save()  # Try to save existing wallet
                except IntegrityError:
                    existing_wallet.delete()  # Delete existing wallet if saving fails

        # Create missing wallets
        if wallets_to_create:
            try:
                WalletSystem.objects.bulk_create(
                    wallets_to_create
                )  # Create missing wallets
            except IntegrityError:
                pass  # Ignore IntegrityError if any wallets were created by another process

    @classmethod
    def get_or_create_wallet(
        cls,
        user,
        wallet_type: WalletTypes,
        rotation_group_id: str | None = None,
        ajo_user: AjoUser | None = None,
        wallet_number=None,
    ) -> "WalletSystem":

        try:

            instance, created = WalletSystem.objects.get_or_create(
                user=user,
                wallet_type=wallet_type,
                onboarded_user=ajo_user,
                is_active=True,
            )
        except WalletSystem.MultipleObjectsReturned as err:
            instance = WalletSystem.objects.filter(
                user=user,
                wallet_type=wallet_type,
                onboarded_user=ajo_user,
                is_active=True,
            ).last()

        if wallet_number is not None:
            instance.wallet_number = wallet_number
            instance.save(update_fields=["wallet_number"])

        if rotation_group_id is not None:
            instance.rotation_group_id = rotation_group_id
            instance.save(update_fields=["rotation_group_id"])

        return instance

    @classmethod
    def get_or_create_interest_wallet(
        cls,
        user,
        wallet_type: WalletTypes,
        quotation_id: str,
    ) -> "WalletSystem":
        wallet, created = WalletSystem.objects.get_or_create(
            user=user,
            wallet_type=wallet_type,
            wallet_number=quotation_id,
        )
        return wallet


class DebitCreditRecordOnAccount(models.Model):
    # TODO: add the onboarded_user field here for better data collection
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, db_index=True
    )
    onboarded_user = models.ForeignKey(
        to=AjoUser, on_delete=models.CASCADE, null=True, blank=True, db_index=True
    )
    entry = models.CharField(
        max_length=200, choices=TransactionTypeCreditOrDebitChoices.choices
    )
    wallet = models.ForeignKey(WalletSystem, on_delete=models.SET_NULL, null=True)
    balance_type = models.CharField(
        max_length=200,
        choices=WalletBalanceType.choices,
        default=WalletBalanceType.AVAILABLE,
    )
    balance_before = RoundedFloatField(default=0.00)
    amount = RoundedFloatField()
    balance_after = RoundedFloatField(default=0.00)
    wallet_type = models.CharField(
        max_length=200, null=True, blank=True, choices=WalletTypes.choices
    )
    type_of_trans = models.CharField(max_length=200, null=True, blank=True)
    transaction_id = models.CharField(
        max_length=400, null=True, blank=True, db_index=True
    )
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    hold_balance_before = RoundedFloatField(default=0)
    hold_balance_after = RoundedFloatField(default=0)
    float_bal_before = models.FloatField(default=0)
    float_bal_after = models.FloatField(default=0)
    unique_reference = models.CharField(
        max_length=200, null=True, blank=True, unique=True, db_index=True
    )


##################FOR THE WEBHOOK ########################################
class WebhookSettings(BaseModel):
    auth_header = models.CharField(max_length=255)
    ip_addresses = models.ManyToManyField("IPAddress")

    def save(self, *args, **kwargs):
        # check if auth_header is not already hashed
        if not self.auth_header.startswith("pbkdf2_sha256$"):
            self.auth_header = make_password(
                password=self.auth_header,
                salt=None,
                hasher="default",
            )

        super().save(*args, **kwargs)

        cache_key = "webhook_settings"
        cache.set(cache_key, self, timeout=432000)

    @classmethod
    def get_settings(cls):
        cache_key = "webhook_settings"
        settings = cache.get(cache_key)
        if settings is None:
            try:
                settings = cls.objects.get()
                cache.set(cache_key, settings, timeout=432000)
            except cls.DoesNotExist:
                settings = None
        return settings

    # @staticmethod
    # def pad_secret_key(secret_key: str) -> bytes:
    #     key_length = len(secret_key)
    #     if key_length < 32:
    #         secret_key = secret_key.ljust(32, "X")
    #     elif key_length > 32:
    #         secret_key = secret_key[:32]

    #     return base64.urlsafe_b64encode(secret_key.encode())

    # def save(self, *args, **kwargs):
    #     # Ensure only one instance exists
    #     self.pk = 1
    #     if not self.can_be_decrypted(self.auth_header):
    #         self.auth_header = self.encrypt(self.auth_header)
    #     super().save(*args, **kwargs)
    #     cache_key = "webhook_settings"
    #     try:
    #         self.auth_header = self.decrypt(self.auth_header)
    #     except InvalidToken as err:
    #         pass
    #     cache.set(cache_key, self, timeout=432000)

    # def can_be_decrypted(self, value) -> bool:
    #     try:
    #         self.decrypt(value)
    #         return True
    #     except InvalidToken as err:
    #         return False

    # def encrypt(self, value):
    #     key = self.pad_secret_key(settings.SECRET_KEY)
    #     cipher = Fernet(key)
    #     return cipher.encrypt(value.encode()).decode()

    # def decrypt(self, value):
    #     key = self.pad_secret_key(settings.SECRET_KEY)
    #     cipher = Fernet(key)
    #     return cipher.decrypt(value.encode()).decode()

    # @classmethod
    # def get_settings(cls):
    #     cache_key = "webhook_settings"
    #     settings = cache.get(cache_key)
    #     if settings is None:
    #         try:
    #             settings = cls.objects.get()

    #             try:
    #                 settings.auth_header = cls.decrypt(cls, settings.auth_header)
    #             except InvalidToken as err:
    #                 pass

    #             cache.set(cache_key, settings, timeout=432000)
    #         except cls.DoesNotExist:
    #             settings = None
    #     return settings


class IPAddress(models.Model):
    address = models.CharField(max_length=255, unique=True)

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        # Ensure the IP address is associated with the singleton instance
        settings = WebhookSettings.get_settings()
        if settings is not None:
            settings.ip_addresses.add(self)

    def __str__(self) -> str:
        return self.address


class Commission(BaseModel):
    # INFORMATION ABOUT THE COMMISSION
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name="commissions",
        null=True,
        blank=True,
        db_index=True,
    )
    commission_type = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        choices=CommissionType.choices,
        db_index=True,
    )
    amount = RoundedFloatField(
        default=0.0,
        validators=[
            MinValueValidator(
                limit_value=0.0,
                message="amount should be greater than or equal to 0",
            )
        ],
    )
    reference = models.UUIDField(
        default=uuid.uuid4, editable=False, unique=True, db_index=True
    )
    description = models.CharField(max_length=255, null=True, blank=True)

    # INFORMATION ABOUT THE PLAN
    quotation_id = models.CharField(
        max_length=120, null=True, blank=True, db_index=True
    )
    total_amount_taken_as_commission = RoundedFloatField(
        default=0.0,
        validators=[
            MinValueValidator(
                limit_value=0.0,
                message="total amount as commission should be greater than or equal to 0",
            )
        ],
    )
    plan_type = models.CharField(
        max_length=100, null=True, blank=True, choices=PlanType.choices
    )

    # details concerning them
    withdrawn = models.BooleanField(default=False)

    # def save(self, *args, **kwargs):
    #     self.balance_before = round(self.balance_before, 2) if self.balance_before is not None else None
    #     self.amount = round(self.amount, 2) if self.amount is not None else None
    #     self.balance_after = round(self.balance_after, 2) if self.balance_after is not None else None
    # super(DebitCreditRecordOnAccount, self).save(*args, **kwargs)

    # @staticmethod
    # def debit_record(
    #     user: User,
    #     wallet: WalletSystem,
    #     balance_before,
    #     amount,
    #     balance_after,
    #     wallet_type: WalletTypes,
    #     type_of_trans,
    #     transaction_instance_id,
    #     float_bal_before,
    #     float_bal_after,
    #     entry: TransactionTypeCreditOrDebitChoices = TransactionTypeCreditOrDebitChoices.DEBIT,
    # ):
    #     """
    #     Creates a debit instance of DebitCreditRecord

    #     Args:
    #         #Get the definition of the arguments from Emeka
    #         transaction_quotation_id (str): the transaction_instance_id from eg the DebitCreditRecordOnAccount table
    #         quotation_id (str): the quotation_id of the savings type eg chestlock -> LI-CHK-{{string_of_length_12}}

    #     Returns:
    #         str: A transaction instance created in the  database
    #     """

    #     debit = DebitCreditRecordOnAccount.objects.create(
    #         user=user,
    #         entry=entry,
    #         wallet=wallet,
    #         balance_before=balance_before,
    #         amount=amount,
    #         balance_after=balance_after,
    #         wallet_type=wallet_type,
    #         type_of_trans=type_of_trans,
    #         transaction_instance_id=transaction_instance_id,
    #         float_bal_before=float_bal_before,
    #         float_bal_after=float_bal_after,
    #     )

    #     return debit


class WithdrawalAccount(BaseModel):
    user = models.OneToOneField(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="withdrawal_account",
    )
    account_number = models.CharField(max_length=100)
    account_name = models.CharField(max_length=100)
    bank_name = models.CharField(max_length=100)
    bank_code = models.CharField(max_length=100)

    class Meta:
        verbose_name = "Withdrawal Account"
        verbose_name_plural = "Withdrawal Accounts"


# Initial Card model created when paystack was the go-to payment solution
# class Card(models.Model):
#     user = models.ForeignKey(
#         to=User,
#         on_delete=models.CASCADE,
#         related_name="card",
#         null=True,
#     )

#     # Card details
#     authorization_code = models.CharField(max_length=500, editable=False, blank=True, null=True)
#     card_type = models.CharField(max_length=40)
#     card_last_four_digits = models.CharField(max_length=20)
#     exp_month = models.CharField(max_length=20)
#     exp_year = models.CharField(max_length=20)
#     bin = models.CharField(max_length=30)
#     bank_name = models.CharField(max_length=500)
#     channel = models.CharField(max_length=100)
#     card_signature = models.CharField(max_length=500)
#     reusable = models.BooleanField(default=False)
#     country_code = models.CharField(max_length=10)
#     account_name = models.CharField(max_length=150, null=True, blank=True)
#     brand = models.CharField(max_length=250, null=True, blank=True)

#     # Customer details
#     customer_id = models.CharField(max_length=250, editable=False, null=True, blank=True)
#     customer_code = models.CharField(max_length=250, editable=False, null=True, blank=True)
#     customer_email = models.CharField(max_length=250, null=True, blank=True)

#     # Metadata
#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)

#     # Raw data
#     payload = models.TextField()


# Initial Transaction Model before modification above
# class Transaction(models.Model):
#     user = models.ForeignKey(to=User, on_delete=models.PROTECT, related_name="transaction", null=True)
#     card_instance = models.ForeignKey(to=Card, on_delete=models.PROTECT, null=True, blank=True)
#     transaction_date = models.DateTimeField(null=True, blank=True)
#     reference = models.CharField(max_length=200, null=True, blank=True)
#     amount = models.FloatField(default=0.0, null=True, blank=True)
#     transaction_type = models.CharField(max_length=250, null=True, blank=True)
#     transaction_method = models.CharField(max_length=250, null=True, blank=True)
#     status = models.CharField(max_length=10, choices=Status.choices, null=True, blank=True)
#     paid_at = models.DateTimeField(null=True, blank=True)
#     gateway_response = models.CharField(max_length=100, null=True, blank=True)

#     # This is the unique id of the saving type in order to be able to retrieve the transaction history
#     quotation_id = models.CharField(max_length=120, null=True, blank=True)

#     # metadata
#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)

#     # Transaction characteristics
#     transaction_quotation_id = models.CharField(max_length=120, null=True, blank=True)
#     transaction_form_type = models.CharField(max_length=50, null=True, blank=True, choices=TransactionFormType.choices)

#     # Raw data
#     payload = models.TextField()

# Raw PayStack model created initially when Paystack was the first choice
# class RawPaystackData(models.Model):
#     user = models.ForeignKey(to=User, on_delete=models.PROTECT, null=True, blank=True)
#     data = models.TextField(max_length=2000)
#     date = models.DateTimeField(auto_now_add=True)


class RequestDump(BaseModel):
    request_data = models.JSONField(default=dict)
    response = models.TextField()

    def save(self, *args, **kwargs):
        keys_to_remove = ["transaction_pin"]
        new_data = json.loads(self.request_data).copy()
        for key in keys_to_remove:
            if key in new_data:
                del new_data[key]

        self.request_data = new_data

        super().save(*args, **kwargs)


class ManualTransfer(models.Model):
    from loans.models import AjoLoan

    date_created = models.DateTimeField(default=timezone.localtime)
    user = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name="manual_transaction",
        null=True,
        db_index=True,
    )
    transaction = models.ForeignKey(
        to=Transaction,
        on_delete=models.PROTECT,
        related_name="manual_transaction",
        null=True,
        db_index=True,
    )
    loan = models.ForeignKey(
        to=AjoLoan,
        on_delete=models.PROTECT,
        related_name="manual_transaction",
        null=True,
        db_index=True,
    )
    amount = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="amount should be >= 0"),
        ],
    )
    status = models.CharField(
        max_length=100, null=True, blank=True, choices=Status.choices
    )
    transaction_type = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        choices=TransactionTypeCreditOrDebitChoices.choices,
    )
    transaction_date_completed = models.DateTimeField(null=True, blank=True)

    transfer_provider = models.CharField(
        max_length=100,
        choices=DisbursementProviderType.choices,
        default=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
    )
    description = models.CharField(max_length=3000, blank=True, null=True)

    # Raw data
    request_data = models.TextField(null=True, blank=True)
    payload = models.TextField(null=True, blank=True)


class WalletTransactionReconciliation(BaseModel):
    ajo_user = models.ForeignKey(
        "ajo.AjoUser", blank=True, null=True, on_delete=models.PROTECT
    )
    disburse_to_external_amount = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="amount should be >= 0"),
        ],
    )
    disburse_to_spend_amount = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="amount should be >= 0"),
        ],
    )
    transfer_to_external_amount = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="amount should be >= 0"),
        ],
    )
    reversal_amount = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="amount should be >= 0"),
        ],
    )
    difference_spend_disburse = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="amount should be >= 0"),
        ],
    )
    difference_disburse_external = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="amount should be >= 0"),
        ],
    )


class TempLogOnlendingPayments(BaseModel):

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, null=True, blank=True
    )
    ajo_user = models.ForeignKey(
        AjoUser, on_delete=models.CASCADE, null=True, blank=True
    )
    amount = RoundedFloatField(null=True, blank=True)
    wallet_type = models.CharField(max_length=100, null=True, blank=True)
    wallet = models.ForeignKey(
        WalletSystem, on_delete=models.CASCADE, null=True, blank=True
    )
    response = models.TextField(blank=True, null=True)

    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Temporary Onlending Log"
        verbose_name_plural = "Temporary Onlending Logs"


class MakeAdminTransfer(BaseModel):
    sender = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name="admin_transfers",
    )
    amount = RoundedFloatField(
        default=0.0,
        null=True,
        blank=True,
        validators=[
            MinValueValidator(limit_value=0.0, message="amount should be >= 0"),
        ],
    )
    status = models.CharField(
        max_length=1200, choices=Status.choices, default=Status.PENDING
    )
    transfer_route = models.CharField(
        max_length=1500, choices=TransferRouteTypes.choices, blank=True, null=True
    )
    receiver_account_number = models.CharField(max_length=20, blank=True, null=True)
    receiver_bank_code = models.CharField(
        max_length=20, blank=True, null=True, editable=False
    )
    receiver_bank_name = models.CharField(
        max_length=1200, blank=True, null=True, editable=False
    )
    transfer_pin = models.CharField(max_length=6, blank=True, null=True)
    request_payload = models.TextField(blank=True, null=True)
    payload = models.TextField(blank=True, null=True)
    verification_response = models.TextField(blank=True, null=True)


class PayWiseWaitList(models.Model):
    full_name = models.CharField(max_length=255, null=True, blank=True)
    email = models.EmailField()
    phone_number = models.CharField(max_length=255, null=True, blank=True)
    company_name = models.CharField(max_length=255, null=True, blank=True)
    product_interest = models.JSONField(default=list)
    created_at = models.DateTimeField(_("date_created"), auto_now_add=True)

    def __str__(self):
        return f"{self.full_name}"

    class Meta:
        verbose_name = "PAYWISE WAITLIST"
        verbose_name_plural = "PAYWISE WAITLIST"

