from django.core.management.base import BaseCommand
from payment.models import Transaction, DebitCreditRecordOnAccount
from django.utils import timezone
from payment.model_choices import Status, TransactionFormType
from datetime import datetime


class Command(BaseCommand):
   
    def handle(self, *args, **kwargs):
        ten_minutes_before = timezone.now() - timezone.timedelta(minutes=10)
        five_days_ago = timezone.now() - timezone.timedelta(days=5)

        yesterday_9 = datetime(2023, 3, 3, 21)

        all_trans = Transaction.objects.filter(
            transaction_form_type=TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT,
            status=Status.FAILED,
            date_created__gte=yesterday_9, 
        )

        for trans in all_trans:
            if not DebitCreditRecordOnAccount.objects.filter(transaction_id=trans.transaction_id).first():
                trans_desc = trans.description
                reversal_desc = f"REVERSAL- {trans_desc}"
                get_reversal_trans = Transaction.objects.filter(description=reversal_desc, transaction_form_type=TransactionFormType.REVERSAL_EXTERNAL_TRANSFER, status=Status.SUCCESS, date_created__gte=yesterday_9).first()

                if get_reversal_trans:
                    get_reversal_trans.status = Status.FAILED
                    get_reversal_trans.save()

                    debit_cred_record = DebitCreditRecordOnAccount.objects.filter(transaction_id=get_reversal_trans.transaction_id).first()

                    if debit_cred_record:
                        debit_cred_record.delete()

                        wallet = debit_cred_record.wallet
                        wallet.available_balance -= get_reversal_trans.amount
                        wallet.save()










        # for trans in Transaction.objects.filter(transaction_form_type="LIB_PROSPER_LOAN_DEPOSIT"):
        #     trans.transaction_form_type = "WALLET_DEPOSIT"
        #     trans.description = f"{trans.amount} was funded into your wallet from Liberty Pay."
        #     trans.save()

        from django.db.models import OuterRef, Subquery
        from accounts.models import CustomUser
        from ajo.models import AjoSaving

  
        subquery = AjoSaving.objects.filter(user_id=OuterRef("pk"), amount_saved__gt=30).values("user_id")

        # Query to fetch User instances which have Ajosaving records with age savings than 30
        users_with_savings_gt_30 = CustomUser.objects.filter(pk__in=subquery)

        # Execute the query
        result = users_with_savings_gt_30.all()

        print(users_with_savings_gt_30)
        print(type(users_with_savings_gt_30))

        print(result)
        print(type(result))
