from django.core.management.base import BaseCommand
from payment.models import Transaction, WalletSystem
from payment.model_choices import Status, TransactionFormType
from payment.model_choices import WalletTypes
from ajo.prefunding.actions import (
    AjoUserSelector
)
from payment.services import TransactionService
import ast


class Command(BaseCommand):
   
    def handle(self, *args, **kwargs):
        transaction_id = input("Enter Transaction Id: ")
        reversal_transaction = Transaction.objects.get(
            id=transaction_id
        )
        print("::::::::::::::::::")
        print(reversal_transaction.user)
        print(reversal_transaction.transaction_form_type)
        print("::::::::::::::::::")

        if reversal_transaction.transaction_form_type == TransactionFormType.REVERSAL:
            print("INSIDE::::::::::::::")
            print(reversal_transaction.payload)

            transaction_payload = reversal_transaction.payload
            payload_json = ast.literal_eval(transaction_payload)
            customer_reference = payload_json.get("data").get("data").get("customer_reference")

            print(":::::::::::::::CUSTOMER REF::::::::::::::::::::::::::::")
            print(customer_reference)
            print("::::::::::::::::::CUSTOMER REF:::::::::::::::::::::::::")

            # Fetch actual disbursement transaction
            disbursement_wallet_debit = Transaction.objects.filter(
                transaction_form_type=TransactionFormType.DISBURSE_TO_EXTERNAL_ACCOUNT,
                wallet_type=WalletTypes.LOAN_DISBURSEMENT,
                transaction_id=customer_reference
            ).last()

            if disbursement_wallet_debit is None:
                print("::::::::::::::::::::::::::::::::")
                print("NO DISBURSEMENT WALLET DEBIT AVAILABLE")
                print("::::::::::::::::::::::::::::::::")
                return None
            elif disbursement_wallet_debit.user is reversal_transaction.user:
                print("::::::::::::::::::::::::::::::::")
                print("ALREADY SAME AS INTENDED")
                print("::::::::::::::::::::::::::::::::")
                return None
            else:
                pass

            print(":::::::::::::::disbursement_wallet_debit::::::::::::::::::::::::::::")
            print(disbursement_wallet_debit)
            print("::::::::::::::::::disbursement_wallet_debit:::::::::::::::::::::::::")

            wrong_ajo_user = reversal_transaction.onboarded_user
            wrong_ajo_user_selector = AjoUserSelector(ajo_user=wrong_ajo_user)
            wrong_ajo_user_disbursment_wallet = wrong_ajo_user_selector.get_loan_disbursement_wallet()

            print(":::::::::::::::wrong_ajo_user_disbursment_wallet::::::::::::::::::::::::::::")
            print(wrong_ajo_user_disbursment_wallet)
            print("::::::::::::::::::wrong_ajo_user_disbursment_wallet:::::::::::::::::::::::::")


            # Deduct Amount From Wrongly funded disbursement Wallets
            debit_transaction = TransactionService.dynamic_deduction_from_wallet_transaction(
                user=wrong_ajo_user_disbursment_wallet.user,
                amount=reversal_transaction.amount,
                wallet_type=wrong_ajo_user_disbursment_wallet.wallet_type,
                quotation_id=reversal_transaction.quotation_id,
                ajo_user=wrong_ajo_user,
                description="Admin Manual Deduct Disbursement Wallet from bad reversal",
                transaction_form_type=TransactionFormType.MANUAL_CLEANUP_WALLET_DEDUCTION,
                status=Status.FAILED
            )
            print(":::::::::::debit_transaction:::::::::::::::::::::::")
            print(debit_transaction)
            print("::::::::::::::debit_transaction::::::::::::::::::::")

            deduct_wrong_disbursement_wallet = WalletSystem.deduct_balance(
                wallet=wrong_ajo_user_disbursment_wallet,
                amount=reversal_transaction.amount,
                transaction_instance=debit_transaction,
                onboarded_user=wrong_ajo_user
                )
            print(":::::::::::deduct_wrong_disbursement_wallet:::::::::::::::::::::::")
            print(deduct_wrong_disbursement_wallet)
            print("::::::::::::::deduct_wrong_disbursement_wallet::::::::::::::::::::")
            Transaction.objects.filter(id=debit_transaction.id).update(status=Status.SUCCESS)