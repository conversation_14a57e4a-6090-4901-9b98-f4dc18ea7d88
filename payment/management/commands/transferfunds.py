from typing import Any, Optional

from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand

from ajo import payment_actions as APA
from ajo.model_choices import TransactionDescriptionType
from ajo.selectors import AjoAgentSelector
from payment.model_choices import WalletTypes


class Command(BaseCommand):
    help = "Moves money from one wallet to another specified through arguments"
    help += " e.g. python manage.py transferfunds --type inter_agent --from 'email' --from_wallet ajo_agent --to 'email' --to_wallet ajo_agent"

    def add_arguments(self, parser):
        # Define command-line arguments
        parser.add_argument(
            "--from",
            type=str,
            help="the email of the person the money will be moved from",
        )

        parser.add_argument(
            "--from_wallet",
            type=str,
            help="the wallet type of the person that the money will be moved from",
        )

        parser.add_argument(
            "--to",
            type=str,
            help="the email of the person the money will be moved to",
        )

        parser.add_argument(
            "--to_wallet",
            type=str,
            help="the wallet type of the person that the money will be moved to",
        )

        parser.add_argument(
            "--type",
            type=str,
            help="the type of the inter wallet transfer",
        )

        parser.add_argument(
            "--amount",
            type=float,
            help="the amount to be transferred",
        )

    def handle(self, *args: Any, **options: Any) -> str | None:
        form_type: Optional[str] = options["type"]
        to_user_email: Optional[str] = options["to"]
        to_wallet_type: Optional[str] = options["to_wallet"]
        from_user_email: Optional[str] = options["from"]
        from_wallet_type: Optional[str] = options["from_wallet"]
        amount: Optional[float] = options["amount"]
        print(options)

        valid_form_types = ["INTER_AGENT"]

        form_type = form_type.upper() if form_type else form_type
        to_wallet_type = to_wallet_type.upper() if to_wallet_type else to_wallet_type
        from_wallet_type = from_wallet_type.upper() if from_wallet_type else from_wallet_type

        if not form_type or form_type not in valid_form_types:
            self.stdout.write(self.style.ERROR(f"choose a valid type from this list: {valid_form_types}"))
            return

        if not from_user_email or not from_wallet_type:
            self.stdout.write(self.style.ERROR(f"use the --from with an email and --from_wallet with a wallet type"))
            return

        if not to_user_email or not to_wallet_type:
            self.stdout.write(self.style.ERROR(f"use the --to with an email and --to_wallet with a wallet type"))
            return

        if from_wallet_type not in WalletTypes or to_wallet_type not in WalletTypes:
            self.stdout.write(self.style.ERROR(f"the wallet types chosen are not valid"))
            return

        if amount:
            try:
                amount = float(amount)
            except ValueError:
                self.stdout.write(self.style.ERROR(f"--amount value is not a digit"))
                return
            amount = round(amount, 2)

        User = get_user_model()

        if form_type == "INTER_AGENT":
            try:
                to_user = User.objects.get(email=to_user_email)
                from_user = User.objects.get(email=from_user_email)
            except User.DoesNotExist:
                self.stdout.write(self.style.ERROR("check the emails, one or both does not exist"))

            to_wallet = AjoAgentSelector(user=to_user).get_agent_ajo_wallet()
            from_wallet = AjoAgentSelector(user=from_user).get_agent_ajo_wallet()

            if not amount:
                amount = from_wallet.available_balance

            from_description = APA.transaction_description(
                transaction_type=TransactionDescriptionType.TRANSFER_FROM_AGENT_TO_AGENT,
                amount=amount,
            )(to_email=to_user_email)

            APA.move_money_from_wallet_to_wallet(
                amount=amount,
                from_wallet=from_wallet,
                from_wallet_description=from_description,
                to_wallet=to_wallet,
                to_wallet_description=f"{amount} was deposited to your ajo agent wallet from {from_user_email}",
            )

            self.stdout.write(
                self.style.SUCCESS(f"{amount} was successfully moved from {from_user_email} to {to_user_email}")
            )
            return
