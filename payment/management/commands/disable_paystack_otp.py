from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime
from accounts.paystack import PaystackApi

class Command(BaseCommand):
   
    def handle(self, *args, **kwargs):
        otp = str(input("Enter OTP: "))
        disbale_otp = PaystackApi.disable_otp(otp=otp)

        print("disbale_otp::::", disbale_otp)

        if disbale_otp["status"]:
            print("OTP disabled")
            self.stdout.write(self.style.SUCCESS(disbale_otp["data"]))
        else:
            self.stdout.write(self.style.ERROR(disbale_otp["data"]))