from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.conf import settings
from accounts.models import ConstantTable
from accounts.agency_banking import AgencyBankingClass
from payment.model_choices import (
    TransactionFormType,
    TransactionTypeCreditOrDebitChoices,
    Status,
)
from payment.models import ManualTransfer, Transaction
from payment.services import TransactionService
User = get_user_model()
from accounts.agency_banking import check_balances, agent_login



class Command(BaseCommand):
    def handle(self, *args, **kwargs):
        amount = float(input("Enter amount: "))

        self.stdout.write(
            f"""\nYou are about to transfer N{amount} from savings account to loans account?\n"""
            )
        proceed = input("Do you want to proceed with the transfer? (yes/no): ")

        if proceed.lower() != "yes":
            self.stdout.write(self.style.ERROR("Transfer Cancelled"))
            return

        saving_access_token = agent_login(token_not_valid=True).get("access")
        transaction_pin = settings.AGENCY_BANKING_TRANSACTION_PIN

        # Check savings account balance
        available_balance = check_balances(
            login_purpose="SAVINGS", access_token=saving_access_token
        )

        if available_balance < amount:
            self.stdout.write(
                self.style.ERROR(
                f"Insufficient balance in savings account; Balance is {available_balance}")
                )

        # Get savings User
        savings_user_email = settings.AGENCY_BANKING_USEREMAIL
        savings_user = User.objects.filter(email=savings_user_email).last()
        # savings_phone_number = savings_user.user_phone

        # Get Loans account Phone number
        loans_user_email = settings.LOAN_AGENCY_BANKING_USEREMAIL
        loans_user = User.objects.filter(email=loans_user_email).last()
        loans_phone_number = loans_user.user_phone

        transfer_transaction = TransactionService.create_internal_transfer_between_accounts(
            user=savings_user,
            amount=amount,
            transaction_description="Transfer from savings account to loans account",
            transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
            transaction_form_type=TransactionFormType.INTERNAL_TRANSFER_TO_LOANS,
            status=Status.PENDING,
        )
        reference = str(transfer_transaction.transaction_id)
        narration = "Transfer from savings account to loans account"

        request_data = {
            "from_wallet_type": "COLLECTION",
            "to_wallet_type": "COLLECTION",
            "data": [
                {
                    "buddy_phone_number": loans_phone_number,
                    "amount": amount,
                    "narration": narration,
                    "is_beneficiary": "False",
                    "save_beneficiary": "False",
                    "remove_beneficiary": "False",
                    "is_recurring": "False",
                    "custom_reference": reference,
                }
            ],
            "transaction_pin": "",
        }

        send_money_to_loans = AgencyBankingClass.send_money_from_an_account_in_liberty_to_user_through_pay_buddy(
            transaction_pin=transaction_pin,
            phone_number=loans_phone_number,
            amount=amount,
            transaction_reference=reference,
            access_token=saving_access_token,
            narration=narration,
        )

        if send_money_to_loans.get("data").get("message") == "success":
            transfer_transaction.status = Status.SUCCESS
        else:
            transfer_transaction.status = Status.FAILED

        transfer_transaction.request_data = request_data
        transfer_transaction.payload = send_money_to_loans
        transfer_transaction.save()

        return self.stdout.write(self.style.SUCCESS(send_money_to_loans))