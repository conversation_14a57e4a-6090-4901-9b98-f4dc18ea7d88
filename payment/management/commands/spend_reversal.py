from django.core.management.base import BaseCommand
from django.utils import timezone

from accounts.models import ManualAdjustment
from ajo.models import AjoUser
from ajo.payment_actions import fund_agent_wallet
from ajo.selectors import AjoUserSelector
from payment.model_choices import Status, TransactionFormType
from payment.models import WalletSystem
from payment.services import TransactionService
from django.db import transaction

class Command(BaseCommand):
    help = ""

    def add_arguments(self, parser):
        parser.add_argument("user_id1", type=str, help="From wallet")
        parser.add_argument("user_id2", type=str, help="To Wallet")
        parser.add_argument("amount", type=str, help="Transfer Amount")
        
    @transaction.atomic
    def handle(self, *args, **kwargs):
        user_id1 = kwargs["user_id1"]
        user_id2 = kwargs["user_id2"]
        amount = float(kwargs["amount"])

        print(user_id1)
        print(user_id2)
        print(amount)
        try:
            user1 = AjoUser.objects.get(id=user_id1)
            user2 = AjoUser.objects.get(id=user_id2)
        except AjoUser.DoesNotExist as err:
            err_str = str(err)
            self.stdout.write(self.style.ERROR(err_str))
            return

        ajo_user_selector1 = AjoUserSelector(ajo_user=user1)
        spend1 = ajo_user_selector1.get_spending_wallet()

        ajo_user_selector2 = AjoUserSelector(ajo_user=user2)
        spend2 = ajo_user_selector2.get_spending_wallet()

        if spend1.available_balance >= amount:
            debit_description = f"N{amount}, Debit initiated due to funds mistakenly transferred to the user's wallet."
            unique_reference = f"tfund{spend1.wallet_id}"
            trans_obj = TransactionService.dynamic_deduction_from_wallet_transaction(
                user=user1.user,
                amount=amount,
                wallet_type=spend1.wallet_type,
                transaction_form_type=TransactionFormType.TRANSFER,
                unique_reference=unique_reference,
                ajo_user=user1,
                description=debit_description,
            )

            deduct_ajo_user_escrow_wallet = WalletSystem().deduct_balance(
                wallet=spend1,
                amount=amount,
                transaction_instance=trans_obj,
                onboarded_user=user1,
            )

            trans_obj.status = Status.SUCCESS
            trans_obj.transaction_date_completed = timezone.now()
            trans_obj.wallet_balance_before = deduct_ajo_user_escrow_wallet[
                "balance_before"
            ]
            trans_obj.wallet_balance_after = deduct_ajo_user_escrow_wallet[
                "balance_after"
            ]
            trans_obj.save()

            ###############################################################
            credit_transaction_unique_reference = f"tfund{spend2.wallet_id}"
            credit_description = f"{amount} Credit issued to reverse funds mistakenly debited from the user's wallet."
            credit_transaction = (
                TransactionService.create_deposit_by_wallet_transaction(
                    user=user2.user,
                    amount=amount,
                    wallet_type=spend2.wallet_type,
                    transaction_form_type=TransactionFormType.TRANSFER,
                    unique_reference=credit_transaction_unique_reference,
                    description=credit_description,
                    status=Status.PENDING,
                    ajo_user=user2,
                    plan_type=None,
                    quotation_id=None,
                )
            )

            fund_agent_wallet(
                transaction=credit_transaction,
                wallet=spend2,
                amount=amount,
                unique_reference=credit_transaction_unique_reference,
            )
            ManualAdjustment.objects.create(
                correction_type="wallet",
                amount=amount,
                status="SUCCESS",
                notes=f"{debit_description}\n{credit_description}",
                reason="""We have identified a transaction error where a loan customer mistakenly
                transferred N67,500 to an external user's spend wallet. The details are
                as follows:

                - Amount: N67,500
                - External Customer Wallet (09095213298)
                - Loan Customer Wallet (09166950275)""",
            )
            self.stdout.write(self.style.SUCCESS(f"Successfully Transfer {amount}"))

        else:
            self.stdout.write(
                self.style.ERROR(
                    f"insufficient wallet balance bal: {spend1.available_balance}, amount: {amount}"
                )
            )
