# from django.core.management.base import BaseCommand
# from django.contrib.auth import get_user_model
# from accounts.helper_files.monify import Monnify
# from payment.model_choices import (
#     DisbursementProviderType,
#     PlanType,
#     TransactionFormType,
#     WalletTypes,
#     TransactionTypeCreditOrDebitChoices,
#     Status
# )
# from loans.models import AjoLoan
# from payment.models import ManualTransfer
# from payment.services import TransactionService
# import json
# from django.utils import timezone
# User = get_user_model()

# class Command(BaseCommand):
#     def handle(self, *args, **kwargs):
#         account_number = input("Enter account number: ")
#         # user_email = input("Enter user email: ")
#         # disburse_amount = input("Enter disbursement amount: ")
#         bank_code = input("Enter bank code: ")
#         account_name = input("Enter account name: ")
#         narration = input("Enter narration: ")
#         loan_id = input("Enter loan id: ")

#         try:
#             loan_obj = AjoLoan.objects.filter(id=loan_id).latest("id")
#         except AjoLoan.DoesNotExist:
#             self.stdout.write(self.style.ERROR("This loan does not exist"))
#             return
        
#         try:
#             manual_transfer = ManualTransfer.objects.get(loan=loan_obj, status=Status.SUCCESS)
#             self.stdout.write(self.style.ERROR("This refund has already been completed successfully"))
#             return
#         except ManualTransfer.DoesNotExist:
#             pass

#         disbursement_provider = DisbursementProviderType.MONNIFY_PROVIDER
#         request_data = {
#                 "amount": loan_obj.amount,
#                 "destinationBankCode": bank_code,
#                 "destinationAccountNumber": account_number,
#                 "destinationAccountName": account_name,
#             }
        
#         debit_transaction = TransactionService.create_transfer_to_external_account_transaction(
#             user=loan_obj.agent,
#             amount=loan_obj.amount,
#             wallet_type=WalletTypes.LOAN_DISBURSEMENT,
#             quotation_id=loan_obj.loan_ref,
#             plan_type=PlanType.LOAN,
#             ajo_user=loan_obj.borrower,
#             description=narration,
#             transaction_form_type=TransactionFormType.DISBURSE_TO_EXTERNAL_ACCOUNT,
#             request_data=request_data,
#             transfer_provider=disbursement_provider
#         )

#         customer_reference = str(debit_transaction.transaction_id)

#         monnify_class = Monnify()

#         request_data["reference"] = customer_reference
#         request_data["narration"] = f"LD-{customer_reference}"

#         send_loan_to_borrower_external_account = monnify_class.initiate_single_transfer(**request_data)
#         response_status = send_loan_to_borrower_external_account.get("status")

#         manual_transfer = ManualTransfer.objects.create(
#             loan=loan_obj,
#             user=loan_obj.agent,
#             amount=loan_obj.amount,
#             status= Status.PENDING,
#             description=narration,
#             transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
#             request_data=request_data,
#             payload=send_loan_to_borrower_external_account,
#             transfer_provider=disbursement_provider,
#             transaction=debit_transaction
#         )

#         if response_status is True:
#             manual_transfer.status = Status.SUCCESS
#             manual_transfer.transaction_date_completed = timezone.now()
#             self.stdout.write(self.style.SUCCESS("Transfer successful"))
#         else:
#             manual_transfer.status = Status.FAILED
#             response_error = json.loads(send_loan_to_borrower_external_account.get("response")).get("responseMessage")
#             self.stdout.write(self.style.ERROR(response_error))
#         manual_transfer.save()
