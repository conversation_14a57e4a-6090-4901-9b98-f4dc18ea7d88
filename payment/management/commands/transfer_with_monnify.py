from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.conf import settings
from accounts.helper_files.monify import Monnify
from accounts.models import ConstantTable
from accounts.paystack import PaystackApi
from accounts.agency_banking import AgencyBankingClass
from payment.model_choices import (
    DisbursementProviderType,
    TransactionFormType,
    TransactionTypeCreditOrDebitChoices,
    Status, TransactionSource
)
from payment.models import ManualTransfer, Transaction
from payment.services import TransactionService
import json
from django.utils import timezone
User = get_user_model()


class Command(BaseCommand):
    def handle(self, *args, **kwargs):
        account_number = str(input("Enter account number: "))
        pin = str(input("Enter transfer pin: "))
        amount = float(input("Enter disbursement amount: "))
        bank_code = str(input("Enter bank code: "))
        # account_name = str(input("Enter account name: "))
        narration = str(input("Enter narration: "))
        # loan_id = input("Enter loan id: ")

        cbn_bank_code = AgencyBankingClass.get_bank_cbn_code(nip_code=bank_code)
        paystack_direct = PaystackApi.resolve_account_number(
            account_number=account_number, bank_code=cbn_bank_code
        )

        if paystack_direct.get("status"):
            data = paystack_direct.get("data").get("data")
            self.stdout.write(
                f"""\nConfirm Transfer Details\nAccount Number: {data.get("account_number")}\nAccount Name: {data.get("account_name")}\nBank Name: {data.get("bank_id")}\n"""
                )
        else:
            self.stdout.write(self.style.ERROR("Invalid Account Number or Bank Code"))
            return

        proceed = input("Do you want to proceed with the transfer? (yes/no): ")

        if proceed.lower() != "yes":
            self.stdout.write(self.style.ERROR("Transfer Cancelled"))
            return

        account_name = data.get("account_name")

        monnify_class = Monnify()
        const_inst = ConstantTable.get_constant_table_instance()
        monnify_manual_transfer_pin = const_inst.manual_monnify_trans_pin

        # Encrypt Provided Pin
        decrypt_pin = Monnify.decrypt_manual_transfer_pin(monnify_manual_transfer_pin)

        if pin != decrypt_pin:
            self.stdout.write(self.style.ERROR("Incorrect Pin"))
            return

        disbursement_provider = DisbursementProviderType.MONNIFY_PROVIDER
        request_data = {
                "amount": amount,
                "destinationBankCode": bank_code,
                "destinationAccountNumber": account_number,
                "destinationAccountName": account_name,
            }

        savings_user = User.objects.get(email=settings.AGENCY_BANKING_USEREMAIL)
        debit_transaction = TransactionService.create_internal_transfer_between_accounts(
            user=savings_user,
            amount=amount,
            transaction_description=narration,
            transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
            transaction_form_type=TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT,
            transaction_source=TransactionSource.MONNIFY,
            transfer_provider=DisbursementProviderType.MONNIFY_PROVIDER,
        )

        customer_reference = str(debit_transaction.transaction_id)


        request_data["reference"] = customer_reference
        request_data["narration"] = f"LD-{customer_reference}"

        send_to_external_account = monnify_class.initiate_single_transfer(**request_data)
        response_status = send_to_external_account.get("status")

        manual_transfer = ManualTransfer.objects.create(
            # loan=loan_obj,
            user=savings_user,
            amount=amount,
            status= Status.PENDING,
            description=narration,
            transaction_type=TransactionTypeCreditOrDebitChoices.DEBIT,
            request_data=request_data,
            payload=send_to_external_account,
            transfer_provider=disbursement_provider,
            transaction=debit_transaction,
        )

        if response_status is True:
            manual_transfer.status = Status.SUCCESS
            manual_transfer.transaction_date_completed = timezone.now()
            self.stdout.write(self.style.SUCCESS(send_to_external_account))
        else:
            manual_transfer.status = Status.FAILED
            response_error = json.loads(send_to_external_account.get("response")).get("responseMessage")
            self.stdout.write(self.style.ERROR(response_error))

        debit_transaction.payload = send_to_external_account
        debit_transaction.request_data = request_data
        debit_transaction.save()
        manual_transfer.save()
