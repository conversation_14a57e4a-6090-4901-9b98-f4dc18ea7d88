from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from accounts.helper_files.monify import Monnify
from accounts.models import ConstantTable
User = get_user_model()


class Command(BaseCommand):
    def handle(self, *args, **kwargs):
        pin = input("Enter transfer pin: ")

        monnify_class = Monnify
        encryted_pin = monnify_class.encrypt_manual_transfer_pin(pin)
        const_inst = ConstantTable.get_constant_table_instance()
        const_inst.manual_monnify_trans_pin = encryted_pin
        const_inst.save()
        
        self.stdout.write(self.style.SUCCESS("Pin added successfully"))

        # Encrypt Provided Pin