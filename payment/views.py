# import hashlib
# import hmac
import json
import uuid
from datetime import datetime
from typing import Optional

import requests
from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.contrib.sites.shortcuts import get_current_site
from django.core.exceptions import ObjectDoesNotExist
from django.db import transaction as django_transaction
from django.db.models import Sum
from django.urls import reverse
from django.utils import timezone
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, serializers, status
from rest_framework.generics import GenericAPIView
from rest_framework.response import Response
from rest_framework.views import APIView

from accounts.agency_banking import AgencyBankingClass
from accounts.email_templates.template_choices import EmailTemplates, EmailTypes
from accounts.helper_files.monify import Monnify
from accounts.models import ConstantTable, UserRecurrentChargeTable
from accounts.responses import (
    serializer_validation_error_response,
    value_error_response,
)
from accounts.selectors import UserSelector
from accounts.services import UserService
from accounts.tasks import send_html_email
from ajo.checks import AjoChecks
from ajo.models import RotationGroup, RotationGroupMember
from ajo.payment_actions import (
    RotationGroupPay,
    fund_personal_ajo_savings_position,
    pay_personal_ajo_commissions,
)
from ajo.selectors import (
    AjoAgentSelector,
    AjoSavingsSelector,
    AjoUserSelector,
    PersonalAjoSavingsSelector,
    PersonalSelector,
)
from ajo.tasks import general_send_email
from ajo.utils.otp_utils import verify_sms_voice_otp
from ajo.utils.ussd_utils import verify_ussd_otp
from chestlock.model_choices import InterestType, OnlendingType
from chestlock.models import ChestLock
from chestlock.selectors import OnlendingSelector
from chestlock.services import ChestlockService
from chestlock.tasks import create_loan_requests_for_onlending_plan
from chestlock.utils import LoanRequestsManager
from halal.model_choices import Frequency
from halal.models import Halal
from quicksavings.models import QuickSavings

from .checks import (
    check_if_chestlock_and_if_oneoff,
    check_the_withdrawal_conditions_of_plans,
    obtain_phone_number,
    verify_account,
    verify_transaction_pin,
)
from .make_transactions import (
    SavedAgencyBankingDebitCardPaymentProcessor,
    UserAgencyBankingWalletPaymentProcessor,
    pay_upfront_interest,
)
from .model_choices import (
    PaymentMethod,
    PlanType,
    RecurrentSavingStatus,
    Status,
    TransactionFormType,
    WalletTypes,
    DisbursementProviderType,
)
from .models import PayWiseWaitList, TempLogOnlendingPayments, Transaction, WalletSystem
from .payment_actions import (
    PlanPayments,
    debit_user_plan_and_wallet,
    pay_withdrawal_and_handle_response,
    reverse_debit,
)
from .permissions import WebhookAuthorizationPermission
from .selectors import WalletSelector
from .serializers import (
    ContinueSavingsPlanSerializer,
    DeleteSavingsPlanSerializer,
    GetCardsSerializer,
    PauseSavingsPlanSerializer,
    PayForOnlendingPlanSerializer,
    PayWiseWaitListSerializer,
    PayWithAgencyWalletSerializerV2,
    PayWithCardSerializer,
    PayWithCardSerializerV2,
    PayWithNewCardSerializer,
    PayWithWalletSerializer,
    SavingsBalanceSummarySerializer,
    TransactionIDSerializer,
    VerifiedAccountDetailsSerializer,
    VerifyAccountDetailsSerializer,
    WithdrawalAccountSerializer,
    WithdrawFromWalletSerializer,
    ManualWalletDepletionSerializer,
)
from .services import TransactionService, WithdrawalAccountService
from .tasks import settle_transfer_transaction
from .utils import Utility, format_currency, is_past_month_year
from .wrapper import PayStack, create_transaction_and_charge_card
from accounts.paystack import PaystackApi
from loans.models import AjoLoan

############################ERROR CODES######################################
# validation error, or any type of value error -> '603'
# checks with the plan in any form, completed, inactive etc -> "671"
#############################################################################


class GetCardsAPIView(GenericAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = GetCardsSerializer

    def get(self, request, *args, **kwargs):
        auth_header = request.headers.get("Authorization", "")
        token_type, _, access_token = auth_header.partition(" ")

        cards = AgencyBankingClass.get_user_cards(access_token=access_token)
        if cards.get("status") is True:
            if cards.get("message") == "Cards Retrieved":
                serializer = self.serializer_class(cards.get("data"), many=True)
                return Response(
                    data={
                        "status": True,
                        "message": "cards retrieved",
                        "data": serializer.data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "error": "765",
                        "status": False,
                        "message": "no cards found",
                    },
                    status=status.HTTP_200_OK,
                )
        else:
            return Response(
                {
                    "error": "768",
                    "status": False,
                    "message": "error encountered while trying to retrieve cards. try again.",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )


class SavingsBalanceSummary(GenericAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = SavingsBalanceSummarySerializer

    def get(self, request, *args, **kwargs):
        user = request.user
        access_token = request.headers.get("Authorization", "").partition(" ")[-1]

        try:
            UserService.update_user_fields_from_agency(
                user=user,
                access_token=access_token,
            )
        except ValueError as err:
            return value_error_response(error=err, code="911")
        # user_data_response = AgencyBankingClass.get_all_user_information(access_token=access_token)
        # if user_data_response.get("user_data"):
        #     user.user_type = user_data_response.get("user_data", {}).get("type_of_user", None)
        #     user.user_phone = user_data_response.get("user_data", {}).get("phone_number", None)
        #     user.user_branch = user_data_response.get("user_data", {}).get("user_branch", None)
        #     user.kyc_level = user_data_response.get("user_data", {}).get("kyc_level", 0)
        #     user.save()
        # else:
        #     return Response(
        #         {
        #             "status": False,
        #             "error": "921",
        #             "message": "an error occurred trying to obtain user type",
        #         },
        #         status=status.HTTP_400_BAD_REQUEST,
        #     )

        data = {}
        data["wallets_balance"] = round(
            WalletSystem.objects.filter(user=user)
            .aggregate(Sum("available_balance"))
            .get("available_balance__sum"),
            2,
        )
        data["total_savings"] = (
            int(
                ChestLock.objects.filter(
                    user=user, withdrawn=False, is_active=True
                ).count()
            )
            + int(Halal.objects.filter(user=user, is_active=True).count())
            + int(QuickSavings.objects.filter(user=user, is_active=True).count())
        )
        serializer = self.serializer_class(data)
        return Response(
            {"status": True, "data": serializer.data},
            status=status.HTTP_200_OK,
        )


class SettlePendingTrasfersAPIView(GenericAPIView):
    # permission_classes = (permissions.IsAuthenticated,)
    serializer_class = TransactionIDSerializer

    @swagger_auto_schema(
        request_body=TransactionIDSerializer,
    )
    def post(self, request, *args, **kwargs):
        """Settle a pending external transfer transaction"""
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        not_exist_response = {
            "status": False,
            "error": "404",
            "message": "Transaction does not exist",
        }

        try:
            ten_minutes_before = timezone.now() - timezone.timedelta(minutes=2)

            transaction = Transaction.objects.get(
                transaction_id=serializer.validated_data["transaction_id"],
                transaction_form_type__in=[
                    TransactionFormType.TRANSFER_TO_EXTERNAL_ACCOUNT,
                    TransactionFormType.DISBURSE_TO_EXTERNAL_ACCOUNT,
                ],
                onboarded_user__isnull=False,
                status=Status.PENDING,
                date_created__lte=ten_minutes_before,
            )

            response = settle_transfer_transaction(transaction_id=transaction.id)
            return Response(response, status=status.HTTP_200_OK)
        except Transaction.DoesNotExist:
            response = not_exist_response
            return Response(response, status=status.HTTP_404_NOT_FOUND)


class SaveFromWalletAPIView(GenericAPIView):
    serializer_class = PayWithWalletSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request):
        auth_header = request.headers.get("Authorization", "")
        token_type, _, access_token = auth_header.partition(" ")
        user = request.user

        # Regulator
        if (
            ConstantTable.get_constant_table_instance().pay_from_wallet_regulator
            == False
        ):
            response = {
                "error": "324",
                "status": False,
                "message": "service currently unavailable, please try again later",
            }
            return Response(response, status=status.HTTP_403_FORBIDDEN)

        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        # Get the plan here and get the value of the "is_activated" field
        plan_type = serializer.validated_data.get("plan_type")
        plan_id = serializer.validated_data.get("plan_id")
        plan_instance = Utility.get_plan_instance(
            user=user,
            plan_id=plan_id,
            plan_type=plan_type,
        )
        # return Response(data={"message": plan_instance.plan})

        # Special check
        # Check if the plan is chestlock one off and if it is activated
        if (
            check_if_chestlock_and_if_oneoff(
                plan_type=plan_type, plan_instance=plan_instance
            )
            and plan_instance.is_activated
        ):
            return Response(
                {
                    "error": "671",
                    "status": False,
                    "message": "you cannot add more money to Chestlock ONEOFF plan",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        # Step 1: Check if the plan is completed and if it is active
        if plan_instance.completed == True:
            return Response(
                {
                    "error": "671",
                    "status": False,
                    "message": "plan is marked as complete, contact customer care",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        if plan_instance.is_active == False:
            return Response(
                {
                    "error": "671",
                    "status": False,
                    "message": "plan is marked as inactive, contact customer care",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        # check if chestlock oneoff and if it is activated

        # Step 2: Verify the pin
        transaction_pin = serializer.validated_data.get("transaction_pin")

        # verify the transaction pin
        check_pin = verify_transaction_pin(
            transaction_pin=transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )

        # check if it is a Response object
        if isinstance(check_pin, Response):
            return check_pin

        # get the periodic_amount
        amount = float(plan_instance.periodic_amount)
        remaining_amount = float(plan_instance.target) - float(
            plan_instance.amount_saved
        )

        # Check if the plan has been activated before
        if plan_instance.is_activated == True:
            # Check if the remaining amount is larger than the periodic amount
            if remaining_amount >= amount:
                pass
            else:
                amount = remaining_amount

        # check if the plan is Chestlock ONEOFF
        if check_if_chestlock_and_if_oneoff(
            plan_type=plan_type,
            plan_instance=plan_instance,
        ):
            if plan_instance.maturity_date <= timezone.localdate():
                return Response(
                    {
                        "error": "682",
                        "status": False,
                        "message": "the maturity date for this plan has passed, create a new one",
                    },
                    status.HTTP_403_FORBIDDEN,
                )
            amount = plan_instance.target

        # call the charging user wallet class method
        charge = AgencyBankingClass.charge_user_wallet(
            user=user,
            plan_type=plan_type,
            quotation_id=plan_instance.quotation_id,
            transaction_pin=transaction_pin,
            amount=amount,
            access_token=access_token,
        )

        # check if the charge response is successful
        if (charge.get("status") == True) and ("debit_credit_info" in charge.keys()):
            # Step 3: Check if the plan is not activated
            if plan_instance.is_activated == False:
                # Activate the plan
                plan_instance.is_activated = True

                # Set the payment method
                plan_instance.payment_method = PaymentMethod.WALLET

                # check the frequency
                if plan_instance.frequency != Frequency.JUST_ONCE and not (
                    check_if_chestlock_and_if_oneoff(
                        plan_type=plan_type, plan_instance=plan_instance
                    )
                ):
                    # Set the UserRecurrentChargeTable plan task
                    try:
                        Utility.create_recurrent_charging_for_plan(
                            user=user,
                            plan_type=getattr(PlanType, plan_type.upper()),
                            plan_instance=plan_instance,
                            payment_method=PaymentMethod.WALLET,
                        )

                        # set the recurrent_saving_status
                        plan_instance.recurrent_saving_status = (
                            RecurrentSavingStatus.ACTIVE
                        )
                    except:
                        print(
                            f"A UserRecurrentChargeTable object was not created for {user.email}, with {plan_id} of {plan_type}"
                        )

            # Get the debit_credit_info
            # Sample response
            # #{
            #     "debit_credit_record_id": debit_credit_record.id,
            #     "balance_before": balance_before,
            #     "balance_after": balance_after,
            # }
            debit_credit = charge.get("debit_credit_info")

            # Get the transaction instance
            transaction = charge.get("transaction")

            # set the transaction wallet_balance_before, plan_balance_before and plan instance's balance_before
            transaction.wallet_balance_before = debit_credit.get("balance_before")
            transaction.plan_balance_before = plan_instance.amount_saved
            plan_instance.plan_balance_before = plan_instance.amount_saved

            # increase the amount saved
            plan_instance.amount_saved += transaction.amount

            # set the transaction balance after
            transaction.wallet_balance_after = debit_credit.get("balance_after")
            transaction.plan_balance_after = (
                transaction.plan_balance_before + transaction.amount
            )
            plan_instance.plan_balance_after = (
                plan_instance.plan_balance_before + transaction.amount
            )

            # # set the plan balance before and plan balance after
            # plan_instance.plan_balance_before = debit_credit.get("balance_before")
            # plan_instance.plan_balance_after = debit_credit.get("balance_after")

            # save changes
            with django_transaction.atomic():
                plan_instance.save()
                transaction.save()

            # get the userrecurrentcharge
            auto_charge_table_instance = UserRecurrentChargeTable.objects.filter(
                plan_quotation_id=plan_instance.quotation_id
            ).last()

            ##################SEND THE EMAIL#################################################

            # obtain the next savings date and make it human readable
            if auto_charge_table_instance:
                next_savings_date = auto_charge_table_instance.next_run_time
                next_savings_date = next_savings_date.strftime("%b %d, %Y %H:%M")
            else:
                next_savings_date = None

            # obtain the time the transaction took place and make it human readable
            now = timezone.localtime(timezone.now())
            date_string = now.strftime("%b %d, %Y %H:%M")

            # obtain the maturity date and make it human readable
            maturity_date = plan_instance.maturity_date
            maturity_date = maturity_date.strftime("%B %d, %Y")

            # send the email using the email task
            send_html_email.delay(
                template_name=EmailTemplates.SAVEDFROMWALLET,
                user_email=user.email,
                email_type=EmailTypes.SAVING,
                plan_type=plan_type,
                plan_transaction_object_id=transaction.id,
                email_subject="[CREDIT IN PLAN] Transaction Notification",
                plan_name=plan_instance.plan,
                amount=format_currency(transaction.amount),
                date_and_time=date_string,
                savings_wallet=getattr(WalletTypes, plan_type.upper()),
                next_savings_date=next_savings_date,
                maturity_date=maturity_date,
            )

            # Step 4: This checks two conditions with an OR
            # Condition 1: if the remaining_amount is equal to transaction amount AND the plan's target is equal to the plan's amount saved so far
            # Condition 2: if the plan's frequency type is JUST_ONCE

            # Check if remaining amount was what was saved to know if that ends the plan
            # to foolproof it, check if the plan_instance target is equal to how much has been saved
            if plan_instance.amount_saved >= plan_instance.target:
                plan_instance.completed = True
                # code below is referenced above for the email sending
                # auto_charge_table_instance = UserRecurrentChargeTable.objects.filter(
                #     plan_quotation_id=plan_instance.quotation_id
                # ).last()
                if auto_charge_table_instance:
                    auto_charge_table_instance.is_active = False
                    auto_charge_table_instance.save()
                # plan_instance.is_active = False
                if not check_if_chestlock_and_if_oneoff(
                    plan_type=plan_type, plan_instance=plan_instance
                ):
                    plan_instance.recurrent_saving_status = RecurrentSavingStatus.NONE

                # check if the plan is a halal plan and if the lock is true
                # Update-1: I don't see why it is valid to keep locked halal plans (the code below) active anymore but I left it because I don't use it for anything important yet.
                # Update-2: I may need to track if it's the last amount they are withdrawing and inactivate the plan
                # TODO: Do Update-2 in Withdrawals
                # if plan_type.upper() == PlanType.HALAL:
                #     if plan_instance.lock == True:
                #         plan_instance.is_active = True

                plan_instance.save()

            # # Step 4: Check if the plan is a "JUST_ONCE" type of savings
            # if plan_instance.frequency.upper() == Frequency.JUST_ONCE:
            #     plan_instance.completed = True

            #     # if it is halal, ensure to check the date and lock fields
            #     # if plan_instance
            #     # plan_instance.is_active = False
            #     plan_instance.task = None
            #     plan_instance.save()

            if check_if_chestlock_and_if_oneoff(
                plan_instance=plan_instance,
                plan_type=plan_type,
            ):
                ChestlockService.activate_and_recalculate_interest(
                    chestlock=plan_instance
                )

                plan_instance.refresh_from_db()

                # if the payment was successful
                pay_upfront_interest.delay(
                    user_id=user.id,
                    amount=plan_instance.total_interest_earned,
                    quotation_id=plan_instance.quotation_id,
                    plan_type=plan_type,
                    access_token=access_token,
                )

            return Response(
                {
                    "message": "success",
                    "status": True,
                    "data": "wallet has been funded",
                    "charge_data": charge.get("message"),
                },
                status=status.HTTP_200_OK,
            )

        # check if the charge response is not successful

        # check for insufficient balance failed transaction
        if (charge.get("status") == False) and (
            "insufficient" in charge.get("message")
        ):
            return Response(
                {
                    "error": "735",
                    "status": False,
                    "message": charge.get("message"),
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        # error in amount or user_id
        else:
            return Response(
                {
                    "error": "735",
                    "status": False,
                    "message": charge.get("message"),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )


class WithdrawToWalletAPIView(GenericAPIView):
    serializer_class = WithdrawFromWalletSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        """VFD_Webhook_Whitelist
        This endpoint does a lot of things, you can withdraw from
        chestlock, halal, quicksavings plans by putting in the
        plan_type, plan_id and transaction_pin
        """
        # obtain the user
        user = request.user

        # pass the data to the serializer
        serializer = self.serializer_class(data=request.data)

        # attempt to validate the data
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        # obtain the fields from the data
        amount_to_withdraw: float = serializer.validated_data.get("amount")
        plan_type: PlanType = serializer.validated_data.get("plan_type")
        plan_id: int = serializer.validated_data.get("plan_id")

        # obtain the plan instance, wallet_type and wallet
        plan_instance = Utility.get_plan_instance(
            user=user,
            plan_id=plan_id,
            plan_type=plan_type,
        )
        wallet_type = getattr(WalletTypes, plan_type.upper())

        # For the special case it being a Chestlock plan, the amount_to_withdraw is the amout saved
        if isinstance(plan_instance, ChestLock):
            amount_to_withdraw = int(plan_instance.amount_saved)

        ############# CHECK IF THE WITHDRAWAL REGULATOR IS ACTIVE ###########
        if (
            not ConstantTable.get_constant_table_instance().withdraw_from_wallet_regulator
        ):
            return Response(
                data={
                    "error": "324",
                    "status": False,
                    "message": "service currently unavailable, please try again later",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        ##################CHECK THE USER'S PIN#################################
        # obtain the pin and access token
        transaction_pin: str = serializer.validated_data.get("transaction_pin")
        access_token: str = request.headers.get("Authorization", "").partition(" ")[-1]

        # print(f"ACCESS TOKEN {user.email}>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
        # print(f"ACCESS TOKEN {user.email}>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
        # print(f"ACCESS TOKEN {user.email}>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
        # print(access_token)
        # print(f"ACCESS TOKEN {user.email}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<")
        # print(f"ACCESS TOKEN {user.email}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<")
        # print(f"ACCESS TOKEN {user.email}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<")

        # verify if the pin is correct
        check_pin: bool | Response = verify_transaction_pin(
            transaction_pin=transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )

        # check if it is a Response object
        if isinstance(check_pin, Response):
            return check_pin

        ################## INQUIRE IF THE RECEIVER IS VALID#########################
        try:
            phone_number = obtain_phone_number(
                access_token=access_token, user_id=user.customer_user_id
            )
            phone_number_verification, verification_data = verify_account(
                access_token=access_token, phone_number=phone_number
            )
        except Exception as err:
            return Response(
                data={
                    "error": "343",
                    "status": False,
                    "message": str(err),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        #####check for custom set limits######

        # check if the amount is less than the Constant minimum withdrawal amount
        # MOVED THE CODE BELOW TO BE A VALIDATOR ON THE SERIALIZER
        # minimum_withdrawal_amount: float = ConstantTable.get_constant_table_instance().minimum_withdrawal_amount
        # if amount_to_withdraw < minimum_withdrawal_amount:
        #     # set the response
        #     response: dict = {
        #         "error": "324",
        #         "status": False,
        #         "message": f"you can not withdraw less than {minimum_withdrawal_amount} naira.",
        #     }
        #     # make the transaction failed
        #     transaction_service.set_failed_transaction(failure_reason=response.get("message"), payload=response)
        #     return Response(
        #         data=response,
        #         status=status.HTTP_403_FORBIDDEN,
        #     )

        # if the phone number is valid
        if phone_number_verification:
            ########### Create a Withdrawal transaction instance############
            transaction_instance = (
                TransactionService.create_withdrawal_to_wallet_transaction(
                    user=user,
                    amount=amount_to_withdraw,
                    wallet_type=wallet_type,
                    request_data=json.dumps(request.data),
                    quotation_id=plan_instance.quotation_id,
                    plan_type=plan_type,
                )
            )

            # Instantiate the transaction instance service
            transaction_service = TransactionService(
                transaction_instance=transaction_instance
            )
            # retrieve the user's wallet
            wallet = WalletSelector.get_or_create_savings_wallet(
                user=user,
                wallet_type=wallet_type,
            )

            ##################CHECK IF THE USER HAS THE MONEY TO WITHDRAW OR IF THE PLAN ALLOWS THE PERSON TO WITHDRAW#############
            if plan_instance.amount_saved >= amount_to_withdraw:
                if check_the_withdrawal_conditions_of_plans(
                    plan_instance=plan_instance
                ):
                    ################DEBIT THE USER and CREATE DEBITCREDIT INSTANCE################
                    debit_user_plan_and_wallet(
                            plan_instance=plan_instance,
                            amount=amount_to_withdraw,
                            wallet=wallet,
                            transaction_instance=transaction_instance,
                        )
                    # debit the user's plan and wallet
                    # try:
                    #     debit_user_plan_and_wallet(
                    #         plan_instance=plan_instance,
                    #         amount=amount_to_withdraw,
                    #         wallet=wallet,
                    #         transaction_instance=transaction_instance,
                    #     )
                    # except ValueError as err:
                    #     error_message = str(err)
                    #     response = {
                    #         "error": "695",
                    #         "status": False,
                    #         "message": "an error occurred, please contact customer care",
                    #         "error_message": error_message,
                    #     }
                    #     transaction_service.set_failed_transaction(
                    #         failure_reason=error_message,
                    #         payload=response,
                    #     )
                    #     return Response(
                    #         data=response,
                    #         status=status.HTTP_403_FORBIDDEN,
                    #     )

                    ###MAKE THE CALL TO TRANSFER THE FUNDS####
                    transfer = pay_withdrawal_and_handle_response(
                        transaction_id=transaction_instance.id,
                        amount=amount_to_withdraw,
                        phone_number=phone_number,
                    )

                    ###############CHECK IF IT IS SUCCESSFUL#############
                    if transfer.get("status"):
                        return Response(
                            {
                                "status": True,
                                "message": "money has been withdrawn",
                                "data": transfer.get("data").get("message"),
                            },
                            status=status.HTTP_200_OK,
                        )
                    #############IF FAILED, REVERSE THE MONEY############
                    #####Remember that the transfer call logs all data into transaction already
                    else:
                        # get the response from the call
                        transfer_data = transfer.get("data")
                        obtain_reason = (
                            transfer_data.get("message")
                            if transfer_data.get("data")
                            else transfer_data
                        )

                        # process the reversal
                        reverse_debit(
                            plan_id=plan_id,
                            user_id=user.id,
                            amount=amount_to_withdraw,
                            plan_type=plan_type,
                            request_data=request.data,
                            transaction_reference=str(
                                transaction_instance.transaction_id
                            ),
                        )

                        return Response(
                            {
                                "error": 800,
                                "status": False,
                                "message": obtain_reason,
                            },
                            status=status.HTTP_400_BAD_REQUEST,
                        )

                else:
                    response = {
                        "error": "692",
                        "status": False,
                        "message": "maturity date has not been reached",
                    }
                    transaction_service.set_failed_transaction(
                        failure_reason=response.get("message"), payload=response
                    )
                    return Response(
                        data=response,
                        status=status.HTTP_403_FORBIDDEN,
                    )
            else:
                response = {
                    "error": "695",
                    "status": False,
                    "message": "insufficient amount to withdraw",
                }
                transaction_service.set_failed_transaction(
                    failure_reason=response.get("message"),
                    payload=response,
                )
                return Response(
                    data=response,
                    status=status.HTTP_403_FORBIDDEN,
                )

        else:
            response = {
                "error": "404",
                "status": False,
                "message": "the account to be transferred to is not valid, contact customer care",
            }

            return Response(
                data=response,
                status=status.HTTP_400_BAD_REQUEST,
            )


class SaveFromCardAPIView(GenericAPIView):
    serializer_class = PayWithCardSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        """
        Pay money into a plan using your saved debit cards on agency banking.

        Currently supports:
            - Personal Ajo Savings
            - Personal Ajo Rotation Group Savings (ROSCA)
        """
        auth_header = request.headers.get("Authorization", "")
        token_type, _, access_token = auth_header.partition(" ")
        user = request.user

        # Regulator
        if (
            ConstantTable.get_constant_table_instance().pay_from_wallet_regulator
            == False
        ):
            response = {
                "error": "324",
                "message": "service currently unavailable, please try again later",
            }
            return Response(response, status=status.HTTP_403_FORBIDDEN)

        serializer = self.serializer_class(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        # get the plan here and get the value of the "is_activated" field
        plan_id = serializer.validated_data.get("plan_id")
        plan_type = serializer.validated_data.get("plan_type")
        plan_instance = Utility.get_plan_instance(
            user=user,
            plan_id=plan_id,
            plan_type=plan_type,
        )

        # Check for the things you wouldn't normally expect
        # Special check
        # Check if the plan is chestlock one off and if it is activated
        if (
            check_if_chestlock_and_if_oneoff(
                plan_type=plan_type, plan_instance=plan_instance
            )
            and plan_instance.is_activated
        ):
            return Response(
                {
                    "error": "671",
                    "status": False,
                    "message": "you cannot add more money to Chestlock ONEOFF plan",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        # Step 1: check if the plan is completed and if it is not active
        if plan_instance.completed is True:
            return Response(
                {
                    "error": "671",
                    "status": False,
                    "message": "plan is marked as complete, contact customer care",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        if plan_instance.is_active is False:
            return Response(
                {
                    "error": "672",
                    "status": False,
                    "message": "plan is marked as inactive, contact customer care",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        # Step 2:verify the pin
        transaction_pin = serializer.validated_data.get("transaction_pin")
        # verify the transaction pin
        check_pin = verify_transaction_pin(
            transaction_pin=transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )

        # check if it is a Response object
        if isinstance(check_pin, Response):
            return check_pin

        # Step 3: find the card and its authorization_code from the get_user_cards
        masked_pan = serializer.validated_data.get("masked_pan")
        # this is for test purposes
        auth_code = serializer.validated_data.get("auth_code")
        # if an auth_code was supplied, it's for test
        if auth_code:
            # use the auth_code for charging
            authorization_code = auth_code
        else:
            cards = AgencyBankingClass.get_user_cards(access_token=access_token)
            if cards.get("status") is True:
                if cards.get("message") == "Cards Retrieved":
                    cards_data = cards.get("data")
                    for card_data in cards_data:
                        if masked_pan in card_data.values():
                            authorization_code = card_data.get("authorization_code")
                            break
                    else:
                        return Response(
                            {
                                "error": "761",
                                "status": False,
                                "message": "this user does not possess this card",
                            },
                            status=status.HTTP_400_BAD_REQUEST,
                        )
                else:
                    return Response(
                        {
                            "error": "762",
                            "status": False,
                            "message": "this user has no cards",
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            else:
                return Response(
                    {
                        "error": "763",
                        "status": False,
                        "message": "error occured while checking cards, please try again",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        # Step 4: Get the periodic amount and how much is remaining to be saved
        amount = float(plan_instance.periodic_amount)
        remaining_amount = float(plan_instance.target) - float(
            plan_instance.amount_saved
        )

        # Step 5: check if the plan has been activated before to determine what to charge
        if plan_instance.is_activated is True:
            # check if the remaining amount is larger than the periodic amount
            if remaining_amount >= amount:
                # if the remaining amount is larger or equal to the periodic amount, charge the periodic amount
                amount_to_charge = amount
            else:
                # else charge the remaining amount
                amount_to_charge = remaining_amount

        # if the plan has not been activated before, charge the periodic_amount
        else:
            amount_to_charge = amount

        # check if the plan is Chestlock ONEOFF
        if check_if_chestlock_and_if_oneoff(
            plan_type=plan_type,
            plan_instance=plan_instance,
        ):
            if plan_instance.maturity_date <= timezone.localdate():
                return Response(
                    {
                        "error": "682",
                        "status": False,
                        "message": "the maturity date for this plan has passed, create a new one",
                    },
                    status.HTTP_403_FORBIDDEN,
                )
            amount_to_charge = plan_instance.target

        # Step 6: call the function to call the wrapper to charge authorization and handle cases for success and fail with Transaction, WalletSystem models
        # call the paystack wrapper function to charge the authorization
        charge_dict = create_transaction_and_charge_card(
            user=user,
            amount=amount_to_charge,
            auth_code=authorization_code,
            quotation_id=plan_instance.quotation_id,
            plan_type=plan_type,
        )

        # check if the charge response is successful
        if (charge_dict.get("status") is True) and (
            "debit_credit_info" in charge_dict.keys()
        ):
            # Step 7: Check if the plan is not activated
            if plan_instance.is_activated is False:
                # Activate the plan
                plan_instance.is_activated = True

                # Set the payment method
                plan_instance.payment_method = PaymentMethod.DEBIT_CARD

                if plan_instance != Frequency.JUST_ONCE and not (
                    check_if_chestlock_and_if_oneoff(
                        plan_type=plan_type, plan_instance=plan_instance
                    )
                ):
                    # Set the UserRecurrentChargeTable
                    # Set the UserRecurrentChargeTable plan task
                    try:
                        Utility.create_recurrent_charging_for_plan(
                            user=user,
                            plan_type=getattr(PlanType, plan_type.upper()),
                            plan_instance=plan_instance,
                            payment_method=PaymentMethod.DEBIT_CARD,
                            masked_pan=masked_pan,
                            auth_code=authorization_code,
                        )

                        # set the recurrent_saving_status
                        plan_instance.recurrent_saving_status = (
                            RecurrentSavingStatus.ACTIVE
                        )
                    except:
                        print(
                            f"A UserRecurrentChargeTable object was not created for {user.email}, with {plan_id} of {plan_type}"
                        )

            # Step 8: get the debit_credit_info and transaction instance to update fields
            debit_credit = charge_dict.get("debit_credit_info")
            transaction = charge_dict.get("transaction")

            # set the transaction wallet_balance_before, plan_balance_before and plan_instance's plan_balance_before
            transaction.wallet_balance_before = debit_credit.get("balance_before")
            transaction.plan_balance_before = plan_instance.amount_saved
            plan_instance.plan_balance_before = plan_instance.amount_saved

            # increase the plan_instance's amount_saved
            plan_instance.amount_saved += transaction.amount

            # set the transaction wallet/plan_balance_after and plan_instance's plan_balance_after
            transaction.wallet_balance_after = debit_credit.get("balance_after")
            transaction.plan_balance_after = (
                transaction.plan_balance_before + transaction.amount
            )
            plan_instance.plan_balance_after = (
                plan_instance.plan_balance_before + transaction.amount
            )

            # save the masked_pan_of_card used to the transaction instance
            transaction.masked_pan_of_card = masked_pan

            # save the changes made above
            with django_transaction.atomic():
                plan_instance.save()
                transaction.save()

            # get the userrecurrentcharge object
            auto_charge_table_instance = UserRecurrentChargeTable.objects.filter(
                plan_quotation_id=plan_instance.quotation_id
            ).last()

            # Step 9: Send the email using the asynchronous celery task
            ##################SEND THE EMAIL#################################################

            # obtain the next savings date and make it human readable
            if auto_charge_table_instance:
                next_savings_date = auto_charge_table_instance.next_run_time
                next_savings_date = next_savings_date.strftime("%b %d, %Y %H:%M")
            else:
                next_savings_date = None

            # obtain the time the transaction took place and make it human readable
            now = timezone.localtime(timezone.now())
            date_string = now.strftime("%b %d, %Y %H:%M")

            # obtain the maturity date and make it human readable
            maturity_date = plan_instance.maturity_date
            maturity_date = maturity_date.strftime("%B %d, %Y")

            # send the email using the email task
            send_html_email.delay(
                template_name=EmailTemplates.SAVEDFROMCARD,
                user_email=user.email,
                email_type=EmailTypes.SAVING,
                plan_type=plan_type,
                plan_transaction_object_id=transaction.id,
                email_subject="[CREDIT IN PLAN] Transaction Notification",
                plan_name=plan_instance.plan,
                amount=format_currency(transaction.amount),
                date_and_time=date_string,
                masked_pan=masked_pan,
                savings_wallet=getattr(WalletTypes, plan_type.upper()),
                next_savings_date=next_savings_date,
                maturity_date=maturity_date,
            )

            # Step 10: Check if the remaining amount was what was credited to know if the plan ends
            # To foolproof the solution: It will check two conditions
            # condition 1: if the remaining_amount is equal to the transaction.amount AND the plan's target is equal to the plan's amount saved so far
            # condition 2: if the plan's frequency type is JUST_ONCE

            if plan_instance.amount_saved >= plan_instance.target:
                # change the completed field and is_active filed
                plan_instance.completed = True
                if auto_charge_table_instance:
                    # change the auto charge table is_active to False
                    auto_charge_table_instance.is_active = False
                    auto_charge_table_instance.save()
                # plan_instance.is_active = False
                if not check_if_chestlock_and_if_oneoff(
                    plan_type=plan_type, plan_instance=plan_instance
                ):
                    plan_instance.recurrent_saving_status = RecurrentSavingStatus.NONE

                # Check if the plan is a halal plan and if the lock is true
                # the aim of this is for tracking things like withdrawal and plan checks
                # if plan_type.upper() == PlanType.HALAL:
                #     if plan_instance.lock is True:
                #         plan_instance.is_active = True

                # save all the changes
                plan_instance.save()

            if check_if_chestlock_and_if_oneoff(
                plan_instance=plan_instance,
                plan_type=plan_type,
            ):
                ChestlockService.activate_and_recalculate_interest(
                    chestlock=plan_instance
                )

                plan_instance.refresh_from_db()
                # if the payment was successful
                pay_upfront_interest.delay(
                    user_id=user.id,
                    amount=plan_instance.total_interest_earned,
                    quotation_id=plan_instance.quotation_id,
                    plan_type=plan_type,
                    access_token=access_token,
                )

            return Response(
                {
                    "message": "success",
                    "status": True,
                    "data": "wallet has been funded",
                }
            )

        # check for insufficient balance and failed transaction
        elif (charge_dict.get("status") is False) and (
            "insufficient" in charge_dict.get("message")
        ):
            return Response(
                {
                    "error": "735",
                    "status": False,
                    "message": "insufficient funds, please fund your account and try again",
                    "error_message": charge_dict.get("message"),
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        # error from any other thing
        else:
            return Response(
                {
                    "error": "775",
                    "status": False,
                    "message": charge_dict.get("message"),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )


class SaveFromNewCardAPIView(GenericAPIView):
    serializer_class = PayWithNewCardSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        # obtain the user
        user = request.user

        # obtain the data and validate it
        serializer = self.serializer_class(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        # obtain certain fields from the validated data
        plan_type = serializer.validated_data.get("plan_type")
        plan_id = serializer.validated_data.get("plan_id")
        # obtain the plan instance
        plan_instance = Utility.get_plan_instance(
            user=user,
            plan_id=plan_id,
            plan_type=plan_type,
        )

        # Special check
        # Check if the plan is chestlock one off and if it is activated
        if (
            check_if_chestlock_and_if_oneoff(
                plan_type=plan_type, plan_instance=plan_instance
            )
            and plan_instance.is_activated
        ):
            return Response(
                {
                    "error": "671",
                    "status": False,
                    "message": "you cannot add more money to Chestlock ONEOFF plan",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        # Step 1: Check if the plan is completed or it is marked as inactive
        if plan_instance.completed is True:
            return Response(
                {
                    "error": "671",
                    "status": False,
                    "message": "plan is marked as complete, contact customer care",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        if plan_instance.is_active is False:
            return Response(
                {
                    "error": "671",
                    "status": False,
                    "message": "plan is marked as inactive, contact customer care",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        # Step 2: obtain the amount to be paid at the moment
        amount_to_charge = float(plan_instance.periodic_amount)
        remaining_amount = float(plan_instance.target) - float(
            plan_instance.amount_saved
        )

        # Check if the plan has been activated before to enable you know what to charge
        if plan_instance.is_activated is True:
            # check if the remaining amount is larger than the periodic amount
            if remaining_amount >= amount_to_charge:
                pass
            else:
                amount_to_charge = remaining_amount

        # special case to influence the amount
        if check_if_chestlock_and_if_oneoff(
            plan_type=plan_type,
            plan_instance=plan_instance,
        ):
            if plan_instance.maturity_date <= timezone.localdate():
                return Response(
                    {
                        "error": "682",
                        "status": False,
                        "message": "the maturity date for this plan has passed, create a new one",
                    },
                    status.HTTP_403_FORBIDDEN,
                )
            amount_to_charge = plan_instance.target

        email = user.email
        ps = PayStack.initialize_transaction(email=email, amount=amount_to_charge)
        if ps.get("status") is True:
            TransactionService.create_deposit_by_card_transaction(
                user=user,
                amount=amount_to_charge,
                quotation_id=plan_instance.quotation_id,
                plan_type=getattr(PlanType, plan_type.upper()),
                wallet_type=getattr(WalletTypes, plan_type.upper()),
                unique_reference=ps.get("data").get("reference"),
            )

            return Response(data=ps, status=status.HTTP_200_OK)
        return Response(data=ps, status=status.HTTP_400_BAD_REQUEST)


class CardsResponseAfterChargeAPIView(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def process_failed_paystack_transaction(self, data: dict) -> None:
        """
        process a failed paystack transaction and update the database

        Args:
            data (dict): the paystack data object
        """

        # obtain the transaction object from the transaction table
        transaction = Transaction.objects.filter(
            unique_reference=data.get("data").get("reference")
        ).last()
        if transaction:
            # set the reason the transaction failed
            # since it it paystack, I will use the log history as the reason
            # transaction.failure_reason = str(data.get("data").get("log").get("history"))
            log = data.get("data").get("log")
            if log:
                if not log.get("history"):
                    transaction.failure_reason = str(log)
                else:
                    transaction.failure_reason = str(log.get("history"))
            else:
                transaction.failure_reason = "Declined"

            # change the status of the transaction
            transaction.status = Status.FAILED

            # save the paystack data
            transaction.payload = json.dumps(data)

            # save all the changes
            transaction.save()

    def post(self, request, *args, **kwargs):
        """
        Takes in the response after the user's card is tokenized for the first time
        from the frontend and creates a card for the user.
        *NOTE* A user can only add 3 cards
        """
        # obtain the access_token
        access_token = request.headers.get("Authorization", "").partition(" ")[-1]
        # obtain the data from the request body
        data = request.body.decode("utf-8")
        try:
            data = json.loads(data)
        except json.JSONDecodeError:
            return Response(
                {
                    "error": "603",
                    "status": False,
                    "message": "the request body is not a valid JSON object",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # example of the response
        # Failure response from Flutter Paystack SDK
        # CheckoutResponse{message: Unknown server response, card: PaymentCard{_cvc: 111, expiryMonth: 2, expiryYear: 26, _type: MasterCard, _last4Digits: 1281 , _number: null}, account: null, reference: B-and-R--e37edd55-5866-4a70-90f3-33dcd86de17b-fund, status: false, method: CheckoutMethod.card, verify: false}
        # obtain the status and verify bool of the transaction

        # obtaun the reference gotten from the data
        reference = data.get("reference")
        if not reference:
            return Response(
                {
                    "error": "603",
                    "status": False,
                    "message": "reference string not found in the data body sent to this endpoint",
                },
                status=status.HTTP_422_UNPROCESSABLE_ENTITY,
            )
        try:
            transaction = Transaction.objects.get(unique_reference=reference)
        except Transaction.DoesNotExist:
            return Response(
                {
                    "error": "615",
                    "status": False,
                    "message": "transaction instance not found on this server",
                },
                status=status.HTTP_404_NOT_FOUND,
            )

        # verify the transaction on paystack with the response
        verification = PayStack.verify_transaction(transaction_reference=reference)
        if verification.get("status") is False:
            return Response(
                {
                    "error": "701",
                    "status": False,
                    "message": "there was a problem verifying this transaction",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if transaction.status == Status.PENDING:
            # check if the verification object data status is success or failed
            if verification.get("data").get("status") == "success":
                PaystackWebHookURLAPIView.process_successful_paystack_transaction(
                    self,
                    data=verification,
                    webhook=False,
                    access_token=access_token,
                )
            elif verification.get("data").get("status") == "failed":
                self.process_failed_paystack_transaction(data=verification)
        else:
            return Response(
                {
                    "status": True,
                    "message": f"this transaction has already been processed as {transaction.status.lower()}",
                },
                status.HTTP_200_OK,
            )

        return Response(
            {
                "status": True,
                "message": "transaction processed successfully, check your transaction history",
            },
            status=status.HTTP_200_OK,
        )

        # if res["status"] is False:
        #     return Response(res, status=status.HTTP_400_BAD_REQUEST)

        # return Response(res, status=status.HTTP_201_CREATED)


class PaystackWebHookURLAPIView(APIView):
    permission_classes = (WebhookAuthorizationPermission,)

    def process_successful_paystack_transaction(
        self, data: dict, webhook: bool, access_token: str | None = None
    ) -> None:
        """
        Process the transaction and update the database
        """

        # obtain the transaction object from the transaction table
        transaction = Transaction.objects.filter(
            unique_reference=data.get("data").get("reference")
        ).last()
        if transaction:
            # change the status of the transaction
            transaction.status = Status.SUCCESS

            # collect the transaction completed date, payload
            transaction.transaction_date_completed = data.get("data").get("paid_at")
            # check if webhook
            if webhook:
                # if webhook, save the payload in the webhook field
                transaction.paystack_webhook_payload = json.dumps(data)
            else:
                # if not webhook
                transaction.payload = json.dumps(data)

            transaction.save()

            # get the wallet of the user
            wallet = WalletSystem.get_or_create_wallet(
                user=transaction.user,
                wallet_type=getattr(WalletTypes, transaction.plan_type),
            )

            # fund the wallet
            fund = WalletSystem.fund_balance(
                wallet=wallet,
                amount=transaction.amount,
                transaction_instance=transaction,
            )

            # obtain the plan instance
            plan_instance = Utility.get_plan_instance(
                user=transaction.user, quotation_id=transaction.quotation_id
            )

            # check if the plan si not activated
            if plan_instance.is_activated is False:
                # activate the plan
                plan_instance.is_activated = True

                # set the payment method
                plan_instance.payment_method = PaymentMethod.DEBIT_CARD

                # obtain masked pan
                masked_pan = f"{data.get('data').get('authorization').get('bin')}******{data.get('data').get('authorization').get('last4')}"

                if plan_instance.frequency != Frequency.JUST_ONCE and not (
                    check_if_chestlock_and_if_oneoff(plan_instance=plan_instance)
                ):
                    try:
                        Utility.create_recurrent_charging_for_plan(
                            user=transaction.user,
                            plan_type=getattr(PlanType, transaction.plan_type.upper()),
                            plan_instance=plan_instance,
                            payment_method=PaymentMethod.DEBIT_CARD,
                            masked_pan=masked_pan,
                            auth_code=data.get("data")
                            .get("authorization")
                            .get("authorization_code"),
                        )

                        # set the recurrent saving status
                        plan_instance.recurrent_saving_status = (
                            RecurrentSavingStatus.ACTIVE
                        )
                    except:
                        print(
                            f"A UserRecurrentChargeTable object was not created for {transaction.user.email}, with {plan_instance.plan_id} of {transaction.plan_type}"
                        )

            # update the information on balances
            # set the balance before information
            transaction.wallet_balance_before = fund.get("balance_before")
            transaction.plan_balance_before = plan_instance.amount_saved
            plan_instance.plan_balance_before = plan_instance.amount_saved

            # increase the plan_instance's amount_saved
            plan_instance.amount_saved += transaction.amount

            # set the balance after information
            transaction.wallet_balance_after = fund.get("balance_after")
            transaction.plan_balance_after = (
                transaction.plan_balance_before + transaction.amount
            )
            plan_instance.plan_balance_after = (
                plan_instance.plan_balance_before + transaction.amount
            )

            # save the masked_pan of the card used to the transaction instance
            transaction.masked_pan_of_card = masked_pan

            # save the changes made above
            plan_instance.save()
            transaction.save()

            ######################SEND AN EMAIL##########################
            auto_charge_table_instance = UserRecurrentChargeTable.objects.filter(
                plan_quotation_id=plan_instance.quotation_id,
            ).last()

            # obtain the next savings date and make it human readable
            if auto_charge_table_instance:
                next_savings_date = auto_charge_table_instance.next_run_time
                next_savings_date = next_savings_date.strftime("%b %d, %Y %H:%M")
            else:
                next_savings_date = None

            # obtain the time the transaction took place and make it human readable
            now = timezone.localtime(timezone.now())
            date_string = now.strftime("%b %d, %Y %H:%M")

            # obtain the maturity date and make it human readable
            maturity_date = plan_instance.maturity_date
            maturity_date = maturity_date.strftime("%B %d, %Y")

            # send the email using the email task
            send_html_email.delay(
                template_name=EmailTemplates.SAVEDFROMCARD,
                user_email=transaction.user.email,
                email_type=EmailTypes.SAVING,
                plan_type=transaction.plan_type,
                plan_transaction_object_id=transaction.id,
                email_subject="[CREDIT IN PLAN] Transaction Notification",
                plan_name=plan_instance.plan,
                amount=format_currency(transaction.amount),
                date_and_time=date_string,
                savings_wallet=getattr(WalletTypes, transaction.plan_type.upper()),
                next_savings_date=next_savings_date,
                maturity_date=maturity_date,
            )

            if plan_instance.amount_saved >= plan_instance.target:
                # change the completed field and is_active filed
                plan_instance.completed = True
                if auto_charge_table_instance:
                    # change the auto charge table is_active to False
                    auto_charge_table_instance.is_active = False
                    auto_charge_table_instance.save()

                if not check_if_chestlock_and_if_oneoff(plan_instance=plan_instance):
                    plan_instance.recurrent_saving_status = RecurrentSavingStatus.NONE

                # save all the changes
                plan_instance.save()

            if check_if_chestlock_and_if_oneoff(
                plan_instance=plan_instance,
            ):
                ChestlockService.activate_and_recalculate_interest(
                    chestlock=plan_instance
                )

                plan_instance.refresh_from_db()

                # if the payment was successful
                pay_upfront_interest.delay(
                    user_id=transaction.user.id,
                    amount=plan_instance.total_interest_earned,
                    quotation_id=plan_instance.quotation_id,
                    plan_type=PlanType.CHESTLOCK,
                    access_token=access_token,
                )

    # def _verify_signature(self, request_body, signature):
    #     secret_key = settings.PAYSTACK_AUTHORIZATION_TOKEN
    #     hash_value = hmac.new(secret_key.encode("utf-8"), request_body.encode("utf-8"), hashlib.sha512).hexdigest()
    #     return hash_value == signature

    def post(self, request, *args, **kwargs):
        # Retrieve the request's body
        request_body = request.body.decode("utf-8")
        try:
            event = json.loads(request_body)
        except json.decoder.JSONDecodeError:
            return Response(
                {
                    "error": "603",
                    "status": False,
                    "message": "the request body is not a valid JSON object",
                },
                status=status.HTTP_422_UNPROCESSABLE_ENTITY,
            )

        # Verify event origin
        # signature = request.META.get("HTTP_X_PAYSTACK_SIGNATURE")
        # if not signature:
        #     return Response({"message": "Missing signature header"}, status=status.HTTP_400_BAD_REQUEST)
        # if not self._verify_signature(request_body, signature):
        #     return Response({"message": "Invalid signature"}, status=status.HTTP_400_BAD_REQUEST)

        # Process charge success event
        if event.get("event") == "charge.success":
            reference = event.get("data").get("reference")

            # obtain the transacation instance
            transaction = Transaction.objects.filter(unique_reference=reference).last()
            if transaction:
                if transaction.status == Status.PENDING:
                    self.process_successful_paystack_transaction(
                        data=event, webhook=True
                    )
                else:
                    transaction.paystack_webhook_payload = json.dumps(event)
                    transaction.save()
                # Process charge success event here
            else:
                return Response(
                    {
                        "error": "755",
                        "status": False,
                        "message": "a transaction with this reference does not exist on this server",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        # Acknowledge event
        return Response(
            {"status": True, "message": "paystack information processed successfully"},
            status=status.HTTP_200_OK,
        )


class PauseSavingsPlanAPIView(GenericAPIView):
    serializer_class = PauseSavingsPlanSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def perform_validation_checks_on_the_plan_instance(
        self, plan_instance: ChestLock | Halal | QuickSavings
    ) -> Response | None:
        # Step 1: Check if the plan is completed or it is marked as inactive
        if plan_instance.completed is True:
            return Response(
                {
                    "error": "671",
                    "status": False,
                    "message": "plan is marked as complete, contact customer care",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        if plan_instance.is_active is False:
            return Response(
                {
                    "error": "671",
                    "status": False,
                    "message": "plan is marked as inactive, contact customer care",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        # get the current date
        now = timezone.localtime()
        # check if the maturity date has passed
        if datetime.date(now) >= plan_instance.maturity_date:
            return Response(
                {
                    "error": "671",
                    "status": False,
                    "message": "the maturity date has passed for this plan",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        # check if the plan has been activated with payment
        if plan_instance.is_activated is False:
            return Response(
                {
                    "error": "671",
                    "status": False,
                    "message": "this plan has not been activated with payment before",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

    def post(self, request, *args, **kwargs):
        """
        this pauses a savings plan from recurrent savings
        """
        # obtain the user
        user = request.user

        # obtain the data and validate it
        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)
        # obtain certain fields from validated data
        plan_type = serializer.validated_data.get("plan_type")
        plan_id = serializer.validated_data.get("plan_id")

        # obtain the plan instance
        plan_instance = Utility.get_plan_instance(
            user=user,
            plan_id=plan_id,
            plan_type=plan_type,
        )

        # perform all the checks on the plan
        checks = self.perform_validation_checks_on_the_plan_instance(
            plan_instance=plan_instance
        )
        if isinstance(checks, Response):
            return checks

        # obtain the UserRecurrentChargeTable instance for the plan instance
        user_recurrent_charge_instance = UserRecurrentChargeTable.objects.filter(
            plan_quotation_id=plan_instance.quotation_id
        ).last()

        if not user_recurrent_charge_instance:
            return Response(
                {
                    "error": "689",
                    "status": False,
                    "message": "there is no recurrent saving set for this plan",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        # check if the recurrent
        if user_recurrent_charge_instance.is_active is False:
            return Response(
                {
                    "status": True,
                    "message": "the recurrent saving for this plan is already paused",
                },
                status=status.HTTP_200_OK,
            )

        # make the recurrent charge instance inactive
        user_recurrent_charge_instance.is_active = False
        # change the state of the recurrent_saving_status of the plan
        plan_instance.recurrent_saving_status = RecurrentSavingStatus.PAUSED

        # save the changes
        user_recurrent_charge_instance.save()
        plan_instance.save()

        return Response(
            {
                "status": True,
                "message": "the recurrent saving for this plan has been paused",
            },
            status=status.HTTP_200_OK,
        )


class ContinueSavingsPlanAPIView(GenericAPIView):
    serializer_class = ContinueSavingsPlanSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        """
        this continues a savings plan for recurrent savings
        """

        # obtain the user
        user = request.user

        # obtain the data and validate it
        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        # obtain the fields from validated data
        plan_type = serializer.validated_data.get("plan_type")
        plan_id = serializer.validated_data.get("plan_id")

        # obtain the plan instance
        plan_instance = Utility.get_plan_instance(
            user=user,
            plan_id=plan_id,
            plan_type=plan_type,
        )

        # perform all the checks on the plan
        checks = PauseSavingsPlanAPIView.perform_validation_checks_on_the_plan_instance(
            self, plan_instance=plan_instance
        )
        if isinstance(checks, Response):
            return checks

        # obtain the UserRecurrentChargeTable instance for the plan instance
        user_recurrent_charge_instance = UserRecurrentChargeTable.objects.filter(
            plan_quotation_id=plan_instance.quotation_id
        ).last()

        # check if there a recurrent saving instance
        if not user_recurrent_charge_instance:
            return Response(
                {
                    "error": "689",
                    "status": False,
                    "message": "there is no recurrent saving set for this plan",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # check if the recurrent saving is already active
        if user_recurrent_charge_instance.is_active is True:
            return Response(
                {
                    "status": True,
                    "message": "the recurrent saving for this plan is already active",
                },
                status=status.HTTP_200_OK,
            )

        # activate the recurrent charge instance
        user_recurrent_charge_instance.is_active = True

        # set the next run time
        user_recurrent_charge_instance.next_run_time = Utility.set_next_run_time(
            interval=plan_instance.frequency, hour=plan_instance.hour
        )

        # change the state of the recurrent_saving_status of the plan
        plan_instance.recurrent_saving_status = RecurrentSavingStatus.ACTIVE

        # save the changes
        user_recurrent_charge_instance.save()
        plan_instance.save()

        return Response(
            {
                "status": True,
                "message": "the recurrent saving for this plan has been activated",
            },
            status=status.HTTP_200_OK,
        )


class DeleteSavingsPlanAPIView(GenericAPIView):
    serializer_class = DeleteSavingsPlanSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def obtain_withdrawal_endpoint(self, request) -> str:
        """
        this is a function that retrieves the withdrawal endpoint with the current domain the request is
        being made from
        """
        current_site = get_current_site(request)
        base_url = f"{request.scheme}://{current_site.domain}"

        # use the base_url variable to construct the endpoint URL
        return f"{base_url}{reverse('withdraw_to_wallet')}"

    def move_funds_to_user_wallet(
        self,
        request,
        plan_instance: QuickSavings,
        transaction_pin: str,
        plan_type: PlanType,
    ) -> dict:
        # obtain the url
        url = self.obtain_withdrawal_endpoint(request=request)

        # obtain the access_token
        token_type, _, access_token = request.headers.get(
            "Authorization", ""
        ).partition(" ")

        # set the headers
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        # the request body
        data = json.dumps(
            {
                "amount": float(plan_instance.amount_saved),
                "plan_id": int(plan_instance.id),
                "transaction_pin": str(transaction_pin),
                "plan_type": str(plan_type),
            }
        )

        try:
            r = requests.post(
                url=url,
                headers=headers,
                data=data,
            )

            response = r.json()
            return response
        except requests.exceptions.RequestException as err:
            return {
                "status": False,
                "message": "an error occurred, try again",
                "error_message": str(err),
            }

    @swagger_auto_schema(request_body=DeleteSavingsPlanSerializer)
    def delete(self, request, *args, **kwargs):
        """
        this deletes a savings plan
        """
        # obtain the user
        user = request.user

        # obtain the access_token
        token_type, _, access_token = request.headers.get(
            "Authorization", ""
        ).partition(" ")

        # obtain the data and validate it
        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        # obtain the fields from the validated data
        plan_type = serializer.validated_data.get("plan_type")
        plan_id = serializer.validated_data.get("plan_id")
        transaction_pin = serializer.validated_data.get("transaction_pin")

        # obtain the plan instance
        plan_instance = Utility.get_plan_instance(
            user=user,
            plan_id=plan_id,
            plan_type=plan_type,
        )

        # step 1: confirm the plan type
        # deleting is not supported for Chestlock or Halal plans yet
        if plan_type.upper() in [PlanType.CHESTLOCK, PlanType.HALAL]:
            return Response(
                {
                    "error": "923",
                    "status": False,
                    "message": "deletion of plans for this plan type is not supported",
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        # deletion for Quicksavings is supported
        if plan_type.upper() == PlanType.QUICKSAVINGS:
            # check if the plan has been pseudo deleted before
            if plan_instance.deleted is True:
                return Response(
                    {
                        "error": "404",
                        "status": False,
                        "message": "this plan does not exist",
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )
            # check if the plan has not been activated before
            if plan_instance.is_activated is False:
                # delete the plan
                plan_instance.delete()

            # if the plan has been activated before
            else:
                # check if there is money in the plan instance
                if plan_instance.amount_saved > 0:
                    # verify the transaction pin
                    check_pin = verify_transaction_pin(
                        transaction_pin=transaction_pin,
                        access_token=access_token,
                        customer_user_id=user.customer_user_id,
                    )

                    # check if it is a Response object
                    if isinstance(check_pin, Response):
                        return check_pin

                    # move the saved amount to the person's wallet
                    move_funds = self.move_funds_to_user_wallet(
                        request=request,
                        plan_instance=plan_instance,
                        transaction_pin=transaction_pin,
                        plan_type=plan_type,
                    )

                    if move_funds.get("status") is False:
                        return Response(
                            {
                                "error": "655",
                                "status": False,
                                "message": "there was a problem encountered while moving money to wallet",
                                "error_message": move_funds.get("error_message"),
                            },
                            status=status.HTTP_400_BAD_REQUEST,
                        )
                # obtain the plan instance again and make the changes
                plan_instance = Utility.get_plan_instance(
                    user=user,
                    plan_id=plan_id,
                    plan_type=plan_type,
                )
                # change is_active to False
                plan_instance.is_active = False

                # change deleted to True
                plan_instance.deleted = True

                # save the changes
                plan_instance.save()

                # check for recurrent saving instance and stop it
                user_recurrent_charge_table_instance = (
                    UserRecurrentChargeTable.objects.filter(
                        plan_quotation_id=plan_instance.quotation_id
                    ).last()
                )

                if user_recurrent_charge_table_instance:
                    # change the is_active field to False
                    user_recurrent_charge_table_instance.is_active = False
                    # save the change
                    user_recurrent_charge_table_instance.save()

            return Response(
                {
                    "status": True,
                    "message": f"the quicksavings plan {plan_instance.plan} belonging to {plan_instance.user} has been deleted",
                },
                status=status.HTTP_200_OK,
            )


class SaveFromAgencyWalletAPIViewV2(GenericAPIView):
    serializer_class = PayWithAgencyWalletSerializerV2
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        """
        Pay money into a plan from your agency banking wallet.

        Currently supports:
            - Personal Ajo Savings: "AJO"
            - Personal Ajo Rotation Group Savings (ROSCA): "ROTATIONGROUP"
            - Onlending: "ONLENDING"
        """
        access_token = request.headers.get("Authorization", "").partition(" ")[-1]
        user = request.user

        if not ConstantTable.get_constant_table_instance().pay_from_wallet_regulator:
            return self.handle_failed_response(
                code="324",
                message="service currently unavailable, please try again later",
            )

        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        # get the fields from the serializer
        plan_type = serializer.validated_data.get("plan_type")
        plan_id = serializer.validated_data.get("plan_id")
        transaction_pin = serializer.validated_data.get("transaction_pin")
        amount = serializer.validated_data.get("amount")

        # the valid plan types
        valid_plan_types = [
            PlanType.AJO,
            PlanType.ROTATIONGROUP,
            PlanType.ONLENDING,
        ]

        # express that the endpoint is not ready for Chestlock, Halal, Quicksavings
        if plan_type not in valid_plan_types:
            return self.handle_failed_response(
                code="590",
                message="this plan type is not supported yet",
            )

        # remove sensitive data from the request
        request_data = request.data
        request_data.pop("transaction_pin", None)

        # verify the transaction pin
        verify_agent_pin = verify_transaction_pin(
            transaction_pin=transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )
        if isinstance(verify_agent_pin, Response):
            return verify_agent_pin

        # Personal Ajo
        if plan_type == PlanType.AJO:
            try:
                ajo_plan = PersonalAjoSavingsSelector(
                    id=plan_id, user=user
                ).get_ajo_savings_plan()

                if not ajo_plan.is_active:
                    return self.handle_failed_response(
                        code="632",
                        message="this plan is no longer active",
                    )

                if ajo_plan.amount_saved >= ajo_plan.expected_amount:
                    if not ajo_plan.lock:
                        ajo_plan.is_active = False
                        ajo_plan.save()
                        return self.handle_failed_response(
                            code="672",
                            message="this plan's target has been reached and deactivated",
                        )
                    else:
                        return self.handle_failed_response(
                            code="641",
                            message="you cannot add more money to this plan as target has been reached",
                        )

                # create transaction instance and retrieve wallet
                transaction = self.create_plan_type_transaction(
                    plan_type=plan_type,
                    user=user,
                    amount=amount,
                    quotation_id=ajo_plan.quotation_id,
                    plan_name=ajo_plan.name,
                )
                wallet = PersonalSelector(user=user).get_savings_wallet()

                # charge the user's wallet
                charge = UserAgencyBankingWalletPaymentProcessor.pay(
                    wallet=wallet,
                    amount=amount,
                    transaction_pin=transaction_pin,
                    transaction=transaction,
                    user=user,
                )
                if not charge.get("status"):
                    charge.pop("transaction", None)
                    return self.handle_failed_response(
                        code="400",
                        message=charge.get("message"),
                        error_message=charge,
                    )

                # perform finishing touches
                fund_personal_ajo_savings_position(
                    ajo_savings=ajo_plan,
                    amount=amount,
                    transaction=transaction,
                    debit_credit_info=charge.get("debit_credit_info"),
                )

                if not ajo_plan.commission_paid:
                    pay_personal_ajo_commissions.delay(
                        ajo_savings_id=ajo_plan.id,
                        user_id=user.id,
                    )

            except ValueError as err:
                return value_error_response(error=err, code="400")

        # Rotation Group
        if plan_type == PlanType.ROTATIONGROUP:
            try:
                rotation_group = RotationGroup.objects.get(id=plan_id)
            except RotationGroup.DoesNotExist as err:
                return self.handle_failed_response(
                    code="404", message=f"Rotation group with id {id} does not exist"
                )

            try:
                contribution_amount = (
                    RotationGroupPay.rotation_group_checks_before_payment(
                        rotation_group=rotation_group
                    )
                )
                collector_info = RotationGroupPay.collecting_rotation_group_member_checks_before_payment(
                    rotation_group=rotation_group,
                    user=user,
                )
                collector_wallet: WalletSystem = collector_info.get("wallet")
                collector: RotationGroupMember = collector_info.get("member")

            except ValueError as err:
                return value_error_response(error=err, code="715")

            try:
                transaction = self.create_plan_type_transaction(
                    plan_type=plan_type,
                    user=user,
                    amount=contribution_amount,
                    plan_name=rotation_group.name,
                    quotation_id=rotation_group.quotation_id,
                    collector=collector,
                )

                charge = UserAgencyBankingWalletPaymentProcessor.pay(
                    user=user,
                    wallet=collector_wallet,
                    amount=contribution_amount,
                    transaction_pin=transaction_pin,
                    transaction=transaction,
                )
                if not charge.get("status"):
                    charge.pop("transaction", None)
                    return self.handle_failed_response(
                        code="400",
                        message=charge.get("message"),
                        error_message=charge,
                    )

                RotationGroupPay.pay_rotation_group_contribution(
                    rotation_group=rotation_group,
                    user=user,
                    transaction=transaction,
                    contribution_amount=contribution_amount,
                    debit_credit_info=charge.get("debit_credit_info"),
                )

            except Exception as err:
                return self.handle_failed_response(
                    code="400",
                    message="an error occurred, read 'error_message'",
                    error_message=str(err),
                )

        if plan_type == PlanType.ONLENDING:
            try:
                onlending_plan = OnlendingSelector.get_onlending_plan_by_id(
                    id=plan_id,
                    user=user,
                )

                amount = onlending_plan.target

                if onlending_plan.ajo_user:
                    return self.handle_failed_response(
                        code="777",
                        message="this plan should not belong to an ajo user",
                    )

                plan_checks = PlanPayments.onlending_plan_checks(
                    onlending_plan=onlending_plan
                )
                if plan_checks:
                    return self.handle_failed_response(
                        message=plan_checks.get("message"),
                        code=plan_checks.get("code"),
                    )

                onlending_selector = OnlendingSelector(user=user)
                onlending_wallet = onlending_selector.get_onlending_wallet()

                # create transaction instance and retrieve wallet
                transaction = PlanPayments.create_funding_transaction(
                    onlending_wallet=onlending_wallet,
                    amount=amount,
                    onlending_plan=onlending_plan,
                )

                # charge the user's wallet
                charge = UserAgencyBankingWalletPaymentProcessor.pay(
                    wallet=onlending_wallet,
                    amount=amount,
                    transaction_pin=transaction_pin,
                    transaction=transaction,
                    user=user,
                )

                if not charge.get("status"):
                    charge.pop("transaction", None)
                    return self.handle_failed_response(
                        code="400",
                        message=charge.get("message"),
                        error_message=charge,
                    )

                # perform finishing touches
                PlanPayments.update_positions_activate_plan(
                    amount=amount,
                    onlending_plan=onlending_plan,
                    fund_transaction=transaction,
                )

                onlending_plan.refresh_from_db()
                if onlending_plan.interest_type == InterestType.UPFRONT:
                    pay_upfront_interest.delay(
                        user_id=user.id,
                        amount=onlending_plan.total_interest_earned,
                        quotation_id=onlending_plan.quotation_id,
                        plan_type=plan_type,
                        access_token=access_token,
                    )
                else:
                    # add to the onlending plan
                    onlending_wallet.refresh_from_db()
                    PlanPayments.pay_interest_into_plan(
                        plan_instance=onlending_plan,
                        wallet=onlending_wallet,
                    )

            except ValueError as err:
                return value_error_response(error=err, code="400")

            except Exception as err:
                return self.handle_failed_response(
                    message="something went wrong",
                    code="980",
                    error_message=str(err),
                )

        return Response(
            {
                "status": True,
                "message": "plan funded successfully",
            },
            status.HTTP_200_OK,
        )

    def handle_failed_response(
        self, code: str, message: str, error_message: str = None
    ) -> Response:
        response = {
            "status": False,
            "error": code,
            "message": message,
        }

        if error_message:
            response["error_message"] = error_message

        return Response(
            data=response,
            status=status.HTTP_400_BAD_REQUEST,
        )

    def create_plan_type_transaction(
        self,
        plan_type: PlanType,
        user: AbstractUser,
        amount: float,
        plan_name: str,
        quotation_id: str = None,
        collector: RotationGroupMember | None = None,
    ) -> Transaction:
        if plan_type == PlanType.AJO:
            transaction = TransactionService.create_deposit_by_wallet_transaction(
                user=user,
                amount=amount,
                wallet_type=WalletTypes.AJO_PERSONAL,
                quotation_id=quotation_id,
                plan_type=plan_type,
                transaction_form_type=TransactionFormType.WALLET_DEPOSIT,
                description=f"{amount} was funded into your ajo savings plan, {plan_name}.",
            )

        if plan_type == PlanType.ROTATIONGROUP:
            transaction = TransactionService.create_deposit_by_wallet_transaction(
                user=collector.user,
                amount=amount,
                wallet_type=WalletTypes.ROSCA_PERSONAL,
                quotation_id=quotation_id,
                plan_type=plan_type,
                description=f"{amount} was credited to collector {collector.position}, {collector.user.email}, for {plan_name} rotation group, paid by {user.email}",
            )

        return transaction


class SaveFromCardAPIViewV2(GenericAPIView):
    serializer_class = PayWithCardSerializerV2
    permission_classes = (permissions.IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        """
        Pay money into a plan from your agency banking wallet.

        Currently supports:
            - Personal Ajo Savings: "AJO"
            - Personal Ajo Rotation Group Savings (ROSCA); "ROTATIONGROUP"
            - Onlending: "ONLENDING"
        """
        access_token = request.headers.get("Authorization", "").partition(" ")[-1]
        user = request.user

        if not ConstantTable.get_constant_table_instance().pay_from_wallet_regulator:
            return self.handle_failed_response(
                code="324",
                message="service currently unavailable, please try again later",
            )

        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(err)

        # get the fields from the serializer
        plan_type = serializer.validated_data.get("plan_type")
        plan_id = serializer.validated_data.get("plan_id")
        transaction_pin = serializer.validated_data.get("transaction_pin")
        amount = serializer.validated_data.get("amount")
        masked_pan = serializer.validated_data.get("masked_pan")
        auth_code = serializer.validated_data.get("auth_code", None)

        # Ensure that auth_code is not being passed in PRODUCTION
        if (auth_code) and (settings.ENVIRONMENT != "development"):
            return Response(
                {
                    "status": False,
                    "error": "403",
                    "message": "the auth code should not be passed in production",
                },
                status.HTTP_403_FORBIDDEN,
            )

        # the valid plan types
        valid_plan_types = [PlanType.AJO, PlanType.ROTATIONGROUP]

        # express that the endpoint is not ready for Chestlock, Halal, Quicksavings
        if plan_type not in valid_plan_types:
            return self.handle_failed_response(
                code="590",
                message="this plan type is not supported yet",
            )

        # remove sensitive data from the request
        request_data = request.data
        request_data.pop("transaction_pin", None)

        # verify the transaction pin
        verify_agent_pin = verify_transaction_pin(
            transaction_pin=transaction_pin,
            access_token=access_token,
            customer_user_id=user.customer_user_id,
        )
        if isinstance(verify_agent_pin, Response):
            return verify_agent_pin

        # Personal Ajo
        if plan_type == PlanType.AJO:
            try:
                ajo_plan = PersonalAjoSavingsSelector(
                    id=plan_id, user=user
                ).get_ajo_savings_plan()

                if not ajo_plan.is_active:
                    return self.handle_failed_response(
                        code="632",
                        message="this plan is no longer active",
                    )

                if ajo_plan.amount_saved >= ajo_plan.expected_amount:
                    if not ajo_plan.lock:
                        ajo_plan.is_active = False
                        ajo_plan.save()
                        return self.handle_failed_response(
                            code="672",
                            message="this plan's target has been reached and deactivated",
                        )
                    else:
                        return self.handle_failed_response(
                            code="641",
                            message="you cannot add more money to this plan as target has been reached",
                        )

                # create transaction instance and retrieve wallet
                transaction = self.create_plan_type_transaction(
                    plan_type=plan_type,
                    user=user,
                    amount=amount,
                    quotation_id=ajo_plan.quotation_id,
                    plan_name=ajo_plan.name,
                )
                wallet = PersonalSelector(user=user).get_savings_wallet()

                # charge the user's wallet
                charge = SavedAgencyBankingDebitCardPaymentProcessor.pay(
                    amount=amount,
                    user=user,
                    access_token=access_token,
                    transaction=transaction,
                    masked_pan=masked_pan,
                    wallet=wallet,
                    auth_code=auth_code,
                )
                if not charge.get("status"):
                    charge.pop("transaction", None)
                    return self.handle_failed_response(
                        code="400",
                        message=charge.get("message"),
                        error_message=charge,
                    )

                # perform finishing touches
                fund_personal_ajo_savings_position(
                    ajo_savings=ajo_plan,
                    amount=amount,
                    transaction=transaction,
                    debit_credit_info=charge.get("debit_credit_info"),
                )

                if not ajo_plan.commission_paid:
                    pay_personal_ajo_commissions.delay(
                        ajo_savings_id=ajo_plan.id,
                        user_id=user.id,
                    )

            except ValueError as err:
                return value_error_response(error=err, code="400")

        # Rotation Group
        if plan_type == PlanType.ROTATIONGROUP:
            try:
                rotation_group = RotationGroup.objects.get(id=plan_id)
            except RotationGroup.DoesNotExist as err:
                return self.handle_failed_response(
                    code="404", message=f"Rotation group with id {id} does not exist"
                )

            try:
                contribution_amount = (
                    RotationGroupPay.rotation_group_checks_before_payment(
                        rotation_group=rotation_group
                    )
                )
                collector_info = RotationGroupPay.collecting_rotation_group_member_checks_before_payment(
                    rotation_group=rotation_group,
                    user=user,
                )
                collector_wallet = collector_info.get("wallet")
                collector = collector_info.get("member")

            except ValueError as err:
                return value_error_response(error=err, code="715")

            try:
                transaction = self.create_plan_type_transaction(
                    plan_type=plan_type,
                    user=collector.user,
                    amount=contribution_amount,
                    plan_name=rotation_group.name,
                    quotation_id=rotation_group.quotation_id,
                    collector=collector,
                )

                charge = SavedAgencyBankingDebitCardPaymentProcessor.pay(
                    amount=contribution_amount,
                    masked_pan=masked_pan,
                    access_token=access_token,
                    transaction=transaction,
                    user=user,
                    wallet=collector_wallet,
                    auth_code=auth_code,
                )
                if not charge.get("status"):
                    charge.pop("transaction", None)
                    return self.handle_failed_response(
                        code="400",
                        message=charge.get("message"),
                        error_message=charge,
                    )

                RotationGroupPay.pay_rotation_group_contribution(
                    rotation_group=rotation_group,
                    user=user,
                    transaction=transaction,
                    contribution_amount=contribution_amount,
                    debit_credit_info=charge.get("debit_credit_info"),
                )

            except Exception as err:
                return self.handle_failed_response(
                    code="400",
                    message="an error occurred, read 'error_message'",
                    error_message=str(err),
                )

        if plan_type == PlanType.ONLENDING:
            try:
                onlending_plan = OnlendingSelector.get_onlending_plan_by_id(
                    id=plan_id,
                    user=user,
                )

                amount = onlending_plan.target

                if onlending_plan.ajo_user:
                    return self.handle_failed_response(
                        code="777",
                        message="this plan should not belong to an ajo user",
                    )

                plan_checks = PlanPayments.onlending_plan_checks(
                    onlending_plan=onlending_plan
                )
                if plan_checks:
                    return self.handle_failed_response(
                        message=plan_checks.get("message"),
                        code=plan_checks.get("code"),
                    )

                onlending_selector = OnlendingSelector(user=user)
                onlending_wallet = onlending_selector.get_onlending_wallet()

                # create transaction instance and retrieve wallet
                transaction = PlanPayments.create_funding_transaction(
                    onlending_wallet=onlending_wallet,
                    amount=amount,
                    onlending_plan=onlending_plan,
                    transaction_form_type=TransactionFormType.CARD_DEPOSIT,
                )

                # charge the user's debit card
                charge = SavedAgencyBankingDebitCardPaymentProcessor.pay(
                    amount=amount,
                    user=user,
                    access_token=access_token,
                    transaction=transaction,
                    masked_pan=masked_pan,
                    wallet=onlending_wallet,
                    auth_code=auth_code,
                )

                if not charge.get("status"):
                    charge.pop("transaction", None)
                    return self.handle_failed_response(
                        code="400",
                        message=charge.get("message"),
                        error_message=charge,
                    )

                # perform finishing touches
                PlanPayments.update_positions_activate_plan(
                    onlending_plan=onlending_plan,
                    fund_transaction=transaction,
                    amount=amount,
                )

                onlending_plan.refresh_from_db()
                if onlending_plan.interest_type == InterestType.UPFRONT:
                    pay_upfront_interest.delay(
                        user_id=user.id,
                        amount=onlending_plan.total_interest_earned,
                        quotation_id=onlending_plan.quotation_id,
                        plan_type=plan_type,
                        access_token=access_token,
                    )
                else:
                    # add to the onlending plan
                    onlending_wallet.refresh_from_db()
                    PlanPayments.pay_interest_into_plan(
                        plan_instance=onlending_plan,
                        wallet=onlending_wallet,
                    )

            except ValueError as err:
                return value_error_response(error=err, code="400")

            except Exception as err:
                return self.handle_failed_response(
                    message="something went wrong",
                    code="980",
                    error_message=str(err),
                )
        return Response(
            {
                "status": True,
                "message": "plan funded successfully",
            },
            status.HTTP_200_OK,
        )

    def handle_failed_response(
        self, code: str, message: str, error_message: str = None
    ) -> Response:
        response = {
            "status": False,
            "error": code,
            "message": message,
        }

        if error_message:
            response["error_message"] = error_message

        return Response(
            data=response,
            status=status.HTTP_400_BAD_REQUEST,
        )

    def create_plan_type_transaction(
        self,
        plan_type: PlanType,
        user: AbstractUser,
        amount: float,
        plan_name: str,
        quotation_id: str = None,
        collector: RotationGroupMember | None = None,
    ) -> Transaction:
        reference = f"SAVINGS-CR-{uuid.uuid4()}"
        if plan_type == PlanType.AJO:
            transaction = TransactionService.create_deposit_by_card_transaction(
                user=user,
                amount=amount,
                wallet_type=WalletTypes.AJO_PERSONAL,
                quotation_id=quotation_id,
                plan_type=plan_type,
                description=f"{amount} was funded into your ajo savings plan, {plan_name}.",
                unique_reference=reference,
            )

        if plan_type == PlanType.ROTATIONGROUP:
            transaction = TransactionService.create_deposit_by_card_transaction(
                user=collector.user,
                amount=amount,
                wallet_type=WalletTypes.ROSCA_PERSONAL,
                quotation_id=quotation_id,
                plan_type=plan_type,
                description=f"{amount} was credited to collector {collector.position}, {collector.user.email}, for {plan_name} rotation group, paid by {user.email}",
                unique_reference=reference,
            )

        return transaction


class PayForOnlendingPlanAPIView(GenericAPIView):
    serializer_class = PayForOnlendingPlanSerializer
    permission_classes = (permissions.IsAuthenticated,)

    def failed_response(
        self,
        message: str,
        code: str,
        error_message: Optional[str] = None,
    ) -> Response:

        data = {
            "status": False,
            "error": code,
            "message": message,
        }

        if error_message:
            data["error_message"] = error_message

        return Response(
            data,
            status.HTTP_400_BAD_REQUEST,
        )

    def post(self, request, *args, **kwargs):
        """
        Pay for your onlending plan with the ajo user's spend wallet, an ajo savings plan,
        the agent's ajo wallet or the onlending main wallet.

        If you are using AJO_SPENDING, ONLENDING_MAIN or AJO_USER, you are expected to get an OTP by dialing
        the USSD code.
        If you are using the AJO_AGENT wallet, you need to put in the transaction pin

        Sample request data:
        {
            "plan_id": 7,
            "wallet_type": "AJO_AGENT",
            "transaction_pin: 0000
        }

        {
            "plan_id": 11,
            "wallet_type": "AJO_SPENDING",
            "otp": 891223
        }

        {
            "plan_id": 11,
            "wallet_type": "ONLENDING_MAIN",
            "otp": 891228
        }

        {
            "plan_id": 11,
            "wallet_type": "ONLENDING_MAIN",
            "transaction_pin": 1234
        }

        {
            "plan_id": 5,
            "wallet_type": "AJO_USER",
            "ajo_plan_id": 130,
            "otp": 901221
        }

        Currently Supports:
            1. Agent Ajo Wallet: "AJO_AGENT"
            2. Ajo User's Spend Wallet: "AJO_SPENDING"
            3. Ajo Savings Plan: "AJO_USER"
            4. Onlending Main Wallet: "ONLENDING_MAIN"
        """
        user = request.user
        serializer = self.serializer_class(data=request.data)
        
        log_onlending = TempLogOnlendingPayments.objects.create(user=user)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        plan_id: int = serializer.validated_data.get("plan_id")
        wallet_type: str = serializer.validated_data.get("wallet_type")
        otp: str = serializer.validated_data.get("otp")
        transaction_pin: str = serializer.validated_data.get("transaction_pin")
        ajo_plan_id: int = serializer.validated_data.get("ajo_plan_id")
        valid_wallet_types = [
            "AJO_AGENT",
            "AJO_SPENDING",
            "ONLENDING_MAIN",
            "AJO_USER",
        ]

        try:
            onlending_plan = OnlendingSelector.get_onlending_plan_by_id(
                id=plan_id,
                user=user,
            )
        except ValueError as err:
            return value_error_response(
                error=err,
                code="817",
            )

        if onlending_plan.onlending_type == "RECURRENT":# log here
            amount = onlending_plan.periodic_amount
        else: 
            amount = onlending_plan.target
        
        log_data = {"onlending_type": onlending_plan.onlending_type}
        log_onlending.wallet_type = wallet_type
        log_onlending.amount = amount
        log_onlending.response = log_data
        log_onlending.save()
        
        ajo_user = onlending_plan.ajo_user
        if not ajo_user and wallet_type != "ONLENDING_MAIN":
            return self.failed_response(
                message="this onlending plan was not created for an ajo user",
                code="771",
            )

        log_onlending.ajo_user = ajo_user
        log_onlending.save()
        onlending_selector = OnlendingSelector(user=user, ajo_user=ajo_user)

        plan_checks = PlanPayments.onlending_plan_checks(onlending_plan=onlending_plan)
        if plan_checks:
            return self.failed_response(
                message=plan_checks.get("message"),
                code=plan_checks.get("code"),
            )

        if settings.ENVIRONMENT != "development":
            if otp:
                verify_otp: bool = verify_sms_voice_otp(
                    otp=otp,
                    phone_number=ajo_user.phone_number,
                ) or verify_ussd_otp(
                    otp=otp,
                    phone_number=ajo_user.phone_number,
                )

                if not verify_otp:
                    return self.failed_response(
                        code="304",
                        message="invalid OTP, please try again",
                    )

        if transaction_pin:
            check_pin = verify_transaction_pin(
                transaction_pin=transaction_pin,
                access_token=request.headers.get("Authorization", "").split()[-1],
                customer_user_id=user.customer_user_id,
            )

            if isinstance(check_pin, Response):
                return check_pin

        onlending_wallet = onlending_selector.get_onlending_wallet()
        funding_wallet = None

        #####FOR SPENDING, AGENT, ONLENDING MAIN WALLETS PAYMENTS
        if wallet_type in valid_wallet_types[:-1]:

            if wallet_type == WalletTypes.AJO_SPENDING:
                funding_wallet = AjoUserSelector(
                    ajo_user=ajo_user
                ).get_spending_wallet()

            elif wallet_type == WalletTypes.ONLENDING_MAIN:
                funding_wallet = OnlendingSelector(
                    user=user, ajo_user=ajo_user
                ).get_main_onlending_wallet()

            elif wallet_type == WalletTypes.AJO_AGENT:
                funding_wallet = AjoAgentSelector(user=user).get_agent_ajo_wallet()

            log_onlending.wallet = funding_wallet
            log_onlending.save()
            
            if not funding_wallet:
                return self.failed_response(
                    message="could not retrieve paying wallet, please try again",
                    code="891",
                )

            if funding_wallet.available_balance < amount:
                return self.failed_response(
                    message="insufficient funds, please fund wallet",
                    code="902",
                )

            try:
                PlanPayments.fund_onlending_plan(
                    from_wallet=funding_wallet,
                    amount=amount,
                    onlending_plan=onlending_plan,
                    onlending_wallet=onlending_wallet,
                )

            except Exception as err:
                return self.failed_response(
                    message="something went wrong",
                    code="980",
                    error_message=str(err),
                )

        else:  # wallet type is AJO_USER
            log_data["desc"] = "Wallet is AJO USER"
            log_onlending.response = log_data
            log_onlending.save()
            try:
                ajo_plan = AjoSavingsSelector(
                    id=ajo_plan_id, user=user
                ).get_ajo_saving_plan()
            except ValueError as err:
                return value_error_response(error=err)

            try:
                ajo_checks = AjoChecks.ajo_plan_checks_before_withdrawal(
                    ajo_plan=ajo_plan,
                    amount=amount,
                )
            except ValueError as err:
                return value_error_response(error=err)

            if isinstance(ajo_checks, Response):
                return Response

            try:
                PlanPayments.fund_onlending_from_ajo_savings(
                    ajo_plan=ajo_plan,
                    amount=amount,
                    onlending_plan=onlending_plan,
                    onlending_wallet=onlending_wallet,
                )
            except Exception as err:
                return self.failed_response(
                    message="something went wrong",
                    code="980",
                    error_message=str(err),
                )

        onlending_plan.refresh_from_db()
        try:
            if onlending_plan.interest_type == InterestType.UPFRONT:
                interest_wallet = OnlendingSelector(
                    user=user,
                    ajo_user=ajo_user,
                ).get_main_onlending_wallet()

                PlanPayments.pay_upfront_interest_into_wallet(
                    quotation_id=onlending_plan.quotation_id,
                    plan_type=PlanType.ONLENDING,
                    wallet=interest_wallet,
                )

            else:
                if onlending_plan.onlending_type == "ONEOFF":
                # add the interest into the onlending plan
                    onlending_wallet.refresh_from_db()

                    PlanPayments.pay_interest_into_plan(
                        plan_instance=onlending_plan,
                        wallet=onlending_wallet,
                    )
                else:
                    pass

        except Exception as err:
            return self.failed_response(
                message="something went wrong",
                code="980",
                error_message=str(err),
            )

        if not onlending_plan.ajo_user:
            referring_user = UserSelector(user).get_referring_user()

            if referring_user:
                try:
                    commission_wallet = OnlendingSelector(
                        user=referring_user
                    ).get_onlending_commission_wallet()

                    onlending_wallet.refresh_from_db()

                    PlanPayments.debit_savings_and_pay_commission(
                        savings=onlending_plan,
                        debit_wallet=onlending_wallet,
                        credit_wallet=commission_wallet,
                    )
                except Exception as err:
                    return self.failed_response(
                        message="something went wrong",
                        code="750",
                        error_message=str(err),
                    )
        
        if onlending_plan.onlending_type == "RECURRENT":
            Utility.create_recurrent_charging_for_plan(
                    user=user,
                    plan_type=PlanType.ONLENDING,
                    plan_instance=onlending_plan,
                    payment_method=PaymentMethod.WALLET,
                    hour=onlending_plan.hour,
                    quotation_id=onlending_plan.quotation_id
                )
            onlending_plan.payment_method = PaymentMethod.WALLET
            onlending_plan.save()

        create_loan_requests_for_onlending_plan.delay(plan_id=onlending_plan.id)
        return Response(
            {
                "status": True,
                "message": "onlending plan funded successfully",
            },
            status.HTTP_200_OK,
        )


class VerifyWithdrawalAccountDetailsAPIView(GenericAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_class = VerifyAccountDetailsSerializer

    def post(self, request, *args, **kwargs):
        """
        This endpoint checks if the account number is valid and
        returns the account name.
        """
        serializer = self.serializer_class(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        # obtain the account number, bank code and ajo user phone number
        account_number = serializer.validated_data.get("account_number")
        bank_code = serializer.validated_data.get("bank_code")
        bank_name = serializer.validated_data.get("bank_name")

        # USE BANK CODE FIRST BEFORE TRYING NIP BANK CODE --> TARGET PAYSTACK
        cbn_bank_code = AgencyBankingClass.get_bank_cbn_code(nip_code=bank_code)
        paystack_direct = PaystackApi.resolve_account_number(
            account_number=account_number, bank_code=cbn_bank_code
        )

        if paystack_direct.get("status"):
            return Response(
                {
                    "status": True,
                    "data": {
                        **paystack_direct.get("data").get("data"),
                        "bank_code": bank_code,
                        "bank_name": bank_name,
                    },
                },
                status=status.HTTP_200_OK,
            )

        paystack_account_name = AgencyBankingClass.fetch_account_name(
            account_number=account_number,
            bank_code=cbn_bank_code,
        )

        if paystack_account_name.get("status"):
            return Response(
                {
                    "status": True,
                    "data": {
                        **paystack_account_name.get("data").get("data"),
                        "bank_code": bank_code,
                        "bank_name": bank_name,
                    },
                },
                status=status.HTTP_200_OK,
            )

        # call the agency banking endpoint to verify this account number
        fetch_account_name = AgencyBankingClass.fetch_account_name(
            account_number=account_number,
            bank_code=bank_code,
        )

        if not fetch_account_name.get("status"):
            response_data = fetch_account_name.get("data")
            data = response_data if response_data else fetch_account_name

            return Response(
                {
                    "status": False,
                    "error": "404",
                    "message": "could not retrieve account name",
                    "data": data,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response(
            {
                "status": True,
                "data": {
                    **fetch_account_name.get("data").get("data"),
                    "bank_code": bank_code,
                    "bank_name": bank_name,
                },
            },
            status=status.HTTP_200_OK,
        )


class WithdrawalAccountAPIView(GenericAPIView):
    permission_classes = (permissions.IsAuthenticated,)
    serializer_classes = {
        "GET": WithdrawalAccountSerializer,
        "POST": VerifiedAccountDetailsSerializer,
    }

    def get_serializer_class(self) -> serializers.Serializer:
        return self.serializer_classes.get(self.request.method)

    def post(self, request, *args, **kwargs):
        """
        This endpoint saves the withdrawal account details to the user
        as long as the transaction pin is valid
        """
        user = request.user
        serializer = self.get_serializer_class()(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as err:
            return serializer_validation_error_response(error=err)

        # obtain the information from the validated data
        validated_data = dict(serializer.data)
        transaction_pin = validated_data.pop("transaction_pin")

        # verify the pin
        check_pin = verify_transaction_pin(
            transaction_pin=transaction_pin,
            access_token=request.headers.get("Authorization", "").split()[-1],
            customer_user_id=user.customer_user_id,
        )

        if isinstance(check_pin, Response):
            return check_pin

        validated_data["user"] = user

        try:
            withdrawal_account = WithdrawalAccountService.set_withdrawal_account(
                validated_data
            )
        except ValueError as err:
            return value_error_response(error=err)

        withdrawal_account_serializer = WithdrawalAccountSerializer(withdrawal_account)

        return Response(
            {
                "status": True,
                "message": "withdrawal account set successfully",
                "data": withdrawal_account_serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    def get(self, request, *args, **kwargs):
        """
        This endpoint returns the withdrawal account details set for the user
        """
        try:
            withdrawal_account = request.user.withdrawal_account
        except ObjectDoesNotExist as err:
            return Response(
                {
                    "status": False,
                    "error": "404",
                    "message": "no withdrawal account has been set for this user",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.get_serializer_class()(withdrawal_account)

        return Response(
            {
                "status": True,
                "data": serializer.data,
            },
            status.HTTP_200_OK,
        )

    def delete(self, request, *args, **kwargs):
        """
        delete the withdrawal account attached to a user
        """
        try:
            request.user.withdrawal_account
        except ObjectDoesNotExist as err:
            return Response(
                {
                    "status": True,
                    "message": "this user has no withdrawal account to delete",
                },
                status=status.HTTP_200_OK,
            )

        request.user.withdrawal_account.delete()

        return Response(
            {
                "status": True,
                "message": "withdrawal account deleted successfully",
            },
            status.HTTP_200_OK,
        )


class VerifyMonnifyTransactionAPIView(GenericAPIView):
    permission_classes = (permissions.IsAuthenticated,)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "reference",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description="insert a unique reference to obtain it's status",
            ),
        ],
    )
    def get(self, request):
        unique_ref = request.query_params.get("reference")
        if unique_ref:
            monnify_response = Monnify().get_single_transfer_status(
                reference=str(unique_ref),
            )

            return Response(monnify_response, status=status.HTTP_200_OK)

        else:
            return Response(
                {
                    "error": "691",
                    "message": "invalid reference",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )


##############################OLD CODE######################################################

# class WithdrawToWalletAPIView(GenericAPIView):
#     serializer_class = WithdrawFromWalletSerializer
#     permission_classes = (permissions.IsAuthenticated,)

#     def withdraw_to_wallet(
#         self,
#         amount: float | None,
#         user: QuerySet,
#         access_token: str,
#         plan_type: PlanType,
#         plan_instance: QuerySet,
#     ) -> dict | Exception:
#         phone_number = obtain_phone_number(access_token=access_token, user_id=user.customer_user_id)
#         verification = verify_account(access_token=access_token, phone_number=phone_number)
#         if verification:
#             # call the withdraw class method
#             withdraw = AgencyBankingClass.initiate_withdrawal_through_pay_buddy(
#                 user=user,
#                 plan_type=plan_type,
#                 phone_number=phone_number,
#                 quotation_id=plan_instance.quotation_id,
#                 transaction_pin=settings.AGENCY_BANKING_TRANSACTION_PIN,
#                 amount=amount,
#             )
#             # check if the withdraw response is successful
#             if (withdraw.get("status") is True) and ("debit_credit_info" in withdraw.keys()):
#                 # get debit credit info
#                 debit_credit = withdraw.get("debit_credit_info")

#                 # get the transaction instance
#                 transaction = withdraw.get("transaction")

#                 # set the transaction wallet_balance_before, plan_balance_before and plan's instance balance_before
#                 transaction.wallet_balance_before = debit_credit.get("balance_before")
#                 transaction.plan_balance_before = plan_instance.amount_saved
#                 plan_instance.plan_balance_before = plan_instance.amount_saved

#                 # decrease the amount saved
#                 plan_instance.amount_saved -= transaction.amount

#                 # set the transaction balance after
#                 transaction.wallet_balance_after = debit_credit.get("balance_after")
#                 transaction.plan_balance_after = transaction.plan_balance_before - transaction.amount
#                 plan_instance.plan_balance_after = plan_instance.plan_balance_before - transaction.amount

#                 # save the changes
#                 plan_instance.save()
#                 transaction.save()

#                 return {
#                     "status": True,
#                     "data": withdraw,
#                 }

#             elif (withdraw.get("status") == False) and ("transfer limit" in withdraw.get("message")):
#                 raise Exception("transfer limit reached, contact customer care")

#             elif "Duplicate" in withdraw.get("message"):
#                 raise Exception("duplicate transaction, come back in about 5 minutes")

#             else:
#                 raise Exception("error occured")

#     def trigger_withdrawal(
#         self, amount: float, access_token: str, user: get_user_model(), plan_type: PlanType, plan_instance: QuerySet
#     ) -> Response:
#         try:
#             withdrawal = self.withdraw_to_wallet(
#                 amount=amount,
#                 access_token=access_token,
#                 user=user,
#                 plan_type=plan_type,
#                 plan_instance=plan_instance,
#             )
#             if withdrawal.get("status") is True:
#                 return Response(
#                     {
#                         "status": True,
#                         "message": "success",
#                         "data": "money has been withdrawn",
#                         "withdraw_data": withdrawal.get("data").get("message"),
#                     }
#                 )
#             else:
#                 return Response(
#                     {
#                         "error": "735",
#                         "status": False,
#                         "message": "unsuccessful",
#                         "data": withdrawal.get("data").get("message"),
#                     },
#                     status=status.HTTP_400_BAD_REQUEST,
#                 )
#         except Exception as err:
#             error = str(err)
#             if "user details" in error:
#                 return Response(
#                     {
#                         "error": "105",
#                         "status": False,
#                         "message": "error occured while attempting to obtain user's details, try again",
#                         "error_message": error,
#                     },
#                     status=status.HTTP_400_BAD_REQUEST,
#                 )

#             elif "KYC" in error:
#                 return Response(
#                     {
#                         "status": False,
#                         "error": "752",
#                         "message": "please complete at least one level of KYC before trying to withdraw again",
#                         "error_message": error,
#                     },
#                     status=status.HTTP_400_BAD_REQUEST,
#                 )

#             elif "duplicate" in error:
#                 return Response(
#                     {
#                         "status": False,
#                         "error": "801",
#                         "message": "duplicate transaction, try again in some time",
#                         "error_message": error,
#                     },
#                     status=status.HTTP_400_BAD_REQUEST,
#                 )

#             elif "transfer limit" in error:
#                 return Response(
#                     {
#                         "status": False,
#                         "error": "555",
#                         "message": "encountered a limit error, please try again after some time",
#                         "error_message": error,
#                     },
#                     status=status.HTTP_400_BAD_REQUEST,
#                 )

#             else:
#                 return Response(
#                     {
#                         "status": False,
#                         "error": "800",
#                         "message": "error occured while withdrawing, contact customer care",
#                         "error_message": error,
#                     },
#                     status=status.HTTP_400_BAD_REQUEST,
#                 )

#     def validate_and_trigger_withdrawal(
#         self,
#         amount_to_withdraw: float,
#         plan_instance: QuerySet,
#         access_token: str,
#         plan_type: PlanType,
#         user: get_user_model(),
#     ) -> Response:
#         if (plan_instance.amount_saved >= amount_to_withdraw) and (plan_instance.amount_saved > 0):
#             return self.trigger_withdrawal(
#                 amount=amount_to_withdraw,
#                 access_token=access_token,
#                 user=user,
#                 plan_type=plan_type,
#                 plan_instance=plan_instance,
#             )
#         else:
#             return Response(
#                 {
#                     "status": False,
#                     "error": "695",
#                     "message": "insufficient amount to withdraw",
#                 },
#                 status=status.HTTP_403_FORBIDDEN,
#             )

#     def post(self, request):
#         auth_header = request.headers.get("Authorization", "")
#         token_type, _, access_token = auth_header.partition(" ")
#         user = request.user

#         # Regulator
#         if ConstantTable.get_constant_table_instance().pay_from_wallet_regulator == False:
#             response = {"error": "324", "message": "service currently unavailable, please try again later"}
#             return Response(
#                 response,
#                 status=status.HTTP_403_FORBIDDEN,
#             )

#         serializer = self.serializer_class(data=request.data)
#         serializer.is_valid(raise_exception=True)

#         # get the plan here and different values necessary for withdrawal
#         plan_type = serializer.validated_data["plan_type"].upper()
#         plan_id = serializer.validated_data["plan_id"]
#         amount_to_withdraw = serializer.validated_data.get("amount")
#         plan_instance = Utility.get_plan_instance(
#             user=user,
#             plan_id=plan_id,
#             plan_type=plan_type,
#         )

#         # step 1: check if the pin is correct
#         transaction_pin = serializer.validated_data.get("transaction_pin")

#         # verify the transaction pin
#         check_pin = verify_transaction_pin(transaction_pin=transaction_pin, access_token=access_token)

#         # check if it is a Response object
#         if isinstance(check_pin, Response):
#             return check_pin

#         # step 2: differentiate the plans
#         # Determine the plan type and if withdrawal is allowed there
#         # For Chestlock, The maturity date must have reached
#         if plan_type == PlanType.CHESTLOCK:
#             # it doesn't have to be completed, just the maturity date
#             # if plan_instance.completed == False or plan_instance.is_active == True:
#             #     return Response(
#             #         {
#             #             "status": False,
#             #             "error": "690",s
#             #             "message": "chestlock plan is incomplete and cannot be withdrawn",
#             #         },
#             #         status=status.HTTP_403_FORBIDDEN,
#             #     )

#             now = timezone.localtime(timezone.now())
#             if plan_instance.maturity_date <= datetime.date(now):
#                 amount_to_withdraw = plan_instance.amount_saved
#                 response = self.validate_and_trigger_withdrawal(
#                     amount_to_withdraw=amount_to_withdraw,
#                     plan_instance=plan_instance,
#                     access_token=access_token,
#                     plan_type=plan_type,
#                     user=user,
#                 )
#                 if response.data.get("status") is True:
#                     plan_instance.is_active = False
#                     plan_instance.withdrawn = True
#                     plan_instance.save()
#                 return response

#             else:
#                 return Response(
#                     {
#                         "error": "692",
#                         "status": False,
#                         "message": "maturity date has not been reached",
#                     },
#                     status=status.HTTP_403_FORBIDDEN,
#                 )

#         elif plan_type == PlanType.HALAL:
#             if plan_instance.lock == True:
#                 now = timezone.localtime(timezone.now())
#                 if plan_instance.maturity_date <= datetime.date(now):
#                     response = self.validate_and_trigger_withdrawal(
#                         amount_to_withdraw=amount_to_withdraw,
#                         plan_instance=plan_instance,
#                         access_token=access_token,
#                         plan_type=plan_type,
#                         user=user,
#                     )
#                     if response.data.get("status") is True:
#                         if plan_instance.is_active:
#                             plan_instance.is_active = False
#                             plan_instance.save()
#                     return response
#                 else:
#                     return Response(
#                         {
#                             "error": "692",
#                             "status": False,
#                             "message": "maturity date has not been reached",
#                         },
#                         status=status.HTTP_403_FORBIDDEN,
#                     )

#             else:
#                 return self.validate_and_trigger_withdrawal(
#                     amount_to_withdraw=amount_to_withdraw,
#                     plan_instance=plan_instance,
#                     access_token=access_token,
#                     plan_type=plan_type,
#                     user=user,
#                 )

#         elif plan_type == PlanType.QUICKSAVINGS:
#             return self.validate_and_trigger_withdrawal(
#                 amount_to_withdraw=amount_to_withdraw,
#                 plan_instance=plan_instance,
#                 access_token=access_token,
#                 plan_type=plan_type,
#                 user=user,
#             )

#         else:
#             return Response(
#                 {
#                     "error": "700",
#                     "status": False,
#                     "message": "plan type does not exist",
#                 },
#                 status=status.HTTP_400_BAD_REQUEST,
#             )

# class SaveToAjoSavingsPlanAPIView(GenericAPIView):
#     serializer_class = SaveToAjoSavingsPlanSerializer
#     permission_classes = (permissions.IsAuthenticated,)

#     def post(self, request, *args, **kwargs):
#         # obtain the access token of the user
#         token_type, _, access_token = request.headers.get("Authorization", "").partition(" ")

#         # obtain the user
#         user = request.user

#         # Check if the regulator is active
#         if ConstantTable.get_constant_table_instance().pay_from_wallet_regulator is False:
#             return Response(
#                 {
#                     "error": "324",
#                     "status": False,
#                     "message": "service currently unavailable, please try again later",
#                 },
#                 status=status.HTTP_403_FORBIDDEN,
#             )
#         # validate the data
#         serializer = self.serializer_class(data=request.data)
#         try:
#             serializer.is_valid(raise_exception=True)
#         except serializers.ValidationError as err:
#             return serializer_validation_error_response(err)

#         # obtain the User type
#         try:
#             user_type = UserModelMethods.get_user_type(user, access_token)
#         except Exception as err:
#             return Response(
#                 {
#                     "error": "603",
#                     "status": False,
#                     "message": str(err),
#                 },
#                 status=status.HTTP_400_BAD_REQUEST,
#             )

#         # obtain the serializer validated data
#         amount = serializer.validated_data.get("amount")
#         plan_id = serializer.validated_data.get("plan_id")
#         ajo_user_transaction_pin = serializer.validated_data.get("ajo_user_transaction_pin")
#         transaction_pin = serializer.validated_data.get("transaction_pin")

#         # obtain the plan instance
#         plan_instance: AjoSaving = Utility.get_plan_instance(
#             user=user,
#             plan_id=plan_id,
#             plan_type=PlanType.AJO,
#         )

#         # PERFORM CHECKS ON THE AJO PLAN
#         # TODO: Please find what checks to do here
#         if plan_instance.completed is True:
#             return Response(
#                 {
#                     "error": "671",
#                     "status": False,
#                     "message": "plan is marked as complete, please contact customer care",
#                 },
#                 status=status.HTTP_403_FORBIDDEN,
#             )

#         if plan_instance.is_active is False:
#             return Response(
#                 {
#                     "error": "671",
#                     "status": False,
#                     "message": "plan is marked as inactive, contact customer care",
#                 },
#                 status=status.HTTP_403_FORBIDDEN,
#             )

#         # Perform certain operations based on the User Type
#         if user_type is UserType.PERSONAL:
#             # validate the transaction pin
#             check_pin = verify_transaction_pin(
#                 transaction_pin=transaction_pin,
#                 access_token=access_token,
#                 customer_user_id=user.customer_user_id,
#             )

#             # check if it a Response object
#             if isinstance(check_pin, Response):
#                 return check_pin

#         if user_type is UserType.AGENT:
#             # TODO: validate both the user pin and the ajo user pin????
#             pass

#         # TODO: calculate how much till it's completed and find out if it's the last one the person is paying
#         # check if the plan has been activated before
#         if plan_instance.is_activated is True:
#             # check if the remaining amount is larger than the periodic amount
#             pass

#         # call the charging user wallet class method
#         charge = AgencyBankingClass.charge_user_wallet(
#             user=user,
#             plan_type=PlanType.AJO,
#             quotation_id=plan_instance.quotation_id,
#             transaction_pin=transaction_pin,
#             amount=amount,
#             access_token=access_token,
#             ajo_user=plan_instance.ajo_user,
#         )

#         # check if the charge response is successful
#         if (charge.get("status") is True) and ("debit_credit_info" in charge.keys()):
#             # check if the plan is not activated and activate it
#             if plan_instance.is_activated is False:
#                 # activate the plan
#                 plan_instance.is_activated = True

#             # obtain the debit_credit_info
#             debit_credit = charge.get("debit_credit_info")

#             # get the transaction instance
#             transaction = charge.get("transaction")

#             # set the transaction wallet_balance_before, plan_balance_before and plan_instance's balance_before
#             transaction.wallet_balance_before = debit_credit.get("balance_before")
#             transaction.plan_balance_before = plan_instance.amount_saved
#             plan_instance.plan_balance_before = plan_instance.amount_saved

#             # increase the amount saved
#             plan_instance.amount_saved += transaction.amount

#             # set the transaction balance after
#             transaction.wallet_balance_after = debit_credit.get("balance_after")
#             transaction.plan_balance_after = transaction.plan_balance_before + transaction.amount
#             plan_instance.plan_balance_after = plan_instance.plan_balance_before + transaction.amount

#             # Calculate the days saved
#             plan_instance.frequency_paid = plan_instance.calculate_frequency_paid()

#             # # Check if the plan_instance has an ajo_user linked to it
#             # if plan_instance.ajo_user:
#             #     transaction.onboarded_user = plan_instance.ajo_user

#             # save changes
#             with django_transaction.atomic():
#                 plan_instance.save()
#                 transaction.save()
#                 if plan_instance.ajo_user:
#                     # increase the ajo_user total money saved
#                     increment_total_saved_in_ajo_user(ajo_user=plan_instance.ajo_user, amount=transaction.amount)

#             #############SEND AN EMAIL########################

#             # CHECK IF THIS WAS THE LAST PAYMENT TO COMPLETE THE PLAN!

#             return Response(
#                 {
#                     "status": True,
#                     "message": "successful deposit to ajo savings plan",
#                     "data": charge.get("message"),
#                 },
#                 status=status.HTTP_200_OK,
#             )

#         # Check if the charge was not successful
#         # check for insufficient balance failed transaction
#         if (charge.get("status") is False) and ("insufficient" in charge.get("message")):
#             return Response(
#                 {
#                     "error": "735",
#                     "status": False,
#                     "message": charge.get("message"),
#                 },
#                 status=status.HTTP_400_BAD_REQUEST,
#             )
#         # error in amount or user_id
#         else:
#             return Response(
#                 {
#                     "error": "735",
#                     "status": False,
#                     "message": charge.get("message"),
#                 },
#                 status=status.HTTP_400_BAD_REQUEST,
#             )


# class SaveToRotationSavingsGroupAPIView(GenericAPIView):
#     serializer_class = SaveToRotationSavingsGroupSerializer
#     permission_classes = (
#         permissions.IsAuthenticated,
#         PersonalUserPermission,
#     )

#     def post(self, request, *args, **kwargs):
#         # pass the data to the serializer
#         serializer = self.serializer_class(data=request.data)

#         # attempt to validate the data
#         try:
#             serializer.is_valid(raise_exception=True)
#         except serializers.ValidationError as err:
#             return serializer_validation_error_response(err)

#         # obtain the access token from the Authorization header
#         access_token = request.headers.get("Authorization").partition(" ")[-1]

#         # obtain the user
#         user = request.user

#         # obtain the RotationGroup
#         group_id = serializer.validated_data.get("group_id")
#         try:
#             rotation_group_instance = RotationGroup.objects.get(group_id=group_id)
#         except RotationGroup.DoesNotExist:
#             return Response(
#                 {
#                     "error": "604",
#                     "status": False,
#                     "message": "a rotation group with this id does not exist",
#                 },
#                 status=status.HTTP_400_BAD_REQUEST,
#             )

#         # process the payment
#         # check if the starting date has begun
#         if not rotation_group_instance.has_started:
#             return Response(
#                 {
#                     "error": "705",
#                     "status": False,
#                     "message": "The starting date for this rotation group has not begun",
#                 },
#                 status=status.HTTP_403_FORBIDDEN,
#             )

#         # check if the user is a valid member of the Rotation Group
#         if not rotation_group_instance.is_user_a_member(user=user):
#             return Response(
#                 {
#                     "error": "707",
#                     "status": False,
#                     "message": "this user is not a member of the RotationGroup",
#                 },
#                 status=status.HTTP_400_BAD_REQUEST,
#             )

#         # obtain the payment method, transaction pin and amount
#         payment_method = serializer.validated_data.get("payment_method")
#         transaction_pin = serializer.validated_data.get("transaction_pin")
#         amount = serializer.validated_data.get("amount")

#         if payment_method == PaymentMethod.WALLET:
#             # verify the transaction pin
#             check_pin = verify_transaction_pin(
#                 transaction_pin=transaction_pin,
#                 access_token=access_token,
#                 customer_user_id=user.customer_user_id,
#             )

#             # check if it is a Response object
#             if isinstance(check_pin, Response):
#                 return check_pin

#             # call the Agency banking charging user wallet classmethod
#             charge = AgencyBankingClass.charge_user_wallet(
#                 user=user,
#                 plan_type=PlanType.ROTATIONGROUP,
#                 amount=amount,
#                 transaction_pin=transaction_pin,
#                 rotation_group_instance=rotation_group_instance,
#             )

#             # Check if the charge was successful
#             if (charge.get("status") is True) and ("debit_credit_info" in charge.keys()):
#                 # check if the group has been activated or not
#                 if not rotation_group_instance.is_activated:
#                     # Active the plan
#                     rotation_group_instance.is_activated = True

#                 # If auto-debit
#                 if rotation_group_instance.auto_debit:
#                     # TODO: CREATE AUTO DEBIT INSTANCES HERE
#                     pass

#                 # update the tables
#                 debit_credit = charge.get("debit_credit_info")

#                 # Get the transaction instance
#                 transaction = charge.get("transaction")

#                 # Obtain the RotationGroupMember instance in order to update its fields
#                 rotation_group_member: RotationGroupMember = (
#                     RotationGroupMember.retrieve_rotation_group_member_instance(
#                         user=user, rotation_group=rotation_group_instance
#                     )
#                 )

#                 if not rotation_group_member:
#                     return Response(
#                         {
#                             "error": "702",
#                             "status": False,
#                             "message": "could not find the rotation group member in the system",
#                         },
#                         status=status.HTTP_404_NOT_FOUND,
#                     )

#                 # Obtain the fields
#                 update_transaction_fields_after_credit_in_rotation_group(
#                     transaction=transaction,
#                     debit_credit=debit_credit,
#                     rotation_group=rotation_group_instance,
#                 )
#                 update_rotation_group_fields_after_deposit(
#                     rotation_group=rotation_group_instance,
#                     transaction=transaction,
#                 )
#                 update_rotation_group_member_fields_after_contribution(
#                     rotation_group_member=rotation_group_member, transaction=transaction
#                 )

#                 return Response(
#                     {
#                         "status": True,
#                         "message": "contribution has been made into rotation group",
#                         "data": charge.get("message"),
#                     }
#                 )

#             # Check if the charge response is not successful
#             # check for insufficient balance
#             elif (charge.get("status") is False) and ("insufficient" in charge.get("message")):
#                 return Response(
#                     {
#                         "error": "735",
#                         "status": False,
#                         "message": charge.get("message"),
#                     },
#                     status=status.HTTP_403_FORBIDDEN,
#                 )

#             else:
#                 # any other error that may come up
#                 return Response(
#                     {
#                         "error": "735",
#                         "status": False,
#                         "message": charge.get("message"),
#                     },
#                     status=status.HTTP_400_BAD_REQUEST,
#                 )

#         elif payment_method == PaymentMethod.DEBIT_CARD:
#             return Response(
#                 {
#                     "error": "803",
#                     "status": False,
#                     "message": "this payment method is not supported yet",
#                 },
#                 status=status.HTTP_403_FORBIDDEN,
#             )


class AgencyBankCards(APIView):

    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):

        auth_header = request.headers.get("Authorization", "")
        token_type, _, access_token = auth_header.partition(" ")
        user = request.user

        data = AgencyBankingClass.get_user_cards(access_token=access_token)
        return Response(
                {
                    "status": True,
                    "data": data
                },
                status=status.HTTP_200_OK,
            )

class ManualWalletDepletionView(APIView):
    """"
    Manually deplete wallet.
    """
    serializer_class  = ManualWalletDepletionSerializer

    def post(self, request):
        if request.user.email not in ["<EMAIL>"]:
            return Response(
                {
                    "data": "You are not allowed to use this resource."
                },
                status=status.HTTP_401_UNAUTHORIZED
            )
        serializer = self.serializer_class(data=request.data)
        loan_id = int(request.query_params.get("loan_id"))

        if loan_id:
            existing_loan = AjoLoan.objects.get(id=loan_id)
            loan_ref = existing_loan.loan_ref
        else:
            loan_ref = uuid.uuid4()


        if serializer.is_valid(raise_exception=True):
            amount = serializer.validated_data.get("amount")
            reason = serializer.validated_data.get("reason")
            wallet_id = serializer.validated_data.get("wallet_id")

            try:
                ajo_user_wallet = WalletSystem.objects.get(id=wallet_id)
                # Deplete User wallet
                debit_transaction = debit_transaction = TransactionService.create_deposit_by_wallet_transaction(
                    user=ajo_user_wallet.user,
                    amount=amount,
                    wallet_type=ajo_user_wallet.wallet_type,
                    quotation_id=loan_ref,
                    ajo_user=ajo_user_wallet.onboarded_user,
                    transaction_form_type=TransactionFormType.MANUAL_CLEANUP_WALLET_DEDUCTION,
                    description=reason,
                    status=Status.SUCCESS,
                    transfer_provider=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
                )

                WalletSystem.deduct_balance(
                    wallet=ajo_user_wallet,
                    amount=amount,
                    transaction_instance=debit_transaction,
                    onboarded_user=ajo_user_wallet.onboarded_user,
                )

                return Response(
                    {"message": f"Wallet - {ajo_user_wallet.id} depleted by {amount}"},
                    status=status.HTTP_200_OK
                )

            except Exception as e:
                return Response(
                    {
                        "message": "error",
                        "data": str(e),
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )
            

class PayWiseWaitListView(APIView):
    def post(self, request):
        serializer = PayWiseWaitListSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data

        full_name = serializer_data.get("full_name")
        email = serializer_data.get("email")

        try:
            wait_list = serializer.save()

            general_send_email(
                recipient=email,
                name=full_name,
                subject="PayWise WaitList Profile Created Successfully!",
                template_dir="paywise_wait_list_confirmation_email.html",
            )

            serialized_wait_list = PayWiseWaitListSerializer(wait_list)

            return Response(
                {
                    "message": "Waitlist profile created successfully!",
                    "wait_list_account": serialized_wait_list.data,
                }, status=status.HTTP_201_CREATED
            )
        except Exception as e:
            return Response(
                {
                    "message": "An error occurred while trying to create PayWiseWaitList instance!",
                    "error": str(e),
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
