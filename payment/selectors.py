from django.conf import settings
from django.contrib.auth.models import AbstractUser

from .model_choices import WalletTypes
from .models import Transaction, WalletSystem


class WalletSelector:
    @classmethod
    def get_or_create_savings_wallet(
        cls,
        user: AbstractUser,
        wallet_type: WalletTypes,
    ) -> WalletSystem:
        wallet, created = WalletSystem.objects.get_or_create(
            user=user,
            wallet_type=wallet_type,
        )

        return wallet


class TransactionSelector:
    def __init__(self, id: int) -> None:
        try:
            self.transaction = Transaction.objects.get(id=id)
        except Transaction.DoesNotExist:
            self.transaction = None

    def get_transaction_instance(self) -> Transaction:
        """
        Obtains the transaction from when the class was instantiated

        Raises:
            ValueError: "transaction not found"

        Returns:
            Transaction: an instance of the transaction
        """
        if self.transaction:
            return self.transaction
        else:
            raise ValueError("transaction not found")
