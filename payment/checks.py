from typing import Any, Dict, List, Union

from django.core.cache import cache
from django.utils import timezone
from rest_framework import status
from rest_framework.response import Response

from accounts.agency_banking import AgencyBankingClass
from chestlock.model_choices import ChestlockType

from .model_choices import PlanType
from .utils import ChestL<PERSON>, <PERSON><PERSON>, QuickSavings


def verify_transaction_pin(transaction_pin: str, access_token: str, customer_user_id: int) -> bool | Response:
    """
    Check if the pin is correct and returns a bool or a Response object

    Args:
        transaction_pin (str): transaction pin to be verified
        access_token(str): access token of the user to be used for the verification
        customer_user_id(int): the customer user id of the user.

    Returns:
        bool: True
        Response: Failed Response object
    """
    # perform remote API call
    verify_pin_check = AgencyBankingClass.verify_user_pin(transaction_pin=transaction_pin, access_token=access_token)
    # check the status
    if verify_pin_check.get("status") is False:
        return Response(
            data={
                "error": "791",
                "status": False,
                "message": verify_pin_check.get("message"),
            },
            status=status.HTTP_400_BAD_REQUEST,
        )
    return True


def obtain_phone_number(access_token: str, user_id: int) -> str | Exception:
    user_details = AgencyBankingClass.get_user_info(access_token=access_token, user_id=user_id)
    # print(user_details)
    if user_details.get("status") is True:
        phone_number = user_details.get("data").get("phone_number")

    else:
        raise Exception("error obtaining user details")

    return phone_number


def verify_account(access_token: str, phone_number: str) -> bool | Exception:
    verification = AgencyBankingClass.pay_buddy_fetch_account(access_token=access_token, phone_number=phone_number)
    
    print("BUDDY DETAILS VERIFICATION:---->>>>>>>>>>", verification)
    print("BUDDY DETAILS VERIFICATION:---->>>>>>>>>>", verification)

    return verification.get("status"), verification


def check_if_chestlock_and_if_oneoff(plan_instance: ChestLock, plan_type: PlanType = None) -> bool:
    if plan_type:
        return (plan_type is PlanType.CHESTLOCK) and (plan_instance.chestlock_type == ChestlockType.ONEOFF)
    else:
        return (isinstance(plan_instance, ChestLock)) and (plan_instance.chestlock_type == ChestlockType.ONEOFF)


def check_the_withdrawal_conditions_of_plans(plan_instance: Union[ChestLock, Halal, QuickSavings]) -> bool:
    # obtain the current time
    now = timezone.localtime()

    # Check the withdrawal condition based on plan type
    if isinstance(plan_instance, ChestLock):
        return plan_instance.maturity_date <= now.date()

    elif isinstance(plan_instance, Halal) and plan_instance.lock:
        return plan_instance.maturity_date <= now.date()

    else:
        return True


class VerifyTransactionsOnAgency:
    v2_status_codes = {
        "00": "Successful Transfer",
        "29": "Pending Reversal",
        "25": "Successful Reversal",
        "09": "Pending Transfer",
        "19": "Pending Transfer",
    }

    @classmethod
    def verify_bank_transfers(
        cls,
        escrow_id: str,
    ) -> Dict[str, Any]:
        """
        This verifies if a bank transfer went through or was reversed.

        Keys to look out for in the response for decision making:
            status: if False (Do not process anything)
            status & transferred: True for both fields mean that the transfer was successful
            status & reversed: True for both fields means that the transfer was reversed

        Look out for message key for more information:
            - "cannot verify transaction, contact customer care"
            - "an error occurred, try again"
            - "transaction is not a bank transfer"
            - "transfer completed successfully"
            - "transfer was reversed"

        Args:
            escrow_id (str): the escrow ID or unique ID of the transaction

        Returns:
            Dict[str, Any]: the response dictionary
        """
        response = {
            "status": False,
            "reversed": False,
            "transferred": False,
            "message": "",
        }

        verification = AgencyBankingClass.verify_transaction_from_agency(escrow_id=escrow_id)

        if not verification.get("status"):
            response["message"] = "an error occurred, try again"
            return response

        verification_data = verification.get("data")
        if not verification_data.get("transaction_type") == "SEND_BANK_TRANSFER":
            response["message"] = "transaction is not a bank transfer"
            return response

        verification_status = verification_data.get("status").upper()

        if verification_status == "SUCCESSFUL":
            transaction_leg = verification_data.get("transaction_leg")
            is_reversed = verification_data.get("is_reversed")

            response["status"] = True

            if transaction_leg == "EXTERNAL":
                if not is_reversed:
                    response["transferred"] = True
                    response["message"] = "transfer completed successfully"
                else:
                    response["reversed"] = True
                    response["message"] = "transfer was reversed"

            elif transaction_leg == "REVERSAL":
                if is_reversed:
                    response["reversed"] = True
                    response["message"] = "transfer was reversed"

            return response

        elif verification_status == "PENDING":
            return response

        elif verification_status == "FAILED":
            return response

        elif verification_status == "ERROR":
            return response

        else:
            response["message"] = "cannot verify transaction, contact customer care"
            return response

    @classmethod
    def verify_bank_transfers_v2(
        cls,
        reference: str,
    ) -> Dict[str, Any]:
        """
        This verifies if a bank transfer was successful or was reversed

        Keys to look out for in the response for decision making:
            status: if False (Do not process anything)
            status & transferred: True for both fields mean that the transfer was successful
            status & reversed: True for both fields means that the transfer was reversed

        Look out for message key for more information:
            - "cannot verify transaction, contact customer care"
            - "an error occurred, try again"
            - "transaction is not a bank transfer"
            - "transfer completed successfully"
            - "transfer was reversed"

        Args:
            reference (str): the escrow_id, unique_id or reference of the transaction

        Returns:
            Dict[str, Any]: the response dictionary
        """
        response = {
            "status": False,
            "reversed": False,
            "transferred": False,
            "message": "",
            "data": None,
        }

        verification = AgencyBankingClass.verify_transaction_from_agency_v2(reference=reference)

        if not verification.get("status"):
            response["message"] = "an error occurred, try again"
            response["data"] = verification
            return response

        verification_data = verification.get("data")
        data = verification_data.get("data")
        print("$$$$$$$$", verification_data)

        if (
            not data
            and verification_data.get("status") == "error"
            and verification_data.get("message") == "transaction with ID does not exist"
        ):
            response["status"] = False
            response["reversed"] = False
            response["message"] = "transfer was unsuccessful"
            return response

        if not data:
            return response

        if not data.get("transfer_type") == "SEND_BANK_TRANSFER":
            response["message"] = "transaction is not a bank transfer"
            return response

        verification_status = data.get("status")
        verification_status_code = data.get("status_code")
        is_reversed = data.get("reversed")
        response["data"] = data

        if verification_status == "SUCCESSFUL":
            response["status"] = True

            if verification_status_code == "00":
                # if not is_reversed:
                response["transferred"] = True
                response["message"] = "transfer completed successfully"

            #     else:
            #         response["reversed"] = True
            #         response["message"] = "transfer was reversed"

            # elif verification_status_code == "25":
            #     if is_reversed:
            #         response["reversed"] = True
            #         response["message"] = "transfer was reversed"

        elif verification_status == "REVERSAL" and verification_status_code == "25" and is_reversed:
            response["status"] = True
            response["reversed"] = True
            response["message"] = "transfer was reversed"

        return response


def is_masked_pan_for_user(access_token: str, masked_pan: str) -> Dict[str, Any]:
    """
    Returns a status and authorization_code in a dictionary if the
    masked pan belongs to user

    Args:
        access_token (str): the access token of the user.
        masked_pan (str): the masked pan

    Returns:
        Dict[str, Any]: returned data
    """
    response = {
        "status": False,
        "authorization_code": None,
    }

    cards = AgencyBankingClass.get_user_cards(access_token=access_token)

    if cards.get("status"):
        if cards.get("message") == "Cards Retrieved":
            cards_data: List[Dict[str, Any]] = cards.get("data")
            for card_data in cards_data:
                if masked_pan in card_data.values():
                    response["status"] = True
                    response["authorization_code"] = card_data.get("authorization_code")
                    break

    return response
