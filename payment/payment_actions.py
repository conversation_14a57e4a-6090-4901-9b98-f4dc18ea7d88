import json
from typing import Any, Dict, Optional, Union

from celery import shared_task
from django.conf import settings
from django.contrib.auth import get_user_model
from django.db import transaction as django_transaction
from django.utils import timezone

from accounts.agency_banking import AgencyBankingClass
from accounts.services import InterestsPaidTableMethods
from ajo import payment_actions as APA
from ajo.models import AjoSaving, AjoUser
from chestlock.models import Onlending, OnlendingCommissionHistory
from chestlock.selectors import OnlendingSelector
from chestlock.services import OnlendingService
from payment.services import CommissionService, CommissionType, DisbursementProviderType

from .model_choices import (
    PlanType,
    Status,
    TransactionDestination,
    TransactionFormType,
    WalletTypes,
)
from .models import Transaction, WalletSystem
from .selectors import TransactionSelector, WalletSelector
from .services import TransactionService
from .utils import (
    ChestLock,
    Halal,
    QuickSavings,
    Utility,
    convert_string_to_aware_datetime_object,
)


def debit_wallet(
    wallet: WalletSystem,
    amount: float,
    transaction_instance: Transaction,
    unique_reference: str | None = None,
    onboarded_user: AjoUser | None = None,
) -> Dict[str, Any]:
    try:
        deduction = WalletSystem.deduct_balance(
            wallet=wallet,
            amount=amount,
            transaction_instance=transaction_instance,
            unique_reference=unique_reference,
            onboarded_user=onboarded_user,
        )

    except Exception as err:
        error_message = str(err)
        if "amount is less than zero" in error_message:
            raise ValueError("no amount to debit")
        raise ValueError(error_message)

    return deduction


@django_transaction.atomic
def debit_user_plan_and_wallet(
    plan_instance: ChestLock | Halal | QuickSavings,
    amount,
    wallet: WalletSystem,
    transaction_instance: Transaction,
) -> None:
    # firstly deduct the wallet and create the debit_credit_info instance
    # deduct = WalletSystem.deduct_balance(
    #     wallet=wallet,
    #     amount=amount,
    #     transaction_instance=transaction_instance,
    # )
    print(plan_instance, amount, wallet, transaction_instance)

    deduct = debit_wallet(
        wallet=wallet,
        amount=amount,
        transaction_instance=transaction_instance,
    )

    # set the transaction wallet, plan balance before and plan_instance balance before
    transaction_instance.wallet_balance_before = deduct.get("balance_before")
    transaction_instance.plan_balance_before = plan_instance.amount_saved
    plan_instance.plan_balance_before = plan_instance.amount_saved

    # decrease the amount saved
    plan_instance.amount_saved -= amount

    # set the balance after as done above
    transaction_instance.wallet_balance_after = deduct.get("balance_after")
    transaction_instance.plan_balance_after = (
        transaction_instance.plan_balance_before - amount
    )
    plan_instance.plan_balance_after = plan_instance.plan_balance_before - amount

    # save the changes
    plan_instance.save()
    transaction_instance.save()


class DebitCreditActions:

    @staticmethod
    @django_transaction.atomic
    def fund_wallet_and_update_transaction(
        wallet: WalletSystem,
        amount: float,
        transaction_instance: Transaction,
        unique_reference: str | None = None,
        ajo_user: AjoUser | None = None,
    ) -> dict:
        """
        Increments the transaction and wallet

        Args:
            wallet (WalletSystem): the ajo agent's wallet
            amount (float): the amount of money
            transaction_instance (Transaction): the transaction instance
            unique_reference (str): the unique reference. Defaults to None.
            ajo_user (AjoUser): the ajo user the transaction belongs to. Defaults to None.

        Returns:
            Dict
        """
        # firstly increment the ajo user's wallet and create the debit credit info
        fund = WalletSystem.fund_balance(
            wallet=wallet,
            amount=amount,
            transaction_instance=transaction_instance,
            unique_reference=unique_reference,
            onboarded_user=ajo_user,
        )

        # update the transaction fields
        transaction_instance.status = Status.SUCCESS
        transaction_instance.transaction_date_completed = timezone.localtime()
        transaction_instance.wallet_balance_before = fund.get("balance_before")
        transaction_instance.wallet_balance_after = fund.get("balance_after")
        transaction_instance.save()

        return fund

    @staticmethod
    @django_transaction.atomic
    def debit_wallet_and_update_transaction(
        amount: float,
        wallet: WalletSystem,
        transaction_instance: Transaction,
    ) -> Dict[str, Any]:
        # firstly deduct the ajo user wallet and create the debit_credit_info instance
        ajo_user = transaction_instance.onboarded_user

        deduct = debit_wallet(
            wallet=wallet,
            amount=amount,
            transaction_instance=transaction_instance,
            onboarded_user=ajo_user,
        )
        # deduct = WalletSystem.deduct_balance(
        #     wallet=wallet,
        #     amount=amount,
        #     transaction_instance=transaction_instance,
        #     onboarded_user=ajo_user,
        # )

        # update the fields
        transaction_instance.status = Status.SUCCESS
        transaction_instance.transaction_date_completed = timezone.localtime()
        transaction_instance.wallet_balance_before = deduct.get("balance_before")
        transaction_instance.wallet_balance_after = deduct.get("balance_after")
        transaction_instance.save()

        return deduct


def pay_withdrawal_and_handle_response(
    transaction_id: int,
    amount: float,
    phone_number: str,
) -> Dict[str, Any]:
    # obtain the transaction instance
    try:
        transaction = TransactionSelector(id=transaction_id).get_transaction_instance()
    except ValueError as err:
        return {
            "status": False,
            "message": "could not obtain transaction instance",
        }

    transfer_funds = (
        AgencyBankingClass.send_money_from_liberty_to_user_through_pay_buddy(
            phone_number=phone_number,
            amount=amount,
            transaction_reference=str(transaction.transaction_id),
        )
    )

    # create a response dictionary variable
    response = {}

    # get the data object in the transfer funds
    data: Optional[Dict[str, Any]] = transfer_funds.get("data", {})

    if not data:
        transaction.status = Status.FAILED
        transaction.failure_reason = "an internal provider issue"
        response["status"] = False

    else:
        # check if the status is True
        if transfer_funds.get("status"):
            # Ensure that the money was successfully sent
            if "message" in data.keys() and data.get("message") == "success":
                transaction.status = Status.SUCCESS
                transaction.transaction_date_completed = (
                    convert_string_to_aware_datetime_object(data.get("date_completed"))
                )
                transaction.unique_reference = data.get("data").get("escrow_id")
                response["status"] = True

        # check if the status is False
        else:
            if data:
                failure_reason = data.get("message")
            else:
                failure_reason = (
                    "something went wrong while transferring, check payload."
                )

            # this indicates that the payment did not go through and hence the debit should be reversed
            transaction.status = Status.FAILED
            transaction.failure_reason = failure_reason
            response["status"] = False

    # log the data received
    transaction.payload = json.dumps(transfer_funds)

    # save the changes
    transaction.save()

    response.update(
        {
            "transaction": transaction,
            "data": data,
            "status_code": transfer_funds.get("status_code"),
        }
    )
    return response


@shared_task
@django_transaction.atomic
def reverse_debit(
    plan_id: int,
    user_id: int,
    amount: float,
    plan_type: PlanType,
    request_data: dict,
    transaction_reference: str | None = None,
):
    # obtain user id
    user = get_user_model().objects.get(id=user_id)

    # obtain the plan instance
    plan_instance = Utility.get_plan_instance(
        user=user,
        plan_id=plan_id,
        plan_type=plan_type,
    )

    # create reversal transaction
    transaction = TransactionService.create_reversal_of_funds_transaction(
        user=user,
        amount=amount,
        wallet_type=getattr(WalletTypes, plan_type.upper()),
        request_data=request_data,
        quotation_id=plan_instance.quotation_id,
        plan_type=plan_type,
        unique_reference=f"{transaction_reference}_reversal",
        transfer_provider=DisbursementProviderType.AGENCY_BANKING_PROVIDER,
    )

    # retrieve wallet instance
    wallet = WalletSelector.get_or_create_savings_wallet(
        user=user,
        wallet_type=getattr(WalletTypes, plan_type.upper()),
    )

    # fund the wallet
    fund = WalletSystem.fund_balance(
        wallet=wallet,
        amount=amount,
        transaction_instance=transaction,
    )

    # set the transaction wallet, plan balance before and plan_instance balance before
    transaction.wallet_balance_before = fund.get("balance_before")
    transaction.plan_balance_before = plan_instance.amount_saved
    plan_instance.plan_balance_before = plan_instance.amount_saved

    # decrease the amount saved
    plan_instance.amount_saved += amount

    # set the balance after as done above
    transaction.wallet_balance_after = fund.get("balance_after")
    transaction.plan_balance_after = transaction.plan_balance_before + amount
    plan_instance.plan_balance_after = plan_instance.plan_balance_before + amount

    # save the changes
    plan_instance.save()
    transaction.save()


# def fund_personal_ajo_savings_plan_with_wallet(
#     user: settings.AUTH_USER_MODEL,
#     ajo_savings: AjoSaving,
#     amount: float,
#     request_data: dict | None = None,
# ) -> Transaction:
#     pass


class Discrepancies:

    @classmethod
    @django_transaction.atomic
    def debit_double_funding_transaction(
        cls,
        user,
        amount: float,
        wallet: WalletSystem,
        unique_reference: str = None,
    ) -> None:
        debit_transaction = (
            TransactionService.dynamic_deduction_from_wallet_transaction(
                user=user,
                amount=amount,
                wallet_type=wallet.wallet_type,
                request_data=None,
                description=f"{amount} was debited due to disparity with a transaction",
                transaction_destination=None,
                unique_reference=f"{unique_reference}_discrepancy",
                transaction_form_type=TransactionFormType.DISCREPANCY_DEBIT,
            )
        )

        DebitCreditActions.debit_wallet_and_update_transaction(
            amount=amount,
            wallet=wallet,
            transaction_instance=debit_transaction,
        )


class PlanPayments:

    def onlending_plan_checks(onlending_plan: Onlending) -> Dict[str, Any] | None:
        data = {
            "message": None,
            "code": None,
        }

        if onlending_plan.is_activated:
            data["message"] = "plan has already been activated"
            data["code"] = "902"

        if not onlending_plan.is_active and onlending_plan.is_activated:
            data["message"] = "this is a closed plan"
            data["code"] = "801"

        if onlending_plan.maturity_date <= timezone.localdate():
            data["message"] = (
                "the maturity date for this plan has passed, create a new one"
            )
            data["code"] = "682"

        if data.get("message"):
            return data
        else:
            return None

    @classmethod
    @django_transaction.atomic
    def increment_positions(
        cls, plan: Union[Onlending], fund_transaction: Transaction, amount: float
    ):
        plan.plan_balance_before = plan.amount_saved
        fund_transaction.plan_balance_before = plan.amount_saved

        plan.amount_saved += amount

        plan.plan_balance_after = plan.plan_balance_before + amount
        fund_transaction.plan_balance_after = plan.plan_balance_before + amount

        plan.save()
        fund_transaction.save()

    @classmethod
    @django_transaction.atomic
    def decrement_positions(
        cls, plan: Union[Onlending], debit_transaction: Transaction, amount: float
    ):
        debit_transaction.plan_balance_before = plan.amount_saved
        plan.plan_balance_before = plan.amount_saved

        # decrease the amount saved
        plan.amount_saved -= amount

        # set the balance after as done above for balance before
        debit_transaction.plan_balance_after = (
            debit_transaction.plan_balance_before - amount
        )
        plan.plan_balance_after = plan.plan_balance_before - amount

        plan.save()
        debit_transaction.save()

    @classmethod
    def create_funding_transaction(
        cls,
        onlending_wallet: WalletSystem,
        amount: float,
        onlending_plan: Onlending,
        transaction_form_type: TransactionFormType = TransactionFormType.WALLET_DEPOSIT,
    ) -> Transaction:
        """create a funding transaction instance"""
        fund_transaction = TransactionService.create_deposit_by_wallet_transaction(
            user=onlending_wallet.user,
            amount=amount,
            quotation_id=onlending_plan.quotation_id,
            ajo_user=onlending_wallet.onboarded_user,
            unique_reference=None,
            description=f"{amount} was funded into your onlending plan, {onlending_plan.name}",
            wallet_type=onlending_wallet.wallet_type,
            plan_type=PlanType.ONLENDING,
            transaction_form_type=transaction_form_type,
        )
        return fund_transaction

    @classmethod
    @django_transaction.atomic
    def update_positions_activate_plan(
        cls,
        onlending_plan: Onlending,
        fund_transaction: Transaction,
        amount: float,
    ) -> None:
        cls.increment_positions(
            plan=onlending_plan,
            fund_transaction=fund_transaction,
            amount=amount,
        )

        onlending_plan.refresh_from_db()
        OnlendingService.activate_and_recalculate_interest(onlending=onlending_plan)

        onlending_plan.refresh_from_db()
        onlending_plan.is_activated = True
        onlending_plan.is_active = True

        onlending_plan.save()

    @classmethod
    @django_transaction.atomic
    def perform_onlending_funding(
        cls,
        onlending_wallet: WalletSystem,
        amount: float,
        onlending_plan: Onlending,
        fund_transaction: Optional[Transaction] = None,
    ) -> None:

        APA.fund_wallet_and_update_transaction(
            wallet=onlending_wallet,
            amount=amount,
            transaction_instance=fund_transaction,
            ajo_user=onlending_wallet.onboarded_user,
        )

        cls.update_positions_activate_plan(
            onlending_plan=onlending_plan,
            fund_transaction=fund_transaction,
            amount=amount,
        )

    @classmethod
    @django_transaction.atomic
    def fund_onlending_plan(
        cls,
        from_wallet: WalletSystem,
        amount: float,
        onlending_plan: Onlending,
        onlending_wallet: WalletSystem,
    ) -> None:
        """
        Fund an onlending plan from any wallet
        """
        APA.debit_wallet_through_transfer(
            from_wallet=from_wallet,
            amount=amount,
            description=f"{amount} transferred for funding onlending plan {onlending_plan.name}.",
            plan_type=PlanType.ONLENDING,
        )
        fund_transaction = cls.create_funding_transaction(
            onlending_wallet=onlending_wallet,
            amount=amount,
            onlending_plan=onlending_plan,
        )
        cls.perform_onlending_funding(
            onlending_wallet=onlending_wallet,
            amount=amount,
            onlending_plan=onlending_plan,
            fund_transaction=fund_transaction,
        )

    @classmethod
    def create_funding_transaction(
        cls,
        onlending_wallet: WalletSystem,
        amount: float,
        onlending_plan: Onlending,
        transaction_form_type: TransactionFormType = TransactionFormType.WALLET_DEPOSIT,
    ) -> Transaction:
        """create a funding transaction instance"""
        fund_transaction = TransactionService.create_deposit_by_wallet_transaction(
            user=onlending_wallet.user,
            amount=amount,
            quotation_id=onlending_plan.quotation_id,
            ajo_user=onlending_wallet.onboarded_user,
            unique_reference=None,
            description=f"{amount} was funded into your onlending plan, {onlending_plan.name}",
            wallet_type=onlending_wallet.wallet_type,
            plan_type=PlanType.ONLENDING,
            transaction_form_type=transaction_form_type,
        )
        return fund_transaction

    @classmethod
    @django_transaction.atomic
    def update_positions_activate_plan(
        cls,
        onlending_plan: Onlending,
        fund_transaction: Transaction,
        amount: float,
    ) -> None:
        cls.increment_positions(
            plan=onlending_plan,
            fund_transaction=fund_transaction,
            amount=amount,
        )

        onlending_plan.refresh_from_db()
        OnlendingService.activate_and_recalculate_interest(onlending=onlending_plan)

        onlending_plan.refresh_from_db()
        onlending_plan.is_activated = True
        onlending_plan.is_active = True

        onlending_plan.save()

    @classmethod
    @django_transaction.atomic
    def perform_onlending_funding(
        cls,
        onlending_wallet: WalletSystem,
        amount: float,
        onlending_plan: Onlending,
        fund_transaction: Optional[Transaction] = None,
    ) -> None:
        APA.fund_wallet_and_update_transaction(
            wallet=onlending_wallet,
            amount=amount,
            transaction_instance=fund_transaction,
            ajo_user=onlending_wallet.onboarded_user,
        )

        cls.update_positions_activate_plan(
            onlending_plan=onlending_plan,
            fund_transaction=fund_transaction,
            amount=amount,
        )

    @classmethod
    @django_transaction.atomic
    def fund_onlending_from_ajo_savings(
        cls,
        ajo_plan: AjoSaving,
        amount: float,
        onlending_plan: Onlending,
        onlending_wallet: WalletSystem,
    ) -> None:
        """
        Fund the onlending plan from ajo savings
        Args:
            ajo_plan (AjoSaving): the ajo plan
            amount (float): the amount to be funded
            onlending_plan (Onlending): the onlending plan
            onlending_wallet (WalletSystem): the onlending wallet
        Returns:
            None
        """
        debit_transaction = TransactionService.create_ajo_cashout_by_ussd(
            user=ajo_plan.user,
            amount=amount,
            transaction_description=f"{amount} was transferred for funding your onlending plan, {onlending_plan.name}",
            quotation_id=ajo_plan.quotation_id,
            transaction_form_type=TransactionFormType.TRANSFER,
            transaction_destination=TransactionDestination.PLAN,
            ajo_user=ajo_plan.ajo_user,
        )
        APA.debit_ajo_plan(
            ajo_savings=ajo_plan,
            amount=amount,
            wallet=APA.AjoUserSelector(
                ajo_user=ajo_plan.ajo_user
            ).get_ajo_user_wallet(),
            transaction_instance=debit_transaction,
        )
        fund_transaction = cls.create_funding_transaction(
            onlending_wallet=onlending_wallet,
            amount=amount,
            onlending_plan=onlending_plan,
        )
        cls.perform_onlending_funding(
            onlending_wallet=onlending_wallet,
            amount=amount,
            onlending_plan=onlending_plan,
            fund_transaction=fund_transaction,
        )

    @classmethod
    @shared_task
    @django_transaction.atomic
    def pay_upfront_interest_into_wallet(
        cls,
        quotation_id: str,
        plan_type: PlanType,
        wallet: WalletSystem,
    ) -> Dict[str, str]:
        plan_instance = None

        if plan_type == PlanType.ONLENDING:
            try:
                plan_instance = Onlending.objects.get(quotation_id=quotation_id)
            except Onlending.DoesNotExist as err:
                return {
                    "status": "failed",
                    "message": "could not obtain onlending plan",
                }

        if not plan_instance:
            return {
                "status": "failed",
                "message": "could not obtain plan instance",
            }

        if plan_instance.interest_paid:
            raise ValueError("interest has been paid already")

        amount = plan_instance.total_interest_earned

        interest_transaction = TransactionService.create_upfront_interest_to_wallet_transaction(
            user=plan_instance.user,
            amount=plan_instance.total_interest_earned,
            quotation_id=quotation_id,
            plan_type=plan_type,
            ajo_user=plan_instance.ajo_user,
            description=f"{amount} paid as interest for '{plan_instance.name}' {plan_type.lower()} plan",
            wallet_type=wallet.wallet_type,
        )

        fund = APA.fund_wallet_and_update_transaction(
            wallet=wallet,
            amount=amount,
            transaction_instance=interest_transaction,
            ajo_user=plan_instance.ajo_user,
        )

        InterestsPaidTableMethods.create_upfront_interest_instance(
            user=plan_instance.user,
            plan_type=plan_type,
            total_interest_paid=plan_instance.total_interest_earned,
            plan_quotation_id=plan_instance.quotation_id,
        )

        plan_instance.interest_paid = True
        plan_instance.save()

    @classmethod
    @shared_task
    @django_transaction.atomic
    def pay_interest_into_plan(
        cls,
        plan_instance: Union[Onlending],
        wallet: WalletSystem,
    ) -> None:
        if plan_instance.interest_paid:
            raise ValueError("interest has been paid already")

        amount = plan_instance.total_interest_earned

        if isinstance(plan_instance, Onlending):
            plan_type = PlanType.ONLENDING

        interest_transaction = TransactionService.create_upfront_interest_to_wallet_transaction(
            user=plan_instance.user,
            amount=amount,
            quotation_id=plan_instance.quotation_id,
            plan_type=plan_type,
            ajo_user=plan_instance.ajo_user,
            description=f"{amount} paid as interest for '{plan_instance.name}' {plan_type.lower()} plan",
            wallet_type=wallet.wallet_type,
            transaction_form_type=TransactionFormType.INTEREST_DEPOSIT,
        )

        APA.fund_wallet_and_update_transaction(
            wallet=wallet,
            amount=amount,
            transaction_instance=interest_transaction,
            ajo_user=plan_instance.ajo_user,
        )

        InterestsPaidTableMethods.create_upfront_interest_instance(
            user=plan_instance.user,
            plan_type=plan_type,
            total_interest_paid=amount,
            plan_quotation_id=plan_instance.quotation_id,
        )

        cls.increment_positions(
            plan=plan_instance,
            fund_transaction=interest_transaction,
            amount=amount,
        )

        plan_instance.refresh_from_db()
        plan_instance.interest_paid = True
        plan_instance.save()

    @classmethod
    @django_transaction.atomic
    def debit_savings_plan(
        cls,
        savings: Union[Onlending],
        amount: float,
        wallet: WalletSystem,
        transaction_instance: Transaction,
        close_plan: bool = True,
    ) -> None:
        # firstly deduct the ajo user wallet and create the debit_credit_info instance

        ajo_user = savings.ajo_user

        deduct = debit_wallet(
            wallet=wallet,
            amount=amount,
            transaction_instance=transaction_instance,
            onboarded_user=ajo_user,
        )

        # set the transaction wallet, plan balance before and plan_instance balance before
        transaction_instance.wallet_balance_before = deduct.get("balance_before")
        transaction_instance.plan_balance_before = savings.amount_saved
        savings.plan_balance_before = savings.amount_saved

        # decrease the amount saved
        savings.amount_saved -= amount

        # set the balance after as done above for balance before
        transaction_instance.wallet_balance_after = deduct.get("balance_after")
        transaction_instance.plan_balance_after = (
            transaction_instance.plan_balance_before - amount
        )
        savings.plan_balance_after = savings.plan_balance_before - amount

        # save the changes
        savings.save()
        savings.refresh_from_db()

        # set some other fields
        transaction_instance.transaction_date_completed = timezone.localtime()
        transaction_instance.save()

        if isinstance(savings, Onlending):

            if close_plan:
                savings.is_active = False
                savings.withdrawn = True
                savings.save()

    @classmethod
    @shared_task
    @django_transaction.atomic
    def reverse_onlending_plan_debit(
        cls,
        user_id: int,
        amount: float,
        failed_transaction_id: str,
        request_data: dict,
        plan_type: PlanType = PlanType.ONLENDING,
        plan_id: int | None = None,
        quotation_id: str | None = None,
    ) -> Dict[str, Any]:
        # obtain user id
        user = get_user_model().objects.get(id=user_id)

        # obtain the plan instance
        if plan_id:
            plan_instance: Onlending = OnlendingSelector.get_onlending_plan_by_id(
                id=plan_id, user=user
            )
        else:
            plan_instance = Onlending.objects.get(quotation_id=quotation_id)

        # create reversal transaction
        transaction = TransactionService.create_reversal_of_funds_transaction(
            user=user,
            amount=amount,
            wallet_type=WalletTypes.ONLENDING,
            request_data=request_data,
            quotation_id=plan_instance.quotation_id,
            plan_type=plan_type,
            description=f"{amount} was reversed to your plan due to failed transaction",
            unique_reference=f"{failed_transaction_id}_reversal",
        )

        # retrieve wallet instance
        if plan_instance.ajo_user:
            wallet = OnlendingSelector(
                user=user, ajo_user=plan_instance.ajo_user
            ).get_onlending_wallet()
        else:
            wallet = OnlendingSelector(user=user).get_onlending_wallet()

        # fund the wallet
        fund = WalletSystem.fund_balance(
            wallet=wallet,
            amount=amount,
            transaction_instance=transaction,
        )

        # set the transaction wallet, plan balance before and plan_instance balance before
        transaction.wallet_balance_before = fund.get("balance_before")
        transaction.plan_balance_before = plan_instance.amount_saved
        plan_instance.plan_balance_before = plan_instance.amount_saved

        # decrease the amount saved
        plan_instance.amount_saved += amount

        # set the balance after as done above
        transaction.wallet_balance_after = fund.get("balance_after")
        transaction.plan_balance_after = transaction.plan_balance_before + amount
        plan_instance.plan_balance_after = plan_instance.plan_balance_before + amount

        # save the changes
        plan_instance.save()
        transaction.save()

        return {
            "status": "success",
            "message": f"onlending plan {plan_id} had {amount} reversed due to failed transaction",
        }

    @classmethod
    @django_transaction.atomic
    def rollover_operation(
        cls,
        old_onlending_plan: Onlending,
        new_onlending_plan: Onlending,
        amount: float,
        request_data: Dict[str, Any],
    ) -> None:
        """
        Rollover the money from old onlending plan to new
        onlending plan if it belongs to the same user

        Args:
            old_onlending_plan (Onlending): Old onlending
            new_onlending_plan (Onlending): Rollover onlending plan
        """

        debit_transaction = TransactionService.create_transfer_to_external_account_transaction(
            user=old_onlending_plan.user,
            amount=amount,
            description=f"{amount} was debited from your plan to rollover into your new plan.",
            ajo_user=old_onlending_plan.ajo_user,
            wallet_type=WalletTypes.ONLENDING,
            request_data=request_data,
            quotation_id=old_onlending_plan.quotation_id,
            plan_type=PlanType.ONLENDING,
            transaction_form_type=TransactionFormType.ROLLOVER,
            status=Status.SUCCESS,
            transaction_source=None,
            transaction_destination=TransactionDestination.PLAN,
        )

        cls.decrement_positions(
            plan=old_onlending_plan,
            debit_transaction=debit_transaction,
            amount=amount,
        )

        old_onlending_plan.refresh_from_db()

        fund_transaction = TransactionService.create_deposit_by_wallet_transaction(
            user=new_onlending_plan.user,
            amount=amount,
            quotation_id=new_onlending_plan.quotation_id,
            plan_type=PlanType.ONLENDING,
            wallet_type=WalletTypes.ONLENDING,
            ajo_user=new_onlending_plan.ajo_user,
            unique_reference=None,
            transaction_form_type=TransactionFormType.ROLLOVER,
            status=Status.SUCCESS,
            description=f"{amount} was funded into plan from '{old_onlending_plan.name}' onlending plan",
        )

        cls.increment_positions(
            plan=new_onlending_plan,
            fund_transaction=fund_transaction,
            amount=amount,
        )

        new_onlending_plan.refresh_from_db()
        new_onlending_plan.is_activated = True
        new_onlending_plan.is_active = True
        new_onlending_plan.save()

    @classmethod
    @django_transaction.atomic
    def debit_wallet_and_pay_interest(
        cls,
        plan: Union[Onlending],
        from_wallet: WalletSystem,
        to_wallet: WalletSystem,
    ) -> Dict[str, str]:
        """
        Pay the interest from the wallet into another wallet

        Args:
            plan (Union[Onlending]): the plan instance
            from_wallet (WalletSystem): the wallet being debited
            to_wallet (WalletSystem): the wallet being funded

        Returns:
            Dict[str, str]: The dictionary response
        """
        amount = plan.total_interest_earned

        debit_transaction = TransactionService.dynamic_deduction_from_wallet_transaction(
            user=plan.user,
            amount=amount,
            wallet_type=from_wallet.wallet_type,
            request_data=None,
            description=f"your interest, {amount}, has been debited from your onlending plan",
            ajo_user=plan.ajo_user,
        )

        deduct = APA.debit_wallet_and_update_transaction(
            wallet=from_wallet,
            amount=amount,
            transaction_instance=debit_transaction,
        )

        cls.decrement_positions(
            plan=plan,
            debit_transaction=debit_transaction,
            amount=amount,
        )

        debit_transaction.wallet_balance_before = deduct.get("balance_before")
        debit_transaction.wallet_balance_after = deduct.get("balance_after")
        debit_transaction.transaction_date_completed = timezone.localtime()
        debit_transaction.save()

        interest_transaction = (
            TransactionService.create_upfront_interest_to_wallet_transaction(
                user=plan.user,
                amount=plan.total_interest_earned,
                quotation_id=plan.quotation_id,
                plan_type=PlanType.ONLENDING,
                ajo_user=plan.ajo_user,
                description=f"{amount} paid as interest for '{plan.name}' plan",
                wallet_type=to_wallet.wallet_type,
                transaction_form_type=TransactionFormType.WALLET_DEPOSIT,
            )
        )

        fund = APA.fund_wallet_and_update_transaction(
            wallet=to_wallet,
            amount=amount,
            transaction_instance=interest_transaction,
            ajo_user=plan.ajo_user,
        )

    @classmethod
    @django_transaction.atomic
    def debit_savings_and_pay_commission(
        cls,
        savings: Union[Onlending],
        debit_wallet: WalletSystem,
        credit_wallet: WalletSystem,
    ) -> None:
        """
        Debits savings account for commission and pays commission to the agent

        Args:
            savings (Union[Onlending]): The savings account to debit for commission
            debit_wallet (WalletSystem): The wallet to debit the commission amount from
            credit_wallet (WalletSystem): The wallet to credit the commission amount to
        """
        commission = savings.commission_amount

        OnlendingCommissionHistory.objects.create(
            plan=savings,
            amount=commission,
            rate=savings.commission_rate,
            paid_to=credit_wallet.user.email,
            paid_from=debit_wallet.user.email,
        )

        debit_transaction = TransactionService.create_ajo_commissions_transaction(
            user=savings.user,
            amount=commission,
            quotation_id=savings.quotation_id,
            ajo_user=savings.ajo_user,
            transaction_description=f"NGN{commission} was debited for commission",
            wallet_type=debit_wallet.wallet_type,
            plan_type=PlanType.ONLENDING,
        )
        cls.debit_savings_plan(
            savings=savings,
            amount=commission,
            wallet=debit_wallet,
            transaction_instance=debit_transaction,
            close_plan=False,
        )

        CommissionService.create_commission_instance(
            user=savings.user,
            commission_type=CommissionType.AGENT,
            amount=commission,
            description=f"NGN{commission} was credited for commission",
            quotation_id=savings.quotation_id,
            total_amount_taken_as_commission=commission,
            plan_type=PlanType.ONLENDING,
        )

        # create the transaction instance
        increment_transaction = (
            TransactionService.create_agent_commissions_increment_transaction(
                user=credit_wallet.user,
                amount=commission,
                quotation_id=savings.quotation_id,
                transaction_description=f"NGN{commission} was credited for commission",
                plan_type=PlanType.ONLENDING,
                wallet_type=credit_wallet.wallet_type,
            )
        )

        APA.fund_wallet_and_update_transaction(
            wallet=credit_wallet,
            amount=commission,
            transaction_instance=increment_transaction,
        )

        savings.commission_paid = True
        savings.save()
